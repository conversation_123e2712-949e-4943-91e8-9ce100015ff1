"use strict";let t={};t={data:()=>({is_show:"none"}),watch:{show(t){this.is_show=this.show}},created(){this.swipeaction=this.getSwipeAction(),this.swipeaction&&Array.isArray(this.swipeaction.children)&&this.swipeaction.children.push(this)},mounted(){this.is_show=this.show},methods:{closeSwipe(t){this.autoClose&&this.swipeaction&&this.swipeaction.closeOther(this)},change(t){this.$emit("change",t.open),this.is_show!==t.open&&(this.is_show=t.open)},appTouchStart(t){const{clientX:i}=t.changedTouches[0];this.clientX=i,this.timestamp=(new Date).getTime()},appTouchEnd(t,i,s,e){const{clientX:h}=t.changedTouches[0];let o=Math.abs(this.clientX-h),n=(new Date).getTime()-this.timestamp;o<40&&n<300&&this.$emit("click",{content:s,index:i,position:e})},onClickForPC(t,i,s){}}};const i=t;exports.mpwxs=i;
