"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const api_index = require("../../api/index.js");
const stores_user = require("../../stores/user.js");
const api_common = require("../../api/common.js");
const vipIcon = "https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/c4d5b3ada53740d5b862f274ce48ef0c.png";
const _sfc_main = {
  __name: "my",
  setup(__props) {
    const userStore = stores_user.useUserStore();
    const tabs = ["我的智能体", "共创收益", "我的工作台"];
    const activeTab = common_vendor.ref(0);
    const workspaceTabs = ["我的收藏", "我的数字人"];
    const activeWorkspaceTab = common_vendor.ref(1);
    const selectedOption = common_vendor.ref(0);
    const rechargeOptions = common_vendor.ref([]);
    const selectedPrice = common_vendor.computed(() => {
      var _a;
      return ((_a = rechargeOptions.value[selectedOption.value]) == null ? void 0 : _a.price) || "0.00";
    });
    const getChatGoods = async () => {
      try {
        let res = await api_index.getChatGoodsApi();
        if (res.code === 0 && res.data) {
          rechargeOptions.value = res.data;
        } else {
          rechargeOptions.value = [];
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/my/my.vue:356", "获取聊天点数商品列表失败:", error);
        rechargeOptions.value = [];
      }
    };
    const agentTypes = common_vendor.ref([
      { label: "内部", value: 1 },
      { label: "dify", value: 2 },
      { label: "coze", value: 3 },
      { label: "阿里云百炼", value: 4 }
    ]);
    const auditStatus = {
      1: "待审核",
      2: "审核通过",
      3: "审核拒绝"
    };
    const agentList = common_vendor.ref([]);
    const getMyAgentList = async () => {
      let res = await api_index.getMyAgentListApi({
        merchantGuid: userStore.merchantGuid
      });
      agentList.value = res.data.data;
    };
    const rulesList = common_vendor.ref([
      { name: "推广规则", icon: common_assets.rule1 },
      { name: "创作规则", icon: common_assets.rule2 },
      { name: "平台规则", icon: common_assets.rule3 }
    ]);
    const incomeData = common_vendor.ref({
      availableAmount: "0.00",
      availableAmountText: "¥0.00",
      frozenAmount: "0.00",
      frozenAmountText: "¥0.00",
      totalEarnings: "0.00",
      totalEarningsText: "¥0.00",
      totalWithdrawn: "0.00",
      totalWithdrawnText: "¥0.00"
    });
    const favoritesList = common_vendor.ref([]);
    let collectReq = common_vendor.reactive({
      merchantGuid: userStore.merchantGuid,
      page: 1,
      pageSize: 10
    });
    const showContentModal = common_vendor.ref(false);
    const currentContent = common_vendor.ref("");
    const showRechargeModal = common_vendor.ref(false);
    const getMyCollectionList = async () => {
      try {
        let res = await api_index.getMyCollectionListApi(collectReq);
        favoritesList.value = res.data.list;
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/my/my.vue:421", "获取收藏列表失败:", error);
        common_vendor.index.showToast({
          title: "加载失败",
          icon: "none"
        });
      }
    };
    const handleEditProfile = () => {
      common_vendor.index.navigateTo({
        url: "/pages/profile/index"
      });
    };
    const handleAgentClick = (agent) => {
      common_vendor.index.navigateTo({
        url: `/pages/msg/index?sessionGuid=${agent.guid}`
      });
    };
    const handleOpenVip = () => {
      common_vendor.index.navigateTo({
        url: "/pages/my/vip-list"
      });
    };
    const handleCreateAgent = () => {
      common_vendor.index.navigateTo({
        url: "/pages/create-agent/index"
      });
    };
    const handleEditAgent = (agent) => {
      common_vendor.index.navigateTo({
        url: `/pages/create-agent/index?guid=${agent.guid}`
      });
    };
    const handleDeleteAgent = async (agent) => {
      common_vendor.index.showModal({
        title: "提示",
        content: "确定要删除该智能体吗？",
        success: async (res) => {
          if (res.confirm) {
            await api_index.deleteAgentApi({
              merchantGuid: userStore.merchantGuid,
              agentGuid: agent.guid
            });
            common_vendor.index.showToast({
              title: "删除成功",
              icon: "success"
            });
            getMyAgentList();
          }
        }
      });
    };
    const handleRuleClick = (index) => {
      if (index === 0) {
        common_vendor.index.navigateTo({
          url: "/pages/rule/tg-index"
        });
      }
      if (index === 1) {
        common_vendor.index.navigateTo({
          url: "/pages/rule/cz-index"
        });
      }
      if (index === 2) {
        common_vendor.index.navigateTo({
          url: "/pages/rule/pt-index"
        });
      }
    };
    const handleWithdraw = () => {
      common_vendor.index.__f__("log", "at pages/my/my.vue:503", "提现");
    };
    const handleWithdrawRecord = () => {
      common_vendor.index.navigateTo({
        url: "/pages/raw-record/index"
      });
    };
    const handleFinanceFlow = () => {
      common_vendor.index.navigateTo({
        url: "/pages/finance/index"
      });
    };
    const handleCopy = (item) => {
      common_vendor.index.setClipboardData({
        data: item.messageContent,
        success() {
          common_vendor.index.showToast({
            title: "复制成功",
            icon: "none"
          });
        }
      });
    };
    const handleViewAll = (item) => {
      currentContent.value = item.messageContent || "";
      showContentModal.value = true;
    };
    const closeContentModal = () => {
      showContentModal.value = false;
      currentContent.value = "";
    };
    const openRechargeModal = async () => {
      await getChatGoods();
      showRechargeModal.value = true;
    };
    const closeRechargeModal = () => {
      showRechargeModal.value = false;
    };
    const selectOption = (index) => {
      selectedOption.value = index;
    };
    const confirmRecharge = async () => {
      const option = rechargeOptions.value[selectedOption.value];
      if (!option) {
        common_vendor.index.showToast({
          title: "请选择充值选项",
          icon: "none"
        });
        return;
      }
      try {
        common_vendor.index.showLoading({
          title: "正在创建订单...",
          mask: true
        });
        const payInfo = await api_index.buyChatGoodsApi({
          chatGoodsGuid: option.guid,
          payEnv: "xcx"
        });
        common_vendor.index.hideLoading();
        api_common.miniPay(payInfo.data).then(
          async () => {
            queryPayChatStatus(payInfo.data.orderNo, 0);
          },
          (res) => {
            common_vendor.index.showToast({
              title: res.msg || "支付失败",
              icon: "none"
            });
          }
        );
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: error.message || "创建订单失败",
          icon: "none"
        });
      }
    };
    const queryPayChatStatus = async (orderNo, number) => {
      number++;
      try {
        const orderInfo = await api_index.queryPayChatStautsApi({
          orderNo
        });
        if (orderInfo.data.isPay) {
          common_vendor.index.showToast({
            title: "充值成功",
            icon: "success",
            duration: 3e3
          });
          closeRechargeModal();
          if (userStore.userToken) {
            getVipInfo();
          }
        } else {
          setTimeout(() => {
            queryPayChatStatus(orderNo, number);
          }, 2e3);
        }
      } catch (error) {
        common_vendor.index.showToast({
          title: error.msg || "查询支付状态失败",
          icon: "none"
        });
      }
    };
    const goToMyWorks = () => {
      common_vendor.index.navigateTo({
        url: "/pages/my/works-list"
      });
    };
    const goToMyFigure = () => {
      common_vendor.index.navigateTo({
        url: "/pages/my/figure-list"
      });
    };
    const goToMyVideoCreate = () => {
      common_vendor.index.navigateTo({
        url: "/pages/my/video-create"
      });
      return;
    };
    const handleUnfavorite = async (item) => {
      await api_index.cancelCollectMessageApi({
        merchantGuid: userStore.merchantGuid,
        messageGuid: item.messageGuid
      });
      common_vendor.index.showToast({
        title: "取消收藏成功",
        icon: "none"
      });
      collectReq.page = 1;
      getMyCollectionList();
    };
    const handleViewMoreAgents = () => {
      common_vendor.index.navigateTo({
        url: "/pages/my/agent-list"
      });
    };
    const handleViewMoreFavorites = () => {
      common_vendor.index.navigateTo({
        url: "/pages/my/favorite-list"
      });
    };
    let userInfo = common_vendor.reactive({
      headImgUrl: "",
      email: "",
      nickname: ""
    });
    const getUserInfo = async () => {
      let res = await api_index.getUserInfoApi();
      userInfo = Object.assign(userInfo, res.data);
    };
    const getMyEarnings = async () => {
      let res = await api_index.getMyEarningsApi({
        merchantGuid: userStore.merchantGuid
      });
      common_vendor.index.__f__("log", "at pages/my/my.vue:694", res.data, "---------res.data");
      incomeData.value = res.data;
    };
    common_vendor.watch(
      () => userStore.userToken,
      (newValue, oldValue) => {
        if (newValue && oldValue === "") {
          getUserInfo();
          getMyAgentList();
          bindInvitation();
          getMyEarnings();
          getChatGoods();
        }
      }
    );
    let invite = common_vendor.ref("");
    const bindInvitation = async () => {
      try {
        common_vendor.index.__f__("log", "at pages/my/my.vue:712", "invite.value", invite.value);
        await api_index.bindInvitationApi({
          merchantGuid: userStore.merchantGuid,
          invitationCode: invite.value
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/my/my.vue:718", "绑定邀请码失败:", error);
      }
    };
    common_vendor.onLoad((params) => {
      if (params.invite) {
        invite.value = params.invite;
        if (userStore.userToken) {
          bindInvitation();
        }
      }
    });
    let vipInfo = common_vendor.reactive({
      hasMembership: false,
      isExpired: true,
      membership: null,
      remainingDays: 0,
      creatorSubscriptionCount: 0,
      categorySubscriptionCount: 0,
      userPoints: {
        totalPointsText: ""
      }
    });
    const getVipInfo = async () => {
      try {
        let res = await api_index.getUserVipInfoApi({
          merchantGuid: userStore.merchantGuid
        });
        vipInfo = Object.assign(vipInfo, res.data);
        common_vendor.index.__f__("log", "at pages/my/my.vue:748", res, "会员信息");
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/my/my.vue:750", "获取会员信息失败:", error);
        common_vendor.index.showToast({
          title: "加载失败",
          icon: "none"
        });
      }
    };
    common_vendor.onShow(() => {
      if (userStore.userToken) {
        getUserInfo();
        getMyAgentList();
        getMyCollectionList();
        getVipInfo();
        getMyEarnings();
        getChatGoods();
      }
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.unref(userInfo).headImgUrl,
        b: common_vendor.t(common_vendor.unref(userInfo).nickname || "未设置昵称"),
        c: common_vendor.t(common_vendor.unref(userInfo).email || "未设置邮箱"),
        d: common_vendor.o(handleEditProfile),
        e: !common_vendor.unref(vipInfo).hasMembership
      }, !common_vendor.unref(vipInfo).hasMembership ? {} : {
        f: common_vendor.t(common_vendor.unref(vipInfo).remainingDays)
      }, {
        g: !common_vendor.unref(vipInfo).hasMembership
      }, !common_vendor.unref(vipInfo).hasMembership ? {} : {}, {
        h: vipIcon,
        i: !common_vendor.unref(vipInfo).hasMembership
      }, !common_vendor.unref(vipInfo).hasMembership ? {} : {}, {
        j: common_vendor.o(handleOpenVip),
        k: common_assets._imports_0$3,
        l: common_vendor.t(common_vendor.unref(vipInfo).userPoints.totalPointsText),
        m: common_vendor.f(tabs, (tab, idx, i0) => {
          return {
            a: common_vendor.t(tab),
            b: tab,
            c: common_vendor.n({
              active: activeTab.value === idx
            }),
            d: common_vendor.o(($event) => activeTab.value = idx, tab)
          };
        }),
        n: activeTab.value === 0
      }, activeTab.value === 0 ? common_vendor.e({
        o: common_assets._imports_0$1,
        p: common_vendor.o(handleCreateAgent),
        q: common_vendor.f(agentList.value, (agent, index, i0) => {
          return common_vendor.e({
            a: agent.agentAvatar,
            b: common_vendor.t(agent.agentName),
            c: common_vendor.t(agent.agentDesc),
            d: common_vendor.t(agentTypes.value.find((item) => item.value === agent.agentType).label),
            e: common_vendor.t(agent.priceText),
            f: common_vendor.t(agent.isPublicText),
            g: common_vendor.o(($event) => handleAgentClick(agent), index),
            h: common_vendor.o(($event) => handleEditAgent(agent), index),
            i: common_vendor.o(($event) => handleDeleteAgent(agent), index),
            j: agent.auditStatus != 2
          }, agent.auditStatus != 2 ? {
            k: common_vendor.t(auditStatus[agent.auditStatus])
          } : {}, {
            l: agent.auditStatus === 2
          }, agent.auditStatus === 2 ? {
            m: common_vendor.t(auditStatus[agent.auditStatus])
          } : {}, {
            n: index
          });
        }),
        r: agentList.value.length > 0
      }, agentList.value.length > 0 ? {
        s: common_assets._imports_2,
        t: common_vendor.o(handleViewMoreAgents)
      } : {}) : activeTab.value === 1 ? {
        w: common_vendor.f(rulesList.value, (rule, index, i0) => {
          return {
            a: rule.icon,
            b: common_vendor.t(rule.name),
            c: index,
            d: common_vendor.o(($event) => handleRuleClick(index), index)
          };
        }),
        x: common_vendor.t(incomeData.value.availableAmountText),
        y: common_vendor.o(handleWithdraw),
        z: common_vendor.t(incomeData.value.totalEarningsText),
        A: common_vendor.t(incomeData.value.totalWithdrawnText),
        B: common_assets._imports_3,
        C: common_vendor.o(handleWithdrawRecord),
        D: common_assets._imports_4,
        E: common_vendor.o(handleFinanceFlow)
      } : common_vendor.e({
        F: common_vendor.f(workspaceTabs, (tab, idx, i0) => {
          return {
            a: common_vendor.t(tab),
            b: tab,
            c: common_vendor.n({
              active: activeWorkspaceTab.value === idx
            }),
            d: common_vendor.o(($event) => activeWorkspaceTab.value = idx, tab)
          };
        }),
        G: activeWorkspaceTab.value === 0
      }, activeWorkspaceTab.value === 0 ? common_vendor.e({
        H: common_vendor.f(favoritesList.value, (item, index, i0) => {
          return {
            a: common_vendor.t(item.collectTime),
            b: common_vendor.t(item.contentPreview),
            c: common_vendor.o(($event) => handleCopy(item), index),
            d: common_vendor.o(($event) => handleViewAll(item), index),
            e: common_vendor.o(($event) => handleUnfavorite(item), index),
            f: index
          };
        }),
        I: common_assets._imports_0$2,
        J: common_assets._imports_1$1,
        K: common_assets._imports_2$2,
        L: favoritesList.value.length > 0
      }, favoritesList.value.length > 0 ? {
        M: common_assets._imports_2,
        N: common_vendor.o(handleViewMoreFavorites)
      } : {}, {
        O: favoritesList.value.length === 0
      }, favoritesList.value.length === 0 ? {} : {}) : {
        P: common_assets._imports_8,
        Q: common_vendor.t(common_vendor.unref(userInfo).chat_count),
        R: common_vendor.o(openRechargeModal),
        S: common_assets._imports_9,
        T: common_vendor.o(goToMyFigure),
        U: common_assets._imports_10,
        V: common_vendor.o(goToMyVideoCreate),
        W: common_assets._imports_11,
        X: common_vendor.o(goToMyWorks)
      }), {
        v: activeTab.value === 1,
        Y: showRechargeModal.value
      }, showRechargeModal.value ? common_vendor.e({
        Z: rechargeOptions.value.length > 0
      }, rechargeOptions.value.length > 0 ? {
        aa: common_vendor.f(rechargeOptions.value, (option, index, i0) => {
          return {
            a: common_vendor.t(option.goodsName || `${option.count}次`),
            b: common_vendor.t(option.price),
            c: option.guid || index,
            d: selectedOption.value === index ? 1 : "",
            e: common_vendor.o(($event) => selectOption(index), option.guid || index)
          };
        })
      } : {}, {
        ab: common_vendor.t(selectedPrice.value || "50.00"),
        ac: common_vendor.o(confirmRecharge),
        ad: common_assets._imports_2$1,
        ae: common_vendor.o(closeRechargeModal),
        af: common_vendor.o(() => {
        })
      }) : {}, {
        ag: showContentModal.value
      }, showContentModal.value ? {
        ah: common_vendor.o(closeContentModal),
        ai: currentContent.value,
        aj: common_vendor.o(() => {
        }),
        ak: common_vendor.o(closeContentModal)
      } : {});
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-2f1ef635"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/my/my.js.map
