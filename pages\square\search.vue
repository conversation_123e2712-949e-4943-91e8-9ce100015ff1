<template>
  <view class="container">
    <!-- 搜索框 -->
    <view class="search-container">
      <view class="search-box">
        <image src="@/static/square/<EMAIL>" class="search-icon" mode="aspectFit"></image>
        <input v-model="searchKeyword" class="search-input" placeholder="搜索智能体" placeholder-class="placeholder"
          @input="handleSearchInput" @confirm="handleSearch" focus />
        <view v-if="searchKeyword" class="clear-btn" @tap="clearSearch">
          <text class="clear-text">×</text>
        </view>
      </view>
      <view class="cancel-btn" @tap="handleCancel">
        <text class="cancel-text">取消</text>
      </view>
    </view>

    <!-- 搜索结果 -->
    <view class="content-container">
      <!-- 加载状态 -->
      <view v-if="loading" class="loading-container">
        <text class="loading-text">搜索中...</text>
      </view>

      <!-- 空状态 -->
      <view v-else-if="!loading && searchResults.length === 0 && hasSearched" class="empty-container">
        <image src="@/static/square/<EMAIL>" class="empty-icon" mode="aspectFit"></image>
        <text class="empty-text">未找到相关智能体</text>
        <text class="empty-desc">试试其他关键词吧</text>
      </view>

      <!-- 搜索结果列表 -->
      <scroll-view v-else-if="searchResults.length > 0" scroll-y="true" class="scroll-view" @scrolltolower="loadMore">
        <view class="agent-list">
          <view v-for="item in searchResults" :key="item.guid" class="agent-item">
            <!-- 头像 -->
            <view class="avatar" @click="handleAgentClick(item)">
              <image :src="item.agentAvatar" class="avatar-img" mode="aspectFill"></image>
            </view>

            <!-- 内容区域 -->
            <view class="content" @click="handleAgentClick(item)">
              <view class="title">{{ item.agentName }}</view>
              <view class="description">{{ item.agentDesc }}</view>
              <view class="author">@{{ item.creator.nickname }}</view>
            </view>

            <!-- 右侧按钮 -->
            <view class="action-btn" :class="{ subscribed: item.isSubscribed }" @click.stop="onSub(item)">
              <text class="btn-text">{{ item.isSubscribed ? '已订阅' : '订阅' }}</text>
            </view>
          </view>
        </view>

        <!-- 加载更多 -->
        <view v-if="hasMore" class="load-more">
          <text class="load-more-text">{{ loadingMore ? '加载中...' : '上拉加载更多' }}</text>
        </view>

        <!-- 没有更多 -->
        <view v-else-if="searchResults.length > 0" class="no-more">
          <text class="no-more-text">没有更多了</text>
        </view>
      </scroll-view>

      <!-- 默认状态 -->
      <view v-else class="default-container">
        <image src="@/static/square/<EMAIL>" class="default-icon" mode="aspectFit"></image>
        <text class="default-text">输入关键词搜索智能体</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { useUserStore } from '@/stores/user.js'
import { getAgentListApi, subscribeAgentApi } from '@/api/index.js'

const userStore = useUserStore()

// 搜索相关
const searchKeyword = ref('')
const searchResults = ref([])
const loading = ref(false)
const hasSearched = ref(false)
const agentName = ref('')

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const hasMore = ref(true)
const loadingMore = ref(false)

// 搜索输入处理
const handleSearchInput = () => {
  // 可以在这里添加防抖逻辑
}


// 执行搜索
const handleSearch = async () => {
  if (!searchKeyword.value.trim()) {
    uni.showToast({
      title: '请输入搜索关键词',
      icon: 'none'
    })
    return
  }

  // 重置搜索状态
  currentPage.value = 1
  hasMore.value = true
  searchResults.value = []

  await performSearch()
}

// 执行搜索请求
const performSearch = async () => {
  if (loading.value || loadingMore.value) return

  try {
    if (currentPage.value === 1) {
      loading.value = true
    } else {
      loadingMore.value = true
    }

    const res = await getAgentListApi({
      merchantGuid: userStore.merchantGuid,
      agentName: searchKeyword.value.trim(),
      pageSize: pageSize.value,
      page: currentPage.value,
    })

    if (res.code === 0) {
      const newData = res.data.data || []

      if (currentPage.value === 1) {
        searchResults.value = newData
        hasSearched.value = true

      } else {
        searchResults.value = [...searchResults.value, ...newData]
      }

      // 判断是否还有更多数据
      hasMore.value = currentPage.value < res.data.last_page
    }
  } catch (error) {
    console.error('搜索失败:', error)
    uni.showToast({
      title: '搜索失败，请重试',
      icon: 'none'
    })
  } finally {
    loading.value = false
    loadingMore.value = false
  }
}

// 加载更多
const loadMore = async () => {
  if (!hasMore.value || loadingMore.value) return

  currentPage.value++
  await performSearch()
}

// 清除搜索
const clearSearch = () => {
  searchKeyword.value = ''
  searchResults.value = []
  hasSearched.value = false
  currentPage.value = 1
  hasMore.value = true
}

// 取消搜索
const handleCancel = () => {
  uni.navigateBack()
}

// 点击智能体
const handleAgentClick = (item) => {
  console.log('点击智能体:', item.agentName)
  // 跳转到详情页
  uni.navigateTo({
    url: `/pages/square/detail?sysId=${item.sysId}`
  })
}

// 订阅/取消订阅
const onSub = async (item) => {
  const req = {
    merchantGuid: userStore.merchantGuid,
    agentGuid: item.guid
  }

  try {
    await subscribeAgentApi(req)

    // 更新本地数据
    const index = searchResults.value.findIndex(agent => agent.guid === item.guid)
    if (index !== -1) {
      searchResults.value[index].isSubscribed = !searchResults.value[index].isSubscribed
    }

    uni.showToast({
      title: item.isSubscribed ? '取消订阅成功' : '订阅成功',
      icon: 'none'
    })
  } catch (error) {
    uni.showToast({
      title: '操作失败',
      icon: 'none'
    })
  }
}

// 页面加载时处理URL参数
onLoad((options) => {
  if (options.agentName) {
    // 接收到agentName参数，设置搜索关键词并自动搜索
    agentName.value = options.agentName
    searchKeyword.value = options.agentName;
    performSearch()
  }
})
</script>

<style lang="scss" scoped>
.container {
  background-color: #ffffff;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 搜索容器 */
.search-container {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #ffffff;
  border-bottom: 1px solid #f0f0f0;
}

.search-box {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #f8f9fa;
  border-radius: 40rpx;
  padding: 0 24rpx;
  height: 80rpx;
  margin-right: 20rpx;
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 16rpx;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  height: 100%;
}

.placeholder {
  color: #999;
}

.clear-btn {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #ccc;
  border-radius: 50%;
}

.clear-text {
  font-size: 24rpx;
  color: #fff;
  line-height: 1;
}

.cancel-btn {
  padding: 0 10rpx;
}

.cancel-text {
  font-size: 28rpx;
  color: #222222;
}

/* 内容容器 */
.content-container {
  flex: 1;
  overflow: hidden;
  background-color: #ffffff;
}

.scroll-view {
  height: 100%;
}

/* 状态容器 */
.loading-container,
.empty-container,
.default-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

.empty-icon,
.default-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 24rpx;
}

.empty-text,
.default-text {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 12rpx;
}

.empty-desc {
  font-size: 24rpx;
  color: #999;
}

/* 智能体列表 */
.agent-list {
  padding: 0 20px;
  background-color: #ffffff;
}

.agent-item {
  display: flex;
  align-items: center;
  padding: 20px 0;

  &:last-child {
    border-bottom: none;
  }
}

.avatar {
  width: 48px;
  height: 48px;
  margin-right: 16px;
  flex-shrink: 0;

  .avatar-img {
    width: 100%;
    height: 100%;
    border-radius: 24px;
  }
}

.content {
  flex: 1;
  margin-right: 16px;

  .title {
    font-size: 32rpx;
    font-weight: 600;
    color: #222;
    margin-bottom: 6px;
    line-height: 1.3;
  }

  .description {
    font-size: 24rpx;
    color: #333;
    line-height: 1.4;
    margin-bottom: 6px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
  }

  .author {
    font-size: 24rpx;
    color: #999999;
  }
}

.action-btn {
  flex-shrink: 0;
  height: 32px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 116rpx;
  background-color: #007AFF;

  .btn-text {
    font-size: 28rpx;
    font-weight: 500;
    color: #ffffff;
  }

  &.subscribed {
    background-color: #F2F2F7;
    border: none;

    .btn-text {
      color: #8E8E93;
    }
  }
}

/* 加载更多 */
.load-more,
.no-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.load-more-text,
.no-more-text {
  font-size: 24rpx;
  color: #999;
}
</style>
