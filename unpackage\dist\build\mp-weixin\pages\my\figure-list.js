"use strict";const e=require("../../common/vendor.js"),o=require("../../common/assets.js"),a=require("../../api/index.js"),s=require("../../stores/user.js"),i=require("../../config/config.js");if(!Array){e.resolveComponent("z-paging")()}Math;const n={__name:"figure-list",setup(n){const r=s.useUserStore(),t=e.ref(!1),l=e.ref([]),u=e.ref(null),c=e.ref(!1),d=e.ref(null),p=async(o,s)=>{try{const e=await a.getMyPersonListApi({merchantGuid:r.merchantGuid,page:o,pageSize:s});u.value.complete(e.data.list||[])}catch(i){console.error("获取形象列表失败:",i),e.index.showToast({title:"加载失败",icon:"none"}),u.value.complete(!1)}},m=()=>{c.value=!0,d.value=null},v=()=>{c.value=!1,d.value=null},h=()=>{d.value?e.index.showModal({title:"确认删除",content:"确定要删除选中的形象吗？",success:async o=>{if(o.confirm)try{await a.deletePersonApi({merchantGuid:r.merchantGuid,personGuid:d.value}),d.value=null,c.value=!1,u.value.reload(),e.index.showToast({title:"删除成功",icon:"success"})}catch(s){console.error("删除形象失败:",s),e.index.showToast({title:"删除失败",icon:"none"})}}}):e.index.showToast({title:"请先选择要删除的形象",icon:"none"})},f=()=>{t.value=!0},g=()=>{t.value=!1},w=()=>{e.index.chooseVideo({sourceType:["camera","album"],maxDuration:60,success:async o=>{console.log("选择的视频:",o.tempFilePath),e.index.showLoading({title:"上传中...",mask:!0});try{const s=await new Promise(((a,s)=>{e.index.uploadFile({url:`${i.base.baseUrl}user/api.userinfo/uploadVideo`,name:"video",fileType:"video",filePath:o.tempFilePath,success:e=>{a(e)},fail:e=>{s(e)}})})),n=JSON.parse(s.data);if(console.log("视频上传结果:",n),0!==n.code)throw new Error(n.msg||"视频上传失败");{const o=await a.createPersonkApi({merchantGuid:r.merchantGuid,materialVideoUrl:n.data,personName:"定制形象"});if(0!==o.code)throw new Error(o.msg||"创建形象失败");e.index.hideLoading(),e.index.showToast({title:"形象创建成功",icon:"success"}),u.value.reload(),g()}}catch(s){e.index.hideLoading(),console.error("上传或创建失败:",s),e.index.showToast({title:s.message||"操作失败，请重试",icon:"none"})}},fail:o=>{console.log("选择视频失败:",o),e.index.showToast({title:"选择视频失败",icon:"none"})}})};return(a,s)=>e.e({a:o._imports_0$4,b:e.o(f),c:e.f(l.value,((a,s,i)=>e.e({a:a.picUrl,b:e.t(a.statusText)},c.value?e.e({c:d.value===a.personGuid},d.value===a.personGuid?{d:o._imports_1$2}:{e:o._imports_2$3}):{},{f:c.value&&d.value===a.personGuid?1:"",g:e.t(a.personName),h:a.personGuid,i:e.o((e=>(e=>{if(!c.value)return;const o=e.personGuid;d.value===o?d.value=null:d.value=o})(a)),a.personGuid)}))),d:c.value,e:e.sr(u,"9264a81f-0",{k:"paging"}),f:e.o(p),g:e.o((e=>l.value=e)),h:e.p({"refresher-enabled":!0,auto:!0,modelValue:l.value}),i:!c.value},c.value?{l:e.o(v),m:e.o(h)}:{j:o._imports_3$1,k:e.o(m)},{n:t.value},t.value?{o:o._imports_2$1,p:e.o(g),q:e.o(w),r:e.o((()=>{}))}:{})}},r=e._export_sfc(n,[["__scopeId","data-v-9264a81f"]]);wx.createPage(r);
