{"version": 3, "file": "l-painter-image.js", "sources": ["uni_modules/lime-painter/components/l-painter-image/l-painter-image.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RToveW91bmdQcm9qZWN0L2FnZW50LW1pbmktdWkvdW5pX21vZHVsZXMvbGltZS1wYWludGVyL2NvbXBvbmVudHMvbC1wYWludGVyLWltYWdlL2wtcGFpbnRlci1pbWFnZS52dWU"], "sourcesContent": ["<template>\r\n\t\r\n</template>\r\n\r\n<script>\r\n\timport {parent, children} from '../common/relation';\r\n\texport default {\r\n\t\tname: 'lime-painter-image',\r\n\t\tmixins:[children('painter')],\r\n\t\tprops: {\r\n\t\t\tid: String,\r\n\t\t\tcss: [String, Object],\r\n\t\t\tsrc: String\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\ttype: 'image',\r\n\t\t\t\tel: {\r\n\t\t\t\t\tcss: {},\r\n\t\t\t\t\tsrc: null\r\n\t\t\t\t},\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n</style>\r\n", "import Component from 'E:/youngProject/agent-mini-ui/uni_modules/lime-painter/components/l-painter-image/l-painter-image.vue'\nwx.createComponent(Component)"], "names": ["children"], "mappings": ";;;AAMC,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,QAAO,CAACA,4DAAS,SAAS,CAAC;AAAA,EAC3B,OAAO;AAAA,IACN,IAAI;AAAA,IACJ,KAAK,CAAC,QAAQ,MAAM;AAAA,IACpB,KAAK;AAAA,EACL;AAAA,EACD,OAAO;AACN,WAAO;AAAA,MACN,MAAM;AAAA,MACN,IAAI;AAAA,QACH,KAAK,CAAE;AAAA,QACP,KAAK;AAAA,MACL;AAAA,IACF;AAAA,EACD;AACD;;;;;ACtBD,GAAG,gBAAgB,SAAS;"}