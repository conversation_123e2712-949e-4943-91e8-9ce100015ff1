"use strict";const t=require("../../../../../../common/vendor.js"),o=require("../z-paging-utils.js"),l=require("../z-paging-enum.js"),e={props:{usePageScroll:{type:Boolean,default:o.u.gc("usePageScroll",!1)},scrollable:{type:Boolean,default:o.u.gc("scrollable",!0)},showScrollbar:{type:Boolean,default:o.u.gc("showScrollbar",!0)},scrollX:{type:Boolean,default:o.u.gc("scrollX",!1)},scrollToTopBounceEnabled:{type:Boolean,default:o.u.gc("scrollToTopBounceEnabled",!1)},scrollToBottomBounceEnabled:{type:Boolean,default:o.u.gc("scrollToBottomBounceEnabled",!0)},scrollWithAnimation:{type:<PERSON><PERSON><PERSON>,default:o.u.gc("scrollWithAnimation",!1)},scrollIntoView:{type:String,default:o.u.gc("scrollIntoView","")}},data:()=>({scrollTop:0,oldScrollTop:0,scrollViewStyle:{},scrollViewContainerStyle:{},scrollViewInStyle:{},pageScrollTop:-1,scrollEnable:!0,privateScrollWithAnimation:-1,cacheScrollNodeHeight:-1,superContentHeight:0}),watch:{oldScrollTop(t){!this.usePageScroll&&this._scrollTopChange(t,!1)},pageScrollTop(t){this.usePageScroll&&this._scrollTopChange(t,!0)},usePageScroll:{handler(t){this.loaded&&this.autoHeight&&this._setAutoHeight(!t)},immediate:!0},finalScrollTop(t){this.renderPropScrollTop=t<6?0:10}},computed:{finalScrollWithAnimation(){return-1!==this.privateScrollWithAnimation?1===this.privateScrollWithAnimation:this.scrollWithAnimation},finalScrollViewStyle(){return 1!=this.superContentZIndex&&(this.scrollViewStyle["z-index"]=this.superContentZIndex,this.scrollViewStyle.position="relative"),this.scrollViewStyle},finalScrollTop(){return this.usePageScroll?this.pageScrollTop:this.oldScrollTop},finalIsOldWebView(){return this.isOldWebView&&!this.usePageScroll},finalScrollable(){return this.scrollable&&!this.usePageScroll&&this.scrollEnable&&(!!this.refresherCompleteScrollable||this.refresherStatus!==l.Enum.Refresher.Complete)&&(!!this.refresherRefreshingScrollable||this.refresherStatus!==l.Enum.Refresher.Loading)}},methods:{scrollToTop(t,o=!0){this.useChatRecordMode&&o&&!this.isChatRecordModeAndNotInversion?this.scrollToBottom(t,!1):this.$nextTick((()=>{this._scrollToTop(t,!1)}))},scrollToBottom(t,o=!0){this.useChatRecordMode&&o&&!this.isChatRecordModeAndNotInversion?this.scrollToTop(t,!1):this.$nextTick((()=>{this._scrollToBottom(t)}))},scrollIntoViewById(t,o,l){this._scrollIntoView(t,o,l)},scrollIntoViewByNodeTop(t,o,l){this.scrollTop=this.oldScrollTop,this.$nextTick((()=>{this._scrollIntoViewByNodeTop(t,o,l)}))},scrollToY(t,o,l){this.scrollTop=this.oldScrollTop,this.$nextTick((()=>{this._scrollToY(t,o,l)}))},scrollIntoViewByIndex(t,e,i){t>=this.realTotalData.length?o.u.consoleErr("当前滚动的index超出已渲染列表长度，请先通过refreshToPage加载到对应index页并等待渲染成功后再调用此方法！"):this.$nextTick((()=>{if(this.finalUseVirtualList){const s=this.cellHeightMode===l.Enum.CellHeightMode.Fixed;o.u.delay((()=>{if(this.finalUseVirtualList){const o=s?this.virtualCellHeight*t:this.virtualHeightCacheList[t].lastTotalHeight;this.scrollToY(o,e,i)}}),s?0:100)}}))},scrollIntoViewByView(t,o,l){this._scrollIntoView(t,o,l)},updatePageScrollTop(t){this.pageScrollTop=t},updatePageScrollTopHeight(){this._updatePageScrollTopOrBottomHeight("top")},updatePageScrollBottomHeight(){this._updatePageScrollTopOrBottomHeight("bottom")},updateLeftAndRightWidth(){this.finalIsOldWebView&&this.$nextTick((()=>this._updateLeftAndRightWidth(this.scrollViewContainerStyle,"zp-page")))},updateScrollViewScrollTop(t,o=!0){this._updatePrivateScrollWithAnimation(o),this.scrollTop=this.oldScrollTop,this.$nextTick((()=>{this.scrollTop=t,this.oldScrollTop=this.scrollTop}))},_onScrollToUpper(){this.$emit("scrolltoupper"),this.$emit("scrollTopChange",0),this.$nextTick((()=>{this.oldScrollTop=0}))},_onScrollToLower(t){(!t.detail||!t.detail.direction||"bottom"===t.detail.direction)&&this._onLoadingMore(this.useChatRecordMode?"click":"toBottom")},_scrollToTop(o=!0,l=!0){this.usePageScroll?this.$nextTick((()=>{t.index.pageScrollTo({scrollTop:0,duration:o?100:0})})):(this._updatePrivateScrollWithAnimation(o),this.scrollTop=this.oldScrollTop,this.$nextTick((()=>{this.scrollTop=0,this.oldScrollTop=this.scrollTop})))},async _scrollToBottom(o=!0){if(this.usePageScroll)this.$nextTick((()=>{t.index.pageScrollTo({scrollTop:Number.MAX_VALUE,duration:o?100:0})}));else try{this._updatePrivateScrollWithAnimation(o);const t=await this._getNodeClientRect(".zp-paging-container"),l=await this._getNodeClientRect(".zp-scroll-view"),e=t?t[0].height:0,i=l?l[0].height:0;e>i&&(this.scrollTop=this.oldScrollTop,this.$nextTick((()=>{this.scrollTop=e-i+this.virtualPlaceholderTopHeight,this.oldScrollTop=this.scrollTop})))}catch(l){}},_scrollIntoView(t,o=0,l=!1,e){try{this.scrollTop=this.oldScrollTop,this.$nextTick((()=>{this._getNodeClientRect("#"+t.replace("#",""),this.$parent).then((t=>{if(t){let i=t[0].top;this._scrollIntoViewByNodeTop(i,o,l),e&&e()}}))}))}catch(i){}},_scrollIntoViewByNodeTop(t,o=0,l=!1){this.isChatRecordModeAndInversion?this._getNodeClientRect(".zp-scroll-view").then((e=>{e&&this._scrollToY(e[0].height-t,o,l,!0)})):this._scrollToY(t,o,l,!0)},_scrollToY(l,e=0,i=!1,s=!1){this._updatePrivateScrollWithAnimation(i),o.u.delay((()=>{if(this.usePageScroll){s&&-1!==this.pageScrollTop&&(l+=this.pageScrollTop);const o=l-e;t.index.pageScrollTo({scrollTop:o,duration:i?100:0})}else s&&(l+=this.oldScrollTop),this.scrollTop=l-e}),10)},_scroll(t){this.$emit("scroll",t);const o=t.detail.scrollTop;this.finalUseVirtualList&&this._updateVirtualScroll(o,this.oldScrollTop-o),this.oldScrollTop=o;const l=t.detail.scrollHeight-this.oldScrollTop;!this.isIos&&this._checkScrolledToBottom(l)},_updatePrivateScrollWithAnimation(t){this.privateScrollWithAnimation=t?1:0,o.u.delay((()=>this.$nextTick((()=>{this.privateScrollWithAnimation=-1}))),100,"updateScrollWithAnimationDelay")},_doCheckScrollViewShouldFullHeight(t){this.autoFullHeight&&this.usePageScroll&&this.isTotalChangeFromAddData?this.$nextTick((()=>{this._checkScrollViewShouldFullHeight(((o,l)=>{this._preCheckShowNoMoreInside(t,o,l)}))})):this._preCheckShowNoMoreInside(t)},async _checkScrollViewShouldFullHeight(t){try{const o=await this._getNodeClientRect(".zp-scroll-view"),l=await this._getNodeClientRect(".zp-paging-container-content");if(!o||!l)return;const e=l[0].height,i=o[0].top;this.isAddedData&&e+i<=this.windowHeight?(this._setAutoHeight(!0,o),t(o,l)):(this._setAutoHeight(!1),t(null,null))}catch(o){t(null,null)}},async _updateCachedSuperContentHeight(){const t=await this._getNodeClientRect(".z-paging-content");t&&(this.superContentHeight=t[0].height)},_scrollTopChange(t,o){this.$emit("scrollTopChange",t),this.$emit("update:scrollTop",t),this._checkShouldShowBackToTop(t);const l=t>5?6:0;o&&this.wxsPageScrollTop!==l?this.wxsPageScrollTop=l:o||this.wxsScrollTop===l||(this.wxsScrollTop=l,l>6&&(this.scrollEnable=!0))},_updatePageScrollTopOrBottomHeight(t){if(!this.usePageScroll)return;this._doCheckScrollViewShouldFullHeight(this.realTotalData);const l=`.zp-page-${t}`,e=`margin${t.slice(0,1).toUpperCase()+t.slice(1)}`;let i=this.safeAreaInsetBottom;this.$nextTick((()=>{o.u.delay((()=>{this._getNodeClientRect(l).then((o=>{if(o){let l=o[0].height;"bottom"===t?i&&(l+=this.safeAreaBottom):this.cacheTopHeight=l,this.$set(this.scrollViewStyle,e,`${l}px`)}else i&&this.$set(this.scrollViewStyle,e,`${this.safeAreaBottom}px`)}))}),0)}))}}};exports.scrollerModule=e;
