"use strict";const e=require("../../../uv-ui-tools/libs/mixin/mpMixin.js"),t=require("../../../uv-ui-tools/libs/mixin/mixin.js"),s=require("./props.js"),i=require("../../../../common/vendor.js"),o={name:"uv-badge",mixins:[e.mpMixin,t.mixin,s.props],computed:{boxStyle:()=>({}),badgeStyle(){const e={};if(this.color&&(e.color=this.color),this.bgColor&&!this.inverted&&(e.backgroundColor=this.bgColor),this.absolute&&(e.position="absolute",this.offset.length)){const t=this.offset[0],s=this.offset[1]||t;e.top=this.$uv.addUnit(t),e.right=this.$uv.addUnit(s)}return e},showValue(){switch(this.numberType){case"overflow":return Number(this.value)>Number(this.max)?this.max+"+":this.value;case"ellipsis":return Number(this.value)>Number(this.max)?"...":this.value;case"limit":return Number(this.value)>999?Number(this.value)>=9999?Math.floor(this.value/1e4*100)/100+"w":Math.floor(this.value/1e3*100)/100+"k":this.value;default:return Number(this.value)}},propsType(){return this.type||"error"}}};const r=i._export_sfc(o,[["render",function(e,t,s,o,r,u){return i.e({a:e.show&&(0!==Number(e.value)||e.showZero||e.isDot)},e.show&&(0!==Number(e.value)||e.showZero||e.isDot)?{b:i.t(e.isDot?"":u.showValue),c:i.n(e.isDot?"uv-badge--dot":"uv-badge--not-dot"),d:i.n(e.inverted&&"uv-badge--inverted"),e:i.n("horn"===e.shape&&"uv-badge--horn"),f:i.n(`uv-badge--${u.propsType}${e.inverted?"--inverted":""}`),g:i.s(e.$uv.addStyle(e.customStyle)),h:i.s(u.badgeStyle)}:{})}],["__scopeId","data-v-8c3e535e"]]);wx.createComponent(r);
