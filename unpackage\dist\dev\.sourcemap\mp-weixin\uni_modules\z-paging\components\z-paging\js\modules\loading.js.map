{"version": 3, "file": "loading.js", "sources": ["uni_modules/z-paging/components/z-paging/js/modules/loading.js"], "sourcesContent": ["// [z-paging]loading相关模块\r\nimport u from '.././z-paging-utils'\r\nimport Enum from '.././z-paging-enum'\r\n\r\nexport default {\r\n\tprops: {\r\n\t\t// 第一次加载后自动隐藏loading slot，默认为是\r\n\t\tautoHideLoadingAfterFirstLoaded: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('autoHideLoadingAfterFirstLoaded', true)\r\n\t\t},\r\n\t\t// loading slot是否铺满屏幕并固定，默认为否\r\n\t\tloadingFullFixed: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('loadingFullFixed', false)\r\n\t\t},\r\n\t\t// 是否自动显示系统Loading：即uni.showLoading，若开启则将在刷新列表时(调用reload、refresh时)显示，下拉刷新和滚动到底部加载更多不会显示，默认为false。\r\n\t\tautoShowSystemLoading: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('autoShowSystemLoading', false)\r\n\t\t},\r\n\t\t// 显示系统Loading时是否显示透明蒙层，防止触摸穿透，默认为是(H5、App、微信小程序、百度小程序有效)\r\n\t\tsystemLoadingMask: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('systemLoadingMask', true)\r\n\t\t},\r\n\t\t// 显示系统Loading时显示的文字，默认为\"加载中\"\r\n\t\tsystemLoadingText: {\r\n\t\t\ttype: [String, Object],\r\n\t\t\tdefault: u.gc('systemLoadingText', null)\r\n\t\t},\r\n\t},\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tloading: false,\r\n\t\t\tloadingForNow: false,\r\n\t\t}\r\n\t},\r\n\twatch: {\r\n\t\t// loading状态\r\n\t\tloadingStatus(newVal) {\r\n\t\t\tthis.$emit('loadingStatusChange', newVal);\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\tthis.loadingStatusAfterRender = newVal;\r\n\t\t\t})\r\n\t\t\tif (this.useChatRecordMode) {\r\n\t\t\t\tif (this.isFirstPage && (newVal === Enum.More.NoMore || newVal === Enum.More.Fail)) {\r\n\t\t\t\t\tthis.isFirstPageAndNoMore = true;\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tthis.isFirstPageAndNoMore = false;\r\n\t\t},\r\n\t\tloading(newVal){\r\n\t\t\tif (newVal) {\r\n\t\t\t\tthis.loadingForNow = newVal;\r\n\t\t\t}\r\n\t\t},\r\n\t},\r\n\tcomputed: {\r\n\t\t// 是否显示loading\r\n\t\tshowLoading() {\r\n\t\t\tif (this.firstPageLoaded || !this.loading || !this.loadingForNow) return false;\r\n\t\t\tif (this.finalShowSystemLoading) {\r\n\t\t\t\t// 显示系统loading\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: this.finalSystemLoadingText,\r\n\t\t\t\t\tmask: this.systemLoadingMask\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t\treturn this.autoHideLoadingAfterFirstLoaded ? (this.fromEmptyViewReload ? true : !this.pagingLoaded) : this.loadingType === Enum.LoadingType.Refresher;\r\n\t\t},\r\n\t\t// 最终的是否显示系统loading\r\n\t\tfinalShowSystemLoading() {\r\n\t\t\treturn this.autoShowSystemLoading && this.loadingType === Enum.LoadingType.Refresher;\r\n\t\t}\r\n\t},\r\n\tmethods: {\r\n\t\t// 处理开始加载更多状态\r\n\t\t_startLoading(isReload = false) {\r\n\t\t\tif ((this.showLoadingMoreWhenReload && !this.isUserPullDown) || !isReload) {\r\n\t\t\t\tthis.loadingStatus = Enum.More.Loading;\r\n\t\t\t}\r\n\t\t\tthis.loading = true;\r\n\t\t},\r\n\t\t// 停止系统loading和refresh\r\n\t\t_endSystemLoadingAndRefresh(){\r\n\t\t\tthis.finalShowSystemLoading && uni.hideLoading();\r\n\t\t\t!this.useCustomRefresher && uni.stopPullDownRefresh();\r\n\t\t\t// #ifdef APP-NVUE\r\n\t\t\tthis.usePageScroll && uni.stopPullDownRefresh();\r\n\t\t\t// #endif\r\n\t\t}\r\n\t}\r\n}\r\n"], "names": ["u", "Enum", "uni"], "mappings": ";;;;AAIA,MAAe,gBAAA;AAAA,EACd,OAAO;AAAA;AAAA,IAEN,iCAAiC;AAAA,MAChC,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,mCAAmC,IAAI;AAAA,IACrD;AAAA;AAAA,IAED,kBAAkB;AAAA,MACjB,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,oBAAoB,KAAK;AAAA,IACvC;AAAA;AAAA,IAED,uBAAuB;AAAA,MACtB,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,yBAAyB,KAAK;AAAA,IAC5C;AAAA;AAAA,IAED,mBAAmB;AAAA,MAClB,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,qBAAqB,IAAI;AAAA,IACvC;AAAA;AAAA,IAED,mBAAmB;AAAA,MAClB,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAASA,uDAAC,EAAC,GAAG,qBAAqB,IAAI;AAAA,IACvC;AAAA,EACD;AAAA,EACD,OAAO;AACN,WAAO;AAAA,MACN,SAAS;AAAA,MACT,eAAe;AAAA,IACf;AAAA,EACD;AAAA,EACD,OAAO;AAAA;AAAA,IAEN,cAAc,QAAQ;AACrB,WAAK,MAAM,uBAAuB,MAAM;AACxC,WAAK,UAAU,MAAM;AACpB,aAAK,2BAA2B;AAAA,MACpC,CAAI;AACD,UAAI,KAAK,mBAAmB;AAC3B,YAAI,KAAK,gBAAgB,WAAWC,2DAAK,KAAK,UAAU,WAAWA,sDAAAA,KAAK,KAAK,OAAO;AACnF,eAAK,uBAAuB;AAC5B;AAAA,QACA;AAAA,MACD;AACD,WAAK,uBAAuB;AAAA,IAC5B;AAAA,IACD,QAAQ,QAAO;AACd,UAAI,QAAQ;AACX,aAAK,gBAAgB;AAAA,MACrB;AAAA,IACD;AAAA,EACD;AAAA,EACD,UAAU;AAAA;AAAA,IAET,cAAc;AACb,UAAI,KAAK,mBAAmB,CAAC,KAAK,WAAW,CAAC,KAAK;AAAe,eAAO;AACzE,UAAI,KAAK,wBAAwB;AAEhCC,sBAAAA,MAAI,YAAY;AAAA,UACf,OAAO,KAAK;AAAA,UACZ,MAAM,KAAK;AAAA,QAChB,CAAK;AAAA,MACD;AACD,aAAO,KAAK,kCAAmC,KAAK,sBAAsB,OAAO,CAAC,KAAK,eAAgB,KAAK,gBAAgBD,sDAAAA,KAAK,YAAY;AAAA,IAC7I;AAAA;AAAA,IAED,yBAAyB;AACxB,aAAO,KAAK,yBAAyB,KAAK,gBAAgBA,sDAAI,KAAC,YAAY;AAAA,IAC3E;AAAA,EACD;AAAA,EACD,SAAS;AAAA;AAAA,IAER,cAAc,WAAW,OAAO;AAC/B,UAAK,KAAK,6BAA6B,CAAC,KAAK,kBAAmB,CAAC,UAAU;AAC1E,aAAK,gBAAgBA,2DAAK,KAAK;AAAA,MAC/B;AACD,WAAK,UAAU;AAAA,IACf;AAAA;AAAA,IAED,8BAA6B;AAC5B,WAAK,0BAA0BC,oBAAI;AACnC,OAAC,KAAK,sBAAsBA,cAAG,MAAC,oBAAmB;AAAA,IAInD;AAAA,EACD;AACF;;"}