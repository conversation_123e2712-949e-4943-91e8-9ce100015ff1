"use strict";const e=require("../../common/vendor.js"),t=require("../../common/assets.js"),o={__name:"subscribe-popup",props:{show:{type:Boolean,default:!1},agentInfo:{type:Object,default:()=>({agentName:"",agentDesc:"",agentAvatar:"",price:0})}},emits:["close","subscribe"],setup(o,{emit:a}){const n=o,s=e.ref(!1),c=a,u=e.ref(n.show);e.watch((()=>n.show),(e=>{u.value=e}));const r=()=>{u.value=!1,c("close")},g=()=>{c("subscribe",n.agentInfo)};return e.onMounted((()=>{"ios"===e.index.getSystemInfoSync().osName&&(s.value=!0)})),(a,n)=>e.e({a:u.value},u.value?e.e({b:"https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/b9ed244f8bd849d48375bd6c29b48129.png",c:t._imports_0$11,d:e.o(r),e:o.agentInfo.agentAvatar,f:e.t(o.agentInfo.agentName),g:e.t(o.agentInfo.agentDesc),h:e.t(o.agentInfo.price),i:s.value},s.value?{}:{j:e.o(g)},{k:e.o((()=>{})),l:e.o(r)}):{})}},a=e._export_sfc(o,[["__scopeId","data-v-7da5cd07"]]);wx.createComponent(a);
