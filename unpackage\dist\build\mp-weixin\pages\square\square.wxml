<view class="container data-v-9a5bbf16"><view class="tabs-container data-v-9a5bbf16"><uv-tabs wx:if="{{d}}" class="data-v-9a5bbf16" u-s="{{['right']}}" bindchange="{{c}}" u-i="9a5bbf16-0" bind:__l="__l" u-p="{{d}}"><view class="search-icon data-v-9a5bbf16" bindtap="{{b}}" slot="right"><image src="{{a}}" class="icon data-v-9a5bbf16" mode="aspectFit"></image></view></uv-tabs></view><view class="content-container data-v-9a5bbf16" style="{{'height:' + k}}"><swiper class="tab-swiper data-v-9a5bbf16" current="{{h}}" indicator-dots="{{false}}" autoplay="{{false}}" circular="{{false}}" bindchange="{{i}}" style="{{'height:' + j}}"><swiper-item wx:for="{{e}}" wx:for-item="tab" wx:key="c" class="data-v-9a5bbf16" style="{{'height:' + g}}"><scroll-view scroll-y="true" class="scroll-view data-v-9a5bbf16" style="{{'height:' + f}}"><view class="agent-list data-v-9a5bbf16"><view wx:for="{{tab.a}}" wx:for-item="item" wx:key="j" class="agent-item data-v-9a5bbf16"><view class="avatar data-v-9a5bbf16" bindtap="{{item.b}}"><image src="{{item.a}}" class="avatar-img data-v-9a5bbf16" mode="aspectFill"></image></view><view class="content data-v-9a5bbf16" bindtap="{{item.f}}"><view class="title data-v-9a5bbf16">{{item.c}}</view><view class="description data-v-9a5bbf16">{{item.d}}</view><view class="author data-v-9a5bbf16">@{{item.e}}</view></view><view class="{{['action-btn', 'data-v-9a5bbf16', item.h && 'subscribed']}}" bindtap="{{item.i}}"><text class="btn-text data-v-9a5bbf16">{{item.g}}</text></view></view></view><view wx:if="{{tab.b}}" class="empty-container data-v-9a5bbf16"> 暂无智能体 </view></scroll-view></swiper-item></swiper></view></view>