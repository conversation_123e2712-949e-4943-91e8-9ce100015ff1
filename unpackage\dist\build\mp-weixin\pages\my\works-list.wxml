<view class="my-works-container data-v-256f4771"><z-paging wx:if="{{e}}" class="r data-v-256f4771" u-s="{{['d']}}" u-r="paging" bindquery="{{c}}" u-i="256f4771-0" bind:__l="__l" bindupdateModelValue="{{d}}" u-p="{{e}}"><view class="works-content data-v-256f4771"><view class="avatar-grid data-v-256f4771"><view wx:for="{{a}}" wx:for-item="work" wx:key="d" class="{{['avatar-card', 'data-v-256f4771', work.e && 'selected']}}" bindtap="{{work.f}}"><image class="avatar-image data-v-256f4771" src="{{work.a}}" mode="aspectFill"/><view wx:if="{{work.b}}" class="check-mark data-v-256f4771"><image class="check-icon data-v-256f4771" src="{{work.c}}"/></view></view></view></view></z-paging><view class="bottom-actions data-v-256f4771"><block wx:if="{{f}}"><view class="action-button edit data-v-256f4771" bindtap="{{h}}"><image class="edit-icon data-v-256f4771" src="{{g}}" mode="aspectFit"/><text class="action-text data-v-256f4771">编辑</text></view></block><block wx:else><view class="action-button cancel data-v-256f4771" bindtap="{{i}}"><text class="action-text data-v-256f4771">取消</text></view><view class="action-button delete data-v-256f4771" bindtap="{{j}}"><text class="action-text data-v-256f4771">删除</text></view></block></view></view>