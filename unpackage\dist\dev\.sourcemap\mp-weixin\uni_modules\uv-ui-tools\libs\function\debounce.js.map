{"version": 3, "file": "debounce.js", "sources": ["uni_modules/uv-ui-tools/libs/function/debounce.js"], "sourcesContent": ["let timeout = null\r\n\r\n/**\r\n * 防抖原理：一定时间内，只有最后一次操作，再过wait毫秒后才执行函数\r\n *\r\n * @param {Function} func 要执行的回调函数\r\n * @param {Number} wait 延时的时间\r\n * @param {Boolean} immediate 是否立即执行\r\n * @return null\r\n */\r\nfunction debounce(func, wait = 500, immediate = false) {\r\n    // 清除定时器\r\n    if (timeout !== null) clearTimeout(timeout)\r\n    // 立即执行，此类情况一般用不到\r\n    if (immediate) {\r\n        const callNow = !timeout\r\n        timeout = setTimeout(() => {\r\n            timeout = null\r\n        }, wait)\r\n        if (callNow) typeof func === 'function' && func()\r\n    } else {\r\n        // 设置定时器，当最后一次操作后，timeout不会再被清除，所以在延时wait毫秒后执行func回调方法\r\n        timeout = setTimeout(() => {\r\n            typeof func === 'function' && func()\r\n        }, wait)\r\n    }\r\n}\r\n\r\nexport default debounce\r\n"], "names": [], "mappings": ";AAAA,IAAI,UAAU;AAUd,SAAS,SAAS,MAAM,OAAO,KAAK,YAAY,OAAO;AAEnD,MAAI,YAAY;AAAM,iBAAa,OAAO;AAE1C,MAAI,WAAW;AACX,UAAM,UAAU,CAAC;AACjB,cAAU,WAAW,MAAM;AACvB,gBAAU;AAAA,IACb,GAAE,IAAI;AACP,QAAI;AAAS,aAAO,SAAS,cAAc,KAAM;AAAA,EACzD,OAAW;AAEH,cAAU,WAAW,MAAM;AACvB,aAAO,SAAS,cAAc,KAAM;AAAA,IACvC,GAAE,IAAI;AAAA,EACV;AACL;;"}