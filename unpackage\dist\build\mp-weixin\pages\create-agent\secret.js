"use strict";const e=require("../../common/vendor.js"),t=require("../../api/index.js"),s=require("../../stores/user.js"),c={__name:"secret",setup(c){const r=s.useUserStore(),a=e.ref([]),o=e.ref(!1),i=async()=>{if(a.value.some((e=>e.secretKey&&e.secretKey.trim()))){o.value=!0;try{const s=a.value.filter((e=>e.secretKey&&e.secretKey.trim())).map((e=>({secretKeyType:e.secretKeyType,secretKey:e.secretKey.trim()}))),c={merchantGuid:r.merchantGuid,saveSecretKeyList:s};let i=await t.saveSecretKeyApi(c);0===i.code?(e.index.showToast({title:"保存成功",icon:"success"}),setTimeout((()=>{e.index.navigateBack()}),1500)):e.index.showToast({title:i.msg||"保存失败",icon:"none"})}catch(s){console.error("保存秘钥失败:",s),e.index.showToast({title:"保存失败",icon:"none"})}finally{o.value=!1}}else e.index.showToast({title:"请至少输入一个秘钥",icon:"none"})};return e.onMounted((()=>{(async()=>{try{let e=await t.getSecretKeyListApi({merchantGuid:r.merchantGuid});0===e.code&&e.data&&e.data.length>0&&(a.value=e.data.map((e=>({...e,secretKey:e.secretKey||""}))))}catch(s){console.error("获取秘钥列表失败:",s),e.index.showToast({title:"获取秘钥失败",icon:"none"})}})()})),(t,s)=>({a:e.f(a.value,((t,s,c)=>({a:t.secretKeyTypeText,b:t.secretKey,c:e.o((e=>t.secretKey=e.detail.value),t.secretKeyType),d:t.secretKeyType}))),b:e.t(o.value?"保存中...":"保存修改"),c:o.value?1:"",d:e.o(i)})}},r=e._export_sfc(c,[["__scopeId","data-v-cc3b55e2"]]);wx.createPage(r);
