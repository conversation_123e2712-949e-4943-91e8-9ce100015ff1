"use strict";
const common_vendor = require("../../common/vendor.js");
const api_index = require("../../api/index.js");
const stores_user = require("../../stores/user.js");
const api_common = require("../../api/common.js");
const defaultAvatar = "https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/c4d5b3ada53740d5b862f274ce48ef0c.png";
const icon3 = "https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/46348d225bb54770a614dba856c5193e.png";
const icon2 = "https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/77e643ee1cd3492ba370158addccd825.png";
const icon1 = "https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/84d756efe28f4116a271e357f48d04e6.png";
const _sfc_main = {
  __name: "sub-list",
  setup(__props) {
    const userStore = stores_user.useUserStore();
    const userInfo = common_vendor.reactive({
      headImgUrl: "",
      nickname: "",
      chat_count: 0
    });
    const creatorSubscriptions = common_vendor.ref([]);
    common_vendor.ref([]);
    const showSubscribeModal = common_vendor.ref(false);
    const creatorList = common_vendor.ref([]);
    const isIos = common_vendor.ref(false);
    const queryStatusNum = common_vendor.ref(0);
    const getUserInfo = async () => {
      try {
        const res = await api_index.getUserInfoApi({
          merchantGuid: userStore.merchantGuid
        });
        if (res.code === 0) {
          Object.assign(userInfo, res.data);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/my/sub-list.vue:172", "获取用户信息失败:", error);
      }
    };
    const getCreatorList = async () => {
      try {
        const res = await api_index.getSubscriptionListApi({
          merchantGuid: userStore.merchantGuid
        });
        if (res.code === 0) {
          creatorList.value = res.data.creators;
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/my/sub-list.vue:201", "获取创作者列表失败:", error);
      }
    };
    const rule = common_vendor.reactive({
      rule1: "",
      rule2: "",
      rule_notice: ""
    });
    const getSubscriptionRule = async () => {
      try {
        const res = await api_index.getSubscriptionRuleApi({
          merchantGuid: userStore.merchantGuid
        });
        if (res.code === 0) {
          rule.rule1 = res.data.zhuanshu.rule1;
          rule.rule2 = res.data.zhuanshu.rule2;
          rule.rule_notice = res.data.zhuanshu.rule_notice;
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/my/sub-list.vue:221", "获取规则失败:", error);
      }
    };
    const showCreatorModal = async () => {
      common_vendor.index.showToast({
        title: "暂未开放",
        icon: "none"
      });
      return;
    };
    const closeModal = () => {
      showSubscribeModal.value = false;
    };
    const viewCreatorDetail = (creator) => {
      common_vendor.index.__f__("log", "at pages/my/sub-list.vue:244", "查看创作者详情:", creator);
    };
    const handleCreatorSubscribe = async (creator) => {
      if (isIos.value) {
        common_vendor.index.showToast({
          title: "IOS暂不支持",
          icon: "none"
        });
        return;
      }
      if (creator.isSubscribed) {
        common_vendor.index.showToast({
          title: "已订阅该创作者",
          icon: "none"
        });
        return;
      }
      if (!userStore.userToken) {
        common_vendor.index.showToast({
          title: "请先登录",
          icon: "none"
        });
        return;
      }
      try {
        common_vendor.index.showLoading({
          title: "正在创建订单...",
          mask: true
        });
        const payInfo = await api_index.subscribeCreatorApi({
          merchantGuid: userStore.merchantGuid,
          creatorGuid: creator.guid,
          payEnv: "xcx"
        });
        common_vendor.index.hideLoading();
        api_common.miniPay(payInfo.data.payInfo).then(
          async () => {
            queryPayStatus(payInfo.data.orderNo, queryStatusNum.value);
          },
          (res) => {
            common_vendor.index.showToast({
              title: res.msg || "支付失败",
              icon: "none"
            });
          }
        );
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at pages/my/sub-list.vue:302", "创建订单失败:", error);
        common_vendor.index.showToast({
          title: error.message || "创建订单失败",
          icon: "none"
        });
      }
    };
    const queryPayStatus = async (orderNo, number) => {
      number++;
      try {
        const orderInfo = await api_index.querySubscriptionOrderApi({
          orderNo
        });
        if (orderInfo.data.isPaid) {
          common_vendor.index.showToast({
            title: "订阅成功",
            icon: "success"
          });
          getCreatorList();
          closeModal();
        } else {
          if (number > 12) {
            common_vendor.index.showToast({
              title: "支付超时",
              icon: "none"
            });
          } else {
            setTimeout(() => {
              queryPayStatus(orderNo, number);
            }, 2e3);
          }
        }
      } catch (error) {
        common_vendor.index.showToast({
          title: error.msg || "查询支付状态失败",
          icon: "none"
        });
      }
    };
    common_vendor.onLoad(() => {
      const systemInfo = common_vendor.index.getSystemInfoSync();
      if (systemInfo.osName === "ios") {
        isIos.value = true;
      }
      if (userStore.userToken) {
        getUserInfo();
        getSubscriptionRule();
      }
    });
    common_vendor.onShow(() => {
      if (userStore.userToken) {
        getUserInfo();
        getSubscriptionRule();
      }
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: icon1,
        b: common_vendor.t(rule.rule1),
        c: icon2,
        d: common_vendor.t(rule.rule2),
        e: icon3,
        f: common_vendor.t(rule.rule_notice),
        g: userInfo.headImgUrl || defaultAvatar,
        h: common_vendor.t(userInfo.nickname || "用户Aric"),
        i: common_vendor.t(userInfo.chat_count),
        j: common_vendor.t(creatorSubscriptions.value.length),
        k: common_vendor.o(showCreatorModal),
        l: showSubscribeModal.value
      }, showSubscribeModal.value ? {
        m: common_vendor.o(closeModal),
        n: common_vendor.f(creatorList.value, (creator, k0, i0) => {
          return {
            a: creator.creatorAvatar,
            b: common_vendor.t(creator.creatorName),
            c: common_vendor.t(creator.creatorDesc),
            d: common_vendor.t(creator.agentCount),
            e: common_vendor.t(creator.subscriptionPriceYuan),
            f: common_vendor.o(($event) => viewCreatorDetail(creator), creator.guid),
            g: common_vendor.t(creator.isSubscribed ? "已订阅" : isIos.value ? "IOS暂不支持" : "订阅"),
            h: creator.isSubscribed ? 1 : "",
            i: common_vendor.o(($event) => handleCreatorSubscribe(creator), creator.guid),
            j: creator.guid
          };
        }),
        o: isIos.value ? 1 : "",
        p: common_vendor.o(() => {
        }),
        q: common_vendor.o(closeModal)
      } : {});
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-287e3f54"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/my/sub-list.js.map
