{"version": 3, "file": "works-list.js", "sources": ["pages/my/works-list.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvbXkvd29ya3MtbGlzdC52dWU"], "sourcesContent": ["<template>\r\n\t<view class=\"my-works-container\">\r\n\t\t<!-- 作品内容区域 -->\r\n\t\t<z-paging\r\n\t\t\tref=\"paging\"\r\n\t\t\tv-model=\"worksList\"\r\n\t\t\t@query=\"queryList\"\r\n\t\t\t:refresher-enabled=\"true\"\r\n\t\t\t:auto=\"true\"\r\n\t\t>\r\n\t\t\t<view class=\"works-content\">\r\n\t\t\t\t<!-- 数字人网格 -->\r\n\t\t\t\t<view class=\"avatar-grid\">\r\n\t\t\t\t\t<view\r\n\t\t\t\t\t\tv-for=\"work in worksList\"\r\n\t\t\t\t\t\t:key=\"work.orderNo\"\r\n\t\t\t\t\t\tclass=\"avatar-card\"\r\n\t\t\t\t\t\t:class=\"{ selected: isEditMode && selectedWorks.includes(work.orderNo) }\"\r\n\t\t\t\t\t\t@tap=\"handleWorkTap(work)\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<image class=\"avatar-image\" :src=\"work.previewUrl\" mode=\"aspectFill\" />\r\n\t\t\t\t\t\t<view v-if=\"isEditMode && selectedWorks.includes(work.orderNo)\" class=\"check-mark\">\r\n\t\t\t\t\t\t\t<image class=\"check-icon\" src=\"/static/my/template_select.png\" />\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</z-paging>\r\n\r\n\t\t<!-- 底部操作按钮 -->\r\n\t\t<view class=\"bottom-actions\">\r\n\t\t\t<!-- 编辑模式 -->\r\n\t\t\t<template v-if=\"!isEditMode\">\r\n\t\t\t\t<view class=\"action-button edit\" @tap=\"enterEditMode\">\r\n\t\t\t\t\t<image class=\"edit-icon\" src=\"/static/my/<EMAIL>\" mode=\"aspectFit\" />\r\n\t\t\t\t\t<text class=\"action-text\">编辑</text>\r\n\t\t\t\t</view>\r\n\t\t\t</template>\r\n\t\t\t<!-- 编辑状态下的按钮 -->\r\n\t\t\t<template v-else>\r\n\t\t\t\t<view class=\"action-button cancel\" @tap=\"cancelEdit\">\r\n\t\t\t\t\t<text class=\"action-text\">取消</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"action-button delete\" @tap=\"deleteSelected\">\r\n\t\t\t\t\t<text class=\"action-text\">删除</text>\r\n\t\t\t\t</view>\r\n\t\t\t</template>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref } from 'vue'\r\nimport { worksListApi, deleteWorksApi } from '@/api/index.js'\r\nimport { useUserStore } from '@/stores/user.js'\r\n\r\nconst userStore = useUserStore()\r\n\r\n// 作品列表数据\r\nconst worksList = ref([])\r\nconst paging = ref(null)\r\n\r\n// 编辑模式状态\r\nconst isEditMode = ref(false)\r\nconst selectedWorks = ref([])\r\n\r\n// 分页查询作品列表\r\nconst queryList = async (page, pageSize) => {\r\n\ttry {\r\n\t\tconst res = await worksListApi({\r\n\t\t\tmerchantGuid: userStore.merchantGuid,\r\n\t\t\tpage: page,\r\n\t\t\tpageSize: pageSize\r\n\t\t})\r\n\t\t// 使用z-paging的complete方法处理数据\r\n\t\tpaging.value.complete(res.data.list || [])\r\n\t} catch (error) {\r\n\t\tconsole.error('获取作品列表失败:', error)\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '加载失败',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\tpaging.value.complete(false)\r\n\t}\r\n}\r\n\r\n// 进入编辑模式\r\nconst enterEditMode = () => {\r\n\tisEditMode.value = true\r\n\tselectedWorks.value = []\r\n}\r\n\r\n// 取消编辑模式\r\nconst cancelEdit = () => {\r\n\tisEditMode.value = false\r\n\tselectedWorks.value = []\r\n}\r\n\r\n// 处理作品点击\r\nconst handleWorkTap = (work) => {\r\n\tif (!isEditMode.value) {\r\n\t\t// 非编辑模式下，跳转到作品详情或播放页面\r\n\t\tif (work.workStatus === 'fail'){\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '作品生成失败，无法查看',\r\n\t\t\t\ticon: 'none'\r\n\t\t\t})\r\n\t\t\treturn\r\n\t\t}\r\n\t\tif (work.workStatus === 'doing'){\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '作品加速处理中，无法查看',\r\n\t\t\t\ticon: 'none'\r\n\t\t\t})\r\n\t\t\treturn\r\n\t\t}\r\n\t\tuni.navigateTo({\r\n\t\t\turl: `/pages/my/video-complete?orderNo=${work.orderNo}`\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\r\n\t// 编辑模式下，切换选择状态\r\n\tconst workId = work.orderNo\r\n\tconst selectedIndex = selectedWorks.value.indexOf(workId)\r\n\tif (selectedIndex > -1) {\r\n\t\tselectedWorks.value.splice(selectedIndex, 1)\r\n\t} else {\r\n\t\tselectedWorks.value.push(workId)\r\n\t}\r\n}\r\n\r\n// 删除选中的作品\r\nconst deleteSelected = () => {\r\n\tif (selectedWorks.value.length === 0) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请先选择要删除的作品',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\r\n\tuni.showModal({\r\n\t\ttitle: '确认删除',\r\n\t\tcontent: `确定要删除选中的${selectedWorks.value.length}个作品吗？`,\r\n\t\tsuccess: async (res) => {\r\n\t\t\tif (res.confirm) {\r\n\t\t\t\ttry {\r\n\t\t\t\t\t// 调用删除接口\r\n\t\t\t\t\tconst deleteRes = await deleteWorksApi({\r\n\t\t\t\t\t\tmerchantGuid: userStore.merchantGuid,\r\n\t\t\t\t\t\torderNos: selectedWorks.value\r\n\t\t\t\t\t})\r\n\r\n\t\t\t\t\t// 删除成功后刷新列表\r\n\t\t\t\t\tselectedWorks.value = []\r\n\t\t\t\t\tisEditMode.value = false\r\n\t\t\t\t\tpaging.value.reload()\r\n\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: deleteRes.data.message,\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('删除作品失败:', error)\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '删除失败',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t})\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.my-works-container {\r\n\tmin-height: 100vh;\r\n\tbackground: #ffffff;\r\n\tpadding-bottom: 160rpx;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n}\r\n\r\n// 作品内容区域\r\n.works-content {\r\n\tpadding: 32rpx 30rpx;\r\n}\r\n\r\n// 头像网格\r\n.avatar-grid {\r\n\tdisplay: flex;\r\n\tflex-wrap: wrap;\r\n\tmargin-bottom: 60rpx;\r\n}\r\n\r\n.avatar-card {\r\n\tposition: relative;\r\n\toverflow: hidden;\r\n\twidth: 220rpx;\r\n\theight: 350rpx;\r\n\tborder-radius: 15rpx;\r\n\tmargin-bottom: 15rpx;\r\n\tborder: 3rpx solid #ffffff00;\r\n\tbox-sizing: border-box;\r\n\r\n\t.avatar-image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tobject-fit: cover;\r\n\t}\r\n\r\n\t.check-mark {\r\n\t\tposition: absolute;\r\n\t\ttop: 16rpx;\r\n\t\tright: 16rpx;\r\n\t\twidth: 48rpx;\r\n\t\theight: 48rpx;\r\n\t\tbackground: #5380F2;\r\n\t\tborder-radius: 50%;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\r\n\t\t.check-icon {\r\n\t\t\twidth: 40rpx;\r\n\t\t\theight: 40rpx;\r\n\t\t}\r\n\t}\r\n\r\n\t&.selected {\r\n\t\tborder: 3rpx solid #5380F2;\r\n\t}\r\n}\r\n.avatar-card:not(:nth-child(3n)) {\r\n\tmargin-right: 15rpx;\r\n}\r\n\r\n.bottom-actions {\r\n\tposition: fixed;\r\n\tbottom: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tdisplay: flex;\r\n\tbackground: #ffffff;\r\n\tpadding: 32rpx;\r\n\tborder-top: 1rpx solid #f0f0f0;\r\n\tgap: 24rpx;\r\n\r\n\t.action-button {\r\n\t\tflex: 1;\r\n\t\theight: 96rpx;\r\n\t\tborder-radius: 48rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tgap: 8rpx;\r\n\r\n\t\t&.edit {\r\n\t\t\t.edit-icon {\r\n\t\t\t\twidth: 32rpx;\r\n\t\t\t\theight: 32rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.action-text {\r\n\t\t\t\tcolor: #333333;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&.cancel {\r\n\t\t\tbackground: #f8f8f8;\r\n\r\n\t\t\t.action-text {\r\n\t\t\t\tcolor: #666666;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&.delete {\r\n\t\t\tbackground: #ffffff;\r\n\t\t\tborder: 2rpx solid #FF4757;\r\n\r\n\t\t\t.action-text {\r\n\t\t\t\tcolor: #FF4757;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.action-text {\r\n\t\t\tfont-size: 32rpx;\r\n\t\t\tfont-weight: 500;\r\n\t\t}\r\n\t}\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'E:/youngProject/agent-mini-ui/pages/my/works-list.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useUserStore", "ref", "worksListApi", "uni", "deleteWorksApi"], "mappings": ";;;;;;;;;;;;;;;;AAwDA,UAAM,YAAYA,YAAAA,aAAc;AAGhC,UAAM,YAAYC,cAAG,IAAC,EAAE;AACxB,UAAM,SAASA,cAAG,IAAC,IAAI;AAGvB,UAAM,aAAaA,cAAG,IAAC,KAAK;AAC5B,UAAM,gBAAgBA,cAAG,IAAC,EAAE;AAG5B,UAAM,YAAY,OAAO,MAAM,aAAa;AAC3C,UAAI;AACH,cAAM,MAAM,MAAMC,uBAAa;AAAA,UAC9B,cAAc,UAAU;AAAA,UACxB;AAAA,UACA;AAAA,QACH,CAAG;AAED,eAAO,MAAM,SAAS,IAAI,KAAK,QAAQ,EAAE;AAAA,MACzC,SAAQ,OAAO;AACfC,sBAAAA,sDAAc,aAAa,KAAK;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACT,CAAG;AACD,eAAO,MAAM,SAAS,KAAK;AAAA,MAC3B;AAAA,IACF;AAGA,UAAM,gBAAgB,MAAM;AAC3B,iBAAW,QAAQ;AACnB,oBAAc,QAAQ,CAAE;AAAA,IACzB;AAGA,UAAM,aAAa,MAAM;AACxB,iBAAW,QAAQ;AACnB,oBAAc,QAAQ,CAAE;AAAA,IACzB;AAGA,UAAM,gBAAgB,CAAC,SAAS;AAC/B,UAAI,CAAC,WAAW,OAAO;AAEtB,YAAI,KAAK,eAAe,QAAO;AAC9BA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACV,CAAI;AACD;AAAA,QACA;AACD,YAAI,KAAK,eAAe,SAAQ;AAC/BA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACV,CAAI;AACD;AAAA,QACA;AACDA,sBAAAA,MAAI,WAAW;AAAA,UACd,KAAK,oCAAoC,KAAK,OAAO;AAAA,QACxD,CAAG;AACD;AAAA,MACA;AAGD,YAAM,SAAS,KAAK;AACpB,YAAM,gBAAgB,cAAc,MAAM,QAAQ,MAAM;AACxD,UAAI,gBAAgB,IAAI;AACvB,sBAAc,MAAM,OAAO,eAAe,CAAC;AAAA,MAC7C,OAAQ;AACN,sBAAc,MAAM,KAAK,MAAM;AAAA,MAC/B;AAAA,IACF;AAGA,UAAM,iBAAiB,MAAM;AAC5B,UAAI,cAAc,MAAM,WAAW,GAAG;AACrCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACT,CAAG;AACD;AAAA,MACA;AAEDA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,SAAS,WAAW,cAAc,MAAM,MAAM;AAAA,QAC9C,SAAS,OAAO,QAAQ;AACvB,cAAI,IAAI,SAAS;AAChB,gBAAI;AAEH,oBAAM,YAAY,MAAMC,yBAAe;AAAA,gBACtC,cAAc,UAAU;AAAA,gBACxB,UAAU,cAAc;AAAA,cAC9B,CAAM;AAGD,4BAAc,QAAQ,CAAE;AACxB,yBAAW,QAAQ;AACnB,qBAAO,MAAM,OAAQ;AAErBD,4BAAAA,MAAI,UAAU;AAAA,gBACb,OAAO,UAAU,KAAK;AAAA,gBACtB,MAAM;AAAA,cACZ,CAAM;AAAA,YACD,SAAQ,OAAO;AACfA,4BAAAA,uDAAc,WAAW,KAAK;AAC9BA,4BAAAA,MAAI,UAAU;AAAA,gBACb,OAAO;AAAA,gBACP,MAAM;AAAA,cACZ,CAAM;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,MACH,CAAE;AAAA,IACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5KA,GAAG,WAAW,eAAe;"}