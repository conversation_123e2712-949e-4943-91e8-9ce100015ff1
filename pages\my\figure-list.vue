<template>
	<view class="my-figure-container">
		<!-- 作品内容区域 -->
		<z-paging
			ref="paging"
			v-model="figureList"
			@query="queryList"
			:refresher-enabled="true"
			:auto="true"
		>
			<view class="figure-content">
				<!-- 作品卡片区域 -->
				<view class="figure-grid">
					<!-- 创建新作品卡片 -->
					<view class="figure-item figure-upload" @tap="showUploadModal">
	                    <image class="create-icon" src="/static/my/figure_add.png" />
	                    <text class="create-title">定制数字人</text>
	                    <!-- <text class="create-subtitle">免费次数: 2/3</text> -->
					</view>

					<!-- 数字人作品卡片列表 -->
					<view
						v-for="figure in figureList"
						:key="figure.personGuid"
						class="figure-item figure-list"
						@tap="handleFigureTap(figure)"
					>
						<view class="avatar-container" :class="{ selected: isEditMode && selectedFigure === figure.personGuid }">
							<view class="avatar-bg">
								<image class="avatar-image" :src="figure.picUrl" mode="heightFix" />
							</view>
							<view class="badge">{{ figure.statusText }}</view>
							<view v-if="isEditMode" class="check-mark">
	                            <image class="check-icon" v-if="selectedFigure === figure.personGuid" src="/static/my/template_select.png" />
	                            <image class="check-icon" v-else src="/static/my/template_select1.png" />
							</view>
						</view>
						<view class="avatar-info">
							<view class="avatar-name">{{ figure.personName }}</view>
	                        <!-- <image v-if="!isEditMode" class="more-options" src="/static/my/figure_operate.png" @tap.stop="showOptions(figure)" /> -->
						</view>
					</view>
				</view>
			</view>
		</z-paging>

		<!-- 底部操作按钮 -->
		<view class="bottom-actions">
			<!-- 编辑模式 -->
			<template v-if="!isEditMode">
				<view class="action-button edit" @tap="enterEditMode">
					<image class="edit-icon" src="/static/my/<EMAIL>" mode="aspectFit" />
					<text class="action-text">编辑</text>
				</view>
			</template>
			<!-- 编辑状态下的按钮 -->
			<template v-else>
				<view class="action-button cancel" @tap="cancelEdit">
					<text class="action-text">取消</text>
				</view>
				<view class="action-button delete" @tap="deleteSelected">
					<text class="action-text">删除</text>
				</view>
			</template>
		</view>

		<!-- 上传视频弹窗 -->
		<view v-if="showUploadPopup" class="upload-modal-overlay">
			<view class="upload-modal-content" @tap.stop>
				<!-- 关闭按钮 -->
				<view class="close-btn" @tap="hideUploadModal">
					<image class="close-icon" src="/static/my/popup-close.png" mode="aspectFit" />
				</view>

				<!-- 弹窗标题 -->
				<text class="upload-title">请上传您的视频完成数字人制作</text>

				<!-- 提示内容 -->
				<view class="upload-tips">
					<view class="tip-item">
						<text class="tip-title">录制时可以自然微笑，保持放松</text>
						<text class="tip-desc">您好!我现在感觉很好，语调很轻松，我很有信心能做好这次视频录制。 我现在就在镜头前，准备开始。</text>
					</view>

					<view class="tip-item">
						<text class="tip-title">闭上嘴，用鼻子呼吸，停顿1s</text>
						<text class="tip-desc">光线很好，我的脸上没有任何刺眼的阴影，我的发音很清晰，感觉很放松，我会做一些自然、轻微的手部动作。</text>
					</view>

					<view class="tip-item">
						<text class="tip-title">闭上嘴，用鼻子呼吸，停顿1s</text>
						<text class="tip-desc">一些细微的姿态动作会让我看起来更自然更放松。 在整个视频录制过程中，我不会移动我的身体，也不会做太剧烈的任何动作。</text>
					</view>
				</view>

				<!-- 上传按钮 -->
				<view class="upload-btn" @tap="uploadVideo">
					<text class="upload-btn-text">上传视频</text>
				</view>
			</view>
		</view>

	</view>
</template>

<script setup>
import { ref } from 'vue'
import { getMyPersonListApi, deletePersonApi, uploadVideoApi, createPersonkApi } from '@/api/index.js'
import { useUserStore } from '@/stores/user.js'
import base from '@/config/config.js'

const userStore = useUserStore()

// 响应式数据
const showUploadPopup = ref(false)
const figureList = ref([])
const paging = ref(null)

// 编辑模式状态
const isEditMode = ref(false)
const selectedFigure = ref(null) // 改为单选

// 分页查询形象列表
const queryList = async (page, pageSize) => {
	try {
		const res = await getMyPersonListApi({
			merchantGuid: userStore.merchantGuid,
			page: page,
			pageSize: pageSize
		})
		// 使用z-paging的complete方法处理数据
		paging.value.complete(res.data.list || [])
	} catch (error) {
		console.error('获取形象列表失败:', error)
		uni.showToast({
			title: '加载失败',
			icon: 'none'
		})
		paging.value.complete(false)
	}
}

// 进入编辑模式
const enterEditMode = () => {
	isEditMode.value = true
	selectedFigure.value = null
}

// 取消编辑模式
const cancelEdit = () => {
	isEditMode.value = false
	selectedFigure.value = null
}

// 处理形象点击
const handleFigureTap = (figure) => {
	if (!isEditMode.value) {
		return
	}

	// 编辑模式下，切换选择状态（单选）
	const personGuid = figure.personGuid
	if (selectedFigure.value === personGuid) {
		// 如果已选中，则取消选择
		selectedFigure.value = null
	} else {
		// 选择当前项（单选，会覆盖之前的选择）
		selectedFigure.value = personGuid
	}
}

// 删除选中的形象
const deleteSelected = () => {
	if (!selectedFigure.value) {
		uni.showToast({
			title: '请先选择要删除的形象',
			icon: 'none'
		})
		return
	}

	uni.showModal({
		title: '确认删除',
		content: '确定要删除选中的形象吗？',
		success: async (res) => {
			if (res.confirm) {
				try {
					// 调用删除接口
					await deletePersonApi({
						merchantGuid: userStore.merchantGuid,
						personGuid: selectedFigure.value
					})

					// 删除成功后刷新列表
					selectedFigure.value = null
					isEditMode.value = false
					paging.value.reload()

					uni.showToast({
						title: '删除成功',
						icon: 'success'
					})
				} catch (error) {
					console.error('删除形象失败:', error)
					uni.showToast({
						title: '删除失败',
						icon: 'none'
					})
				}
			}
		}
	})
}

// 显示上传弹窗
const showUploadModal = () => {
	showUploadPopup.value = true
}

// 隐藏上传弹窗
const hideUploadModal = () => {
	showUploadPopup.value = false
}

// 上传视频
const uploadVideo = () => {
	uni.chooseVideo({
		sourceType: ['camera', 'album'],
		maxDuration: 60,
		success: async (res) => {
			console.log('选择的视频:', res.tempFilePath)

			// 显示上传中提示
			uni.showLoading({
				title: '上传中...',
				mask: true
			})

			try {
				// 上传视频文件
				const uploadRes = await new Promise((resolve, reject) => {
					uni.uploadFile({
						url: `${base.baseUrl}user/api.userinfo/uploadVideo`,
						name: 'video',
						fileType: 'video',
						filePath: res.tempFilePath,
						success: (uploadResult) => {
							resolve(uploadResult)
						},
						fail: (error) => {
							reject(error)
						}
					})
				})

				// 解析上传结果
				const uploadData = JSON.parse(uploadRes.data)
				console.log('视频上传结果:', uploadData)

				if (uploadData.code === 0) {
					// 上传成功，调用创建形象接口
					const createRes = await createPersonkApi({
						merchantGuid: userStore.merchantGuid,
						materialVideoUrl: uploadData.data, // 使用上传返回的视频URL
						personName: `定制形象`, // 生成默认名称
					})

					if (createRes.code === 0) {
						uni.hideLoading()
						uni.showToast({
							title: '形象创建成功',
							icon: 'success'
						})

						// 刷新列表
						paging.value.reload()
						hideUploadModal()
					} else {
						throw new Error(createRes.msg || '创建形象失败')
					}
				} else {
					throw new Error(uploadData.msg || '视频上传失败')
				}
			} catch (error) {
				uni.hideLoading()
				console.error('上传或创建失败:', error)
				uni.showToast({
					title: error.message || '操作失败，请重试',
					icon: 'none'
				})
			}
		},
		fail: (err) => {
			console.log('选择视频失败:', err)
			uni.showToast({
				title: '选择视频失败',
				icon: 'none'
			})
		}
	})
}

const showOptions = (figure) => {
	uni.showActionSheet({
		itemList: ['编辑', '复制', '分享'],
		success: (res) => {
			const actions = ['编辑', '复制', '分享']
			uni.showToast({
				title: actions[res.tapIndex],
				icon: 'none',
				duration: 1500
			})
		}
	})
}
</script>

<style lang="scss" scoped>
.my-figure-container {
	min-height: 100vh;
	background: #ffffff;
	padding-bottom: 160rpx;
	display: flex;
	flex-direction: column;
}

.figure-content {
	padding: 32rpx 30rpx;
	flex: 1;
}

.figure-grid {
	display: flex;
    flex-wrap: wrap;
    .figure-upload{
        width: 330rpx;
        height: 330rpx;
        display: flex;
        border-radius: 15rpx;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background: #FAFAFA;
        border: 2rpx dashed #DBDBDB !important;

        .create-icon {
            width: 40rpx;
            height: 40rpx;
            display: block;
            margin-bottom: 16rpx;
        }

        .create-title {
            font-size: 26rpx;
            color: #999999;
            margin-bottom: 4rpx;
        }

        .create-subtitle {
            font-size: 24rpx;
            color: #FD8D2B;
        }
    }
    .figure-item{
        margin-bottom: 40rpx;
        position: relative;
    }
    .figure-list {
        // background: #ffffff;
        width: 326rpx;

        .avatar-container {
            position: relative;
            flex: 1;
            margin-bottom: 24rpx;
            border-radius: 16rpx;
            overflow: hidden;
            border: 3rpx solid transparent;

            &.selected {
                border: 3rpx solid #2A64F6;
            }

            .avatar-bg {
                width: 330rpx;
                height: 330rpx;
                background: #000000;
                display: flex;
                align-items: center;
                justify-content: center;

                .avatar-image {
                    height: 100%;
                    width: auto;
                    max-width: 100%;
                }
            }

            .badge {
                position: absolute;
                top: 16rpx;
                left: 16rpx;
                background: linear-gradient( 132deg, #FFFFB5 0%, #FDDB66 49%, #FAF7B0 100%);
                color: #4A3900;
                font-size: 24rpx;
                padding: 8rpx 16rpx;
                border-radius: 16rpx;
                font-weight: 600;
            }

            .check-mark {
                position: absolute;
                top: 16rpx;
                right: 16rpx;
                width: 48rpx;
                height: 48rpx;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;

                .check-icon {
                    width: 40rpx;
                    height: 40rpx;
                }
            }
        }

        .avatar-info {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20rpx;

            .avatar-name {
                font-size: 28rpx;
                color: #999999;
                font-weight: 500;
                width: calc(100% - 40rpx);
                // 一行隐藏
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            .more-options {
                width: 30rpx;
                height: 30rpx;
            }
        }
    }
    .figure-item:not(:nth-child(2n-1)){
        margin-left: 30rpx;
    }
}

.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	display: flex;
	background: #ffffff;
	padding: 32rpx;
	border-top: 1rpx solid #f0f0f0;
	gap: 24rpx;

	.action-button {
		flex: 1;
		height: 96rpx;
		border-radius: 48rpx;
		display: flex;
		align-items: center;
		justify-content: center;

		&.edit {
			.edit-icon {
				width: 32rpx;
				height: 32rpx;
				margin-right: 12rpx;
			}

			.action-text {
				color: #666666;
			}
		}

		&.cancel {
			background: #f8f8f8;
			.action-text {
				color: #666666;
			}
		}

		&.delete {
			background: #ffffff;
			border: 2rpx solid #FA5151;

			.action-text {
				color: #FA5151;
			}
		}

		.action-text {
			font-size: 32rpx;
			font-weight: 500;
		}
	}
}

// 上传弹窗样式
.upload-modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.6);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 9999;
}

.upload-modal-content {
	background: #ffffff;
	border-radius: 32rpx;
	padding: 60rpx 40rpx 50rpx;
	width: 640rpx;
	max-height: 80vh;
	position: relative;
    box-sizing: border-box;

	.close-btn {
		position: absolute;
		bottom: -80rpx;
		width: 45rpx;
		height: 45rpx;
		left: 50%;
        transform: translateX(-50%);

		.close-icon {
			width: 45rpx;
			height: 45rpx;
		}
	}

	.upload-title {
		display: block;
		font-size: 35rpx;
        font-weight: 500;
		color: #2A64F6;
		text-align: center;
		margin-bottom: 48rpx;
		line-height: 1.4;
	}

	.upload-tips {
		margin-bottom: 48rpx;

		.tip-item {
			margin-bottom: 20rpx;
            background: #F4F6F8;
            border-radius: 15rpx;
            padding: 30rpx;

			&:last-child {
				margin-bottom: 0;
			}

			.tip-title {
				display: block;
				font-size: 30rpx;
				font-weight: 500;
				color: #222222;
				margin-bottom: 6rpx;
			}

			.tip-desc {
				display: block;
				font-size: 22rpx;
				color: #999999;
				line-height: 1.6;
			}
		}
	}

	.upload-btn {
		width: 100%;
		height: 96rpx;
		background: #2A64F6;
		border-radius: 48rpx;
		display: flex;
		align-items: center;
		justify-content: center;

		.upload-btn-text {
			font-size: 32rpx;
			color: #ffffff;
			font-weight: 600;
		}
	}
}

.modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 9999;
}

.modal-content {
	background: #ffffff;
	border-radius: 24rpx;
	padding: 48rpx 32rpx;
	width: 600rpx;
	max-width: 90vw;

	.modal-title {
		display: block;
		font-size: 36rpx;
		font-weight: 600;
		color: #333333;
		text-align: center;
		margin-bottom: 24rpx;
	}

	.modal-message {
		display: block;
		font-size: 28rpx;
		color: #666666;
		text-align: center;
		margin-bottom: 48rpx;
		line-height: 1.5;
	}

	.modal-actions {
		display: flex;
		gap: 24rpx;

		.modal-button {
			flex: 1;
			height: 80rpx;
			border-radius: 40rpx;
			display: flex;
			align-items: center;
			justify-content: center;

			&.cancel {
				background: #f5f5f5;

				.modal-button-text {
					color: #666666;
				}
			}

			&.confirm {
				background: #FF4757;

				.modal-button-text {
					color: #ffffff;
				}
			}

			.modal-button-text {
				font-size: 32rpx;
				font-weight: 600;
			}
		}
	}
}

</style>
