/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.container.data-v-6ddef3fc {
  background: #f7f7f7;
  min-height: 100vh;
  background-repeat: no-repeat;
  background-size: 100% 518rpx;
  width: 100%;
  overflow-x: hidden;
}
.onAdminEdit.data-v-6ddef3fc {
  width: 100%;
  height: 80rpx;
  background-color: #ffffff;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.onAdminEdit .edit-btn.data-v-6ddef3fc {
  border-radius: 10rpx;
  border: 2rpx solid #333333;
  padding: 2rpx 10rpx;
  width: -webkit-fit-content;
  width: fit-content;
  margin-right: 20rpx;
  font-size: 22rpx;
}
.onAdminEdit .edit-btn.on.data-v-6ddef3fc {
  color: #FA5151;
  border-color: #FA5151;
}
.auth-pop-box.data-v-6ddef3fc {
  width: 680rpx;
  padding: 20px;
  box-sizing: border-box;
  background-color: #fff;
  border-radius: 8rpx;
}
.auth-pop-box .title.data-v-6ddef3fc {
  font-size: 28rpx;
  text-align: center;
}
.auth-pop-box .btn.data-v-6ddef3fc {
  margin-top: 20px;
  width: 100%;
  height: 90rpx;
  background: linear-gradient(90deg, #8d40f8 0%, #5e24f5 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 12rpx;
  font-weight: 600;
  font-size: 30rpx;
  color: #ffffff;
}
.on-load-more-box.data-v-6ddef3fc {
  text-align: center;
  padding: 20rpx 0;
  color: #66648a;
  font-size: 24rpx;
}
.chat-main.data-v-6ddef3fc {
  padding-left: 20rpx;
  padding-right: 20rpx;
  padding-top: 40rpx;
  display: flex;
  flex-direction: column;
}
.agent-logo.data-v-6ddef3fc {
  display: flex;
  justify-content: center;
  margin: 30px 0 20px 0;
}
.agent-logo .logo.data-v-6ddef3fc {
  display: block;
  width: 200rpx;
  height: 200rpx;
  border-radius: 50%;
}
.chat-ls.data-v-6ddef3fc {
  padding-bottom: 20px;
  position: relative;
}
.chat-ls .msg-m.data-v-6ddef3fc {
  display: flex;
  padding: 20rpx 0;
  width: 100%;
}
.chat-ls .msg-m .logo-box.data-v-6ddef3fc {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  box-sizing: border-box;
}
.chat-ls .msg-m .logo-box .user-img.data-v-6ddef3fc {
  flex: none;
  display: block;
  width: 100%;
  height: 100%;
  border-radius: 50%;
}
.chat-ls .msg-m .msg-text.data-v-6ddef3fc {
  font-size: 32rpx;
  line-height: 44rpx;
  padding: 18rpx 24rpx;
  box-sizing: border-box;
}
.chat-ls .msg-m .msg-text .rich-text-box .t.data-v-6ddef3fc {
  word-wrap: break-word;
}
.chat-ls .msg-m .msg-text .rich-text-box .richImg.data-v-6ddef3fc {
  width: 100% !important;
  height: auto !important;
}
.chat-ls .msg-m .msg-text .rich-text-box .often-questions.data-v-6ddef3fc {
  margin-top: 30rpx;
}
.chat-ls .msg-m .msg-text .rich-text-box .often-questions .item.data-v-6ddef3fc {
  color: #000;
  font-size: 28rpx;
  padding-left: 30rpx;
  position: relative;
}
.chat-ls .msg-m .msg-text .rich-text-box .often-questions .item.data-v-6ddef3fc::before {
  content: "";
  display: block;
  width: 10rpx;
  height: 10rpx;
  background-color: #000;
  border-radius: 50%;
  position: absolute;
  left: 8rpx;
  top: 50%;
  transform: translateY(-50%);
}
.chat-ls .msg-m .msg-text .rich-text-box .img-list-box.data-v-6ddef3fc {
  margin-top: 10rpx;
}
.chat-ls .msg-m .msg-text .rich-text-box .img-list-box.noWrap .img.data-v-6ddef3fc {
  flex: 1;
  height: auto;
}
.chat-ls .msg-m .msg-text .rich-text-box .img-list-box .img.data-v-6ddef3fc {
  max-width: 100%;
  height: auto;
  display: block;
}
.chat-ls .msg-m .msg-text .img-box .img.data-v-6ddef3fc {
  max-width: 510rpx;
  display: block;
}
.chat-ls .msg-left.data-v-6ddef3fc {
  flex-direction: row;
  position: relative;
}
.chat-ls .msg-left .msg-text.data-v-6ddef3fc {
  margin-left: 16rpx;
  font-size: 32rpx;
  background-color: #F9F9F9;
  box-shadow: 0rpx 3rpx 6rpx 1rpx rgba(35, 0, 131, 0.05);
  border-radius: 0rpx 20rpx 20rpx 20rpx;
  max-width: 680rpx;
  color: #333333;
  margin-top: 50rpx;
  position: relative;
}
.chat-ls .msg-left .msg-text.hasReply.data-v-6ddef3fc {
  margin-top: 0px;
}
.chat-ls .msg-left .msg-text .text-box .link.data-v-6ddef3fc {
  word-break: break-all;
  text-decoration: underline;
  border-bottom: 1px solid #66648a;
}
.chat-ls .msg-left .msg-text .video-box.data-v-6ddef3fc {
  width: 512rpx;
}
.chat-ls .msg-left .msg-text .video-box .video-dom.data-v-6ddef3fc {
  width: 100%;
  height: 300rpx;
}
.chat-ls .msg-left .msg-text .mini-box.data-v-6ddef3fc {
  width: 512rpx;
}
.chat-ls .msg-left .msg-text .mini-box .btn.data-v-6ddef3fc {
  width: 100%;
  font-size: 26rpx;
  height: 70rpx;
  line-height: 70rpx;
  text-align: center;
  transition: background 0.3s;
  color: #ffffff;
  background: linear-gradient(90deg, #8D40F8 0%, #5E24F5 100%);
  border-radius: 12rpx 12rpx 12rpx 12rpx;
}
.chat-ls .msg-left .other-box.data-v-6ddef3fc {
  border-top: 1px solid #ECECEC;
  padding-top: 20rpx;
  margin-top: 20rpx;
  display: flex;
}
.chat-ls .msg-left .other-box .icon.data-v-6ddef3fc {
  display: block;
  width: 60rpx;
  height: 60rpx;
  margin-right: 8rpx;
}
.chat-ls .msg-left .other-box .text-btn.data-v-6ddef3fc {
  background-color: #E7EDFA;
  color: #2963F6;
  font-size: 24rpx;
  height: 60rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 16rpx;
  margin-left: auto;
}
.chat-ls .msg-left .ai-reply-msg.data-v-6ddef3fc {
  color: #66648a;
  font-size: 26rpx;
  padding-left: 16rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  position: absolute;
  top: -50rpx;
  left: 0;
}
.chat-ls .msg-right.data-v-6ddef3fc {
  flex-direction: row-reverse;
  position: relative;
}
.chat-ls .msg-right .msg-text.data-v-6ddef3fc {
  margin-right: 16rpx;
  font-size: 32rpx;
  color: #fff;
  background-color: #5380F2;
  box-shadow: 0rpx 3rpx 6rpx 1rpx rgba(35, 0, 131, 0.05);
  border-radius: 20rpx 0rpx 20rpx 20rpx;
  max-width: 560rpx;
  word-break: break-all;
  word-wrap: break-word;
}
.sy-end-box.data-v-6ddef3fc {
  padding: 20px 0 30px 0;
  background-color: #fff;
  font-size: 30rpx;
  text-align: center;
  color: #2A64F6;
  z-index: 9;
}
.sy-end-box .end-btn.data-v-6ddef3fc {
  width: 530rpx;
  margin: 20px auto 0 auto;
  height: 90rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #2A64F6;
  border-radius: 50rpx 50rpx 50rpx 50rpx;
  color: #fff;
}
.submit-box.data-v-6ddef3fc {
  width: 100%;
  font-size: 30rpx;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  background-color: #fff;
  position: relative;
  padding-top: 20rpx;
  padding-left: 30rpx;
  padding-right: 30rpx;
  /* 为兼容某些Android版本微信小程序，添加最小padding保障 */
  padding-bottom: calc(env(safe-area-inset-bottom, 40rpx) - 20rpx);
  /* 降级方案：当env()不支持时使用固定值 */
  padding-bottom: 40rpx;
}
.submit-box .util-question-box.data-v-6ddef3fc {
  display: flex;
  overflow-x: scroll;
  overflow-y: hidden;
  margin-top: 20rpx;
}
.submit-box .util-question-box .item.data-v-6ddef3fc {
  display: flex;
  background-color: #efefff;
  border-radius: 12rpx;
  padding: 19rpx;
  margin-right: 20rpx;
  align-items: center;
  width: -webkit-fit-content;
  width: fit-content;
  flex: 0 0 auto;
}
.submit-box .util-question-box .item .icon.data-v-6ddef3fc {
  display: block;
  width: 32rpx;
  height: 32rpx;
}
.submit-box .util-question-box .item .text.data-v-6ddef3fc {
  margin-left: 6rpx;
  font-size: 26rpx;
  background: linear-gradient(95deg, #40aaf8 0%, #6732f6 52%, #5e24f5 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.submit-box .sub-input-box.data-v-6ddef3fc {
  display: flex;
  align-items: center;
  border-radius: 12rpx;
  padding: 10rpx 0px;
  background-color: #F7F7F7;
  position: relative;
}
.submit-box .sub-input-box.mt.data-v-6ddef3fc {
  margin-top: 20rpx;
}
.submit-box .sub-input-box .recode-loading-box.data-v-6ddef3fc {
  position: absolute;
  width: 100%;
  height: 100%;
  display: flex;
  background: linear-gradient(136deg, #7531F6 0%, #686BF2 100%);
  justify-content: center;
  align-items: center;
  border-radius: 12rpx;
}
.submit-box .sub-input-box .recode-loading-box.is-close-send.data-v-6ddef3fc {
  background: linear-gradient(136deg, #d45757 0%, #ca3333 100%);
}
.submit-box .sub-input-box .spinner-title.data-v-6ddef3fc {
  position: absolute;
  width: 100%;
  text-align: center;
  top: -40px;
  left: 0;
  z-index: 9;
}
.submit-box .sub-input-box .spinner.data-v-6ddef3fc {
  --accent: #ffffff;
  --max-scale: 4;
  --speed: 0.2;
  display: flex;
  gap: 0.5em;
}
.submit-box .sub-input-box .spinner .sub-spinner.data-v-6ddef3fc {
  display: block;
  background-color: var(--accent);
  box-shadow: 1px 1px 5px 0.2px var(--accent);
  width: 1px;
  height: 0.4em;
}
.submit-box .sub-input-box .spinner .spinner-part-0.data-v-6ddef3fc {
  animation: load432-6ddef3fc calc(1s / var(--speed)) linear infinite;
}
.submit-box .sub-input-box .spinner .spinner-part-1.data-v-6ddef3fc {
  animation: load432-6ddef3fc calc(0.16s / var(--speed)) linear infinite;
}
.submit-box .sub-input-box .spinner .spinner-part-2.data-v-6ddef3fc {
  animation: load432-6ddef3fc calc(0.4s / var(--speed)) linear infinite;
}
.submit-box .sub-input-box .spinner .spinner-part-3.data-v-6ddef3fc {
  animation: load432-6ddef3fc calc(0.5s / var(--speed)) linear infinite;
}
@keyframes load432-6ddef3fc {
50% {
    transform: scaleY(var(--max-scale));
}
}
.submit-box .voice-box .icon.data-v-6ddef3fc {
  width: 60rpx;
  height: 60rpx;
  display: block;
}
.submit-box .send-record-box.data-v-6ddef3fc {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
  transition: background 0.3s;
  color: #ffffff;
  background-color: #333333;
  border-radius: 12rpx 12rpx 12rpx 12rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.submit-box .send-record-box .icon.data-v-6ddef3fc {
  display: block;
  height: 60rpx;
  width: 60rpx;
}
.submit-box .send-record-box.longPress.data-v-6ddef3fc {
  color: #ffffff;
  padding-top: 10rpx;
  width: 1500rpx;
  position: fixed;
  left: -50%;
  height: 750rpx;
  background: linear-gradient(136deg, #7531F6 0%, #686BF2 100%);
  z-index: 11;
  bottom: 0;
  transform: translateY(75%);
  border-radius: 50%;
}
.submit-box .send-record-box.is-close-send.data-v-6ddef3fc {
  background: linear-gradient(136deg, #f63131 0%, #f26868 100%);
}
.submit-box .input-box.data-v-6ddef3fc {
  width: 100%;
  display: flex;
  align-items: center;
  min-height: 60rpx;
  max-height: 300rpx;
  box-sizing: border-box;
}
.submit-box .input-box .textarea-box.data-v-6ddef3fc {
  flex: 1;
  min-height: 60rpx;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  padding: 0 20rpx;
  border-radius: 20rpx;
}
.submit-box .input-box .textarea-input.data-v-6ddef3fc {
  width: 100%;
  color: #333333;
  max-height: 60px;
  overflow-y: scroll;
  box-sizing: border-box;
  font-size: 26rpx;
}
.submit-box .input-box .input.data-v-6ddef3fc {
  flex: 1;
  color: #333333;
  height: 60rpx;
  line-height: 60rpx;
}
.submit-box .input-box .btn-box.data-v-6ddef3fc {
  display: flex;
  width: -webkit-fit-content;
  width: fit-content;
}
.submit-box .input-box .btn-box .upload.data-v-6ddef3fc {
  margin-right: 12rpx;
}
.submit-box .input-box .btn-box .icon.data-v-6ddef3fc {
  width: 60rpx;
  height: 60rpx;
  display: block;
}
.submit-box .input-box .vicoe-box.data-v-6ddef3fc {
  display: flex;
  width: -webkit-fit-content;
  width: fit-content;
}
.submit-box .input-box .vicoe-box .icon.data-v-6ddef3fc {
  width: 60rpx;
  height: 60rpx;
  display: block;
}
.clear-msg.data-v-6ddef3fc {
  color: #66648a;
  font-size: 30rpx;
  z-index: 99;
  text-align: right;
  margin-bottom: 6px;
  position: absolute;
  width: 100%;
  right: 0px;
  bottom: 140rpx;
  z-index: 8;
}
.clear-msg .txt.data-v-6ddef3fc {
  margin-right: 20px;
}
.loading.data-v-6ddef3fc {
  display: flex;
  align-items: center;
  margin-top: 10rpx;
}
.loading .loading-dot.data-v-6ddef3fc {
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
  background-color: #999;
  margin-right: 6rpx;
  animation: loading-blink-6ddef3fc 1.4s infinite both;
}
.loading .loading-dot.data-v-6ddef3fc:nth-child(1) {
  animation-delay: 0s;
}
.loading .loading-dot.data-v-6ddef3fc:nth-child(2) {
  animation-delay: 0.2s;
}
.loading .loading-dot.data-v-6ddef3fc:nth-child(3) {
  animation-delay: 0.4s;
  margin-right: 0;
}
@keyframes loading-blink-6ddef3fc {
0%, 80%, 100% {
    opacity: 0.3;
    transform: scale(0.8);
}
40% {
    opacity: 1;
    transform: scale(1);
}
}