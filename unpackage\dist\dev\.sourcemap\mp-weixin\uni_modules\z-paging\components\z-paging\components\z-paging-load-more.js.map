{"version": 3, "file": "z-paging-load-more.js", "sources": ["uni_modules/z-paging/components/z-paging/components/z-paging-load-more.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RToveW91bmdQcm9qZWN0L2FnZW50LW1pbmktdWkvdW5pX21vZHVsZXMvei1wYWdpbmcvY29tcG9uZW50cy96LXBhZ2luZy9jb21wb25lbnRzL3otcGFnaW5nLWxvYWQtbW9yZS52dWU"], "sourcesContent": ["<!-- [z-paging]上拉加载更多view -->\r\n<template>\r\n\t<view class=\"zp-l-container\" :class=\"{'zp-l-container-rpx':c.unit==='rpx','zp-l-container-px':c.unit==='px'}\" :style=\"[c.customStyle]\" @click=\"doClick\">\r\n\t\t<template v-if=\"!c.hideContent\">\r\n\t\t\t<!-- 底部加载更多没有更多数据分割线 -->\r\n\t\t\t<text v-if=\"c.showNoMoreLine&&finalStatus===M.NoMore\" :class=\"{'zp-l-line-rpx':c.unit==='rpx','zp-l-line-px':c.unit==='px'}\" :style=\"[{backgroundColor:zTheme.line[ts]},c.noMoreLineCustomStyle]\" />\r\n\t\t\t<!-- 底部加载更多loading -->\r\n\t\t\t<!-- #ifndef APP-NVUE -->\r\n\t\t\t<image v-if=\"finalStatus===M.Loading&&!!c.loadingIconCustomImage\"\r\n\t\t\t\t:src=\"c.loadingIconCustomImage\" :style=\"[c.iconCustomStyle]\" :class=\"{'zp-l-line-loading-custom-image':true,'zp-l-line-loading-custom-image-animated':c.loadingAnimated,'zp-l-line-loading-custom-image-rpx':c.unit==='rpx','zp-l-line-loading-custom-image-px':c.unit==='px'}\" />\r\n\t\t\t<image v-if=\"finalStatus===M.Loading&&finalLoadingIconType==='flower'&&!c.loadingIconCustomImage.length\"\r\n\t\t\t\t:class=\"{'zp-line-loading-image':true,'zp-line-loading-image-rpx':c.unit==='rpx','zp-line-loading-image-px':c.unit==='px'}\" :style=\"[c.iconCustomStyle]\" :src=\"zTheme.flower[ts]\" />\r\n\t\t\t<!-- #endif -->\r\n\t\t\t<!-- #ifdef APP-NVUE -->\r\n\t\t\t<!-- 在nvue中底部加载更多loading使用系统自带的 -->\r\n\t\t\t<view>\r\n\t\t\t\t<loading-indicator v-if=\"finalStatus===M.Loading&&finalLoadingIconType!=='circle'\" :class=\"{'zp-line-loading-image-rpx':c.unit==='rpx','zp-line-loading-image-px':c.unit==='px'}\" :style=\"[{color:zTheme.indicator[ts]}]\" :animating=\"true\" />\r\n\t\t\t</view>\r\n\t\t\t<!-- #endif -->\r\n\t\t\t<!-- 底部加载更多文字 -->\r\n\t\t\t<text v-if=\"finalStatus===M.Loading&&finalLoadingIconType==='circle'&&!c.loadingIconCustomImage.length\"\r\n\t\t\t\tclass=\"zp-l-circle-loading-view\" :class=\"{'zp-l-circle-loading-view-rpx':c.unit==='rpx','zp-l-circle-loading-view-px':c.unit==='px'}\" :style=\"[{borderColor:zTheme.circleBorder[ts],borderTopColor:zTheme.circleBorderTop[ts]},c.iconCustomStyle]\" />\r\n\t\t\t<text v-if=\"!c.isChat||(!c.chatDefaultAsLoading&&finalStatus===M.Default)||finalStatus===M.Fail\" :class=\"{'zp-l-text-rpx':c.unit==='rpx','zp-l-text-px':c.unit==='px'}\" :style=\"[{color:zTheme.title[ts]},c.titleCustomStyle]\">{{ownLoadingMoreText}}</text>\r\n\t\t\t<!-- 底部加载更多没有更多数据分割线 -->\r\n\t\t\t<text v-if=\"c.showNoMoreLine&&finalStatus===M.NoMore\" :class=\"{'zp-l-line-rpx':c.unit==='rpx','zp-l-line-px':c.unit==='px'}\" :style=\"[{backgroundColor:zTheme.line[ts]},c.noMoreLineCustomStyle]\" />\r\n\t\t</template>\r\n\t</view>\r\n</template>\r\n<script>\r\n\timport zStatic from '../js/z-paging-static'\r\n\timport Enum from '../js/z-paging-enum'\r\n\texport default {\r\n\t\tname: 'z-paging-load-more',\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tM: Enum.More,\r\n\t\t\t\tzTheme: {\r\n\t\t\t\t\ttitle: { white: '#efefef', black: '#a4a4a4' },\r\n\t\t\t\t\tline: { white: '#efefef', black: '#eeeeee' },\r\n\t\t\t\t\tcircleBorder: { white: '#aaaaaa', black: '#c8c8c8' },\r\n\t\t\t\t\tcircleBorderTop: { white: '#ffffff', black: '#444444' },\r\n\t\t\t\t\tflower: { white: zStatic.base64FlowerWhite, black: zStatic.base64Flower },\r\n\t\t\t\t\tindicator: { white: '#eeeeee', black: '#777777' }\r\n\t\t\t\t}\r\n\t\t\t};\r\n\t\t},\r\n\t\tprops: ['zConfig'],\r\n\t\tcomputed: {\r\n\t\t\tts() {\r\n\t\t\t\treturn this.c.defaultThemeStyle;\r\n\t\t\t},\r\n\t\t\t// 底部加载更多配置\r\n\t\t\tc() {\r\n\t\t\t\treturn this.zConfig || {};\r\n\t\t\t},\r\n\t\t\t// 底部加载更多文字\r\n\t\t\townLoadingMoreText() {\r\n\t\t\t\tconst statusTextArr = [this.c.defaultText,this.c.loadingText,this.c.noMoreText,this.c.failText];\r\n\t\t\t\treturn statusTextArr[this.finalStatus];\r\n\t\t\t},\r\n\t\t\t// 底部加载更多状态\r\n\t\t\tfinalStatus() {\r\n\t\t\t\tif (this.c.defaultAsLoading && this.c.status === this.M.Default) return this.M.Loading;\r\n\t\t\t\treturn this.c.status;\r\n\t\t\t},\r\n\t\t\t// 加载更多icon类型\r\n\t\t\tfinalLoadingIconType() {\r\n\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\treturn 'flower';\r\n\t\t\t\t// #endif\r\n\t\t\t\treturn this.c.loadingIconType;\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 点击了加载更多\r\n\t\t\tdoClick() {\r\n\t\t\t\tthis.$emit('doClick');\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style scoped>\r\n\t@import \"../css/z-paging-static.css\";\r\n\r\n\t.zp-l-container {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tclear: both;\r\n\t\tdisplay: flex;\r\n\t\t/* #endif */\r\n\t\tflex-direction: row;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t}\r\n\t.zp-l-container-rpx {\r\n\t\theight: 80rpx;\r\n\t\tfont-size: 27rpx;\r\n\t}\r\n\t.zp-l-container-px {\r\n\t\theight: 40px;\r\n\t\tfont-size: 14px;\r\n\t}\r\n\r\n\t.zp-l-line-loading-custom-image {\r\n\t\tcolor: #a4a4a4;\r\n\t}\r\n\t.zp-l-line-loading-custom-image-rpx {\r\n\t\tmargin-right: 8rpx;\r\n\t\twidth: 28rpx;\r\n\t\theight: 28rpx;\r\n\t}\r\n\t.zp-l-line-loading-custom-image-px {\r\n\t\tmargin-right: 4px;\r\n\t\twidth: 14px;\r\n\t\theight: 14px;\r\n\t}\r\n\t\r\n\t.zp-l-line-loading-custom-image-animated{\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tanimation: loading-circle 1s linear infinite;\r\n\t\t/* #endif */\r\n\t}\r\n\r\n\t.zp-l-circle-loading-view {\r\n\t\tborder: 3rpx solid #dddddd;\r\n\t\tborder-radius: 50%;\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tanimation: loading-circle 1s linear infinite;\r\n\t\t/* #endif */\r\n\t\t/* #ifdef APP-NVUE */\r\n\t\twidth: 30rpx;\r\n\t\theight: 30rpx;\r\n\t\t/* #endif */\r\n\t}\r\n\t.zp-l-circle-loading-view-rpx {\r\n\t\tmargin-right: 8rpx;\r\n\t\twidth: 23rpx;\r\n\t\theight: 23rpx;\r\n\t}\r\n\t.zp-l-circle-loading-view-px {\r\n\t\tmargin-right: 4px;\r\n\t\twidth: 12px;\r\n\t\theight: 12px;\r\n\t}\r\n\r\n\t.zp-l-text-rpx {\r\n\t\tfont-size: 30rpx;\r\n\t\tmargin: 0rpx 6rpx;\r\n\t}\r\n\t.zp-l-text-px {\r\n\t\tfont-size: 15px;\r\n\t\tmargin: 0px 3px;\r\n\t}\r\n\r\n\t.zp-l-line-rpx {\r\n\t\theight: 1px;\r\n\t\twidth: 100rpx;\r\n\t\tmargin: 0rpx 10rpx;\r\n\t}\r\n\t.zp-l-line-px {\r\n\t\theight: 1px;\r\n\t\twidth: 50px;\r\n\t\tmargin: 0rpx 5px;\r\n\t}\r\n\r\n\t/* #ifndef APP-NVUE */\r\n\t@keyframes loading-circle {\r\n\t\t0% {\r\n\t\t\t-webkit-transform: rotate(0deg);\r\n\t\t\ttransform: rotate(0deg);\r\n\t\t}\r\n\t\t100% {\r\n\t\t\t-webkit-transform: rotate(360deg);\r\n\t\t\ttransform: rotate(360deg);\r\n\t\t}\r\n\t}\r\n\t/* #endif */\r\n</style>\r\n", "import Component from 'E:/youngProject/agent-mini-ui/uni_modules/z-paging/components/z-paging/components/z-paging-load-more.vue'\nwx.createComponent(Component)"], "names": ["Enum", "zStatic"], "mappings": ";;;;AA+BC,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,OAAO;AACN,WAAO;AAAA,MACN,GAAGA,sDAAI,KAAC;AAAA,MACR,QAAQ;AAAA,QACP,OAAO,EAAE,OAAO,WAAW,OAAO,UAAW;AAAA,QAC7C,MAAM,EAAE,OAAO,WAAW,OAAO,UAAW;AAAA,QAC5C,cAAc,EAAE,OAAO,WAAW,OAAO,UAAW;AAAA,QACpD,iBAAiB,EAAE,OAAO,WAAW,OAAO,UAAW;AAAA,QACvD,QAAQ,EAAE,OAAOC,wDAAO,QAAC,mBAAmB,OAAOA,wDAAO,QAAC,aAAc;AAAA,QACzE,WAAW,EAAE,OAAO,WAAW,OAAO,UAAU;AAAA,MACjD;AAAA;EAED;AAAA,EACD,OAAO,CAAC,SAAS;AAAA,EACjB,UAAU;AAAA,IACT,KAAK;AACJ,aAAO,KAAK,EAAE;AAAA,IACd;AAAA;AAAA,IAED,IAAI;AACH,aAAO,KAAK,WAAW;IACvB;AAAA;AAAA,IAED,qBAAqB;AACpB,YAAM,gBAAgB,CAAC,KAAK,EAAE,aAAY,KAAK,EAAE,aAAY,KAAK,EAAE,YAAW,KAAK,EAAE,QAAQ;AAC9F,aAAO,cAAc,KAAK,WAAW;AAAA,IACrC;AAAA;AAAA,IAED,cAAc;AACb,UAAI,KAAK,EAAE,oBAAoB,KAAK,EAAE,WAAW,KAAK,EAAE;AAAS,eAAO,KAAK,EAAE;AAC/E,aAAO,KAAK,EAAE;AAAA,IACd;AAAA;AAAA,IAED,uBAAuB;AAItB,aAAO,KAAK,EAAE;AAAA,IACf;AAAA,EACA;AAAA,EACD,SAAS;AAAA;AAAA,IAER,UAAU;AACT,WAAK,MAAM,SAAS;AAAA,IACrB;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC9ED,GAAG,gBAAgB,SAAS;"}