"use strict";var e,l;const t=require("../../../../common/vendor.js"),o={props:{isDot:{type:Boolean,default:!1},value:{type:[Number,String],default:""},show:{type:Boolean,default:!0},max:{type:[Number,String],default:999},type:{type:[String,void 0,null],default:"error"},showZero:{type:Boolean,default:!1},bgColor:{type:[String,null],default:null},color:{type:[String,null],default:null},shape:{type:[String,void 0,null],default:"circle"},numberType:{type:[String,void 0,null],default:"overflow"},offset:{type:Array,default:()=>[]},inverted:{type:Boolean,default:!1},absolute:{type:Boolean,default:!1},...null==(l=null==(e=t.index.$uv)?void 0:e.props)?void 0:l.badge}};exports.props=o;
