{"version": 3, "file": "relation.js", "sources": ["uni_modules/lime-painter/components/common/relation.js"], "sourcesContent": ["const styles = (v ='') =>  v.split(';').filter(v => v && !/^[\\n\\s]+$/.test(v)).map(v => {\r\n\t\t\t\t\t\tconst key = v.slice(0, v.indexOf(':'))\r\n\t\t\t\t\t\tconst value = v.slice(v.indexOf(':')+1)\n\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\t[key\r\n\t\t\t\t\t\t\t\t.replace(/-([a-z])/g, function() { return arguments[1].toUpperCase()})\r\n\t\t\t\t\t\t\t\t.replace(/\\s+/g, '')\r\n\t\t\t\t\t\t\t]: value.replace(/^\\s+/, '').replace(/\\s+$/, '') || ''\n\t\t\t\t\t\t}\n\t\t\t\t\t})\nexport function parent(parent) {\n\treturn {\n\t\tprovide() {\n\t\t\treturn {\n\t\t\t\t[parent]: this\n\t\t\t}\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tel: {\r\n\t\t\t\t\tid: null,\n\t\t\t\t\tcss: {},\n\t\t\t\t\tviews: []\n\t\t\t\t},\n\t\t\t}\n\t\t},\n\t\twatch: {\n\t\t\tcss: { \n\t\t\t\thandler(v) {\n\t\t\t\t\tif(this.canvasId) {\n\t\t\t\t\t\tthis.el.css = (typeof v == 'object' ? v : v && Object.assign(...styles(v))) || {}\r\n\t\t\t\t\t\tthis.canvasWidth = this.el.css && this.el.css.width || this.canvasWidth\n\t\t\t\t\t\tthis.canvasHeight = this.el.css && this.el.css.height || this.canvasHeight\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\timmediate: true\n\t\t\t}\n\t\t}\n\t}\n}\nexport function children(parent, options = {}) {\n\tconst indexKey = options.indexKey || 'index'\n\treturn {\n\t\tinject: {\n\t\t\t[parent]: {\n\t\t\t\tdefault: null\n\t\t\t}\n\t\t},\n\t\twatch: {\n\t\t\tel: {\n\t\t\t\thandler(v, o) {\n\t\t\t\t\tif(JSON.stringify(v) != JSON.stringify(o))\n\t\t\t\t\t\tthis.bindRelation()\n\t\t\t\t},\n\t\t\t\tdeep: true,\n\t\t\t\timmediate: true\n\t\t\t},\n\t\t\tsrc: {\n\t\t\t\thandler(v, o) {\n\t\t\t\t\tif(v != o)\n\t\t\t\t\t\tthis.bindRelation()\n\t\t\t\t},\n\t\t\t\timmediate: true\n\t\t\t},\n\t\t\ttext: {\n\t\t\t\thandler(v, o) {\n\t\t\t\t\tif(v != o) this.bindRelation()\n\t\t\t\t},\n\t\t\t\timmediate: true\n\t\t\t},\n\t\t\tcss: {\n\t\t\t\thandler(v, o) {\n\t\t\t\t\tif(v != o)\n\t\t\t\t\t\tthis.el.css = (typeof v == 'object' ? v : v && Object.assign(...styles(v))) || {}\n\t\t\t\t},\n\t\t\t\timmediate: true\n\t\t\t},\n\t\t\treplace: {\n\t\t\t\thandler(v, o) {\n\t\t\t\t\tif(JSON.stringify(v) != JSON.stringify(o))\n\t\t\t\t\t\tthis.bindRelation()\n\t\t\t\t},\n\t\t\t\tdeep: true,\n\t\t\t\timmediate: true\n\t\t\t}\n\t\t},\n\t\tcreated() {\r\n\t\t\tif(!this._uid) {\r\n\t\t\t\tthis._uid = this._.uid\r\n\t\t\t}\n\t\t\tObject.defineProperty(this, 'parent', {\n\t\t\t\tget: () => this[parent] || [],\n\t\t\t})\n\t\t\tObject.defineProperty(this, 'index', {\n\t\t\t\tget: () =>  {\n\t\t\t\t\tthis.bindRelation();\r\n\t\t\t\t\tconst {parent: {el: {views=[]}={}}={}} = this\n\t\t\t\t\treturn views.indexOf(this.el)\n\t\t\t\t},\n\t\t\t});\n\t\t\tthis.el.type = this.type\r\n\t\t\tif(this.uid) {\r\n\t\t\t\tthis.el.uid = this.uid\r\n\t\t\t}\r\n\t\t\tthis.bindRelation()\n\t\t},\n\t\t// #ifdef VUE3\r\n\t\tbeforeUnmount() {\r\n\t\t\tthis.removeEl()\r\n\t\t},\r\n\t\t// #endif\r\n\t\t// #ifdef VUE2\r\n\t\tbeforeDestroy() {\r\n\t\t\tthis.removeEl()\r\n\t\t},\r\n\t\t// #endif\r\n\t\tmethods: {\r\n\t\t\tremoveEl() {\r\n\t\t\t\tif (this.parent) {\r\n\t\t\t\t\tthis.parent.el.views = this.parent.el.views.filter(\r\n\t\t\t\t\t\t(item) => item._uid !== this._uid\r\n\t\t\t\t\t);\r\n\t\t\t\t}\r\n\t\t\t},\n\t\t\tbindRelation() {\n\t\t\t\tif(!this.el._uid) {\n\t\t\t\t\tthis.el._uid = this._uid \n\t\t\t\t}\n\t\t\t\tif(['text','qrcode'].includes(this.type)) {\n\t\t\t\t\tthis.el.text = this.$slots && this.$slots.default && this.$slots.default[0].text || `${this.text || ''}`.replace(/\\\\n/g, '\\n')\n\t\t\t\t}\n\t\t\t\tif(this.type == 'image') {\n\t\t\t\t\tthis.el.src = this.src\n\t\t\t\t}\n\t\t\t\tif (!this.parent) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tlet views = this.parent.el.views || [];\n\t\t\t\tif(views.indexOf(this.el) !== -1) {\n\t\t\t\t\tthis.parent.el.views = views.map(v => v._uid == this._uid ? this.el : v)\n\t\t\t\t} else {\n\t\t\t\t\tthis.parent.el.views = [...views, this.el];\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\tmounted() {\n\t\t\t// this.bindRelation()\n\t\t},\n\t}\n}"], "names": ["v", "parent"], "mappings": ";AAAA,MAAM,SAAS,CAAC,IAAG,OAAQ,EAAE,MAAM,GAAG,EAAE,OAAO,CAAAA,OAAKA,MAAK,CAAC,YAAY,KAAKA,EAAC,CAAC,EAAE,IAAI,CAAAA,OAAK;AAClF,QAAM,MAAMA,GAAE,MAAM,GAAGA,GAAE,QAAQ,GAAG,CAAC;AACrC,QAAM,QAAQA,GAAE,MAAMA,GAAE,QAAQ,GAAG,IAAE,CAAC;AACtC,SAAO;AAAA,IACN,CAAC,IACC,QAAQ,aAAa,WAAW;AAAE,aAAO,UAAU,CAAC,EAAE,YAAW;AAAA,IAAE,CAAC,EACpE,QAAQ,QAAQ,EAAE,CAC3B,GAAU,MAAM,QAAQ,QAAQ,EAAE,EAAE,QAAQ,QAAQ,EAAE,KAAK;AAAA,EACpD;AACP,CAAM;AACC,SAAS,OAAOC,SAAQ;AAC9B,SAAO;AAAA,IACN,UAAU;AACT,aAAO;AAAA,QACN,CAACA,OAAM,GAAG;AAAA,MACV;AAAA,IACD;AAAA,IACD,OAAO;AACN,aAAO;AAAA,QACN,IAAI;AAAA,UACH,IAAI;AAAA,UACJ,KAAK,CAAE;AAAA,UACP,OAAO,CAAE;AAAA,QACT;AAAA,MACD;AAAA,IACD;AAAA,IACD,OAAO;AAAA,MACN,KAAK;AAAA,QACJ,QAAQ,GAAG;AACV,cAAG,KAAK,UAAU;AACjB,iBAAK,GAAG,OAAO,OAAO,KAAK,WAAW,IAAI,KAAK,OAAO,OAAO,GAAG,OAAO,CAAC,CAAC,MAAM,CAAE;AACjF,iBAAK,cAAc,KAAK,GAAG,OAAO,KAAK,GAAG,IAAI,SAAS,KAAK;AAC5D,iBAAK,eAAe,KAAK,GAAG,OAAO,KAAK,GAAG,IAAI,UAAU,KAAK;AAAA,UAC9D;AAAA,QACD;AAAA,QACD,WAAW;AAAA,MACX;AAAA,IACD;AAAA,EACD;AACF;AACO,SAAS,SAASA,SAAQ,UAAU,IAAI;AAC7B,UAAQ,YAAY;AACrC,SAAO;AAAA,IACN,QAAQ;AAAA,MACP,CAACA,OAAM,GAAG;AAAA,QACT,SAAS;AAAA,MACT;AAAA,IACD;AAAA,IACD,OAAO;AAAA,MACN,IAAI;AAAA,QACH,QAAQ,GAAG,GAAG;AACb,cAAG,KAAK,UAAU,CAAC,KAAK,KAAK,UAAU,CAAC;AACvC,iBAAK,aAAc;AAAA,QACpB;AAAA,QACD,MAAM;AAAA,QACN,WAAW;AAAA,MACX;AAAA,MACD,KAAK;AAAA,QACJ,QAAQ,GAAG,GAAG;AACb,cAAG,KAAK;AACP,iBAAK,aAAc;AAAA,QACpB;AAAA,QACD,WAAW;AAAA,MACX;AAAA,MACD,MAAM;AAAA,QACL,QAAQ,GAAG,GAAG;AACb,cAAG,KAAK;AAAG,iBAAK,aAAc;AAAA,QAC9B;AAAA,QACD,WAAW;AAAA,MACX;AAAA,MACD,KAAK;AAAA,QACJ,QAAQ,GAAG,GAAG;AACb,cAAG,KAAK;AACP,iBAAK,GAAG,OAAO,OAAO,KAAK,WAAW,IAAI,KAAK,OAAO,OAAO,GAAG,OAAO,CAAC,CAAC,MAAM,CAAE;AAAA,QAClF;AAAA,QACD,WAAW;AAAA,MACX;AAAA,MACD,SAAS;AAAA,QACR,QAAQ,GAAG,GAAG;AACb,cAAG,KAAK,UAAU,CAAC,KAAK,KAAK,UAAU,CAAC;AACvC,iBAAK,aAAc;AAAA,QACpB;AAAA,QACD,MAAM;AAAA,QACN,WAAW;AAAA,MACX;AAAA,IACD;AAAA,IACD,UAAU;AACT,UAAG,CAAC,KAAK,MAAM;AACd,aAAK,OAAO,KAAK,EAAE;AAAA,MACnB;AACD,aAAO,eAAe,MAAM,UAAU;AAAA,QACrC,KAAK,MAAM,KAAKA,OAAM,KAAK,CAAE;AAAA,MACjC,CAAI;AACD,aAAO,eAAe,MAAM,SAAS;AAAA,QACpC,KAAK,MAAO;AACX,eAAK,aAAY;AACjB,gBAAM,EAAC,QAAQ,EAAC,IAAI,EAAC,QAAM,CAAE,EAAA,IAAE,CAAE,EAAA,IAAE,CAAE,EAAA,IAAI;AACzC,iBAAO,MAAM,QAAQ,KAAK,EAAE;AAAA,QAC5B;AAAA,MACL,CAAI;AACD,WAAK,GAAG,OAAO,KAAK;AACpB,UAAG,KAAK,KAAK;AACZ,aAAK,GAAG,MAAM,KAAK;AAAA,MACnB;AACD,WAAK,aAAc;AAAA,IACnB;AAAA,IAED,gBAAgB;AACf,WAAK,SAAU;AAAA,IACf;AAAA,IAOD,SAAS;AAAA,MACR,WAAW;AACV,YAAI,KAAK,QAAQ;AAChB,eAAK,OAAO,GAAG,QAAQ,KAAK,OAAO,GAAG,MAAM;AAAA,YAC3C,CAAC,SAAS,KAAK,SAAS,KAAK;AAAA,UACnC;AAAA,QACK;AAAA,MACD;AAAA,MACD,eAAe;AACd,YAAG,CAAC,KAAK,GAAG,MAAM;AACjB,eAAK,GAAG,OAAO,KAAK;AAAA,QACpB;AACD,YAAG,CAAC,QAAO,QAAQ,EAAE,SAAS,KAAK,IAAI,GAAG;AACzC,eAAK,GAAG,OAAO,KAAK,UAAU,KAAK,OAAO,WAAW,KAAK,OAAO,QAAQ,CAAC,EAAE,QAAQ,GAAG,KAAK,QAAQ,EAAE,GAAG,QAAQ,QAAQ,IAAI;AAAA,QAC7H;AACD,YAAG,KAAK,QAAQ,SAAS;AACxB,eAAK,GAAG,MAAM,KAAK;AAAA,QACnB;AACD,YAAI,CAAC,KAAK,QAAQ;AACjB;AAAA,QACA;AACD,YAAI,QAAQ,KAAK,OAAO,GAAG,SAAS,CAAA;AACpC,YAAG,MAAM,QAAQ,KAAK,EAAE,MAAM,IAAI;AACjC,eAAK,OAAO,GAAG,QAAQ,MAAM,IAAI,OAAK,EAAE,QAAQ,KAAK,OAAO,KAAK,KAAK,CAAC;AAAA,QAC5E,OAAW;AACN,eAAK,OAAO,GAAG,QAAQ,CAAC,GAAG,OAAO,KAAK,EAAE;AAAA,QACzC;AAAA,MACD;AAAA,IACD;AAAA,IACD,UAAU;AAAA,IAET;AAAA,EACD;AACF;;;"}