"use strict";const o=require("../z-paging-utils.js"),e=require("../z-paging-enum.js"),t={props:{loadingMoreCustomStyle:{type:Object,default:o.u.gc("loadingMoreCustomStyle",{})},loadingMoreTitleCustomStyle:{type:Object,default:o.u.gc("loadingMoreTitleCustomStyle",{})},loadingMoreLoadingIconCustomStyle:{type:Object,default:o.u.gc("loadingMoreLoadingIconCustomStyle",{})},loadingMoreLoadingIconType:{type:String,default:o.u.gc("loadingMoreLoadingIconType","flower")},loadingMoreLoadingIconCustomImage:{type:String,default:o.u.gc("loadingMoreLoadingIconCustomImage","")},loadingMoreLoadingAnimated:{type:Boolean,default:o.u.gc("loadingMoreLoadingAnimated",!0)},loadingMoreEnabled:{type:Boolean,default:o.u.gc("loadingMoreEnabled",!0)},toBottomLoadingMoreEnabled:{type:Boolean,default:o.u.gc("toBottomLoadingMoreEnabled",!0)},loadingMoreDefaultAsLoading:{type:Boolean,default:o.u.gc("loadingMoreDefaultAsLoading",!1)},loadingMoreDefaultText:{type:[String,Object],default:o.u.gc("loadingMoreDefaultText",null)},loadingMoreLoadingText:{type:[String,Object],default:o.u.gc("loadingMoreLoadingText",null)},loadingMoreNoMoreText:{type:[String,Object],default:o.u.gc("loadingMoreNoMoreText",null)},loadingMoreFailText:{type:[String,Object],default:o.u.gc("loadingMoreFailText",null)},hideNoMoreInside:{type:Boolean,default:o.u.gc("hideNoMoreInside",!1)},hideNoMoreByLimit:{type:Number,default:o.u.gc("hideNoMoreByLimit",0)},showDefaultLoadingMoreText:{type:Boolean,default:o.u.gc("showDefaultLoadingMoreText",!0)},showLoadingMoreNoMoreView:{type:Boolean,default:o.u.gc("showLoadingMoreNoMoreView",!0)},showLoadingMoreNoMoreLine:{type:Boolean,default:o.u.gc("showLoadingMoreNoMoreLine",!0)},loadingMoreNoMoreLineCustomStyle:{type:Object,default:o.u.gc("loadingMoreNoMoreLineCustomStyle",{})},insideMore:{type:Boolean,default:o.u.gc("insideMore",!1)},lowerThreshold:{type:[Number,String],default:o.u.gc("lowerThreshold","100rpx")}},data:()=>({M:e.Enum.More,loadingStatus:e.Enum.More.Default,loadingStatusAfterRender:e.Enum.More.Default,loadingMoreTimeStamp:0,loadingMoreDefaultSlot:null,showLoadingMore:!1,customNoMore:-1}),computed:{zLoadMoreConfig(){return{status:this.loadingStatusAfterRender,defaultAsLoading:this.loadingMoreDefaultAsLoading||this.useChatRecordMode&&this.chatLoadingMoreDefaultAsLoading,defaultThemeStyle:this.finalLoadingMoreThemeStyle,customStyle:this.loadingMoreCustomStyle,titleCustomStyle:this.loadingMoreTitleCustomStyle,iconCustomStyle:this.loadingMoreLoadingIconCustomStyle,loadingIconType:this.loadingMoreLoadingIconType,loadingIconCustomImage:this.loadingMoreLoadingIconCustomImage,loadingAnimated:this.loadingMoreLoadingAnimated,showNoMoreLine:this.showLoadingMoreNoMoreLine,noMoreLineCustomStyle:this.loadingMoreNoMoreLineCustomStyle,defaultText:this.finalLoadingMoreDefaultText,loadingText:this.finalLoadingMoreLoadingText,noMoreText:this.finalLoadingMoreNoMoreText,failText:this.finalLoadingMoreFailText,hideContent:!this.loadingMoreDefaultAsLoading&&this.listRendering,unit:this.unit,isChat:this.useChatRecordMode,chatDefaultAsLoading:this.chatLoadingMoreDefaultAsLoading}},finalLoadingMoreThemeStyle(){return this.loadingMoreThemeStyle.length?this.loadingMoreThemeStyle:this.defaultThemeStyle},finalLowerThreshold(){return o.u.convertToPx(this.lowerThreshold)},showLoadingMoreDefault(){return this._showLoadingMore("Default")},showLoadingMoreLoading(){return this._showLoadingMore("Loading")},showLoadingMoreNoMore(){return this._showLoadingMore("NoMore")},showLoadingMoreFail(){return this._showLoadingMore("Fail")},showLoadingMoreCustom(){return this._showLoadingMore("Custom")}},methods:{pageReachBottom(){!this.useChatRecordMode&&this._onLoadingMore("toBottom")},doLoadMore(o){this._onLoadingMore(o)},_checkScrolledToBottom(e,t=!1){-1===this.cacheScrollNodeHeight?this._getNodeClientRect(".zp-scroll-view").then((o=>{if(o){const t=o[0].height;this.cacheScrollNodeHeight=t,e-t<=this.finalLowerThreshold&&this._onLoadingMore("toBottom")}})):(e-this.cacheScrollNodeHeight<=this.finalLowerThreshold?this._onLoadingMore("toBottom"):e-this.cacheScrollNodeHeight<=500&&!t&&o.u.delay((()=>{this._getNodeClientRect(".zp-scroll-view",!0,!0).then((o=>{if(o){this.oldScrollTop=o[0].scrollTop;const e=o[0].scrollHeight-this.oldScrollTop;this._checkScrolledToBottom(e,!0)}}))}),150,"checkScrolledToBottomDelay"),this.oldScrollTop<=150&&0!==this.oldScrollTop&&o.u.delay((()=>{0!==this.oldScrollTop&&this._getNodeClientRect(".zp-scroll-view",!0,!0).then((o=>{o&&0===o[0].scrollTop&&0!==this.oldScrollTop&&this._onScrollToUpper()}))}),150,"checkScrolledToTopDelay"))},_onLoadingMore(t="click"){if(this.isIos&&"toBottom"===t&&!this.scrollToBottomBounceEnabled&&this.scrollEnable&&(this.scrollEnable=!1,this.$nextTick((()=>{this.scrollEnable=!0}))),this.$emit("scrolltolower",t),!(this.refresherOnly||!this.loadingMoreEnabled||this.loadingStatus!==e.Enum.More.Default&&this.loadingStatus!==e.Enum.More.Fail||this.loading||this.showEmpty)){if(!this.isIos&&!this.refresherOnly&&!this.usePageScroll){const e=o.u.getTime();if(this.loadingMoreTimeStamp>0&&e-this.loadingMoreTimeStamp<100)return void(this.loadingMoreTimeStamp=0)}this._doLoadingMore()}},_doLoadingMore(){this.pageNo>=this.defaultPageNo&&this.loadingStatus!==e.Enum.More.NoMore&&(this.pageNo++,this._startLoading(!1),this.isLocalPaging?this._localPagingQueryList(this.pageNo,this.defaultPageSize,this.localPagingLoadingTime,(o=>{this.completeByTotal(o,this.totalLocalPagingList.length),this.queryFrom=e.Enum.QueryFrom.LoadingMore})):(this._emitQuery(this.pageNo,this.defaultPageSize,e.Enum.QueryFrom.LoadingMore),this._callMyParentQuery()),this.loadingType=e.Enum.LoadingType.LoadingMore)},_preCheckShowNoMoreInside(o,t,i){this.loadingStatus===e.Enum.More.NoMore&&this.hideNoMoreByLimit>0&&o.length?this.showLoadingMore=o.length>this.hideNoMoreByLimit:this.loadingStatus===e.Enum.More.NoMore&&this.hideNoMoreInside&&o.length||this.insideMore&&!1!==this.insideOfPaging&&o.length?(this.$nextTick((()=>{this._checkShowNoMoreInside(o,t,i)})),this.insideMore&&!1!==this.insideOfPaging&&o.length&&(this.showLoadingMore=o.length)):this.showLoadingMore=o.length},async _checkShowNoMoreInside(o,e,t){try{const o=e||await this._getNodeClientRect(".zp-scroll-view");if(this.usePageScroll){if(o){const e=o[0].top+o[0].height;this.insideOfPaging=e<this.windowHeight,this.hideNoMoreInside&&(this.showLoadingMore=!this.insideOfPaging),this._updateInsideOfPaging()}}else{const e=t||await this._getNodeClientRect(".zp-paging-container-content"),i=e?e[0].height:0,n=o?o[0].height:0;this.insideOfPaging=i<n,this.hideNoMoreInside&&(this.showLoadingMore=!this.insideOfPaging),this._updateInsideOfPaging()}}catch(i){this.insideOfPaging=!o.length,this.hideNoMoreInside&&(this.showLoadingMore=!this.insideOfPaging),this._updateInsideOfPaging()}},_showLoadingMore(o){if(!this.showLoadingMoreWhenReload&&(this.loadingStatus===e.Enum.More.Default&&!this.nShowBottom||!this.realTotalData.length))return!1;if((!this.showLoadingMoreWhenReload||this.isUserPullDown||this.loadingStatus!==e.Enum.More.Loading)&&!this.showLoadingMore||!this.loadingMoreEnabled&&(!this.showLoadingMoreWhenReload||this.isUserPullDown||this.loadingStatus!==e.Enum.More.Loading)||this.refresherOnly)return!1;if(this.useChatRecordMode&&"Loading"!==o)return!1;if(!this.zSlots)return!1;if("Custom"===o)return this.showDefaultLoadingMoreText&&!(this.loadingStatus===e.Enum.More.NoMore&&!this.showLoadingMoreNoMoreView);return this.loadingStatus===e.Enum.More[o]&&this.zSlots[`loadingMore${o}`]&&("NoMore"!==o||this.showLoadingMoreNoMoreView)}}};exports.loadMoreModule=t;
