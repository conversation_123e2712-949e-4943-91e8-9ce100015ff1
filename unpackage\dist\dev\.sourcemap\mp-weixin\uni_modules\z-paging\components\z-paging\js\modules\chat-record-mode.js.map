{"version": 3, "file": "chat-record-mode.js", "sources": ["uni_modules/z-paging/components/z-paging/js/modules/chat-record-mode.js"], "sourcesContent": ["// [z-paging]聊天记录模式模块\r\nimport u from '.././z-paging-utils'\r\n\r\nexport default {\r\n\tprops: {\r\n\t\t// 使用聊天记录模式，默认为否\r\n\t\tuseChatRecordMode: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('useChatRecordMode', false)\r\n\t\t},\r\n\t\t// 使用聊天记录模式时滚动到顶部后，列表垂直移动偏移距离。默认0rpx。单位px（暂时无效）\r\n\t\tchatRecordMoreOffset: {\r\n\t\t\ttype: [Number, String],\r\n\t\t\tdefault: u.gc('chatRecordMoreOffset', '0rpx')\r\n\t\t},\r\n\t\t// 使用聊天记录模式时是否自动隐藏键盘：在用户触摸列表时候自动隐藏键盘，默认为是\r\n\t\tautoHideKeyboardWhenChat: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('autoHideKeyboardWhenChat', true)\r\n\t\t},\r\n\t\t// 使用聊天记录模式中键盘弹出时是否自动调整slot=\"bottom\"高度，默认为是\r\n\t\tautoAdjustPositionWhenChat: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('autoAdjustPositionWhenChat', true)\r\n\t\t},\r\n\t\t// 使用聊天记录模式中键盘弹出时占位高度偏移距离。默认0rpx。单位px\r\n\t\tchatAdjustPositionOffset: {\r\n\t\t\ttype: [Number, String],\r\n\t\t\tdefault: u.gc('chatAdjustPositionOffset', '0rpx')\r\n\t\t},\r\n\t\t// 使用聊天记录模式中键盘弹出时是否自动滚动到底部，默认为否\r\n\t\tautoToBottomWhenChat: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('autoToBottomWhenChat', false)\r\n\t\t},\r\n\t\t// 使用聊天记录模式中reload时是否显示chatLoading，默认为否\r\n\t\tshowChatLoadingWhenReload: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('showChatLoadingWhenReload', false)\r\n\t\t},\r\n\t\t// 在聊天记录模式中滑动到顶部状态为默认状态时，以加载中的状态展示，默认为是。若设置为否，则默认会显示【点击加载更多】，然后才会显示loading\r\n\t\tchatLoadingMoreDefaultAsLoading: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('chatLoadingMoreDefaultAsLoading', true)\r\n\t\t},\r\n\t},\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\t// 键盘高度\r\n\t\t\tkeyboardHeight: 0,\r\n\t\t\t// 键盘高度是否未改变，此时占位高度变化不需要动画效果\r\n\t\t\tisKeyboardHeightChanged: false,\r\n\t\t}\r\n\t},\r\n\tcomputed: {\r\n\t\tfinalChatRecordMoreOffset() {\r\n\t\t\treturn u.convertToPx(this.chatRecordMoreOffset);\r\n\t\t},\r\n\t\tfinalChatAdjustPositionOffset() {\r\n\t\t\treturn u.convertToPx(this.chatAdjustPositionOffset);\r\n\t\t},\r\n\t\t// 聊天记录模式旋转180度style\r\n\t\tchatRecordRotateStyle() {\r\n\t\t\tlet cellStyle;\r\n\t\t\t// 在vue中，直接将列表倒置，因此在vue的cell中，也直接写style=\"transform: scaleY(-1)\"转回来即可。\r\n\t\t\t// #ifndef APP-NVUE\r\n\t\t\tcellStyle = this.useChatRecordMode ? { transform: 'scaleY(-1)' } : {};\r\n\t\t\t// #endif\r\n\t\t\t\r\n\t\t\t// 在nvue中，需要考虑数据量不满一页的情况，因为nvue中的list无法通过flex-end修改不满一页的起始位置，会导致不满一页时列表数据从底部开始，因此需要特别判断\r\n\t\t\t// 当数据不满一屏的时候，不进行列表倒置\r\n\t\t\t// #ifdef APP-NVUE\r\n\t\t\tcellStyle = this.useChatRecordMode ? { transform: this.isFirstPageAndNoMore ? 'scaleY(1)' : 'scaleY(-1)' } : {};\r\n\t\t\t// #endif\r\n\t\t\t\r\n\t\t\tthis.$emit('update:cellStyle', cellStyle);\r\n\t\t\tthis.$emit('cellStyleChange', cellStyle);\r\n\t\t\t\r\n\t\t\t// 在聊天记录模式中，如果列表没有倒置并且当前是第一页，则需要自动滚动到最底部\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\tif (this.isFirstPage && this.isChatRecordModeAndNotInversion) {\r\n\t\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\t\t// 这里多次触发滚动到底部是为了避免在某些情况下，即使是在nextTick但是cell未渲染完毕导致滚动到底部位置不正确的问题\r\n\t\t\t\t\t\tthis._scrollToBottom(false);\r\n\t\t\t\t\t\tu.delay(() => {\r\n\t\t\t\t\t\t\tthis._scrollToBottom(false);\r\n\t\t\t\t\t\t\tu.delay(() => {\r\n\t\t\t\t\t\t\t\tthis._scrollToBottom(false);\r\n\t\t\t\t\t\t\t}, 50)\r\n\t\t\t\t\t\t}, 50)\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\treturn cellStyle;\r\n\t\t},\r\n\t\t// 是否是聊天记录列表并且有配置transform\r\n\t\tisChatRecordModeHasTransform() {\r\n\t\t\treturn this.useChatRecordMode && this.chatRecordRotateStyle && this.chatRecordRotateStyle.transform;\r\n\t\t},\r\n\t\t// 是否是聊天记录列表并且列表未倒置\r\n\t\tisChatRecordModeAndNotInversion() {\r\n\t\t\treturn this.isChatRecordModeHasTransform && this.chatRecordRotateStyle.transform === 'scaleY(1)';\r\n\t\t},\r\n\t\t// 是否是聊天记录列表并且列表倒置\r\n\t\tisChatRecordModeAndInversion() {\r\n\t\t\treturn this.isChatRecordModeHasTransform && this.chatRecordRotateStyle.transform === 'scaleY(-1)';\r\n\t\t},\r\n\t\t// 最终的聊天记录模式中底部安全区域的高度，如果开启了底部安全区域并且键盘未弹出，则添加底部区域高度\r\n\t\tchatRecordModeSafeAreaBottom() {\r\n\t\t\treturn this.safeAreaInsetBottom && !this.keyboardHeight ? this.safeAreaBottom : 0;\r\n\t\t}\r\n\t},\r\n\tmounted() {\r\n\t\t// 监听键盘高度变化（H5、百度小程序、抖音小程序、飞书小程序不支持）\r\n\t\t// #ifndef H5 || MP-BAIDU || MP-TOUTIAO\r\n\t\tif (this.useChatRecordMode) {\r\n\t\t\tuni.onKeyboardHeightChange(this._handleKeyboardHeightChange);\r\n\t\t}\r\n\t\t// #endif\r\n\t},\r\n\tmethods: {\r\n\t\t// 添加聊天记录\r\n\t\taddChatRecordData(data, toBottom = true, toBottomWithAnimate = true) {\r\n\t\t\tif (!this.useChatRecordMode) return;\r\n\t\t\tthis.isTotalChangeFromAddData = true;\r\n\t\t\tthis.addDataFromTop(data, toBottom, toBottomWithAnimate);\r\n\t\t},\r\n\t\t// 手动触发滚动到顶部加载更多，聊天记录模式时有效\r\n\t\tdoChatRecordLoadMore() {\r\n\t\t\tthis.useChatRecordMode && this._onLoadingMore('click');\r\n\t\t},\r\n\t\t// 处理键盘高度变化\r\n\t\t_handleKeyboardHeightChange(res) {\r\n\t\t\tthis.$emit('keyboardHeightChange', res);\r\n\t\t\tif (this.autoAdjustPositionWhenChat) {\r\n\t\t\t\tthis.isKeyboardHeightChanged = true;\r\n\t\t\t\tthis.keyboardHeight = res.height > 0 ? res.height + this.finalChatAdjustPositionOffset : res.height;\r\n\t\t\t}\r\n\t\t\tif (this.autoToBottomWhenChat && this.keyboardHeight > 0) {\r\n\t\t\t\tu.delay(() => {\r\n\t\t\t\t\tthis.scrollToBottom(false);\r\n\t\t\t\t\tu.delay(() => {\r\n\t\t\t\t\t\tthis.scrollToBottom(false);\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t} \r\n\t\t}\r\n\t}\r\n}\r\n"], "names": ["u", "uni"], "mappings": ";;;AAGA,MAAe,wBAAA;AAAA,EACd,OAAO;AAAA;AAAA,IAEN,mBAAmB;AAAA,MAClB,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,qBAAqB,KAAK;AAAA,IACxC;AAAA;AAAA,IAED,sBAAsB;AAAA,MACrB,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAASA,uDAAC,EAAC,GAAG,wBAAwB,MAAM;AAAA,IAC5C;AAAA;AAAA,IAED,0BAA0B;AAAA,MACzB,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,4BAA4B,IAAI;AAAA,IAC9C;AAAA;AAAA,IAED,4BAA4B;AAAA,MAC3B,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,8BAA8B,IAAI;AAAA,IAChD;AAAA;AAAA,IAED,0BAA0B;AAAA,MACzB,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAASA,uDAAC,EAAC,GAAG,4BAA4B,MAAM;AAAA,IAChD;AAAA;AAAA,IAED,sBAAsB;AAAA,MACrB,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,wBAAwB,KAAK;AAAA,IAC3C;AAAA;AAAA,IAED,2BAA2B;AAAA,MAC1B,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,6BAA6B,KAAK;AAAA,IAChD;AAAA;AAAA,IAED,iCAAiC;AAAA,MAChC,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,mCAAmC,IAAI;AAAA,IACrD;AAAA,EACD;AAAA,EACD,OAAO;AACN,WAAO;AAAA;AAAA,MAEN,gBAAgB;AAAA;AAAA,MAEhB,yBAAyB;AAAA,IACzB;AAAA,EACD;AAAA,EACD,UAAU;AAAA,IACT,4BAA4B;AAC3B,aAAOA,yDAAE,YAAY,KAAK,oBAAoB;AAAA,IAC9C;AAAA,IACD,gCAAgC;AAC/B,aAAOA,yDAAE,YAAY,KAAK,wBAAwB;AAAA,IAClD;AAAA;AAAA,IAED,wBAAwB;AACvB,UAAI;AAGJ,kBAAY,KAAK,oBAAoB,EAAE,WAAW,aAAc,IAAG;AASnE,WAAK,MAAM,oBAAoB,SAAS;AACxC,WAAK,MAAM,mBAAmB,SAAS;AAGvC,WAAK,UAAU,MAAM;AACpB,YAAI,KAAK,eAAe,KAAK,iCAAiC;AAC7D,eAAK,UAAU,MAAM;AAEpB,iBAAK,gBAAgB,KAAK;AAC1BA,mEAAC,EAAC,MAAM,MAAM;AACb,mBAAK,gBAAgB,KAAK;AAC1BA,qEAAC,EAAC,MAAM,MAAM;AACb,qBAAK,gBAAgB,KAAK;AAAA,cAC1B,GAAE,EAAE;AAAA,YACL,GAAE,EAAE;AAAA,UACX,CAAM;AAAA,QACD;AAAA,MACL,CAAI;AACD,aAAO;AAAA,IACP;AAAA;AAAA,IAED,+BAA+B;AAC9B,aAAO,KAAK,qBAAqB,KAAK,yBAAyB,KAAK,sBAAsB;AAAA,IAC1F;AAAA;AAAA,IAED,kCAAkC;AACjC,aAAO,KAAK,gCAAgC,KAAK,sBAAsB,cAAc;AAAA,IACrF;AAAA;AAAA,IAED,+BAA+B;AAC9B,aAAO,KAAK,gCAAgC,KAAK,sBAAsB,cAAc;AAAA,IACrF;AAAA;AAAA,IAED,+BAA+B;AAC9B,aAAO,KAAK,uBAAuB,CAAC,KAAK,iBAAiB,KAAK,iBAAiB;AAAA,IAChF;AAAA,EACD;AAAA,EACD,UAAU;AAGT,QAAI,KAAK,mBAAmB;AAC3BC,oBAAAA,MAAI,uBAAuB,KAAK,2BAA2B;AAAA,IAC3D;AAAA,EAED;AAAA,EACD,SAAS;AAAA;AAAA,IAER,kBAAkB,MAAM,WAAW,MAAM,sBAAsB,MAAM;AACpE,UAAI,CAAC,KAAK;AAAmB;AAC7B,WAAK,2BAA2B;AAChC,WAAK,eAAe,MAAM,UAAU,mBAAmB;AAAA,IACvD;AAAA;AAAA,IAED,uBAAuB;AACtB,WAAK,qBAAqB,KAAK,eAAe,OAAO;AAAA,IACrD;AAAA;AAAA,IAED,4BAA4B,KAAK;AAChC,WAAK,MAAM,wBAAwB,GAAG;AACtC,UAAI,KAAK,4BAA4B;AACpC,aAAK,0BAA0B;AAC/B,aAAK,iBAAiB,IAAI,SAAS,IAAI,IAAI,SAAS,KAAK,gCAAgC,IAAI;AAAA,MAC7F;AACD,UAAI,KAAK,wBAAwB,KAAK,iBAAiB,GAAG;AACzDD,+DAAC,EAAC,MAAM,MAAM;AACb,eAAK,eAAe,KAAK;AACzBA,iEAAC,EAAC,MAAM,MAAM;AACb,iBAAK,eAAe,KAAK;AAAA,UAC/B,CAAM;AAAA,QACN,CAAK;AAAA,MACD;AAAA,IACD;AAAA,EACD;AACF;;"}