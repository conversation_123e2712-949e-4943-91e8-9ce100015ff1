"use strict";const t=require("../../../../common/vendor.js"),e=require("../function/index.js"),n=require("../function/test.js"),i=require("../util/route.js"),r=require("../function/debounce.js"),s=require("../function/throttle.js"),a={props:{customStyle:{type:[Object,String],default:()=>({})},customClass:{type:String,default:""},url:{type:String,default:""},linkType:{type:String,default:"navigateTo"}},data:()=>({}),onLoad(){this.$uv.getRect=this.$uvGetRect},created(){this.$uv.getRect=this.$uvGetRect},computed:{$uv(){var a,o,u;return{...e.index,test:n.test,route:i.route,debounce:r.debounce,throttle:s.throttle,unit:null==(u=null==(o=null==(a=t.index)?void 0:a.$uv)?void 0:o.config)?void 0:u.unit}},bem:()=>function(t,e,n){const i=`uv-${t}--`,r={};return e&&e.map((t=>{r[i+this[t]]=!0})),n&&n.map((t=>{this[t]?r[i+t]=this[t]:delete r[i+t]})),Object.keys(r)}},methods:{openPage(e="url"){const n=this[e];n&&t.index[this.linkType]({url:n})},$uvGetRect(e,n){return new Promise((i=>{t.index.createSelectorQuery().in(this)[n?"selectAll":"select"](e).boundingClientRect((t=>{n&&Array.isArray(t)&&t.length&&i(t),!n&&t&&i(t)})).exec()}))},getParentData(t=""){this.parent||(this.parent={}),this.parent=this.$uv.$parent.call(this,t),this.parent.children&&-1===this.parent.children.indexOf(this)&&this.parent.children.push(this),this.parent&&this.parentData&&Object.keys(this.parentData).map((t=>{this.parentData[t]=this.parent[t]}))},preventEvent(t){t&&"function"==typeof t.stopPropagation&&t.stopPropagation()},noop(t){this.preventEvent(t)}},onReachBottom(){t.index.$emit("uvOnReachBottom")},beforeDestroy(){if(this.parent&&n.array(this.parent.children)){const t=this.parent.children;t.map(((e,n)=>{e===this&&t.splice(n,1)}))}},unmounted(){if(this.parent&&n.array(this.parent.children)){const t=this.parent.children;t.map(((e,n)=>{e===this&&t.splice(n,1)}))}}};exports.mixin=a;
