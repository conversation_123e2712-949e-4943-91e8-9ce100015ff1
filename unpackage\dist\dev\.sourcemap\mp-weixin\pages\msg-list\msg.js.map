{"version": 3, "file": "msg.js", "sources": ["pages/msg-list/msg.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvbXNnLWxpc3QvbXNnLnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"container\">\r\n    <!-- 整页滚动容器，支持下拉刷新 -->\r\n    <scroll-view class=\"page-scroll\" scroll-y refresher-enabled :refresher-triggered=\"refreshing\"\r\n      @refresherrefresh=\"onRefresh\" @refresherrestore=\"onRestore\" @scrolltolower=\"onLoadMore\" :lower-threshold=\"100\">\r\n      <!-- 对话列表内容 -->\r\n      <view class=\"chat-list\">\r\n        <!-- 加载状态提示 -->\r\n        <view v-if=\"loading && sessionList.length === 0\" class=\"loading-container\">\r\n          <text class=\"loading-text\">加载中...</text>\r\n        </view>\r\n\r\n        <!-- 空状态 -->\r\n        <view v-else-if=\"!loading && sessionList.length === 0\" class=\"empty-container\">\r\n          <text class=\"empty-text\">暂无对话记录</text>\r\n        </view>\r\n\r\n        <!-- 对话列表内容 -->\r\n        <view v-else>\r\n          <!-- @click=\"handleItemClick($event)\"  :class=\"{ 'selected': selectedId === item.id }\"  -->\r\n          <uni-swipe-action ref=\"swipeActionRef\">\r\n            <uni-swipe-action-item @click=\"handleSwipeClick($event, item)\" :key=\"item.guid\"\r\n              v-for=\"(item, index) in sessionList\" :threshold=\"0\"\r\n              :right-options=\"item.isTop === 1 ? closeSwipeOptions : swipeOptions\">\r\n              <view class=\"chat-item\" @click=\"handleItemClick(item)\">\r\n                <view class=\"avatar\">\r\n                  <image :src=\"item.agent.agentAvatar\" class=\"avatar-img\" mode=\"aspectFill\"></image>\r\n                </view>\r\n                <view class=\"content\">\r\n                  <view class=\"name\">{{ item.sessionTitle }}</view>\r\n                  <view class=\"message\">{{ item.lastMessage.content }}</view>\r\n                </view>\r\n                <!-- <view class=\"badge\">\r\n                  <uv-badge type=\"error\" max=\"99\" :value=\"100\"></uv-badge>\r\n                </view> -->\r\n              </view>\r\n            </uni-swipe-action-item>\r\n          </uni-swipe-action>\r\n\r\n          <!-- 加载更多状态 -->\r\n          <view class=\"load-more-container\" v-if=\"sessionList.length > 0\">\r\n            <view v-if=\"loadingMore\" class=\"load-more-item\">\r\n              <text class=\"load-more-text\">加载中...</text>\r\n            </view>\r\n            <view v-else-if=\"noMoreData\" class=\"load-more-item\">\r\n              <text class=\"load-more-text\">没有更多数据了</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </scroll-view>\r\n    <!-- 底部创建按钮 -->\r\n    <!-- <view class=\"create-btn-container\">\r\n      <view class=\"create-btn\" @click=\"handleCreate\">\r\n        <image src=\"/static/msg/<EMAIL>\" class=\"plus-icon\" mode=\"aspectFit\"></image>\r\n        <text class=\"create-text\">创建AI智能体</text>\r\n      </view>\r\n    </view> -->\r\n    <!-- 编辑弹窗 -->\r\n    <view class=\"modal-overlay\" v-if=\"showEditModal\" @click=\"closeEditModal\">\r\n      <view class=\"edit-modal\" @click.stop>\r\n        <view class=\"modal-title\">对话名称</view>\r\n        <view class=\"input-container\">\r\n          <input v-model=\"updateReq.sessionTitle\" class=\"edit-input\" placeholder=\"请输入对话名称\" :focus=\"showEditModal\" />\r\n        </view>\r\n        <view class=\"modal-buttons\">\r\n          <view class=\"cancel-btn\" @click=\"closeEditModal\">取消</view>\r\n          <view class=\"confirm-btn\" @click=\"confirmEdit\">确认</view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { reactive, ref, watch } from 'vue'\r\nimport {\r\n  onShow\r\n} from '@dcloudio/uni-app';\r\nimport { useUserStore } from '@/stores/user.js'\r\nimport { getMySessionListApi, updateSessionTitleApi, deleteSessionApi, setSessionTopApi } from '@/api';\r\nconst userStore = useUserStore()\r\n\r\n// 对话列表数据\r\nconst sessionList = ref([])\r\n\r\n// 分页相关\r\nconst pageParams = reactive({\r\n  page: 1,\r\n  pageSize: 10\r\n})\r\n\r\n// 加载状态\r\nconst loading = ref(false)\r\nconst refreshing = ref(false)\r\nconst loadingMore = ref(false)\r\nconst noMoreData = ref(false)\r\n\r\n// 分页信息\r\nconst pageInfo = reactive({\r\n  current_page: 1,\r\n  last_page: 1,\r\n  per_page: 10,\r\n  total: 0\r\n})\r\n\r\n// 编辑弹窗相关\r\nconst showEditModal = ref(false)\r\n\r\n// 左滑操作配置\r\nconst swipeOptions = ref([\r\n  {\r\n    text: '置顶',\r\n    style: {\r\n      backgroundColor: '#5A7BF7',\r\n      color: '#fff'\r\n    }\r\n  },\r\n  {\r\n    text: '编辑',\r\n    style: {\r\n      backgroundColor: '#FF9F40',\r\n      color: '#fff'\r\n    }\r\n  },\r\n  {\r\n    text: '删除',\r\n    style: {\r\n      backgroundColor: '#FF6B6B',\r\n      color: '#fff'\r\n    }\r\n  }\r\n])\r\nconst closeSwipeOptions = ref([\r\n  {\r\n    text: '取消置顶',\r\n    style: {\r\n      backgroundColor: '#FF6B6B',\r\n      color: '#fff'\r\n    }\r\n  },\r\n  {\r\n    text: '编辑',\r\n    style: {\r\n      backgroundColor: '#FF9F40',\r\n      color: '#fff'\r\n    }\r\n  },\r\n  {\r\n    text: '删除',\r\n    style: {\r\n      backgroundColor: '#FF6B6B',\r\n      color: '#fff'\r\n    }\r\n  }\r\n])\r\nconst swipeActionRef = ref(null)\r\n\r\n// 下拉刷新\r\nconst onRefresh = () => {\r\n  refreshing.value = true\r\n  pageParams.page = 1\r\n  noMoreData.value = false\r\n  getMySessionList(true)\r\n}\r\n\r\n// 刷新完成\r\nconst onRestore = () => {\r\n  refreshing.value = false\r\n}\r\n\r\n// 上拉加载更多\r\nconst onLoadMore = () => {\r\n  if (loadingMore.value || noMoreData.value) return\r\n\r\n  if (pageInfo.current_page < pageInfo.last_page) {\r\n    pageParams.page++\r\n    getMySessionList(false)\r\n  } else {\r\n    noMoreData.value = true\r\n  }\r\n}\r\n\r\n// 左滑操作点击事件\r\nconst handleSwipeClick = (event, item) => {\r\n  const { index } = event\r\n  switch (index) {\r\n    case 0: // 置顶\r\n      handleTop(item)\r\n      break\r\n    case 1: // 编辑\r\n      handleEdit(item)\r\n      break\r\n    case 2: // 删除\r\n      handleDelete(item)\r\n      break\r\n  }\r\n}\r\n\r\n// 置顶操作\r\nconst handleTop = async (item) => {\r\n  let isTop = 1;\r\n  let text = '置顶成功'\r\n  if (item.isTop === 1) {\r\n    isTop = 0;\r\n    text = '取消置顶成功'\r\n  } else {\r\n    isTop = 1\r\n  }\r\n  swipeActionRef.value.closeAll()\r\n  await setSessionTopApi({\r\n    merchantGuid: userStore.merchantGuid,\r\n    sessionGuid: item.guid,\r\n    isTop: isTop\r\n  })\r\n  await getMySessionList()\r\n  uni.showToast({\r\n    title: text,\r\n    icon: 'success'\r\n  })\r\n}\r\nconst updateReq = reactive({\r\n  merchantGuid: userStore.merchantGuid, //商户uuid\r\n  sessionGuid: '', //智能体对话uuid\r\n  sessionTitle: '' //对话自定义标题\r\n})\r\n// 编辑操作\r\nconst handleEdit = (item) => {\r\n  console.log('编辑:', item.sessionTitle)\r\n  updateReq.sessionTitle = item.sessionTitle\r\n  updateReq.sessionGuid = item.guid;\r\n  showEditModal.value = true\r\n}\r\n\r\n// 删除操作\r\nconst handleDelete = (item) => {\r\n  uni.showModal({\r\n    title: '确认删除',\r\n    content: '确定要删除这个对话吗？',\r\n    success: async (res) => {\r\n      if (res.confirm) {\r\n        let res = await deleteSessionApi({\r\n          merchantGuid: userStore.merchantGuid,\r\n          sessionGuid: item.guid\r\n        })\r\n        if (res.code === 0) {\r\n          uni.showToast({\r\n            title: '已删除',\r\n            icon: 'success'\r\n          })\r\n          getMySessionList()\r\n        } else {\r\n          uni.showToast({\r\n            title: res.msg,\r\n            icon: 'none'\r\n          })\r\n        }\r\n\r\n      }\r\n    }\r\n  })\r\n}\r\n\r\n// 创建新智能体\r\nconst handleCreate = () => {\r\n  uni.navigateTo({\r\n    url: '/pages/create-agent/index'\r\n  })\r\n}\r\n\r\n// 关闭编辑弹窗\r\nconst closeEditModal = () => {\r\n  showEditModal.value = false\r\n  // editName.value = ''\r\n  // editingItem.value = null\r\n  updateReq.sessionTitle = '';\r\n  updateReq.sessionGuid = '';\r\n}\r\n\r\n// 确认编辑\r\nconst confirmEdit = async () => {\r\n  if (!updateReq.sessionTitle.trim()) {\r\n    uni.showToast({\r\n      title: '请输入对话名称',\r\n      icon: 'none'\r\n    })\r\n    return\r\n  }\r\n  await updateSessionTitleApi(updateReq)\r\n  // 更新对话名称\r\n  uni.showToast({\r\n    title: '修改成功',\r\n    icon: 'success'\r\n  })\r\n  closeEditModal()\r\n  getMySessionList()\r\n}\r\n// 获取对话列表\r\nconst getMySessionList = async (isRefresh = false) => {\r\n  try {\r\n    // 设置加载状态\r\n    if (isRefresh) {\r\n      refreshing.value = true\r\n    } else if (pageParams.page === 1) {\r\n      loading.value = true\r\n    } else {\r\n      loadingMore.value = true\r\n    }\r\n\r\n    const res = await getMySessionListApi({\r\n      merchantGuid: userStore.merchantGuid,\r\n      pageSize: pageParams.pageSize,\r\n      page: pageParams.page\r\n    })\r\n\r\n    if (res.data) {\r\n      // 更新分页信息\r\n      pageInfo.current_page = res.data.current_page\r\n      pageInfo.last_page = res.data.last_page\r\n      pageInfo.per_page = res.data.per_page\r\n      pageInfo.total = res.data.total\r\n\r\n      // 更新列表数据\r\n      if (isRefresh || pageParams.page === 1) {\r\n        // 刷新或首次加载，替换数据\r\n        sessionList.value = res.data.data || []\r\n      } else {\r\n        // 加载更多，追加数据\r\n        sessionList.value.push(...(res.data.data || []))\r\n      }\r\n\r\n      // 检查是否还有更多数据\r\n      if (pageInfo.current_page >= pageInfo.last_page) {\r\n        noMoreData.value = true\r\n      }\r\n    }\r\n  } catch (error) {\r\n    console.error('获取对话列表失败:', error)\r\n    uni.showToast({\r\n      title: '加载失败',\r\n      icon: 'none'\r\n    })\r\n  } finally {\r\n    // 重置加载状态\r\n    loading.value = false\r\n    refreshing.value = false\r\n    loadingMore.value = false\r\n  }\r\n}\r\nconst handleItemClick = (item) => {\r\n  uni.navigateTo({\r\n    url: `/pages/msg/index?sessionGuid=${item.agentGuid}&sysId=${item.agent.sysId}`\r\n  })\r\n}\r\nonShow(() => {\r\n  if (userStore.userToken) {\r\n    getMySessionList()\r\n  }\r\n})\r\nwatch(\r\n  () => userStore.userToken,\r\n  (newValue, oldValue) => {\r\n    if (newValue && oldValue === '') {\r\n      getMySessionList()\r\n    }\r\n  }\r\n);\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.container {\r\n  background-color: #ffffff;\r\n  height: 100vh;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.page-scroll {\r\n  flex: 1;\r\n  height: 100%;\r\n}\r\n\r\n.chat-list {\r\n  min-height: calc(100vh - 140rpx);\r\n  /* 减去底部按钮高度 */\r\n  //padding-bottom: 100px;\r\n}\r\n\r\n/* 加载状态样式 */\r\n.loading-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  min-height: 400rpx;\r\n  padding: 60rpx 0;\r\n\r\n  .loading-text {\r\n    font-size: 28rpx;\r\n    color: #999999;\r\n  }\r\n}\r\n\r\n/* 空状态样式 */\r\n.empty-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  min-height: 400rpx;\r\n  padding: 120rpx 0;\r\n\r\n  .empty-text {\r\n    font-size: 28rpx;\r\n    color: #999999;\r\n  }\r\n}\r\n\r\n/* 加载更多样式 */\r\n.load-more-container {\r\n  padding: 0;\r\n\r\n  .load-more-item {\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    padding: 32rpx 0;\r\n    background-color: #ffffff;\r\n\r\n    .load-more-text {\r\n      font-size: 26rpx;\r\n      color: #999999;\r\n    }\r\n  }\r\n}\r\n\r\n.chat-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 16px;\r\n  background-color: #ffffff;\r\n  border-bottom: 1px solid #f5f5f5;\r\n\r\n  &.selected {\r\n    background-color: #f0f0f0;\r\n  }\r\n\r\n  .avatar {\r\n    width: 50px;\r\n    height: 50px;\r\n    margin-right: 12px;\r\n    flex-shrink: 0;\r\n\r\n    .avatar-img {\r\n      width: 100%;\r\n      height: 100%;\r\n      border-radius: 25px;\r\n\r\n    }\r\n  }\r\n\r\n  .content {\r\n    flex: 1;\r\n    width: 530rpx;\r\n\r\n    .name {\r\n      font-size: 16px;\r\n      font-weight: 500;\r\n      color: #333;\r\n      margin-bottom: 4px;\r\n    }\r\n\r\n    .message {\r\n      font-size: 14px;\r\n      color: #666;\r\n      line-height: 1.4;\r\n      overflow: hidden;\r\n      text-overflow: ellipsis;\r\n      white-space: nowrap;\r\n    }\r\n  }\r\n\r\n  .badge {\r\n    width: 60rpx;\r\n    height: 100%;\r\n  }\r\n}\r\n\r\n.create-btn-container {\r\n  position: fixed;\r\n  width: 100%;\r\n  bottom: 30px;\r\n  left: 0;\r\n  // padding: 40rpx 32rpx;\r\n  // padding-bottom: calc(40rpx + env(safe-area-inset-bottom));\r\n  display: flex;\r\n  justify-content: center;\r\n  //background-color: #ffffff;\r\n  margin-top: 20rpx;\r\n\r\n\r\n  .create-btn {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    width: 300rpx;\r\n    height: 90rpx;\r\n    background: #3478f6;\r\n    border-radius: 48rpx;\r\n    border: none;\r\n\r\n    .plus-icon {\r\n      width: 32rpx;\r\n      height: 32rpx;\r\n      margin-right: 8px;\r\n    }\r\n\r\n    .create-text {\r\n      color: #fff;\r\n      font-size: 32rpx;\r\n      font-weight: 500;\r\n    }\r\n  }\r\n}\r\n\r\n\r\n/* 编辑弹窗样式 */\r\n.modal-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background-color: rgba(0, 0, 0, 0.5);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  z-index: 1000;\r\n}\r\n\r\n.edit-modal {\r\n  background-color: #ffffff;\r\n  border-radius: 12px;\r\n  width: 320px;\r\n  padding: 24px;\r\n  box-sizing: border-box;\r\n\r\n  .modal-title {\r\n    font-size: 18px;\r\n    font-weight: 600;\r\n    color: #333;\r\n    text-align: center;\r\n    margin-bottom: 24px;\r\n  }\r\n\r\n  .input-container {\r\n    margin-bottom: 32px;\r\n\r\n    .edit-input {\r\n      width: 100%;\r\n      height: 44px;\r\n      background-color: #F8F9FA;\r\n      border-radius: 8px;\r\n      padding: 0 16px;\r\n      font-size: 16px;\r\n      color: #333;\r\n      border: none;\r\n      box-sizing: border-box;\r\n\r\n      &::placeholder {\r\n        color: #999;\r\n      }\r\n    }\r\n  }\r\n\r\n  .modal-buttons {\r\n    display: flex;\r\n    gap: 12px;\r\n\r\n    .cancel-btn,\r\n    .confirm-btn {\r\n      flex: 1;\r\n      height: 44px;\r\n      border-radius: 8px;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      font-size: 16px;\r\n      font-weight: 500;\r\n    }\r\n\r\n    .cancel-btn {\r\n      background-color: #F8F9FA;\r\n      color: #666;\r\n    }\r\n\r\n    .confirm-btn {\r\n      background-color: #5A7BF7;\r\n      color: #fff;\r\n    }\r\n  }\r\n}\r\n</style>", "import MiniProgramPage from 'E:/youngProject/agent-mini-ui/pages/msg-list/msg.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useUserStore", "ref", "reactive", "setSessionTopApi", "uni", "res", "deleteSessionApi", "updateSessionTitleApi", "getMySessionListApi", "onShow", "watch"], "mappings": ";;;;;;;;;;;;;;;;;AAiFA,UAAM,YAAYA,YAAAA,aAAc;AAGhC,UAAM,cAAcC,cAAG,IAAC,EAAE;AAG1B,UAAM,aAAaC,cAAAA,SAAS;AAAA,MAC1B,MAAM;AAAA,MACN,UAAU;AAAA,IACZ,CAAC;AAGD,UAAM,UAAUD,cAAG,IAAC,KAAK;AACzB,UAAM,aAAaA,cAAG,IAAC,KAAK;AAC5B,UAAM,cAAcA,cAAG,IAAC,KAAK;AAC7B,UAAM,aAAaA,cAAG,IAAC,KAAK;AAG5B,UAAM,WAAWC,cAAAA,SAAS;AAAA,MACxB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT,CAAC;AAGD,UAAM,gBAAgBD,cAAG,IAAC,KAAK;AAG/B,UAAM,eAAeA,cAAAA,IAAI;AAAA,MACvB;AAAA,QACE,MAAM;AAAA,QACN,OAAO;AAAA,UACL,iBAAiB;AAAA,UACjB,OAAO;AAAA,QACR;AAAA,MACF;AAAA,MACD;AAAA,QACE,MAAM;AAAA,QACN,OAAO;AAAA,UACL,iBAAiB;AAAA,UACjB,OAAO;AAAA,QACR;AAAA,MACF;AAAA,MACD;AAAA,QACE,MAAM;AAAA,QACN,OAAO;AAAA,UACL,iBAAiB;AAAA,UACjB,OAAO;AAAA,QACR;AAAA,MACF;AAAA,IACH,CAAC;AACD,UAAM,oBAAoBA,cAAAA,IAAI;AAAA,MAC5B;AAAA,QACE,MAAM;AAAA,QACN,OAAO;AAAA,UACL,iBAAiB;AAAA,UACjB,OAAO;AAAA,QACR;AAAA,MACF;AAAA,MACD;AAAA,QACE,MAAM;AAAA,QACN,OAAO;AAAA,UACL,iBAAiB;AAAA,UACjB,OAAO;AAAA,QACR;AAAA,MACF;AAAA,MACD;AAAA,QACE,MAAM;AAAA,QACN,OAAO;AAAA,UACL,iBAAiB;AAAA,UACjB,OAAO;AAAA,QACR;AAAA,MACF;AAAA,IACH,CAAC;AACD,UAAM,iBAAiBA,cAAG,IAAC,IAAI;AAG/B,UAAM,YAAY,MAAM;AACtB,iBAAW,QAAQ;AACnB,iBAAW,OAAO;AAClB,iBAAW,QAAQ;AACnB,uBAAiB,IAAI;AAAA,IACvB;AAGA,UAAM,YAAY,MAAM;AACtB,iBAAW,QAAQ;AAAA,IACrB;AAGA,UAAM,aAAa,MAAM;AACvB,UAAI,YAAY,SAAS,WAAW;AAAO;AAE3C,UAAI,SAAS,eAAe,SAAS,WAAW;AAC9C,mBAAW;AACX,yBAAiB,KAAK;AAAA,MAC1B,OAAS;AACL,mBAAW,QAAQ;AAAA,MACpB;AAAA,IACH;AAGA,UAAM,mBAAmB,CAAC,OAAO,SAAS;AACxC,YAAM,EAAE,MAAK,IAAK;AAClB,cAAQ,OAAK;AAAA,QACX,KAAK;AACH,oBAAU,IAAI;AACd;AAAA,QACF,KAAK;AACH,qBAAW,IAAI;AACf;AAAA,QACF,KAAK;AACH,uBAAa,IAAI;AACjB;AAAA,MACH;AAAA,IACH;AAGA,UAAM,YAAY,OAAO,SAAS;AAChC,UAAI,QAAQ;AACZ,UAAI,OAAO;AACX,UAAI,KAAK,UAAU,GAAG;AACpB,gBAAQ;AACR,eAAO;AAAA,MACX,OAAS;AACL,gBAAQ;AAAA,MACT;AACD,qBAAe,MAAM,SAAU;AAC/B,YAAME,2BAAiB;AAAA,QACrB,cAAc,UAAU;AAAA,QACxB,aAAa,KAAK;AAAA,QAClB;AAAA,MACJ,CAAG;AACD,YAAM,iBAAkB;AACxBC,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAG;AAAA,IACH;AACA,UAAM,YAAYF,cAAAA,SAAS;AAAA,MACzB,cAAc,UAAU;AAAA;AAAA,MACxB,aAAa;AAAA;AAAA,MACb,cAAc;AAAA;AAAA,IAChB,CAAC;AAED,UAAM,aAAa,CAAC,SAAS;AAC3BE,oBAAY,MAAA,MAAA,OAAA,iCAAA,OAAO,KAAK,YAAY;AACpC,gBAAU,eAAe,KAAK;AAC9B,gBAAU,cAAc,KAAK;AAC7B,oBAAc,QAAQ;AAAA,IACxB;AAGA,UAAM,eAAe,CAAC,SAAS;AAC7BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,OAAO,QAAQ;AACtB,cAAI,IAAI,SAAS;AACf,gBAAIC,OAAM,MAAMC,2BAAiB;AAAA,cAC/B,cAAc,UAAU;AAAA,cACxB,aAAa,KAAK;AAAA,YAC5B,CAAS;AACD,gBAAID,KAAI,SAAS,GAAG;AAClBD,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,cAClB,CAAW;AACD,+BAAkB;AAAA,YAC5B,OAAe;AACLA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAOC,KAAI;AAAA,gBACX,MAAM;AAAA,cAClB,CAAW;AAAA,YACF;AAAA,UAEF;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAUA,UAAM,iBAAiB,MAAM;AAC3B,oBAAc,QAAQ;AAGtB,gBAAU,eAAe;AACzB,gBAAU,cAAc;AAAA,IAC1B;AAGA,UAAM,cAAc,YAAY;AAC9B,UAAI,CAAC,UAAU,aAAa,QAAQ;AAClCD,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AACD,YAAMG,UAAAA,sBAAsB,SAAS;AAErCH,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAG;AACD,qBAAgB;AAChB,uBAAkB;AAAA,IACpB;AAEA,UAAM,mBAAmB,OAAO,YAAY,UAAU;AACpD,UAAI;AAEF,YAAI,WAAW;AACb,qBAAW,QAAQ;AAAA,QACzB,WAAe,WAAW,SAAS,GAAG;AAChC,kBAAQ,QAAQ;AAAA,QACtB,OAAW;AACL,sBAAY,QAAQ;AAAA,QACrB;AAED,cAAM,MAAM,MAAMI,8BAAoB;AAAA,UACpC,cAAc,UAAU;AAAA,UACxB,UAAU,WAAW;AAAA,UACrB,MAAM,WAAW;AAAA,QACvB,CAAK;AAED,YAAI,IAAI,MAAM;AAEZ,mBAAS,eAAe,IAAI,KAAK;AACjC,mBAAS,YAAY,IAAI,KAAK;AAC9B,mBAAS,WAAW,IAAI,KAAK;AAC7B,mBAAS,QAAQ,IAAI,KAAK;AAG1B,cAAI,aAAa,WAAW,SAAS,GAAG;AAEtC,wBAAY,QAAQ,IAAI,KAAK,QAAQ,CAAE;AAAA,UAC/C,OAAa;AAEL,wBAAY,MAAM,KAAK,GAAI,IAAI,KAAK,QAAQ,EAAI;AAAA,UACjD;AAGD,cAAI,SAAS,gBAAgB,SAAS,WAAW;AAC/C,uBAAW,QAAQ;AAAA,UACpB;AAAA,QACF;AAAA,MACF,SAAQ,OAAO;AACdJ,sBAAAA,MAAA,MAAA,SAAA,iCAAc,aAAa,KAAK;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACL,UAAY;AAER,gBAAQ,QAAQ;AAChB,mBAAW,QAAQ;AACnB,oBAAY,QAAQ;AAAA,MACrB;AAAA,IACH;AACA,UAAM,kBAAkB,CAAC,SAAS;AAChCA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,gCAAgC,KAAK,SAAS,UAAU,KAAK,MAAM,KAAK;AAAA,MACjF,CAAG;AAAA,IACH;AACAK,kBAAAA,OAAO,MAAM;AACX,UAAI,UAAU,WAAW;AACvB,yBAAkB;AAAA,MACnB;AAAA,IACH,CAAC;AACDC,kBAAK;AAAA,MACH,MAAM,UAAU;AAAA,MAChB,CAAC,UAAU,aAAa;AACtB,YAAI,YAAY,aAAa,IAAI;AAC/B,2BAAkB;AAAA,QACnB;AAAA,MACF;AAAA,IACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7WA,GAAG,WAAW,eAAe;"}