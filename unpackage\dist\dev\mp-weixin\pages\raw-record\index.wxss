/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.withdraw-record-page.data-v-e241807e {
  background: #f5f5f5;
  min-height: 100vh;
}
.withdraw-record-page .record-list.data-v-e241807e {
  padding: 32rpx;
}
.withdraw-record-page .record-list .record-item.data-v-e241807e {
  background: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  overflow: hidden;
}
.withdraw-record-page .record-list .record-item .record-content.data-v-e241807e {
  padding: 40rpx 32rpx;
}
.withdraw-record-page .record-list .record-item .record-content .amount-section.data-v-e241807e {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}
.withdraw-record-page .record-list .record-item .record-content .amount-section .amount.data-v-e241807e {
  font-size: 40rpx;
  font-weight: 600;
  color: #3478f6;
}
.withdraw-record-page .record-list .record-item .record-content .amount-section .status.data-v-e241807e {
  font-size: 28rpx;
  font-weight: 500;
}
.withdraw-record-page .record-list .record-item .record-content .amount-section .status.status-success.data-v-e241807e {
  color: #52c41a;
}
.withdraw-record-page .record-list .record-item .record-content .amount-section .status.status-failed.data-v-e241807e {
  color: #ff4d4f;
}
.withdraw-record-page .record-list .record-item .record-content .amount-section .status.status-processing.data-v-e241807e {
  color: #52c41a;
}
.withdraw-record-page .record-list .record-item .record-content .time-section.data-v-e241807e {
  display: flex;
  align-items: center;
}
.withdraw-record-page .record-list .record-item .record-content .time-section .time-label.data-v-e241807e {
  font-size: 28rpx;
  color: #999999;
}
.withdraw-record-page .record-list .record-item .record-content .time-section .time-value.data-v-e241807e {
  font-size: 28rpx;
  color: #999999;
  margin-left: 8rpx;
}
.withdraw-record-page .empty-state.data-v-e241807e {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 200rpx 0;
}
.withdraw-record-page .empty-state .empty-icon.data-v-e241807e {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
  opacity: 0.6;
}
.withdraw-record-page .empty-state .empty-text.data-v-e241807e {
  font-size: 28rpx;
  color: #999999;
}