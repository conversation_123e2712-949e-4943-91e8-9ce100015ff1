"use strict";const e=require("../../common/vendor.js"),a=require("../../stores/user.js"),s=require("../../api/index.js");if(!Array){(e.resolveComponent("uni-swipe-action-item")+e.resolveComponent("uni-swipe-action"))()}Math||((()=>"../../uni_modules/uni-swipe-action/components/uni-swipe-action-item/uni-swipe-action-item.js")+(()=>"../../uni_modules/uni-swipe-action/components/uni-swipe-action/uni-swipe-action.js"))();const t={__name:"msg",setup(t){const o=a.useUserStore(),i=e.ref([]),n=e.reactive({page:1,pageSize:10}),l=e.ref(!1),u=e.ref(!1),r=e.ref(!1),c=e.ref(!1),d=e.reactive({current_page:1,last_page:1,per_page:10,total:0}),g=e.ref(!1),p=e.ref([{text:"置顶",style:{backgroundColor:"#5A7BF7",color:"#fff"}},{text:"编辑",style:{backgroundColor:"#FF9F40",color:"#fff"}},{text:"删除",style:{backgroundColor:"#FF6B6B",color:"#fff"}}]),v=e.ref([{text:"取消置顶",style:{backgroundColor:"#FF6B6B",color:"#fff"}},{text:"编辑",style:{backgroundColor:"#FF9F40",color:"#fff"}},{text:"删除",style:{backgroundColor:"#FF6B6B",color:"#fff"}}]),f=e.ref(null),h=()=>{u.value=!0,n.page=1,c.value=!1,k(!0)},m=()=>{u.value=!1},w=()=>{r.value||c.value||(d.current_page<d.last_page?(n.page++,k(!1)):c.value=!0)},T=async a=>{let t=1,i="置顶成功";1===a.isTop?(t=0,i="取消置顶成功"):t=1,f.value.closeAll(),await s.setSessionTopApi({merchantGuid:o.merchantGuid,sessionGuid:a.guid,isTop:t}),await k(),e.index.showToast({title:i,icon:"success"})},_=e.reactive({merchantGuid:o.merchantGuid,sessionGuid:"",sessionTitle:""}),x=e=>{console.log("编辑:",e.sessionTitle),_.sessionTitle=e.sessionTitle,_.sessionGuid=e.guid,g.value=!0},y=a=>{e.index.showModal({title:"确认删除",content:"确定要删除这个对话吗？",success:async t=>{if(t.confirm){let t=await s.deleteSessionApi({merchantGuid:o.merchantGuid,sessionGuid:a.guid});0===t.code?(e.index.showToast({title:"已删除",icon:"success"}),k()):e.index.showToast({title:t.msg,icon:"none"})}}})},G=()=>{g.value=!1,_.sessionTitle="",_.sessionGuid=""},F=async()=>{_.sessionTitle.trim()?(await s.updateSessionTitleApi(_),e.index.showToast({title:"修改成功",icon:"success"}),G(),k()):e.index.showToast({title:"请输入对话名称",icon:"none"})},k=async(a=!1)=>{try{a?u.value=!0:1===n.page?l.value=!0:r.value=!0;const e=await s.getMySessionListApi({merchantGuid:o.merchantGuid,pageSize:n.pageSize,page:n.page});e.data&&(d.current_page=e.data.current_page,d.last_page=e.data.last_page,d.per_page=e.data.per_page,d.total=e.data.total,a||1===n.page?i.value=e.data.data||[]:i.value.push(...e.data.data||[]),d.current_page>=d.last_page&&(c.value=!0))}catch(t){console.error("获取对话列表失败:",t),e.index.showToast({title:"加载失败",icon:"none"})}finally{l.value=!1,u.value=!1,r.value=!1}};return e.onShow((()=>{o.userToken&&k()})),e.watch((()=>o.userToken),((e,a)=>{e&&""===a&&k()})),(a,s)=>e.e({a:l.value&&0===i.value.length},l.value&&0===i.value.length?{}:l.value||0!==i.value.length?e.e({c:e.f(i.value,((a,s,t)=>({a:a.agent.agentAvatar,b:e.t(a.sessionTitle),c:e.t(a.lastMessage.content),d:e.o((s=>(a=>{e.index.navigateTo({url:`/pages/msg/index?sessionGuid=${a.agentGuid}&sysId=${a.agent.sysId}`})})(a)),a.guid),e:e.o((e=>((e,a)=>{const{index:s}=e;switch(s){case 0:T(a);break;case 1:x(a);break;case 2:y(a)}})(e,a)),a.guid),f:a.guid,g:"5a716017-1-"+t+",5a716017-0",h:e.p({threshold:0,"right-options":1===a.isTop?v.value:p.value})}))),d:e.sr(f,"5a716017-0",{k:"swipeActionRef"}),e:i.value.length>0},i.value.length>0?e.e({f:r.value},(r.value||c.value,{}),{g:c.value}):{}):{},{b:!l.value&&0===i.value.length,h:u.value,i:e.o(h),j:e.o(m),k:e.o(w),l:g.value},g.value?{m:g.value,n:_.sessionTitle,o:e.o((e=>_.sessionTitle=e.detail.value)),p:e.o(G),q:e.o(F),r:e.o((()=>{})),s:e.o(G)}:{})}},o=e._export_sfc(t,[["__scopeId","data-v-5a716017"]]);wx.createPage(o);
