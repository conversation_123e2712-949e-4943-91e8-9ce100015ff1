"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const stores_user = require("../../stores/user.js");
const api_index = require("../../api/index.js");
const config_config = require("../../config/config.js");
const sqbg = "https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/eb2cbe34cdce4cda956ba6715110ee38.png";
const _sfc_main = {
  __name: "index",
  setup(__props) {
    let update = "https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/a2b6d4b5e3374fc79f54819c5f5c09e3.png";
    const userStore = stores_user.useUserStore();
    const icons = {
      maintenance: common_assets.maintenance,
      activity: common_assets.activity,
      system: common_assets.system,
      update
    };
    let indexBanner = common_vendor.ref([]);
    const getBannerList = async () => {
      let res = await api_index.getBannerListApi({
        merchantGuid: userStore.merchantGuid,
        bannerType: "user_pay_agent"
      });
      indexBanner.value = res.data;
    };
    const onLinkTo = (item) => {
      var tabPages = ["pages/index/index", "pages/msg-list/msg", "pages/square/square", "pages/my/my"];
      switch (item.linkType) {
        case 1:
          common_vendor.index.navigateTo({
            url: "pages/webview/webview",
            success: function success(res) {
              res.eventChannel.emit("urlEvent", item.linkUrl);
            }
          });
          break;
        case 2:
          if (tabPages.includes(item.linkUrl)) {
            common_vendor.index.switchTab({
              url: "/" + item.linkUrl
            });
          } else {
            common_vendor.index.navigateTo({
              url: "/" + item.linkUrl
            });
          }
          break;
        case 3:
          common_vendor.index.navigateToMiniProgram({
            appId: item.linkUrl,
            fail: function fail(err) {
              common_vendor.index.$u.toast("跳转失败...");
            }
          });
          break;
      }
    };
    const handleCardClick = (agentName) => {
      common_vendor.index.navigateTo({
        url: `/pages/square/search?agentName=${agentName}`
      });
    };
    const handleMoreLick = () => {
      common_vendor.index.switchTab({
        url: "/pages/square/square"
      });
    };
    const handleTagClick = (categoryGuid) => {
      common_vendor.index.__f__("log", "at pages/index/index.vue:188", "点击标签:", categoryGuid);
      userStore.set_target_category(categoryGuid);
      common_vendor.index.switchTab({
        url: "/pages/square/square"
      });
    };
    const handleCreatorClick = (item) => {
      common_vendor.index.__f__("log", "at pages/index/index.vue:205", "点击创作者:", item);
      var tabPages = ["pages/index/index", "pages/msg-list/msg", "pages/square/square", "pages/my/my"];
      switch (item.jumpType) {
        case 1:
          if (tabPages.includes(item.link)) {
            common_vendor.index.switchTab({
              url: "/" + item.link
            });
          } else {
            common_vendor.index.navigateTo({
              url: "/" + item.link
            });
          }
          break;
        case 2:
          common_vendor.index.navigateTo({
            url: "pages/webview/webview",
            success: function success(res) {
              res.eventChannel.emit("urlEvent", item.link);
            }
          });
          break;
        case 3:
          common_vendor.index.navigateToMiniProgram({
            appId: item.link,
            fail: function fail(err) {
              common_vendor.index.$u.toast("跳转失败...");
            }
          });
          break;
      }
    };
    common_vendor.onShareAppMessage(() => {
      return {
        title: userStore.appName || "智能体",
        path: `/pages/index/index?invite=${userStore.invitationCode}`,
        imageUrl: bannerList.value[0] || config_config.base.shareImg,
        success(res) {
          common_vendor.index.showToast({
            title: "分享成功"
          });
        },
        fail(res) {
          common_vendor.index.showToast({
            title: "分享失败",
            icon: "none"
          });
        }
      };
    });
    common_vendor.onShareTimeline(() => {
      return {
        title: userStore.appName || "智能体",
        path: `/pages/index/index?invite=${userStore.invitationCode}`,
        imageUrl: bannerList.value[0] || config_config.base.shareImg,
        success(res) {
          common_vendor.index.showToast({
            title: "分享成功"
          });
        },
        fail(res) {
          common_vendor.index.showToast({
            title: "分享失败",
            icon: "none"
          });
        }
      };
    });
    let fullGreeting = common_vendor.ref("");
    let recommendAgents = common_vendor.ref([]);
    let recommendCategories = common_vendor.ref([]);
    let systemNotices = common_vendor.ref([]);
    let bannerList = common_vendor.ref([]);
    const getHomeData = async () => {
      let res = await api_index.getHomeDataApi({
        merchantGuid: userStore.merchantGuid
      });
      fullGreeting.value = res.data.dailyGreeting.fullGreeting;
      recommendAgents.value = res.data.recommendAgents;
      systemNotices.value = res.data.systemNotices;
      recommendCategories.value = res.data.recommendCategories;
      bannerList.value = res.data.bannerList;
    };
    const shenqun_img = common_vendor.ref("");
    const showImageModal = common_vendor.ref(false);
    const showBannerUrls = async () => {
      let res = await api_index.showBannerUrlsApi({
        merchantGuid: userStore.merchantGuid
      });
      shenqun_img.value = res.data.shenqun_img;
    };
    const onShowPoster = () => {
      showImageModal.value = true;
    };
    const closeImageModal = () => {
      showImageModal.value = false;
    };
    common_vendor.onLoad(() => {
      getHomeData();
      showBannerUrls();
      getBannerList();
    });
    common_vendor.watch(
      () => userStore.userToken,
      (newValue, oldValue) => {
        if (newValue && oldValue === "") {
          getHomeData();
          showBannerUrls();
        }
      }
    );
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.t(common_vendor.unref(fullGreeting)),
        b: common_assets._imports_0,
        c: sqbg,
        d: common_assets._imports_1,
        e: common_vendor.o(onShowPoster),
        f: common_vendor.f(common_vendor.unref(recommendAgents), (item, index, i0) => {
          return {
            a: item.agentAvatar,
            b: common_vendor.t(item.agentName),
            c: common_vendor.t(item.agentDesc),
            d: common_vendor.t(item.creator.nickname),
            e: common_vendor.o(($event) => handleCardClick(item.agentName), item.guid),
            f: item.guid
          };
        }),
        g: common_assets._imports_2,
        h: common_vendor.o(handleMoreLick),
        i: common_vendor.f(common_vendor.unref(recommendCategories), (item, k0, i0) => {
          return {
            a: common_vendor.t(item.categoryName),
            b: item.guid,
            c: common_vendor.o(($event) => handleTagClick(item.guid), item.guid)
          };
        }),
        j: common_vendor.f(common_vendor.unref(indexBanner), (item, index, i0) => {
          return {
            a: item.bannerImg,
            b: index,
            c: common_vendor.o(($event) => onLinkTo(item), index)
          };
        }),
        k: common_vendor.f(common_vendor.unref(systemNotices), (item, k0, i0) => {
          return {
            a: icons[item.type],
            b: common_vendor.t(item.title),
            c: common_vendor.t(item.content),
            d: item.guid,
            e: common_vendor.o(($event) => handleCreatorClick(item), item.guid)
          };
        }),
        l: showImageModal.value
      }, showImageModal.value ? common_vendor.e({
        m: shenqun_img.value
      }, shenqun_img.value ? {
        n: shenqun_img.value,
        o: common_vendor.o((...args) => _ctx.onImageLoad && _ctx.onImageLoad(...args)),
        p: common_vendor.o((...args) => _ctx.onImageError && _ctx.onImageError(...args))
      } : {}, {
        q: common_vendor.o(closeImageModal)
      }) : {});
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-1cf27b2a"]]);
_sfc_main.__runtimeHooks = 6;
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/index/index.js.map
