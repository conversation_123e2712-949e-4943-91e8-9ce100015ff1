<template>
	<view class="nav-box">
		<view class="nav_nar" :style="{
			height: navHeight + 'px',
			backgroundImage:
				'url(https://api.zhimoai.com/storage/topic/20240830/22c0fd242cddcc1d0f7ccbbf8f4fcff1.png)',
		}">
			<view class="status_bar" :style="{ height: menuButtonTop + 'px' }"></view>
			<view class="nav-box" :style="{ height: menuButtonHeight + 'px' }">
				<view class="back-btn" v-if="isBack" @click="handleBack">
					<uni-icons v-if="isWhite" type="left" color="#000000" size="24"></uni-icons>
					<uni-icons v-else type="left" color="#ffffff" size="24"></uni-icons>
				</view>
				<view :class="['home-btn', { white: isWhite }]" v-if="isHome" @click="handleHome">
					<uni-icons type="home" :color="isWhite ? '#333' : '#ffffff'" size="24"></uni-icons>
				</view>
				<view :class="['title', { 'title-center': isBack }, { white: isWhite }, { black: !isWhite }]" v-if="isTitle">
					<view class="text">{{ title }}</view>
				</view>
			</view>
		</view>
		<view class="placeholder-box" :style="{ height: navHeight + 'px' }"></view>
	</view>
</template>

<script setup>
import {
	ref,
	onMounted
} from 'vue';
import {
	systemInfo
} from '@/utils/systemInfo.js';
const props = defineProps({
	title: {
		type: String,
		default: '',
	},
	isBack: {
		// 是否需要返回按钮
		type: Boolean,
		default: true,
	},

	isTitle: {
		type: Boolean,
		default: true,
	},
	isWhite: {
		type: Boolean,
		default: true,
	},
});
// import imgUrl from '@/common/imgUrl.js';
let navHeight = ref(164);
let navigationBarHeight = ref(80);
let menuButtonTop = ref(0);
let statusBarHeight = ref(0);
let menuButtonHeight = ref(32);
const isHome = ref(false)
// const isBack = ref(false)
onMounted(() => {
	/* 获取设备信息 */
	const SystemInfomations = systemInfo();
	// #ifdef MP-WEIXIN
	menuButtonHeight.value = SystemInfomations.menuButtonHeight;
	//判断在pc端打开小程序情况下 状态栏高度为0时
	if (SystemInfomations.statusBarHeight === 0) {
		menuButtonTop.value = 4;
	} else {
		menuButtonTop.value = SystemInfomations.menuButtonTop;
	}
	navHeight.value = SystemInfomations.navHeight + SystemInfomations.statusBarHeight; //头部导航栏总高度
	statusBarHeight.value = SystemInfomations.statusBarHeight;
	// #endif
	checkIsHome()
});
const checkIsHome = () => {
	let pages = getCurrentPages();
	let currentPage = pages[pages.length - 1]
	if (currentPage.route !== 'pages/index/index' && currentPage.route !== 'pages/my/my' && pages.length === 1) {
		isHome.value = true;
	}
	// else {
	// 	isBack.value = true;
	// }
}
const handleBack = () => {
	try {
		uni.navigateBack({
			delta: 1,
		});
	} catch {
		uni.redirectTo({
			url: 'pages/index/index',
		});
	}
};
const handleHome = () => {
	uni.reLaunch({
		url: '/pages/index/index',
	});
};
</script>

<style scoped lang="scss">
.bgimg {
	position: fixed;
	z-index: 98;
	width: 100%;
	top: 0px;
	left: 0px;
}

.nav_nar {
	position: fixed;
	top: 0px;
	left: 0px;
	width: 100%;
	box-sizing: border-box;
	// background: linear-gradient(65deg, #10131e, #10131c);
	// background: linear-gradient(65deg, #161922, #1c202a);
	background-repeat: no-repeat;
	background-size: 100% 518rpx;
	z-index: 99;

	// background-color: #161922;
	// background-color: #151A25;
	.status_bar {
		height: var(--status-bar-height);
		width: 100%;
	}

	.nav-box {
		// padding-left: 28rpx;
		display: flex;
		align-items: center;
		position: relative;
		justify-content: center;

		.back-btn {
			position: absolute;
			left: 28rpx;
			width: 64rpx;
			height: 64rpx;
			z-index: 9;
			display: flex;
			justify-content: center;
			align-items: center;

			.image {
				width: 100%;
				height: 100%;
			}
		}

		.home-btn {
			position: absolute;
			left: 28rpx;
			width: 64rpx;
			height: 64rpx;
			z-index: 9;
			display: flex;
			justify-content: center;
			align-items: center;
			border-radius: 50%;
			background-color: #272c39;

			&.white {
				background-color: #fff;
			}
		}

		.title {
			color: #fff;
			flex: 1;
			// padding-left: 28rpx;
			display: flex;
			justify-content: center;

			&.title-center {
				position: absolute;
				padding-left: 0px;
				z-index: 8;
				width: 100%;
				text-align: center;
			}

			.text {
				width: 300rpx;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
				text-align: center;
			}

			&.white {
				color: #333;
			}

			&.black {
				color: #fff;
			}
		}
	}
}
</style>