<view class="create-agent-page data-v-a2eb353b"><scroll-view class="page-scroll data-v-a2eb353b" scroll-y><view class="avatar-section data-v-a2eb353b"><view class="avatar-container data-v-a2eb353b" bindtap="{{e}}"><image src="{{a}}" class="avatar data-v-a2eb353b" mode="aspectFill"/><view wx:if="{{b}}" class="avatar-add data-v-a2eb353b"><image src="{{c}}" class="add-icon data-v-a2eb353b" mode="aspectFit"/></view><view wx:if="{{d}}" class="avatar-loading data-v-a2eb353b"><view class="loading-spinner data-v-a2eb353b"></view></view></view><text bindtap="{{g}}" class="{{['avatar-label', 'data-v-a2eb353b', h && 'disabled']}}">{{f}}</text></view><view class="form-section data-v-a2eb353b"><view class="form-item data-v-a2eb353b"><text class="label data-v-a2eb353b">选择分类:</text><view class="category-selector data-v-a2eb353b" bindtap="{{k}}"><text class="{{['category-text', 'data-v-a2eb353b', j && 'placeholder']}}">{{i}}</text></view></view><view class="form-item data-v-a2eb353b"><text class="label data-v-a2eb353b">名称:</text><input class="input data-v-a2eb353b" placeholder="输入名称" placeholder-class="placeholder" maxlength="50" value="{{l}}" bindinput="{{m}}"/></view><view class="form-item data-v-a2eb353b"><text class="label data-v-a2eb353b">智能体描述:</text><block wx:if="{{r0}}"><textarea class="textarea data-v-a2eb353b" placeholder="输入描述" placeholder-class="placeholder" maxlength="500" value="{{n}}" bindinput="{{o}}"/></block></view><view class="form-item data-v-a2eb353b"><text class="label data-v-a2eb353b">选择智能体类型:</text><view class="radio-group data-v-a2eb353b"><view wx:for="{{p}}" wx:for-item="type" wx:key="d" class="radio-item data-v-a2eb353b" bindtap="{{type.e}}"><view class="{{['radio', 'data-v-a2eb353b', type.b && 'checked']}}"><view wx:if="{{type.a}}" class="radio-inner data-v-a2eb353b"></view></view><text class="radio-text data-v-a2eb353b">{{type.c}}</text></view></view></view><view wx:if="{{q}}" class="form-item data-v-a2eb353b"><text class="label data-v-a2eb353b">角色设定提示词:</text><block wx:if="{{r0}}"><textarea class="textarea large data-v-a2eb353b" placeholder="输入提示词" placeholder-class="placeholder" maxlength="2000" value="{{r}}" bindinput="{{s}}"/></block></view><view wx:if="{{t}}" class="form-item data-v-a2eb353b"><text class="label data-v-a2eb353b">扣子智能体ID:</text><input class="input data-v-a2eb353b" placeholder="输入密码" placeholder-class="placeholder" value="{{v}}" bindinput="{{w}}"/></view><view wx:if="{{x}}" class="form-item data-v-a2eb353b"><text class="label data-v-a2eb353b">智能体秘钥:</text><input class="input data-v-a2eb353b" placeholder="输入秘钥" placeholder-class="placeholder" value="{{y}}" bindinput="{{z}}"/></view><view class="form-item data-v-a2eb353b"><text class="label data-v-a2eb353b">开场白:</text><input class="input data-v-a2eb353b" placeholder="输入开场白" placeholder-class="placeholder" value="{{A}}" bindinput="{{B}}"/></view><view class="form-item data-v-a2eb353b"><text class="label data-v-a2eb353b">引导问题列表:</text><view class="question-list data-v-a2eb353b"><view wx:for="{{C}}" wx:for-item="question" wx:key="d" class="question-item data-v-a2eb353b"><input class="question-input data-v-a2eb353b" placeholder="{{question.a}}" placeholder-class="placeholder" value="{{question.b}}" bindinput="{{question.c}}"/></view></view></view><view class="form-item data-v-a2eb353b"><text class="label data-v-a2eb353b">选择智能体类型:</text><view class="radio-group data-v-a2eb353b"><view wx:for="{{D}}" wx:for-item="visibility" wx:key="d" class="radio-item data-v-a2eb353b" bindtap="{{visibility.e}}"><view class="{{['radio', 'data-v-a2eb353b', visibility.b && 'checked']}}"><view wx:if="{{visibility.a}}" class="radio-inner data-v-a2eb353b"></view></view><text class="radio-text data-v-a2eb353b">{{visibility.c}}</text></view></view></view><view wx:if="{{E}}" class="form-item data-v-a2eb353b"><text class="label data-v-a2eb353b">收费金额:</text><input class="input data-v-a2eb353b" placeholder="输入金额" placeholder-class="placeholder" type="digit" value="{{F}}" bindinput="{{G}}"/></view><view wx:if="{{H}}" class="form-item data-v-a2eb353b"><text class="label data-v-a2eb353b">试用聊天次数:</text><input class="input data-v-a2eb353b" placeholder="试用聊天次数" placeholder-class="placeholder" type="digit" value="{{I}}" bindinput="{{J}}"/></view><view class="form-note data-v-a2eb353b"><text class="note-text data-v-a2eb353b">注意：智能体不得违反相关法律法规，禁止涉及政治敏感话题，详情请查看《平台协议》</text></view></view><view class="create-section data-v-a2eb353b"><view bindtap="{{L}}" class="{{['create-btn', 'data-v-a2eb353b', M && 'disabled']}}"><text class="create-text data-v-a2eb353b">{{K}}</text></view></view></scroll-view><view wx:if="{{N}}" class="category-modal data-v-a2eb353b" bindtap="{{aa}}"><view class="modal-content data-v-a2eb353b" catchtap="{{Z}}"><view class="modal-header data-v-a2eb353b"><text class="modal-title data-v-a2eb353b">选择分类</text></view><view class="search-section data-v-a2eb353b"><view class="search-box data-v-a2eb353b"><image src="{{O}}" class="search-icon data-v-a2eb353b" mode="aspectFit"/><input class="search-input data-v-a2eb353b" placeholder="搜索分类" placeholder-class="placeholder" bindinput="{{P}}" value="{{Q}}"/><view wx:if="{{R}}" class="clear-btn data-v-a2eb353b" bindtap="{{T}}"><image src="{{S}}" class="clear-icon data-v-a2eb353b" mode="aspectFit"/></view></view></view><scroll-view class="category-list data-v-a2eb353b" scroll-y><view wx:for="{{U}}" wx:for-item="category" wx:key="d" class="category-item data-v-a2eb353b" bindtap="{{category.e}}"><view class="{{['radio', 'data-v-a2eb353b', category.b && 'checked']}}"><view wx:if="{{category.a}}" class="radio-inner data-v-a2eb353b"></view></view><text class="category-name data-v-a2eb353b">{{category.c}}</text></view><view wx:if="{{V}}" class="empty-state data-v-a2eb353b"><text class="empty-text data-v-a2eb353b">暂无匹配的分类</text></view></scroll-view><view class="modal-footer data-v-a2eb353b"><view class="cancel-btn data-v-a2eb353b" bindtap="{{W}}"><text class="cancel-text data-v-a2eb353b">取消</text></view><view bindtap="{{X}}" class="{{['confirm-btn', 'data-v-a2eb353b', Y && 'disabled']}}"><text class="confirm-text data-v-a2eb353b">确认</text></view></view></view></view></view>