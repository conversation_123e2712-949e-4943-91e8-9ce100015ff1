"use strict";const e=require("../common/vendor.js"),t=require("../config/config.js"),o=require("../utils/requestUtil.js"),r=require("../stores/user.js");const n=t.base.baseUrl,s=(t,o,r)=>{e.index.showToast({title:t||"网络连接超时,请稍后重试!",icon:o||"none",duration:r||1500})},a=e=>{if(e){let t="";for(let o in e){let r=e[o];Array.isArray(r)&&(r=r.join(",")),t+=`&${o}=${r}`}return t=t.replace("&","?"),t}return""};exports.request=t=>{const i=r.useUserStore();if(t.urlSuffix={app_guid:"e108201b02ae42e686bcc4c302cbbd11",expires:parseInt(((new Date).getTime()/1e3).toFixed(0)),token:i.userToken||"notoken",noncestr:o.randomStr(!0,!0,!0,32),merchantGuid:i.merchantGuid,app_type:"wechat"},t.urlSuffix.signature=encodeURIComponent(o.autographFun(t)),t.params){let e=t.url+"?"+(e=>{let t="";for(const o of(void 0).getKeys(e)){const r=e[o];let n=encodeURIComponent(o)+"=";if(null!==r&&""!==r&&void 0!==r)if("object"==typeof r)for(const e of(void 0).getKeys(r))null!==r[e]&&""!==r[e]&&void 0!==r[e]&&(t+=encodeURIComponent(o+"["+e+"]")+"="+encodeURIComponent(r[e])+"&");else t+=n+encodeURIComponent(r)+"&"}return t})(t.params);e=e.slice(0,-1),t.url=e}return new Promise(((o,r)=>{e.index.request({method:t.method||"POST",timeout:t.timeout||6e4,url:n+t.url+a(t.urlSuffix),data:t.data,header:t.header,dataType:"json",success(e){704001!==e.data.code?0===e.data.code?o(e.data):(console.log(e.data,"--------------------???"),s(e.data.msg),r(e.data)):i.delete_user_info()},fail:e=>{console.log(e,"errorerrorerror");let{errMsg:t}=e;"Network Error"===t?t="后端接口连接异常":t.includes("timeout")?t="AI算力繁忙":t.includes("Request failed with status code")&&(t="系统接口"+t.substr(t.length-3)+"异常"),s(t,"none",1500),r(e)},complete(e){}})}))};
