/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.subscribe-popup-overlay.data-v-58f08366 {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 999;
}
.subscribe-popup-content.data-v-58f08366 {
  height: 1058rpx;
  width: 750rpx;
  position: relative;
}
.subscribe-popup-content .bg.data-v-58f08366 {
  width: 100%;
  height: 100%;
}
.subscribe-popup-content .content-box.data-v-58f08366 {
  position: absolute;
  left: 74rpx;
  top: 185rpx;
  width: 600rpx;
  height: 642rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 30rpx;
  box-sizing: border-box;
}
.close-btn.data-v-58f08366 {
  position: absolute;
  bottom: 116rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.close-btn .icon.data-v-58f08366 {
  width: 60rpx;
  height: 60rpx;
  display: block;
}
.agent-avatar-section.data-v-58f08366 {
  margin-top: -90rpx;
}
.agent-avatar-section .agent-avatar.data-v-58f08366 {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  background: #f0f0f0;
  border: 6rpx solid #4CAF50;
}
.agent-info-section.data-v-58f08366 {
  width: 100%;
  margin-bottom: 40rpx;
  text-align: center;
  margin-top: 60rpx;
}
.agent-info-section .agent-name.data-v-58f08366 {
  display: block;
  font-size: 32rpx;
  color: #333333;
  font-weight: 600;
  margin-bottom: 20rpx;
}
.agent-info-section .agent-desc.data-v-58f08366 {
  display: block;
  font-size: 28rpx;
  color: #666666;
  line-height: 1.5;
}
.price-section.data-v-58f08366 {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30rpx;
}
.price-section .price-label.data-v-58f08366 {
  font-size: 28rpx;
  color: #333333;
  margin-right: 10rpx;
}
.price-section .price-value.data-v-58f08366 {
  font-size: 48rpx;
  color: #3478f6;
  font-weight: 700;
}
.subscribe-btn.data-v-58f08366 {
  width: 310rpx;
  height: 100rpx;
  background: linear-gradient(99deg, #2A5AF6 0%, #1198FF 100%);
  border-radius: 50rpx 50rpx 50rpx 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(52, 120, 246, 0.3);
}
.subscribe-btn.ios.data-v-58f08366 {
  background: #999;
}
.subscribe-btn .subscribe-text.data-v-58f08366 {
  font-size: 32rpx;
  color: #ffffff;
  font-weight: 600;
}