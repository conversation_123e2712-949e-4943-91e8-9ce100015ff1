/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.my-figure-container.data-v-da8e5c67 {
  min-height: 100vh;
  background: #ffffff;
  padding-bottom: 160rpx;
  display: flex;
  flex-direction: column;
}
.figure-content.data-v-da8e5c67 {
  padding: 32rpx 30rpx;
  flex: 1;
}
.figure-grid.data-v-da8e5c67 {
  display: flex;
  flex-wrap: wrap;
}
.figure-grid .figure-upload.data-v-da8e5c67 {
  width: 330rpx;
  height: 330rpx;
  display: flex;
  border-radius: 15rpx;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #FAFAFA;
  border: 2rpx dashed #DBDBDB !important;
}
.figure-grid .figure-upload .create-icon.data-v-da8e5c67 {
  width: 40rpx;
  height: 40rpx;
  display: block;
  margin-bottom: 16rpx;
}
.figure-grid .figure-upload .create-title.data-v-da8e5c67 {
  font-size: 26rpx;
  color: #999999;
  margin-bottom: 4rpx;
}
.figure-grid .figure-upload .create-subtitle.data-v-da8e5c67 {
  font-size: 24rpx;
  color: #FD8D2B;
}
.figure-grid .figure-item.data-v-da8e5c67 {
  margin-bottom: 40rpx;
  position: relative;
}
.figure-grid .figure-list.data-v-da8e5c67 {
  width: 326rpx;
}
.figure-grid .figure-list .avatar-container.data-v-da8e5c67 {
  position: relative;
  flex: 1;
  margin-bottom: 24rpx;
  border-radius: 16rpx;
  overflow: hidden;
  border: 3rpx solid transparent;
}
.figure-grid .figure-list .avatar-container.selected.data-v-da8e5c67 {
  border: 3rpx solid #2A64F6;
}
.figure-grid .figure-list .avatar-container .avatar-bg.data-v-da8e5c67 {
  width: 330rpx;
  height: 330rpx;
  background: #000000;
  display: flex;
  align-items: center;
  justify-content: center;
}
.figure-grid .figure-list .avatar-container .avatar-bg .avatar-image.data-v-da8e5c67 {
  height: 100%;
  width: auto;
  max-width: 100%;
}
.figure-grid .figure-list .avatar-container .badge.data-v-da8e5c67 {
  position: absolute;
  top: 16rpx;
  left: 16rpx;
  background: linear-gradient(132deg, #FFFFB5 0%, #FDDB66 49%, #FAF7B0 100%);
  color: #4A3900;
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-weight: 600;
}
.figure-grid .figure-list .avatar-container .check-mark.data-v-da8e5c67 {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.figure-grid .figure-list .avatar-container .check-mark .check-icon.data-v-da8e5c67 {
  width: 40rpx;
  height: 40rpx;
}
.figure-grid .figure-list .avatar-info.data-v-da8e5c67 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}
.figure-grid .figure-list .avatar-info .avatar-name.data-v-da8e5c67 {
  font-size: 28rpx;
  color: #999999;
  font-weight: 500;
  width: calc(100% - 40rpx);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.figure-grid .figure-list .avatar-info .more-options.data-v-da8e5c67 {
  width: 30rpx;
  height: 30rpx;
}
.figure-grid .figure-item.data-v-da8e5c67:not(:nth-child(2n-1)) {
  margin-left: 30rpx;
}
.bottom-actions.data-v-da8e5c67 {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  background: #ffffff;
  padding: 32rpx;
  border-top: 1rpx solid #f0f0f0;
  gap: 24rpx;
}
.bottom-actions .action-button.data-v-da8e5c67 {
  flex: 1;
  height: 96rpx;
  border-radius: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.bottom-actions .action-button.edit .edit-icon.data-v-da8e5c67 {
  width: 32rpx;
  height: 32rpx;
  margin-right: 12rpx;
}
.bottom-actions .action-button.edit .action-text.data-v-da8e5c67 {
  color: #666666;
}
.bottom-actions .action-button.cancel.data-v-da8e5c67 {
  background: #f8f8f8;
}
.bottom-actions .action-button.cancel .action-text.data-v-da8e5c67 {
  color: #666666;
}
.bottom-actions .action-button.delete.data-v-da8e5c67 {
  background: #ffffff;
  border: 2rpx solid #FA5151;
}
.bottom-actions .action-button.delete .action-text.data-v-da8e5c67 {
  color: #FA5151;
}
.bottom-actions .action-button .action-text.data-v-da8e5c67 {
  font-size: 32rpx;
  font-weight: 500;
}
.upload-modal-overlay.data-v-da8e5c67 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}
.upload-modal-content.data-v-da8e5c67 {
  background: #ffffff;
  border-radius: 32rpx;
  padding: 60rpx 40rpx 50rpx;
  width: 640rpx;
  max-height: 80vh;
  position: relative;
  box-sizing: border-box;
}
.upload-modal-content .close-btn.data-v-da8e5c67 {
  position: absolute;
  bottom: -80rpx;
  width: 45rpx;
  height: 45rpx;
  left: 50%;
  transform: translateX(-50%);
}
.upload-modal-content .close-btn .close-icon.data-v-da8e5c67 {
  width: 45rpx;
  height: 45rpx;
}
.upload-modal-content .upload-title.data-v-da8e5c67 {
  display: block;
  font-size: 35rpx;
  font-weight: 500;
  color: #2A64F6;
  text-align: center;
  margin-bottom: 48rpx;
  line-height: 1.4;
}
.upload-modal-content .upload-tips.data-v-da8e5c67 {
  margin-bottom: 48rpx;
}
.upload-modal-content .upload-tips .tip-item.data-v-da8e5c67 {
  margin-bottom: 20rpx;
  background: #F4F6F8;
  border-radius: 15rpx;
  padding: 30rpx;
}
.upload-modal-content .upload-tips .tip-item.data-v-da8e5c67:last-child {
  margin-bottom: 0;
}
.upload-modal-content .upload-tips .tip-item .tip-title.data-v-da8e5c67 {
  display: block;
  font-size: 30rpx;
  font-weight: 500;
  color: #222222;
  margin-bottom: 6rpx;
}
.upload-modal-content .upload-tips .tip-item .tip-desc.data-v-da8e5c67 {
  display: block;
  font-size: 22rpx;
  color: #999999;
  line-height: 1.6;
}
.upload-modal-content .upload-btn.data-v-da8e5c67 {
  width: 100%;
  height: 96rpx;
  background: #2A64F6;
  border-radius: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.upload-modal-content .upload-btn .upload-btn-text.data-v-da8e5c67 {
  font-size: 32rpx;
  color: #ffffff;
  font-weight: 600;
}
.modal-overlay.data-v-da8e5c67 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}
.modal-content.data-v-da8e5c67 {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 48rpx 32rpx;
  width: 600rpx;
  max-width: 90vw;
}
.modal-content .modal-title.data-v-da8e5c67 {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
  text-align: center;
  margin-bottom: 24rpx;
}
.modal-content .modal-message.data-v-da8e5c67 {
  display: block;
  font-size: 28rpx;
  color: #666666;
  text-align: center;
  margin-bottom: 48rpx;
  line-height: 1.5;
}
.modal-content .modal-actions.data-v-da8e5c67 {
  display: flex;
  gap: 24rpx;
}
.modal-content .modal-actions .modal-button.data-v-da8e5c67 {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.modal-content .modal-actions .modal-button.cancel.data-v-da8e5c67 {
  background: #f5f5f5;
}
.modal-content .modal-actions .modal-button.cancel .modal-button-text.data-v-da8e5c67 {
  color: #666666;
}
.modal-content .modal-actions .modal-button.confirm.data-v-da8e5c67 {
  background: #FF4757;
}
.modal-content .modal-actions .modal-button.confirm .modal-button-text.data-v-da8e5c67 {
  color: #ffffff;
}
.modal-content .modal-actions .modal-button .modal-button-text.data-v-da8e5c67 {
  font-size: 32rpx;
  font-weight: 600;
}