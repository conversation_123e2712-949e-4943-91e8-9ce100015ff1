<template>
  <view class="container">
    <!-- 整页滚动容器，支持下拉刷新 -->
    <scroll-view class="page-scroll" scroll-y refresher-enabled :refresher-triggered="refreshing"
      @refresherrefresh="onRefresh" @refresherrestore="onRestore" @scrolltolower="onLoadMore" :lower-threshold="100">
      <!-- 对话列表内容 -->
      <view class="chat-list">
        <!-- 加载状态提示 -->
        <view v-if="loading && sessionList.length === 0" class="loading-container">
          <text class="loading-text">加载中...</text>
        </view>

        <!-- 空状态 -->
        <view v-else-if="!loading && sessionList.length === 0" class="empty-container">
          <text class="empty-text">暂无对话记录</text>
        </view>

        <!-- 对话列表内容 -->
        <view v-else>
          <!-- @click="handleItemClick($event)"  :class="{ 'selected': selectedId === item.id }"  -->
          <uni-swipe-action ref="swipeActionRef">
            <uni-swipe-action-item @click="handleSwipeClick($event, item)" :key="item.guid"
              v-for="(item, index) in sessionList" :threshold="0"
              :right-options="item.isTop === 1 ? closeSwipeOptions : swipeOptions">
              <view class="chat-item" @click="handleItemClick(item)">
                <view class="avatar">
                  <image :src="item.agent.agentAvatar" class="avatar-img" mode="aspectFill"></image>
                </view>
                <view class="content">
                  <view class="name">{{ item.sessionTitle }}</view>
                  <view class="message">{{ item.lastMessage.content }}</view>
                </view>
                <!-- <view class="badge">
                  <uv-badge type="error" max="99" :value="100"></uv-badge>
                </view> -->
              </view>
            </uni-swipe-action-item>
          </uni-swipe-action>

          <!-- 加载更多状态 -->
          <view class="load-more-container" v-if="sessionList.length > 0">
            <view v-if="loadingMore" class="load-more-item">
              <text class="load-more-text">加载中...</text>
            </view>
            <view v-else-if="noMoreData" class="load-more-item">
              <text class="load-more-text">没有更多数据了</text>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
    <!-- 底部创建按钮 -->
    <!-- <view class="create-btn-container">
      <view class="create-btn" @click="handleCreate">
        <image src="/static/msg/<EMAIL>" class="plus-icon" mode="aspectFit"></image>
        <text class="create-text">创建AI智能体</text>
      </view>
    </view> -->
    <!-- 编辑弹窗 -->
    <view class="modal-overlay" v-if="showEditModal" @click="closeEditModal">
      <view class="edit-modal" @click.stop>
        <view class="modal-title">对话名称</view>
        <view class="input-container">
          <input v-model="updateReq.sessionTitle" class="edit-input" placeholder="请输入对话名称" :focus="showEditModal" />
        </view>
        <view class="modal-buttons">
          <view class="cancel-btn" @click="closeEditModal">取消</view>
          <view class="confirm-btn" @click="confirmEdit">确认</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { reactive, ref, watch } from 'vue'
import {
  onShow
} from '@dcloudio/uni-app';
import { useUserStore } from '@/stores/user.js'
import { getMySessionListApi, updateSessionTitleApi, deleteSessionApi, setSessionTopApi } from '@/api';
const userStore = useUserStore()

// 对话列表数据
const sessionList = ref([])

// 分页相关
const pageParams = reactive({
  page: 1,
  pageSize: 10
})

// 加载状态
const loading = ref(false)
const refreshing = ref(false)
const loadingMore = ref(false)
const noMoreData = ref(false)

// 分页信息
const pageInfo = reactive({
  current_page: 1,
  last_page: 1,
  per_page: 10,
  total: 0
})

// 编辑弹窗相关
const showEditModal = ref(false)

// 左滑操作配置
const swipeOptions = ref([
  {
    text: '置顶',
    style: {
      backgroundColor: '#5A7BF7',
      color: '#fff'
    }
  },
  {
    text: '编辑',
    style: {
      backgroundColor: '#FF9F40',
      color: '#fff'
    }
  },
  {
    text: '删除',
    style: {
      backgroundColor: '#FF6B6B',
      color: '#fff'
    }
  }
])
const closeSwipeOptions = ref([
  {
    text: '取消置顶',
    style: {
      backgroundColor: '#FF6B6B',
      color: '#fff'
    }
  },
  {
    text: '编辑',
    style: {
      backgroundColor: '#FF9F40',
      color: '#fff'
    }
  },
  {
    text: '删除',
    style: {
      backgroundColor: '#FF6B6B',
      color: '#fff'
    }
  }
])
const swipeActionRef = ref(null)

// 下拉刷新
const onRefresh = () => {
  refreshing.value = true
  pageParams.page = 1
  noMoreData.value = false
  getMySessionList(true)
}

// 刷新完成
const onRestore = () => {
  refreshing.value = false
}

// 上拉加载更多
const onLoadMore = () => {
  if (loadingMore.value || noMoreData.value) return

  if (pageInfo.current_page < pageInfo.last_page) {
    pageParams.page++
    getMySessionList(false)
  } else {
    noMoreData.value = true
  }
}

// 左滑操作点击事件
const handleSwipeClick = (event, item) => {
  const { index } = event
  switch (index) {
    case 0: // 置顶
      handleTop(item)
      break
    case 1: // 编辑
      handleEdit(item)
      break
    case 2: // 删除
      handleDelete(item)
      break
  }
}

// 置顶操作
const handleTop = async (item) => {
  let isTop = 1;
  let text = '置顶成功'
  if (item.isTop === 1) {
    isTop = 0;
    text = '取消置顶成功'
  } else {
    isTop = 1
  }
  swipeActionRef.value.closeAll()
  await setSessionTopApi({
    merchantGuid: userStore.merchantGuid,
    sessionGuid: item.guid,
    isTop: isTop
  })
  await getMySessionList()
  uni.showToast({
    title: text,
    icon: 'success'
  })
}
const updateReq = reactive({
  merchantGuid: userStore.merchantGuid, //商户uuid
  sessionGuid: '', //智能体对话uuid
  sessionTitle: '' //对话自定义标题
})
// 编辑操作
const handleEdit = (item) => {
  console.log('编辑:', item.sessionTitle)
  updateReq.sessionTitle = item.sessionTitle
  updateReq.sessionGuid = item.guid;
  showEditModal.value = true
}

// 删除操作
const handleDelete = (item) => {
  uni.showModal({
    title: '确认删除',
    content: '确定要删除这个对话吗？',
    success: async (res) => {
      if (res.confirm) {
        let res = await deleteSessionApi({
          merchantGuid: userStore.merchantGuid,
          sessionGuid: item.guid
        })
        if (res.code === 0) {
          uni.showToast({
            title: '已删除',
            icon: 'success'
          })
          getMySessionList()
        } else {
          uni.showToast({
            title: res.msg,
            icon: 'none'
          })
        }

      }
    }
  })
}

// 创建新智能体
const handleCreate = () => {
  uni.navigateTo({
    url: '/pages/create-agent/index'
  })
}

// 关闭编辑弹窗
const closeEditModal = () => {
  showEditModal.value = false
  // editName.value = ''
  // editingItem.value = null
  updateReq.sessionTitle = '';
  updateReq.sessionGuid = '';
}

// 确认编辑
const confirmEdit = async () => {
  if (!updateReq.sessionTitle.trim()) {
    uni.showToast({
      title: '请输入对话名称',
      icon: 'none'
    })
    return
  }
  await updateSessionTitleApi(updateReq)
  // 更新对话名称
  uni.showToast({
    title: '修改成功',
    icon: 'success'
  })
  closeEditModal()
  getMySessionList()
}
// 获取对话列表
const getMySessionList = async (isRefresh = false) => {
  try {
    // 设置加载状态
    if (isRefresh) {
      refreshing.value = true
    } else if (pageParams.page === 1) {
      loading.value = true
    } else {
      loadingMore.value = true
    }

    const res = await getMySessionListApi({
      merchantGuid: userStore.merchantGuid,
      pageSize: pageParams.pageSize,
      page: pageParams.page
    })

    if (res.data) {
      // 更新分页信息
      pageInfo.current_page = res.data.current_page
      pageInfo.last_page = res.data.last_page
      pageInfo.per_page = res.data.per_page
      pageInfo.total = res.data.total

      // 更新列表数据
      if (isRefresh || pageParams.page === 1) {
        // 刷新或首次加载，替换数据
        sessionList.value = res.data.data || []
      } else {
        // 加载更多，追加数据
        sessionList.value.push(...(res.data.data || []))
      }

      // 检查是否还有更多数据
      if (pageInfo.current_page >= pageInfo.last_page) {
        noMoreData.value = true
      }
    }
  } catch (error) {
    console.error('获取对话列表失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    })
  } finally {
    // 重置加载状态
    loading.value = false
    refreshing.value = false
    loadingMore.value = false
  }
}
const handleItemClick = (item) => {
  uni.navigateTo({
    url: `/pages/msg/index?sessionGuid=${item.agentGuid}&sysId=${item.agent.sysId}`
  })
}
onShow(() => {
  if (userStore.userToken) {
    getMySessionList()
  }
})
watch(
  () => userStore.userToken,
  (newValue, oldValue) => {
    if (newValue && oldValue === '') {
      getMySessionList()
    }
  }
);
</script>

<style lang="scss" scoped>
.container {
  background-color: #ffffff;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.page-scroll {
  flex: 1;
  height: 100%;
}

.chat-list {
  min-height: calc(100vh - 140rpx);
  /* 减去底部按钮高度 */
  //padding-bottom: 100px;
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400rpx;
  padding: 60rpx 0;

  .loading-text {
    font-size: 28rpx;
    color: #999999;
  }
}

/* 空状态样式 */
.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400rpx;
  padding: 120rpx 0;

  .empty-text {
    font-size: 28rpx;
    color: #999999;
  }
}

/* 加载更多样式 */
.load-more-container {
  padding: 0;

  .load-more-item {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 32rpx 0;
    background-color: #ffffff;

    .load-more-text {
      font-size: 26rpx;
      color: #999999;
    }
  }
}

.chat-item {
  display: flex;
  align-items: center;
  padding: 16px;
  background-color: #ffffff;
  border-bottom: 1px solid #f5f5f5;

  &.selected {
    background-color: #f0f0f0;
  }

  .avatar {
    width: 50px;
    height: 50px;
    margin-right: 12px;
    flex-shrink: 0;

    .avatar-img {
      width: 100%;
      height: 100%;
      border-radius: 25px;

    }
  }

  .content {
    flex: 1;
    width: 530rpx;

    .name {
      font-size: 16px;
      font-weight: 500;
      color: #333;
      margin-bottom: 4px;
    }

    .message {
      font-size: 14px;
      color: #666;
      line-height: 1.4;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .badge {
    width: 60rpx;
    height: 100%;
  }
}

.create-btn-container {
  position: fixed;
  width: 100%;
  bottom: 30px;
  left: 0;
  // padding: 40rpx 32rpx;
  // padding-bottom: calc(40rpx + env(safe-area-inset-bottom));
  display: flex;
  justify-content: center;
  //background-color: #ffffff;
  margin-top: 20rpx;


  .create-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 300rpx;
    height: 90rpx;
    background: #3478f6;
    border-radius: 48rpx;
    border: none;

    .plus-icon {
      width: 32rpx;
      height: 32rpx;
      margin-right: 8px;
    }

    .create-text {
      color: #fff;
      font-size: 32rpx;
      font-weight: 500;
    }
  }
}


/* 编辑弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.edit-modal {
  background-color: #ffffff;
  border-radius: 12px;
  width: 320px;
  padding: 24px;
  box-sizing: border-box;

  .modal-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    text-align: center;
    margin-bottom: 24px;
  }

  .input-container {
    margin-bottom: 32px;

    .edit-input {
      width: 100%;
      height: 44px;
      background-color: #F8F9FA;
      border-radius: 8px;
      padding: 0 16px;
      font-size: 16px;
      color: #333;
      border: none;
      box-sizing: border-box;

      &::placeholder {
        color: #999;
      }
    }
  }

  .modal-buttons {
    display: flex;
    gap: 12px;

    .cancel-btn,
    .confirm-btn {
      flex: 1;
      height: 44px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      font-weight: 500;
    }

    .cancel-btn {
      background-color: #F8F9FA;
      color: #666;
    }

    .confirm-btn {
      background-color: #5A7BF7;
      color: #fff;
    }
  }
}
</style>