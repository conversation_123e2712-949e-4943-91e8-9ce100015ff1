{"version": 3, "file": "z-paging-main.js", "sources": ["uni_modules/z-paging/components/z-paging/js/z-paging-main.js?vue&type=script&src=true&lang.js"], "sourcesContent": ["// [z-paging]核心js\r\n\r\nimport zStatic from './z-paging-static'\r\nimport c from './z-paging-constant'\r\nimport u from './z-paging-utils'\r\n\r\nimport zPagingRefresh from '../components/z-paging-refresh'\r\nimport zPagingLoadMore from '../components/z-paging-load-more'\r\nimport zPagingEmptyView from '../../z-paging-empty-view/z-paging-empty-view'\r\n\r\n// modules\r\nimport commonLayoutModule from './modules/common-layout'\r\nimport dataHandleModule from './modules/data-handle'\r\nimport i18nModule from './modules/i18n'\r\nimport nvueModule from './modules/nvue'\r\nimport emptyModule from './modules/empty'\r\nimport refresherModule from './modules/refresher'\r\nimport loadMoreModule from './modules/load-more'\r\nimport loadingModule from './modules/loading'\r\nimport chatRecordModerModule from './modules/chat-record-mode'\r\nimport scrollerModule from './modules/scroller'\r\nimport backToTopModule from './modules/back-to-top'\r\nimport virtualListModule from './modules/virtual-list'\r\n\r\nimport Enum from './z-paging-enum'\r\n\r\nconst systemInfo = uni.getSystemInfoSync();\r\n\r\nexport default {\r\n\tname: \"z-paging\",\r\n\tcomponents: {\r\n\t\tzPagingRefresh,\r\n\t\tzPagingLoadMore,\r\n\t\tzPagingEmptyView\r\n\t},\r\n\tmixins: [\r\n\t\tcommonLayoutModule,\r\n\t\tdataHandleModule,\r\n\t\ti18nModule,\r\n\t\tnvueModule,\r\n\t\temptyModule,\r\n\t\trefresherModule,\r\n\t\tloadMoreModule,\r\n\t\tloadingModule,\r\n\t\tchatRecordModerModule,\r\n\t\tscrollerModule,\r\n\t\tbackToTopModule,\r\n\t\tvirtualListModule\r\n\t],\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\t// --------------静态资源---------------\r\n\t\t\tbase64Arrow: zStatic.base64Arrow,\r\n\t\t\tbase64Flower: zStatic.base64Flower,\r\n\t\t\tbase64BackToTop: zStatic.base64BackToTop,\r\n\r\n\t\t\t// -------------全局数据相关--------------\r\n\t\t\t// 当前加载类型\r\n\t\t\tloadingType: Enum.LoadingType.Refresher,\r\n\t\t\trequestTimeStamp: 0,\r\n\t\t\twxsPropType: '',\r\n\t\t\trenderPropScrollTop: -1,\r\n\t\t\tcheckScrolledToBottomTimeOut: null,\r\n\t\t\tcacheTopHeight: -1,\r\n\t\t\tstatusBarHeight: systemInfo.statusBarHeight,\r\n\r\n\t\t\t// --------------状态&判断---------------\r\n\t\t\tinsideOfPaging: -1,\r\n\t\t\tisLoadFailed: false,\r\n\t\t\tisIos: systemInfo.platform === 'ios',\r\n\t\t\tdisabledBounce: false,\r\n\t\t\tfromCompleteEmit: false,\r\n\t\t\tdisabledCompleteEmit: false,\r\n\t\t\tpageLaunched: false,\r\n\t\t\tactive: false,\r\n\t\t\t\r\n\t\t\t// ---------------wxs相关---------------\r\n\t\t\twxsIsScrollTopInTopRange: true,\r\n\t\t\twxsScrollTop: 0,\r\n\t\t\twxsPageScrollTop: 0,\r\n\t\t\twxsOnPullingDown: false,\r\n\t\t};\r\n\t},\r\n\tprops: {\r\n\t\t// 调用complete后延迟处理的时间，单位为毫秒，默认0毫秒，优先级高于minDelay\r\n\t\tdelay: {\r\n\t\t\ttype: [Number, String],\r\n\t\t\tdefault: u.gc('delay', 0),\r\n\t\t},\r\n\t\t// 触发@query后最小延迟处理的时间，单位为毫秒，默认0毫秒，优先级低于delay（假设设置为300毫秒，若分页请求时间小于300毫秒，则在调用complete后延迟[300毫秒-请求时长]；若请求时长大于300毫秒，则不延迟），当show-refresher-when-reload为true或reload(true)时，其最小值为400\r\n\t\tminDelay: {\r\n\t\t\ttype: [Number, String],\r\n\t\t\tdefault: u.gc('minDelay', 0),\r\n\t\t},\r\n\t\t// 设置z-paging的style，部分平台(如微信小程序)无法直接修改组件的style，可使用此属性代替\r\n\t\tpagingStyle: {\r\n\t\t\ttype: Object,\r\n\t\t\tdefault: u.gc('pagingStyle', {}),\r\n\t\t},\r\n\t\t// z-paging的高度，优先级低于pagingStyle中设置的height；传字符串，如100px、100rpx、100%\r\n\t\theight: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: u.gc('height', '')\r\n\t\t},\r\n\t\t// z-paging的宽度，优先级低于pagingStyle中设置的width；传字符串，如100px、100rpx、100%\r\n\t\twidth: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: u.gc('width', '')\r\n\t\t},\r\n\t\t// z-paging的最大宽度，优先级低于pagingStyle中设置的max-width；传字符串，如100px、100rpx、100%。默认为空，也就是铺满窗口宽度，若设置了特定值则会自动添加margin: 0 auto\r\n\t\tmaxWidth: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: u.gc('maxWidth', '')\r\n\t\t},\r\n\t\t// z-paging的背景色，优先级低于pagingStyle中设置的background。传字符串，如\"#ffffff\"\r\n\t\tbgColor: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: u.gc('bgColor', '')\r\n\t\t},\r\n\t\t// 设置z-paging的容器(插槽的父view)的style\r\n\t\tpagingContentStyle: {\r\n\t\t\ttype: Object,\r\n\t\t\tdefault: u.gc('pagingContentStyle', {}),\r\n\t\t},\r\n\t\t// z-paging是否自动高度，若自动高度则会自动铺满屏幕\r\n\t\tautoHeight: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('autoHeight', false)\r\n\t\t},\r\n\t\t// z-paging是否自动高度时，附加的高度，注意添加单位px或rpx，若需要减少高度，则传负数\r\n\t\tautoHeightAddition: {\r\n\t\t\ttype: [Number, String],\r\n\t\t\tdefault: u.gc('autoHeightAddition', '0px')\r\n\t\t},\r\n\t\t// loading(下拉刷新、上拉加载更多)的主题样式，支持black，white，默认black\r\n\t\tdefaultThemeStyle: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: u.gc('defaultThemeStyle', 'black')\r\n\t\t},\r\n\t\t// z-paging是否使用fixed布局，若使用fixed布局，则z-paging的父view无需固定高度，z-paging高度默认为100%，默认为是(当使用内置scroll-view滚动时有效)\r\n\t\tfixed: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('fixed', true)\r\n\t\t},\r\n\t\t// 是否开启底部安全区域适配\r\n\t\tsafeAreaInsetBottom: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('safeAreaInsetBottom', false)\r\n\t\t},\r\n\t\t// 开启底部安全区域适配后，是否使用placeholder形式实现，默认为否。为否时滚动区域会自动避开底部安全区域，也就是所有滚动内容都不会挡住底部安全区域，若设置为是，则滚动时滚动内容会挡住底部安全区域，但是当滚动到底部时才会避开底部安全区域\r\n\t\tuseSafeAreaPlaceholder: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('useSafeAreaPlaceholder', false)\r\n\t\t},\r\n\t\t// z-paging bottom的背景色，默认透明，传字符串，如\"#ffffff\"\r\n\t\tbottomBgColor: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: u.gc('bottomBgColor', '')\r\n\t\t},\r\n\t\t// slot=\"top\"的view的z-index，默认为99，仅使用页面滚动时有效\r\n\t\ttopZIndex: {\r\n\t\t\ttype: Number,\r\n\t\t\tdefault: u.gc('topZIndex', 99)\r\n\t\t},\r\n\t\t// z-paging内容容器父view的z-index，默认为1\r\n\t\tsuperContentZIndex: {\r\n\t\t\ttype: Number,\r\n\t\t\tdefault: u.gc('superContentZIndex', 1)\r\n\t\t},\r\n\t\t// z-paging内容容器部分的z-index，默认为1\r\n\t\tcontentZIndex: {\r\n\t\t\ttype: Number,\r\n\t\t\tdefault: u.gc('contentZIndex', 1)\r\n\t\t},\r\n\t\t// z-paging二楼的z-index，默认为100\r\n\t\tf2ZIndex: {\r\n\t\t\ttype: Number,\r\n\t\t\tdefault: u.gc('f2ZIndex', 100)\r\n\t\t},\r\n\t\t// 使用页面滚动时，是否在不满屏时自动填充满屏幕，默认为是\r\n\t\tautoFullHeight: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('autoFullHeight', true)\r\n\t\t},\r\n\t\t// 是否监听列表触摸方向改变，默认为否\r\n\t\twatchTouchDirectionChange: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('watchTouchDirectionChange', false)\r\n\t\t},\r\n\t\t// z-paging中布局的单位，默认为rpx\r\n\t\tunit: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: u.gc('unit', 'rpx')\r\n\t\t}\r\n\t},\r\n\tcreated() {\r\n\t\t// 组件创建时，检测是否开始加载状态\r\n\t\tif (this.createdReload && !this.refresherOnly && this.auto) {\r\n\t\t\tthis._startLoading();\r\n\t\t\tthis.$nextTick(this._preReload);\r\n\t\t}\r\n\t},\r\n\tmounted() {\r\n\t\tthis.active = true;\r\n\t\tthis.wxsPropType = u.getTime().toString();\r\n\t\tthis.renderJsIgnore;\r\n\t\tif (!this.createdReload && !this.refresherOnly && this.auto) {\r\n\t\t\t// 开始预加载\r\n\t\t\tu.delay(() => this.$nextTick(this._preReload), 0);\r\n\t\t}\r\n\t\t// 如果开启了列表缓存，在初始化的时候通过缓存数据填充列表数据\r\n\t\tthis.finalUseCache && this._setListByLocalCache();\r\n\t\tlet delay = 0;\r\n\t\t// #ifdef H5 || MP\r\n\t\tdelay = c.delayTime;\r\n\t\t// #endif\r\n\t\tthis.$nextTick(() => {\r\n\t\t\t// 初始化systemInfo\r\n\t\t\tthis.systemInfo = uni.getSystemInfoSync();\r\n\t\t\t// 初始化z-paging高度\r\n\t\t\t!this.usePageScroll && this.autoHeight && this._setAutoHeight();\r\n\t\t\tthis.loaded = true;\r\n\t\t\tu.delay(() => {\r\n\t\t\t\t// 更新fixed模式下z-paging的布局，主要是更新windowTop、windowBottom\r\n\t\t\t\tthis.updateFixedLayout();\r\n\t\t\t\t// 更新缓存中z-paging整个内容容器高度\r\n\t\t\t\tthis._updateCachedSuperContentHeight();\r\n\t\t\t});\r\n\t\t})\r\n\t\t// 初始化页面滚动模式下slot=\"top\"、slot=\"bottom\"高度\r\n\t\tthis.updatePageScrollTopHeight();\r\n\t\tthis.updatePageScrollBottomHeight();\r\n\t\t// 初始化slot=\"left\"、slot=\"right\"宽度\r\n\t\tthis.updateLeftAndRightWidth();\r\n\t\tif (this.finalRefresherEnabled && this.useCustomRefresher) {\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\tthis.isTouchmoving = true;\r\n\t\t\t})\r\n\t\t}\r\n\t\t// 监听uni.$emit中全局emit的complete error等事件\r\n\t\tthis._onEmit();\r\n\t\t// #ifdef APP-NVUE\r\n\t\tif (!this.isIos && !this.useChatRecordMode) {\r\n\t\t\tthis.nLoadingMoreFixedHeight = true;\r\n\t\t}\r\n\t\t// 在nvue中更新nvue下拉刷新view容器的宽度，而不是写死默认的750rpx，需要考虑列表宽度不是铺满屏幕的情况\r\n\t\tthis._nUpdateRefresherWidth();\r\n\t\t// #endif\r\n\t\t// #ifndef APP-NVUE\r\n\t\t// 虚拟列表模式时，初始化数据\r\n\t\tthis.finalUseVirtualList && this._virtualListInit();\r\n\t\t// #endif\r\n\t\t// #ifndef APP-PLUS\r\n\t\tthis.$nextTick(() => {\r\n\t\t\t// 非app平台中，在通过获取css设置的底部安全区域占位view高度设置bottom距离后，更新页面滚动底部高度\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\tthis._getCssSafeAreaInsetBottom(() => this.safeAreaInsetBottom && this.updatePageScrollBottomHeight());\r\n\t\t\t}, delay)\r\n\t\t})\r\n\t\t// #endif\r\n\t},\r\n\tdestroyed() {\r\n\t\tthis._handleUnmounted();\r\n\t},\r\n\t// #ifdef VUE3\r\n\tunmounted() {\r\n\t\tthis._handleUnmounted();\r\n\t},\r\n\t// #endif\r\n\twatch: {\r\n\t\tdefaultThemeStyle: {\r\n\t\t\thandler(newVal) {\r\n\t\t\t\tif (newVal.length) {\r\n\t\t\t\t\tthis.finalRefresherDefaultStyle = newVal;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\timmediate: true\r\n\t\t},\r\n\t\tautoHeight(newVal) {\r\n\t\t\tthis.loaded && !this.usePageScroll && this._setAutoHeight(newVal);\r\n\t\t},\r\n\t\tautoHeightAddition(newVal) {\r\n\t\t\tthis.loaded && !this.usePageScroll && this.autoHeight && this._setAutoHeight(newVal);\r\n\t\t},\r\n\t},\r\n\tcomputed: {\r\n\t\t// 当前z-paging的内置样式\r\n\t\tfinalPagingStyle() {\r\n\t\t\tconst pagingStyle = { ...this.pagingStyle };\r\n\t\t\tif (!this.systemInfo) return pagingStyle;\r\n\t\t\tconst { windowTop, windowBottom } = this;\r\n\t\t\tif (!this.usePageScroll && this.fixed) {\r\n\t\t\t\tif (windowTop && !pagingStyle.top) {\r\n\t\t\t\t\tpagingStyle.top = windowTop + 'px';\r\n\t\t\t\t}\r\n\t\t\t\tif (windowBottom && !pagingStyle.bottom) {\r\n\t\t\t\t\tpagingStyle.bottom = windowBottom + 'px';\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tif (this.bgColor.length && !pagingStyle['background']) {\r\n\t\t\t\tpagingStyle['background'] = this.bgColor;\r\n\t\t\t}\r\n\t\t\tif (this.height.length && !pagingStyle['height']) {\r\n\t\t\t\tpagingStyle['height'] = this.height;\r\n\t\t\t}\r\n\t\t\tif (this.width.length && !pagingStyle['width']) {\r\n\t\t\t\tpagingStyle['width'] = this.width;\r\n\t\t\t}\r\n\t\t\tif (this.maxWidth.length && !pagingStyle['max-width']) {\r\n\t\t\t\tpagingStyle['max-width'] = this.maxWidth;\r\n\t\t\t\tpagingStyle['margin'] = '0 auto';\r\n\t\t\t}\r\n\t\t\treturn pagingStyle;\r\n\t\t},\r\n\t\t// 当前z-paging内容的样式\r\n\t\tfinalPagingContentStyle() {\r\n\t\t\tif (this.contentZIndex != 1) {\r\n\t\t\t\tthis.pagingContentStyle['z-index'] = this.contentZIndex;\r\n\t\t\t\tthis.pagingContentStyle['position'] = 'relative';\r\n\t\t\t}\r\n\t\t\treturn this.pagingContentStyle;\r\n\t\t},\r\n\t\t\r\n\t\trenderJsIgnore() {\r\n\t\t\tif ((this.usePageScroll && this.useChatRecordMode) || (!this.refresherEnabled && this.scrollable) || !this.useCustomRefresher) {\r\n\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\tthis.renderPropScrollTop = 10;\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t\treturn 0;\r\n\t\t},\r\n\t\twindowHeight() {\r\n\t\t\tif (!this.systemInfo) return 0;\r\n\t\t\treturn this.systemInfo.windowHeight || 0;\r\n\t\t},\r\n\t\twindowBottom() {\r\n\t\t\tif (!this.systemInfo) return 0;\r\n\t\t\tlet windowBottom = this.systemInfo.windowBottom || 0;\r\n\t\t\t// 如果开启底部安全区域适配并且不使用placeholder的形式体现并且不是聊天记录模式（因为聊天记录模式在keyboardHeight计算初已添加了底部安全区域），在windowBottom添加底部安全区域高度\r\n\t\t\tif (this.safeAreaInsetBottom && !this.useSafeAreaPlaceholder && !this.useChatRecordMode) {\r\n\t\t\t\twindowBottom += this.safeAreaBottom;\r\n\t\t\t}\r\n\t\t\treturn windowBottom;\r\n\t\t},\r\n\t\tisIosAndH5() {\r\n\t\t\t// #ifndef H5\r\n\t\t\treturn false;\r\n\t\t\t// #endif\r\n\t\t\treturn this.isIos;\r\n\t\t}\r\n\t},\r\n\tmethods: {\r\n\t\t// 当前版本号\r\n\t\tgetVersion() {\r\n\t\t\treturn `z-paging v${c.version}`;\r\n\t\t},\r\n\t\t// 设置nvue List的specialEffects\r\n\t\tsetSpecialEffects(args) {\r\n\t\t\tthis.setListSpecialEffects(args);\r\n\t\t},\r\n\t\t// 与setSpecialEffects等效，兼容旧版本\r\n\t\tsetListSpecialEffects(args) {\r\n\t\t\tthis.nFixFreezing = args && Object.keys(args).length;\r\n\t\t\tif (this.isIos) {\r\n\t\t\t\tthis.privateRefresherEnabled = 0;\r\n\t\t\t}\r\n\t\t\t!this.usePageScroll && this.$refs['zp-n-list'].setSpecialEffects(args);\r\n\t\t},\r\n\t\t// #ifdef APP-VUE\r\n\t\t// 当app长时间进入后台后进入前台，因系统内存管理导致app重新加载时，进行一些适配处理\r\n\t\t_handlePageLaunch() {\r\n\t\t\t// 首次触发不进行处理，只有进入后台后打开app重新加载时才处理\r\n\t\t\tif (this.pageLaunched) {\r\n\t\t\t\t// 解决在vue3+ios中，app ReLaunch时顶部下拉刷新展示位置向下偏移的问题\r\n\t\t\t\t// #ifdef VUE3\r\n\t\t\t\tthis.refresherThresholdUpdateTag = 1;\r\n\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\tthis.refresherThresholdUpdateTag = 0;\r\n\t\t\t\t})\r\n\t\t\t\t// #endif\r\n\t\t\t\t// 解决使用虚拟列表时，app ReLaunch时白屏问题\r\n\t\t\t\tthis._checkVirtualListScroll();\r\n\t\t\t}\r\n\t\t\tthis.pageLaunched = true;\r\n\t\t},\r\n\t\t// #endif\r\n\t\t// 使手机发生较短时间的振动（15ms）\r\n\t\t_doVibrateShort() {\r\n\t\t\t// #ifndef H5\r\n\t\t\t\r\n\t\t\t// #ifdef APP-PLUS\r\n\t\t\tif (this.isIos) {\r\n\t\t\t\tconst UISelectionFeedbackGenerator = plus.ios.importClass('UISelectionFeedbackGenerator');\r\n\t\t\t\tconst feedbackGenerator = new UISelectionFeedbackGenerator();\r\n\t\t\t\tfeedbackGenerator.init();\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tfeedbackGenerator.selectionChanged();\r\n\t\t\t\t}, 0)\r\n\t\t\t} else {\r\n\t\t\t\tplus.device.vibrate(15);\r\n\t\t\t}\r\n\t\t\t// #endif\r\n\t\t\t// #ifndef APP-PLUS\r\n\t\t\tuni.vibrateShort();\r\n\t\t\t// #endif\r\n\t\t\t\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\t// 设置z-paging高度\r\n\t\tasync _setAutoHeight(shouldFullHeight = true, scrollViewNode = null) {\r\n\t\t\tlet heightKey = 'min-height';\r\n\t\t\t// #ifndef APP-NVUE\r\n\t\t\theightKey = 'min-height';\r\n\t\t\t// #endif\r\n\t\t\ttry {\r\n\t\t\t\tif (shouldFullHeight) {\r\n\t\t\t\t\t// 如果需要铺满全屏，则计算当前全屏可是区域的高度\r\n\t\t\t\t\tlet finalScrollViewNode = scrollViewNode || await this._getNodeClientRect('.zp-scroll-view');\r\n\t\t\t\t\tlet finalScrollBottomNode = await this._getNodeClientRect('.zp-page-bottom');\r\n\t\t\t\t\tif (finalScrollViewNode) {\r\n\t\t\t\t\t\tconst scrollViewTop = finalScrollViewNode[0].top;\r\n\t\t\t\t\t\tlet scrollViewHeight = this.windowHeight - scrollViewTop;\r\n\t\t\t\t\t\tscrollViewHeight -= finalScrollBottomNode ? finalScrollBottomNode[0].height : 0;\r\n\t\t\t\t\t\tconst additionHeight = u.convertToPx(this.autoHeightAddition);\r\n\t\t\t\t\t\tconst finalHeight = scrollViewHeight + additionHeight - (this.insideMore ? 1 : 0) + 'px !important';\r\n\t\t\t\t\t\tthis.$set(this.scrollViewStyle, heightKey, finalHeight);\r\n\t\t\t\t\t\tthis.$set(this.scrollViewInStyle, heightKey, finalHeight);\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$delete(this.scrollViewStyle, heightKey);\r\n\t\t\t\t\tthis.$delete(this.scrollViewInStyle, heightKey);\r\n\t\t\t\t}\r\n\t\t\t} catch (e) {}\r\n\t\t},\r\n\t\t// 组件销毁后续处理\r\n\t\t_handleUnmounted() {\r\n\t\t\tthis.active = false;\r\n\t\t\tthis._offEmit();\r\n\t\t\t// 取消监听键盘高度变化事件（H5、百度小程序、抖音小程序、飞书小程序、QQ小程序、快手小程序不支持）\r\n\t\t\t// #ifndef H5 || MP-BAIDU || MP-TOUTIAO || MP-QQ || MP-KUAISHOU\r\n\t\t\tthis.useChatRecordMode && uni.offKeyboardHeightChange(this._handleKeyboardHeightChange);\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\t// 触发更新是否超出页面状态\r\n\t\t_updateInsideOfPaging() {\r\n\t\t\tthis.insideMore && this.insideOfPaging === true && setTimeout(this.doLoadMore, 200)\r\n\t\t},\r\n\t\t// 清除timeout\r\n\t\t_cleanTimeout(timeout) {\r\n\t\t\tif (timeout) {\r\n\t\t\t\tclearTimeout(timeout);\r\n\t\t\t\ttimeout = null;\r\n\t\t\t}\r\n\t\t\treturn timeout;\r\n\t\t},\r\n\t\t// 添加全局emit监听\r\n\t\t_onEmit() {\r\n\t\t\tuni.$on(c.errorUpdateKey, (errorMsg) => {\r\n\t\t\t\tif (this.loading) {\r\n\t\t\t\t\tif (!!errorMsg) {\r\n\t\t\t\t\t\tthis.customerEmptyViewErrorText = errorMsg;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.complete(false).catch(() => {});\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\tuni.$on(c.completeUpdateKey, (data) => {\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tif (this.loading) {\r\n\t\t\t\t\t\tif (!this.disabledCompleteEmit) {\r\n\t\t\t\t\t\t\tconst type = data.type || 'normal';\r\n\t\t\t\t\t\t\tconst list = data.list || data;\r\n\t\t\t\t\t\t\tconst rule = data.rule;\r\n\t\t\t\t\t\t\tthis.fromCompleteEmit = true;\r\n\t\t\t\t\t\t\tswitch (type){\r\n\t\t\t\t\t\t\t\tcase 'normal':\r\n\t\t\t\t\t\t\t\t\tthis.complete(list);\r\n\t\t\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\t\t\tcase 'total':\r\n\t\t\t\t\t\t\t\t\tthis.completeByTotal(list, rule);\r\n\t\t\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\t\t\tcase 'nomore':\r\n\t\t\t\t\t\t\t\t\tthis.completeByNoMore(list, rule);\r\n\t\t\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\t\t\tcase 'key':\r\n\t\t\t\t\t\t\t\t\tthis.completeByKey(list, rule);\r\n\t\t\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\t\t\tdefault:\r\n\t\t\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthis.disabledCompleteEmit = false;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}, 1);\r\n\t\t\t})\r\n\t\t},\r\n\t\t// 销毁全局emit和listener监听\r\n\t\t_offEmit(){\r\n\t\t\tuni.$off(c.errorUpdateKey);\r\n\t\t\tuni.$off(c.completeUpdateKey);\r\n\t\t},\r\n\t},\r\n};\r\n"], "names": ["uni", "commonLayoutModule", "dataHandleModule", "i18nModule", "nvueModule", "emptyModule", "refresherModule", "loadMoreModule", "loadingModule", "chatRecordModerModule", "scrollerModule", "backToTopModule", "virtualListModule", "zStatic", "Enum", "u", "c"], "mappings": ";;;;;;;;;;;;;;;;;;AAMA,MAAM,iBAAiB,MAAW;AAClC,MAAM,kBAAkB,MAAW;AACnC,MAAM,mBAAmB,MAAW;AAkBpC,MAAM,aAAaA,cAAAA,MAAI;AAEvB,MAAe,YAAA;AAAA,EACd,MAAM;AAAA,EACN,YAAY;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,EACA;AAAA,EACD,QAAQ;AAAA,IACPC,+DAAkB;AAAA,IAClBC,6DAAgB;AAAA,IAChBC,uDAAU;AAAA,IACVC,uDAAU;AAAA,IACVC,wDAAW;AAAA,IACXC,4DAAe;AAAA,IACfC,2DAAc;AAAA,IACdC,0DAAa;AAAA,IACbC,iEAAqB;AAAA,IACrBC,2DAAc;AAAA,IACdC,4DAAe;AAAA,IACfC,8DAAiB;AAAA,EACjB;AAAA,EACD,OAAO;AACN,WAAO;AAAA;AAAA,MAEN,aAAaC,wDAAO,QAAC;AAAA,MACrB,cAAcA,wDAAO,QAAC;AAAA,MACtB,iBAAiBA,wDAAO,QAAC;AAAA;AAAA;AAAA,MAIzB,aAAaC,sDAAAA,KAAK,YAAY;AAAA,MAC9B,kBAAkB;AAAA,MAClB,aAAa;AAAA,MACb,qBAAqB;AAAA,MACrB,8BAA8B;AAAA,MAC9B,gBAAgB;AAAA,MAChB,iBAAiB,WAAW;AAAA;AAAA,MAG5B,gBAAgB;AAAA,MAChB,cAAc;AAAA,MACd,OAAO,WAAW,aAAa;AAAA,MAC/B,gBAAgB;AAAA,MAChB,kBAAkB;AAAA,MAClB,sBAAsB;AAAA,MACtB,cAAc;AAAA,MACd,QAAQ;AAAA;AAAA,MAGR,0BAA0B;AAAA,MAC1B,cAAc;AAAA,MACd,kBAAkB;AAAA,MAClB,kBAAkB;AAAA,IACrB;AAAA,EACE;AAAA,EACD,OAAO;AAAA;AAAA,IAEN,OAAO;AAAA,MACN,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAASC,uDAAC,EAAC,GAAG,SAAS,CAAC;AAAA,IACxB;AAAA;AAAA,IAED,UAAU;AAAA,MACT,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAASA,uDAAC,EAAC,GAAG,YAAY,CAAC;AAAA,IAC3B;AAAA;AAAA,IAED,aAAa;AAAA,MACZ,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,eAAe,CAAA,CAAE;AAAA,IAC/B;AAAA;AAAA,IAED,QAAQ;AAAA,MACP,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,UAAU,EAAE;AAAA,IAC1B;AAAA;AAAA,IAED,OAAO;AAAA,MACN,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,SAAS,EAAE;AAAA,IACzB;AAAA;AAAA,IAED,UAAU;AAAA,MACT,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,YAAY,EAAE;AAAA,IAC5B;AAAA;AAAA,IAED,SAAS;AAAA,MACR,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,WAAW,EAAE;AAAA,IAC3B;AAAA;AAAA,IAED,oBAAoB;AAAA,MACnB,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,sBAAsB,CAAA,CAAE;AAAA,IACtC;AAAA;AAAA,IAED,YAAY;AAAA,MACX,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,cAAc,KAAK;AAAA,IACjC;AAAA;AAAA,IAED,oBAAoB;AAAA,MACnB,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAASA,uDAAC,EAAC,GAAG,sBAAsB,KAAK;AAAA,IACzC;AAAA;AAAA,IAED,mBAAmB;AAAA,MAClB,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,qBAAqB,OAAO;AAAA,IAC1C;AAAA;AAAA,IAED,OAAO;AAAA,MACN,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,SAAS,IAAI;AAAA,IAC3B;AAAA;AAAA,IAED,qBAAqB;AAAA,MACpB,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,uBAAuB,KAAK;AAAA,IAC1C;AAAA;AAAA,IAED,wBAAwB;AAAA,MACvB,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,0BAA0B,KAAK;AAAA,IAC7C;AAAA;AAAA,IAED,eAAe;AAAA,MACd,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,iBAAiB,EAAE;AAAA,IACjC;AAAA;AAAA,IAED,WAAW;AAAA,MACV,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,aAAa,EAAE;AAAA,IAC7B;AAAA;AAAA,IAED,oBAAoB;AAAA,MACnB,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,sBAAsB,CAAC;AAAA,IACrC;AAAA;AAAA,IAED,eAAe;AAAA,MACd,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,iBAAiB,CAAC;AAAA,IAChC;AAAA;AAAA,IAED,UAAU;AAAA,MACT,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,YAAY,GAAG;AAAA,IAC7B;AAAA;AAAA,IAED,gBAAgB;AAAA,MACf,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,kBAAkB,IAAI;AAAA,IACpC;AAAA;AAAA,IAED,2BAA2B;AAAA,MAC1B,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,6BAA6B,KAAK;AAAA,IAChD;AAAA;AAAA,IAED,MAAM;AAAA,MACL,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,QAAQ,KAAK;AAAA,IAC3B;AAAA,EACD;AAAA,EACD,UAAU;AAET,QAAI,KAAK,iBAAiB,CAAC,KAAK,iBAAiB,KAAK,MAAM;AAC3D,WAAK,cAAa;AAClB,WAAK,UAAU,KAAK,UAAU;AAAA,IAC9B;AAAA,EACD;AAAA,EACD,UAAU;AACT,SAAK,SAAS;AACd,SAAK,cAAcA,uDAAAA,EAAE,QAAS,EAAC,SAAQ;AACvC,SAAK;AACL,QAAI,CAAC,KAAK,iBAAiB,CAAC,KAAK,iBAAiB,KAAK,MAAM;AAE5DA,+DAAE,MAAM,MAAM,KAAK,UAAU,KAAK,UAAU,GAAG,CAAC;AAAA,IAChD;AAED,SAAK,iBAAiB,KAAK;AAC3B,QAAI,QAAQ;AAEZ,YAAQC,0DAAC,EAAC;AAEV,SAAK,UAAU,MAAM;AAEpB,WAAK,aAAahB,oBAAI;AAEtB,OAAC,KAAK,iBAAiB,KAAK,cAAc,KAAK;AAC/C,WAAK,SAAS;AACde,6DAAC,EAAC,MAAM,MAAM;AAEb,aAAK,kBAAiB;AAEtB,aAAK,gCAA+B;AAAA,MACxC,CAAI;AAAA,IACJ,CAAG;AAED,SAAK,0BAAyB;AAC9B,SAAK,6BAA4B;AAEjC,SAAK,wBAAuB;AAC5B,QAAI,KAAK,yBAAyB,KAAK,oBAAoB;AAC1D,WAAK,UAAU,MAAM;AACpB,aAAK,gBAAgB;AAAA,MACzB,CAAI;AAAA,IACD;AAED,SAAK,QAAO;AAUZ,SAAK,uBAAuB,KAAK;AAGjC,SAAK,UAAU,MAAM;AAEpB,iBAAW,MAAM;AAChB,aAAK,2BAA2B,MAAM,KAAK,uBAAuB,KAAK,6BAA4B,CAAE;AAAA,MACrG,GAAE,KAAK;AAAA,IACX,CAAG;AAAA,EAED;AAAA,EACD,YAAY;AACX,SAAK,iBAAgB;AAAA,EACrB;AAAA,EAED,YAAY;AACX,SAAK,iBAAgB;AAAA,EACrB;AAAA,EAED,OAAO;AAAA,IACN,mBAAmB;AAAA,MAClB,QAAQ,QAAQ;AACf,YAAI,OAAO,QAAQ;AAClB,eAAK,6BAA6B;AAAA,QAClC;AAAA,MACD;AAAA,MACD,WAAW;AAAA,IACX;AAAA,IACD,WAAW,QAAQ;AAClB,WAAK,UAAU,CAAC,KAAK,iBAAiB,KAAK,eAAe,MAAM;AAAA,IAChE;AAAA,IACD,mBAAmB,QAAQ;AAC1B,WAAK,UAAU,CAAC,KAAK,iBAAiB,KAAK,cAAc,KAAK,eAAe,MAAM;AAAA,IACnF;AAAA,EACD;AAAA,EACD,UAAU;AAAA;AAAA,IAET,mBAAmB;AAClB,YAAM,cAAc,EAAE,GAAG,KAAK,YAAW;AACzC,UAAI,CAAC,KAAK;AAAY,eAAO;AAC7B,YAAM,EAAE,WAAW,aAAc,IAAG;AACpC,UAAI,CAAC,KAAK,iBAAiB,KAAK,OAAO;AACtC,YAAI,aAAa,CAAC,YAAY,KAAK;AAClC,sBAAY,MAAM,YAAY;AAAA,QAC9B;AACD,YAAI,gBAAgB,CAAC,YAAY,QAAQ;AACxC,sBAAY,SAAS,eAAe;AAAA,QACpC;AAAA,MACD;AACD,UAAI,KAAK,QAAQ,UAAU,CAAC,YAAY,YAAY,GAAG;AACtD,oBAAY,YAAY,IAAI,KAAK;AAAA,MACjC;AACD,UAAI,KAAK,OAAO,UAAU,CAAC,YAAY,QAAQ,GAAG;AACjD,oBAAY,QAAQ,IAAI,KAAK;AAAA,MAC7B;AACD,UAAI,KAAK,MAAM,UAAU,CAAC,YAAY,OAAO,GAAG;AAC/C,oBAAY,OAAO,IAAI,KAAK;AAAA,MAC5B;AACD,UAAI,KAAK,SAAS,UAAU,CAAC,YAAY,WAAW,GAAG;AACtD,oBAAY,WAAW,IAAI,KAAK;AAChC,oBAAY,QAAQ,IAAI;AAAA,MACxB;AACD,aAAO;AAAA,IACP;AAAA;AAAA,IAED,0BAA0B;AACzB,UAAI,KAAK,iBAAiB,GAAG;AAC5B,aAAK,mBAAmB,SAAS,IAAI,KAAK;AAC1C,aAAK,mBAAmB,UAAU,IAAI;AAAA,MACtC;AACD,aAAO,KAAK;AAAA,IACZ;AAAA,IAED,iBAAiB;AAChB,UAAK,KAAK,iBAAiB,KAAK,qBAAuB,CAAC,KAAK,oBAAoB,KAAK,cAAe,CAAC,KAAK,oBAAoB;AAC9H,aAAK,UAAU,MAAM;AACpB,eAAK,sBAAsB;AAAA,QAChC,CAAK;AAAA,MACD;AACD,aAAO;AAAA,IACP;AAAA,IACD,eAAe;AACd,UAAI,CAAC,KAAK;AAAY,eAAO;AAC7B,aAAO,KAAK,WAAW,gBAAgB;AAAA,IACvC;AAAA,IACD,eAAe;AACd,UAAI,CAAC,KAAK;AAAY,eAAO;AAC7B,UAAI,eAAe,KAAK,WAAW,gBAAgB;AAEnD,UAAI,KAAK,uBAAuB,CAAC,KAAK,0BAA0B,CAAC,KAAK,mBAAmB;AACxF,wBAAgB,KAAK;AAAA,MACrB;AACD,aAAO;AAAA,IACP;AAAA,IACD,aAAa;AAEZ,aAAO;AAAA,IAGP;AAAA,EACD;AAAA,EACD,SAAS;AAAA;AAAA,IAER,aAAa;AACZ,aAAO,aAAaC,4DAAE,OAAO;AAAA,IAC7B;AAAA;AAAA,IAED,kBAAkB,MAAM;AACvB,WAAK,sBAAsB,IAAI;AAAA,IAC/B;AAAA;AAAA,IAED,sBAAsB,MAAM;AAC3B,WAAK,eAAe,QAAQ,OAAO,KAAK,IAAI,EAAE;AAC9C,UAAI,KAAK,OAAO;AACf,aAAK,0BAA0B;AAAA,MAC/B;AACD,OAAC,KAAK,iBAAiB,KAAK,MAAM,WAAW,EAAE,kBAAkB,IAAI;AAAA,IACrE;AAAA;AAAA,IAoBD,kBAAkB;AAgBjBhB,oBAAG,MAAC,aAAY;AAAA,IAIhB;AAAA;AAAA,IAED,MAAM,eAAe,mBAAmB,MAAM,iBAAiB,MAAM;AACpE,UAAI,YAAY;AAEhB,kBAAY;AAEZ,UAAI;AACH,YAAI,kBAAkB;AAErB,cAAI,sBAAsB,kBAAkB,MAAM,KAAK,mBAAmB,iBAAiB;AAC3F,cAAI,wBAAwB,MAAM,KAAK,mBAAmB,iBAAiB;AAC3E,cAAI,qBAAqB;AACxB,kBAAM,gBAAgB,oBAAoB,CAAC,EAAE;AAC7C,gBAAI,mBAAmB,KAAK,eAAe;AAC3C,gCAAoB,wBAAwB,sBAAsB,CAAC,EAAE,SAAS;AAC9E,kBAAM,iBAAiBe,uDAAC,EAAC,YAAY,KAAK,kBAAkB;AAC5D,kBAAM,cAAc,mBAAmB,kBAAkB,KAAK,aAAa,IAAI,KAAK;AACpF,iBAAK,KAAK,KAAK,iBAAiB,WAAW,WAAW;AACtD,iBAAK,KAAK,KAAK,mBAAmB,WAAW,WAAW;AAAA,UACxD;AAAA,QACN,OAAW;AACN,eAAK,QAAQ,KAAK,iBAAiB,SAAS;AAC5C,eAAK,QAAQ,KAAK,mBAAmB,SAAS;AAAA,QAC9C;AAAA,MACL,SAAY,GAAG;AAAA,MAAE;AAAA,IACd;AAAA;AAAA,IAED,mBAAmB;AAClB,WAAK,SAAS;AACd,WAAK,SAAQ;AAGb,WAAK,qBAAqBf,cAAG,MAAC,wBAAwB,KAAK,2BAA2B;AAAA,IAEtF;AAAA;AAAA,IAED,wBAAwB;AACvB,WAAK,cAAc,KAAK,mBAAmB,QAAQ,WAAW,KAAK,YAAY,GAAG;AAAA,IAClF;AAAA;AAAA,IAED,cAAc,SAAS;AACtB,UAAI,SAAS;AACZ,qBAAa,OAAO;AACpB,kBAAU;AAAA,MACV;AACD,aAAO;AAAA,IACP;AAAA;AAAA,IAED,UAAU;AACTA,oBAAAA,MAAI,IAAIgB,0DAAAA,EAAE,gBAAgB,CAAC,aAAa;AACvC,YAAI,KAAK,SAAS;AACjB,cAAI,CAAC,CAAC,UAAU;AACf,iBAAK,6BAA6B;AAAA,UAClC;AACD,eAAK,SAAS,KAAK,EAAE,MAAM,MAAM;AAAA,UAAE,CAAA;AAAA,QACnC;AAAA,MACL,CAAI;AACDhB,oBAAAA,MAAI,IAAIgB,0DAAAA,EAAE,mBAAmB,CAAC,SAAS;AACtC,mBAAW,MAAM;AAChB,cAAI,KAAK,SAAS;AACjB,gBAAI,CAAC,KAAK,sBAAsB;AAC/B,oBAAM,OAAO,KAAK,QAAQ;AAC1B,oBAAM,OAAO,KAAK,QAAQ;AAC1B,oBAAM,OAAO,KAAK;AAClB,mBAAK,mBAAmB;AACxB,sBAAQ,MAAI;AAAA,gBACX,KAAK;AACJ,uBAAK,SAAS,IAAI;AAClB;AAAA,gBACD,KAAK;AACJ,uBAAK,gBAAgB,MAAM,IAAI;AAC/B;AAAA,gBACD,KAAK;AACJ,uBAAK,iBAAiB,MAAM,IAAI;AAChC;AAAA,gBACD,KAAK;AACJ,uBAAK,cAAc,MAAM,IAAI;AAC7B;AAAA,cAGD;AAAA,YACR,OAAa;AACN,mBAAK,uBAAuB;AAAA,YAC5B;AAAA,UACD;AAAA,QACD,GAAE,CAAC;AAAA,MACR,CAAI;AAAA,IACD;AAAA;AAAA,IAED,WAAU;AACThB,oBAAAA,MAAI,KAAKgB,4DAAE,cAAc;AACzBhB,oBAAAA,MAAI,KAAKgB,4DAAE,iBAAiB;AAAA,IAC5B;AAAA,EACD;AACF;;"}