"use strict";
const common_vendor = require("../common/vendor.js");
const config_config = require("../config/config.js");
const miniPay = function(data) {
  return new Promise((resolve, reject) => {
    const miniPayData = {
      nonceStr: data.nonceStr,
      package: data.package,
      paySign: data.paySign,
      signType: data.signType,
      timeStamp: data.timeStamp
    };
    common_vendor.index.requestPayment({
      ...miniPayData,
      success: function(wxPayRes) {
        resolve(wxPayRes);
      },
      fail: function(wxPayErr) {
        reject({
          msg: "支付失败"
        });
      },
      cancel: function() {
        reject({
          msg: "取消支付"
        });
      }
    });
  });
};
const updataFileFun = async function(data) {
  try {
    let url = `${config_config.base.baseUrl}user/api.userinfo/uploadImg`;
    let uploadFileRes = common_vendor.index.uploadFile({
      // url: 'https://ai-api.deepcity.cn/user/api.userinfo/uploadImg',
      url,
      name: "img",
      filePath: data,
      header: {
        "content-type": "multipart/form-data"
      }
    });
    if (uploadFileRes.statusCode < 200 || uploadFileRes.statusCode > 300) {
      return Promise.reject({
        errMsg: "statusCode is not 200 series"
      });
    } else {
      return uploadFileRes;
    }
  } catch (e) {
    common_vendor.index.hideLoading();
    return Promise.reject(e);
  }
};
exports.miniPay = miniPay;
exports.updataFileFun = updataFileFun;
//# sourceMappingURL=../../.sourcemap/mp-weixin/api/common.js.map
