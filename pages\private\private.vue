<template>
  <view class="container"
    :style="{ backgroundImage: 'url(https://api.zhimoai.com/storage/topic/20240830/22c0fd242cddcc1d0f7ccbbf8f4fcff1.png)' }">
    <view class="richText">
      <rich-text :nodes="richText"></rich-text>
    </view>
  </view>
</template>

<script setup>
import {
  onShareAppMessage,
  onShareTimeline
} from '@dcloudio/uni-app';
const richText =
  '<p style="text-align: center;"><strong>用户隐私协议</strong></p><p>感谢您使用我们的微信小程序。我们深知用户隐私的重要性，并承诺保护您的个人信息安全。为此，我们制定了本用户隐私协议，解释我们如何收集、使用、存储和保护您的个人信息，以及您对个人信息的控制权。</p><p><br></p><p>请您在使用小程序之前，仔细阅读并理解本隐私协议的内容。当您开始使用小程序时，即表示您已同意本协议的内容。</p><p><br></p><p><strong>1. 我们收集的信息</strong></p><p><br></p><p><strong>1.1 个人信息</strong></p><p>为了向您提供更好的服务，我们可能会收集以下类型的个人信息：</p><p>· <strong>注册信息</strong>：包括您的微信昵称、头像、手机号码、电子邮件等。</p><p>· <strong>身份信息</strong>：如身份验证过程中收集的姓名、身份证号等信息（如适用）。</p><p>· <strong>使用信息</strong>：我们会收集您在使用小程序时的操作日志、浏览记录、点击行为等数据。</p><p><br></p><p><strong>1.2 非个人信息</strong></p><p><br></p><p>我们还可能收集不涉及个人身份的数据，包括但不限于：</p><p>· <strong>设备信息</strong>：设备型号、操作系统类型、唯一设备标识符、网络类型等。</p><p>· <strong>位置信息</strong>：为提供基于位置的服务，我们可能会收集您的位置信息，但前提是您明确同意。</p><p><br></p><p><strong>2. 信息的使用</strong></p><p><br></p><p>我们收集的信息将用于以下目的：</p><p>· <strong>提供服务</strong>：基于您的个人信息，向您提供定制化服务和产品。</p><p>· <strong>优化服务体验</strong>：通过分析用户的使用习惯和行为，不断优化小程序的功能和用户体验。</p><p>· <strong>安全保障</strong>：确保小程序的安全运行，防止欺诈、网络攻击等违法行为。</p><p>· <strong>客户服务</strong>：为您提供技术支持和客户服务。</p><p><br></p><p><strong>3. 信息的分享和披露</strong></p><p><br></p><p>我们不会向任何第三方出售或出租您的个人信息，但在以下情况下，我们可能会分享您的信息：</p><p>· <strong>法定要求</strong>：应法律法规要求或相关政府机关的要求。</p><p>· <strong>业务合作</strong>：为向您提供服务，可能会与合作伙伴共享您的部分信息，前提是合作方同样承担信息保密义务。</p><p>· <strong>公司变更</strong>：如果发生重组、并购或资产转让等情况，您的信息可能作为业务资产的一部分被转移，但我们仍将确保您的个人信息保密。</p><p><br></p><p><strong>4. 信息的存储</strong></p><p><br></p><p>我们会在满足法律法规要求的基础上，尽可能采取技术和管理措施，确保您的信息安全。您的个人信息将存储在中国境内的服务器上，且仅在达到收集目的所需的时间内保存，除非法律要求延长保存期限。</p><h4><strong>5. 您的权利</strong></h4><p><br></p><p><strong>5.1 访问和修改</strong></p><p><br></p><p>您有权随时通过小程序的个人账户访问或修改您的个人信息。</p><p><br></p><p><strong>5.2 删除</strong></p><p><br></p><p>在某些情况下，您可以要求我们删除您的个人信息。我们将在法律要求的情况下处理您的删除请求。</p><p><br></p><p><strong>5.3 撤回同意</strong></p><p><br></p><p>您有权随时撤回对我们收集、使用您的个人信息的同意。但请注意，撤回同意可能会影响您继续使用我们的某些服务。</p><p><br></p><p><strong>6. 信息安全</strong></p><p><br></p><p>我们采取多种安全措施以确保您的个人信息不被丢失、滥用或未经授权的访问、披露或修改。这些措施包括但不限于数据加密、访问控制和安全审计等。</p><p><br></p><p><strong>7. 未成年人的个人信息保护</strong></p><p><br></p><p>我们的服务主要面向成年人。如果您未满18岁，您应在父母或监护人的监督下使用小程序，并确保父母或监护人同意本协议。</p><p><br></p><p><strong>8. 本协议的变更</strong></p><p><br></p><p>我们可能会不时更新本隐私协议。如果我们对本协议进行重大更改，我们将通过适当方式通知您，并在必要时再次征求您的同意。</p><p><br></p><p><strong>9. 联系我们</strong></p><p><br></p><p>如果您对本隐私协议有任何疑问或建议，或希望行使您的权利，请通过以下联系方式与我们联系：</p><p>· <strong>邮箱</strong>：<span style="font-family: 宋体;"><EMAIL></span></p>'
onShareAppMessage(() => {
  return {
    title: userStore.appName || 'AI商协通',
    path: '/pages/index/index',
    imageUrl: userStore.shareImg,
    success(res) {
      uni.showToast({
        title: '分享成功'
      })
    },
    fail(res) {
      uni.showToast({
        title: '分享失败',
        icon: 'none'
      })
    }
  }
})
// 分享到朋友圈功能
// onShareTimeline(() => {
//   return {
//     title: userStore.appName || 'AI商协通',
//     path: '/pages/index/index',
//     imageUrl: userStore.shareImg,
//     success(res) {
//       uni.showToast({
//         title: '分享成功'
//       })
//     },
//     fail(res) {
//       uni.showToast({
//         title: '分享失败',
//         icon: 'none'
//       })
//     }
//   }
// })
</script>

<style scoped lang="scss">
.container {
  background: #F7F7F7;
  min-height: 100vh;
  // padding-bottom: 30rpx;
  background-repeat: no-repeat;
  background-size: 100% 518rpx;
  width: 100%;
  overflow-x: hidden;
}

.richText {
  padding: 20px;
}
</style>