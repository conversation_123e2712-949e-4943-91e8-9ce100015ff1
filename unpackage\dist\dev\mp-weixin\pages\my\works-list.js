"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const api_index = require("../../api/index.js");
const stores_user = require("../../stores/user.js");
if (!Array) {
  const _easycom_z_paging2 = common_vendor.resolveComponent("z-paging");
  _easycom_z_paging2();
}
const _easycom_z_paging = () => "../../uni_modules/z-paging/components/z-paging/z-paging.js";
if (!Math) {
  _easycom_z_paging();
}
const _sfc_main = {
  __name: "works-list",
  setup(__props) {
    const userStore = stores_user.useUserStore();
    const worksList = common_vendor.ref([]);
    const paging = common_vendor.ref(null);
    const isEditMode = common_vendor.ref(false);
    const selectedWorks = common_vendor.ref([]);
    const queryList = async (page, pageSize) => {
      try {
        const res = await api_index.worksListApi({
          merchantGuid: userStore.merchantGuid,
          page,
          pageSize
        });
        paging.value.complete(res.data.list || []);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/my/works-list.vue:78", "获取作品列表失败:", error);
        common_vendor.index.showToast({
          title: "加载失败",
          icon: "none"
        });
        paging.value.complete(false);
      }
    };
    const enterEditMode = () => {
      isEditMode.value = true;
      selectedWorks.value = [];
    };
    const cancelEdit = () => {
      isEditMode.value = false;
      selectedWorks.value = [];
    };
    const handleWorkTap = (work) => {
      if (!isEditMode.value) {
        if (work.workStatus === "fail") {
          common_vendor.index.showToast({
            title: "作品生成失败，无法查看",
            icon: "none"
          });
          return;
        }
        if (work.workStatus === "doing") {
          common_vendor.index.showToast({
            title: "作品加速处理中，无法查看",
            icon: "none"
          });
          return;
        }
        common_vendor.index.navigateTo({
          url: `/pages/my/video-complete?orderNo=${work.orderNo}`
        });
        return;
      }
      const workId = work.orderNo;
      const selectedIndex = selectedWorks.value.indexOf(workId);
      if (selectedIndex > -1) {
        selectedWorks.value.splice(selectedIndex, 1);
      } else {
        selectedWorks.value.push(workId);
      }
    };
    const deleteSelected = () => {
      if (selectedWorks.value.length === 0) {
        common_vendor.index.showToast({
          title: "请先选择要删除的作品",
          icon: "none"
        });
        return;
      }
      common_vendor.index.showModal({
        title: "确认删除",
        content: `确定要删除选中的${selectedWorks.value.length}个作品吗？`,
        success: async (res) => {
          if (res.confirm) {
            try {
              const deleteRes = await api_index.deleteWorksApi({
                merchantGuid: userStore.merchantGuid,
                orderNos: selectedWorks.value
              });
              selectedWorks.value = [];
              isEditMode.value = false;
              paging.value.reload();
              common_vendor.index.showToast({
                title: deleteRes.data.message,
                icon: "none"
              });
            } catch (error) {
              common_vendor.index.__f__("error", "at pages/my/works-list.vue:165", "删除作品失败:", error);
              common_vendor.index.showToast({
                title: "删除失败",
                icon: "none"
              });
            }
          }
        }
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.f(worksList.value, (work, k0, i0) => {
          return common_vendor.e({
            a: work.previewUrl,
            b: isEditMode.value && selectedWorks.value.includes(work.orderNo)
          }, isEditMode.value && selectedWorks.value.includes(work.orderNo) ? {
            c: common_assets._imports_1$2
          } : {}, {
            d: work.orderNo,
            e: isEditMode.value && selectedWorks.value.includes(work.orderNo) ? 1 : "",
            f: common_vendor.o(($event) => handleWorkTap(work), work.orderNo)
          });
        }),
        b: common_vendor.sr(paging, "5332091b-0", {
          "k": "paging"
        }),
        c: common_vendor.o(queryList),
        d: common_vendor.o(($event) => worksList.value = $event),
        e: common_vendor.p({
          ["refresher-enabled"]: true,
          auto: true,
          modelValue: worksList.value
        }),
        f: !isEditMode.value
      }, !isEditMode.value ? {
        g: common_assets._imports_3$1,
        h: common_vendor.o(enterEditMode)
      } : {
        i: common_vendor.o(cancelEdit),
        j: common_vendor.o(deleteSelected)
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-5332091b"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/my/works-list.js.map
