{"version": 3, "file": "figure-list.js", "sources": ["pages/my/figure-list.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvbXkvZmlndXJlLWxpc3QudnVl"], "sourcesContent": ["<template>\r\n\t<view class=\"my-figure-container\">\r\n\t\t<!-- 作品内容区域 -->\r\n\t\t<z-paging\r\n\t\t\tref=\"paging\"\r\n\t\t\tv-model=\"figureList\"\r\n\t\t\t@query=\"queryList\"\r\n\t\t\t:refresher-enabled=\"true\"\r\n\t\t\t:auto=\"true\"\r\n\t\t>\r\n\t\t\t<view class=\"figure-content\">\r\n\t\t\t\t<!-- 作品卡片区域 -->\r\n\t\t\t\t<view class=\"figure-grid\">\r\n\t\t\t\t\t<!-- 创建新作品卡片 -->\r\n\t\t\t\t\t<view class=\"figure-item figure-upload\" @tap=\"showUploadModal\">\r\n\t                    <image class=\"create-icon\" src=\"/static/my/figure_add.png\" />\r\n\t                    <text class=\"create-title\">定制数字人</text>\r\n\t                    <!-- <text class=\"create-subtitle\">免费次数: 2/3</text> -->\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<!-- 数字人作品卡片列表 -->\r\n\t\t\t\t\t<view\r\n\t\t\t\t\t\tv-for=\"figure in figureList\"\r\n\t\t\t\t\t\t:key=\"figure.personGuid\"\r\n\t\t\t\t\t\tclass=\"figure-item figure-list\"\r\n\t\t\t\t\t\t@tap=\"handleFigureTap(figure)\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<view class=\"avatar-container\" :class=\"{ selected: isEditMode && selectedFigure === figure.personGuid }\">\r\n\t\t\t\t\t\t\t<view class=\"avatar-bg\">\r\n\t\t\t\t\t\t\t\t<image class=\"avatar-image\" :src=\"figure.picUrl\" mode=\"heightFix\" />\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"badge\">{{ figure.statusText }}</view>\r\n\t\t\t\t\t\t\t<view v-if=\"isEditMode\" class=\"check-mark\">\r\n\t                            <image class=\"check-icon\" v-if=\"selectedFigure === figure.personGuid\" src=\"/static/my/template_select.png\" />\r\n\t                            <image class=\"check-icon\" v-else src=\"/static/my/template_select1.png\" />\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"avatar-info\">\r\n\t\t\t\t\t\t\t<view class=\"avatar-name\">{{ figure.personName }}</view>\r\n\t                        <!-- <image v-if=\"!isEditMode\" class=\"more-options\" src=\"/static/my/figure_operate.png\" @tap.stop=\"showOptions(figure)\" /> -->\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</z-paging>\r\n\r\n\t\t<!-- 底部操作按钮 -->\r\n\t\t<view class=\"bottom-actions\">\r\n\t\t\t<!-- 编辑模式 -->\r\n\t\t\t<template v-if=\"!isEditMode\">\r\n\t\t\t\t<view class=\"action-button edit\" @tap=\"enterEditMode\">\r\n\t\t\t\t\t<image class=\"edit-icon\" src=\"/static/my/<EMAIL>\" mode=\"aspectFit\" />\r\n\t\t\t\t\t<text class=\"action-text\">编辑</text>\r\n\t\t\t\t</view>\r\n\t\t\t</template>\r\n\t\t\t<!-- 编辑状态下的按钮 -->\r\n\t\t\t<template v-else>\r\n\t\t\t\t<view class=\"action-button cancel\" @tap=\"cancelEdit\">\r\n\t\t\t\t\t<text class=\"action-text\">取消</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"action-button delete\" @tap=\"deleteSelected\">\r\n\t\t\t\t\t<text class=\"action-text\">删除</text>\r\n\t\t\t\t</view>\r\n\t\t\t</template>\r\n\t\t</view>\r\n\r\n\t\t<!-- 上传视频弹窗 -->\r\n\t\t<view v-if=\"showUploadPopup\" class=\"upload-modal-overlay\">\r\n\t\t\t<view class=\"upload-modal-content\" @tap.stop>\r\n\t\t\t\t<!-- 关闭按钮 -->\r\n\t\t\t\t<view class=\"close-btn\" @tap=\"hideUploadModal\">\r\n\t\t\t\t\t<image class=\"close-icon\" src=\"/static/my/popup-close.png\" mode=\"aspectFit\" />\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 弹窗标题 -->\r\n\t\t\t\t<text class=\"upload-title\">请上传您的视频完成数字人制作</text>\r\n\r\n\t\t\t\t<!-- 提示内容 -->\r\n\t\t\t\t<view class=\"upload-tips\">\r\n\t\t\t\t\t<view class=\"tip-item\">\r\n\t\t\t\t\t\t<text class=\"tip-title\">录制时可以自然微笑，保持放松</text>\r\n\t\t\t\t\t\t<text class=\"tip-desc\">您好!我现在感觉很好，语调很轻松，我很有信心能做好这次视频录制。 我现在就在镜头前，准备开始。</text>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view class=\"tip-item\">\r\n\t\t\t\t\t\t<text class=\"tip-title\">闭上嘴，用鼻子呼吸，停顿1s</text>\r\n\t\t\t\t\t\t<text class=\"tip-desc\">光线很好，我的脸上没有任何刺眼的阴影，我的发音很清晰，感觉很放松，我会做一些自然、轻微的手部动作。</text>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view class=\"tip-item\">\r\n\t\t\t\t\t\t<text class=\"tip-title\">闭上嘴，用鼻子呼吸，停顿1s</text>\r\n\t\t\t\t\t\t<text class=\"tip-desc\">一些细微的姿态动作会让我看起来更自然更放松。 在整个视频录制过程中，我不会移动我的身体，也不会做太剧烈的任何动作。</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 上传按钮 -->\r\n\t\t\t\t<view class=\"upload-btn\" @tap=\"uploadVideo\">\r\n\t\t\t\t\t<text class=\"upload-btn-text\">上传视频</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t</view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref } from 'vue'\r\nimport { getMyPersonListApi, deletePersonApi, uploadVideoApi, createPersonkApi } from '@/api/index.js'\r\nimport { useUserStore } from '@/stores/user.js'\r\nimport base from '@/config/config.js'\r\n\r\nconst userStore = useUserStore()\r\n\r\n// 响应式数据\r\nconst showUploadPopup = ref(false)\r\nconst figureList = ref([])\r\nconst paging = ref(null)\r\n\r\n// 编辑模式状态\r\nconst isEditMode = ref(false)\r\nconst selectedFigure = ref(null) // 改为单选\r\n\r\n// 分页查询形象列表\r\nconst queryList = async (page, pageSize) => {\r\n\ttry {\r\n\t\tconst res = await getMyPersonListApi({\r\n\t\t\tmerchantGuid: userStore.merchantGuid,\r\n\t\t\tpage: page,\r\n\t\t\tpageSize: pageSize\r\n\t\t})\r\n\t\t// 使用z-paging的complete方法处理数据\r\n\t\tpaging.value.complete(res.data.list || [])\r\n\t} catch (error) {\r\n\t\tconsole.error('获取形象列表失败:', error)\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '加载失败',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\tpaging.value.complete(false)\r\n\t}\r\n}\r\n\r\n// 进入编辑模式\r\nconst enterEditMode = () => {\r\n\tisEditMode.value = true\r\n\tselectedFigure.value = null\r\n}\r\n\r\n// 取消编辑模式\r\nconst cancelEdit = () => {\r\n\tisEditMode.value = false\r\n\tselectedFigure.value = null\r\n}\r\n\r\n// 处理形象点击\r\nconst handleFigureTap = (figure) => {\r\n\tif (!isEditMode.value) {\r\n\t\treturn\r\n\t}\r\n\r\n\t// 编辑模式下，切换选择状态（单选）\r\n\tconst personGuid = figure.personGuid\r\n\tif (selectedFigure.value === personGuid) {\r\n\t\t// 如果已选中，则取消选择\r\n\t\tselectedFigure.value = null\r\n\t} else {\r\n\t\t// 选择当前项（单选，会覆盖之前的选择）\r\n\t\tselectedFigure.value = personGuid\r\n\t}\r\n}\r\n\r\n// 删除选中的形象\r\nconst deleteSelected = () => {\r\n\tif (!selectedFigure.value) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请先选择要删除的形象',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\r\n\tuni.showModal({\r\n\t\ttitle: '确认删除',\r\n\t\tcontent: '确定要删除选中的形象吗？',\r\n\t\tsuccess: async (res) => {\r\n\t\t\tif (res.confirm) {\r\n\t\t\t\ttry {\r\n\t\t\t\t\t// 调用删除接口\r\n\t\t\t\t\tawait deletePersonApi({\r\n\t\t\t\t\t\tmerchantGuid: userStore.merchantGuid,\r\n\t\t\t\t\t\tpersonGuid: selectedFigure.value\r\n\t\t\t\t\t})\r\n\r\n\t\t\t\t\t// 删除成功后刷新列表\r\n\t\t\t\t\tselectedFigure.value = null\r\n\t\t\t\t\tisEditMode.value = false\r\n\t\t\t\t\tpaging.value.reload()\r\n\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '删除成功',\r\n\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t})\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('删除形象失败:', error)\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '删除失败',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t})\r\n}\r\n\r\n// 显示上传弹窗\r\nconst showUploadModal = () => {\r\n\tshowUploadPopup.value = true\r\n}\r\n\r\n// 隐藏上传弹窗\r\nconst hideUploadModal = () => {\r\n\tshowUploadPopup.value = false\r\n}\r\n\r\n// 上传视频\r\nconst uploadVideo = () => {\r\n\tuni.chooseVideo({\r\n\t\tsourceType: ['camera', 'album'],\r\n\t\tmaxDuration: 60,\r\n\t\tsuccess: async (res) => {\r\n\t\t\tconsole.log('选择的视频:', res.tempFilePath)\r\n\r\n\t\t\t// 显示上传中提示\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '上传中...',\r\n\t\t\t\tmask: true\r\n\t\t\t})\r\n\r\n\t\t\ttry {\r\n\t\t\t\t// 上传视频文件\r\n\t\t\t\tconst uploadRes = await new Promise((resolve, reject) => {\r\n\t\t\t\t\tuni.uploadFile({\r\n\t\t\t\t\t\turl: `${base.baseUrl}user/api.userinfo/uploadVideo`,\r\n\t\t\t\t\t\tname: 'video',\r\n\t\t\t\t\t\tfileType: 'video',\r\n\t\t\t\t\t\tfilePath: res.tempFilePath,\r\n\t\t\t\t\t\tsuccess: (uploadResult) => {\r\n\t\t\t\t\t\t\tresolve(uploadResult)\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tfail: (error) => {\r\n\t\t\t\t\t\t\treject(error)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\r\n\t\t\t\t// 解析上传结果\r\n\t\t\t\tconst uploadData = JSON.parse(uploadRes.data)\r\n\t\t\t\tconsole.log('视频上传结果:', uploadData)\r\n\r\n\t\t\t\tif (uploadData.code === 0) {\r\n\t\t\t\t\t// 上传成功，调用创建形象接口\r\n\t\t\t\t\tconst createRes = await createPersonkApi({\r\n\t\t\t\t\t\tmerchantGuid: userStore.merchantGuid,\r\n\t\t\t\t\t\tmaterialVideoUrl: uploadData.data, // 使用上传返回的视频URL\r\n\t\t\t\t\t\tpersonName: `定制形象`, // 生成默认名称\r\n\t\t\t\t\t})\r\n\r\n\t\t\t\t\tif (createRes.code === 0) {\r\n\t\t\t\t\t\tuni.hideLoading()\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '形象创建成功',\r\n\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t})\r\n\r\n\t\t\t\t\t\t// 刷新列表\r\n\t\t\t\t\t\tpaging.value.reload()\r\n\t\t\t\t\t\thideUploadModal()\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthrow new Error(createRes.msg || '创建形象失败')\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthrow new Error(uploadData.msg || '视频上传失败')\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {\r\n\t\t\t\tuni.hideLoading()\r\n\t\t\t\tconsole.error('上传或创建失败:', error)\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: error.message || '操作失败，请重试',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\t\tfail: (err) => {\r\n\t\t\tconsole.log('选择视频失败:', err)\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '选择视频失败',\r\n\t\t\t\ticon: 'none'\r\n\t\t\t})\r\n\t\t}\r\n\t})\r\n}\r\n\r\nconst showOptions = (figure) => {\r\n\tuni.showActionSheet({\r\n\t\titemList: ['编辑', '复制', '分享'],\r\n\t\tsuccess: (res) => {\r\n\t\t\tconst actions = ['编辑', '复制', '分享']\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: actions[res.tapIndex],\r\n\t\t\t\ticon: 'none',\r\n\t\t\t\tduration: 1500\r\n\t\t\t})\r\n\t\t}\r\n\t})\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.my-figure-container {\r\n\tmin-height: 100vh;\r\n\tbackground: #ffffff;\r\n\tpadding-bottom: 160rpx;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n}\r\n\r\n.figure-content {\r\n\tpadding: 32rpx 30rpx;\r\n\tflex: 1;\r\n}\r\n\r\n.figure-grid {\r\n\tdisplay: flex;\r\n    flex-wrap: wrap;\r\n    .figure-upload{\r\n        width: 330rpx;\r\n        height: 330rpx;\r\n        display: flex;\r\n        border-radius: 15rpx;\r\n        flex-direction: column;\r\n        align-items: center;\r\n        justify-content: center;\r\n        background: #FAFAFA;\r\n        border: 2rpx dashed #DBDBDB !important;\r\n\r\n        .create-icon {\r\n            width: 40rpx;\r\n            height: 40rpx;\r\n            display: block;\r\n            margin-bottom: 16rpx;\r\n        }\r\n\r\n        .create-title {\r\n            font-size: 26rpx;\r\n            color: #999999;\r\n            margin-bottom: 4rpx;\r\n        }\r\n\r\n        .create-subtitle {\r\n            font-size: 24rpx;\r\n            color: #FD8D2B;\r\n        }\r\n    }\r\n    .figure-item{\r\n        margin-bottom: 40rpx;\r\n        position: relative;\r\n    }\r\n    .figure-list {\r\n        // background: #ffffff;\r\n        width: 326rpx;\r\n\r\n        .avatar-container {\r\n            position: relative;\r\n            flex: 1;\r\n            margin-bottom: 24rpx;\r\n            border-radius: 16rpx;\r\n            overflow: hidden;\r\n            border: 3rpx solid transparent;\r\n\r\n            &.selected {\r\n                border: 3rpx solid #2A64F6;\r\n            }\r\n\r\n            .avatar-bg {\r\n                width: 330rpx;\r\n                height: 330rpx;\r\n                background: #000000;\r\n                display: flex;\r\n                align-items: center;\r\n                justify-content: center;\r\n\r\n                .avatar-image {\r\n                    height: 100%;\r\n                    width: auto;\r\n                    max-width: 100%;\r\n                }\r\n            }\r\n\r\n            .badge {\r\n                position: absolute;\r\n                top: 16rpx;\r\n                left: 16rpx;\r\n                background: linear-gradient( 132deg, #FFFFB5 0%, #FDDB66 49%, #FAF7B0 100%);\r\n                color: #4A3900;\r\n                font-size: 24rpx;\r\n                padding: 8rpx 16rpx;\r\n                border-radius: 16rpx;\r\n                font-weight: 600;\r\n            }\r\n\r\n            .check-mark {\r\n                position: absolute;\r\n                top: 16rpx;\r\n                right: 16rpx;\r\n                width: 48rpx;\r\n                height: 48rpx;\r\n                border-radius: 50%;\r\n                display: flex;\r\n                align-items: center;\r\n                justify-content: center;\r\n\r\n                .check-icon {\r\n                    width: 40rpx;\r\n                    height: 40rpx;\r\n                }\r\n            }\r\n        }\r\n\r\n        .avatar-info {\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: space-between;\r\n            margin-bottom: 20rpx;\r\n\r\n            .avatar-name {\r\n                font-size: 28rpx;\r\n                color: #999999;\r\n                font-weight: 500;\r\n                width: calc(100% - 40rpx);\r\n                // 一行隐藏\r\n                overflow: hidden;\r\n                text-overflow: ellipsis;\r\n                white-space: nowrap;\r\n            }\r\n\r\n            .more-options {\r\n                width: 30rpx;\r\n                height: 30rpx;\r\n            }\r\n        }\r\n    }\r\n    .figure-item:not(:nth-child(2n-1)){\r\n        margin-left: 30rpx;\r\n    }\r\n}\r\n\r\n.bottom-actions {\r\n\tposition: fixed;\r\n\tbottom: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tdisplay: flex;\r\n\tbackground: #ffffff;\r\n\tpadding: 32rpx;\r\n\tborder-top: 1rpx solid #f0f0f0;\r\n\tgap: 24rpx;\r\n\r\n\t.action-button {\r\n\t\tflex: 1;\r\n\t\theight: 96rpx;\r\n\t\tborder-radius: 48rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\r\n\t\t&.edit {\r\n\t\t\t.edit-icon {\r\n\t\t\t\twidth: 32rpx;\r\n\t\t\t\theight: 32rpx;\r\n\t\t\t\tmargin-right: 12rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.action-text {\r\n\t\t\t\tcolor: #666666;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&.cancel {\r\n\t\t\tbackground: #f8f8f8;\r\n\t\t\t.action-text {\r\n\t\t\t\tcolor: #666666;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&.delete {\r\n\t\t\tbackground: #ffffff;\r\n\t\t\tborder: 2rpx solid #FA5151;\r\n\r\n\t\t\t.action-text {\r\n\t\t\t\tcolor: #FA5151;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.action-text {\r\n\t\t\tfont-size: 32rpx;\r\n\t\t\tfont-weight: 500;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// 上传弹窗样式\r\n.upload-modal-overlay {\r\n\tposition: fixed;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tbottom: 0;\r\n\tbackground: rgba(0, 0, 0, 0.6);\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tz-index: 9999;\r\n}\r\n\r\n.upload-modal-content {\r\n\tbackground: #ffffff;\r\n\tborder-radius: 32rpx;\r\n\tpadding: 60rpx 40rpx 50rpx;\r\n\twidth: 640rpx;\r\n\tmax-height: 80vh;\r\n\tposition: relative;\r\n    box-sizing: border-box;\r\n\r\n\t.close-btn {\r\n\t\tposition: absolute;\r\n\t\tbottom: -80rpx;\r\n\t\twidth: 45rpx;\r\n\t\theight: 45rpx;\r\n\t\tleft: 50%;\r\n        transform: translateX(-50%);\r\n\r\n\t\t.close-icon {\r\n\t\t\twidth: 45rpx;\r\n\t\t\theight: 45rpx;\r\n\t\t}\r\n\t}\r\n\r\n\t.upload-title {\r\n\t\tdisplay: block;\r\n\t\tfont-size: 35rpx;\r\n        font-weight: 500;\r\n\t\tcolor: #2A64F6;\r\n\t\ttext-align: center;\r\n\t\tmargin-bottom: 48rpx;\r\n\t\tline-height: 1.4;\r\n\t}\r\n\r\n\t.upload-tips {\r\n\t\tmargin-bottom: 48rpx;\r\n\r\n\t\t.tip-item {\r\n\t\t\tmargin-bottom: 20rpx;\r\n            background: #F4F6F8;\r\n            border-radius: 15rpx;\r\n            padding: 30rpx;\r\n\r\n\t\t\t&:last-child {\r\n\t\t\t\tmargin-bottom: 0;\r\n\t\t\t}\r\n\r\n\t\t\t.tip-title {\r\n\t\t\t\tdisplay: block;\r\n\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\tfont-weight: 500;\r\n\t\t\t\tcolor: #222222;\r\n\t\t\t\tmargin-bottom: 6rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.tip-desc {\r\n\t\t\t\tdisplay: block;\r\n\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\tcolor: #999999;\r\n\t\t\t\tline-height: 1.6;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.upload-btn {\r\n\t\twidth: 100%;\r\n\t\theight: 96rpx;\r\n\t\tbackground: #2A64F6;\r\n\t\tborder-radius: 48rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\r\n\t\t.upload-btn-text {\r\n\t\t\tfont-size: 32rpx;\r\n\t\t\tcolor: #ffffff;\r\n\t\t\tfont-weight: 600;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.modal-overlay {\r\n\tposition: fixed;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tbottom: 0;\r\n\tbackground: rgba(0, 0, 0, 0.5);\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tz-index: 9999;\r\n}\r\n\r\n.modal-content {\r\n\tbackground: #ffffff;\r\n\tborder-radius: 24rpx;\r\n\tpadding: 48rpx 32rpx;\r\n\twidth: 600rpx;\r\n\tmax-width: 90vw;\r\n\r\n\t.modal-title {\r\n\t\tdisplay: block;\r\n\t\tfont-size: 36rpx;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: #333333;\r\n\t\ttext-align: center;\r\n\t\tmargin-bottom: 24rpx;\r\n\t}\r\n\r\n\t.modal-message {\r\n\t\tdisplay: block;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #666666;\r\n\t\ttext-align: center;\r\n\t\tmargin-bottom: 48rpx;\r\n\t\tline-height: 1.5;\r\n\t}\r\n\r\n\t.modal-actions {\r\n\t\tdisplay: flex;\r\n\t\tgap: 24rpx;\r\n\r\n\t\t.modal-button {\r\n\t\t\tflex: 1;\r\n\t\t\theight: 80rpx;\r\n\t\t\tborder-radius: 40rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\r\n\t\t\t&.cancel {\r\n\t\t\t\tbackground: #f5f5f5;\r\n\r\n\t\t\t\t.modal-button-text {\r\n\t\t\t\t\tcolor: #666666;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t&.confirm {\r\n\t\t\t\tbackground: #FF4757;\r\n\r\n\t\t\t\t.modal-button-text {\r\n\t\t\t\t\tcolor: #ffffff;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.modal-button-text {\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tfont-weight: 600;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n</style>\r\n", "import MiniProgramPage from 'E:/youngProject/agent-mini-ui/pages/my/figure-list.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useUserStore", "ref", "getMyPersonListApi", "uni", "deletePersonApi", "base", "createPersonkApi"], "mappings": ";;;;;;;;;;;;;;;;;AA+GA,UAAM,YAAYA,YAAAA,aAAc;AAGhC,UAAM,kBAAkBC,cAAG,IAAC,KAAK;AACjC,UAAM,aAAaA,cAAG,IAAC,EAAE;AACzB,UAAM,SAASA,cAAG,IAAC,IAAI;AAGvB,UAAM,aAAaA,cAAG,IAAC,KAAK;AAC5B,UAAM,iBAAiBA,cAAG,IAAC,IAAI;AAG/B,UAAM,YAAY,OAAO,MAAM,aAAa;AAC3C,UAAI;AACH,cAAM,MAAM,MAAMC,6BAAmB;AAAA,UACpC,cAAc,UAAU;AAAA,UACxB;AAAA,UACA;AAAA,QACH,CAAG;AAED,eAAO,MAAM,SAAS,IAAI,KAAK,QAAQ,EAAE;AAAA,MACzC,SAAQ,OAAO;AACfC,sBAAAA,wDAAc,aAAa,KAAK;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACT,CAAG;AACD,eAAO,MAAM,SAAS,KAAK;AAAA,MAC3B;AAAA,IACF;AAGA,UAAM,gBAAgB,MAAM;AAC3B,iBAAW,QAAQ;AACnB,qBAAe,QAAQ;AAAA,IACxB;AAGA,UAAM,aAAa,MAAM;AACxB,iBAAW,QAAQ;AACnB,qBAAe,QAAQ;AAAA,IACxB;AAGA,UAAM,kBAAkB,CAAC,WAAW;AACnC,UAAI,CAAC,WAAW,OAAO;AACtB;AAAA,MACA;AAGD,YAAM,aAAa,OAAO;AAC1B,UAAI,eAAe,UAAU,YAAY;AAExC,uBAAe,QAAQ;AAAA,MACzB,OAAQ;AAEN,uBAAe,QAAQ;AAAA,MACvB;AAAA,IACF;AAGA,UAAM,iBAAiB,MAAM;AAC5B,UAAI,CAAC,eAAe,OAAO;AAC1BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACT,CAAG;AACD;AAAA,MACA;AAEDA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,OAAO,QAAQ;AACvB,cAAI,IAAI,SAAS;AAChB,gBAAI;AAEH,oBAAMC,0BAAgB;AAAA,gBACrB,cAAc,UAAU;AAAA,gBACxB,YAAY,eAAe;AAAA,cACjC,CAAM;AAGD,6BAAe,QAAQ;AACvB,yBAAW,QAAQ;AACnB,qBAAO,MAAM,OAAQ;AAErBD,4BAAAA,MAAI,UAAU;AAAA,gBACb,OAAO;AAAA,gBACP,MAAM;AAAA,cACZ,CAAM;AAAA,YACD,SAAQ,OAAO;AACfA,4BAAAA,wDAAc,WAAW,KAAK;AAC9BA,4BAAAA,MAAI,UAAU;AAAA,gBACb,OAAO;AAAA,gBACP,MAAM;AAAA,cACZ,CAAM;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,MACH,CAAE;AAAA,IACF;AAGA,UAAM,kBAAkB,MAAM;AAC7B,sBAAgB,QAAQ;AAAA,IACzB;AAGA,UAAM,kBAAkB,MAAM;AAC7B,sBAAgB,QAAQ;AAAA,IACzB;AAGA,UAAM,cAAc,MAAM;AACzBA,oBAAAA,MAAI,YAAY;AAAA,QACf,YAAY,CAAC,UAAU,OAAO;AAAA,QAC9B,aAAa;AAAA,QACb,SAAS,OAAO,QAAQ;AACvBA,wBAAY,MAAA,MAAA,OAAA,mCAAA,UAAU,IAAI,YAAY;AAGtCA,wBAAAA,MAAI,YAAY;AAAA,YACf,OAAO;AAAA,YACP,MAAM;AAAA,UACV,CAAI;AAED,cAAI;AAEH,kBAAM,YAAY,MAAM,IAAI,QAAQ,CAAC,SAAS,WAAW;AACxDA,4BAAAA,MAAI,WAAW;AAAA,gBACd,KAAK,GAAGE,mBAAK,OAAO;AAAA,gBACpB,MAAM;AAAA,gBACN,UAAU;AAAA,gBACV,UAAU,IAAI;AAAA,gBACd,SAAS,CAAC,iBAAiB;AAC1B,0BAAQ,YAAY;AAAA,gBACpB;AAAA,gBACD,MAAM,CAAC,UAAU;AAChB,yBAAO,KAAK;AAAA,gBACZ;AAAA,cACP,CAAM;AAAA,YACN,CAAK;AAGD,kBAAM,aAAa,KAAK,MAAM,UAAU,IAAI;AAC5CF,0BAAAA,MAAY,MAAA,OAAA,mCAAA,WAAW,UAAU;AAEjC,gBAAI,WAAW,SAAS,GAAG;AAE1B,oBAAM,YAAY,MAAMG,2BAAiB;AAAA,gBACxC,cAAc,UAAU;AAAA,gBACxB,kBAAkB,WAAW;AAAA;AAAA,gBAC7B,YAAY;AAAA;AAAA,cAClB,CAAM;AAED,kBAAI,UAAU,SAAS,GAAG;AACzBH,8BAAAA,MAAI,YAAa;AACjBA,8BAAAA,MAAI,UAAU;AAAA,kBACb,OAAO;AAAA,kBACP,MAAM;AAAA,gBACb,CAAO;AAGD,uBAAO,MAAM,OAAQ;AACrB,gCAAiB;AAAA,cACvB,OAAY;AACN,sBAAM,IAAI,MAAM,UAAU,OAAO,QAAQ;AAAA,cACzC;AAAA,YACN,OAAW;AACN,oBAAM,IAAI,MAAM,WAAW,OAAO,QAAQ;AAAA,YAC1C;AAAA,UACD,SAAQ,OAAO;AACfA,0BAAAA,MAAI,YAAa;AACjBA,0BAAAA,MAAA,MAAA,SAAA,mCAAc,YAAY,KAAK;AAC/BA,0BAAAA,MAAI,UAAU;AAAA,cACb,OAAO,MAAM,WAAW;AAAA,cACxB,MAAM;AAAA,YACX,CAAK;AAAA,UACD;AAAA,QACD;AAAA,QACD,MAAM,CAAC,QAAQ;AACdA,wBAAAA,MAAA,MAAA,OAAA,mCAAY,WAAW,GAAG;AAC1BA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACV,CAAI;AAAA,QACD;AAAA,MACH,CAAE;AAAA,IACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3SA,GAAG,WAAW,eAAe;"}