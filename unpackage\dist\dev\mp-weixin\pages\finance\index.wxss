/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.finance-flow-page.data-v-b0c916dd {
  background: #f5f5f5;
  min-height: 100vh;
}
.finance-flow-page .record-list.data-v-b0c916dd {
  padding: 32rpx;
}
.finance-flow-page .record-list .record-item.data-v-b0c916dd {
  background: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  overflow: hidden;
}
.finance-flow-page .record-list .record-item .record-content.data-v-b0c916dd {
  padding: 40rpx 32rpx;
}
.finance-flow-page .record-list .record-item .record-content .main-info.data-v-b0c916dd {
  margin-bottom: 24rpx;
}
.finance-flow-page .record-list .record-item .record-content .main-info .amount-line.data-v-b0c916dd {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}
.finance-flow-page .record-list .record-item .record-content .main-info .amount-line .amount-label.data-v-b0c916dd {
  font-size: 28rpx;
  color: #999999;
  margin-right: 8rpx;
}
.finance-flow-page .record-list .record-item .record-content .main-info .amount-line .amount.data-v-b0c916dd {
  font-size: 32rpx;
  font-weight: 600;
}
.finance-flow-page .record-list .record-item .record-content .main-info .amount-line .amount.amount-income.data-v-b0c916dd {
  color: #3478f6;
}
.finance-flow-page .record-list .record-item .record-content .main-info .amount-line .amount.amount-expense.data-v-b0c916dd {
  color: #3478f6;
}
.finance-flow-page .record-list .record-item .record-content .main-info .reason-line.data-v-b0c916dd {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16rpx;
}
.finance-flow-page .record-list .record-item .record-content .main-info .reason-line .reason-label.data-v-b0c916dd {
  font-size: 28rpx;
  color: #999999;
  margin-right: 8rpx;
  flex-shrink: 0;
}
.finance-flow-page .record-list .record-item .record-content .main-info .reason-line .reason-text.data-v-b0c916dd {
  font-size: 28rpx;
  color: #333333;
  line-height: 1.4;
  flex: 1;
}
.finance-flow-page .record-list .record-item .record-content .main-info .time-line.data-v-b0c916dd {
  display: flex;
  align-items: center;
}
.finance-flow-page .record-list .record-item .record-content .main-info .time-line .time-label.data-v-b0c916dd {
  font-size: 28rpx;
  color: #999999;
  margin-right: 8rpx;
}
.finance-flow-page .record-list .record-item .record-content .main-info .time-line .time-value.data-v-b0c916dd {
  font-size: 28rpx;
  color: #999999;
}
.finance-flow-page .record-list .record-item .record-content .balance-info.data-v-b0c916dd {
  text-align: right;
}
.finance-flow-page .record-list .record-item .record-content .balance-info .balance-text.data-v-b0c916dd {
  font-size: 28rpx;
  color: #999999;
}
.finance-flow-page .empty-state.data-v-b0c916dd {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 200rpx 0;
}
.finance-flow-page .empty-state .empty-icon.data-v-b0c916dd {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
  opacity: 0.6;
}
.finance-flow-page .empty-state .empty-text.data-v-b0c916dd {
  font-size: 28rpx;
  color: #999999;
}