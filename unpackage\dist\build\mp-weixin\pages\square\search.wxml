<view class="container data-v-59b7fb21"><view class="search-container data-v-59b7fb21"><view class="search-box data-v-59b7fb21"><image src="{{a}}" class="search-icon data-v-59b7fb21" mode="aspectFit"></image><input class="search-input data-v-59b7fb21" placeholder="搜索智能体" placeholder-class="placeholder" bindinput="{{b}}" bindconfirm="{{c}}" focus value="{{d}}"/><view wx:if="{{e}}" class="clear-btn data-v-59b7fb21" bindtap="{{f}}"><text class="clear-text data-v-59b7fb21">×</text></view></view><view class="cancel-btn data-v-59b7fb21" bindtap="{{g}}"><text class="cancel-text data-v-59b7fb21">取消</text></view></view><view class="content-container data-v-59b7fb21"><view wx:if="{{h}}" class="loading-container data-v-59b7fb21"><text class="loading-text data-v-59b7fb21">搜索中...</text></view><view wx:elif="{{i}}" class="empty-container data-v-59b7fb21"><image src="{{j}}" class="empty-icon data-v-59b7fb21" mode="aspectFit"></image><text class="empty-text data-v-59b7fb21">未找到相关智能体</text><text class="empty-desc data-v-59b7fb21">试试其他关键词吧</text></view><scroll-view wx:elif="{{k}}" scroll-y="true" class="scroll-view data-v-59b7fb21" bindscrolltolower="{{p}}"><view class="agent-list data-v-59b7fb21"><view wx:for="{{l}}" wx:for-item="item" wx:key="j" class="agent-item data-v-59b7fb21"><view class="avatar data-v-59b7fb21" bindtap="{{item.b}}"><image src="{{item.a}}" class="avatar-img data-v-59b7fb21" mode="aspectFill"></image></view><view class="content data-v-59b7fb21" bindtap="{{item.f}}"><view class="title data-v-59b7fb21">{{item.c}}</view><view class="description data-v-59b7fb21">{{item.d}}</view><view class="author data-v-59b7fb21">@{{item.e}}</view></view><view class="{{['action-btn', 'data-v-59b7fb21', item.h && 'subscribed']}}" catchtap="{{item.i}}"><text class="btn-text data-v-59b7fb21">{{item.g}}</text></view></view></view><view wx:if="{{m}}" class="load-more data-v-59b7fb21"><text class="load-more-text data-v-59b7fb21">{{n}}</text></view><view wx:elif="{{o}}" class="no-more data-v-59b7fb21"><text class="no-more-text data-v-59b7fb21">没有更多了</text></view></scroll-view><view wx:else class="default-container data-v-59b7fb21"><image src="{{q}}" class="default-icon data-v-59b7fb21" mode="aspectFit"></image><text class="default-text data-v-59b7fb21">输入关键词搜索智能体</text></view></view></view>