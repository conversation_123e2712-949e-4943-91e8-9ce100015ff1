{"name": "jshashes", "description": "A fast and independent hashing library pure JavaScript implemented (ES3 compliant) for both server and client side (MD5, SHA1, SHA256, SHA512, RIPEMD, HMAC and Base64)", "version": "1.0.8", "repository": "h2non/jshashes", "author": "<PERSON> <<EMAIL>>", "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "web": "http://cscott.net"}], "engines": {"node": "*"}, "main": "./hashes.js", "bin": {"hashes": "./bin/hashes"}, "licenses": [{"type": "New BSD", "url": "http://github.com/h2non/jshashes/raw/master/LICENSE"}], "directories": {"test": "test"}, "scripts": {"test": "mocha"}, "devDependencies": {"mocha": "~1.9.0", "uglify-js": "~3.7.6"}, "keywords": ["hash", "md5", "sha1", "sha256", "hashes", "sha512", "RIPEMD", "base64", "hmac", "crc", "encoding", "algorithm"]}