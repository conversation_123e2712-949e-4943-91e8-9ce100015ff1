{"version": 3, "file": "index.js", "sources": ["pages/finance/index.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvZmluYW5jZS9pbmRleC52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"finance-flow-page\">\r\n    <!-- 记录列表 -->\r\n    <view class=\"record-list\">\r\n      <view class=\"record-item\" v-for=\"(item, index) in recordList\" :key=\"index\">\r\n        <view class=\"record-content\">\r\n          <view class=\"main-info\">\r\n            <view class=\"amount-line\">\r\n              <text class=\"amount-label\">变动金额：</text>\r\n              <text class=\"amount\" :class=\"getAmountClass(item.type)\">{{ item.amount }}</text>\r\n            </view>\r\n            <view class=\"reason-line\">\r\n              <text class=\"reason-label\">变动原因：</text>\r\n              <text class=\"reason-text\">{{ item.reason }}</text>\r\n            </view>\r\n            <view class=\"time-line\" v-if=\"item.time\">\r\n              <text class=\"time-label\">{{ item.timeLabel }}：</text>\r\n              <text class=\"time-value\">{{ item.time }}</text>\r\n            </view>\r\n          </view>\r\n          <view class=\"balance-info\">\r\n            <text class=\"balance-text\">变动后余额：{{ item.afterBalance }}</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 空状态 -->\r\n    <view class=\"empty-state\" v-if=\"recordList.length === 0\">\r\n      <image src=\"@/static/common/empty_icon.png\" class=\"empty-icon\" mode=\"aspectFit\" />\r\n      <text class=\"empty-text\">暂无财务流水</text>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref } from 'vue'\r\nimport { onLoad } from '@dcloudio/uni-app'\r\n\r\n// 财务流水列表\r\nconst recordList = ref([\r\n  // {\r\n  //   amount: '+2000.00',\r\n  //   type: 'income', // income: 收入, expense: 支出\r\n  //   reason: '用户订阅购买了您的智能体\"我的修仙女友\"',\r\n  //   timeLabel: '',\r\n  //   time: '',\r\n  //   afterBalance: '2000'\r\n  // },\r\n  // {\r\n  //   amount: '-2000.00',\r\n  //   type: 'expense',\r\n  //   reason: '用户订阅购买了您的智能体\"我的修仙女友\"',\r\n  //   timeLabel: '变动时间',\r\n  //   time: '2025-05-18 12:56:00',\r\n  //   afterBalance: '0'\r\n  // }\r\n])\r\n\r\n// 获取金额样式类\r\nconst getAmountClass = (type) => {\r\n  return type === 'income' ? 'amount-income' : 'amount-expense'\r\n}\r\n\r\n// 加载财务流水列表\r\nconst loadRecordList = async () => {\r\n  try {\r\n    // 这里调用API获取财务流水\r\n    // const res = await getFinanceFlowApi()\r\n    // recordList.value = res.data\r\n    console.log('加载财务流水')\r\n  } catch (error) {\r\n    console.error('加载财务流水失败:', error)\r\n    uni.showToast({\r\n      title: '加载失败',\r\n      icon: 'none'\r\n    })\r\n  }\r\n}\r\n\r\nonLoad(() => {\r\n  loadRecordList()\r\n})\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.finance-flow-page {\r\n  background: #f5f5f5;\r\n  min-height: 100vh;\r\n\r\n  .record-list {\r\n    padding: 32rpx;\r\n\r\n    .record-item {\r\n      background: #ffffff;\r\n      border-radius: 16rpx;\r\n      margin-bottom: 16rpx;\r\n      overflow: hidden;\r\n\r\n      .record-content {\r\n        padding: 40rpx 32rpx;\r\n\r\n        .main-info {\r\n          margin-bottom: 24rpx;\r\n\r\n          .amount-line {\r\n            display: flex;\r\n            align-items: center;\r\n            margin-bottom: 16rpx;\r\n\r\n            .amount-label {\r\n              font-size: 28rpx;\r\n              color: #999999;\r\n              margin-right: 8rpx;\r\n            }\r\n\r\n            .amount {\r\n              font-size: 32rpx;\r\n              font-weight: 600;\r\n\r\n              &.amount-income {\r\n                color: #3478f6;\r\n              }\r\n\r\n              &.amount-expense {\r\n                color: #3478f6;\r\n              }\r\n            }\r\n          }\r\n\r\n          .reason-line {\r\n            display: flex;\r\n            align-items: flex-start;\r\n            margin-bottom: 16rpx;\r\n\r\n            .reason-label {\r\n              font-size: 28rpx;\r\n              color: #999999;\r\n              margin-right: 8rpx;\r\n              flex-shrink: 0;\r\n            }\r\n\r\n            .reason-text {\r\n              font-size: 28rpx;\r\n              color: #333333;\r\n              line-height: 1.4;\r\n              flex: 1;\r\n            }\r\n          }\r\n\r\n          .time-line {\r\n            display: flex;\r\n            align-items: center;\r\n\r\n            .time-label {\r\n              font-size: 28rpx;\r\n              color: #999999;\r\n              margin-right: 8rpx;\r\n            }\r\n\r\n            .time-value {\r\n              font-size: 28rpx;\r\n              color: #999999;\r\n            }\r\n          }\r\n        }\r\n\r\n        .balance-info {\r\n          text-align: right;\r\n\r\n          .balance-text {\r\n            font-size: 28rpx;\r\n            color: #999999;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .empty-state {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    justify-content: center;\r\n    padding: 200rpx 0;\r\n\r\n    .empty-icon {\r\n      width: 200rpx;\r\n      height: 200rpx;\r\n      margin-bottom: 40rpx;\r\n      opacity: 0.6;\r\n    }\r\n\r\n    .empty-text {\r\n      font-size: 28rpx;\r\n      color: #999999;\r\n    }\r\n  }\r\n}\r\n</style>", "import MiniProgramPage from 'E:/youngProject/agent-mini-ui/pages/finance/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "uni", "onLoad"], "mappings": ";;;;;;AAwCA,UAAM,aAAaA,cAAAA,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAiBvB,CAAC;AAGD,UAAM,iBAAiB,CAAC,SAAS;AAC/B,aAAO,SAAS,WAAW,kBAAkB;AAAA,IAC/C;AAGA,UAAM,iBAAiB,YAAY;AACjC,UAAI;AAIFC,sBAAAA,MAAA,MAAA,OAAA,iCAAY,QAAQ;AAAA,MACrB,SAAQ,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,iCAAc,aAAa,KAAK;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACF;AAAA,IACH;AAEAC,kBAAAA,OAAO,MAAM;AACX,qBAAgB;AAAA,IAClB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;ACjFD,GAAG,WAAW,eAAe;"}