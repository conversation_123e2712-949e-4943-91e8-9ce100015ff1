{"version": 3, "file": "l-painter-text.js", "sources": ["uni_modules/lime-painter/components/l-painter-text/l-painter-text.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RToveW91bmdQcm9qZWN0L2FnZW50LW1pbmktdWkvdW5pX21vZHVsZXMvbGltZS1wYWludGVyL2NvbXBvbmVudHMvbC1wYWludGVyLXRleHQvbC1wYWludGVyLXRleHQudnVl"], "sourcesContent": ["<template>\r\n\t<text style=\"opacity: 0;height: 0;\"><slot/></text>\r\n</template>\r\n\r\n<script>\r\n\timport {parent, children} from '../common/relation';\r\n\texport default {\r\n\t\tname: 'lime-painter-text',\r\n\t\tmixins:[children('painter')],\r\n\t\tprops: {\r\n\t\t\ttype: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'text'\r\n\t\t\t},\r\n\t\t\tuid: String,\r\n\t\t\tcss: [String, Object],\r\n\t\t\ttext: [String, Number],\r\n\t\t\treplace: Object,\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\t// type: 'text',\r\n\t\t\t\tel: {\r\n\t\t\t\t\tcss: {},\r\n\t\t\t\t\ttext: null\r\n\t\t\t\t},\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n</style>\r\n", "import Component from 'E:/youngProject/agent-mini-ui/uni_modules/lime-painter/components/l-painter-text/l-painter-text.vue'\nwx.createComponent(Component)"], "names": ["children"], "mappings": ";;;AAMC,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,QAAO,CAACA,4DAAS,SAAS,CAAC;AAAA,EAC3B,OAAO;AAAA,IACN,MAAM;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA,IACD,KAAK;AAAA,IACL,KAAK,CAAC,QAAQ,MAAM;AAAA,IACpB,MAAM,CAAC,QAAQ,MAAM;AAAA,IACrB,SAAS;AAAA,EACT;AAAA,EACD,OAAO;AACN,WAAO;AAAA;AAAA,MAEN,IAAI;AAAA,QACH,KAAK,CAAE;AAAA,QACP,MAAM;AAAA,MACN;AAAA,IACF;AAAA,EACD;AACD;;;;;AC3BD,GAAG,gBAAgB,SAAS;"}