{"version": 3, "file": "test.js", "sources": ["uni_modules/uv-ui-tools/libs/function/test.js"], "sourcesContent": ["/**\r\n * 验证电子邮箱格式\r\n */\r\nfunction email(value) {\r\n    return /^\\w+((-\\w+)|(\\.\\w+))*\\@[A-Za-z0-9]+((\\.|-)[A-Za-z0-9]+)*\\.[A-Za-z0-9]+$/.test(value)\r\n}\r\n\r\n/**\r\n * 验证手机格式\r\n */\r\nfunction mobile(value) {\r\n    return /^1([3589]\\d|4[5-9]|6[1-2,4-7]|7[0-8])\\d{8}$/.test(value)\r\n}\r\n\r\n/**\r\n * 验证URL格式\r\n */\r\nfunction url(value) {\r\n    return /^((https|http|ftp|rtsp|mms):\\/\\/)(([0-9a-zA-Z_!~*'().&=+$%-]+: )?[0-9a-zA-Z_!~*'().&=+$%-]+@)?(([0-9]{1,3}.){3}[0-9]{1,3}|([0-9a-zA-Z_!~*'()-]+.)*([0-9a-zA-Z][0-9a-zA-Z-]{0,61})?[0-9a-zA-Z].[a-zA-Z]{2,6})(:[0-9]{1,4})?((\\/?)|(\\/[0-9a-zA-Z_!~*'().;?:@&=+$,%#-]+)+\\/?)$/\r\n        .test(value)\r\n}\r\n\r\n/**\r\n * 验证日期格式\r\n */\r\nfunction date(value) {\r\n    if (!value) return false\r\n    // 判断是否数值或者字符串数值(意味着为时间戳)，转为数值，否则new Date无法识别字符串时间戳\r\n    if (number(value)) value = +value\r\n    return !/Invalid|NaN/.test(new Date(value).toString())\r\n}\r\n\r\n/**\r\n * 验证ISO类型的日期格式\r\n */\r\nfunction dateISO(value) {\r\n    return /^\\d{4}[\\/\\-](0?[1-9]|1[012])[\\/\\-](0?[1-9]|[12][0-9]|3[01])$/.test(value)\r\n}\r\n\r\n/**\r\n * 验证十进制数字\r\n */\r\nfunction number(value) {\r\n    return /^[\\+-]?(\\d+\\.?\\d*|\\.\\d+|\\d\\.\\d+e\\+\\d+)$/.test(value)\r\n}\r\n\r\n/**\r\n * 验证字符串\r\n */\r\nfunction string(value) {\r\n    return typeof value === 'string'\r\n}\r\n\r\n/**\r\n * 验证整数\r\n */\r\nfunction digits(value) {\r\n    return /^\\d+$/.test(value)\r\n}\r\n\r\n/**\r\n * 验证身份证号码\r\n */\r\nfunction idCard(value) {\r\n    return /^[1-9]\\d{5}[1-9]\\d{3}((0\\d)|(1[0-2]))(([0|1|2]\\d)|3[0-1])\\d{3}([0-9]|X)$/.test(\r\n        value\r\n    )\r\n}\r\n\r\n/**\r\n * 是否车牌号\r\n */\r\nfunction carNo(value) {\r\n    // 新能源车牌\r\n    const xreg = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}(([0-9]{5}[DF]$)|([DF][A-HJ-NP-Z0-9][0-9]{4}$))/\r\n    // 旧车牌\r\n    const creg = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳]{1}$/\r\n    if (value.length === 7) {\r\n        return creg.test(value)\r\n    } if (value.length === 8) {\r\n        return xreg.test(value)\r\n    }\r\n    return false\r\n}\r\n\r\n/**\r\n * 金额,只允许2位小数\r\n */\r\nfunction amount(value) {\r\n    // 金额，只允许保留两位小数\r\n    return /^[1-9]\\d*(,\\d{3})*(\\.\\d{1,2})?$|^0\\.\\d{1,2}$/.test(value)\r\n}\r\n\r\n/**\r\n * 中文\r\n */\r\nfunction chinese(value) {\r\n    const reg = /^[\\u4e00-\\u9fa5]+$/gi\r\n    return reg.test(value)\r\n}\r\n\r\n/**\r\n * 只能输入字母\r\n */\r\nfunction letter(value) {\r\n    return /^[a-zA-Z]*$/.test(value)\r\n}\r\n\r\n/**\r\n * 只能是字母或者数字\r\n */\r\nfunction enOrNum(value) {\r\n    // 英文或者数字\r\n    const reg = /^[0-9a-zA-Z]*$/g\r\n    return reg.test(value)\r\n}\r\n\r\n/**\r\n * 验证是否包含某个值\r\n */\r\nfunction contains(value, param) {\r\n    return value.indexOf(param) >= 0\r\n}\r\n\r\n/**\r\n * 验证一个值范围[min, max]\r\n */\r\nfunction range(value, param) {\r\n    return value >= param[0] && value <= param[1]\r\n}\r\n\r\n/**\r\n * 验证一个长度范围[min, max]\r\n */\r\nfunction rangeLength(value, param) {\r\n    return value.length >= param[0] && value.length <= param[1]\r\n}\r\n\r\n/**\r\n * 是否固定电话\r\n */\r\nfunction landline(value) {\r\n    const reg = /^\\d{3,4}-\\d{7,8}(-\\d{3,4})?$/\r\n    return reg.test(value)\r\n}\r\n\r\n/**\r\n * 判断是否为空\r\n */\r\nfunction empty(value) {\r\n    switch (typeof value) {\r\n    case 'undefined':\r\n        return true\r\n    case 'string':\r\n        if (value.replace(/(^[ \\t\\n\\r]*)|([ \\t\\n\\r]*$)/g, '').length == 0) return true\r\n        break\r\n    case 'boolean':\r\n        if (!value) return true\r\n        break\r\n    case 'number':\r\n        if (value === 0 || isNaN(value)) return true\r\n        break\r\n    case 'object':\r\n        if (value === null || value.length === 0) return true\r\n        for (const i in value) {\r\n            return false\r\n        }\r\n        return true\r\n    }\r\n    return false\r\n}\r\n\r\n/**\r\n * 是否json字符串\r\n */\r\nfunction jsonString(value) {\r\n    if (typeof value === 'string') {\r\n        try {\r\n            const obj = JSON.parse(value)\r\n            if (typeof obj === 'object' && obj) {\r\n                return true\r\n            }\r\n            return false\r\n        } catch (e) {\r\n            return false\r\n        }\r\n    }\r\n    return false\r\n}\r\n\r\n/**\r\n * 是否数组\r\n */\r\nfunction array(value) {\r\n    if (typeof Array.isArray === 'function') {\r\n        return Array.isArray(value)\r\n    }\r\n    return Object.prototype.toString.call(value) === '[object Array]'\r\n}\r\n\r\n/**\r\n * 是否对象\r\n */\r\nfunction object(value) {\r\n    return Object.prototype.toString.call(value) === '[object Object]'\r\n}\r\n\r\n/**\r\n * 是否短信验证码\r\n */\r\nfunction code(value, len = 6) {\r\n    return new RegExp(`^\\\\d{${len}}$`).test(value)\r\n}\r\n\r\n/**\r\n * 是否函数方法\r\n * @param {Object} value\r\n */\r\nfunction func(value) {\r\n    return typeof value === 'function'\r\n}\r\n\r\n/**\r\n * 是否promise对象\r\n * @param {Object} value\r\n */\r\nfunction promise(value) {\r\n    return object(value) && func(value.then) && func(value.catch)\r\n}\r\n\r\n/** 是否图片格式\r\n * @param {Object} value\r\n */\r\nfunction image(value) {\r\n    const newValue = value.split('?')[0]\r\n    const IMAGE_REGEXP = /\\.(jpeg|jpg|gif|png|svg|webp|jfif|bmp|dpg)/i\r\n    return IMAGE_REGEXP.test(newValue)\r\n}\r\n\r\n/**\r\n * 是否视频格式\r\n * @param {Object} value\r\n */\r\nfunction video(value) {\r\n    const VIDEO_REGEXP = /\\.(mp4|mpg|mpeg|dat|asf|avi|rm|rmvb|mov|wmv|flv|mkv|m3u8)/i\r\n    return VIDEO_REGEXP.test(value)\r\n}\r\n\r\n/**\r\n * 是否为正则对象\r\n * @param {Object}\r\n * @return {Boolean}\r\n */\r\nfunction regExp(o) {\r\n    return o && Object.prototype.toString.call(o) === '[object RegExp]'\r\n}\r\n\r\nexport {\r\n    email,\r\n    mobile,\r\n    url,\r\n    date,\r\n    dateISO,\r\n    number,\r\n    digits,\r\n    idCard,\r\n    carNo,\r\n    amount,\r\n    chinese,\r\n    letter,\r\n    enOrNum,\r\n    contains,\r\n    range,\r\n    rangeLength,\r\n    empty,\r\n    jsonString,\r\n    landline,\r\n    object,\r\n    array,\r\n    code,\r\n    func,\r\n    promise,\r\n    video,\r\n    image,\r\n    regExp,\r\n    string\r\n}\r\n"], "names": [], "mappings": ";AAGA,SAAS,MAAM,OAAO;AAClB,SAAO,0EAA0E,KAAK,KAAK;AAC/F;AAKA,SAAS,OAAO,OAAO;AACnB,SAAO,8CAA8C,KAAK,KAAK;AACnE;AAKA,SAAS,IAAI,OAAO;AAChB,SAAO,8QACF,KAAK,KAAK;AACnB;AAKA,SAAS,KAAK,OAAO;AACjB,MAAI,CAAC;AAAO,WAAO;AAEnB,MAAI,OAAO,KAAK;AAAG,YAAQ,CAAC;AAC5B,SAAO,CAAC,cAAc,KAAK,IAAI,KAAK,KAAK,EAAE,UAAU;AACzD;AAKA,SAAS,QAAQ,OAAO;AACpB,SAAO,+DAA+D,KAAK,KAAK;AACpF;AAKA,SAAS,OAAO,OAAO;AACnB,SAAO,0CAA0C,KAAK,KAAK;AAC/D;AAKA,SAAS,OAAO,OAAO;AACnB,SAAO,OAAO,UAAU;AAC5B;AAKA,SAAS,OAAO,OAAO;AACnB,SAAO,QAAQ,KAAK,KAAK;AAC7B;AAKA,SAAS,OAAO,OAAO;AACnB,SAAO,2EAA2E;AAAA,IAC9E;AAAA,EACH;AACL;AAKA,SAAS,MAAM,OAAO;AAElB,QAAM,OAAO;AAEb,QAAM,OAAO;AACb,MAAI,MAAM,WAAW,GAAG;AACpB,WAAO,KAAK,KAAK,KAAK;AAAA,EAC9B;AAAM,MAAI,MAAM,WAAW,GAAG;AACtB,WAAO,KAAK,KAAK,KAAK;AAAA,EACzB;AACD,SAAO;AACX;AAKA,SAAS,OAAO,OAAO;AAEnB,SAAO,+CAA+C,KAAK,KAAK;AACpE;AAKA,SAAS,QAAQ,OAAO;AACpB,QAAM,MAAM;AACZ,SAAO,IAAI,KAAK,KAAK;AACzB;AAKA,SAAS,OAAO,OAAO;AACnB,SAAO,cAAc,KAAK,KAAK;AACnC;AAKA,SAAS,QAAQ,OAAO;AAEpB,QAAM,MAAM;AACZ,SAAO,IAAI,KAAK,KAAK;AACzB;AAKA,SAAS,SAAS,OAAO,OAAO;AAC5B,SAAO,MAAM,QAAQ,KAAK,KAAK;AACnC;AAKA,SAAS,MAAM,OAAO,OAAO;AACzB,SAAO,SAAS,MAAM,CAAC,KAAK,SAAS,MAAM,CAAC;AAChD;AAKA,SAAS,YAAY,OAAO,OAAO;AAC/B,SAAO,MAAM,UAAU,MAAM,CAAC,KAAK,MAAM,UAAU,MAAM,CAAC;AAC9D;AAKA,SAAS,SAAS,OAAO;AACrB,QAAM,MAAM;AACZ,SAAO,IAAI,KAAK,KAAK;AACzB;AAKA,SAAS,MAAM,OAAO;AAClB,UAAQ,OAAO,OAAK;AAAA,IACpB,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,UAAI,MAAM,QAAQ,gCAAgC,EAAE,EAAE,UAAU;AAAG,eAAO;AAC1E;AAAA,IACJ,KAAK;AACD,UAAI,CAAC;AAAO,eAAO;AACnB;AAAA,IACJ,KAAK;AACD,UAAI,UAAU,KAAK,MAAM,KAAK;AAAG,eAAO;AACxC;AAAA,IACJ,KAAK;AACD,UAAI,UAAU,QAAQ,MAAM,WAAW;AAAG,eAAO;AACjD,iBAAW,KAAK,OAAO;AACnB,eAAO;AAAA,MACV;AACD,aAAO;AAAA,EACV;AACD,SAAO;AACX;AAKA,SAAS,WAAW,OAAO;AACvB,MAAI,OAAO,UAAU,UAAU;AAC3B,QAAI;AACA,YAAM,MAAM,KAAK,MAAM,KAAK;AAC5B,UAAI,OAAO,QAAQ,YAAY,KAAK;AAChC,eAAO;AAAA,MACV;AACD,aAAO;AAAA,IACV,SAAQ,GAAG;AACR,aAAO;AAAA,IACV;AAAA,EACJ;AACD,SAAO;AACX;AAKA,SAAS,MAAM,OAAO;AAClB,MAAI,OAAO,MAAM,YAAY,YAAY;AACrC,WAAO,MAAM,QAAQ,KAAK;AAAA,EAC7B;AACD,SAAO,OAAO,UAAU,SAAS,KAAK,KAAK,MAAM;AACrD;AAKA,SAAS,OAAO,OAAO;AACnB,SAAO,OAAO,UAAU,SAAS,KAAK,KAAK,MAAM;AACrD;AAKA,SAAS,KAAK,OAAO,MAAM,GAAG;AAC1B,SAAO,IAAI,OAAO,QAAQ,GAAG,IAAI,EAAE,KAAK,KAAK;AACjD;AAMA,SAAS,KAAK,OAAO;AACjB,SAAO,OAAO,UAAU;AAC5B;AAMA,SAAS,QAAQ,OAAO;AACpB,SAAO,OAAO,KAAK,KAAK,KAAK,MAAM,IAAI,KAAK,KAAK,MAAM,KAAK;AAChE;AAKA,SAAS,MAAM,OAAO;AAClB,QAAM,WAAW,MAAM,MAAM,GAAG,EAAE,CAAC;AACnC,QAAM,eAAe;AACrB,SAAO,aAAa,KAAK,QAAQ;AACrC;AAMA,SAAS,MAAM,OAAO;AAClB,QAAM,eAAe;AACrB,SAAO,aAAa,KAAK,KAAK;AAClC;AAOA,SAAS,OAAO,GAAG;AACf,SAAO,KAAK,OAAO,UAAU,SAAS,KAAK,CAAC,MAAM;AACtD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}