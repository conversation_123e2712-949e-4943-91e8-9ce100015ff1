{"version": 3, "file": "utils.js", "sources": ["utils/utils.js"], "sourcesContent": ["/**\r\n * 防抖\r\n */\r\n// export function debounce(fn, delay) {\r\n//   let timeoutId = null;\r\n//   return function(...args) {\r\n//     clearTimeout(timeoutId);\r\n//     timeoutId = setTimeout(() => {\r\n//       fn.apply(this, args);\r\n//     }, delay);\r\n//   };\r\n// }\r\n//防抖\r\nexport function debounce(fn, wait) {\r\n\tlet timeout = null;\r\n\treturn function() {\r\n\t\tlet context = this;\r\n\t\tlet args = arguments;\r\n\t\tif (timeout) clearTimeout(timeout);\r\n\t\tlet callNow = !timeout;\r\n\t\ttimeout = setTimeout(() => {\r\n\t\t\ttimeout = null;\r\n\t\t}, wait);\r\n\t\tif (callNow) fn.apply(context, args);\r\n\t};\r\n}\r\n//节流\r\nexport function throttle(fn, wait) {\r\n\tlet previous = 0;\r\n\treturn function() {\r\n\t\tlet context = this;\r\n\t\tlet args = arguments;\r\n\t\tlet now = new Date();\r\n\t\tif (now - previous > wait) {\r\n\t\t\tfn.apply(context, args);\r\n\t\t\tprevious = now;\r\n\t\t}\r\n\t};\r\n}"], "names": [], "mappings": ";AA2BO,SAAS,SAAS,IAAI,MAAM;AAClC,MAAI,WAAW;AACf,SAAO,WAAW;AACjB,QAAI,UAAU;AACd,QAAI,OAAO;AACX,QAAI,MAAM,oBAAI;AACd,QAAI,MAAM,WAAW,MAAM;AAC1B,SAAG,MAAM,SAAS,IAAI;AACtB,iBAAW;AAAA,IACX;AAAA,EACH;AACA;;"}