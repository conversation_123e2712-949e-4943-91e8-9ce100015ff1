/**
 * 防抖
 */
// export function debounce(fn, delay) {
//   let timeoutId = null;
//   return function(...args) {
//     clearTimeout(timeoutId);
//     timeoutId = setTimeout(() => {
//       fn.apply(this, args);
//     }, delay);
//   };
// }
//防抖
export function debounce(fn, wait) {
	let timeout = null;
	return function() {
		let context = this;
		let args = arguments;
		if (timeout) clearTimeout(timeout);
		let callNow = !timeout;
		timeout = setTimeout(() => {
			timeout = null;
		}, wait);
		if (callNow) fn.apply(context, args);
	};
}
//节流
export function throttle(fn, wait) {
	let previous = 0;
	return function() {
		let context = this;
		let args = arguments;
		let now = new Date();
		if (now - previous > wait) {
			fn.apply(context, args);
			previous = now;
		}
	};
}