"use strict";
const common_vendor = require("../../common/vendor.js");
const api_index = require("../../api/index.js");
const stores_user = require("../../stores/user.js");
const ruleTitleBg1 = "https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/6886986325964ded9f4e231bd0c0e6ad.png";
const ruleBg1 = "https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/134edc12f1494bb6b1c23098140f5610.png";
const _sfc_main = {
  __name: "cz-index",
  setup(__props) {
    const userStore = stores_user.useUserStore();
    const handleGoCreate = () => {
      common_vendor.index.navigateTo({
        url: "/pages/create-agent/index"
      });
    };
    let htmlContent = common_vendor.ref("");
    const platformRules = async () => {
      let res = await api_index.platformRulesApi({
        merchantGuid: userStore.merchantGuid
      });
      htmlContent.value = res.data.creationRules.content;
    };
    platformRules();
    return (_ctx, _cache) => {
      return {
        a: ruleBg1,
        b: ruleTitleBg1,
        c: common_vendor.unref(htmlContent),
        d: common_vendor.o(handleGoCreate)
      };
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-96b5e223"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/rule/cz-index.js.map
