<wxs src="./wxs/z-paging-wxs.wxs" module="pagingWxs"/>
<view class="{{['data-v-f8f0d035', 'z-paging-content', bA && 'z-paging-content-full', bB && 'z-paging-content-fixed', bC && 'z-paging-content-page', bD && 'z-paging-reached-top', bE && 'z-paging-use-chat-record-mode']}}" style="{{bF}}"><view wx:if="{{a}}" class="zp-safe-area-inset-bottom data-v-f8f0d035"></view><view wx:if="{{b}}" catchtouchmove="{{c}}" class="zp-f2-content data-v-f8f0d035" style="{{d}}"><slot name="f2"/></view><slot wx:if="{{e}}" name="top"/><view wx:elif="{{f}}" class="zp-page-top data-v-f8f0d035" catchtouchmove="{{g}}" style="{{h}}"><slot name="top"/></view><view class="{{['data-v-f8f0d035', 'zp-view-super', bi && 'zp-scroll-view-super']}}" style="{{bj}}"><view wx:if="{{i}}" class="{{['data-v-f8f0d035', 'zp-page-left', j && 'zp-absoulte']}}"><slot name="left"/></view><view class="{{['data-v-f8f0d035', 'zp-scroll-view-container', be && 'zp-absoulte']}}" style="{{bf}}"><scroll-view ref="zp-scroll-view" class="{{['data-v-f8f0d035', 'zp-scroll-view', aJ && 'zp-scroll-view-absolute', aK && 'zp-scroll-view-hide-scrollbar']}}" style="{{aL}}" scroll-top="{{aM}}" scroll-x="{{aN}}" scroll-y="{{aO}}" enable-back-to-top="{{aP}}" show-scrollbar="{{aQ}}" scroll-with-animation="{{aR}}" scroll-into-view="{{aS}}" lower-threshold="{{aT}}" upper-threshold="{{5}}" refresher-enabled="{{aU}}" refresher-threshold="{{aV}}" refresher-default-style="{{aW}}" refresher-background="{{aX}}" refresher-triggered="{{aY}}" bindscroll="{{aZ}}" bindscrolltolower="{{ba}}" bindscrolltoupper="{{bb}}" bindrefresherrestore="{{bc}}" bindrefresherrefresh="{{bd}}"><view class="zp-paging-touch-view data-v-f8f0d035" bindtouchstart="{{pagingWxs.touchstart}}" bindtouchmove="{{pagingWxs.touchmove}}" bindtouchend="{{pagingWxs.touchend}}" bindtouchcancel="{{pagingWxs.touchend}}" bindmousedown="{{pagingWxs.mousedown}}" bindmousemove="{{pagingWxs.mousemove}}" bindmouseup="{{pagingWxs.mouseup}}" bindmouseleave="{{pagingWxs.mouseleave}}"><view wx:if="{{k}}" class="zp-fixed-bac-view data-v-f8f0d035" style="{{l}}"></view><view class="zp-paging-main data-v-f8f0d035" style="{{am + ';' + an}}" change:prop="{{pagingWxs.propObserver}}" prop="{{ao}}" data-refresherThreshold="{{ap}}" data-refresherF2Enabled="{{aq}}" data-refresherF2Threshold="{{ar}}" data-isIos="{{as}}" data-loading="{{at}}" data-useChatRecordMode="{{av}}" data-refresherEnabled="{{aw}}" data-useCustomRefresher="{{ax}}" data-pageScrollTop="{{ay}}" data-scrollTop="{{az}}" data-refresherMaxAngle="{{aA}}" data-refresherNoTransform="{{aB}}" data-refresherAecc="{{aC}}" data-usePageScroll="{{aD}}" data-watchTouchDirectionChange="{{aE}}" data-oldIsTouchmoving="{{aF}}" data-refresherOutRate="{{aG}}" data-refresherPullRate="{{aH}}" data-hasTouchmove="{{aI}}"><view wx:if="{{m}}" class="zp-custom-refresher-view data-v-f8f0d035" style="{{z}}"><view class="zp-custom-refresher-container data-v-f8f0d035" style="{{y}}"><view wx:if="{{n}}" class="zp-custom-refresher-status-bar-placeholder data-v-f8f0d035" style="{{o}}"/><view class="zp-custom-refresher-slot-view data-v-f8f0d035"><slot wx:if="{{p}}" name="refresher"/></view><slot wx:if="{{r}}" name="refresherComplete"/><slot wx:elif="{{s}}" name="refresherF2"/><z-paging-refresh wx:elif="{{t}}" u-r="refresh" class="zp-custom-refresher-refresh r data-v-f8f0d035" style="{{w}}" u-i="f8f0d035-0" bind:__l="__l" u-p="{{x}}"/></view></view><view class="zp-paging-container data-v-f8f0d035" style="{{al}}"><slot wx:if="{{A}}" name="loading"/><view class="zp-paging-container-content data-v-f8f0d035" style="{{aa + ';' + ab}}"><slot/><block wx:if="{{B}}"><slot name="header"/><view class="zp-list-container data-v-f8f0d035" style="{{H}}"><block wx:if="{{C}}"><view wx:for="{{D}}" wx:for-item="item" wx:key="d" class="zp-list-cell data-v-f8f0d035" style="{{F}}" id="{{item.c}}" bindtap="{{item.e}}"><view wx:if="{{E}}" class="data-v-f8f0d035">使用兼容模式请在组件源码z-paging.vue第99行中注释这一行，并打开下面一行注释</view><slot wx:else name="{{item.a}}"/></view></block><block wx:else><view wx:for="{{G}}" wx:for-item="item" wx:key="c" class="zp-list-cell data-v-f8f0d035" bindtap="{{item.d}}"><slot name="{{item.a}}"/></view></block></view><slot name="footer"/></block><block wx:if="{{I}}"><view class="data-v-f8f0d035" style="{{O}}"><slot wx:if="{{J}}" name="chatNoMore"/><block wx:else><slot wx:if="{{K}}" name="chatLoading"/><z-paging-load-more wx:else class="data-v-f8f0d035" binddoClick="{{M}}" u-i="f8f0d035-1" bind:__l="__l" u-p="{{N||''}}"/></block></view></block><view wx:if="{{P}}" class="zp-virtual-placeholder data-v-f8f0d035" style="{{Q}}"/><slot wx:if="{{R}}" name="loadingMoreDefault"/><slot wx:elif="{{S}}" name="loadingMoreLoading"/><slot wx:elif="{{T}}" name="loadingMoreNoMore"/><slot wx:elif="{{U}}" name="loadingMoreFail"/><z-paging-load-more wx:elif="{{V}}" class="data-v-f8f0d035" binddoClick="{{W}}" u-i="f8f0d035-2" bind:__l="__l" u-p="{{X}}"/><view wx:if="{{Y}}" class="zp-safe-area-placeholder data-v-f8f0d035" style="{{Z}}"/></view><view wx:if="{{ac}}" class="{{['data-v-f8f0d035', 'zp-empty-view', ai && 'zp-empty-view-center']}}" style="{{aj + ';' + ak}}"><slot wx:if="{{ad}}" name="empty"/><z-paging-empty-view wx:else class="data-v-f8f0d035" bindreload="{{af}}" bindviewClick="{{ag}}" u-i="f8f0d035-3" bind:__l="__l" u-p="{{ah||''}}"/></view></view></view></view></scroll-view></view><view wx:if="{{bg}}" class="{{['data-v-f8f0d035', 'zp-page-right', bh && 'zp-absoulte zp-right']}}"><slot name="right"/></view></view><view class="zp-page-bottom-container data-v-f8f0d035" style="{{'background:' + br}}"><slot wx:if="{{bk}}" name="bottom"/><view wx:elif="{{bl}}" class="zp-page-bottom data-v-f8f0d035" catchtouchmove="{{bm}}" style="{{bn}}"><slot name="bottom"/></view><block wx:if="{{bo}}"><view class="data-v-f8f0d035" style="{{bp}}"/><view class="zp-page-bottom-keyboard-placeholder-animate data-v-f8f0d035" style="{{bq}}"/></block></view><view wx:if="{{bs}}" class="{{['data-v-f8f0d035', bw]}}" style="{{bx}}" catchtap="{{by}}"><slot wx:if="{{bt}}" name="backToTop"/><image wx:else class="zp-back-to-top-img data-v-f8f0d035" src="{{bv}}"/></view><view wx:if="{{bz}}" class="zp-loading-fixed data-v-f8f0d035"><slot name="loading"/></view></view>