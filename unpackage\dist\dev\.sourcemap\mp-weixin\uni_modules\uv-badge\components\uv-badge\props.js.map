{"version": 3, "file": "props.js", "sources": ["uni_modules/uv-badge/components/uv-badge/props.js"], "sourcesContent": ["export default {\r\n\tprops: {\r\n\t\t// 是否显示圆点\r\n\t\tisDot: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t},\r\n\t\t// 显示的内容\r\n\t\tvalue: {\r\n\t\t\ttype: [Number, String],\r\n\t\t\tdefault: ''\r\n\t\t},\r\n\t\t// 是否显示\r\n\t\tshow: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: true\r\n\t\t},\r\n\t\t// 最大值，超过最大值会显示 '{max}+'\r\n\t\tmax: {\r\n\t\t\ttype: [Number, String],\r\n\t\t\tdefault: 999\r\n\t\t},\r\n\t\t// 主题类型，error|warning|success|primary\r\n\t\ttype: {\r\n\t\t\ttype: [String,undefined,null],\r\n\t\t\tdefault: 'error'\r\n\t\t},\r\n\t\t// 当数值为 0 时，是否展示 Badge\r\n\t\tshowZero: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t},\r\n\t\t// 背景颜色，优先级比type高，如设置，type参数会失效\r\n\t\tbgColor: {\r\n\t\t\ttype: [String, null],\r\n\t\t\tdefault: null\r\n\t\t},\r\n\t\t// 字体颜色\r\n\t\tcolor: {\r\n\t\t\ttype: [String, null],\r\n\t\t\tdefault: null\r\n\t\t},\r\n\t\t// 徽标形状，circle-四角均为圆角，horn-左下角为直角\r\n\t\tshape: {\r\n\t\t\ttype: [String,undefined,null],\r\n\t\t\tdefault: 'circle'\r\n\t\t},\r\n\t\t// 设置数字的显示方式，overflow|ellipsis|limit\r\n\t\t// overflow会根据max字段判断，超出显示`${max}+`\r\n\t\t// ellipsis会根据max判断，超出显示`${max}...`\r\n\t\t// limit会依据1000作为判断条件，超出1000，显示`${value/1000}K`，比如2.2k、3.34w，最多保留2位小数\r\n\t\tnumberType: {\r\n\t\t\ttype: [String,undefined,null],\r\n\t\t\tdefault: 'overflow'\r\n\t\t},\r\n\t\t// 设置badge的位置偏移，格式为 [x, y]，也即设置的为top和right的值，absolute为true时有效\r\n\t\toffset: {\r\n\t\t\ttype: Array,\r\n\t\t\tdefault: () => []\r\n\t\t},\r\n\t\t// 是否反转背景和字体颜色\r\n\t\tinverted: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t},\r\n\t\t// 是否绝对定位\r\n\t\tabsolute: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t},\r\n\t\t...uni.$uv?.props?.badge\r\n\t}\r\n}"], "names": ["uni"], "mappings": ";;;AAAA,MAAe,QAAA;AAAA,EACd,OAAO;AAAA;AAAA,IAEN,OAAO;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA;AAAA,IAED,OAAO;AAAA,MACN,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS;AAAA,IACT;AAAA;AAAA,IAED,MAAM;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA;AAAA,IAED,KAAK;AAAA,MACJ,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS;AAAA,IACT;AAAA;AAAA,IAED,MAAM;AAAA,MACL,MAAM,CAAC,QAAO,QAAU,IAAI;AAAA,MAC5B,SAAS;AAAA,IACT;AAAA;AAAA,IAED,UAAU;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA;AAAA,IAED,SAAS;AAAA,MACR,MAAM,CAAC,QAAQ,IAAI;AAAA,MACnB,SAAS;AAAA,IACT;AAAA;AAAA,IAED,OAAO;AAAA,MACN,MAAM,CAAC,QAAQ,IAAI;AAAA,MACnB,SAAS;AAAA,IACT;AAAA;AAAA,IAED,OAAO;AAAA,MACN,MAAM,CAAC,QAAO,QAAU,IAAI;AAAA,MAC5B,SAAS;AAAA,IACT;AAAA;AAAA;AAAA;AAAA;AAAA,IAKD,YAAY;AAAA,MACX,MAAM,CAAC,QAAO,QAAU,IAAI;AAAA,MAC5B,SAAS;AAAA,IACT;AAAA;AAAA,IAED,QAAQ;AAAA,MACP,MAAM;AAAA,MACN,SAAS,MAAM,CAAE;AAAA,IACjB;AAAA;AAAA,IAED,UAAU;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA;AAAA,IAED,UAAU;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA,IACD,IAAGA,yBAAG,MAAC,QAAJA,mBAAS,UAATA,mBAAgB;AAAA,EACnB;AACF;;"}