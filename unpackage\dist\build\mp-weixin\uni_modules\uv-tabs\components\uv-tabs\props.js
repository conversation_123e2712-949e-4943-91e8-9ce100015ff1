"use strict";var e,t;const r=require("../../../../common/vendor.js"),l={props:{duration:{type:Number,default:300},list:{type:Array,default:()=>[]},lineColor:{type:String,default:"#3c9cff"},activeStyle:{type:[String,Object],default:()=>({color:"#303133"})},inactiveStyle:{type:[String,Object],default:()=>({color:"#606266"})},lineWidth:{type:[String,Number],default:20},lineHeight:{type:[String,Number],default:3},lineBgSize:{type:String,default:"cover"},itemStyle:{type:[String,Object],default:()=>({height:"44px"})},scrollable:{type:Boolean,default:!0},current:{type:[Number,String],default:0},keyName:{type:String,default:"name"},...null==(t=null==(e=r.index.$uv)?void 0:e.props)?void 0:t.tabs}};exports.props=l;
