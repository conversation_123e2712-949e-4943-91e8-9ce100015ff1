{"version": 3, "file": "mpother.js", "sources": ["uni_modules/uni-swipe-action/components/uni-swipe-action-item/mpother.js"], "sourcesContent": ["let otherMixins = {}\r\n\r\n// #ifndef APP-PLUS|| MP-WEIXIN  ||  H5\r\nconst MIN_DISTANCE = 10;\r\notherMixins = {\r\n\tdata() {\r\n\t\t// TODO 随机生生元素ID，解决百度小程序获取同一个元素位置信息的bug\r\n\t\tconst elClass = `Uni_${Math.ceil(Math.random() * 10e5).toString(36)}`\r\n\t\treturn {\r\n\t\t\tuniShow: false,\r\n\t\t\tleft: 0,\r\n\t\t\tbuttonShow: 'none',\r\n\t\t\tani: false,\r\n\t\t\tmoveLeft: '',\r\n\t\t\telClass\r\n\t\t}\r\n\t},\r\n\twatch: {\r\n\t\tshow(newVal) {\r\n\t\t\tif (this.autoClose) return\r\n\t\t\tthis.openState(newVal)\r\n\t\t},\r\n\t\tleft() {\r\n\t\t\tthis.moveLeft = `translateX(${this.left}px)`\r\n\t\t},\r\n\t\tbuttonShow(newVal) {\r\n\t\t\tif (this.autoClose) return\r\n\t\t\tthis.openState(newVal)\r\n\t\t},\r\n\t\tleftOptions() {\r\n\t\t\tthis.init()\r\n\t\t},\r\n\t\trightOptions() {\r\n\t\t\tthis.init()\r\n\t\t}\r\n\t},\r\n\tmounted() {\r\n\t\tthis.swipeaction = this.getSwipeAction()\r\n\t\tif (this.swipeaction && Array.isArray(this.swipeaction.children)) {\r\n\t\t\tthis.swipeaction.children.push(this)\r\n\t\t}\r\n\t\tthis.init()\r\n\t},\r\n\tmethods: {\r\n\t\tinit() {\r\n\t\t\tclearTimeout(this.timer)\r\n\t\t\tthis.timer = setTimeout(() => {\r\n\t\t\t\tthis.getSelectorQuery()\r\n\t\t\t}, 100)\r\n\t\t\t// 移动距离\r\n\t\t\tthis.left = 0\r\n\t\t\tthis.x = 0\r\n\t\t},\r\n\r\n\t\tcloseSwipe(e) {\r\n\t\t\tif (this.autoClose && this.swipeaction) {\r\n\t\t\t\tthis.swipeaction.closeOther(this)\r\n\t\t\t}\r\n\t\t},\r\n\t\tappTouchStart(e) {\r\n\t\t\tconst {\r\n\t\t\t\tclientX\r\n\t\t\t} = e.changedTouches[0]\r\n\t\t\tthis.clientX = clientX\r\n\t\t\tthis.timestamp = new Date().getTime()\r\n\t\t},\r\n\t\tappTouchEnd(e, index, item, position) {\r\n\t\t\tconst {\r\n\t\t\t\tclientX\r\n\t\t\t} = e.changedTouches[0]\r\n\t\t\t// fixed by xxxx 模拟点击事件，解决 ios 13 点击区域错位的问题\r\n\t\t\tlet diff = Math.abs(this.clientX - clientX)\r\n\t\t\tlet time = (new Date().getTime()) - this.timestamp\r\n\t\t\tif (diff < 40 && time < 300) {\r\n\t\t\t\tthis.$emit('click', {\r\n\t\t\t\t\tcontent: item,\r\n\t\t\t\t\tindex,\r\n\t\t\t\t\tposition\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\t\ttouchstart(e) {\r\n\t\t\tif (this.disabled) return\r\n\t\t\tthis.ani = false\r\n\t\t\tthis.x = this.left || 0\r\n\t\t\tthis.stopTouchStart(e)\r\n\t\t\tthis.autoClose && this.closeSwipe()\r\n\t\t},\r\n\t\ttouchmove(e) {\r\n\t\t\tif (this.disabled) return\r\n\t\t\t// 是否可以滑动页面\r\n\t\t\tthis.stopTouchMove(e);\r\n\t\t\tif (this.direction !== 'horizontal') {\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\tthis.move(this.x + this.deltaX)\r\n\t\t\treturn false\r\n\t\t},\r\n\t\ttouchend() {\r\n\t\t\tif (this.disabled) return\r\n\t\t\tthis.moveDirection(this.left)\r\n\t\t},\r\n\t\t/**\r\n\t\t * 设置移动距离\r\n\t\t * @param {Object} value\r\n\t\t */\r\n\t\tmove(value) {\r\n\t\t\tvalue = value || 0\r\n\t\t\tconst leftWidth = this.leftWidth\r\n\t\t\tconst rightWidth = this.rightWidth\r\n\t\t\t// 获取可滑动范围\r\n\t\t\tthis.left = this.range(value, -rightWidth, leftWidth);\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t * 获取范围\r\n\t\t * @param {Object} num\r\n\t\t * @param {Object} min\r\n\t\t * @param {Object} max\r\n\t\t */\r\n\t\trange(num, min, max) {\r\n\t\t\treturn Math.min(Math.max(num, min), max);\r\n\t\t},\r\n\t\t/**\r\n\t\t * 移动方向判断\r\n\t\t * @param {Object} left\r\n\t\t * @param {Object} value\r\n\t\t */\r\n\t\tmoveDirection(left) {\r\n\t\t\tconst threshold = this.threshold\r\n\t\t\tconst isopen = this.isopen || 'none'\r\n\t\t\tconst leftWidth = this.leftWidth\r\n\t\t\tconst rightWidth = this.rightWidth\r\n\t\t\tif (this.deltaX === 0) {\r\n\t\t\t\tthis.openState('none')\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\t\t\tif ((isopen === 'none' && rightWidth > 0 && -left > threshold) || (isopen !== 'none' && rightWidth >\r\n\t\t\t\t\t0 && rightWidth +\r\n\t\t\t\t\tleft < threshold)) {\r\n\t\t\t\t// right\r\n\t\t\t\tthis.openState('right')\r\n\t\t\t} else if ((isopen === 'none' && leftWidth > 0 && left > threshold) || (isopen !== 'none' && leftWidth >\r\n\t\t\t\t\t0 &&\r\n\t\t\t\t\tleftWidth - left < threshold)) {\r\n\t\t\t\t// left\r\n\t\t\t\tthis.openState('left')\r\n\t\t\t} else {\r\n\t\t\t\t// default\r\n\t\t\t\tthis.openState('none')\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t * 开启状态\r\n\t\t * @param {Boolean} type\r\n\t\t */\r\n\t\topenState(type) {\r\n\t\t\tconst leftWidth = this.leftWidth\r\n\t\t\tconst rightWidth = this.rightWidth\r\n\t\t\tlet left = ''\r\n\t\t\tthis.isopen = this.isopen ? this.isopen : 'none'\r\n\t\t\tswitch (type) {\r\n\t\t\t\tcase \"left\":\r\n\t\t\t\t\tleft = leftWidth\r\n\t\t\t\t\tbreak\r\n\t\t\t\tcase \"right\":\r\n\t\t\t\t\tleft = -rightWidth\r\n\t\t\t\t\tbreak\r\n\t\t\t\tdefault:\r\n\t\t\t\t\tleft = 0\r\n\t\t\t}\r\n\r\n\r\n\t\t\tif (this.isopen !== type) {\r\n\t\t\t\tthis.throttle = true\r\n\t\t\t\tthis.$emit('change', type)\r\n\t\t\t}\r\n\r\n\t\t\tthis.isopen = type\r\n\t\t\t// 添加动画类\r\n\t\t\tthis.ani = true\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\tthis.move(left)\r\n\t\t\t})\r\n\t\t\t// 设置最终移动位置,理论上只要进入到这个函数，肯定是要打开的\r\n\t\t},\r\n\t\tclose() {\r\n\t\t\tthis.openState('none')\r\n\t\t},\r\n\t\tgetDirection(x, y) {\r\n\t\t\tif (x > y && x > MIN_DISTANCE) {\r\n\t\t\t\treturn 'horizontal';\r\n\t\t\t}\r\n\t\t\tif (y > x && y > MIN_DISTANCE) {\r\n\t\t\t\treturn 'vertical';\r\n\t\t\t}\r\n\t\t\treturn '';\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t * 重置滑动状态\r\n\t\t * @param {Object} event\r\n\t\t */\r\n\t\tresetTouchStatus() {\r\n\t\t\tthis.direction = '';\r\n\t\t\tthis.deltaX = 0;\r\n\t\t\tthis.deltaY = 0;\r\n\t\t\tthis.offsetX = 0;\r\n\t\t\tthis.offsetY = 0;\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t * 设置滑动开始位置\r\n\t\t * @param {Object} event\r\n\t\t */\r\n\t\tstopTouchStart(event) {\r\n\t\t\tthis.resetTouchStatus();\r\n\t\t\tconst touch = event.touches[0];\r\n\t\t\tthis.startX = touch.clientX;\r\n\t\t\tthis.startY = touch.clientY;\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t * 滑动中，是否禁止打开\r\n\t\t * @param {Object} event\r\n\t\t */\r\n\t\tstopTouchMove(event) {\r\n\t\t\tconst touch = event.touches[0];\r\n\t\t\tthis.deltaX = touch.clientX - this.startX;\r\n\t\t\tthis.deltaY = touch.clientY - this.startY;\r\n\t\t\tthis.offsetX = Math.abs(this.deltaX);\r\n\t\t\tthis.offsetY = Math.abs(this.deltaY);\r\n\t\t\tthis.direction = this.direction || this.getDirection(this.offsetX, this.offsetY);\r\n\t\t},\r\n\r\n\t\tgetSelectorQuery() {\r\n\t\t\tconst views = uni.createSelectorQuery().in(this)\r\n\t\t\tviews\r\n\t\t\t\t.selectAll('.' + this.elClass)\r\n\t\t\t\t.boundingClientRect(data => {\r\n\t\t\t\t\tif (data.length === 0) return\r\n\t\t\t\t\tlet show = 'none'\r\n\t\t\t\t\tif (this.autoClose) {\r\n\t\t\t\t\t\tshow = 'none'\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tshow = this.show\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.leftWidth = data[0].width || 0\r\n\t\t\t\t\tthis.rightWidth = data[1].width || 0\r\n\t\t\t\t\tthis.buttonShow = show\r\n\t\t\t\t})\r\n\t\t\t\t.exec()\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// #endif\r\n\r\nexport default otherMixins\r\n"], "names": [], "mappings": ";AAAG,IAAC,cAAc,CAAA;;"}