"use strict";const e=require("../common/vendor.js"),n=require("../config/config.js");exports.miniPay=function(n){return new Promise(((t,i)=>{const s={nonceStr:n.nonceStr,package:n.package,paySign:n.paySign,signType:n.signType,timeStamp:n.timeStamp};e.index.requestPayment({...s,success:function(e){t(e)},fail:function(e){i({msg:"支付失败"})},cancel:function(){i({msg:"取消支付"})}})}))},exports.updataFileFun=async function(t){try{let i=`${n.base.baseUrl}user/api.userinfo/uploadImg`,s=e.index.uploadFile({url:i,name:"img",filePath:t,header:{"content-type":"multipart/form-data"}});return s.statusCode<200||s.statusCode>300?Promise.reject({errMsg:"statusCode is not 200 series"}):s}catch(i){return e.index.hideLoading(),Promise.reject(i)}};
