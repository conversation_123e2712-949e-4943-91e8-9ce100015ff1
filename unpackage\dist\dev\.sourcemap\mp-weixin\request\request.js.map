{"version": 3, "file": "request.js", "sources": ["request/request.js"], "sourcesContent": ["import base from '@/config/config.js';\r\nimport {\r\n\trandomStr,\r\n\tautographFun\r\n} from '../utils/requestUtil.js';\r\nimport {\r\n\tuseUserStore\r\n} from '@/stores/user.js'\r\n\r\nlet timeout = 60000;\r\nconst baseUrl = base.baseUrl;\r\nconst toastTip = (title, icon, time) => {\r\n\tuni.showToast({\r\n\t\ttitle: title || '网络连接超时,请稍后重试!',\r\n\t\ticon: icon || 'none',\r\n\t\tduration: time || 1500\r\n\t})\r\n}\r\n\r\nconst getKeys = (obj) => {\r\n\tlet keys = [];\r\n\tfor (let key in obj) {\r\n\t\tif (obj.hasOwnProperty(key)) {\r\n\t\t\tkeys.push(key);\r\n\t\t}\r\n\t}\r\n\treturn keys;\r\n}\r\nconst tansParams = (params) => {\r\n\tlet result = '';\r\n\tfor (const propName of this.getKeys(params)) {\r\n\t\tconst value = params[propName]\r\n\t\tlet part = encodeURIComponent(propName) + \"=\";\r\n\t\tif (value !== null && value !== \"\" && typeof (value) !== \"undefined\") {\r\n\t\t\tif (typeof value === 'object') {\r\n\t\t\t\tfor (const key of this.getKeys(value)) {\r\n\t\t\t\t\tif (value[key] !== null && value[key] !== \"\" && typeof (value[key]) !== 'undefined') {\r\n\t\t\t\t\t\tlet params = propName + '[' + key + ']';\r\n\t\t\t\t\t\tlet subPart = encodeURIComponent(params) + \"=\";\r\n\t\t\t\t\t\tresult += subPart + encodeURIComponent(value[key]) + \"&\";\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\tresult += part + encodeURIComponent(value) + \"&\"\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\treturn result\r\n}\r\n// 对象转query字符串的方法\r\nconst queryStr = (obj) => {\r\n\t// 首先判断obj是否为真，为真则进行处理，不然直接return\r\n\tif (obj) {\r\n\t\t// 定义变量接收query字符串\r\n\t\tlet query = \"\"\r\n\t\t// 循环遍历对象\r\n\t\tfor (let i in obj) {\r\n\t\t\t// 定义变量接收对象的value值\r\n\t\t\tlet value = obj[i]\r\n\t\t\t// 若对象的value值为数组，则进行join打断\r\n\t\t\tif (Array.isArray(value)) {\r\n\t\t\t\tvalue = value.join(\",\")\r\n\t\t\t}\r\n\t\t\t// 进行字符串拼接\r\n\t\t\tquery += `&${i}=${value}`\r\n\t\t}\r\n\t\t// replace返回一个新的字符串，要用query重新接受一下，并把第一个&替换为?\r\n\t\tquery = query.replace('&', '?')\r\n\t\t// 返回生成的query字符串\r\n\t\treturn query\r\n\t}\r\n\treturn \"\"\r\n}\r\nconst request = (config) => {\r\n\t/**\r\n\t * @description  如果isToken为true的时候需要token(一般会在登录的时候存储token和token_type)  为false不需要token\r\n\t * @description  config.header['Authorization'] = 'Bearer ' + uni.getStorageSync('token'); // token_type 一般情况下为'Bearer ' 切记有空格哦\r\n\t * @description  config.header['Content-Type'] = 'application/x-www-form-urlencoded'; // 常规请求头配置\r\n\t */\r\n\t// const isToken = config.header?.isToken ? config.header?.isToken : false;\r\n\t// config.header = config.header ? config.header : {};\r\n\t// if (getToken() && isToken) {\r\n\t//   config.header['Authorization'] = uni.getStorageSync('token_type') + uni.getStorageSync('token');\r\n\t// };\r\n\tconst userStore = useUserStore()\r\n\tconfig.urlSuffix = {\r\n\t\tapp_guid: 'e108201b02ae42e686bcc4c302cbbd11', //应用唯一标识\r\n\t\texpires: parseInt((new Date().getTime() / 1000).toFixed(0)), //当前时间戳\r\n\t\ttoken: userStore.userToken || 'notoken',\r\n\t\tnoncestr: randomStr(true, true, true, 32),\r\n\t\tmerchantGuid: userStore.merchantGuid,\r\n\t\tapp_type: 'wechat',\r\n\t};\r\n\t// if (!config.header.ifTouristLogin) {\r\n\tconfig.urlSuffix.signature = encodeURIComponent(autographFun(config)); //签名\r\n\t// }\r\n\t/**\r\n\t * @description  get请求映射params参数\r\n\t */\r\n\tif (config.params) {\r\n\t\tlet url = config.url + '?' + tansParams(config.params)\r\n\t\turl = url.slice(0, -1)\r\n\t\tconfig.url = url\r\n\t}\r\n\treturn new Promise((resolve, reject) => {\r\n\t\tuni.request({\r\n\t\t\tmethod: config.method || 'POST',\r\n\t\t\ttimeout: config.timeout || timeout,\r\n\t\t\turl: baseUrl + config.url + queryStr(config.urlSuffix),\r\n\t\t\tdata: config.data,\r\n\t\t\theader: config.header,\r\n\t\t\tdataType: 'json',\r\n\t\t\tsuccess(res) {\r\n\t\t\t\tif (res.data.code === 704001) {\r\n\t\t\t\t\t// toastTip(res.data.msg)\r\n\t\t\t\t\t// userStore.modalStatus = \"login\"\r\n\t\t\t\t\tuserStore.delete_user_info();\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\t/**\r\n\t\t\t\t * @description 请求成功返回的数据\r\n\t\t\t\t */\r\n\t\t\t\tif (res.data.code === 0) {\r\n\t\t\t\t\tresolve(res.data)\r\n\t\t\t\t} else {\r\n\t\t\t\t\tconsole.log(res.data, '--------------------???')\r\n\t\t\t\t\ttoastTip(res.data.msg)\r\n\t\t\t\t\treject(res.data)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tfail: (error) => {\r\n\t\t\t\tconsole.log(error, 'errorerrorerror')\r\n\t\t\t\tlet {\r\n\t\t\t\t\terrMsg\r\n\t\t\t\t} = error\r\n\t\t\t\tif (errMsg === 'Network Error') {\r\n\t\t\t\t\terrMsg = '后端接口连接异常'\r\n\t\t\t\t} else if (errMsg.includes('timeout')) {\r\n\t\t\t\t\terrMsg = 'AI算力繁忙'\r\n\t\t\t\t} else if (errMsg.includes('Request failed with status code')) {\r\n\t\t\t\t\terrMsg = '系统接口' + errMsg.substr(errMsg.length - 3) + '异常'\r\n\t\t\t\t}\r\n\t\t\t\ttoastTip(errMsg, 'none', 1500)\r\n\t\t\t\t/**\r\n\t\t\t\t * @description 请求失败返回的消息\r\n\t\t\t\t */\r\n\t\t\t\treject(error)\r\n\t\t\t},\r\n\t\t\tcomplete(res) {\r\n\t\t\t\t/**\r\n\t\t\t\t * @description 请求完做的事\r\n\t\t\t\t */\r\n\t\t\t}\r\n\t\t})\r\n\t})\r\n}\r\n\r\n/**\r\n *  @description 暴露出request请求供其他业务使用\r\n */\r\n\r\nexport default request"], "names": ["base", "uni", "this", "params", "useUserStore", "randomStr", "autographFun"], "mappings": ";;;;;AASA,IAAI,UAAU;AACd,MAAM,UAAUA,cAAI,KAAC;AACrB,MAAM,WAAW,CAAC,OAAO,MAAM,SAAS;AACvCC,gBAAAA,MAAI,UAAU;AAAA,IACb,OAAO,SAAS;AAAA,IAChB,MAAM,QAAQ;AAAA,IACd,UAAU,QAAQ;AAAA,EACpB,CAAE;AACF;AAWA,MAAM,aAAa,CAAC,WAAW;AAC9B,MAAI,SAAS;AACb,aAAW,YAAYC,SAAK,QAAQ,MAAM,GAAG;AAC5C,UAAM,QAAQ,OAAO,QAAQ;AAC7B,QAAI,OAAO,mBAAmB,QAAQ,IAAI;AAC1C,QAAI,UAAU,QAAQ,UAAU,MAAM,OAAQ,UAAW,aAAa;AACrE,UAAI,OAAO,UAAU,UAAU;AAC9B,mBAAW,OAAOA,SAAK,QAAQ,KAAK,GAAG;AACtC,cAAI,MAAM,GAAG,MAAM,QAAQ,MAAM,GAAG,MAAM,MAAM,OAAQ,MAAM,GAAG,MAAO,aAAa;AACpF,gBAAIC,UAAS,WAAW,MAAM,MAAM;AACpC,gBAAI,UAAU,mBAAmBA,OAAM,IAAI;AAC3C,sBAAU,UAAU,mBAAmB,MAAM,GAAG,CAAC,IAAI;AAAA,UACrD;AAAA,QACD;AAAA,MACL,OAAU;AACN,kBAAU,OAAO,mBAAmB,KAAK,IAAI;AAAA,MAC7C;AAAA,IACD;AAAA,EACD;AACD,SAAO;AACR;AAEA,MAAM,WAAW,CAAC,QAAQ;AAEzB,MAAI,KAAK;AAER,QAAI,QAAQ;AAEZ,aAAS,KAAK,KAAK;AAElB,UAAI,QAAQ,IAAI,CAAC;AAEjB,UAAI,MAAM,QAAQ,KAAK,GAAG;AACzB,gBAAQ,MAAM,KAAK,GAAG;AAAA,MACtB;AAED,eAAS,IAAI,CAAC,IAAI,KAAK;AAAA,IACvB;AAED,YAAQ,MAAM,QAAQ,KAAK,GAAG;AAE9B,WAAO;AAAA,EACP;AACD,SAAO;AACR;AACK,MAAC,UAAU,CAAC,WAAW;AAW3B,QAAM,YAAYC,YAAAA,aAAc;AAChC,SAAO,YAAY;AAAA,IAClB,UAAU;AAAA;AAAA,IACV,SAAS,WAAU,oBAAI,KAAM,GAAC,QAAO,IAAK,KAAM,QAAQ,CAAC,CAAC;AAAA;AAAA,IAC1D,OAAO,UAAU,aAAa;AAAA,IAC9B,UAAUC,kBAAAA,UAAU,MAAM,MAAM,MAAM,EAAE;AAAA,IACxC,cAAc,UAAU;AAAA,IACxB,UAAU;AAAA,EACZ;AAEC,SAAO,UAAU,YAAY,mBAAmBC,kBAAY,aAAC,MAAM,CAAC;AAKpE,MAAI,OAAO,QAAQ;AAClB,QAAI,MAAM,OAAO,MAAM,MAAM,WAAW,OAAO,MAAM;AACrD,UAAM,IAAI,MAAM,GAAG,EAAE;AACrB,WAAO,MAAM;AAAA,EACb;AACD,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACvCL,kBAAAA,MAAI,QAAQ;AAAA,MACX,QAAQ,OAAO,UAAU;AAAA,MACzB,SAAS,OAAO,WAAW;AAAA,MAC3B,KAAK,UAAU,OAAO,MAAM,SAAS,OAAO,SAAS;AAAA,MACrD,MAAM,OAAO;AAAA,MACb,QAAQ,OAAO;AAAA,MACf,UAAU;AAAA,MACV,QAAQ,KAAK;AACZ,YAAI,IAAI,KAAK,SAAS,QAAQ;AAG7B,oBAAU,iBAAgB;AAC1B;AAAA,QACA;AAID,YAAI,IAAI,KAAK,SAAS,GAAG;AACxB,kBAAQ,IAAI,IAAI;AAAA,QACrB,OAAW;AACNA,wBAAY,MAAA,MAAA,OAAA,6BAAA,IAAI,MAAM,yBAAyB;AAC/C,mBAAS,IAAI,KAAK,GAAG;AACrB,iBAAO,IAAI,IAAI;AAAA,QACf;AAAA,MACD;AAAA,MACD,MAAM,CAAC,UAAU;AAChBA,sBAAAA,MAAA,MAAA,OAAA,6BAAY,OAAO,iBAAiB;AACpC,YAAI;AAAA,UACH;AAAA,QACL,IAAQ;AACJ,YAAI,WAAW,iBAAiB;AAC/B,mBAAS;AAAA,QACT,WAAU,OAAO,SAAS,SAAS,GAAG;AACtC,mBAAS;AAAA,QACT,WAAU,OAAO,SAAS,iCAAiC,GAAG;AAC9D,mBAAS,SAAS,OAAO,OAAO,OAAO,SAAS,CAAC,IAAI;AAAA,QACrD;AACD,iBAAS,QAAQ,QAAQ,IAAI;AAI7B,eAAO,KAAK;AAAA,MACZ;AAAA,MACD,SAAS,KAAK;AAAA,MAIb;AAAA,IACJ,CAAG;AAAA,EACH,CAAE;AACF;;"}