<view class="container data-v-59e0655d"><scroll-view class="page-scroll data-v-59e0655d" scroll-y refresher-enabled refresher-triggered="{{h}}" bindrefresherrefresh="{{i}}" bindrefresherrestore="{{j}}" bindscrolltolower="{{k}}" lower-threshold="{{100}}"><view class="chat-list data-v-59e0655d"><view wx:if="{{a}}" class="loading-container data-v-59e0655d"><text class="loading-text data-v-59e0655d">加载中...</text></view><view wx:elif="{{b}}" class="empty-container data-v-59e0655d"><text class="empty-text data-v-59e0655d">暂无对话记录</text></view><view wx:else class="data-v-59e0655d"><uni-swipe-action class="r data-v-59e0655d" u-s="{{['d']}}" u-r="swipeActionRef" u-i="59e0655d-0" bind:__l="__l"><uni-swipe-action-item wx:for="{{c}}" wx:for-item="item" wx:key="f" class="data-v-59e0655d" u-s="{{['d']}}" bindclick="{{item.e}}" u-i="{{item.g}}" bind:__l="__l" u-p="{{item.h}}"><view class="chat-item data-v-59e0655d" bindtap="{{item.d}}"><view class="avatar data-v-59e0655d"><image src="{{item.a}}" class="avatar-img data-v-59e0655d" mode="aspectFill"></image></view><view class="content data-v-59e0655d"><view class="name data-v-59e0655d">{{item.b}}</view><view class="message data-v-59e0655d">{{item.c}}</view></view></view></uni-swipe-action-item></uni-swipe-action><view wx:if="{{e}}" class="load-more-container data-v-59e0655d"><view wx:if="{{f}}" class="load-more-item data-v-59e0655d"><text class="load-more-text data-v-59e0655d">加载中...</text></view><view wx:elif="{{g}}" class="load-more-item data-v-59e0655d"><text class="load-more-text data-v-59e0655d">没有更多数据了</text></view></view></view></view></scroll-view><view wx:if="{{l}}" class="modal-overlay data-v-59e0655d" bindtap="{{s}}"><view class="edit-modal data-v-59e0655d" catchtap="{{r}}"><view class="modal-title data-v-59e0655d">对话名称</view><view class="input-container data-v-59e0655d"><input class="edit-input data-v-59e0655d" placeholder="请输入对话名称" focus="{{m}}" value="{{n}}" bindinput="{{o}}"/></view><view class="modal-buttons data-v-59e0655d"><view class="cancel-btn data-v-59e0655d" bindtap="{{p}}">取消</view><view class="confirm-btn data-v-59e0655d" bindtap="{{q}}">确认</view></view></view></view></view>