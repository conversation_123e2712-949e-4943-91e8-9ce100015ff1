<template>
  <view class="container">
    <!-- 顶部标签栏 -->
    <view class="tabs-container">
      <uv-tabs :list="tabsList" :current="currentTab" @change="handleTabChange" lineColor="#222222"
        keyName="categoryName">
        <template v-slot:right>
          <view class="search-icon" @tap="handleSearch">
            <image src="@/static/square/<EMAIL>" class="icon" mode="aspectFit"></image>
          </view>
        </template>
      </uv-tabs>
    </view>

    <!-- 列表内容 -->
    <view class="content-container" :style="{ height: containerHeight }">
      <swiper class="tab-swiper" :current="currentTab" :indicator-dots="false" :autoplay="false" :circular="false"
        @change="handleSwiperChange" :style="{ height: containerHeight }">
        <!-- 为每个分类创建一个swiper-item -->
        <swiper-item v-for="(tab, tabIndex) in tabsList" :key="tab.guid" :style="{ height: containerHeight }">
          <scroll-view scroll-y="true" class="scroll-view" :style="{ height: containerHeight }">
            <view class="agent-list">
              <view v-for="item in categoryAgentData[tabIndex] || []" :key="item.guid" class="agent-item">
                <!-- 头像 -->
                <view class="avatar" @click="handleAgentClick(item)">
                  <image :src="item.agentAvatar" class="avatar-img" mode="aspectFill"></image>
                </view>

                <!-- 内容区域 -->
                <view class="content" @click="handleAgentClick(item)">
                  <view class="title">{{ item.agentName }}</view>
                  <view class="description">{{ item.agentDesc }}</view>
                  <view class="author">@{{ item.creator.nickname }}</view>
                </view>

                <!-- 右侧按钮 -->
                <view class="action-btn" :class="{ subscribed: item.isSubscribed }" @click="onSub(item)">
                  <text class="btn-text">{{ item.isSubscribed ? '已合伙' : '去招募' }}</text>
                </view>
              </view>
            </view>
            <view class="empty-container" v-if="categoryAgentData[tabIndex]?.length === 0">
              暂无智能体
            </view>
          </scroll-view>

        </swiper-item>
      </swiper>
    </view>

  </view>
</template>

<script setup>
import { ref, watch, nextTick } from 'vue'
import {
  onShow,
  onLoad,
  onShareAppMessage,
  onShareTimeline
} from '@dcloudio/uni-app'
import { useUserStore } from '@/stores/user.js'
import { getCategoryListApi, getAgentListApi, subscribeAgentApi, bindInvitationApi } from '@/api/index.js'

const userStore = useUserStore()
// 当前选中的标签
const currentTab = ref(0)
// 标签列表
const tabsList = ref([])
// 动态计算的容器高度
const containerHeight = ref('calc(100vh - 88rpx)')

// 初始化容器高度
const initContainerHeight = () => {
  nextTick(() => {
    uni.getSystemInfo({
      success: (res) => {
        // 获取系统信息
        const windowHeight = res.windowHeight

        // 计算可用高度，减去tabs高度(大约44px)
        const availableHeight = windowHeight - 44

        // 设置最小高度为600px，确保Android兼容性
        const finalHeight = Math.max(availableHeight, 600)

        containerHeight.value = `${finalHeight}px`

        console.log('Container height set to:', containerHeight.value)
      },
      fail: () => {
        // 失败时使用默认值
        containerHeight.value = '600px'
      }
    })
  })
}

const getCategoryList = async () => {
  let res = await getCategoryListApi({
    merchantGuid: userStore.merchantGuid
  })
  tabsList.value = res.data;
  // 初始化categoryAgentData数组
  categoryAgentData.value = new Array(tabsList.value.length)

  // 检查是否需要切换到指定分类
  if (userStore.targetCategoryGuid) {
    switchToCategory(userStore.targetCategoryGuid)
    userStore.clear_target_category()
  } else {
    // 只加载第一个分类的数据
    getAgentList()
  }
}
// 按分类存储的数据，数组索引对应tabsList的索引
const categoryAgentData = ref([])

const getAgentList = async (categoryIndex = null) => {
  if (tabsList.value.length > 0) {
    const targetIndex = categoryIndex !== null ? categoryIndex : currentTab.value
    let guid = tabsList.value[targetIndex].guid
    let res = await getAgentListApi({
      merchantGuid: userStore.merchantGuid,
      categoryGuid: guid,
      pageSize: 100
    })
    console.log('categoryAgentData before:', categoryAgentData.value)
    // 确保数组有足够的长度
    if (categoryAgentData.value.length <= targetIndex) {
      categoryAgentData.value = [...categoryAgentData.value, ...new Array(targetIndex + 1 - categoryAgentData.value.length)]
    }
    categoryAgentData.value[targetIndex] = res.data.data;
    console.log('categoryAgentData after:', categoryAgentData.value)
    console.log('categoryAgentData length:', categoryAgentData.value[targetIndex]?.length)
  }
}



// 标签切换
const handleTabChange = (event) => {
  console.log('handleTabChange', event)
  currentTab.value = event.index
  // console.log('切换到标签:', tabsList.value[event.index]?.categoryName)
  // 如果该分类数据还没有加载，则加载
  if (!categoryAgentData.value[event.index]) {
    getAgentList(event.index)
  }
}

// swiper切换处理
const handleSwiperChange = (e) => {
  currentTab.value = e.detail.current
  // console.log('swiper切换到:', tabsList.value[e.detail.current]?.categoryName)
  // 如果该分类数据还没有加载，则加载
  if (!categoryAgentData.value[e.detail.current]) {
    getAgentList(e.detail.current)
  }
}

// 搜索功能
const handleSearch = () => {
  console.log('点击搜索')
  uni.navigateTo({
    url: '/pages/square/search'
  })
}

// 点击智能体
const handleAgentClick = (item) => {
  console.log('点击智能体:', item.agentName)
  // 跳转到详情页
  uni.navigateTo({
    url: `/pages/square/detail?sysId=${item.sysId}`
  })
}
const onSub = async (item) => {
  if (item.isSubscribed) {
    uni.navigateTo({
      url: `/pages/msg/index?sessionGuid=${item.guid}`
    })
    return
  } else {
    let req = {
      merchantGuid: userStore.merchantGuid,
      agentGuid: item.guid
    }
    try {
      await subscribeAgentApi(req)
      getAgentList(currentTab.value)
      uni.showToast({
        title: '招募成功',
        icon: 'none'
      })
    } catch (error) {
      uni.showToast({
        title: '招募失败',
        icon: 'none'
      })
    }
  }


}
onShareAppMessage(() => {
  return {
    title: userStore.appName || '智能体',
    path: `/pages/square/square?invite=${userStore.invitationCode}`,
    success(res) {
      console.log('userStore.invitationCode', userStore.invitationCode)
      uni.showToast({
        title: '分享成功'
      })
    },
    fail(res) {
      uni.showToast({
        title: '分享失败',
        icon: 'none'
      })
    }
  }
})
// 分享到朋友圈功能
// onShareTimeline(() => {
//   return {
//     title: userStore.appName || '智能体',
//     path: `/pages/square/square?invite=${userStore.invitationCode}`,
//     success(res) {
//       uni.showToast({
//         title: '分享成功'
//       })
//     },
//     fail(res) {
//       uni.showToast({
//         title: '分享失败',
//         icon: 'none'
//       })
//     }
//   }
// })



// 根据分类guid查找对应的索引
const findCategoryIndex = (categoryGuid) => {
  return tabsList.value.findIndex(item => item.guid === categoryGuid)
}

// 切换到指定分类
const switchToCategory = (categoryGuid) => {
  const index = findCategoryIndex(categoryGuid)
  if (index !== -1) {
    currentTab.value = index
    // 如果该分类数据还没有加载，则加载
    if (!categoryAgentData.value[index]) {
      getAgentList(index)
    }
  }
}

// 页面显示时检查是否需要切换分类
onShow(() => {
  // 初始化容器高度
  initContainerHeight()
  if (userStore.targetCategoryGuid) {
    console.log(tabsList.value, 'tabsList.valuetabsList.value')
    // 如果分类列表已加载，直接切换
    if (tabsList.value.length > 0) {
      switchToCategory(userStore.targetCategoryGuid)
      userStore.clear_target_category()
    } else {
      getCategoryList()
    }
  } else {
    getCategoryList()
  }
})
// let invite = ref('')
// const bindInvitation = async () => {
//   try {
//     console.log('invite.value', invite.value)
//     await bindInvitationApi({
//       merchantGuid: userStore.merchantGuid,
//       invitationCode: invite.value
//     })
//   } catch (error) {
//     console.error('绑定邀请码失败:', error)
//   }
// }

// onLoad((params) => {
//   if (params.invite) {
//     invite.value = params.invite;
//     if (userStore.userToken) {
//       bindInvitation()
//     }
//   }
// });
watch(
  () => userStore.userToken,
  (newValue, oldValue) => {
    console.log('userToken changed', newValue, oldValue)
    if (newValue && oldValue === '') {
      getCategoryList()
      // bindInvitation()
    }
  }
);
</script>

<style lang="scss" scoped>
.container {
  background-color: #ffffff;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 120rpx 0;
  color: #999999;
  font-size: 28rpx;
}

/* 顶部标签栏 */
.tabs-container {
  background-color: #ffffff;
  padding: 0;

  .search-icon {
    padding: 0 20px;
    display: flex;
    align-items: center;

    .icon {
      width: 60rpx;
      height: 60rpx;
      display: block;
    }
  }
}

/* 内容容器 */
.content-container {
  flex: 1;
  overflow: hidden;
  background-color: #ffffff;
  /* 默认高度，会被JavaScript动态设置覆盖 */
  height: calc(100vh - 88rpx);
  /* 为Android机型添加最小高度保障 */
  min-height: 600px;
}

/* tab切换容器 */
.tab-swiper {
  width: 100%;
  /* 默认高度，会被JavaScript动态设置覆盖 */
  height: calc(100vh - 88rpx);
  /* 为Android机型添加最小高度保障 */
  min-height: 600px;
}

.scroll-view {
  /* 默认高度，会被JavaScript动态设置覆盖 */
  height: calc(100vh - 88rpx);
  min-height: 600px;
  width: 100%;
}

/* 确保swiper-item高度正确 */
swiper-item {
  /* 默认高度，会被JavaScript动态设置覆盖 */
  height: calc(100vh - 88rpx);
  min-height: 600px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 智能体列表 */
.agent-list {
  padding: 0 20px;
  background-color: #ffffff;
}

.agent-item {
  display: flex;
  align-items: center;
  padding: 20px 0;

  &:last-child {
    border-bottom: none;
  }
}

.avatar {
  width: 48px;
  height: 48px;
  margin-right: 16px;
  flex-shrink: 0;

  .avatar-img {
    width: 100%;
    height: 100%;
    border-radius: 24px;
  }
}

.content {
  flex: 1;
  margin-right: 16px;

  .title {
    font-size: 32rpx;
    font-weight: 600;
    color: #222;
    margin-bottom: 6px;
    line-height: 1.3;
  }

  .description {
    font-size: 24rpx;
    color: #333;
    line-height: 1.4;
    margin-bottom: 6px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
  }

  .author {
    font-size: 24rpx;
    color: #999999;
  }
}

.action-btn {
  flex-shrink: 0;
  height: 32px;
  // padding: 0 20px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 116rpx;
  background-color: #007AFF;

  .btn-text {
    font-size: 28rpx;
    font-weight: 500;
    color: #ffffff;
  }

  // 可订阅状态 - 蓝色按钮
  // &.available {


  //   .btn-text {
  //     color: #ffffff;
  //   }
  // }

  // 已订阅状态 - 灰色按钮
  &.subscribed {
    background-color: #F2F2F7;
    border: none;

    .btn-text {
      color: #8E8E93;
    }
  }

  // 已激活状态 - 蓝色文字按钮
  &.activated {
    background-color: #E7EDFA;

    .btn-text {
      color: #2A64F6;
    }
  }
}
</style>