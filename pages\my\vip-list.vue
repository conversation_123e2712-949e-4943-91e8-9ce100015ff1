<template>
  <view class="vip-page">
    <view class="card-top">
      <!-- 顶部用户信息卡片 -->
      <view class="user-card"
        :style="{ backgroundImage: 'url(https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/a95919155b8142f181c097ee21a9ae8b.png)' }">
        <view class="user-avatar">
          <image class="avatar-img" :src="userInfo.headImgUrl || defaultAvatar" mode="aspectFill" />
        </view>
        <view class="user-info">
          <text class="user-name">{{ userInfo.nickname || '用户Aric' }}</text>
          <text class="user-status" v-if="!vipInfo.hasMembership">成为思链AI会员</text>
          <text class="user-status" v-else>已激活思链AI会员({{ vipInfo.remainingDays }}天)</text>
          <text class="user-desc" v-if="!vipInfo.hasMembership">让AI成为你的IP合伙人</text>
          <text class="user-desc" v-else>让AI成为你的IP合伙人</text>
        </view>
      </view>

      <!-- 提示文字 -->
      <view class="tip-text">
        {{ rule }}
      </view>
    </view>


    <!-- 会员套餐选择 -->
    <view class="vip-plans">
      <view class="plan-box">
        <view v-for="(plan, index) in vipPlans" :key="index"
          :class="['plan-card', { 'selected': selectedPlan === index }]" @tap="selectPlan(index)">
          <view class="plan-header">
            <text class="plan-name">{{ plan.packageName }}</text>
          </view>
          <view class="plan-price">
            <text class="price-symbol">¥</text>
            <text class="price-amount">{{ plan.salePriceYuan }}</text>
          </view>
          <view class="plan-duration">{{ plan.durationDays }}天</view>
          <view class="plan-daily">{{ plan.packageDesc }}</view>
          <!-- <view class="plan-daily">仅{{ plan.dailyPriceYuan }}/天</view> -->
        </view>
      </view>
      <!-- 立即订阅按钮 -->
      <!-- <view class="subscribe-btn ios" v-if="isIos" @click="onShowPoster">
        <text class="btn-text">{{ isYearCrad ? '联系白先生' : 'IOS暂不支持' }}</text>
      </view> -->
      <!-- v-else -->
      <view class="subscribe-btn" @click="handleSubscribe">
        <text class="btn-text">立即订阅</text>
      </view>
    </view>

    <!-- 图片弹窗 -->
    <view v-if="showImageModal" class="image-modal-overlay" @click="closeImageModal">
      <view class="image-modal-content">
        <!-- 图片 -->
        <image v-if="shenqun_img" :src="shenqun_img" class="modal-image" mode="aspectFit" @load="onImageLoad"
          @error="onImageError" show-menu-by-longpress />
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { onLoad, onShow } from '@dcloudio/uni-app'
import { getUserVipInfoApi, getVipPackageListApi, createVipOrderApi, queryVipOrderApi, getSubscriptionRuleApi, showBannerUrlsApi } from '@/api/index.js'
import { useUserStore } from '@/stores/user.js'
import { miniPay } from '@/api/common.js'

const userStore = useUserStore()

// 默认头像
const defaultAvatar = 'https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/c4d5b3ada53740d5b862f274ce48ef0c.png'
const userBg = 'https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/bda9547fe82a404ca6ac801b6c9178ec.png'
// 用户信息
const userInfo = computed(() => {
  return userStore.userInfo
})

// 会员套餐数据
const vipPlans = ref([])
const getVipPackageList = async () => {
  try {
    let res = await getVipPackageListApi({
      merchantGuid: userStore.merchantGuid
    })
    vipPlans.value = res.data.packages
  } catch (error) {
    console.error('获取会员套餐列表失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    })
  }
}
// 选中的套餐
const selectedPlan = ref(0) // 默认选中年卡

// 支付状态
const queryStatusNum = ref(0)

const rule = ref('')
const getSubscriptionRule = async () => {
  try {
    const res = await getSubscriptionRuleApi({
      merchantGuid: userStore.merchantGuid
    })
    if (res.code === 0) {
      rule.value = res.data.member_card_notice;
    }
  } catch (error) {
    console.error('获取规则失败:', error)
  }
}
//如果是年卡 弹出那个弹窗
const isYearCrad = ref(false)
// 选择套餐
const selectPlan = (index) => {
  selectedPlan.value = index;
  if (vipPlans.value[selectedPlan.value].packageType === 3) {
    isYearCrad.value = true
  } else {
    isYearCrad.value = false
  }

}

const shenqun_img = ref('')
const showImageModal = ref(false)

const showBannerUrls = async () => {
  let res = await showBannerUrlsApi({
    merchantGuid: userStore.merchantGuid
  })
  shenqun_img.value = res.data.buy_banner_img;
}

// 显示图片弹窗
const onShowPoster = () => {
  if (isYearCrad.value) {
    showImageModal.value = true;
  }
}

// 关闭图片弹窗
const closeImageModal = () => {
  showImageModal.value = false;
}
// 立即订阅
const handleSubscribe = async () => {
  if (!userStore.userToken) {
    uni.showToast({
      title: '请先登录',
      icon: 'none'
    })
    return
  }
  if (isYearCrad.value) {
    onShowPoster()
    return;
  }
  const currentPlan = vipPlans.value[selectedPlan.value]

  try {
    uni.showLoading({
      title: '正在创建订单...',
      mask: true
    })

    // 创建会员订阅订单
    const payInfo = await createVipOrderApi({
      merchantGuid: userStore.merchantGuid,
      packageGuid: currentPlan.guid,
      payEnv: 'xcx'
    })

    uni.hideLoading()

    // 调用微信支付
    miniPay(payInfo.data.payInfo).then(
      async () => {
        queryPayStatus(payInfo.data.orderNo, queryStatusNum.value)
      },
      (res) => {
        uni.showToast({
          title: res.msg || '支付失败',
          icon: 'none'
        })
      }
    )
  } catch (error) {
    uni.hideLoading()
    console.error('创建订单失败:', error)
    uni.showToast({
      title: error.message || '创建订单失败',
      icon: 'none'
    })
  }
}

// 查询支付状态
const queryPayStatus = async (orderNo, number) => {
  number++
  try {
    const orderInfo = await queryVipOrderApi({
      orderNo
    })

    if (orderInfo.data.isPaid) {
      uni.showToast({
        title: '支付成功',
        icon: 'success'
      })
      // 支付成功后返回上一页或跳转到会员页面
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    } else {
      if (number > 12) {
        uni.showToast({
          title: '支付超时',
          icon: 'none'
        })
      } else {
        setTimeout(() => {
          queryPayStatus(orderNo, number)
        }, 2000)
      }
    }
  } catch (error) {
    uni.showToast({
      title: error.msg || '查询支付状态失败',
      icon: 'none'
    })
  }
}
//获取会员信息
let vipInfo = reactive({
  hasMembership: false,
  isExpired: true,
  membership: null,
  remainingDays: 0
})
const getVipInfo = async () => {
  try {
    let res = await getUserVipInfoApi({
      merchantGuid: userStore.merchantGuid
    })
    vipInfo = Object.assign(vipInfo, res.data)
  } catch (error) {
    console.error('获取会员信息失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    })
  }
}
const isIos = ref(false)
onLoad(() => {
  let systemInfomations = uni.getSystemInfoSync()
  if (systemInfomations.osName === 'ios') {
    isIos.value = true
  }
  getSubscriptionRule()
  showBannerUrls()
})

onShow(() => {
  if (userStore.userToken) {
    getVipPackageList()
    getVipInfo()
    getSubscriptionRule()
    showBannerUrls()
  }
})
</script>

<style lang="scss" scoped>
/* 图片弹窗样式 */
.image-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.image-modal-content {
  width: 90%;
  height: 80vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}

.modal-image {
  max-width: 100%;
  max-height: 200px;
  width: 100%;
  height: 300px;
  display: block;
  object-fit: contain;
}

.vip-page {
  height: 100vh;
  background-color: #1E2541;
  padding-top: 40rpx;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

.card-top {
  padding: 0 32rpx;
}

.user-card {
  display: flex;
  flex-direction: column;
  height: 508rpx;
  width: 100%;
  justify-content: center;
  align-items: center;
  text-align: center;
  background-image: url();
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

.user-avatar {
  width: 220rpx;
  height: 220rpx;
  border-radius: 50%;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.2);
  // border: 4rpx solid rgba(255, 255, 255, 0.3);
  margin-bottom: 24rpx;
}

.avatar-img {
  width: 100%;
  height: 100%;
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.user-name {
  font-size: 35rpx;
  font-weight: 500;
  color: #F7D4BE;
  margin-bottom: 12rpx;
}

.user-status {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 8rpx;
}

.user-desc {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
}

.tip-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
  padding: 50rpx 20rpx 30rpx 20rpx;
  line-height: 1.5;
  background-color: #252D44;
  margin-top: 20rpx;
  border-radius: 25rpx 25rpx 25rpx 25rpx;
  margin-bottom: 20px;
}

.vip-plans {
  display: flex;
  flex: 1;
  flex-direction: column;
  justify-content: space-between;
  background-color: #fff;
  gap: 20rpx;
  padding-bottom: 80rpx;
}

.plan-box {
  display: flex;
  justify-content: space-around;
  margin-top: 30px;
  flex-wrap: wrap;
  padding: 0 30rpx;
}

.plan-card {
  background: #FFFFFF;
  border-radius: 25rpx;
  border: 3rpx solid #EDEFF0;
  text-align: center;
  position: relative;
  transition: all 0.3s ease;
  width: 210rpx;
  height: 260rpx;
  box-sizing: border-box;
  overflow: hidden;
  margin-bottom: 20rpx;
}

.plan-card.selected {
  border-color: #DAAA76;
  background: #FDF6EB;

  .plan-name {
    background: #DAAA76;
    color: #FDF6EB;
  }

  .price-amount {
    color: #CB8D59;
  }

  .plan-daily {
    background-color: #FBE9DA;
    color: #CD8D57;
  }
}

.plan-header {
  margin-bottom: 20rpx;
  display: flex;
  justify-content: center;
}

.plan-name {
  font-size: 24rpx;
  // font-weight: 600;
  color: #303649;
  background: #EDEFF0;
  border-radius: 0rpx 0rpx 20rpx 20rpx;
  width: fit-content;
  height: 48rpx;
  display: flex;
  align-items: center;
  padding: 0 4px;

}

.plan-price {
  display: flex;
  align-items: baseline;
  justify-content: center;
  margin-bottom: 12rpx;
}

.price-symbol {
  font-size: 24rpx;
  color: #CB8D59;
  font-weight: 600;
}

.price-amount {
  font-size: 48rpx;
  font-weight: 700;
  color: #1D253B;
}

.plan-duration {
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 8rpx;
}

.plan-daily {
  font-size: 18rpx;
  color: #999999;
  background-color: #F6F8FA;
  width: 100%;
  height: 56rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  left: 0;
  bottom: 0;
}

.subscribe-btn {
  width: 530rpx;
  margin: 0 auto;
  height: 100rpx;
  background: #2A64F6;
  border-radius: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;

  &.ios {
    background: #999;
  }
}

.btn-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #FFFFFF;
}
</style>