"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const stores_user = require("../../stores/user.js");
const api_index = require("../../api/index.js");
const api_common = require("../../api/common.js");
const _sfc_main = {
  __name: "index",
  setup(__props) {
    const userStore = stores_user.useUserStore();
    let userForm = common_vendor.reactive({
      headImgUrl: "",
      nickname: "",
      email: ""
    });
    const getUserInfo = async () => {
      let res = await api_index.getUserInfoApi();
      userForm = Object.assign(userForm, res.data);
    };
    const saving = common_vendor.ref(false);
    const uploading = common_vendor.ref(false);
    common_vendor.onMounted(() => {
      getUserInfo();
    });
    const chooseAvatar = () => {
      if (uploading.value)
        return;
      common_vendor.index.chooseImage({
        count: 1,
        sizeType: ["compressed"],
        sourceType: ["album", "camera"],
        success: async (res) => {
          const tempFilePath = res.tempFilePaths[0];
          await uploadAvatar(tempFilePath);
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/profile/index.vue:80", "选择图片失败:", err);
        }
      });
    };
    const uploadAvatar = async (filePath) => {
      try {
        uploading.value = true;
        common_vendor.index.showLoading({
          title: "上传中...",
          mask: true
        });
        const uploadRes = await api_common.updataFileFun(filePath);
        const result = JSON.parse(uploadRes.data);
        if (result.code === 0) {
          userForm.headImgUrl = result.data;
          common_vendor.index.showToast({
            title: "头像上传成功",
            icon: "success"
          });
        } else {
          throw new Error(result.msg || "上传失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/profile/index.vue:107", "上传头像失败:", error);
        common_vendor.index.showToast({
          title: "上传失败",
          icon: "none"
        });
      } finally {
        uploading.value = false;
        common_vendor.index.hideLoading();
      }
    };
    const handleSave = async () => {
      if (saving.value)
        return;
      if (!userForm.nickname.trim()) {
        common_vendor.index.showToast({
          title: "请输入昵称",
          icon: "none"
        });
        return;
      }
      if (userForm.email && !isValidEmail(userForm.email)) {
        common_vendor.index.showToast({
          title: "请输入正确的邮箱格式",
          icon: "none"
        });
        return;
      }
      try {
        saving.value = true;
        common_vendor.index.showLoading({
          title: "保存中...",
          mask: true
        });
        const updateData = {
          headImgUrl: userForm.headImgUrl,
          nickname: userForm.nickname.trim(),
          email: userForm.email.trim()
        };
        const res = await api_index.updateUserInfoApi(updateData);
        if (res.code === 0) {
          const updatedUserInfo = { ...userStore.userInfo, ...updateData };
          userStore.set_user_info(updatedUserInfo);
          common_vendor.index.showToast({
            title: "保存成功",
            icon: "success"
          });
          setTimeout(() => {
            common_vendor.index.navigateBack();
          }, 1500);
        } else {
          throw new Error(res.msg || "保存失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/profile/index.vue:172", "保存失败:", error);
        common_vendor.index.showToast({
          title: error.message || "保存失败",
          icon: "none"
        });
      } finally {
        saving.value = false;
        common_vendor.index.hideLoading();
      }
    };
    const isValidEmail = (email) => {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return emailRegex.test(email);
    };
    return (_ctx, _cache) => {
      return {
        a: common_vendor.unref(userForm).headImgUrl,
        b: common_assets._imports_0$1,
        c: common_vendor.o(chooseAvatar),
        d: common_vendor.unref(userForm).nickname,
        e: common_vendor.o(($event) => common_vendor.unref(userForm).nickname = $event.detail.value),
        f: common_vendor.unref(userForm).email,
        g: common_vendor.o(($event) => common_vendor.unref(userForm).email = $event.detail.value),
        h: common_vendor.t(saving.value ? "保存中..." : "保存修改"),
        i: common_vendor.o(handleSave),
        j: saving.value ? 1 : ""
      };
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-201c0da5"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/profile/index.js.map
