"use strict";const e=require("../../common/vendor.js"),a=require("../../common/assets.js"),t=require("../../stores/user.js"),n=require("../../api/index.js");if(!Array){e.resolveComponent("uv-tabs")()}Math;const o={__name:"square",setup(o){const i=t.useUserStore(),l=e.ref(0),u=e.ref([]),s=e.ref("calc(100vh - 88rpx)"),r=async()=>{let e=await n.getCategoryListApi({merchantGuid:i.merchantGuid});u.value=e.data,g.value=new Array(u.value.length),i.targetCategoryGuid?(p(i.targetCategoryGuid),i.clear_target_category()):c()},g=e.ref([]),c=async(e=null)=>{var a;if(u.value.length>0){const t=null!==e?e:l.value;let o=u.value[t].guid,s=await n.getAgentListApi({merchantGuid:i.merchantGuid,categoryGuid:o,pageSize:100});console.log("categoryAgentData before:",g.value),g.value.length<=t&&(g.value=[...g.value,...new Array(t+1-g.value.length)]),g.value[t]=s.data.data,console.log("categoryAgentData after:",g.value),console.log("categoryAgentData length:",null==(a=g.value[t])?void 0:a.length)}},d=e=>{console.log("handleTabChange",e),l.value=e.index,g.value[e.index]||c(e.index)},v=e=>{l.value=e.detail.current,g.value[e.detail.current]||c(e.detail.current)},h=()=>{console.log("点击搜索"),e.index.navigateTo({url:"/pages/square/search"})},m=a=>{console.log("点击智能体:",a.agentName),e.index.navigateTo({url:`/pages/square/detail?sysId=${a.sysId}`})};e.onShareAppMessage((()=>({title:i.appName||"智能体",path:`/pages/square/square?invite=${i.invitationCode}`,success(a){console.log("userStore.invitationCode",i.invitationCode),e.index.showToast({title:"分享成功"})},fail(a){e.index.showToast({title:"分享失败",icon:"none"})}})));const p=e=>{const a=(e=>u.value.findIndex((a=>a.guid===e)))(e);-1!==a&&(l.value=a,g.value[a]||c(a))};return e.onShow((()=>{e.nextTick$1((()=>{e.index.getSystemInfo({success:e=>{const a=e.windowHeight-44,t=Math.max(a,600);s.value=`${t}px`,console.log("Container height set to:",s.value)},fail:()=>{s.value="600px"}})})),i.targetCategoryGuid?(console.log(u.value,"tabsList.valuetabsList.value"),u.value.length>0?(p(i.targetCategoryGuid),i.clear_target_category()):r()):r()})),e.watch((()=>i.userToken),((e,a)=>{console.log("userToken changed",e,a),e&&""===a&&r()})),(t,o)=>({a:a._imports_0$9,b:e.o(h),c:e.o(d),d:e.p({list:u.value,current:l.value,lineColor:"#222222",keyName:"categoryName"}),e:e.f(u.value,((a,t,o)=>{var u,s;return e.e({a:e.f(g.value[t]||[],((a,t,o)=>({a:a.agentAvatar,b:e.o((e=>m(a)),a.guid),c:e.t(a.agentName),d:e.t(a.agentDesc),e:e.t(a.creator.nickname),f:e.o((e=>m(a)),a.guid),g:e.t(a.isSubscribed?"已合伙":"去招募"),h:a.isSubscribed?1:"",i:e.o((t=>(async a=>{if(a.isSubscribed)e.index.navigateTo({url:`/pages/msg/index?sessionGuid=${a.guid}`});else{let o={merchantGuid:i.merchantGuid,agentGuid:a.guid};try{await n.subscribeAgentApi(o),c(l.value),e.index.showToast({title:"招募成功",icon:"none"})}catch(t){e.index.showToast({title:"招募失败",icon:"none"})}}})(a)),a.guid),j:a.guid}))),b:0===(null==(u=g.value[t])?void 0:u.length)},(null==(s=g.value[t])||s.length,{}),{c:a.guid})})),f:s.value,g:s.value,h:l.value,i:e.o(v),j:s.value,k:s.value})}},i=e._export_sfc(o,[["__scopeId","data-v-9a5bbf16"]]);o.__runtimeHooks=6,wx.createPage(i);
