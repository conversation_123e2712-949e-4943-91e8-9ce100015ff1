"use strict";
const common_vendor = require("../common/vendor.js");
const randomStr = function(num, maxA, minlA, fqy) {
  let arr = [];
  let arr1 = [];
  let arr2 = [];
  if (num) {
    for (let m = 0; m <= 9; m++) {
      arr.push(m);
    }
  }
  if (maxA) {
    for (let m = 65; m <= 90; m++) {
      arr1.push(m);
    }
  }
  if (minlA) {
    for (let m = 97; m <= 122; m++) {
      arr2.push(m);
    }
  }
  if (!fqy) {
    common_vendor.index.__f__("log", "at utils/requestUtil.js:29", "生成位数必传");
    return;
  }
  let mergeArr = arr.concat(arr1);
  let mergeArr1 = mergeArr.concat(arr2);
  let _length = mergeArr1.length;
  let text = "";
  for (let m = 0; m < fqy; m++) {
    let text1 = "";
    let random = randomNum(0, _length);
    if (mergeArr1[random] <= 9) {
      text1 = mergeArr1[random];
    } else if (mergeArr1[random] > 9) {
      text1 = String.fromCharCode(mergeArr1[random]);
    }
    text += text1;
  }
  return text;
};
const randomNum = function(a, b) {
  var max = a;
  var min = b;
  if (a < b) {
    max = b;
    min = a;
  }
  return parseInt(Math.random() * (max - min)) + min;
};
var MD5 = new common_vendor.Hashes.MD5();
var Hash = new common_vendor.Hashes.SHA1();
const autographFun = (configData) => {
  let config = configData;
  let signData;
  let queryData = {
    app_guid: config.urlSuffix.app_guid,
    app_type: config.urlSuffix.app_type,
    token: config.urlSuffix.token
  };
  let queryMd5 = MD5.hex(JSON.stringify(queryData));
  if (config.method === "POST") {
    let dataMd5 = config.data ? MD5.hex(JSON.stringify(config.data)) : "";
    signData = config.method + "\n" + dataMd5 + "\n" + queryMd5 + "\napplication/json\n" + config.urlSuffix.expires + "\n" + config.urlSuffix.noncestr + "\n/" + config.url.toLowerCase();
  } else {
    signData = config.method + "\n" + queryMd5 + "\n" + config.urlSuffix.expires + "\n" + config.urlSuffix.noncestr + "\n/" + config.url.toLowerCase();
  }
  return Hash.b64_hmac(config.urlSuffix.token, signData);
};
exports.autographFun = autographFun;
exports.randomStr = randomStr;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/requestUtil.js.map
