{"version": 3, "file": "index.js", "sources": ["pages/index/index.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvaW5kZXgvaW5kZXgudnVl"], "sourcesContent": ["<template>\r\n\t<view class=\"container\">\r\n\t\t<!-- 顶部问候区域 -->\r\n\t\t<view class=\"greeting-section\">\r\n\t\t\t<view class=\"greeting-text\">{{ fullGreeting }}</view>\r\n\t\t\t<view class=\"notification-icon\">\r\n\t\t\t\t<image src=\"@/static/index/<EMAIL>\" class=\"bell-icon\" mode=\"aspectFit\"></image>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<view class=\"recommend-section\">\r\n\t\t\t<view class=\"section-title\">推荐\r\n\t\t\t\t<view class=\"sq-btn\" @click=\"onShowPoster\">\r\n\t\t\t\t\t<image class=\"bg\" :src=\"sqbg\"></image> 进入社区 <image class=\"arrow\" src=\"@/static/index/blue-arrow-icon.png\">\r\n\t\t\t\t\t</image>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"recommend-swiper-container\">\r\n\t\t\t\t<!-- :skip-hidden-item-layout=\"true\" -->\r\n\t\t\t\t<swiper class=\"recommend-swiper\" :indicator-dots=\"false\" :autoplay=\"false\" :circular=\"false\" next-margin=\"60px\"\r\n\t\t\t\t\t:skip-hidden-item-layout=\"true\">\r\n\t\t\t\t\t<swiper-item v-for=\"(item, index) in recommendAgents\" :key=\"item.guid\">\r\n\t\t\t\t\t\t<view class=\"recommend-card\" @click=\"handleCardClick(item.agentName)\">\r\n\t\t\t\t\t\t\t<view class=\"card-content\">\r\n\t\t\t\t\t\t\t\t<view class=\"card-title\">\r\n\t\t\t\t\t\t\t\t\t<image :src=\"item.agentAvatar\" class=\"icon\" mode=\"aspectFit\"></image> {{ item.agentName }}\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"card-desc\">{{ item.agentDesc }}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"card-author\">@{{ item.creator.nickname }}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<!-- <view class=\"card-icon\">\r\n\t\t\t\t\t\t\t\t<image :src=\"item.agentAvatar\" class=\"icon\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t\t\t</view> -->\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</swiper-item>\r\n\t\t\t\t</swiper>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 分类标签区域 -->\r\n\t\t<view class=\"category-section\">\r\n\t\t\t<view class=\"section-title\">\r\n\t\t\t\t热门\r\n\t\t\t\t<view class=\"more-btn\" @click=\"handleMoreLick\">探索更多 <image class=\"icon\"\r\n\t\t\t\t\t\tsrc=\"@/static/index/<EMAIL>\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"category-tags\">\r\n\t\t\t\t<view class=\"tag-item\" v-for=\"item in recommendCategories\" :key=\"item.guid\" @click=\"handleTagClick(item.guid)\">\r\n\t\t\t\t\t{{ item.categoryName }}</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- AI智能体众创计划横幅 -->\r\n\t\t<view class=\"banner-section\">\r\n\t\t\t<view class=\"ai-banner\">\r\n\t\t\t\t<!-- <view class=\"banner-bg\">\r\n\t\t\t\t\t<image :src=\"bannerList[0]\" class=\"banner-image\" mode=\"aspectFill\"></image>\r\n\t\t\t\t</view> -->\r\n\t\t\t\t<swiper class=\"swiper\" :autoplay=\"true\" :interval=\"3000\">\r\n\t\t\t\t\t<swiper-item v-for=\"(item, index) in indexBanner\" :key=\"index\" @click=\"onLinkTo(item)\">\r\n\t\t\t\t\t\t<image :src=\"item.bannerImg\" class=\"banner-image\" mode=\"aspectFill\"></image>\r\n\t\t\t\t\t</swiper-item>\r\n\t\t\t\t</swiper>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<view class=\"creator-section\">\r\n\t\t\t<view class=\"section-title\">最近</view>\r\n\t\t\t<view class=\"creator-list\">\r\n\t\t\t\t<view class=\"creator-item\" v-for=\"item in systemNotices\" :key=\"item.guid\" @click=\"handleCreatorClick(item)\">\r\n\t\t\t\t\t<view class=\"creator-icon\">\r\n\t\t\t\t\t\t<image :src=\"icons[item.type]\" class=\"icon\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"creator-content\">\r\n\t\t\t\t\t\t<view class=\"creator-title\">{{ item.title }}</view>\r\n\t\t\t\t\t\t<view class=\"creator-desc\">{{ item.content }}</view>\r\n\t\t\t\t\t\t<!-- <view class=\"creator-stats\">{{ item.typeText }}</view> -->\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n\r\n\t<!-- 图片弹窗 -->\r\n\t<view v-if=\"showImageModal\" class=\"image-modal-overlay\" @click=\"closeImageModal\">\r\n\t\t<view class=\"image-modal-content\">\r\n\t\t\t<!-- 图片 -->\r\n\t\t\t<image v-if=\"shenqun_img\" :src=\"shenqun_img\" class=\"modal-image\" mode=\"aspectFit\" @load=\"onImageLoad\"\r\n\t\t\t\t@error=\"onImageError\" show-menu-by-longpress />\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script setup>\r\nimport {\r\n\tref,\r\n\twatch\r\n} from 'vue';\r\nimport {\r\n\tonLoad,\r\n\tonShareAppMessage,\r\n\tonShareTimeline\r\n} from '@dcloudio/uni-app';\r\nimport {\r\n\tuseUserStore\r\n} from '@/stores/user.js'\r\nimport {\r\n\tgetHomeDataApi,\r\n\tshowBannerUrlsApi,\r\n\tgetBannerListApi\r\n} from '@/api';\r\nimport base from '@/config/config.js';\r\nimport system from '@/static/index/<EMAIL>';\r\nimport activity from '@/static/index/<EMAIL>';\r\nimport maintenance from '@/static/index/<EMAIL>';\r\nlet update = 'https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/a2b6d4b5e3374fc79f54819c5f5c09e3.png'\r\n\r\nconst userStore = useUserStore()\r\n// let banner = 'https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/99b88efa86b340a988adf95fcf3952b7.png'\r\nconst sqbg = 'https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/eb2cbe34cdce4cda956ba6715110ee38.png'\r\n\r\nconst icons = {\r\n\tmaintenance,\r\n\tactivity,\r\n\tsystem,\r\n\tupdate\r\n}\r\n// 获取banner列表\r\nlet indexBanner = ref([])\r\nconst getBannerList = async () => {\r\n\tlet res = await getBannerListApi({\r\n\t\tmerchantGuid: userStore.merchantGuid,\r\n\t\tbannerType: 'user_pay_agent'\r\n\t})\r\n\tindexBanner.value = res.data;\r\n}\r\nconst onLinkTo = (item) => {\r\n\tvar tabPages = ['pages/index/index', 'pages/msg-list/msg', 'pages/square/square', 'pages/my/my'];\r\n\tswitch (item.linkType) {\r\n\t\tcase 1:\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: 'pages/webview/webview',\r\n\t\t\t\tsuccess: function success(res) {\r\n\t\t\t\t\tres.eventChannel.emit('urlEvent', item.linkUrl);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\tbreak;\r\n\t\tcase 2:\r\n\t\t\tif (tabPages.includes(item.linkUrl)) {\r\n\t\t\t\tuni.switchTab({\r\n\t\t\t\t\turl: '/' + item.linkUrl\r\n\t\t\t\t});\r\n\t\t\t} else {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/' + item.linkUrl\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t\tbreak;\r\n\t\tcase 3:\r\n\t\t\tuni.navigateToMiniProgram({\r\n\t\t\t\tappId: item.linkUrl,\r\n\t\t\t\tfail: function fail(err) {\r\n\t\t\t\t\tuni.$u.toast('跳转失败...');\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\tbreak;\r\n\t\tdefault:\r\n\t\t\tbreak;\r\n\t}\r\n}\r\n// 点击事件处理\r\nconst handleCardClick = (agentName) => {\r\n\t// 跳转到广场搜索页面，传递sysId参数，自动搜索后跳转到详情页\r\n\tuni.navigateTo({\r\n\t\turl: `/pages/square/search?agentName=${agentName}`\r\n\t})\r\n}\r\n\r\nconst handleMoreLick = () => {\r\n\tuni.switchTab({\r\n\t\turl: '/pages/square/square'\r\n\t})\r\n}\r\n\r\n\r\nconst handleTagClick = (categoryGuid) => {\r\n\tconsole.log('点击标签:', categoryGuid)\r\n\t// 设置目标分类到全局状态\r\n\tuserStore.set_target_category(categoryGuid)\r\n\t// 跳转到广场页面\r\n\tuni.switchTab({\r\n\t\turl: '/pages/square/square'\r\n\t})\r\n}\r\n\r\n// const handleBannerClick = () => {\r\n// \t// 这里可以添加跳转逻辑\r\n// \tuni.navigateTo({\r\n// \t\turl: '/pages/rule/cz-index'\r\n// \t})\r\n// }\r\n\r\nconst handleCreatorClick = (item) => {\r\n\tconsole.log('点击创作者:', item)\r\n\t// 这里可以添加跳转逻辑\r\n\tvar tabPages = ['pages/index/index', 'pages/msg-list/msg', 'pages/square/square', 'pages/my/my'];\r\n\tswitch (item.jumpType) {\r\n\t\tcase 1:\r\n\t\t\tif (tabPages.includes(item.link)) {\r\n\t\t\t\tuni.switchTab({\r\n\t\t\t\t\turl: '/' + item.link\r\n\t\t\t\t});\r\n\t\t\t} else {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/' + item.link\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t\tbreak;\r\n\t\tcase 2:\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: 'pages/webview/webview',\r\n\t\t\t\tsuccess: function success(res) {\r\n\t\t\t\t\tres.eventChannel.emit('urlEvent', item.link);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\tbreak;\r\n\t\tcase 3:\r\n\t\t\tuni.navigateToMiniProgram({\r\n\t\t\t\tappId: item.link,\r\n\t\t\t\tfail: function fail(err) {\r\n\t\t\t\t\tuni.$u.toast('跳转失败...');\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\tbreak;\r\n\t\tdefault:\r\n\t\t\tbreak;\r\n\t}\r\n}\r\n\r\n// 页面加载时的初始化（如果需要的话）\r\n// onLoad(() => {\r\n// \t// 初始化逻辑\r\n// })\r\nonShareAppMessage(() => {\r\n\treturn {\r\n\t\ttitle: userStore.appName || '智能体',\r\n\t\tpath: `/pages/index/index?invite=${userStore.invitationCode}`,\r\n\t\timageUrl: bannerList.value[0] || base.shareImg,\r\n\t\tsuccess(res) {\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '分享成功'\r\n\t\t\t})\r\n\t\t},\r\n\t\tfail(res) {\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '分享失败',\r\n\t\t\t\ticon: 'none'\r\n\t\t\t})\r\n\t\t}\r\n\t}\r\n})\r\n// 分享到朋友圈功能\r\nonShareTimeline(() => {\r\n\treturn {\r\n\t\ttitle: userStore.appName || '智能体',\r\n\t\tpath: `/pages/index/index?invite=${userStore.invitationCode}`,\r\n\t\timageUrl: bannerList.value[0] || base.shareImg,\r\n\t\tsuccess(res) {\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '分享成功'\r\n\t\t\t})\r\n\t\t},\r\n\t\tfail(res) {\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '分享失败',\r\n\t\t\t\ticon: 'none'\r\n\t\t\t})\r\n\t\t}\r\n\t}\r\n})\r\n// const showConfigs = async () => {\r\n// \tlet res = await showConfigsApi({\r\n// \t\tmerchantGuid: userStore.merchantGuid\r\n// \t})\r\n// \tuni.setNavigationBarTitle({\r\n// \t\ttitle: res.data.zhanhuiName || 'AI商协通'\r\n// \t})\r\n// }\r\nlet fullGreeting = ref('')\r\nlet recommendAgents = ref([])\r\nlet recommendCategories = ref([])\r\nlet systemNotices = ref([])\r\nlet bannerList = ref([])\r\n// 获取首页数据\r\nconst getHomeData = async () => {\r\n\tlet res = await getHomeDataApi({\r\n\t\tmerchantGuid: userStore.merchantGuid\r\n\t})\r\n\tfullGreeting.value = res.data.dailyGreeting.fullGreeting;\r\n\trecommendAgents.value = res.data.recommendAgents;\r\n\tsystemNotices.value = res.data.systemNotices;\r\n\trecommendCategories.value = res.data.recommendCategories;\r\n\tbannerList.value = res.data.bannerList;\r\n}\r\nconst shenqun_img = ref('')\r\nconst showImageModal = ref(false)\r\n\r\nconst showBannerUrls = async () => {\r\n\tlet res = await showBannerUrlsApi({\r\n\t\tmerchantGuid: userStore.merchantGuid\r\n\t})\r\n\tshenqun_img.value = res.data.shenqun_img;\r\n}\r\n\r\n// 显示图片弹窗\r\nconst onShowPoster = () => {\r\n\tshowImageModal.value = true\r\n}\r\n\r\n// 关闭图片弹窗\r\nconst closeImageModal = () => {\r\n\tshowImageModal.value = false;\r\n}\r\n\r\n\r\nonLoad(() => {\r\n\t// if (userStore.userToken) {\r\n\tgetHomeData();\r\n\tshowBannerUrls()\r\n\tgetBannerList()\r\n\t// }\r\n});\r\nwatch(\r\n\t() => userStore.userToken,\r\n\t(newValue, oldValue) => {\r\n\t\tif (newValue && oldValue === '') {\r\n\t\t\tgetHomeData();\r\n\t\t\tshowBannerUrls()\r\n\t\t}\r\n\t}\r\n);\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.container {\r\n\tbackground-color: #ffffff;\r\n\tmin-height: 100vh;\r\n\tpadding: 0 16px;\r\n}\r\n\r\n/* 顶部问候区域 */\r\n.greeting-section {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tpadding: 20px 0 16px;\r\n\r\n\t.greeting-text {\r\n\t\tfont-size: 30rpx;\r\n\t\tfont-weight: 500;\r\n\t\tcolor: #66648A;\r\n\t\t// overflow: hidden;\r\n\t\t// text-overflow: ellipsis;\r\n\t\t// white-space: nowrap;\r\n\t}\r\n\r\n\t.notification-icon {\r\n\t\t.bell-icon {\r\n\t\t\twidth: 60rpx;\r\n\t\t\theight: 60rpx;\r\n\t\t\tdisplay: block;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n/* 推荐卡片区域 */\r\n.recommend-section {\r\n\tmargin-bottom: 24px;\r\n\r\n\t.section-title {\r\n\t\tfont-size: 34rpx;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: #222;\r\n\t\tmargin-bottom: 20rpx;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\r\n\t\t.sq-btn {\r\n\t\t\twidth: 189rpx;\r\n\t\t\theight: 55rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tbackground: #E8ECFC;\r\n\t\t\tborder-radius: 28rpx;\r\n\t\t\tposition: relative;\r\n\t\t\tfont-size: 20rpx;\r\n\t\t\tcolor: #4F72F6;\r\n\t\t\tjustify-content: flex-end;\r\n\r\n\t\t\t.arrow {\r\n\t\t\t\tdisplay: block;\r\n\t\t\t\twidth: 30rpx;\r\n\t\t\t\theight: 30rpx;\r\n\t\t\t\tmargin-left: 2px;\r\n\t\t\t}\r\n\r\n\t\t\t.bg {\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\ttop: -30rpx;\r\n\t\t\t\tleft: 0;\r\n\t\t\t\twidth: 81rpx;\r\n\t\t\t\theight: 81rpx;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t}\r\n\r\n\t.recommend-swiper-container {\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.recommend-swiper {\r\n\t\theight: 140px;\r\n\r\n\t\tswiper-item {\r\n\t\t\tpadding-right: 12px;\r\n\t\t\tbox-sizing: border-box;\r\n\t\t}\r\n\r\n\t\t.recommend-card {\r\n\t\t\tbackground: #F8F9FA;\r\n\t\t\tborder-radius: 12px;\r\n\t\t\tpadding: 16px;\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-direction: column;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\theight: 100%;\r\n\t\t\tbox-sizing: border-box;\r\n\t\t\twidth: calc(100% - 12px);\r\n\t\t\tmargin-right: 12px;\r\n\r\n\t\t\t.card-content {\r\n\t\t\t\tflex: 1;\r\n\r\n\t\t\t\t.card-title {\r\n\t\t\t\t\tfont-size: 16px;\r\n\t\t\t\t\tfont-weight: 600;\r\n\t\t\t\t\tcolor: #333;\r\n\t\t\t\t\tmargin-bottom: 8px;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\r\n\t\t\t\t\t.icon {\r\n\t\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\t\tmargin-right: 15rpx;\r\n\t\t\t\t\t\twidth: 60rpx;\r\n\t\t\t\t\t\theight: 60rpx;\r\n\t\t\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.card-desc {\r\n\t\t\t\t\tfont-size: 12px;\r\n\t\t\t\t\tcolor: #666;\r\n\t\t\t\t\tline-height: 1.4;\r\n\t\t\t\t\tmargin-bottom: 12px;\r\n\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\ttext-overflow: ellipsis;\r\n\t\t\t\t\tdisplay: -webkit-box;\r\n\t\t\t\t\t-webkit-line-clamp: 3;\r\n\t\t\t\t\tline-clamp: 3;\r\n\t\t\t\t\t-webkit-box-orient: vertical;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.card-author {\r\n\t\t\t\t\tfont-size: 11px;\r\n\t\t\t\t\tcolor: #999;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t// .card-icon {\r\n\t\t\t// \talign-self: flex-end;\r\n\t\t\t// \tmargin-top: 8px;\r\n\r\n\t\t\t// \t.icon {\r\n\t\t\t// \t\twidth: 32px;\r\n\t\t\t// \t\theight: 32px;\r\n\t\t\t// \t}\r\n\t\t\t// }\r\n\t\t}\r\n\t}\r\n}\r\n\r\n/* 分类标签区域 */\r\n.category-section {\r\n\tmargin-bottom: 24px;\r\n\r\n\t.section-title {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tfont-size: 34rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #222;\r\n\t\tmargin-bottom: 20rpx;\r\n\r\n\t\t.more-btn {\r\n\t\t\tfont-size: 30rpx;\r\n\t\t\tcolor: #999999;\r\n\t\t\tfont-weight: normal;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\r\n\t\t\t.icon {\r\n\t\t\t\twidth: 40rpx;\r\n\t\t\t\theight: 40rpx;\r\n\t\t\t\tdisplay: block;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.category-tags {\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tgap: 8px;\r\n\r\n\t\t.tag-item {\r\n\t\t\tpadding: 8px 16px;\r\n\t\t\tbackground: #F4F7FF;\r\n\t\t\tborder-radius: 20px;\r\n\t\t\tfont-size: 26rpx;\r\n\t\t\tcolor: #666;\r\n\t\t\tborder: 1px solid #E7EDFA;\r\n\t\t\tcolor: #5380F2;\r\n\r\n\t\t\t// &.active {\r\n\t\t\t// \tbackground: #007aff;\r\n\t\t\t// \tcolor: #fff;\r\n\t\t\t// \tborder-color: #007aff;\r\n\t\t\t// }\r\n\t\t}\r\n\t}\r\n}\r\n\r\n/* AI智能体众创计划横幅 */\r\n.banner-section {\r\n\tmargin-bottom: 24px;\r\n\r\n\t.ai-banner {\r\n\t\tposition: relative;\r\n\t\t// background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n\t\tborder-radius: 16px;\r\n\t\t// padding: 24px;\r\n\t\toverflow: hidden;\r\n\t\tmin-height: 380rpx;\r\n\r\n\t\t.banner-content {\r\n\t\t\tposition: relative;\r\n\t\t\tz-index: 2;\r\n\r\n\t\t\t.banner-title {\r\n\t\t\t\tfont-size: 24px;\r\n\t\t\t\tfont-weight: 700;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tmargin-bottom: 8px;\r\n\t\t\t}\r\n\r\n\t\t\t.banner-subtitle {\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\tcolor: rgba(255, 255, 255, 0.9);\r\n\t\t\t\tmargin-bottom: 16px;\r\n\t\t\t\tline-height: 1.4;\r\n\t\t\t}\r\n\r\n\t\t\t.banner-btn {\r\n\t\t\t\tdisplay: inline-block;\r\n\t\t\t\tbackground: rgba(255, 255, 255, 0.2);\r\n\t\t\t\tborder: 1px solid rgba(255, 255, 255, 0.3);\r\n\t\t\t\tborder-radius: 20px;\r\n\t\t\t\tpadding: 8px 20px;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tbackdrop-filter: blur(10px);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.swiper {\r\n\t\t\theight: 380rpx;\r\n\t\t}\r\n\r\n\t\t.banner-image {\r\n\t\t\theight: 100%;\r\n\t\t\twidth: 100%;\r\n\t\t}\r\n\r\n\t\t// .banner-bg {\r\n\t\t// \tposition: absolute;\r\n\t\t// \ttop: 0;\r\n\t\t// \tright: 0;\r\n\t\t// \tbottom: 0;\r\n\t\t// \tleft: 0;\r\n\t\t// \tz-index: 1;\r\n\r\n\t\t// \t.banner-image {\r\n\t\t// \t\twidth: 100%;\r\n\t\t// \t\theight: 100%;\r\n\t\t// \t}\r\n\t\t// }\r\n\t}\r\n}\r\n\r\n/* 创作大神推荐列表 */\r\n.creator-section {\r\n\tpadding-bottom: 30rpx;\r\n\r\n\t.section-title {\r\n\t\tfont-size: 20px;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: #333;\r\n\t\tmargin-bottom: 16px;\r\n\t}\r\n\r\n\t.creator-list {\r\n\t\t.creator-item {\r\n\t\t\tbackground: #F8F9FA;\r\n\t\t\tborder-radius: 12px;\r\n\t\t\tpadding: 16px;\r\n\t\t\tmargin-bottom: 12px;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tgap: 12px;\r\n\r\n\t\t\t.creator-icon {\r\n\t\t\t\t.icon {\r\n\t\t\t\t\twidth: 60rpx;\r\n\t\t\t\t\theight: 60rpx;\r\n\t\t\t\t\tborder-radius: 8px;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.creator-content {\r\n\t\t\t\tflex: 1;\r\n\r\n\t\t\t\t.creator-title {\r\n\t\t\t\t\twidth: 540rpx;\r\n\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\tfont-weight: 600;\r\n\t\t\t\t\tcolor: #333;\r\n\t\t\t\t\tmargin-bottom: 8px;\r\n\t\t\t\t\tline-height: 1.3;\r\n\t\t\t\t\ttext-overflow: ellipsis;\r\n\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\twhite-space: nowrap;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.creator-desc {\r\n\t\t\t\t\twidth: 560rpx;\r\n\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\tcolor: #999;\r\n\t\t\t\t\tline-height: 1.4;\r\n\t\t\t\t\t// overflow: hidden;\r\n\t\t\t\t\t// text-overflow: ellipsis;\r\n\t\t\t\t\t// white-space: nowrap;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.creator-stats {\r\n\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\tcolor: #999;\r\n\t\t\t\t\tmargin-top: 4px;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n/* 图片弹窗样式 */\r\n.image-modal-overlay {\r\n\tposition: fixed;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\twidth: 100%;\r\n\theight: 100%;\r\n\tbackground-color: rgba(0, 0, 0, 0.8);\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tz-index: 9999;\r\n}\r\n\r\n.image-modal-content {\r\n\twidth: 90%;\r\n\theight: 80vh;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tposition: relative;\r\n}\r\n\r\n.modal-image {\r\n\tmax-width: 100%;\r\n\tmax-height: 200px;\r\n\twidth: 100%;\r\n\theight: 300px;\r\n\tdisplay: block;\r\n\tobject-fit: contain;\r\n}\r\n</style>", "import MiniProgramPage from 'E:/youngProject/agent-mini-ui/pages/index/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useUserStore", "maintenance", "activity", "system", "ref", "getBannerListApi", "uni", "onShareAppMessage", "base", "onShareTimeline", "getHomeDataApi", "showBannerUrlsApi", "onLoad", "watch"], "mappings": ";;;;;;AAwHA,MAAA,OAAA;;;;AAJA,QAAA,SAAA;AAEA,UAAA,YAAAA,YAAAA,aAAA;AAIA,UAAA,QAAA;AAAA,MACA,aAAAC,cAAA;AAAA,MACA,UAAAC,cAAA;AAAA,MACA,QAAAC,cAAA;AAAA,MACA;AAAA,IACA;AAEA,QAAA,cAAAC,cAAA,IAAA,EAAA;AACA,UAAA,gBAAA,YAAA;AACA,UAAA,MAAA,MAAAC,2BAAA;AAAA,QACA,cAAA,UAAA;AAAA,QACA,YAAA;AAAA,MACA,CAAA;AACA,kBAAA,QAAA,IAAA;AAAA,IACA;AACA,UAAA,WAAA,CAAA,SAAA;AACA,UAAA,WAAA,CAAA,qBAAA,sBAAA,uBAAA,aAAA;AACA,cAAA,KAAA,UAAA;AAAA,QACA,KAAA;AACAC,wBAAAA,MAAA,WAAA;AAAA,YACA,KAAA;AAAA,YACA,SAAA,SAAA,QAAA,KAAA;AACA,kBAAA,aAAA,KAAA,YAAA,KAAA,OAAA;AAAA,YACA;AAAA,UACA,CAAA;AACA;AAAA,QACA,KAAA;AACA,cAAA,SAAA,SAAA,KAAA,OAAA,GAAA;AACAA,0BAAAA,MAAA,UAAA;AAAA,cACA,KAAA,MAAA,KAAA;AAAA,YACA,CAAA;AAAA,UACA,OAAA;AACAA,0BAAAA,MAAA,WAAA;AAAA,cACA,KAAA,MAAA,KAAA;AAAA,YACA,CAAA;AAAA,UACA;AACA;AAAA,QACA,KAAA;AACAA,wBAAAA,MAAA,sBAAA;AAAA,YACA,OAAA,KAAA;AAAA,YACA,MAAA,SAAA,KAAA,KAAA;AACAA,4BAAAA,MAAA,GAAA,MAAA,SAAA;AAAA,YACA;AAAA,UACA,CAAA;AACA;AAAA,MAGA;AAAA,IACA;AAEA,UAAA,kBAAA,CAAA,cAAA;AAEAA,oBAAAA,MAAA,WAAA;AAAA,QACA,KAAA,kCAAA,SAAA;AAAA,MACA,CAAA;AAAA,IACA;AAEA,UAAA,iBAAA,MAAA;AACAA,oBAAAA,MAAA,UAAA;AAAA,QACA,KAAA;AAAA,MACA,CAAA;AAAA,IACA;AAGA,UAAA,iBAAA,CAAA,iBAAA;AACAA,oBAAAA,MAAA,MAAA,OAAA,gCAAA,SAAA,YAAA;AAEA,gBAAA,oBAAA,YAAA;AAEAA,oBAAAA,MAAA,UAAA;AAAA,QACA,KAAA;AAAA,MACA,CAAA;AAAA,IACA;AASA,UAAA,qBAAA,CAAA,SAAA;AACAA,oBAAAA,MAAA,MAAA,OAAA,gCAAA,UAAA,IAAA;AAEA,UAAA,WAAA,CAAA,qBAAA,sBAAA,uBAAA,aAAA;AACA,cAAA,KAAA,UAAA;AAAA,QACA,KAAA;AACA,cAAA,SAAA,SAAA,KAAA,IAAA,GAAA;AACAA,0BAAAA,MAAA,UAAA;AAAA,cACA,KAAA,MAAA,KAAA;AAAA,YACA,CAAA;AAAA,UACA,OAAA;AACAA,0BAAAA,MAAA,WAAA;AAAA,cACA,KAAA,MAAA,KAAA;AAAA,YACA,CAAA;AAAA,UACA;AACA;AAAA,QACA,KAAA;AACAA,wBAAAA,MAAA,WAAA;AAAA,YACA,KAAA;AAAA,YACA,SAAA,SAAA,QAAA,KAAA;AACA,kBAAA,aAAA,KAAA,YAAA,KAAA,IAAA;AAAA,YACA;AAAA,UACA,CAAA;AACA;AAAA,QACA,KAAA;AACAA,wBAAAA,MAAA,sBAAA;AAAA,YACA,OAAA,KAAA;AAAA,YACA,MAAA,SAAA,KAAA,KAAA;AACAA,4BAAAA,MAAA,GAAA,MAAA,SAAA;AAAA,YACA;AAAA,UACA,CAAA;AACA;AAAA,MAGA;AAAA,IACA;AAMAC,kBAAAA,kBAAA,MAAA;AACA,aAAA;AAAA,QACA,OAAA,UAAA,WAAA;AAAA,QACA,MAAA,6BAAA,UAAA,cAAA;AAAA,QACA,UAAA,WAAA,MAAA,CAAA,KAAAC,cAAA,KAAA;AAAA,QACA,QAAA,KAAA;AACAF,wBAAAA,MAAA,UAAA;AAAA,YACA,OAAA;AAAA,UACA,CAAA;AAAA,QACA;AAAA,QACA,KAAA,KAAA;AACAA,wBAAAA,MAAA,UAAA;AAAA,YACA,OAAA;AAAA,YACA,MAAA;AAAA,UACA,CAAA;AAAA,QACA;AAAA,MACA;AAAA,IACA,CAAA;AAEAG,kBAAAA,gBAAA,MAAA;AACA,aAAA;AAAA,QACA,OAAA,UAAA,WAAA;AAAA,QACA,MAAA,6BAAA,UAAA,cAAA;AAAA,QACA,UAAA,WAAA,MAAA,CAAA,KAAAD,cAAA,KAAA;AAAA,QACA,QAAA,KAAA;AACAF,wBAAAA,MAAA,UAAA;AAAA,YACA,OAAA;AAAA,UACA,CAAA;AAAA,QACA;AAAA,QACA,KAAA,KAAA;AACAA,wBAAAA,MAAA,UAAA;AAAA,YACA,OAAA;AAAA,YACA,MAAA;AAAA,UACA,CAAA;AAAA,QACA;AAAA,MACA;AAAA,IACA,CAAA;AASA,QAAA,eAAAF,cAAA,IAAA,EAAA;AACA,QAAA,kBAAAA,cAAA,IAAA,EAAA;AACA,QAAA,sBAAAA,cAAA,IAAA,EAAA;AACA,QAAA,gBAAAA,cAAA,IAAA,EAAA;AACA,QAAA,aAAAA,cAAA,IAAA,EAAA;AAEA,UAAA,cAAA,YAAA;AACA,UAAA,MAAA,MAAAM,yBAAA;AAAA,QACA,cAAA,UAAA;AAAA,MACA,CAAA;AACA,mBAAA,QAAA,IAAA,KAAA,cAAA;AACA,sBAAA,QAAA,IAAA,KAAA;AACA,oBAAA,QAAA,IAAA,KAAA;AACA,0BAAA,QAAA,IAAA,KAAA;AACA,iBAAA,QAAA,IAAA,KAAA;AAAA,IACA;AACA,UAAA,cAAAN,cAAA,IAAA,EAAA;AACA,UAAA,iBAAAA,cAAA,IAAA,KAAA;AAEA,UAAA,iBAAA,YAAA;AACA,UAAA,MAAA,MAAAO,4BAAA;AAAA,QACA,cAAA,UAAA;AAAA,MACA,CAAA;AACA,kBAAA,QAAA,IAAA,KAAA;AAAA,IACA;AAGA,UAAA,eAAA,MAAA;AACA,qBAAA,QAAA;AAAA,IACA;AAGA,UAAA,kBAAA,MAAA;AACA,qBAAA,QAAA;AAAA,IACA;AAGAC,kBAAAA,OAAA,MAAA;AAEA;AACA,qBAAA;AACA,oBAAA;AAAA,IAEA,CAAA;AACAC,kBAAA;AAAA,MACA,MAAA,UAAA;AAAA,MACA,CAAA,UAAA,aAAA;AACA,YAAA,YAAA,aAAA,IAAA;AACA;AACA,yBAAA;AAAA,QACA;AAAA,MACA;AAAA,IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpVA,GAAG,WAAW,eAAe;"}