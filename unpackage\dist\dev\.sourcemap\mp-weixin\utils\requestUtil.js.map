{"version": 3, "file": "requestUtil.js", "sources": ["utils/requestUtil.js"], "sourcesContent": ["import Hashes from 'jshashes'\r\n/**\r\n * randomStr 生成随机字符串\r\n */\r\nexport const randomStr = function(num, maxA, minlA, fqy) {\r\n\t// num:随机数中是否包含数字\r\n\t// maxA:随机数中是否包大写字母\r\n\t// minlA:随机数中是否包小写字母\r\n\t// fqy:生成多少位随机数\r\n\tlet arr = []\r\n\tlet arr1 = []\r\n\tlet arr2 = []\r\n\tif (num) {\r\n\t\tfor (let m = 0; m <= 9; m++) {\r\n\t\t\tarr.push(m)\r\n\t\t}\r\n\t}\r\n\tif (maxA) {\r\n\t\tfor (let m = 65; m <= 90; m++) {\r\n\t\t\tarr1.push(m)\r\n\t\t}\r\n\t}\r\n\tif (minlA) {\r\n\t\tfor (let m = 97; m <= 122; m++) {\r\n\t\t\tarr2.push(m)\r\n\t\t}\r\n\t}\r\n\tif (!fqy) {\r\n\t\tconsole.log('生成位数必传')\r\n\t\treturn\r\n\t}\r\n\tlet mergeArr = arr.concat(arr1);\r\n\tlet mergeArr1 = mergeArr.concat(arr2);\r\n\tlet _length = mergeArr1.length\r\n\tlet text = ''\r\n\tfor (let m = 0; m < fqy; m++) {\r\n\t\tlet text1 = ''\r\n\t\tlet random = randomNum(0, _length)\r\n\t\tif ((mergeArr1[random]) <= 9) {\r\n\t\t\ttext1 = mergeArr1[random]\r\n\t\t} else if ((mergeArr1[random]) > 9) {\r\n\t\t\ttext1 = String.fromCharCode(mergeArr1[random])\r\n\t\t}\r\n\t\ttext += text1\r\n\t}\r\n\treturn text\r\n};\r\n/**\r\n * randomNum 生成随机数\r\n * @param {number} max 最大\r\n * @param {number} mix 最小\r\n */\r\nexport const randomNum = function(a, b) {\r\n\tvar max = a;\r\n\tvar min = b;\r\n\tif (a < b) {\r\n\t\tmax = b;\r\n\t\tmin = a;\r\n\t}\r\n\treturn parseInt(Math.random() * (max - min)) + min;\r\n}\r\n\r\n// var Hashes = require('jshashes')\r\nvar MD5 = new Hashes.MD5;\r\nvar Hash = new Hashes.SHA1;\r\n\r\n// import { getSecretKey } from \"@/store/user/userConfig.js\"\r\n/**\r\n * autographFun 生成签名函数\r\n */\r\nexport const autographFun = (configData) => {\r\n\tlet config = configData;\r\n\tlet signData;\r\n\tlet queryData = {\r\n\t\tapp_guid: config.urlSuffix.app_guid,\r\n\t\tapp_type: config.urlSuffix.app_type,\r\n\t\ttoken: config.urlSuffix.token\r\n\t}\r\n\tlet queryMd5 = MD5.hex(JSON.stringify(queryData))\r\n\tif (config.method === 'POST') {\r\n\t\tlet dataMd5 = config.data ? MD5.hex(JSON.stringify(config.data)) : ''\r\n\t\tsignData = config.method + '\\n' + dataMd5 + '\\n' + queryMd5 + '\\n' + 'application/json' + '\\n' + config\r\n\t\t\t.urlSuffix.expires + '\\n' + config.urlSuffix.noncestr + '\\n' + '/' + (config.url.toLowerCase())\r\n\t} else {\r\n\t\tsignData = config.method + '\\n' + queryMd5 + '\\n' + config.urlSuffix.expires + '\\n' + config.urlSuffix\r\n\t\t\t.noncestr + '\\n' + '/' + (config.url.toLowerCase())\r\n\t}\r\n\t//签名key\r\n\t// let secretKey = store.state.secretKey || store.state.touristSecretKey || ''\r\n\t// if (!secretKey) {\r\n\t//     console.log('未进行任何登录')\r\n\t//     secretKeyFun()\r\n\t// }\r\n\t// let secretKey = getSecretKey() || ''\r\n\t// return Hash.b64_hmac(secretKey, signData)\r\n\treturn Hash.b64_hmac(config.urlSuffix.token, signData)\r\n\r\n}"], "names": ["uni", "<PERSON>hes"], "mappings": ";;AAIY,MAAC,YAAY,SAAS,KAAK,MAAM,OAAO,KAAK;AAKxD,MAAI,MAAM,CAAE;AACZ,MAAI,OAAO,CAAE;AACb,MAAI,OAAO,CAAE;AACb,MAAI,KAAK;AACR,aAAS,IAAI,GAAG,KAAK,GAAG,KAAK;AAC5B,UAAI,KAAK,CAAC;AAAA,IACV;AAAA,EACD;AACD,MAAI,MAAM;AACT,aAAS,IAAI,IAAI,KAAK,IAAI,KAAK;AAC9B,WAAK,KAAK,CAAC;AAAA,IACX;AAAA,EACD;AACD,MAAI,OAAO;AACV,aAAS,IAAI,IAAI,KAAK,KAAK,KAAK;AAC/B,WAAK,KAAK,CAAC;AAAA,IACX;AAAA,EACD;AACD,MAAI,CAAC,KAAK;AACTA,kBAAAA,MAAY,MAAA,OAAA,8BAAA,QAAQ;AACpB;AAAA,EACA;AACD,MAAI,WAAW,IAAI,OAAO,IAAI;AAC9B,MAAI,YAAY,SAAS,OAAO,IAAI;AACpC,MAAI,UAAU,UAAU;AACxB,MAAI,OAAO;AACX,WAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC7B,QAAI,QAAQ;AACZ,QAAI,SAAS,UAAU,GAAG,OAAO;AACjC,QAAK,UAAU,MAAM,KAAM,GAAG;AAC7B,cAAQ,UAAU,MAAM;AAAA,IACxB,WAAW,UAAU,MAAM,IAAK,GAAG;AACnC,cAAQ,OAAO,aAAa,UAAU,MAAM,CAAC;AAAA,IAC7C;AACD,YAAQ;AAAA,EACR;AACD,SAAO;AACR;AAMO,MAAM,YAAY,SAAS,GAAG,GAAG;AACvC,MAAI,MAAM;AACV,MAAI,MAAM;AACV,MAAI,IAAI,GAAG;AACV,UAAM;AACN,UAAM;AAAA,EACN;AACD,SAAO,SAAS,KAAK,OAAM,KAAM,MAAM,IAAI,IAAI;AAChD;AAGA,IAAI,MAAM,IAAIC,cAAM,OAAC;AACrB,IAAI,OAAO,IAAIA,cAAM,OAAC;AAMV,MAAC,eAAe,CAAC,eAAe;AAC3C,MAAI,SAAS;AACb,MAAI;AACJ,MAAI,YAAY;AAAA,IACf,UAAU,OAAO,UAAU;AAAA,IAC3B,UAAU,OAAO,UAAU;AAAA,IAC3B,OAAO,OAAO,UAAU;AAAA,EACxB;AACD,MAAI,WAAW,IAAI,IAAI,KAAK,UAAU,SAAS,CAAC;AAChD,MAAI,OAAO,WAAW,QAAQ;AAC7B,QAAI,UAAU,OAAO,OAAO,IAAI,IAAI,KAAK,UAAU,OAAO,IAAI,CAAC,IAAI;AACnE,eAAW,OAAO,SAAS,OAAO,UAAU,OAAO,WAAW,yBAAmC,OAC/F,UAAU,UAAU,OAAO,OAAO,UAAU,WAAW,QAAc,OAAO,IAAI,YAAW;AAAA,EAC/F,OAAQ;AACN,eAAW,OAAO,SAAS,OAAO,WAAW,OAAO,OAAO,UAAU,UAAU,OAAO,OAAO,UAC3F,WAAW,QAAc,OAAO,IAAI,YAAW;AAAA,EACjD;AASD,SAAO,KAAK,SAAS,OAAO,UAAU,OAAO,QAAQ;AAEtD;;;"}