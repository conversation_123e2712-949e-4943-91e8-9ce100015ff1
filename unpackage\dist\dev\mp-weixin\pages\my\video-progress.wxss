/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.video-progress-page.data-v-dbc65c27 {
  background: #F5F5F5;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 32rpx;
  box-sizing: border-box;
}
.video-progress-page .digital-person-container.data-v-dbc65c27 {
  margin-top: 120rpx;
  width: 340rpx;
  height: 602rpx;
  border-radius: 20rpx;
  overflow: hidden;
}
.video-progress-page .digital-person-container .person-image.data-v-dbc65c27 {
  width: 100%;
  height: 100%;
}
.video-progress-page .camera-container.data-v-dbc65c27 {
  margin-top: 94rpx;
  width: 110rpx;
  height: 110rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.video-progress-page .camera-container .camera-icon.data-v-dbc65c27 {
  width: 100%;
  height: 100%;
}
.video-progress-page .progress-text-container.data-v-dbc65c27 {
  margin-top: 15rpx;
}
.video-progress-page .progress-text-container .progress-text.data-v-dbc65c27 {
  font-size: 28rpx;
  color: #333333;
  font-weight: 400;
}
.video-progress-page .progress-text-container .progress-text .progress-percent.data-v-dbc65c27 {
  color: #2A64F6;
}
.video-progress-page .progress-bar-container.data-v-dbc65c27 {
  margin-top: 62rpx;
  width: 430rpx;
}
.video-progress-page .progress-bar-container .progress-bar.data-v-dbc65c27 {
  width: 100%;
  height: 8rpx;
  background: #ECECEC;
  border-radius: 4rpx;
  overflow: hidden;
}
.video-progress-page .progress-bar-container .progress-bar .progress-fill.data-v-dbc65c27 {
  height: 100%;
  background: #5380F2;
  border-radius: 4rpx;
  transition: width 0.3s ease;
}
.video-progress-page .tip-text-container.data-v-dbc65c27 {
  margin-top: 40rpx;
}
.video-progress-page .tip-text-container .tip-text.data-v-dbc65c27 {
  font-size: 28rpx;
  color: #999999;
}