{"version": 3, "file": "vip-list.js", "sources": ["pages/my/vip-list.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvbXkvdmlwLWxpc3QudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"vip-page\">\r\n    <view class=\"card-top\">\r\n      <!-- 顶部用户信息卡片 -->\r\n      <view class=\"user-card\"\r\n        :style=\"{ backgroundImage: 'url(https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/a95919155b8142f181c097ee21a9ae8b.png)' }\">\r\n        <view class=\"user-avatar\">\r\n          <image class=\"avatar-img\" :src=\"userInfo.headImgUrl || defaultAvatar\" mode=\"aspectFill\" />\r\n        </view>\r\n        <view class=\"user-info\">\r\n          <text class=\"user-name\">{{ userInfo.nickname || '用户Aric' }}</text>\r\n          <text class=\"user-status\" v-if=\"!vipInfo.hasMembership\">成为思链AI会员</text>\r\n          <text class=\"user-status\" v-else>已激活思链AI会员({{ vipInfo.remainingDays }}天)</text>\r\n          <text class=\"user-desc\" v-if=\"!vipInfo.hasMembership\">让AI成为你的IP合伙人</text>\r\n          <text class=\"user-desc\" v-else>让AI成为你的IP合伙人</text>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 提示文字 -->\r\n      <view class=\"tip-text\">\r\n        {{ rule }}\r\n      </view>\r\n    </view>\r\n\r\n\r\n    <!-- 会员套餐选择 -->\r\n    <view class=\"vip-plans\">\r\n      <view class=\"plan-box\">\r\n        <view v-for=\"(plan, index) in vipPlans\" :key=\"index\"\r\n          :class=\"['plan-card', { 'selected': selectedPlan === index }]\" @tap=\"selectPlan(index)\">\r\n          <view class=\"plan-header\">\r\n            <text class=\"plan-name\">{{ plan.packageName }}</text>\r\n          </view>\r\n          <view class=\"plan-price\">\r\n            <text class=\"price-symbol\">¥</text>\r\n            <text class=\"price-amount\">{{ plan.salePriceYuan }}</text>\r\n          </view>\r\n          <view class=\"plan-duration\">{{ plan.durationDays }}天</view>\r\n          <view class=\"plan-daily\">{{ plan.packageDesc }}</view>\r\n          <!-- <view class=\"plan-daily\">仅{{ plan.dailyPriceYuan }}/天</view> -->\r\n        </view>\r\n      </view>\r\n      <!-- 立即订阅按钮 -->\r\n      <!-- <view class=\"subscribe-btn ios\" v-if=\"isIos\" @click=\"onShowPoster\">\r\n        <text class=\"btn-text\">{{ isYearCrad ? '联系白先生' : 'IOS暂不支持' }}</text>\r\n      </view> -->\r\n      <!-- v-else -->\r\n      <view class=\"subscribe-btn\" @click=\"handleSubscribe\">\r\n        <text class=\"btn-text\">立即订阅</text>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 图片弹窗 -->\r\n    <view v-if=\"showImageModal\" class=\"image-modal-overlay\" @click=\"closeImageModal\">\r\n      <view class=\"image-modal-content\">\r\n        <!-- 图片 -->\r\n        <image v-if=\"shenqun_img\" :src=\"shenqun_img\" class=\"modal-image\" mode=\"aspectFit\" @load=\"onImageLoad\"\r\n          @error=\"onImageError\" show-menu-by-longpress />\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, reactive, computed } from 'vue'\r\nimport { onLoad, onShow } from '@dcloudio/uni-app'\r\nimport { getUserVipInfoApi, getVipPackageListApi, createVipOrderApi, queryVipOrderApi, getSubscriptionRuleApi, showBannerUrlsApi } from '@/api/index.js'\r\nimport { useUserStore } from '@/stores/user.js'\r\nimport { miniPay } from '@/api/common.js'\r\n\r\nconst userStore = useUserStore()\r\n\r\n// 默认头像\r\nconst defaultAvatar = 'https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/c4d5b3ada53740d5b862f274ce48ef0c.png'\r\nconst userBg = 'https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/bda9547fe82a404ca6ac801b6c9178ec.png'\r\n// 用户信息\r\nconst userInfo = computed(() => {\r\n  return userStore.userInfo\r\n})\r\n\r\n// 会员套餐数据\r\nconst vipPlans = ref([])\r\nconst getVipPackageList = async () => {\r\n  try {\r\n    let res = await getVipPackageListApi({\r\n      merchantGuid: userStore.merchantGuid\r\n    })\r\n    vipPlans.value = res.data.packages\r\n  } catch (error) {\r\n    console.error('获取会员套餐列表失败:', error)\r\n    uni.showToast({\r\n      title: '加载失败',\r\n      icon: 'none'\r\n    })\r\n  }\r\n}\r\n// 选中的套餐\r\nconst selectedPlan = ref(0) // 默认选中年卡\r\n\r\n// 支付状态\r\nconst queryStatusNum = ref(0)\r\n\r\nconst rule = ref('')\r\nconst getSubscriptionRule = async () => {\r\n  try {\r\n    const res = await getSubscriptionRuleApi({\r\n      merchantGuid: userStore.merchantGuid\r\n    })\r\n    if (res.code === 0) {\r\n      rule.value = res.data.member_card_notice;\r\n    }\r\n  } catch (error) {\r\n    console.error('获取规则失败:', error)\r\n  }\r\n}\r\n//如果是年卡 弹出那个弹窗\r\nconst isYearCrad = ref(false)\r\n// 选择套餐\r\nconst selectPlan = (index) => {\r\n  selectedPlan.value = index;\r\n  if (vipPlans.value[selectedPlan.value].packageType === 3) {\r\n    isYearCrad.value = true\r\n  } else {\r\n    isYearCrad.value = false\r\n  }\r\n\r\n}\r\n\r\nconst shenqun_img = ref('')\r\nconst showImageModal = ref(false)\r\n\r\nconst showBannerUrls = async () => {\r\n  let res = await showBannerUrlsApi({\r\n    merchantGuid: userStore.merchantGuid\r\n  })\r\n  shenqun_img.value = res.data.buy_banner_img;\r\n}\r\n\r\n// 显示图片弹窗\r\nconst onShowPoster = () => {\r\n  if (isYearCrad.value) {\r\n    showImageModal.value = true;\r\n  }\r\n}\r\n\r\n// 关闭图片弹窗\r\nconst closeImageModal = () => {\r\n  showImageModal.value = false;\r\n}\r\n// 立即订阅\r\nconst handleSubscribe = async () => {\r\n  if (!userStore.userToken) {\r\n    uni.showToast({\r\n      title: '请先登录',\r\n      icon: 'none'\r\n    })\r\n    return\r\n  }\r\n  if (isYearCrad.value) {\r\n    onShowPoster()\r\n    return;\r\n  }\r\n  const currentPlan = vipPlans.value[selectedPlan.value]\r\n\r\n  try {\r\n    uni.showLoading({\r\n      title: '正在创建订单...',\r\n      mask: true\r\n    })\r\n\r\n    // 创建会员订阅订单\r\n    const payInfo = await createVipOrderApi({\r\n      merchantGuid: userStore.merchantGuid,\r\n      packageGuid: currentPlan.guid,\r\n      payEnv: 'xcx'\r\n    })\r\n\r\n    uni.hideLoading()\r\n\r\n    // 调用微信支付\r\n    miniPay(payInfo.data.payInfo).then(\r\n      async () => {\r\n        queryPayStatus(payInfo.data.orderNo, queryStatusNum.value)\r\n      },\r\n      (res) => {\r\n        uni.showToast({\r\n          title: res.msg || '支付失败',\r\n          icon: 'none'\r\n        })\r\n      }\r\n    )\r\n  } catch (error) {\r\n    uni.hideLoading()\r\n    console.error('创建订单失败:', error)\r\n    uni.showToast({\r\n      title: error.message || '创建订单失败',\r\n      icon: 'none'\r\n    })\r\n  }\r\n}\r\n\r\n// 查询支付状态\r\nconst queryPayStatus = async (orderNo, number) => {\r\n  number++\r\n  try {\r\n    const orderInfo = await queryVipOrderApi({\r\n      orderNo\r\n    })\r\n\r\n    if (orderInfo.data.isPaid) {\r\n      uni.showToast({\r\n        title: '支付成功',\r\n        icon: 'success'\r\n      })\r\n      // 支付成功后返回上一页或跳转到会员页面\r\n      setTimeout(() => {\r\n        uni.navigateBack()\r\n      }, 1500)\r\n    } else {\r\n      if (number > 12) {\r\n        uni.showToast({\r\n          title: '支付超时',\r\n          icon: 'none'\r\n        })\r\n      } else {\r\n        setTimeout(() => {\r\n          queryPayStatus(orderNo, number)\r\n        }, 2000)\r\n      }\r\n    }\r\n  } catch (error) {\r\n    uni.showToast({\r\n      title: error.msg || '查询支付状态失败',\r\n      icon: 'none'\r\n    })\r\n  }\r\n}\r\n//获取会员信息\r\nlet vipInfo = reactive({\r\n  hasMembership: false,\r\n  isExpired: true,\r\n  membership: null,\r\n  remainingDays: 0\r\n})\r\nconst getVipInfo = async () => {\r\n  try {\r\n    let res = await getUserVipInfoApi({\r\n      merchantGuid: userStore.merchantGuid\r\n    })\r\n    vipInfo = Object.assign(vipInfo, res.data)\r\n  } catch (error) {\r\n    console.error('获取会员信息失败:', error)\r\n    uni.showToast({\r\n      title: '加载失败',\r\n      icon: 'none'\r\n    })\r\n  }\r\n}\r\nconst isIos = ref(false)\r\nonLoad(() => {\r\n  let systemInfomations = uni.getSystemInfoSync()\r\n  if (systemInfomations.osName === 'ios') {\r\n    isIos.value = true\r\n  }\r\n  getSubscriptionRule()\r\n  showBannerUrls()\r\n})\r\n\r\nonShow(() => {\r\n  if (userStore.userToken) {\r\n    getVipPackageList()\r\n    getVipInfo()\r\n    getSubscriptionRule()\r\n    showBannerUrls()\r\n  }\r\n})\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n/* 图片弹窗样式 */\r\n.image-modal-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background-color: rgba(0, 0, 0, 0.8);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  z-index: 9999;\r\n}\r\n\r\n.image-modal-content {\r\n  width: 90%;\r\n  height: 80vh;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  position: relative;\r\n}\r\n\r\n.modal-image {\r\n  max-width: 100%;\r\n  max-height: 200px;\r\n  width: 100%;\r\n  height: 300px;\r\n  display: block;\r\n  object-fit: contain;\r\n}\r\n\r\n.vip-page {\r\n  height: 100vh;\r\n  background-color: #1E2541;\r\n  padding-top: 40rpx;\r\n  display: flex;\r\n  flex-direction: column;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.card-top {\r\n  padding: 0 32rpx;\r\n}\r\n\r\n.user-card {\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: 508rpx;\r\n  width: 100%;\r\n  justify-content: center;\r\n  align-items: center;\r\n  text-align: center;\r\n  background-image: url();\r\n  background-size: 100% 100%;\r\n  background-repeat: no-repeat;\r\n}\r\n\r\n.user-avatar {\r\n  width: 220rpx;\r\n  height: 220rpx;\r\n  border-radius: 50%;\r\n  overflow: hidden;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  // border: 4rpx solid rgba(255, 255, 255, 0.3);\r\n  margin-bottom: 24rpx;\r\n}\r\n\r\n.avatar-img {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.user-info {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n}\r\n\r\n.user-name {\r\n  font-size: 35rpx;\r\n  font-weight: 500;\r\n  color: #F7D4BE;\r\n  margin-bottom: 12rpx;\r\n}\r\n\r\n.user-status {\r\n  font-size: 28rpx;\r\n  color: rgba(255, 255, 255, 0.9);\r\n  margin-bottom: 8rpx;\r\n}\r\n\r\n.user-desc {\r\n  font-size: 24rpx;\r\n  color: rgba(255, 255, 255, 0.7);\r\n}\r\n\r\n.tip-text {\r\n  font-size: 24rpx;\r\n  color: rgba(255, 255, 255, 0.8);\r\n  text-align: center;\r\n  padding: 50rpx 20rpx 30rpx 20rpx;\r\n  line-height: 1.5;\r\n  background-color: #252D44;\r\n  margin-top: 20rpx;\r\n  border-radius: 25rpx 25rpx 25rpx 25rpx;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.vip-plans {\r\n  display: flex;\r\n  flex: 1;\r\n  flex-direction: column;\r\n  justify-content: space-between;\r\n  background-color: #fff;\r\n  gap: 20rpx;\r\n  padding-bottom: 80rpx;\r\n}\r\n\r\n.plan-box {\r\n  display: flex;\r\n  justify-content: space-around;\r\n  margin-top: 30px;\r\n  flex-wrap: wrap;\r\n  padding: 0 30rpx;\r\n}\r\n\r\n.plan-card {\r\n  background: #FFFFFF;\r\n  border-radius: 25rpx;\r\n  border: 3rpx solid #EDEFF0;\r\n  text-align: center;\r\n  position: relative;\r\n  transition: all 0.3s ease;\r\n  width: 210rpx;\r\n  height: 260rpx;\r\n  box-sizing: border-box;\r\n  overflow: hidden;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.plan-card.selected {\r\n  border-color: #DAAA76;\r\n  background: #FDF6EB;\r\n\r\n  .plan-name {\r\n    background: #DAAA76;\r\n    color: #FDF6EB;\r\n  }\r\n\r\n  .price-amount {\r\n    color: #CB8D59;\r\n  }\r\n\r\n  .plan-daily {\r\n    background-color: #FBE9DA;\r\n    color: #CD8D57;\r\n  }\r\n}\r\n\r\n.plan-header {\r\n  margin-bottom: 20rpx;\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.plan-name {\r\n  font-size: 24rpx;\r\n  // font-weight: 600;\r\n  color: #303649;\r\n  background: #EDEFF0;\r\n  border-radius: 0rpx 0rpx 20rpx 20rpx;\r\n  width: fit-content;\r\n  height: 48rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 0 4px;\r\n\r\n}\r\n\r\n.plan-price {\r\n  display: flex;\r\n  align-items: baseline;\r\n  justify-content: center;\r\n  margin-bottom: 12rpx;\r\n}\r\n\r\n.price-symbol {\r\n  font-size: 24rpx;\r\n  color: #CB8D59;\r\n  font-weight: 600;\r\n}\r\n\r\n.price-amount {\r\n  font-size: 48rpx;\r\n  font-weight: 700;\r\n  color: #1D253B;\r\n}\r\n\r\n.plan-duration {\r\n  font-size: 24rpx;\r\n  color: #666666;\r\n  margin-bottom: 8rpx;\r\n}\r\n\r\n.plan-daily {\r\n  font-size: 18rpx;\r\n  color: #999999;\r\n  background-color: #F6F8FA;\r\n  width: 100%;\r\n  height: 56rpx;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  position: absolute;\r\n  left: 0;\r\n  bottom: 0;\r\n}\r\n\r\n.subscribe-btn {\r\n  width: 530rpx;\r\n  margin: 0 auto;\r\n  height: 100rpx;\r\n  background: #2A64F6;\r\n  border-radius: 50rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n\r\n  &.ios {\r\n    background: #999;\r\n  }\r\n}\r\n\r\n.btn-text {\r\n  font-size: 32rpx;\r\n  font-weight: 600;\r\n  color: #FFFFFF;\r\n}\r\n</style>", "import MiniProgramPage from 'E:/youngProject/agent-mini-ui/pages/my/vip-list.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useUserStore", "computed", "ref", "getVipPackageListApi", "uni", "getSubscriptionRuleApi", "showBannerUrlsApi", "createVipOrderApi", "miniPay", "queryVipOrderApi", "reactive", "getUserVipInfoApi", "onLoad", "onShow"], "mappings": ";;;;;AAyEA,MAAM,gBAAgB;;;;AAHtB,UAAM,YAAYA,YAAAA,aAAc;AAMhC,UAAM,WAAWC,cAAQ,SAAC,MAAM;AAC9B,aAAO,UAAU;AAAA,IACnB,CAAC;AAGD,UAAM,WAAWC,cAAG,IAAC,EAAE;AACvB,UAAM,oBAAoB,YAAY;AACpC,UAAI;AACF,YAAI,MAAM,MAAMC,+BAAqB;AAAA,UACnC,cAAc,UAAU;AAAA,QAC9B,CAAK;AACD,iBAAS,QAAQ,IAAI,KAAK;AAAA,MAC3B,SAAQ,OAAO;AACdC,sBAAAA,MAAc,MAAA,SAAA,+BAAA,eAAe,KAAK;AAClCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACF;AAAA,IACH;AAEA,UAAM,eAAeF,cAAG,IAAC,CAAC;AAG1B,UAAM,iBAAiBA,cAAG,IAAC,CAAC;AAE5B,UAAM,OAAOA,cAAG,IAAC,EAAE;AACnB,UAAM,sBAAsB,YAAY;AACtC,UAAI;AACF,cAAM,MAAM,MAAMG,iCAAuB;AAAA,UACvC,cAAc,UAAU;AAAA,QAC9B,CAAK;AACD,YAAI,IAAI,SAAS,GAAG;AAClB,eAAK,QAAQ,IAAI,KAAK;AAAA,QACvB;AAAA,MACF,SAAQ,OAAO;AACdD,sBAAAA,MAAA,MAAA,SAAA,gCAAc,WAAW,KAAK;AAAA,MAC/B;AAAA,IACH;AAEA,UAAM,aAAaF,cAAG,IAAC,KAAK;AAE5B,UAAM,aAAa,CAAC,UAAU;AAC5B,mBAAa,QAAQ;AACrB,UAAI,SAAS,MAAM,aAAa,KAAK,EAAE,gBAAgB,GAAG;AACxD,mBAAW,QAAQ;AAAA,MACvB,OAAS;AACL,mBAAW,QAAQ;AAAA,MACpB;AAAA,IAEH;AAEA,UAAM,cAAcA,cAAG,IAAC,EAAE;AAC1B,UAAM,iBAAiBA,cAAG,IAAC,KAAK;AAEhC,UAAM,iBAAiB,YAAY;AACjC,UAAI,MAAM,MAAMI,4BAAkB;AAAA,QAChC,cAAc,UAAU;AAAA,MAC5B,CAAG;AACD,kBAAY,QAAQ,IAAI,KAAK;AAAA,IAC/B;AAGA,UAAM,eAAe,MAAM;AACzB,UAAI,WAAW,OAAO;AACpB,uBAAe,QAAQ;AAAA,MACxB;AAAA,IACH;AAGA,UAAM,kBAAkB,MAAM;AAC5B,qBAAe,QAAQ;AAAA,IACzB;AAEA,UAAM,kBAAkB,YAAY;AAClC,UAAI,CAAC,UAAU,WAAW;AACxBF,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AACD,UAAI,WAAW,OAAO;AACpB,qBAAc;AACd;AAAA,MACD;AACD,YAAM,cAAc,SAAS,MAAM,aAAa,KAAK;AAErD,UAAI;AACFA,sBAAAA,MAAI,YAAY;AAAA,UACd,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAGD,cAAM,UAAU,MAAMG,4BAAkB;AAAA,UACtC,cAAc,UAAU;AAAA,UACxB,aAAa,YAAY;AAAA,UACzB,QAAQ;AAAA,QACd,CAAK;AAEDH,sBAAAA,MAAI,YAAa;AAGjBI,mBAAAA,QAAQ,QAAQ,KAAK,OAAO,EAAE;AAAA,UAC5B,YAAY;AACV,2BAAe,QAAQ,KAAK,SAAS,eAAe,KAAK;AAAA,UAC1D;AAAA,UACD,CAAC,QAAQ;AACPJ,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO,IAAI,OAAO;AAAA,cAClB,MAAM;AAAA,YAChB,CAAS;AAAA,UACF;AAAA,QACF;AAAA,MACF,SAAQ,OAAO;AACdA,sBAAAA,MAAI,YAAa;AACjBA,sBAAAA,MAAA,MAAA,SAAA,gCAAc,WAAW,KAAK;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,MAAM,WAAW;AAAA,UACxB,MAAM;AAAA,QACZ,CAAK;AAAA,MACF;AAAA,IACH;AAGA,UAAM,iBAAiB,OAAO,SAAS,WAAW;AAChD;AACA,UAAI;AACF,cAAM,YAAY,MAAMK,2BAAiB;AAAA,UACvC;AAAA,QACN,CAAK;AAED,YAAI,UAAU,KAAK,QAAQ;AACzBL,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AAED,qBAAW,MAAM;AACfA,0BAAAA,MAAI,aAAc;AAAA,UACnB,GAAE,IAAI;AAAA,QACb,OAAW;AACL,cAAI,SAAS,IAAI;AACfA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YAChB,CAAS;AAAA,UACT,OAAa;AACL,uBAAW,MAAM;AACf,6BAAe,SAAS,MAAM;AAAA,YAC/B,GAAE,GAAI;AAAA,UACR;AAAA,QACF;AAAA,MACF,SAAQ,OAAO;AACdA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,MAAM,OAAO;AAAA,UACpB,MAAM;AAAA,QACZ,CAAK;AAAA,MACF;AAAA,IACH;AAEA,QAAI,UAAUM,cAAAA,SAAS;AAAA,MACrB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB,CAAC;AACD,UAAM,aAAa,YAAY;AAC7B,UAAI;AACF,YAAI,MAAM,MAAMC,4BAAkB;AAAA,UAChC,cAAc,UAAU;AAAA,QAC9B,CAAK;AACD,kBAAU,OAAO,OAAO,SAAS,IAAI,IAAI;AAAA,MAC1C,SAAQ,OAAO;AACdP,sBAAAA,MAAA,MAAA,SAAA,gCAAc,aAAa,KAAK;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACF;AAAA,IACH;AACA,UAAM,QAAQF,cAAG,IAAC,KAAK;AACvBU,kBAAAA,OAAO,MAAM;AACX,UAAI,oBAAoBR,cAAG,MAAC,kBAAmB;AAC/C,UAAI,kBAAkB,WAAW,OAAO;AACtC,cAAM,QAAQ;AAAA,MACf;AACD,0BAAqB;AACrB,qBAAgB;AAAA,IAClB,CAAC;AAEDS,kBAAAA,OAAO,MAAM;AACX,UAAI,UAAU,WAAW;AACvB,0BAAmB;AACnB,mBAAY;AACZ,4BAAqB;AACrB,uBAAgB;AAAA,MACjB;AAAA,IACH,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClRD,GAAG,WAAW,eAAe;"}