"use strict";function n(){return getApp()}function t(){return n()&&n().globalData}function e(e,a){try{setTimeout((function(){t()&&(n().globalData[`zp_handle${e}Callback`]=a)}),1)}catch(u){}}function a(e){return t()?n().globalData[`zp_handle${e}Callback`]:null}const u={handleQuery:function(n){return e("Query",n),this},_handleQuery:function(n,t,e,u){const c=a("Query");return c?c(n,t,e,u):[n,t,e]},handleFetchParams:function(n){return e("FetchParams",n),this},_handleFetchParams:function(n,t){const e=a("FetchParams");return e?e(n,t||{}):{pageNo:n.pageNo,pageSize:n.pageSize,...t||{}}},handleFetchResult:function(n){return e("FetchResult",n),this},_handleFetchResult:function(n,t,e){const u=a("FetchResult");return u&&u(n,t,e),!!u},handleLanguage2Local:function(n){return e("Language2Local",n),this},_handleLanguage2Local:function(n,t){const e=a("Language2Local");return e?e(n,t):t}};exports.interceptor=u;
