<template>
	<view class="container">
		<!-- 顶部问候区域 -->
		<view class="greeting-section">
			<view class="greeting-text">{{ fullGreeting }}</view>
			<view class="notification-icon">
				<image src="@/static/index/<EMAIL>" class="bell-icon" mode="aspectFit"></image>
			</view>
		</view>

		<view class="recommend-section">
			<view class="section-title">推荐
				<view class="sq-btn" @click="onShowPoster">
					<image class="bg" :src="sqbg"></image> 进入社区 <image class="arrow" src="@/static/index/blue-arrow-icon.png">
					</image>
				</view>
			</view>
			<view class="recommend-swiper-container">
				<!-- :skip-hidden-item-layout="true" -->
				<swiper class="recommend-swiper" :indicator-dots="false" :autoplay="false" :circular="false" next-margin="60px"
					:skip-hidden-item-layout="true">
					<swiper-item v-for="(item, index) in recommendAgents" :key="item.guid">
						<view class="recommend-card" @click="handleCardClick(item.agentName)">
							<view class="card-content">
								<view class="card-title">
									<image :src="item.agentAvatar" class="icon" mode="aspectFit"></image> {{ item.agentName }}
								</view>
								<view class="card-desc">{{ item.agentDesc }}</view>
								<view class="card-author">@{{ item.creator.nickname }}</view>
							</view>
							<!-- <view class="card-icon">
								<image :src="item.agentAvatar" class="icon" mode="aspectFit"></image>
							</view> -->
						</view>
					</swiper-item>
				</swiper>
			</view>
		</view>

		<!-- 分类标签区域 -->
		<view class="category-section">
			<view class="section-title">
				热门
				<view class="more-btn" @click="handleMoreLick">探索更多 <image class="icon"
						src="@/static/index/<EMAIL>"></image>
				</view>
			</view>
			<view class="category-tags">
				<view class="tag-item" v-for="item in recommendCategories" :key="item.guid" @click="handleTagClick(item.guid)">
					{{ item.categoryName }}</view>
			</view>
		</view>

		<!-- AI智能体众创计划横幅 -->
		<view class="banner-section">
			<view class="ai-banner">
				<!-- <view class="banner-bg">
					<image :src="bannerList[0]" class="banner-image" mode="aspectFill"></image>
				</view> -->
				<swiper class="swiper" :autoplay="true" :interval="3000">
					<swiper-item v-for="(item, index) in indexBanner" :key="index" @click="onLinkTo(item)">
						<image :src="item.bannerImg" class="banner-image" mode="aspectFill"></image>
					</swiper-item>
				</swiper>
			</view>
		</view>

		<view class="creator-section">
			<view class="section-title">最近</view>
			<view class="creator-list">
				<view class="creator-item" v-for="item in systemNotices" :key="item.guid" @click="handleCreatorClick(item)">
					<view class="creator-icon">
						<image :src="icons[item.type]" class="icon" mode="aspectFit"></image>
					</view>
					<view class="creator-content">
						<view class="creator-title">{{ item.title }}</view>
						<view class="creator-desc">{{ item.content }}</view>
						<!-- <view class="creator-stats">{{ item.typeText }}</view> -->
					</view>
				</view>
			</view>
		</view>
	</view>

	<!-- 图片弹窗 -->
	<view v-if="showImageModal" class="image-modal-overlay" @click="closeImageModal">
		<view class="image-modal-content">
			<!-- 图片 -->
			<image v-if="shenqun_img" :src="shenqun_img" class="modal-image" mode="aspectFit" @load="onImageLoad"
				@error="onImageError" show-menu-by-longpress />
		</view>
	</view>
</template>

<script setup>
import {
	ref,
	watch
} from 'vue';
import {
	onLoad,
	onShareAppMessage,
	onShareTimeline
} from '@dcloudio/uni-app';
import {
	useUserStore
} from '@/stores/user.js'
import {
	getHomeDataApi,
	showBannerUrlsApi,
	getBannerListApi
} from '@/api';
import base from '@/config/config.js';
import system from '@/static/index/<EMAIL>';
import activity from '@/static/index/<EMAIL>';
import maintenance from '@/static/index/<EMAIL>';
let update = 'https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/a2b6d4b5e3374fc79f54819c5f5c09e3.png'

const userStore = useUserStore()
// let banner = 'https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/99b88efa86b340a988adf95fcf3952b7.png'
const sqbg = 'https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/eb2cbe34cdce4cda956ba6715110ee38.png'

const icons = {
	maintenance,
	activity,
	system,
	update
}
// 获取banner列表
let indexBanner = ref([])
const getBannerList = async () => {
	let res = await getBannerListApi({
		merchantGuid: userStore.merchantGuid,
		bannerType: 'user_pay_agent'
	})
	indexBanner.value = res.data;
}
const onLinkTo = (item) => {
	var tabPages = ['pages/index/index', 'pages/msg-list/msg', 'pages/square/square', 'pages/my/my'];
	switch (item.linkType) {
		case 1:
			uni.navigateTo({
				url: 'pages/webview/webview',
				success: function success(res) {
					res.eventChannel.emit('urlEvent', item.linkUrl);
				}
			});
			break;
		case 2:
			if (tabPages.includes(item.linkUrl)) {
				uni.switchTab({
					url: '/' + item.linkUrl
				});
			} else {
				uni.navigateTo({
					url: '/' + item.linkUrl
				});
			}
			break;
		case 3:
			uni.navigateToMiniProgram({
				appId: item.linkUrl,
				fail: function fail(err) {
					uni.$u.toast('跳转失败...');
				}
			});
			break;
		default:
			break;
	}
}
// 点击事件处理
const handleCardClick = (agentName) => {
	// 跳转到广场搜索页面，传递sysId参数，自动搜索后跳转到详情页
	uni.navigateTo({
		url: `/pages/square/search?agentName=${agentName}`
	})
}

const handleMoreLick = () => {
	uni.switchTab({
		url: '/pages/square/square'
	})
}


const handleTagClick = (categoryGuid) => {
	console.log('点击标签:', categoryGuid)
	// 设置目标分类到全局状态
	userStore.set_target_category(categoryGuid)
	// 跳转到广场页面
	uni.switchTab({
		url: '/pages/square/square'
	})
}

// const handleBannerClick = () => {
// 	// 这里可以添加跳转逻辑
// 	uni.navigateTo({
// 		url: '/pages/rule/cz-index'
// 	})
// }

const handleCreatorClick = (item) => {
	console.log('点击创作者:', item)
	// 这里可以添加跳转逻辑
	var tabPages = ['pages/index/index', 'pages/msg-list/msg', 'pages/square/square', 'pages/my/my'];
	switch (item.jumpType) {
		case 1:
			if (tabPages.includes(item.link)) {
				uni.switchTab({
					url: '/' + item.link
				});
			} else {
				uni.navigateTo({
					url: '/' + item.link
				});
			}
			break;
		case 2:
			uni.navigateTo({
				url: 'pages/webview/webview',
				success: function success(res) {
					res.eventChannel.emit('urlEvent', item.link);
				}
			});
			break;
		case 3:
			uni.navigateToMiniProgram({
				appId: item.link,
				fail: function fail(err) {
					uni.$u.toast('跳转失败...');
				}
			});
			break;
		default:
			break;
	}
}

// 页面加载时的初始化（如果需要的话）
// onLoad(() => {
// 	// 初始化逻辑
// })
onShareAppMessage(() => {
	return {
		title: userStore.appName || '智能体',
		path: `/pages/index/index?invite=${userStore.invitationCode}`,
		imageUrl: bannerList.value[0] || base.shareImg,
		success(res) {
			uni.showToast({
				title: '分享成功'
			})
		},
		fail(res) {
			uni.showToast({
				title: '分享失败',
				icon: 'none'
			})
		}
	}
})
// 分享到朋友圈功能
onShareTimeline(() => {
	return {
		title: userStore.appName || '智能体',
		path: `/pages/index/index?invite=${userStore.invitationCode}`,
		imageUrl: bannerList.value[0] || base.shareImg,
		success(res) {
			uni.showToast({
				title: '分享成功'
			})
		},
		fail(res) {
			uni.showToast({
				title: '分享失败',
				icon: 'none'
			})
		}
	}
})
// const showConfigs = async () => {
// 	let res = await showConfigsApi({
// 		merchantGuid: userStore.merchantGuid
// 	})
// 	uni.setNavigationBarTitle({
// 		title: res.data.zhanhuiName || 'AI商协通'
// 	})
// }
let fullGreeting = ref('')
let recommendAgents = ref([])
let recommendCategories = ref([])
let systemNotices = ref([])
let bannerList = ref([])
// 获取首页数据
const getHomeData = async () => {
	let res = await getHomeDataApi({
		merchantGuid: userStore.merchantGuid
	})
	fullGreeting.value = res.data.dailyGreeting.fullGreeting;
	recommendAgents.value = res.data.recommendAgents;
	systemNotices.value = res.data.systemNotices;
	recommendCategories.value = res.data.recommendCategories;
	bannerList.value = res.data.bannerList;
}
const shenqun_img = ref('')
const showImageModal = ref(false)

const showBannerUrls = async () => {
	let res = await showBannerUrlsApi({
		merchantGuid: userStore.merchantGuid
	})
	shenqun_img.value = res.data.shenqun_img;
}

// 显示图片弹窗
const onShowPoster = () => {
	showImageModal.value = true
}

// 关闭图片弹窗
const closeImageModal = () => {
	showImageModal.value = false;
}


onLoad(() => {
	// if (userStore.userToken) {
	getHomeData();
	showBannerUrls()
	getBannerList()
	// }
});
watch(
	() => userStore.userToken,
	(newValue, oldValue) => {
		if (newValue && oldValue === '') {
			getHomeData();
			showBannerUrls()
		}
	}
);
</script>

<style lang="scss" scoped>
.container {
	background-color: #ffffff;
	min-height: 100vh;
	padding: 0 16px;
}

/* 顶部问候区域 */
.greeting-section {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20px 0 16px;

	.greeting-text {
		font-size: 30rpx;
		font-weight: 500;
		color: #66648A;
		// overflow: hidden;
		// text-overflow: ellipsis;
		// white-space: nowrap;
	}

	.notification-icon {
		.bell-icon {
			width: 60rpx;
			height: 60rpx;
			display: block;
		}
	}
}

/* 推荐卡片区域 */
.recommend-section {
	margin-bottom: 24px;

	.section-title {
		font-size: 34rpx;
		font-weight: 600;
		color: #222;
		margin-bottom: 20rpx;
		display: flex;
		justify-content: space-between;

		.sq-btn {
			width: 189rpx;
			height: 55rpx;
			display: flex;
			align-items: center;
			background: #E8ECFC;
			border-radius: 28rpx;
			position: relative;
			font-size: 20rpx;
			color: #4F72F6;
			justify-content: flex-end;

			.arrow {
				display: block;
				width: 30rpx;
				height: 30rpx;
				margin-left: 2px;
			}

			.bg {
				position: absolute;
				top: -30rpx;
				left: 0;
				width: 81rpx;
				height: 81rpx;
			}
		}

	}

	.recommend-swiper-container {
		overflow: hidden;
	}

	.recommend-swiper {
		height: 140px;

		swiper-item {
			padding-right: 12px;
			box-sizing: border-box;
		}

		.recommend-card {
			background: #F8F9FA;
			border-radius: 12px;
			padding: 16px;
			display: flex;
			flex-direction: column;
			justify-content: space-between;
			height: 100%;
			box-sizing: border-box;
			width: calc(100% - 12px);
			margin-right: 12px;

			.card-content {
				flex: 1;

				.card-title {
					font-size: 16px;
					font-weight: 600;
					color: #333;
					margin-bottom: 8px;
					display: flex;
					align-items: center;

					.icon {
						display: block;
						margin-right: 15rpx;
						width: 60rpx;
						height: 60rpx;
						border-radius: 50%;
					}
				}

				.card-desc {
					font-size: 12px;
					color: #666;
					line-height: 1.4;
					margin-bottom: 12px;
					overflow: hidden;
					text-overflow: ellipsis;
					display: -webkit-box;
					-webkit-line-clamp: 3;
					line-clamp: 3;
					-webkit-box-orient: vertical;
				}

				.card-author {
					font-size: 11px;
					color: #999;
				}
			}

			// .card-icon {
			// 	align-self: flex-end;
			// 	margin-top: 8px;

			// 	.icon {
			// 		width: 32px;
			// 		height: 32px;
			// 	}
			// }
		}
	}
}

/* 分类标签区域 */
.category-section {
	margin-bottom: 24px;

	.section-title {
		display: flex;
		justify-content: space-between;
		align-items: center;
		font-size: 34rpx;
		font-weight: bold;
		color: #222;
		margin-bottom: 20rpx;

		.more-btn {
			font-size: 30rpx;
			color: #999999;
			font-weight: normal;
			display: flex;
			align-items: center;

			.icon {
				width: 40rpx;
				height: 40rpx;
				display: block;
			}
		}
	}

	.category-tags {
		display: flex;
		flex-wrap: wrap;
		gap: 8px;

		.tag-item {
			padding: 8px 16px;
			background: #F4F7FF;
			border-radius: 20px;
			font-size: 26rpx;
			color: #666;
			border: 1px solid #E7EDFA;
			color: #5380F2;

			// &.active {
			// 	background: #007aff;
			// 	color: #fff;
			// 	border-color: #007aff;
			// }
		}
	}
}

/* AI智能体众创计划横幅 */
.banner-section {
	margin-bottom: 24px;

	.ai-banner {
		position: relative;
		// background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		border-radius: 16px;
		// padding: 24px;
		overflow: hidden;
		min-height: 380rpx;

		.banner-content {
			position: relative;
			z-index: 2;

			.banner-title {
				font-size: 24px;
				font-weight: 700;
				color: #fff;
				margin-bottom: 8px;
			}

			.banner-subtitle {
				font-size: 14px;
				color: rgba(255, 255, 255, 0.9);
				margin-bottom: 16px;
				line-height: 1.4;
			}

			.banner-btn {
				display: inline-block;
				background: rgba(255, 255, 255, 0.2);
				border: 1px solid rgba(255, 255, 255, 0.3);
				border-radius: 20px;
				padding: 8px 20px;
				font-size: 14px;
				color: #fff;
				backdrop-filter: blur(10px);
			}
		}

		.swiper {
			height: 380rpx;
		}

		.banner-image {
			height: 100%;
			width: 100%;
		}

		// .banner-bg {
		// 	position: absolute;
		// 	top: 0;
		// 	right: 0;
		// 	bottom: 0;
		// 	left: 0;
		// 	z-index: 1;

		// 	.banner-image {
		// 		width: 100%;
		// 		height: 100%;
		// 	}
		// }
	}
}

/* 创作大神推荐列表 */
.creator-section {
	padding-bottom: 30rpx;

	.section-title {
		font-size: 20px;
		font-weight: 600;
		color: #333;
		margin-bottom: 16px;
	}

	.creator-list {
		.creator-item {
			background: #F8F9FA;
			border-radius: 12px;
			padding: 16px;
			margin-bottom: 12px;
			display: flex;
			align-items: center;
			gap: 12px;

			.creator-icon {
				.icon {
					width: 60rpx;
					height: 60rpx;
					border-radius: 8px;
				}
			}

			.creator-content {
				flex: 1;

				.creator-title {
					width: 540rpx;
					font-size: 30rpx;
					font-weight: 600;
					color: #333;
					margin-bottom: 8px;
					line-height: 1.3;
					text-overflow: ellipsis;
					overflow: hidden;
					white-space: nowrap;
				}

				.creator-desc {
					width: 560rpx;
					font-size: 26rpx;
					color: #999;
					line-height: 1.4;
					// overflow: hidden;
					// text-overflow: ellipsis;
					// white-space: nowrap;
				}

				.creator-stats {
					font-size: 26rpx;
					color: #999;
					margin-top: 4px;
				}
			}
		}
	}
}

/* 图片弹窗样式 */
.image-modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, 0.8);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 9999;
}

.image-modal-content {
	width: 90%;
	height: 80vh;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	position: relative;
}

.modal-image {
	max-width: 100%;
	max-height: 200px;
	width: 100%;
	height: 300px;
	display: block;
	object-fit: contain;
}
</style>