<template>
  <view v-if="showPopup" class="subscribe-popup-overlay" @tap="closePopup" catchtouchmove="true">
    <view class="subscribe-popup-content" @tap.stop>
      <image class="bg" :src="bg" />
      <!-- 关闭按钮 -->
      <view class="close-btn" @click="closePopup">
        <image class="icon" src="@/static/msg/<EMAIL>" mode="aspectFit" />
      </view>
      <view class="content-box">
        <!-- 智能体头像 -->
        <view class="agent-avatar-section">
          <image class="agent-avatar" :src="agentInfo.agentAvatar" mode="aspectFill" />
        </view>

        <!-- 智能体信息 -->
        <view class="agent-info-section">
          <text class="agent-name">智能体名称：{{ agentInfo.agentName }}</text>
          <text class="agent-desc">智能体介绍：{{ agentInfo.agentDesc }}</text>
        </view>

        <!-- 价格信息 -->
        <view class="price-section">
          <text class="price-label">订阅价格：</text>
          <text class="price-value">¥ {{ agentInfo.price }}</text>
        </view>


        <!-- 订阅按钮 -->
        <view class="subscribe-btn ios" v-if="isIos">
          <text class="subscribe-text">IOS不可使用</text>
        </view>
        <view class="subscribe-btn" @tap="handleSubscribe" v-else>
          <text class="subscribe-text">立即订阅</text>
        </view>

      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, watch, defineProps, onMounted } from 'vue'
const bg = 'https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/b9ed244f8bd849d48375bd6c29b48129.png'

// 定义 props
const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  agentInfo: {
    type: Object,
    default: () => ({
      agentName: '',
      agentDesc: '',
      agentAvatar: '',
      price: 0,
    })
  }
})
const isIos = ref(false)
// 定义 emits
const emit = defineEmits(['close', 'subscribe'])

// 响应式数据
const showPopup = ref(props.show)

// 监听 props.show 变化
watch(() => props.show, (newVal) => {
  showPopup.value = newVal
})

// 关闭弹窗
const closePopup = () => {
  showPopup.value = false
  emit('close')
}

// 处理订阅
const handleSubscribe = () => {
  emit('subscribe', props.agentInfo)
}
onMounted(() => {
  let systemInfomations = uni.getSystemInfoSync()
  if (systemInfomations.osName === 'ios') {
    isIos.value = true
  }
})

</script>

<style lang="scss" scoped>
.subscribe-popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 999;
}

.subscribe-popup-content {
  height: 1058rpx;
  width: 750rpx;
  position: relative;


  .bg {
    width: 100%;
    height: 100%;
  }

  .content-box {
    position: absolute;
    left: 74rpx;
    top: 185rpx;
    width: 600rpx;
    height: 642rpx;
    // background-color: rgba(0, 0, 0, 0.3);
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0 30rpx;
    box-sizing: border-box;
  }
}

.close-btn {
  position: absolute;
  bottom: 116rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;

  .icon {
    width: 60rpx;
    height: 60rpx;
    display: block;
  }
}

.agent-avatar-section {
  margin-top: -90rpx;

  .agent-avatar {
    width: 160rpx;
    height: 160rpx;
    border-radius: 50%;
    background: #f0f0f0;
    border: 6rpx solid #4CAF50;
  }
}

.agent-info-section {
  width: 100%;
  margin-bottom: 40rpx;
  text-align: center;
  margin-top: 60rpx;

  .agent-name {
    display: block;
    font-size: 32rpx;
    color: #333333;
    font-weight: 600;
    margin-bottom: 20rpx;
  }

  .agent-desc {
    display: block;
    font-size: 28rpx;
    color: #666666;
    line-height: 1.5;
  }
}

.price-section {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30rpx;

  .price-label {
    font-size: 28rpx;
    color: #333333;
    margin-right: 10rpx;
  }

  .price-value {
    font-size: 48rpx;
    color: #3478f6;
    font-weight: 700;
  }
}


.subscribe-btn {
  width: 310rpx;
  height: 100rpx;
  background: linear-gradient(99deg, #2A5AF6 0%, #1198FF 100%);
  border-radius: 50rpx 50rpx 50rpx 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(52, 120, 246, 0.3);

  &.ios {
    background: #999;
  }

  .subscribe-text {
    font-size: 32rpx;
    color: #ffffff;
    font-weight: 600;
  }
}
</style>
