"use strict";const e=require("../common/vendor.js"),t=e.defineStore("user",{state:()=>({wxappid:"wx4658f2fe45d90c20",merchantGuid:"2a5edd395c26499ab0d8966f8061573f",userToken:e.index.getStorageSync("userToken")||"",userInfo:e.index.getStorageSync("userInfo")||{chat_count:0},modalStatus:"",shareImg:"",appName:"思链IP智能体",targetCategoryGuid:"",invitationCode:""}),actions:{set_user_token(t){this.userToken=t,e.index.setStorageSync("userToken",t)},set_user_info(t){this.userInfo=t,e.index.setStorageSync("userInfo",t)},delete_user_info(){this.userToken="",this.userInfo={},e.index.removeStorageSync("userInfo"),e.index.removeStorageSync("userToken")},set_target_category(e){this.targetCategoryGuid=e},clear_target_category(){this.targetCategoryGuid=""},set_invitation_code(e){this.invitationCode=e}}});exports.useUserStore=t;
