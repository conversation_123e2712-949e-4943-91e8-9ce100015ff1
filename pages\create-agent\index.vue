<template>
  <view class="create-agent-page">
    <scroll-view class="page-scroll" scroll-y>
      <!-- 头像区域 -->
      <view class="avatar-section">
        <view class="avatar-container" @tap="chooseAvatar">
          <image :src="agentForm.agentAvatar" class="avatar" mode="aspectFill" />
          <view class="avatar-add" v-if="!generating">
            <image src="@/static/msg/<EMAIL>" class="add-icon" mode="aspectFit" />
          </view>
          <!-- 生成中的加载效果 -->
          <view class="avatar-loading" v-if="generating">
            <view class="loading-spinner"></view>
          </view>
        </view>
        <text class="avatar-label" @click="onCreateLogo" :class="{ 'disabled': generating }">
          {{ generating ? 'AI生成中...' : 'AI生成形象' }}
        </text>
        <view class="secret-bnt" @click="onSecret">秘钥管理</view>
      </view>

      <!-- 表单区域 -->
      <view class="form-section">

        <!-- 选择分类 -->
        <view class="form-item">
          <text class="label">选择分类:</text>
          <view class="category-selector" @tap="showCategoryPicker">
            <text class="category-text" :class="{ 'placeholder': !selectedCategory }">
              {{ selectedCategory ? selectedCategory.categoryName : '请选择分类' }}
            </text>
            <!-- <image src="/static/placeholder-arrow-down.png" class="arrow-icon" mode="aspectFit" /> -->
          </view>
        </view>
        <!-- 名称 -->
        <view class="form-item">
          <text class="label">名称:</text>
          <input v-model="agentForm.agentName" class="input" placeholder="输入名称" placeholder-class="placeholder"
            maxlength="50" />
        </view>

        <!-- 智能体描述 -->
        <view class="form-item">
          <text class="label">智能体描述:</text>
          <textarea v-model="agentForm.agentDesc" class="textarea" placeholder="输入描述" placeholder-class="placeholder"
            maxlength="500" />
        </view>

        <!-- 选择智能体类型 -->
        <view class="form-item">
          <text class="label">选择智能体类型:</text>
          <view class="radio-group">
            <view class="radio-item" v-for="type in agentTypes" :key="type.value" @tap="selectAgentType(type.value)">
              <view class="radio" :class="{ 'checked': agentForm.agentType === type.value }">
                <view class="radio-inner" v-if="agentForm.agentType === type.value"></view>
              </view>
              <text class="radio-text">{{ type.label }}</text>
            </view>
          </view>
        </view>

        <!-- 角色设定提示词 -->
        <view class="form-item" v-if="agentForm.agentType === 1">
          <text class="label">角色设定提示词:</text>
          <textarea v-model="agentForm.promptContent" class="textarea large" placeholder="输入提示词"
            placeholder-class="placeholder" maxlength="2000" />
        </view>
        <!-- 密钥读取类型 -->
        <view class="form-item">
          <text class="label">密钥读取类型:</text>
          <view class="radio-group">
            <view class="radio-item" v-for="visibility in secretTypes" :key="visibility.value"
              @tap="selectSecretType(visibility.value)">
              <view class="radio" :class="{ 'checked': agentForm.agentConfig.secret_key_type === visibility.value }">
                <view class="radio-inner" v-if="agentForm.agentConfig.secret_key_type === visibility.value"></view>
              </view>
              <text class="radio-text">{{ visibility.label }}</text>
            </view>
          </view>
        </view>
        <!-- 智能体密码 -->
        <view class="form-item" v-if="agentForm.agentType === 3">
          <text class="label">扣子智能体ID:</text>
          <input v-model="agentForm.agentConfig.coze_sign" class="input" placeholder="输入密码"
            placeholder-class="placeholder" />
        </view>

        <!-- 智能体密码 -->
        <view class="form-item" v-if="agentForm.agentType !== 1">
          <text class="label">智能体秘钥:</text>
          <input v-model="agentForm.agentConfig.secret_token" class="input" placeholder="输入秘钥"
            placeholder-class="placeholder" />
        </view>
        <!-- 自定义部署地址 -->
        <view class="form-item" v-if="agentForm.agentType === 2">
          <text class="label">dify自定义部署地址:</text>
          <input v-model="agentForm.agentConfig.deploy_address" class="input" placeholder="输入地址"
            placeholder-class="placeholder" />
        </view>


        <!-- 开场白 -->
        <view class="form-item">
          <text class="label">开场白:</text>
          <input v-model="agentForm.welcomeMessage" class="input" placeholder="输入开场白" placeholder-class="placeholder" />
        </view>

        <!-- 引导问题列表 -->
        <view class="form-item">
          <text class="label">引导问题列表:</text>
          <view class="question-list">
            <view class="question-item" v-for="(question, index) in agentForm.commonQuestions" :key="index">
              <input v-model="agentForm.commonQuestions[index]" class="question-input"
                :placeholder="`请输入引导问题${index + 1}`" placeholder-class="placeholder" />
            </view>
          </view>
        </view>

        <!-- 选择智能体类型 -->
        <view class="form-item">
          <text class="label">选择智能体类型:</text>
          <view class="radio-group">
            <view class="radio-item" v-for="visibility in visibilityTypes" :key="visibility.value"
              @tap="selectVisibility(visibility.value)">
              <view class="radio" :class="{ 'checked': agentForm.isPaid === visibility.value }">
                <view class="radio-inner" v-if="agentForm.isPaid === visibility.value"></view>
              </view>
              <text class="radio-text">{{ visibility.label }}</text>
            </view>
          </view>
        </view>

        <!-- 收费金额 -->
        <view class="form-item" v-if="agentForm.isPaid === 1">
          <text class="label">收费金额:</text>
          <input v-model="agentForm.price" class="input" placeholder="输入金额" placeholder-class="placeholder"
            type="digit" />
        </view>

        <view class="form-item" v-if="agentForm.isPaid === 1">
          <text class="label">试用聊天次数:</text>
          <input v-model="agentForm.trial_chat_count" class="input" placeholder="试用聊天次数" placeholder-class="placeholder"
            type="digit" />
        </view>

        <!-- 底部说明 -->
        <view class="form-note">
          <text class="note-text">注意：智能体不得违反相关法律法规，禁止涉及政治敏感话题，详情请查看《平台协议》</text>
        </view>
      </view>

      <!-- 创建按钮 -->
      <view class="create-section">
        <view class="create-btn" @tap="handleCreate" :class="{ 'disabled': creating }">
          <text class="create-text">{{ creating ? (isEditMode ? '更新中...' : '创建中...') : (isEditMode ? '完成' : '创建AI智能体')
          }}</text>
        </view>
      </view>
    </scroll-view>

    <!-- 分类选择弹窗 -->
    <view v-if="showCategoryModal" class="category-modal" @tap="closeCategoryModal">
      <view class="modal-content" @tap.stop>
        <!-- 弹窗头部 -->
        <view class="modal-header">
          <text class="modal-title">选择分类</text>
          <!-- <view class="close-btn" @tap="closeCategoryModal">
            <image src="/static/placeholder-close.png" class="close-icon" mode="aspectFit" />
          </view> -->
        </view>

        <!-- 搜索框 -->
        <view class="search-section">
          <view class="search-box">
            <image src="/static/placeholder-search.png" class="search-icon" mode="aspectFit" />
            <input v-model="searchKeyword" class="search-input" placeholder="搜索分类" placeholder-class="placeholder"
              @input="handleSearch" />
            <view v-if="searchKeyword" class="clear-btn" @tap="clearSearch">
              <image src="/static/placeholder-close.png" class="clear-icon" mode="aspectFit" />
            </view>
          </view>
        </view>

        <!-- 分类列表 -->
        <scroll-view class="category-list" scroll-y>
          <view v-for="(category, index) in filteredCategories" :key="category.guid" class="category-item"
            @tap="selectTempCategory(category)">
            <view class="radio" :class="{ 'checked': tempSelectedCategory?.guid === category.guid }">
              <view class="radio-inner" v-if="tempSelectedCategory?.guid === category.guid"></view>
            </view>
            <text class="category-name">{{ category.categoryName }}</text>
          </view>

          <!-- 空状态 -->
          <view v-if="filteredCategories.length === 0" class="empty-state">
            <text class="empty-text">暂无匹配的分类</text>
          </view>
        </scroll-view>

        <!-- 确认按钮 -->
        <view class="modal-footer">
          <view class="cancel-btn" @tap="closeCategoryModal">
            <text class="cancel-text">取消</text>
          </view>
          <view class="confirm-btn" @tap="confirmSelectCategory" :class="{ 'disabled': !tempSelectedCategory }">
            <text class="confirm-text">确认</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { createAgentApi, getCategoryListApi, generateAvatarApi, getMyDetailApi, updateMyAgentApi } from '@/api/index.js'
import { useUserStore } from '@/stores/user.js'
import { updataFileFun } from '@/api/common.js'

const userStore = useUserStore()

// 表单数据
const agentForm = reactive({
  merchantGuid: userStore.merchantGuid,
  agentName: '',
  agentType: 1, // 默认内部类型
  categoryGuid: '', // 需要从分类列表获取
  promptContent: '',
  agentDesc: '',
  agentAvatar: '',
  isPaid: 0, // 默认免费
  price: '',
  trial_chat_count: 0, // 试用聊天次数
  isPublic: 1, // 默认公开
  agentConfig: {
    // model: 'gpt-4',
    // temperature: 0.7,
    // max_tokens: 2000,
    // api_key: '',
    // workflow_id: '',
    deploy_address: '',
    coze_sign: '',
    secret_token: '',
    secret_key_type: 'default'
  },
  knowledgeBaseIds: [],
  welcomeMessage: '',
  commonQuestions: ['', '', ''], // 默认3个问题
})

// 加载状态
const creating = ref(false)
const uploading = ref(false)
const generating = ref(false) // AI生成头像状态

// 编辑模式相关
const isEditMode = ref(false)
const editGuid = ref('')

// 分类相关
const selectedCategory = ref(null)
const categoryList = ref([])
const showCategoryModal = ref(false)
const tempSelectedCategory = ref(null)
const searchKeyword = ref('')

// 计算属性 - 过滤后的分类列表
const filteredCategories = computed(() => {
  if (!searchKeyword.value) {
    return categoryList.value
  }
  return categoryList.value.filter(item =>
    item.categoryName.toLowerCase().includes(searchKeyword.value.toLowerCase())
  )
})

// 智能体类型选项
const agentTypes = ref([
  { label: '内部', value: 1 },
  { label: 'dify', value: 2 },
  { label: 'coze', value: 3 },
  // { label: '阿里云百炼', value: 4 }
])

// 秘钥类型
const secretTypes = ref([
  { label: '自定义输入', value: 'default' },
  { label: '读取用户保存密钥', value: 'user' }
])
let secretType = ref('default')
// 可见性类型选项
const visibilityTypes = ref([
  { label: '免费', value: 0 },
  { label: '付费', value: 1 }
])
//跳转秘钥管理
const onSecret = () => {
  console.log('跳转秘钥管理')
  uni.navigateTo({
    url: '/pages/create-agent/secret'
  })
}

// 选择头像
const chooseAvatar = () => {
  if (uploading.value) return

  uni.chooseImage({
    count: 1,
    sizeType: ['compressed'],
    sourceType: ['album', 'camera'],
    success: async (res) => {
      const tempFilePath = res.tempFilePaths[0]
      await uploadAvatar(tempFilePath)
    },
    fail: (err) => {
      console.error('选择图片失败:', err)
    }
  })
}

// 上传头像
const uploadAvatar = async (filePath) => {
  try {
    uploading.value = true
    uni.showLoading({
      title: '上传中...',
      mask: true
    })

    const uploadRes = await updataFileFun(filePath)
    const result = JSON.parse(uploadRes.data)

    if (result.code === 0) {
      agentForm.agentAvatar = result.data
      uni.showToast({
        title: '头像上传成功',
        icon: 'success'
      })
    } else {
      throw new Error(result.msg || '上传失败')
    }
  } catch (error) {
    console.error('上传头像失败:', error)
    uni.showToast({
      title: '上传失败',
      icon: 'none'
    })
  } finally {
    uploading.value = false
    uni.hideLoading()
  }
}
// AI生成头像
const onCreateLogo = async () => {
  // 验证必填字段
  if (!agentForm.agentName.trim()) {
    uni.showToast({
      title: '请先输入智能体名称',
      icon: 'none'
    })
    return
  }

  if (!agentForm.agentDesc.trim()) {
    uni.showToast({
      title: '请先输入智能体描述',
      icon: 'none'
    })
    return
  }

  if (generating.value) return

  try {
    generating.value = true
    uni.showLoading({
      title: 'AI生成中...',
      mask: true
    })

    const generateData = {
      merchantGuid: userStore.merchantGuid,
      agentName: agentForm.agentName.trim(),
      agentDesc: agentForm.agentDesc.trim()
    }

    const res = await generateAvatarApi(generateData)

    if (res.code === 0) {
      agentForm.agentAvatar = res.data.data.imageUrl
      uni.showToast({
        title: '头像生成成功',
        icon: 'success'
      })
    } else {
      throw new Error(res.msg || '生成失败')
    }
  } catch (error) {
    console.error('AI生成头像失败:', error)
    uni.showToast({
      title: error.message || '生成失败',
      icon: 'none'
    })
  } finally {
    generating.value = false
    uni.hideLoading()
  }
}
// 选择智能体类型
const selectAgentType = (value) => {
  agentForm.agentType = value
}

// 选择可见性类型
const selectVisibility = (value) => {
  agentForm.isPaid = value
}

const selectSecretType = (value) => {
  agentForm.secret_key_type = value
}
// 获取分类列表
const getCategoryList = async () => {
  try {
    const res = await getCategoryListApi({
      merchantGuid: userStore.merchantGuid
    })
    if (res.code === 0) {
      categoryList.value = res.data || []
    }
  } catch (error) {
    console.error('获取分类列表失败:', error)
  }
}

// 显示分类选择器
const showCategoryPicker = () => {
  if (categoryList.value.length === 0) {
    getCategoryList()
  }
  // 打开弹窗时重置临时选择状态，不显示当前已选项
  // tempSelectedCategory.value = null
  showCategoryModal.value = true
}

// 关闭分类选择弹窗
const closeCategoryModal = () => {
  showCategoryModal.value = false
  searchKeyword.value = ''
  // tempSelectedCategory.value = null
}

// 搜索处理
const handleSearch = () => {
  // 搜索时保持当前选中状态，不重置
  // 用户可以在搜索结果中看到当前选中项
}

// 清除搜索
const clearSearch = () => {
  searchKeyword.value = ''
  // 保持当前临时选中状态，不重置为原来的选中状态
}

const selectTempCategory = (category) => {
  // 直接选中当前分类（自动取消之前的选中）
  tempSelectedCategory.value = category
}

// 确认选择分类
const confirmSelectCategory = () => {
  if (!tempSelectedCategory.value) {
    uni.showToast({
      title: '请选择分类',
      icon: 'none'
    })
    return
  }

  selectedCategory.value = tempSelectedCategory.value
  agentForm.categoryGuid = tempSelectedCategory.value.guid
  closeCategoryModal()
}

// 获取智能体详情
const getAgentDetail = async (guid) => {
  try {
    uni.showLoading({
      title: '加载中...',
      mask: true
    })

    const res = await getMyDetailApi({ guid })
    if (res.code === 0) {
      const agentData = res.data

      // 填充表单数据
      agentForm.agentName = agentData.agentName || ''
      agentForm.agentType = agentData.agentType || 1
      agentForm.categoryGuid = agentData.categoryGuid || ''
      agentForm.promptContent = agentData.promptContent || ''
      agentForm.agentDesc = agentData.agentDesc || ''
      agentForm.agentAvatar = agentData.agentAvatar || ''
      agentForm.isPaid = agentData.isPaid || 0
      agentForm.price = agentData.price || ''
      agentForm.trial_chat_count = agentData.trialChatCount || 0 // 试用聊天次数
      agentForm.isPublic = agentData.isPublic || 1
      agentForm.agentConfig = agentData.agentConfig || { secret_token: '', secret_key_type: 'default', deploy_address: '' }
      agentForm.knowledgeBaseIds = agentData.knowledgeBaseIds || []
      agentForm.welcomeMessage = agentData.welcomeMessage || ''
      agentForm.commonQuestions = agentData.commonQuestions.length > 0 ? agentData.commonQuestions : ['', '', '']
      // 设置选中的分类
      if (agentData.category) {
        selectedCategory.value = agentData.category
      }

      // 存储编辑的guid
      editGuid.value = guid
    } else {
      throw new Error(res.msg || '获取智能体详情失败')
    }
  } catch (error) {
    console.error('获取智能体详情失败:', error)
    uni.showToast({
      title: error.message || '获取智能体详情失败',
      icon: 'none'
    })
    // 获取失败时返回上一页
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  } finally {
    uni.hideLoading()
  }
}

// 页面加载时检查参数
onLoad((options) => {
  if (options.guid) {
    isEditMode.value = true
    getAgentDetail(options.guid)
  }
})

// 页面初始化
onMounted(() => {
  getCategoryList()
})

// 创建智能体
const handleCreate = async () => {
  if (creating.value) return

  // 表单验证
  if (!agentForm.categoryGuid) {
    uni.showToast({
      title: '请选择分类',
      icon: 'none'
    })
    return
  }

  if (!agentForm.agentName.trim()) {
    uni.showToast({
      title: '请输入智能体名称',
      icon: 'none'
    })
    return
  }

  // if (!agentForm.promptContent.trim()) {
  //   uni.showToast({
  //     title: '请输入角色设定提示词',
  //     icon: 'none'
  //   })
  //   return
  // }

  try {
    creating.value = true
    uni.showLoading({
      title: isEditMode.value ? '更新中...' : '创建中...',
      mask: true
    })

    // 处理价格
    if (agentForm.isPaid === 1 && !agentForm.price) {
      uni.showToast({
        title: '付费智能体请输入价格',
        icon: 'none'
      })
      creating.value = false
      uni.hideLoading()
      return
    }

    // 过滤空的引导问题
    const filteredQuestions = agentForm.commonQuestions.filter(q => q.trim())

    const submitData = {
      agentName: agentForm.agentName.trim(),
      agentType: agentForm.agentType,
      categoryGuid: agentForm.categoryGuid,
      promptContent: agentForm.promptContent.trim(),
      agentDesc: agentForm.agentDesc.trim(),
      agentAvatar: agentForm.agentAvatar,
      isPaid: agentForm.isPaid,
      price: agentForm.isPaid === 1 ? parseFloat(agentForm.price) : 0,
      isPublic: 1, // 暂时都设为公开
      trial_chat_count: agentForm.trial_chat_count, // 试用聊天次数
      agentConfig: agentForm.agentConfig,
      knowledgeBaseIds: agentForm.knowledgeBaseIds,
      welcomeMessage: agentForm.welcomeMessage.trim(),
      commonQuestions: filteredQuestions
    }

    // 根据模式选择不同的参数和API
    if (isEditMode.value) {
      submitData.guid = editGuid.value
      console.log('更新智能体数据:', submitData)
      const res = await updateMyAgentApi(submitData)
      if (res.code === 0) {
        uni.showToast({
          title: '更新成功',
          icon: 'success'
        })
        // 延迟返回上一页
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)
      } else {
        throw new Error(res.msg || '更新失败')
      }
    } else {
      submitData.merchantGuid = agentForm.merchantGuid
      console.log('创建智能体数据:', submitData)
      const res = await createAgentApi(submitData)
      if (res.code === 0) {
        uni.showToast({
          title: '创建成功',
          icon: 'success'
        })
        // 延迟返回上一页
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)
      } else {
        throw new Error(res.msg || '创建失败')
      }
    }

  } catch (error) {
    console.error(isEditMode.value ? '更新失败:' : '创建失败:', error)
    uni.showToast({
      title: error.msg || (isEditMode.value ? '更新失败' : '创建失败'),
      icon: 'none'
    })
  } finally {
    creating.value = false
    uni.hideLoading()
  }
}
</script>

<style lang="scss" scoped>
.create-agent-page {
  background: #F5F5F5;
  min-height: 100vh;
}

.page-scroll {
  height: 100vh;
}

.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 0 40rpx;
  position: relative;

  .avatar-container {
    position: relative;
    width: 160rpx;
    height: 160rpx;
    margin-bottom: 16rpx;

    .avatar {
      width: 100%;
      height: 100%;
      border-radius: 50%;
      background: #f0f0f0;
    }

    .avatar-add {
      position: absolute;
      bottom: 0;
      right: 0;
      width: 48rpx;
      height: 48rpx;
      background: #3478f6;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 4rpx solid #ffffff;

      .add-icon {
        width: 24rpx;
        height: 24rpx;
      }
    }

    .avatar-loading {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.6);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;

      .loading-spinner {
        width: 40rpx;
        height: 40rpx;
        border: 4rpx solid rgba(255, 255, 255, 0.3);
        border-top: 4rpx solid #ffffff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
    }
  }

  .avatar-label {
    font-size: 26rpx;
    color: #3478f6;
    background-color: #FFFFFF;
    border-radius: 35rpx;
    padding: 10rpx 20rpx;
    transition: all 0.3s ease;

    &.disabled {
      color: #CCCCCC;
      background-color: #F5F5F5;
    }
  }

  .secret-bnt {
    font-size: 26rpx;
    color: #fff;
    background-color: #3478f6;
    border-radius: 35rpx;
    padding: 10rpx 20rpx;
    transition: all 0.3s ease;
    position: absolute;
    right: 10px;
    top: 10px;
  }
}

.form-section {
  // background: #ffffff;
  margin-top: 20rpx;
  padding: 0 32rpx;

  .form-item {
    padding: 30rpx;
    background: #fff;
    margin-bottom: 30rpx;
    border-radius: 16rpx;

    &:last-child {
      border-bottom: none;
    }

    .label {
      font-size: 30rpx;
      color: #333333;
      font-weight: 500;
      margin-bottom: 24rpx;
      display: block;
    }

    .input {
      width: 100%;
      height: 88rpx;
      font-size: 28rpx;
      color: #333;
      background: #F8F9FA;
      border-radius: 12rpx;
      padding: 0 24rpx;
      border: none;
      box-sizing: border-box;
      line-height: 88rpx;
    }

    .category-selector {
      width: 100%;
      height: 88rpx;
      background: #F8F9FA;
      border-radius: 12rpx;
      padding: 0 24rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;
      box-sizing: border-box;

      .category-text {
        font-size: 28rpx;
        color: #1a1a1a;
        flex: 1;
        line-height: 88rpx;

        &.placeholder {
          color: #CCCCCC;
        }
      }

      .arrow-icon {
        width: 24rpx;
        height: 24rpx;
        margin-left: 16rpx;
      }
    }

    .textarea {
      width: 100%;
      min-height: 120rpx;
      font-size: 28rpx;
      color: #1a1a1a;
      background: #F8F9FA;
      border-radius: 12rpx;
      padding: 24rpx;
      border: none;
      box-sizing: border-box;
      line-height: 1.5;

      &.large {
        min-height: 200rpx;
      }
    }

    .radio-group {
      display: flex;
      flex-wrap: wrap;
      gap: 32rpx;

      .radio-item {
        display: flex;
        align-items: center;

        .radio {
          width: 32rpx;
          height: 32rpx;
          border: 2rpx solid #CCCCCC;
          border-radius: 50%;
          margin-right: 16rpx;
          display: flex;
          align-items: center;
          justify-content: center;

          &.checked {
            border-color: #3478f6;

            .radio-inner {
              width: 16rpx;
              height: 16rpx;
              background: #3478f6;
              border-radius: 50%;
            }
          }
        }

        .radio-text {
          font-size: 28rpx;
          color: #1a1a1a;
        }
      }
    }

    .question-list {
      .question-item {
        margin-bottom: 24rpx;

        &:last-child {
          margin-bottom: 0;
        }

        .question-input {
          width: 100%;
          height: 80rpx;
          font-size: 28rpx;
          color: #1a1a1a;
          background: #F8F9FA;
          border-radius: 12rpx;
          padding: 0 20rpx;
          border: none;
          box-sizing: border-box;
          line-height: 80rpx;
        }
      }
    }
  }

  .form-note {
    padding: 32rpx 0;

    .note-text {
      font-size: 24rpx;
      color: #999999;
      line-height: 1.6;
    }
  }
}

.create-section {
  padding: 40rpx 32rpx;
  padding-bottom: calc(40rpx + env(safe-area-inset-bottom));
  background: #F5F5F5;

  .create-btn {
    width: 100%;
    height: 96rpx;
    background: #3478f6;
    border-radius: 48rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;

    &.disabled {
      background: #CCCCCC;
    }

    .create-text {
      font-size: 32rpx;
      color: #ffffff;
      font-weight: 600;
    }
  }
}

/* 占位符样式 */
.placeholder {
  color: #CCCCCC !important;
}

/* 小程序input组件特殊样式 */
input {
  outline: none;
}

textarea {
  outline: none;
}

/* 分类选择弹窗样式 */
.category-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  box-sizing: border-box;

  .modal-content {
    width: 100%;
    max-height: 80%;
    background: #ffffff;
    border-radius: 24rpx;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .modal-header {
    padding: 40rpx 32rpx 24rpx;
    border-bottom: 1px solid #F0F0F0;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .modal-title {
      font-size: 36rpx;
      font-weight: 600;
      color: #1a1a1a;
    }

    .close-btn {
      width: 48rpx;
      height: 48rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      .close-icon {
        width: 24rpx;
        height: 24rpx;
      }
    }
  }

  .search-section {
    padding: 24rpx 32rpx;
    border-bottom: 1px solid #F0F0F0;

    .search-box {
      position: relative;
      background: #F8F9FA;
      border-radius: 24rpx;
      padding: 0 48rpx 0 80rpx;
      height: 72rpx;
      display: flex;
      align-items: center;

      .search-icon {
        position: absolute;
        left: 24rpx;
        width: 32rpx;
        height: 32rpx;
      }

      .search-input {
        flex: 1;
        font-size: 28rpx;
        color: #1a1a1a;
        height: 72rpx;
        line-height: 72rpx;
      }

      .clear-btn {
        position: absolute;
        right: 16rpx;
        width: 48rpx;
        height: 48rpx;
        display: flex;
        align-items: center;
        justify-content: center;

        .clear-icon {
          width: 24rpx;
          height: 24rpx;
        }
      }
    }
  }

  .category-list {
    flex: 1;
    min-height: 400rpx;
    max-height: 600rpx;

    .category-item {
      padding: 32rpx;
      border-bottom: 1px solid #F0F0F0;
      display: flex;
      align-items: center;

      .radio {
        width: 32rpx;
        height: 32rpx;
        border: 2rpx solid #CCCCCC;
        border-radius: 50%;
        margin-right: 24rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;

        &.checked {
          border-color: #3478f6;

          .radio-inner {
            width: 16rpx;
            height: 16rpx;
            background: #3478f6;
            border-radius: 50%;
          }
        }
      }

      .category-name {
        font-size: 32rpx;
        color: #1a1a1a;
        flex: 1;
      }
    }

    .empty-state {
      padding: 120rpx 32rpx;
      text-align: center;

      .empty-text {
        font-size: 28rpx;
        color: #999999;
      }
    }
  }

  .modal-footer {
    padding: 32rpx;
    border-top: 1px solid #F0F0F0;
    display: flex;
    gap: 24rpx;

    .cancel-btn,
    .confirm-btn {
      flex: 1;
      height: 80rpx;
      border-radius: 40rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
    }

    .cancel-btn {
      background: #F8F9FA;
      border: 1px solid #E5E5E5;

      .cancel-text {
        font-size: 32rpx;
        color: #666666;
      }
    }

    .confirm-btn {
      background: #3478f6;

      &.disabled {
        background: #CCCCCC;
      }

      .confirm-text {
        font-size: 32rpx;
        color: #ffffff;
        font-weight: 600;
      }
    }
  }
}

/* 旋转动画 */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
</style>