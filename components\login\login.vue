<template>
  <uni-popup ref="loginPopup" background-color="#fff" type="bottom" @change="onLoginPopChange">
    <view class="login-content">
      <view class="logo-box">
        <!-- <image class="icon" src="../../static/logo.jpg"></image> -->
        <view class="name">AI商协通</view>
      </view>
      <!-- <view class="right">
				<uni-icons :type="isAgree?'checkbox':'checkbox-filled'" size="30"></uni-icons>若手机号未注册将进入注册流程，注册即为同意<text>《AI》</text>
			</view> -->
      <view class="login-btn-box">
        <button class="login-btn" open-type="getPhoneNumber" @getphonenumber="decryptPhoneNumber">
          手机号快捷登录
        </button>
      </view>
    </view>
  </uni-popup>
</template>

<script setup>
  import {
    onMounted,
    ref,
    watch
  } from 'vue';
  import {
    useUserStore
  } from '@/stores/user.js'
  import {
    phoneAuthLoginApi
  } from '@/api/index.js';
  const userStore = useUserStore()
  // 授权弹窗类型
  // const authType = computed(() => userStore.modalStatus);
  const isAgree = ref(false)
  const loginPopup = ref(null);

  const openPopup = () => {
    loginPopup.value.open()
  }
  watch(() => userStore.modalStatus, (newValue, oldValue) => {
    if (newValue === 'login') {
      loginPopup.value.open()
      userStore.delete_user_info();
    }
  })
  const onLoginPopChange = (e) => {
    if (!e.show) {
      userStore.modalStatus = '';
    }
  }
  const decryptPhoneNumber = async (e) => {
    console.log(e, '-------------???')
    try {
      if (!e.detail.code) {
        uni.showToast({
          title: '用户拒绝授权',
          icon: 'none',
        });
        return;
      }
      uni.showLoading({
        title: '请稍后',
        mask: true,
      });
      uni.login({
        provider: 'weixin',
        success: (loginRes) => {
          let code = loginRes.code;
          phoneAuthLoginApi({
            jsCode: code,
            getPhoneCode: e.detail.code,
            merchantGuid: userStore.merchantGuid,
            invitationText: '',
          }).then((res) => {
            userStore.set_user_token(res.data.token);
            userStore.set_user_info(res.data.userInfo);
            uni.hideLoading();
            uni.showToast({
              title: '登录成功',
              icon: 'none',
              mask: true,
              duration: 2000,
            });
            loginPopup.value.close()
            userStore.modalStatus = "";
          });
        },
      });
    } catch (err) {
      uni.hideLoading();
      loginPopup.value.close()
      userStore.modalStatus = "";
      uni.showToast({
        title: err.msg || '登录失败',
        icon: 'none',
        mask: true,
        duration: 2000,
      });
    }
  }
</script>

<style lang="scss" scoped>
  .login-content {
    height: 500rpx;

    .logo-box {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      padding: 40rpx 0;

      .icon {
        width: 160rpx;
        height: 160rpx;
        display: block;
        border-radius: 50%;
      }

      .name {
        font-size: 36rpx;
        font-weight: bold;
        margin-top: 30rpx;
      }
    }

    .login-btn {
      width: 600rpx;
      margin-top: 60rpx;
      color: #fff;
      background: linear-gradient(136deg, #7531f6 0%, #686bf2 100%);
      height: 80rpx;
      line-height: 80rpx;
      font-size: 28rpx;
    }
  }
</style>