import base from '@/config/config.js';
import {
	randomStr,
	autographFun
} from '../utils/requestUtil.js';
import {
	useUserStore
} from '@/stores/user.js'

let timeout = 60000;
const baseUrl = base.baseUrl;
const toastTip = (title, icon, time) => {
	uni.showToast({
		title: title || '网络连接超时,请稍后重试!',
		icon: icon || 'none',
		duration: time || 1500
	})
}

const getKeys = (obj) => {
	let keys = [];
	for (let key in obj) {
		if (obj.hasOwnProperty(key)) {
			keys.push(key);
		}
	}
	return keys;
}
const tansParams = (params) => {
	let result = '';
	for (const propName of this.getKeys(params)) {
		const value = params[propName]
		let part = encodeURIComponent(propName) + "=";
		if (value !== null && value !== "" && typeof (value) !== "undefined") {
			if (typeof value === 'object') {
				for (const key of this.getKeys(value)) {
					if (value[key] !== null && value[key] !== "" && typeof (value[key]) !== 'undefined') {
						let params = propName + '[' + key + ']';
						let subPart = encodeURIComponent(params) + "=";
						result += subPart + encodeURIComponent(value[key]) + "&";
					}
				}
			} else {
				result += part + encodeURIComponent(value) + "&"
			}
		}
	}
	return result
}
// 对象转query字符串的方法
const queryStr = (obj) => {
	// 首先判断obj是否为真，为真则进行处理，不然直接return
	if (obj) {
		// 定义变量接收query字符串
		let query = ""
		// 循环遍历对象
		for (let i in obj) {
			// 定义变量接收对象的value值
			let value = obj[i]
			// 若对象的value值为数组，则进行join打断
			if (Array.isArray(value)) {
				value = value.join(",")
			}
			// 进行字符串拼接
			query += `&${i}=${value}`
		}
		// replace返回一个新的字符串，要用query重新接受一下，并把第一个&替换为?
		query = query.replace('&', '?')
		// 返回生成的query字符串
		return query
	}
	return ""
}
const request = (config) => {
	/**
	 * @description  如果isToken为true的时候需要token(一般会在登录的时候存储token和token_type)  为false不需要token
	 * @description  config.header['Authorization'] = 'Bearer ' + uni.getStorageSync('token'); // token_type 一般情况下为'Bearer ' 切记有空格哦
	 * @description  config.header['Content-Type'] = 'application/x-www-form-urlencoded'; // 常规请求头配置
	 */
	// const isToken = config.header?.isToken ? config.header?.isToken : false;
	// config.header = config.header ? config.header : {};
	// if (getToken() && isToken) {
	//   config.header['Authorization'] = uni.getStorageSync('token_type') + uni.getStorageSync('token');
	// };
	const userStore = useUserStore()
	config.urlSuffix = {
		app_guid: 'e108201b02ae42e686bcc4c302cbbd11', //应用唯一标识
		expires: parseInt((new Date().getTime() / 1000).toFixed(0)), //当前时间戳
		token: userStore.userToken || 'notoken',
		noncestr: randomStr(true, true, true, 32),
		merchantGuid: userStore.merchantGuid,
		app_type: 'wechat',
	};
	// if (!config.header.ifTouristLogin) {
	config.urlSuffix.signature = encodeURIComponent(autographFun(config)); //签名
	// }
	/**
	 * @description  get请求映射params参数
	 */
	if (config.params) {
		let url = config.url + '?' + tansParams(config.params)
		url = url.slice(0, -1)
		config.url = url
	}
	return new Promise((resolve, reject) => {
		uni.request({
			method: config.method || 'POST',
			timeout: config.timeout || timeout,
			url: baseUrl + config.url + queryStr(config.urlSuffix),
			data: config.data,
			header: config.header,
			dataType: 'json',
			success(res) {
				if (res.data.code === 704001) {
					// toastTip(res.data.msg)
					// userStore.modalStatus = "login"
					userStore.delete_user_info();
					return
				}
				/**
				 * @description 请求成功返回的数据
				 */
				if (res.data.code === 0) {
					resolve(res.data)
				} else {
					console.log(res.data, '--------------------???')
					toastTip(res.data.msg)
					reject(res.data)
				}
			},
			fail: (error) => {
				console.log(error, 'errorerrorerror')
				let {
					errMsg
				} = error
				if (errMsg === 'Network Error') {
					errMsg = '后端接口连接异常'
				} else if (errMsg.includes('timeout')) {
					errMsg = 'AI算力繁忙'
				} else if (errMsg.includes('Request failed with status code')) {
					errMsg = '系统接口' + errMsg.substr(errMsg.length - 3) + '异常'
				}
				toastTip(errMsg, 'none', 1500)
				/**
				 * @description 请求失败返回的消息
				 */
				reject(error)
			},
			complete(res) {
				/**
				 * @description 请求完做的事
				 */
			}
		})
	})
}

/**
 *  @description 暴露出request请求供其他业务使用
 */

export default request