"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const api_index = require("../../api/index.js");
const stores_user = require("../../stores/user.js");
if (!Array) {
  const _easycom_z_paging2 = common_vendor.resolveComponent("z-paging");
  _easycom_z_paging2();
}
const _easycom_z_paging = () => "../../uni_modules/z-paging/components/z-paging/z-paging.js";
if (!Math) {
  _easycom_z_paging();
}
const _sfc_main = {
  __name: "favorite-list",
  setup(__props) {
    const userStore = stores_user.useUserStore();
    const favoritesList = common_vendor.ref([]);
    const paging = common_vendor.ref(null);
    const showContentModal = common_vendor.ref(false);
    const currentContent = common_vendor.ref("");
    const queryList = async (page, pageSize) => {
      try {
        let res = await api_index.getMyCollectionListApi({
          merchantGuid: userStore.merchantGuid,
          page,
          pageSize
        });
        paging.value.complete(res.data.list || []);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/my/favorite-list.vue:74", "获取收藏列表失败:", error);
        common_vendor.index.showToast({
          title: "加载失败",
          icon: "none"
        });
        paging.value.complete(false);
      }
    };
    const handleCopy = (item) => {
      common_vendor.index.setClipboardData({
        data: item.messageContent,
        success() {
          common_vendor.index.showToast({
            title: "复制成功",
            icon: "none"
          });
        }
      });
    };
    const handleViewAll = (item) => {
      currentContent.value = item.messageContent || "";
      showContentModal.value = true;
    };
    const closeContentModal = () => {
      showContentModal.value = false;
      currentContent.value = "";
    };
    const handleUnfavorite = async (item) => {
      try {
        await api_index.cancelCollectMessageApi({
          merchantGuid: userStore.merchantGuid,
          messageGuid: item.messageGuid
        });
        common_vendor.index.showToast({
          title: "取消收藏成功",
          icon: "none"
        });
        if (paging.value) {
          paging.value.reload();
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/my/favorite-list.vue:122", "取消收藏失败:", error);
        common_vendor.index.showToast({
          title: "操作失败",
          icon: "none"
        });
      }
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.f(favoritesList.value, (item, index, i0) => {
          return {
            a: common_vendor.t(item.collectTime),
            b: common_vendor.t(item.contentPreview),
            c: common_vendor.o(($event) => handleCopy(item), index),
            d: common_vendor.o(($event) => handleViewAll(item), index),
            e: common_vendor.o(($event) => handleUnfavorite(item), index),
            f: index
          };
        }),
        b: common_assets._imports_0$2,
        c: common_assets._imports_1$1,
        d: common_assets._imports_2$2,
        e: common_vendor.sr(paging, "09d2b5a3-0", {
          "k": "paging"
        }),
        f: common_vendor.o(queryList),
        g: common_vendor.o(($event) => favoritesList.value = $event),
        h: common_vendor.p({
          auto: true,
          ["auto-clean-list-when-reload"]: false,
          modelValue: favoritesList.value
        }),
        i: showContentModal.value
      }, showContentModal.value ? {
        j: common_vendor.o(closeContentModal),
        k: currentContent.value,
        l: common_vendor.o(() => {
        }),
        m: common_vendor.o(closeContentModal)
      } : {});
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-09d2b5a3"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/my/favorite-list.js.map
