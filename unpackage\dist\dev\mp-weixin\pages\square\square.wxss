/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.container.data-v-6bc6c6b7 {
  background-color: #ffffff;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}
.empty-container.data-v-6bc6c6b7 {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 120rpx 0;
  color: #999999;
  font-size: 28rpx;
}

/* 顶部标签栏 */
.tabs-container.data-v-6bc6c6b7 {
  background-color: #ffffff;
  padding: 0;
}
.tabs-container .search-icon.data-v-6bc6c6b7 {
  padding: 0 20px;
  display: flex;
  align-items: center;
}
.tabs-container .search-icon .icon.data-v-6bc6c6b7 {
  width: 60rpx;
  height: 60rpx;
  display: block;
}

/* 内容容器 */
.content-container.data-v-6bc6c6b7 {
  flex: 1;
  overflow: hidden;
  background-color: #ffffff;
  /* 默认高度，会被JavaScript动态设置覆盖 */
  height: calc(100vh - 88rpx);
  /* 为Android机型添加最小高度保障 */
  min-height: 600px;
}

/* tab切换容器 */
.tab-swiper.data-v-6bc6c6b7 {
  width: 100%;
  /* 默认高度，会被JavaScript动态设置覆盖 */
  height: calc(100vh - 88rpx);
  /* 为Android机型添加最小高度保障 */
  min-height: 600px;
}
.scroll-view.data-v-6bc6c6b7 {
  /* 默认高度，会被JavaScript动态设置覆盖 */
  height: calc(100vh - 88rpx);
  min-height: 600px;
  width: 100%;
}

/* 确保swiper-item高度正确 */
swiper-item.data-v-6bc6c6b7 {
  /* 默认高度，会被JavaScript动态设置覆盖 */
  height: calc(100vh - 88rpx);
  min-height: 600px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 智能体列表 */
.agent-list.data-v-6bc6c6b7 {
  padding: 0 20px;
  background-color: #ffffff;
}
.agent-item.data-v-6bc6c6b7 {
  display: flex;
  align-items: center;
  padding: 20px 0;
}
.agent-item.data-v-6bc6c6b7:last-child {
  border-bottom: none;
}
.avatar.data-v-6bc6c6b7 {
  width: 48px;
  height: 48px;
  margin-right: 16px;
  flex-shrink: 0;
}
.avatar .avatar-img.data-v-6bc6c6b7 {
  width: 100%;
  height: 100%;
  border-radius: 24px;
}
.content.data-v-6bc6c6b7 {
  flex: 1;
  margin-right: 16px;
}
.content .title.data-v-6bc6c6b7 {
  font-size: 32rpx;
  font-weight: 600;
  color: #222;
  margin-bottom: 6px;
  line-height: 1.3;
}
.content .description.data-v-6bc6c6b7 {
  font-size: 24rpx;
  color: #333;
  line-height: 1.4;
  margin-bottom: 6px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}
.content .author.data-v-6bc6c6b7 {
  font-size: 24rpx;
  color: #999999;
}
.action-btn.data-v-6bc6c6b7 {
  flex-shrink: 0;
  height: 32px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 116rpx;
  background-color: #007AFF;
}
.action-btn .btn-text.data-v-6bc6c6b7 {
  font-size: 28rpx;
  font-weight: 500;
  color: #ffffff;
}
.action-btn.subscribed.data-v-6bc6c6b7 {
  background-color: #F2F2F7;
  border: none;
}
.action-btn.subscribed .btn-text.data-v-6bc6c6b7 {
  color: #8E8E93;
}
.action-btn.activated.data-v-6bc6c6b7 {
  background-color: #E7EDFA;
}
.action-btn.activated .btn-text.data-v-6bc6c6b7 {
  color: #2A64F6;
}