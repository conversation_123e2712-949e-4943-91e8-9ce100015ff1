/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.modal-overlay.data-v-09d2b5a3 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  overflow: hidden;
  touch-action: none;
}
.modal-overlay .modal-content.data-v-09d2b5a3 {
  background: #ffffff;
  width: 90%;
  height: 600px;
  border-radius: 24rpx;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
.modal-overlay .modal-content .modal-header.data-v-09d2b5a3 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1px solid #F0F0F0;
  flex-shrink: 0;
}
.modal-overlay .modal-content .modal-header .modal-title.data-v-09d2b5a3 {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
}
.modal-overlay .modal-content .modal-header .close-btn.data-v-09d2b5a3 {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #F5F5F5;
}
.modal-overlay .modal-content .modal-header .close-btn .close-text.data-v-09d2b5a3 {
  font-size: 36rpx;
  color: #666666;
  line-height: 1;
}
.modal-overlay .modal-content .modal-body.data-v-09d2b5a3 {
  flex: 1;
  height: 0;
  overflow: hidden;
}
.modal-overlay .modal-content .modal-body .rich-content.data-v-09d2b5a3 {
  font-size: 28rpx;
  line-height: 1.6;
  color: #1a1a1a;
  word-break: break-all;
  padding: 32rpx;
}
.favorite-list-page.data-v-09d2b5a3 {
  background: #F5F5F5;
  min-height: 100vh;
}
.favorite-list-page .favorites-content.data-v-09d2b5a3 {
  padding: 32rpx;
}
.favorite-list-page .favorites-content .favorite-card.data-v-09d2b5a3 {
  background: #fff;
  border-radius: 24rpx;
  margin-bottom: 24rpx;
  padding: 32rpx;
}
.favorite-list-page .favorites-content .favorite-card .favorite-header.data-v-09d2b5a3 {
  margin-bottom: 16rpx;
}
.favorite-list-page .favorites-content .favorite-card .favorite-header .favorite-date.data-v-09d2b5a3 {
  font-size: 24rpx;
  color: #999999;
  display: block;
}
.favorite-list-page .favorites-content .favorite-card .favorite-content.data-v-09d2b5a3 {
  margin-bottom: 24rpx;
}
.favorite-list-page .favorites-content .favorite-card .favorite-content .content-text.data-v-09d2b5a3 {
  font-size: 28rpx;
  color: #1a1a1a;
  line-height: 1.6;
  display: block;
}
.favorite-list-page .favorites-content .favorite-card .favorite-actions.data-v-09d2b5a3 {
  display: flex;
  gap: 16rpx;
  flex-wrap: wrap;
}
.favorite-list-page .favorites-content .favorite-card .favorite-actions .action-btn.data-v-09d2b5a3 {
  display: flex;
  align-items: center;
  background: #E6F0FF;
  border-radius: 20rpx;
  padding: 12rpx 20rpx;
  border: none;
  flex-shrink: 0;
  min-width: 0;
}
.favorite-list-page .favorites-content .favorite-card .favorite-actions .action-btn .action-icon.data-v-09d2b5a3 {
  width: 38rpx;
  height: 38rpx;
  margin-right: 8rpx;
  flex-shrink: 0;
}
.favorite-list-page .favorites-content .favorite-card .favorite-actions .action-btn .action-text.data-v-09d2b5a3 {
  font-size: 24rpx;
  color: #222222;
  font-weight: 500;
  white-space: nowrap;
}