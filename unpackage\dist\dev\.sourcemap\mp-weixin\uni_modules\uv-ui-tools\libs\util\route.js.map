{"version": 3, "file": "route.js", "sources": ["uni_modules/uv-ui-tools/libs/util/route.js"], "sourcesContent": ["/**\r\n * 路由跳转方法，该方法相对于直接使用uni.xxx的好处是使用更加简单快捷\r\n * 并且带有路由拦截功能\r\n */\r\nimport { queryParams, deepMerge, page } from '@/uni_modules/uv-ui-tools/libs/function/index.js'\r\nclass Router {\r\n\tconstructor() {\r\n\t\t// 原始属性定义\r\n\t\tthis.config = {\r\n\t\t\ttype: 'navigateTo',\r\n\t\t\turl: '',\r\n\t\t\tdelta: 1, // navigateBack页面后退时,回退的层数\r\n\t\t\tparams: {}, // 传递的参数\r\n\t\t\tanimationType: 'pop-in', // 窗口动画,只在APP有效\r\n\t\t\tanimationDuration: 300, // 窗口动画持续时间,单位毫秒,只在APP有效\r\n\t\t\tintercept: false ,// 是否需要拦截\r\n\t\t\tevents: {} // 页面间通信接口，用于监听被打开页面发送到当前页面的数据。hbuilderx 2.8.9+ 开始支持。\r\n\t\t}\r\n\t\t// 因为route方法是需要对外赋值给另外的对象使用，同时route内部有使用this，会导致route失去上下文\r\n\t\t// 这里在构造函数中进行this绑定\r\n\t\tthis.route = this.route.bind(this)\r\n\t}\r\n\r\n\t// 判断url前面是否有\"/\"，如果没有则加上，否则无法跳转\r\n\taddRootPath(url) {\r\n\t\treturn url[0] === '/' ? url : `/${url}`\r\n\t}\r\n\r\n\t// 整合路由参数\r\n\tmixinParam(url, params) {\r\n\t\turl = url && this.addRootPath(url)\r\n\r\n\t\t// 使用正则匹配，主要依据是判断是否有\"/\",\"?\",\"=\"等，如“/page/index/index?name=mary\"\r\n\t\t// 如果有url中有get参数，转换后无需带上\"?\"\r\n\t\tlet query = ''\r\n\t\tif (/.*\\/.*\\?.*=.*/.test(url)) {\r\n\t\t\t// object对象转为get类型的参数\r\n\t\t\tquery = queryParams(params, false)\r\n\t\t\t// 因为已有get参数,所以后面拼接的参数需要带上\"&\"隔开\r\n\t\t\treturn url += `&${query}`\r\n\t\t}\r\n\t\t// 直接拼接参数，因为此处url中没有后面的query参数，也就没有\"?/&\"之类的符号\r\n\t\tquery = queryParams(params)\r\n\t\treturn url += query\r\n\t}\r\n\r\n\t// 对外的方法名称\r\n\tasync route(options = {}, params = {}) {\r\n\t\t// 合并用户的配置和内部的默认配置\r\n\t\tlet mergeConfig = {}\r\n\r\n\t\tif (typeof options === 'string') {\r\n\t\t\t// 如果options为字符串，则为route(url, params)的形式\r\n\t\t\tmergeConfig.url = this.mixinParam(options, params)\r\n\t\t\tmergeConfig.type = 'navigateTo'\r\n\t\t} else {\r\n\t\t\tmergeConfig = deepMerge(this.config, options)\r\n\t\t\t// 否则正常使用mergeConfig中的url和params进行拼接\r\n\t\t\tmergeConfig.url = this.mixinParam(options.url, options.params)\r\n\t\t}\r\n\t\t// 如果本次跳转的路径和本页面路径一致，不执行跳转，防止用户快速点击跳转按钮，造成多次跳转同一个页面的问题\r\n\t\tif (mergeConfig.url === page()) return\r\n\r\n\t\tif (params.intercept) {\r\n\t\t\tmergeConfig.intercept = params.intercept\r\n\t\t}\r\n\t\t// params参数也带给拦截器\r\n\t\tmergeConfig.params = params\r\n\t\t// 合并内外部参数\r\n\t\tmergeConfig = deepMerge(this.config, mergeConfig)\r\n\t\t// 判断用户是否定义了拦截器\r\n\t\tif (typeof mergeConfig.intercept === 'function') {\r\n\t\t\t// 定一个promise，根据用户执行resolve(true)或者resolve(false)来决定是否进行路由跳转\r\n\t\t\tconst isNext = await new Promise((resolve, reject) => {\r\n\t\t\t\tmergeConfig.intercept(mergeConfig, resolve)\r\n\t\t\t})\r\n\t\t\t// 如果isNext为true，则执行路由跳转\r\n\t\t\tisNext && this.openPage(mergeConfig)\r\n\t\t} else {\r\n\t\t\tthis.openPage(mergeConfig)\r\n\t\t}\r\n\t}\r\n\r\n\t// 执行路由跳转\r\n\topenPage(config) {\r\n\t\t// 解构参数\r\n\t\tconst {\r\n\t\t\turl,\r\n\t\t\ttype,\r\n\t\t\tdelta,\r\n\t\t\tanimationType,\r\n\t\t\tanimationDuration,\r\n\t\t\tevents\r\n\t\t} = config\r\n\t\tif (config.type == 'navigateTo' || config.type == 'to') {\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl,\r\n\t\t\t\tanimationType,\r\n\t\t\t\tanimationDuration,\r\n\t\t\t\tevents\r\n\t\t\t})\r\n\t\t}\r\n\t\tif (config.type == 'redirectTo' || config.type == 'redirect') {\r\n\t\t\tuni.redirectTo({\r\n\t\t\t\turl\r\n\t\t\t})\r\n\t\t}\r\n\t\tif (config.type == 'switchTab' || config.type == 'tab') {\r\n\t\t\tuni.switchTab({\r\n\t\t\t\turl\r\n\t\t\t})\r\n\t\t}\r\n\t\tif (config.type == 'reLaunch' || config.type == 'launch') {\r\n\t\t\tuni.reLaunch({\r\n\t\t\t\turl\r\n\t\t\t})\r\n\t\t}\r\n\t\tif (config.type == 'navigateBack' || config.type == 'back') {\r\n\t\t\tuni.navigateBack({\r\n\t\t\t\tdelta\r\n\t\t\t})\r\n\t\t}\r\n\t}\r\n}\r\n\r\nexport default (new Router()).route"], "names": ["queryParams", "deepMerge", "page", "uni"], "mappings": ";;;AAKA,MAAM,OAAO;AAAA,EACZ,cAAc;AAEb,SAAK,SAAS;AAAA,MACb,MAAM;AAAA,MACN,KAAK;AAAA,MACL,OAAO;AAAA;AAAA,MACP,QAAQ,CAAE;AAAA;AAAA,MACV,eAAe;AAAA;AAAA,MACf,mBAAmB;AAAA;AAAA,MACnB,WAAW;AAAA;AAAA,MACX,QAAQ,CAAE;AAAA;AAAA,IACV;AAGD,SAAK,QAAQ,KAAK,MAAM,KAAK,IAAI;AAAA,EACjC;AAAA;AAAA,EAGD,YAAY,KAAK;AAChB,WAAO,IAAI,CAAC,MAAM,MAAM,MAAM,IAAI,GAAG;AAAA,EACrC;AAAA;AAAA,EAGD,WAAW,KAAK,QAAQ;AACvB,UAAM,OAAO,KAAK,YAAY,GAAG;AAIjC,QAAI,QAAQ;AACZ,QAAI,gBAAgB,KAAK,GAAG,GAAG;AAE9B,cAAQA,0CAAAA,YAAY,QAAQ,KAAK;AAEjC,aAAO,OAAO,IAAI,KAAK;AAAA,IACvB;AAED,YAAQA,0CAAW,YAAC,MAAM;AAC1B,WAAO,OAAO;AAAA,EACd;AAAA;AAAA,EAGD,MAAM,MAAM,UAAU,IAAI,SAAS,CAAA,GAAI;AAEtC,QAAI,cAAc,CAAE;AAEpB,QAAI,OAAO,YAAY,UAAU;AAEhC,kBAAY,MAAM,KAAK,WAAW,SAAS,MAAM;AACjD,kBAAY,OAAO;AAAA,IACtB,OAAS;AACN,oBAAcC,0CAAS,UAAC,KAAK,QAAQ,OAAO;AAE5C,kBAAY,MAAM,KAAK,WAAW,QAAQ,KAAK,QAAQ,MAAM;AAAA,IAC7D;AAED,QAAI,YAAY,QAAQC,0CAAI,KAAA;AAAI;AAEhC,QAAI,OAAO,WAAW;AACrB,kBAAY,YAAY,OAAO;AAAA,IAC/B;AAED,gBAAY,SAAS;AAErB,kBAAcD,0CAAS,UAAC,KAAK,QAAQ,WAAW;AAEhD,QAAI,OAAO,YAAY,cAAc,YAAY;AAEhD,YAAM,SAAS,MAAM,IAAI,QAAQ,CAAC,SAAS,WAAW;AACrD,oBAAY,UAAU,aAAa,OAAO;AAAA,MAC9C,CAAI;AAED,gBAAU,KAAK,SAAS,WAAW;AAAA,IACtC,OAAS;AACN,WAAK,SAAS,WAAW;AAAA,IACzB;AAAA,EACD;AAAA;AAAA,EAGD,SAAS,QAAQ;AAEhB,UAAM;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACH,IAAM;AACJ,QAAI,OAAO,QAAQ,gBAAgB,OAAO,QAAQ,MAAM;AACvDE,oBAAAA,MAAI,WAAW;AAAA,QACd;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ,CAAI;AAAA,IACD;AACD,QAAI,OAAO,QAAQ,gBAAgB,OAAO,QAAQ,YAAY;AAC7DA,oBAAAA,MAAI,WAAW;AAAA,QACd;AAAA,MACJ,CAAI;AAAA,IACD;AACD,QAAI,OAAO,QAAQ,eAAe,OAAO,QAAQ,OAAO;AACvDA,oBAAAA,MAAI,UAAU;AAAA,QACb;AAAA,MACJ,CAAI;AAAA,IACD;AACD,QAAI,OAAO,QAAQ,cAAc,OAAO,QAAQ,UAAU;AACzDA,oBAAAA,MAAI,SAAS;AAAA,QACZ;AAAA,MACJ,CAAI;AAAA,IACD;AACD,QAAI,OAAO,QAAQ,kBAAkB,OAAO,QAAQ,QAAQ;AAC3DA,oBAAAA,MAAI,aAAa;AAAA,QAChB;AAAA,MACJ,CAAI;AAAA,IACD;AAAA,EACD;AACF;AAEA,MAAA,QAAgB,IAAI,OAAM,EAAI;;"}