"use strict";const e=require("../../common/vendor.js"),a=require("../../common/assets.js"),i=require("../../stores/user.js"),n=require("../../api/index.js"),s=require("../../api/common.js"),o={__name:"index",setup(o){const t=i.useUserStore();let r=e.reactive({headImgUrl:"",nickname:"",email:""});const c=e.ref(!1),l=e.ref(!1);e.onMounted((()=>{(async()=>{let e=await n.getUserInfoApi();r=Object.assign(r,e.data)})()}));const d=()=>{l.value||e.index.chooseImage({count:1,sizeType:["compressed"],sourceType:["album","camera"],success:async e=>{const a=e.tempFilePaths[0];await m(a)},fail:e=>{console.error("选择图片失败:",e)}})},m=async a=>{try{l.value=!0,e.index.showLoading({title:"上传中...",mask:!0});const i=await s.updataFileFun(a),n=JSON.parse(i.data);if(0!==n.code)throw new Error(n.msg||"上传失败");r.headImgUrl=n.data,e.index.showToast({title:"头像上传成功",icon:"success"})}catch(i){console.error("上传头像失败:",i),e.index.showToast({title:"上传失败",icon:"none"})}finally{l.value=!1,e.index.hideLoading()}},u=async()=>{if(!c.value)if(r.nickname.trim())if(!r.email||h(r.email))try{c.value=!0,e.index.showLoading({title:"保存中...",mask:!0});const a={headImgUrl:r.headImgUrl,nickname:r.nickname.trim(),email:r.email.trim()},i=await n.updateUserInfoApi(a);if(0!==i.code)throw new Error(i.msg||"保存失败");{const i={...t.userInfo,...a};t.set_user_info(i),e.index.showToast({title:"保存成功",icon:"success"}),setTimeout((()=>{e.index.navigateBack()}),1500)}}catch(a){console.error("保存失败:",a),e.index.showToast({title:a.message||"保存失败",icon:"none"})}finally{c.value=!1,e.index.hideLoading()}else e.index.showToast({title:"请输入正确的邮箱格式",icon:"none"});else e.index.showToast({title:"请输入昵称",icon:"none"})},h=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e);return(i,n)=>({a:e.unref(r).headImgUrl,b:a._imports_0$1,c:e.o(d),d:e.unref(r).nickname,e:e.o((a=>e.unref(r).nickname=a.detail.value)),f:e.unref(r).email,g:e.o((a=>e.unref(r).email=a.detail.value)),h:e.t(c.value?"保存中...":"保存修改"),i:e.o(u),j:c.value?1:""})}},t=e._export_sfc(o,[["__scopeId","data-v-1a0cd2e4"]]);wx.createPage(t);
