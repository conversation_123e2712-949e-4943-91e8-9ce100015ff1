"use strict";const e=require("../../common/vendor.js"),o=require("../../api/index.js"),a=require("../../stores/user.js"),t=require("../../api/common.js"),i={__name:"sub-list",setup(i){const n=a.useUserStore(),c=e.reactive({headImgUrl:"",nickname:"",chat_count:0}),s=e.ref([]);e.ref([]);const r=e.ref(!1),u=e.ref([]),d=e.ref(!1),l=e.ref(0),h=async()=>{try{const e=await o.getUserInfoApi({merchantGuid:n.merchantGuid});0===e.code&&Object.assign(c,e.data)}catch(e){console.error("获取用户信息失败:",e)}},g=e.reactive({rule1:"",rule2:"",rule_notice:""}),m=async()=>{try{const e=await o.getSubscriptionRuleApi({merchantGuid:n.merchantGuid});0===e.code&&(g.rule1=e.data.zhuanshu.rule1,g.rule2=e.data.zhuanshu.rule2,g.rule_notice=e.data.zhuanshu.rule_notice)}catch(e){console.error("获取规则失败:",e)}},p=async()=>{e.index.showToast({title:"暂未开放",icon:"none"})},y=()=>{r.value=!1},b=async(a,t)=>{t++;try{(await o.querySubscriptionOrderApi({orderNo:a})).data.isPaid?(e.index.showToast({title:"订阅成功",icon:"success"}),(async()=>{try{const e=await o.getSubscriptionListApi({merchantGuid:n.merchantGuid});0===e.code&&(u.value=e.data.creators)}catch(e){console.error("获取创作者列表失败:",e)}})(),y()):t>12?e.index.showToast({title:"支付超时",icon:"none"}):setTimeout((()=>{b(a,t)}),2e3)}catch(i){e.index.showToast({title:i.msg||"查询支付状态失败",icon:"none"})}};return e.onLoad((()=>{"ios"===e.index.getSystemInfoSync().osName&&(d.value=!0),n.userToken&&(h(),m())})),e.onShow((()=>{n.userToken&&(h(),m())})),(a,i)=>e.e({a:"https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/84d756efe28f4116a271e357f48d04e6.png",b:e.t(g.rule1),c:"https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/77e643ee1cd3492ba370158addccd825.png",d:e.t(g.rule2),e:"https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/46348d225bb54770a614dba856c5193e.png",f:e.t(g.rule_notice),g:c.headImgUrl||"https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/c4d5b3ada53740d5b862f274ce48ef0c.png",h:e.t(c.nickname||"用户Aric"),i:e.t(c.chat_count),j:e.t(s.value.length),k:e.o(p),l:r.value},r.value?{m:e.o(y),n:e.f(u.value,((a,i,c)=>({a:a.creatorAvatar,b:e.t(a.creatorName),c:e.t(a.creatorDesc),d:e.t(a.agentCount),e:e.t(a.subscriptionPriceYuan),f:e.o((e=>(e=>{console.log("查看创作者详情:",e)})(a)),a.guid),g:e.t(a.isSubscribed?"已订阅":d.value?"IOS暂不支持":"订阅"),h:a.isSubscribed?1:"",i:e.o((i=>(async a=>{if(d.value)e.index.showToast({title:"IOS暂不支持",icon:"none"});else if(a.isSubscribed)e.index.showToast({title:"已订阅该创作者",icon:"none"});else if(n.userToken)try{e.index.showLoading({title:"正在创建订单...",mask:!0});const i=await o.subscribeCreatorApi({merchantGuid:n.merchantGuid,creatorGuid:a.guid,payEnv:"xcx"});e.index.hideLoading(),t.miniPay(i.data.payInfo).then((async()=>{b(i.data.orderNo,l.value)}),(o=>{e.index.showToast({title:o.msg||"支付失败",icon:"none"})}))}catch(i){e.index.hideLoading(),console.error("创建订单失败:",i),e.index.showToast({title:i.message||"创建订单失败",icon:"none"})}else e.index.showToast({title:"请先登录",icon:"none"})})(a)),a.guid),j:a.guid}))),o:d.value?1:"",p:e.o((()=>{})),q:e.o(y)}:{})}},n=e._export_sfc(i,[["__scopeId","data-v-eb00fcfb"]]);wx.createPage(n);
