{"version": 3, "file": "cz-index.js", "sources": ["pages/rule/cz-index.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvcnVsZS9jei1pbmRleC52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"rule-page\">\r\n    <!-- 背景图片 -->\r\n    <image class=\"background-image\" :src=\"ruleBg1\" mode=\"aspectFill\" />\r\n\r\n    <!-- 标题图片 -->\r\n    <view class=\"title-section\">\r\n      <image class=\"title-image\" :src=\"ruleTitleBg1\" mode=\"aspectFit\" />\r\n    </view>\r\n\r\n    <!-- 规则内容 -->\r\n    <view class=\"content-section\">\r\n      <view class=\"rule-content\">\r\n        <!-- <view class=\"rule-item\" v-html=\"htmlContent\"></view> -->\r\n        <rich-text :nodes=\"htmlContent\"></rich-text>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 去创作按钮 -->\r\n    <view class=\"action-section\">\r\n      <view class=\"create-btn\" @click=\"handleGoCreate\">\r\n        <text class=\"create-text\">去创作</text>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref } from 'vue'\r\nimport { platformRulesApi } from '@/api/index.js'\r\nimport { useUserStore } from '@/stores/user.js'\r\nconst userStore = useUserStore()\r\nconst ruleTitleBg1 = 'https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/6886986325964ded9f4e231bd0c0e6ad.png'\r\n// const ruleTitleBg2 = 'https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/3901e1ecde244f119393a75b5acf16b4.png'\r\n// const ruleTitleBg3 = 'https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/306dd6d0badd46b697aadce17ef1d853.png'\r\nconst ruleBg1 = 'https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/134edc12f1494bb6b1c23098140f5610.png'\r\n// const ruleBg2 = 'https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/9c3e9e617f9a46f38419cb9f0fd1632d.png'\r\n// const ruleBg3 = 'https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/fab54f3a68b04bc5bf6df56a918d9a10.png'\r\n\r\n// 去创作按钮点击事件\r\nconst handleGoCreate = () => {\r\n  // 跳转到创建智能体页面\r\n  uni.navigateTo({\r\n    url: '/pages/create-agent/index'\r\n  })\r\n}\r\nlet htmlContent = ref('')\r\nconst platformRules = async () => {\r\n  let res = await platformRulesApi({\r\n    merchantGuid: userStore.merchantGuid\r\n  })\r\n  htmlContent.value = res.data.creationRules.content\r\n}\r\nplatformRules()\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.rule-page {\r\n  background: #ffffff;\r\n  min-height: 100vh;\r\n  position: relative;\r\n\r\n  .background-image {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 100%;\r\n    z-index: 1;\r\n  }\r\n\r\n  .title-section {\r\n    position: relative;\r\n    z-index: 2;\r\n    padding: 40rpx 32rpx 20rpx;\r\n\r\n    .title-image {\r\n      width: 560rpx;\r\n      height: 60px;\r\n    }\r\n  }\r\n\r\n  .content-section {\r\n    position: relative;\r\n    z-index: 2;\r\n    margin: 20rpx 32rpx;\r\n    background: #ffffff;\r\n    border-radius: 24rpx;\r\n    padding: 32rpx 32rpx 100px;\r\n    margin-top: -20px;\r\n\r\n    .rule-content {\r\n      .rule-item {\r\n        margin-bottom: 32rpx;\r\n\r\n        &:last-child {\r\n          margin-bottom: 0;\r\n        }\r\n\r\n        .rule-title {\r\n          display: block;\r\n          font-size: 32rpx;\r\n          font-weight: 600;\r\n          color: #1a1a1a;\r\n          margin-bottom: 16rpx;\r\n          line-height: 1.4;\r\n        }\r\n\r\n        .rule-text {\r\n          display: block;\r\n          font-size: 28rpx;\r\n          color: #333333;\r\n          line-height: 1.6;\r\n          margin-bottom: 12rpx;\r\n\r\n          &:last-child {\r\n            margin-bottom: 0;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .action-section {\r\n    position: fixed;\r\n    bottom: 40rpx;\r\n    left: 50%;\r\n    transform: translateX(-50%);\r\n    z-index: 3;\r\n\r\n    .create-btn {\r\n      width: 400rpx;\r\n      height: 88rpx;\r\n      background: #3478f6;\r\n      border-radius: 44rpx;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n\r\n      .create-text {\r\n        font-size: 32rpx;\r\n        color: #ffffff;\r\n        font-weight: 600;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>", "import MiniProgramPage from 'E:/youngProject/agent-mini-ui/pages/rule/cz-index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useUserStore", "uni", "ref", "platformRulesApi"], "mappings": ";;;;AAgCA,MAAM,eAAe;AAGrB,MAAM,UAAU;;;;AAJhB,UAAM,YAAYA,YAAAA,aAAc;AAShC,UAAM,iBAAiB,MAAM;AAE3BC,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACT,CAAG;AAAA,IACH;AACA,QAAI,cAAcC,cAAG,IAAC,EAAE;AACxB,UAAM,gBAAgB,YAAY;AAChC,UAAI,MAAM,MAAMC,2BAAiB;AAAA,QAC/B,cAAc,UAAU;AAAA,MAC5B,CAAG;AACD,kBAAY,QAAQ,IAAI,KAAK,cAAc;AAAA,IAC7C;AACA,kBAAe;;;;;;;;;;;;ACpDf,GAAG,WAAW,eAAe;"}