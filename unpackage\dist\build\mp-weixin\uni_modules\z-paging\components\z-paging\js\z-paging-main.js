"use strict";const e=require("../../../../../common/vendor.js"),t=require("./z-paging-static.js"),i=require("./z-paging-constant.js"),o=require("./z-paging-utils.js"),s=require("./modules/common-layout.js"),a=require("./modules/data-handle.js"),n=require("./modules/i18n.js"),l=require("./modules/nvue.js"),r=require("./modules/empty.js"),h=require("./modules/refresher.js"),d=require("./modules/load-more.js"),u=require("./modules/loading.js"),g=require("./modules/chat-record-mode.js"),c=require("./modules/scroller.js"),m=require("./modules/back-to-top.js"),p=require("./modules/virtual-list.js"),f=require("./z-paging-enum.js"),y=e.index.getSystemInfoSync(),S={name:"z-paging",components:{zPagingRefresh:()=>"../components/z-paging-refresh.js",zPagingLoadMore:()=>"../components/z-paging-load-more.js",zPagingEmptyView:()=>"../../z-paging-empty-view/z-paging-empty-view.js"},mixins:[s.commonLayoutModule,a.dataHandleModule,n.i18nModule,l.nvueModule,r.emptyModule,h.refresherModule,d.loadMoreModule,u.loadingModule,g.chatRecordModerModule,c.scrollerModule,m.backToTopModule,p.virtualListModule],data:()=>({base64Arrow:t.zStatic.base64Arrow,base64Flower:t.zStatic.base64Flower,base64BackToTop:t.zStatic.base64BackToTop,loadingType:f.Enum.LoadingType.Refresher,requestTimeStamp:0,wxsPropType:"",renderPropScrollTop:-1,checkScrolledToBottomTimeOut:null,cacheTopHeight:-1,statusBarHeight:y.statusBarHeight,insideOfPaging:-1,isLoadFailed:!1,isIos:"ios"===y.platform,disabledBounce:!1,fromCompleteEmit:!1,disabledCompleteEmit:!1,pageLaunched:!1,active:!1,wxsIsScrollTopInTopRange:!0,wxsScrollTop:0,wxsPageScrollTop:0,wxsOnPullingDown:!1}),props:{delay:{type:[Number,String],default:o.u.gc("delay",0)},minDelay:{type:[Number,String],default:o.u.gc("minDelay",0)},pagingStyle:{type:Object,default:o.u.gc("pagingStyle",{})},height:{type:String,default:o.u.gc("height","")},width:{type:String,default:o.u.gc("width","")},maxWidth:{type:String,default:o.u.gc("maxWidth","")},bgColor:{type:String,default:o.u.gc("bgColor","")},pagingContentStyle:{type:Object,default:o.u.gc("pagingContentStyle",{})},autoHeight:{type:Boolean,default:o.u.gc("autoHeight",!1)},autoHeightAddition:{type:[Number,String],default:o.u.gc("autoHeightAddition","0px")},defaultThemeStyle:{type:String,default:o.u.gc("defaultThemeStyle","black")},fixed:{type:Boolean,default:o.u.gc("fixed",!0)},safeAreaInsetBottom:{type:Boolean,default:o.u.gc("safeAreaInsetBottom",!1)},useSafeAreaPlaceholder:{type:Boolean,default:o.u.gc("useSafeAreaPlaceholder",!1)},bottomBgColor:{type:String,default:o.u.gc("bottomBgColor","")},topZIndex:{type:Number,default:o.u.gc("topZIndex",99)},superContentZIndex:{type:Number,default:o.u.gc("superContentZIndex",1)},contentZIndex:{type:Number,default:o.u.gc("contentZIndex",1)},f2ZIndex:{type:Number,default:o.u.gc("f2ZIndex",100)},autoFullHeight:{type:Boolean,default:o.u.gc("autoFullHeight",!0)},watchTouchDirectionChange:{type:Boolean,default:o.u.gc("watchTouchDirectionChange",!1)},unit:{type:String,default:o.u.gc("unit","rpx")}},created(){this.createdReload&&!this.refresherOnly&&this.auto&&(this._startLoading(),this.$nextTick(this._preReload))},mounted(){this.active=!0,this.wxsPropType=o.u.getTime().toString(),this.renderJsIgnore,this.createdReload||this.refresherOnly||!this.auto||o.u.delay((()=>this.$nextTick(this._preReload)),0),this.finalUseCache&&this._setListByLocalCache();let t=0;t=i.c.delayTime,this.$nextTick((()=>{this.systemInfo=e.index.getSystemInfoSync(),!this.usePageScroll&&this.autoHeight&&this._setAutoHeight(),this.loaded=!0,o.u.delay((()=>{this.updateFixedLayout(),this._updateCachedSuperContentHeight()}))})),this.updatePageScrollTopHeight(),this.updatePageScrollBottomHeight(),this.updateLeftAndRightWidth(),this.finalRefresherEnabled&&this.useCustomRefresher&&this.$nextTick((()=>{this.isTouchmoving=!0})),this._onEmit(),this.finalUseVirtualList&&this._virtualListInit(),this.$nextTick((()=>{setTimeout((()=>{this._getCssSafeAreaInsetBottom((()=>this.safeAreaInsetBottom&&this.updatePageScrollBottomHeight()))}),t)}))},destroyed(){this._handleUnmounted()},unmounted(){this._handleUnmounted()},watch:{defaultThemeStyle:{handler(e){e.length&&(this.finalRefresherDefaultStyle=e)},immediate:!0},autoHeight(e){this.loaded&&!this.usePageScroll&&this._setAutoHeight(e)},autoHeightAddition(e){this.loaded&&!this.usePageScroll&&this.autoHeight&&this._setAutoHeight(e)}},computed:{finalPagingStyle(){const e={...this.pagingStyle};if(!this.systemInfo)return e;const{windowTop:t,windowBottom:i}=this;return!this.usePageScroll&&this.fixed&&(t&&!e.top&&(e.top=t+"px"),i&&!e.bottom&&(e.bottom=i+"px")),this.bgColor.length&&!e.background&&(e.background=this.bgColor),this.height.length&&!e.height&&(e.height=this.height),this.width.length&&!e.width&&(e.width=this.width),this.maxWidth.length&&!e["max-width"]&&(e["max-width"]=this.maxWidth,e.margin="0 auto"),e},finalPagingContentStyle(){return 1!=this.contentZIndex&&(this.pagingContentStyle["z-index"]=this.contentZIndex,this.pagingContentStyle.position="relative"),this.pagingContentStyle},renderJsIgnore(){return(this.usePageScroll&&this.useChatRecordMode||!this.refresherEnabled&&this.scrollable||!this.useCustomRefresher)&&this.$nextTick((()=>{this.renderPropScrollTop=10})),0},windowHeight(){return this.systemInfo&&this.systemInfo.windowHeight||0},windowBottom(){if(!this.systemInfo)return 0;let e=this.systemInfo.windowBottom||0;return!this.safeAreaInsetBottom||this.useSafeAreaPlaceholder||this.useChatRecordMode||(e+=this.safeAreaBottom),e},isIosAndH5:()=>!1},methods:{getVersion:()=>`z-paging v${i.c.version}`,setSpecialEffects(e){this.setListSpecialEffects(e)},setListSpecialEffects(e){this.nFixFreezing=e&&Object.keys(e).length,this.isIos&&(this.privateRefresherEnabled=0),!this.usePageScroll&&this.$refs["zp-n-list"].setSpecialEffects(e)},_doVibrateShort(){e.index.vibrateShort()},async _setAutoHeight(e=!0,t=null){let i="min-height";i="min-height";try{if(e){let e=t||await this._getNodeClientRect(".zp-scroll-view"),s=await this._getNodeClientRect(".zp-page-bottom");if(e){const t=e[0].top;let a=this.windowHeight-t;a-=s?s[0].height:0;const n=a+o.u.convertToPx(this.autoHeightAddition)-(this.insideMore?1:0)+"px !important";this.$set(this.scrollViewStyle,i,n),this.$set(this.scrollViewInStyle,i,n)}}else this.$delete(this.scrollViewStyle,i),this.$delete(this.scrollViewInStyle,i)}catch(s){}},_handleUnmounted(){this.active=!1,this._offEmit(),this.useChatRecordMode&&e.index.offKeyboardHeightChange(this._handleKeyboardHeightChange)},_updateInsideOfPaging(){this.insideMore&&!0===this.insideOfPaging&&setTimeout(this.doLoadMore,200)},_cleanTimeout:e=>(e&&(clearTimeout(e),e=null),e),_onEmit(){e.index.$on(i.c.errorUpdateKey,(e=>{this.loading&&(e&&(this.customerEmptyViewErrorText=e),this.complete(!1).catch((()=>{})))})),e.index.$on(i.c.completeUpdateKey,(e=>{setTimeout((()=>{if(this.loading)if(this.disabledCompleteEmit)this.disabledCompleteEmit=!1;else{const t=e.type||"normal",i=e.list||e,o=e.rule;switch(this.fromCompleteEmit=!0,t){case"normal":this.complete(i);break;case"total":this.completeByTotal(i,o);break;case"nomore":this.completeByNoMore(i,o);break;case"key":this.completeByKey(i,o)}}}),1)}))},_offEmit(){e.index.$off(i.c.errorUpdateKey),e.index.$off(i.c.completeUpdateKey)}}};exports._sfc_main=S;
