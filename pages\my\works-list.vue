<template>
	<view class="my-works-container">
		<!-- 作品内容区域 -->
		<z-paging
			ref="paging"
			v-model="worksList"
			@query="queryList"
			:refresher-enabled="true"
			:auto="true"
		>
			<view class="works-content">
				<!-- 数字人网格 -->
				<view class="avatar-grid">
					<view
						v-for="work in worksList"
						:key="work.orderNo"
						class="avatar-card"
						:class="{ selected: isEditMode && selectedWorks.includes(work.orderNo) }"
						@tap="handleWorkTap(work)"
					>
						<image class="avatar-image" :src="work.previewUrl" mode="aspectFill" />
						<view v-if="isEditMode && selectedWorks.includes(work.orderNo)" class="check-mark">
							<image class="check-icon" src="/static/my/template_select.png" />
						</view>
					</view>
				</view>
			</view>
		</z-paging>

		<!-- 底部操作按钮 -->
		<view class="bottom-actions">
			<!-- 编辑模式 -->
			<template v-if="!isEditMode">
				<view class="action-button edit" @tap="enterEditMode">
					<image class="edit-icon" src="/static/my/<EMAIL>" mode="aspectFit" />
					<text class="action-text">编辑</text>
				</view>
			</template>
			<!-- 编辑状态下的按钮 -->
			<template v-else>
				<view class="action-button cancel" @tap="cancelEdit">
					<text class="action-text">取消</text>
				</view>
				<view class="action-button delete" @tap="deleteSelected">
					<text class="action-text">删除</text>
				</view>
			</template>
		</view>
	</view>
</template>

<script setup>
import { ref } from 'vue'
import { worksListApi, deleteWorksApi } from '@/api/index.js'
import { useUserStore } from '@/stores/user.js'

const userStore = useUserStore()

// 作品列表数据
const worksList = ref([])
const paging = ref(null)

// 编辑模式状态
const isEditMode = ref(false)
const selectedWorks = ref([])

// 分页查询作品列表
const queryList = async (page, pageSize) => {
	try {
		const res = await worksListApi({
			merchantGuid: userStore.merchantGuid,
			page: page,
			pageSize: pageSize
		})
		// 使用z-paging的complete方法处理数据
		paging.value.complete(res.data.list || [])
	} catch (error) {
		console.error('获取作品列表失败:', error)
		uni.showToast({
			title: '加载失败',
			icon: 'none'
		})
		paging.value.complete(false)
	}
}

// 进入编辑模式
const enterEditMode = () => {
	isEditMode.value = true
	selectedWorks.value = []
}

// 取消编辑模式
const cancelEdit = () => {
	isEditMode.value = false
	selectedWorks.value = []
}

// 处理作品点击
const handleWorkTap = (work) => {
	if (!isEditMode.value) {
		// 非编辑模式下，跳转到作品详情或播放页面
		if (work.workStatus === 'fail'){
			uni.showToast({
				title: '作品生成失败，无法查看',
				icon: 'none'
			})
			return
		}
		if (work.workStatus === 'doing'){
			uni.showToast({
				title: '作品加速处理中，无法查看',
				icon: 'none'
			})
			return
		}
		uni.navigateTo({
			url: `/pages/my/video-complete?orderNo=${work.orderNo}`
		})
		return
	}

	// 编辑模式下，切换选择状态
	const workId = work.orderNo
	const selectedIndex = selectedWorks.value.indexOf(workId)
	if (selectedIndex > -1) {
		selectedWorks.value.splice(selectedIndex, 1)
	} else {
		selectedWorks.value.push(workId)
	}
}

// 删除选中的作品
const deleteSelected = () => {
	if (selectedWorks.value.length === 0) {
		uni.showToast({
			title: '请先选择要删除的作品',
			icon: 'none'
		})
		return
	}

	uni.showModal({
		title: '确认删除',
		content: `确定要删除选中的${selectedWorks.value.length}个作品吗？`,
		success: async (res) => {
			if (res.confirm) {
				try {
					// 调用删除接口
					const deleteRes = await deleteWorksApi({
						merchantGuid: userStore.merchantGuid,
						orderNos: selectedWorks.value
					})

					// 删除成功后刷新列表
					selectedWorks.value = []
					isEditMode.value = false
					paging.value.reload()

					uni.showToast({
						title: deleteRes.data.message,
						icon: 'none'
					})
				} catch (error) {
					console.error('删除作品失败:', error)
					uni.showToast({
						title: '删除失败',
						icon: 'none'
					})
				}
			}
		}
	})
}
</script>

<style lang="scss" scoped>
.my-works-container {
	min-height: 100vh;
	background: #ffffff;
	padding-bottom: 160rpx;
	display: flex;
	flex-direction: column;
}

// 作品内容区域
.works-content {
	padding: 32rpx 30rpx;
}

// 头像网格
.avatar-grid {
	display: flex;
	flex-wrap: wrap;
	margin-bottom: 60rpx;
}

.avatar-card {
	position: relative;
	overflow: hidden;
	width: 220rpx;
	height: 350rpx;
	border-radius: 15rpx;
	margin-bottom: 15rpx;
	border: 3rpx solid #ffffff00;
	box-sizing: border-box;

	.avatar-image {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}

	.check-mark {
		position: absolute;
		top: 16rpx;
		right: 16rpx;
		width: 48rpx;
		height: 48rpx;
		background: #5380F2;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;

		.check-icon {
			width: 40rpx;
			height: 40rpx;
		}
	}

	&.selected {
		border: 3rpx solid #5380F2;
	}
}
.avatar-card:not(:nth-child(3n)) {
	margin-right: 15rpx;
}

.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	display: flex;
	background: #ffffff;
	padding: 32rpx;
	border-top: 1rpx solid #f0f0f0;
	gap: 24rpx;

	.action-button {
		flex: 1;
		height: 96rpx;
		border-radius: 48rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 8rpx;

		&.edit {
			.edit-icon {
				width: 32rpx;
				height: 32rpx;
			}

			.action-text {
				color: #333333;
			}
		}

		&.cancel {
			background: #f8f8f8;

			.action-text {
				color: #666666;
			}
		}

		&.delete {
			background: #ffffff;
			border: 2rpx solid #FF4757;

			.action-text {
				color: #FF4757;
			}
		}

		.action-text {
			font-size: 32rpx;
			font-weight: 500;
		}
	}
}
</style>
