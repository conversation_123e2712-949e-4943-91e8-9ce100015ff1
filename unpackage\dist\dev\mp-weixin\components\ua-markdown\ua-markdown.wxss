pre code.hljs{display:block;overflow-x:auto;padding:1em}code.hljs{padding:3px 5px}.hljs{color:#abb2bf;background:#282c34}.hljs-comment,.hljs-quote{color:#5c6370;font-style:italic}.hljs-doctag,.hljs-formula,.hljs-keyword{color:#c678dd}.hljs-deletion,.hljs-name,.hljs-section,.hljs-selector-tag,.hljs-subst{color:#e06c75}.hljs-literal{color:#56b6c2}.hljs-addition,.hljs-attribute,.hljs-meta .hljs-string,.hljs-regexp,.hljs-string{color:#98c379}.hljs-attr,.hljs-number,.hljs-selector-attr,.hljs-selector-class,.hljs-selector-pseudo,.hljs-template-variable,.hljs-type,.hljs-variable{color:#d19a66}.hljs-bullet,.hljs-link,.hljs-meta,.hljs-selector-id,.hljs-symbol,.hljs-title{color:#61aeee}.hljs-built_in,.hljs-class .hljs-title,.hljs-title.class_{color:#e6c07b}.hljs-emphasis{font-style:italic}.hljs-strong{font-weight:700}.hljs-link{text-decoration:underline}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.ua__markdown.data-v-64f4d077 {
  font-size: 16px;
  line-height: 1.5;
  word-break: break-all;
}
.ua__markdown h1.data-v-64f4d077,
.ua__markdown h2.data-v-64f4d077,
.ua__markdown h3.data-v-64f4d077,
.ua__markdown h4.data-v-64f4d077,
.ua__markdown h5.data-v-64f4d077,
.ua__markdown h6.data-v-64f4d077 {
  font-family: inherit;
  font-weight: 500;
  line-height: 1.1;
  color: inherit;
}
.ua__markdown h1.data-v-64f4d077,
.ua__markdown h2.data-v-64f4d077,
.ua__markdown h3.data-v-64f4d077 {
  margin-top: 20px;
  margin-bottom: 10px;
}
.ua__markdown h4.data-v-64f4d077,
.ua__markdown h5.data-v-64f4d077,
.ua__markdown h6.data-v-64f4d077 {
  margin-top: 10px;
  margin-bottom: 10px;
}
.ua__markdown .h1.data-v-64f4d077,
.ua__markdown h1.data-v-64f4d077 {
  font-size: 36px;
}
.ua__markdown .h2.data-v-64f4d077,
.ua__markdown h2.data-v-64f4d077 {
  font-size: 30px;
}
.ua__markdown .h3.data-v-64f4d077,
.ua__markdown h3.data-v-64f4d077 {
  font-size: 24px;
}
.ua__markdown .h4.data-v-64f4d077,
.ua__markdown h4.data-v-64f4d077 {
  font-size: 18px;
}
.ua__markdown .h5.data-v-64f4d077,
.ua__markdown h5.data-v-64f4d077 {
  font-size: 14px;
}
.ua__markdown .h6.data-v-64f4d077,
.ua__markdown h6.data-v-64f4d077 {
  font-size: 12px;
}
.ua__markdown a.data-v-64f4d077 {
  background-color: transparent;
  color: #2196f3;
  text-decoration: none;
}
.ua__markdown hr.data-v-64f4d077,
.ua__markdown.data-v-64f4d077 .hr {
  margin-top: 20px;
  margin-bottom: 20px;
  border: 0;
  border-top: 1px solid #e5e5e5;
}
.ua__markdown img.data-v-64f4d077 {
  max-width: 35%;
}
.ua__markdown p.data-v-64f4d077 {
  margin: 0 0 10px;
}
.ua__markdown em.data-v-64f4d077 {
  font-style: italic;
  font-weight: inherit;
}
.ua__markdown ol.data-v-64f4d077,
.ua__markdown ul.data-v-64f4d077 {
  margin-top: 0;
  margin-bottom: 10px;
  padding-left: 40px;
}
.ua__markdown ol ol.data-v-64f4d077,
.ua__markdown ol ul.data-v-64f4d077,
.ua__markdown ul ol.data-v-64f4d077,
.ua__markdown ul ul.data-v-64f4d077 {
  margin-bottom: 0;
}
.ua__markdown ol ol.data-v-64f4d077,
.ua__markdown ul ol.data-v-64f4d077 {
  list-style-type: lower-roman;
}
.ua__markdown ol ol ol.data-v-64f4d077,
.ua__markdown ul ul ol.data-v-64f4d077 {
  list-style-type: lower-alpha;
}
.ua__markdown dl.data-v-64f4d077 {
  margin-top: 0;
  margin-bottom: 20px;
}
.ua__markdown dt.data-v-64f4d077 {
  font-weight: 600;
}
.ua__markdown dt.data-v-64f4d077,
.ua__markdown dd.data-v-64f4d077 {
  line-height: 1.4;
}
.ua__markdown .task-list-item.data-v-64f4d077 {
  list-style-type: none;
}
.ua__markdown .task-list-item input.data-v-64f4d077 {
  margin: 0 0.2em 0.25em -1.6em;
  vertical-align: middle;
}
.ua__markdown pre.data-v-64f4d077 {
  position: relative;
  z-index: 11;
}
.ua__markdown code.data-v-64f4d077,
.ua__markdown kbd.data-v-64f4d077,
.ua__markdown pre.data-v-64f4d077,
.ua__markdown samp.data-v-64f4d077 {
  font-family: Menlo, Monaco, Consolas, "Courier New", monospace;
}
.ua__markdown code.data-v-64f4d077:not(.hljs) {
  padding: 2px 4px;
  font-size: 90%;
  color: #c7254e;
  background-color: #ffe7ee;
  border-radius: 4px;
}
.ua__markdown code.data-v-64f4d077:empty {
  display: none;
}
.ua__markdown pre code.hljs.data-v-64f4d077 {
  color: var(--vg__text-1);
  border-radius: 16px;
  background: var(--vg__bg-1);
  font-size: 12px;
}
.ua__markdown .markdown-wrap.data-v-64f4d077 {
  font-size: 12px;
  margin-bottom: 10px;
}
.ua__markdown pre.code-block-wrapper.data-v-64f4d077 {
  background: #2b2b2b;
  color: #f8f8f2;
  border-radius: 4px;
  overflow-x: auto;
  padding: 1em;
  position: relative;
}
.ua__markdown pre.code-block-wrapper code.data-v-64f4d077 {
  padding: auto;
  font-size: inherit;
  color: inherit;
  background-color: inherit;
  border-radius: 0;
}
.ua__markdown .code-block-header__copy.data-v-64f4d077 {
  font-size: 16px;
  margin-left: 5px;
}
.ua__markdown abbr[data-original-title].data-v-64f4d077,
.ua__markdown abbr[title].data-v-64f4d077 {
  cursor: help;
  border-bottom: 1px dotted #777;
}
.ua__markdown blockquote.data-v-64f4d077 {
  padding: 10px 20px;
  margin: 0 0 20px;
  font-size: 17.5px;
  border-left: 5px solid #e5e5e5;
}
.ua__markdown blockquote ol.data-v-64f4d077:last-child,
.ua__markdown blockquote p.data-v-64f4d077:last-child,
.ua__markdown blockquote ul.data-v-64f4d077:last-child {
  margin-bottom: 0;
}
.ua__markdown blockquote .small.data-v-64f4d077,
.ua__markdown blockquote footer.data-v-64f4d077,
.ua__markdown blockquote small.data-v-64f4d077 {
  display: block;
  font-size: 80%;
  line-height: 1.42857143;
  color: #777;
}
.ua__markdown blockquote .small.data-v-64f4d077:before,
.ua__markdown blockquote footer.data-v-64f4d077:before,
.ua__markdown blockquote small.data-v-64f4d077:before {
  content: "— ";
}
.ua__markdown .blockquote-reverse.data-v-64f4d077,
.ua__markdown blockquote.pull-right.data-v-64f4d077 {
  padding-right: 15px;
  padding-left: 0;
  text-align: right;
  border-right: 5px solid #eee;
  border-left: 0;
}
.ua__markdown .blockquote-reverse .small.data-v-64f4d077:before,
.ua__markdown .blockquote-reverse footer.data-v-64f4d077:before,
.ua__markdown .blockquote-reverse small.data-v-64f4d077:before,
.ua__markdown blockquote.pull-right .small.data-v-64f4d077:before,
.ua__markdown blockquote.pull-right footer.data-v-64f4d077:before,
.ua__markdown blockquote.pull-right small.data-v-64f4d077:before {
  content: "";
}
.ua__markdown .blockquote-reverse .small.data-v-64f4d077:after,
.ua__markdown .blockquote-reverse footer.data-v-64f4d077:after,
.ua__markdown .blockquote-reverse small.data-v-64f4d077:after,
.ua__markdown blockquote.pull-right .small.data-v-64f4d077:after,
.ua__markdown blockquote.pull-right footer.data-v-64f4d077:after,
.ua__markdown blockquote.pull-right small.data-v-64f4d077:after {
  content: " —";
}
.ua__markdown .footnotes.data-v-64f4d077 {
  column-count: 2;
}
.ua__markdown .footnotes-list.data-v-64f4d077 {
  padding-left: 2em;
}
.ua__markdown table.data-v-64f4d077,
.ua__markdown.data-v-64f4d077 .table {
  border-spacing: 0;
  border-collapse: collapse;
  width: 100%;
  max-width: 65em;
  overflow: auto;
  margin-top: 0;
  margin-bottom: 16px;
}
.ua__markdown table tr.data-v-64f4d077,
.ua__markdown.data-v-64f4d077 .table .tr {
  border-top: 1px solid #e5e5e5;
}
.ua__markdown table th.data-v-64f4d077,
.ua__markdown table td.data-v-64f4d077,
.ua__markdown.data-v-64f4d077 .table .th,
.ua__markdown.data-v-64f4d077 .table .td {
  padding: 6px 13px;
  border: 1px solid #e5e5e5;
}
.ua__markdown table th.data-v-64f4d077,
.ua__markdown.data-v-64f4d077 .table .th {
  font-weight: 600;
  background-color: #eee;
}
.ua__markdown .hljs[class*=language-].data-v-64f4d077:before {
  position: absolute;
  z-index: 3;
  top: 0.8em;
  right: 1em;
  font-size: 0.8em;
  color: #999;
}
.ua__markdown .hljs[class~=language-js].data-v-64f4d077:before {
  content: "js";
}
.ua__markdown .hljs[class~=language-ts].data-v-64f4d077:before {
  content: "ts";
}
.ua__markdown .hljs[class~=language-html].data-v-64f4d077:before {
  content: "html";
}
.ua__markdown .hljs[class~=language-md].data-v-64f4d077:before {
  content: "md";
}
.ua__markdown .hljs[class~=language-vue].data-v-64f4d077:before {
  content: "vue";
}
.ua__markdown .hljs[class~=language-css].data-v-64f4d077:before {
  content: "css";
}
.ua__markdown .hljs[class~=language-sass].data-v-64f4d077:before {
  content: "sass";
}
.ua__markdown .hljs[class~=language-scss].data-v-64f4d077:before {
  content: "scss";
}
.ua__markdown .hljs[class~=language-less].data-v-64f4d077:before {
  content: "less";
}
.ua__markdown .hljs[class~=language-stylus].data-v-64f4d077:before {
  content: "stylus";
}
.ua__markdown .hljs[class~=language-go].data-v-64f4d077:before {
  content: "go";
}
.ua__markdown .hljs[class~=language-java].data-v-64f4d077:before {
  content: "java";
}
.ua__markdown .hljs[class~=language-c].data-v-64f4d077:before {
  content: "c";
}
.ua__markdown .hljs[class~=language-sh].data-v-64f4d077:before {
  content: "sh";
}
.ua__markdown .hljs[class~=language-yaml].data-v-64f4d077:before {
  content: "yaml";
}
.ua__markdown .hljs[class~=language-py].data-v-64f4d077:before {
  content: "py";
}
.ua__markdown .hljs[class~=language-docker].data-v-64f4d077:before {
  content: "docker";
}
.ua__markdown .hljs[class~=language-dockerfile].data-v-64f4d077:before {
  content: "dockerfile";
}
.ua__markdown .hljs[class~=language-makefile].data-v-64f4d077:before {
  content: "makefile";
}
.ua__markdown .hljs[class~=language-javascript].data-v-64f4d077:before {
  content: "js";
}
.ua__markdown .hljs[class~=language-typescript].data-v-64f4d077:before {
  content: "ts";
}
.ua__markdown .hljs[class~=language-markup].data-v-64f4d077:before {
  content: "html";
}
.ua__markdown .hljs[class~=language-markdown].data-v-64f4d077:before {
  content: "md";
}
.ua__markdown .hljs[class~=language-json].data-v-64f4d077:before {
  content: "json";
}
.ua__markdown .hljs[class~=language-ruby].data-v-64f4d077:before {
  content: "rb";
}
.ua__markdown .hljs[class~=language-python].data-v-64f4d077:before {
  content: "py";
}
.ua__markdown .hljs[class~=language-bash].data-v-64f4d077:before {
  content: "sh";
}
.ua__markdown .hljs[class~=language-php].data-v-64f4d077:before {
  content: "php";
}