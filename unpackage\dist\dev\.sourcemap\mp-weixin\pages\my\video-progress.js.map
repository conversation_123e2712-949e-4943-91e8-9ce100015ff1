{"version": 3, "file": "video-progress.js", "sources": ["pages/my/video-progress.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvbXkvdmlkZW8tcHJvZ3Jlc3MudnVl"], "sourcesContent": ["<template>\r\n\t<view class=\"video-progress-page\">\r\n\t\t<!-- 数字人形象 -->\r\n\t\t<view class=\"digital-person-container\">\r\n\t\t\t<image class=\"person-image\" :src=\"currentAvatar\" mode=\"aspectFill\" />\r\n\t\t</view>\r\n\r\n\t\t<!-- 相机图标 -->\r\n\t\t<view class=\"camera-container\">\r\n\t\t\t<image class=\"camera-icon\" src=\"/static/my/template_camera.png\" mode=\"aspectFit\" />\r\n\t\t</view>\r\n\r\n\t\t<!-- 进度文本 -->\r\n\t\t<view class=\"progress-text-container\">\r\n\t\t\t<text class=\"progress-text\">视频正在制作中<text class=\"progress-percent\">{{ Math.round(progressPercent) }}%</text></text>\r\n\t\t</view>\r\n\r\n\t\t<!-- 进度条 -->\r\n\t\t<view class=\"progress-bar-container\">\r\n\t\t\t<view class=\"progress-bar\">\r\n\t\t\t\t<view class=\"progress-fill\" :style=\"{ width: progressPercent + '%' }\"></view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 底部提示文本 -->\r\n\t\t<view class=\"tip-text-container\">\r\n\t\t\t<text class=\"tip-text\">您的作品正在精心打磨中</text>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, onMounted, onUnmounted } from 'vue'\r\nimport { onLoad } from '@dcloudio/uni-app'\r\nimport { getVideoDetailApi } from '@/api/index.js'\r\nimport { useUserStore } from '@/stores/user.js'\r\n\r\n// 用户store\r\nconst userStore = useUserStore()\r\n\r\n// 当前显示的头像（从上一页传递过来）\r\nconst currentAvatar = ref('')\r\n\r\n// 进度相关状态\r\nconst progressPercent = ref(0)\r\nconst videoStatus = ref(0)\r\nconst orderNo = ref('')\r\n\r\n// 定时器\r\nlet progressTimer = null\r\n\r\n// 获取视频详情\r\nconst getVideoDetail = async () => {\r\n\ttry {\r\n\t\tconst res = await getVideoDetailApi({\r\n\t\t\tmerchantGuid: userStore.merchantGuid,\r\n\t\t\torderNo: orderNo.value\r\n\t\t})\r\n\r\n\t\tif (res.code === 0) {\r\n\t\t\tconst { progress, status } = res.data\r\n\t\t\tprogressPercent.value = progress || 0\r\n\t\t\tvideoStatus.value = status || 0\r\n\r\n\t\t\t// 当状态等于30或者进度大于等于100时表示创建成功\r\n\t\t\tif (status === 30 || progress >= 100) {\r\n\t\t\t\tclearInterval(progressTimer)\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '视频生成完成！',\r\n\t\t\t\t\t\ticon: 'success',\r\n\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t})\r\n\t\t\t\t\t// 跳转到视频完成页面，传递orderNo参数\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tuni.redirectTo({\r\n\t\t\t\t\t\t\turl: `/pages/my/video-complete?orderNo=${orderNo.value}`\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}, 2000)\r\n\t\t\t\t}, 500)\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tconsole.error('获取视频详情失败:', res.msg)\r\n\t\t}\r\n\t} catch (error) {\r\n\t\tconsole.error('获取视频详情失败:', error)\r\n\t}\r\n}\r\n\r\n// 使用onLoad获取页面参数\r\nonLoad((options) => {\r\n\tif (options.orderNo) {\r\n\t\torderNo.value = options.orderNo\r\n\t\tcurrentAvatar.value = options.previewUrl\r\n\r\n\t\t// 立即获取一次视频详情\r\n\t\tgetVideoDetail()\r\n\r\n\t\t// 启动轮询定时器，每3秒查询一次\r\n\t\tprogressTimer = setInterval(getVideoDetail, 3000)\r\n\t} else {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '参数错误',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\tsetTimeout(() => {\r\n\t\t\tuni.navigateBack()\r\n\t\t}, 2000)\r\n\t}\r\n})\r\n\r\nonMounted(() => {\r\n\t// 页面挂载时的其他初始化操作\r\n})\r\n\r\nonUnmounted(() => {\r\n\t// 清理定时器\r\n\tif (progressTimer) {\r\n\t\tclearInterval(progressTimer)\r\n\t}\r\n})\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.video-progress-page {\r\n\tbackground: #F5F5F5;\r\n\tmin-height: 100vh;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tpadding: 0 32rpx;\r\n\tbox-sizing: border-box;\r\n\r\n\t.digital-person-container {\r\n\t\tmargin-top: 120rpx;\r\n\t\twidth: 340rpx;\r\n\t\theight: 602rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t\toverflow: hidden;\r\n\r\n\t\t.person-image {\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 100%;\r\n\t\t}\r\n\t}\r\n\r\n\t.camera-container {\r\n\t\tmargin-top: 94rpx;\r\n\t\twidth: 110rpx;\r\n\t\theight: 110rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\r\n\t\t.camera-icon {\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 100%;\r\n\t\t}\r\n\t}\r\n\r\n\t.progress-text-container {\r\n\t\tmargin-top: 15rpx;\r\n\r\n\t\t.progress-text {\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tcolor: #333333;\r\n\t\t\tfont-weight: 400;\r\n\r\n\t\t\t.progress-percent {\r\n\t\t\t\tcolor: #2A64F6;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.progress-bar-container {\r\n\t\tmargin-top: 62rpx;\r\n\t\twidth: 430rpx;\r\n\r\n\t\t.progress-bar {\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 8rpx;\r\n\t\t\tbackground: #ECECEC;\r\n\t\t\tborder-radius: 4rpx;\r\n\t\t\toverflow: hidden;\r\n\r\n\t\t\t.progress-fill {\r\n\t\t\t\theight: 100%;\r\n\t\t\t\tbackground: #5380F2;\r\n\t\t\t\tborder-radius: 4rpx;\r\n\t\t\t\ttransition: width 0.3s ease;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.tip-text-container {\r\n\t\tmargin-top: 40rpx;\r\n\r\n\t\t.tip-text {\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tcolor: #999999;\r\n\t\t}\r\n\t}\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'E:/youngProject/agent-mini-ui/pages/my/video-progress.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useUserStore", "ref", "getVideoDetailApi", "uni", "onLoad", "onMounted", "onUnmounted"], "mappings": ";;;;;;;;AAsCA,UAAM,YAAYA,YAAAA,aAAc;AAGhC,UAAM,gBAAgBC,cAAG,IAAC,EAAE;AAG5B,UAAM,kBAAkBA,cAAG,IAAC,CAAC;AAC7B,UAAM,cAAcA,cAAG,IAAC,CAAC;AACzB,UAAM,UAAUA,cAAG,IAAC,EAAE;AAGtB,QAAI,gBAAgB;AAGpB,UAAM,iBAAiB,YAAY;AAClC,UAAI;AACH,cAAM,MAAM,MAAMC,4BAAkB;AAAA,UACnC,cAAc,UAAU;AAAA,UACxB,SAAS,QAAQ;AAAA,QACpB,CAAG;AAED,YAAI,IAAI,SAAS,GAAG;AACnB,gBAAM,EAAE,UAAU,OAAQ,IAAG,IAAI;AACjC,0BAAgB,QAAQ,YAAY;AACpC,sBAAY,QAAQ,UAAU;AAG9B,cAAI,WAAW,MAAM,YAAY,KAAK;AACrC,0BAAc,aAAa;AAC3B,uBAAW,MAAM;AAChBC,4BAAAA,MAAI,UAAU;AAAA,gBACb,OAAO;AAAA,gBACP,MAAM;AAAA,gBACN,UAAU;AAAA,cAChB,CAAM;AAED,yBAAW,MAAM;AAChBA,8BAAAA,MAAI,WAAW;AAAA,kBACd,KAAK,oCAAoC,QAAQ,KAAK;AAAA,gBAC7D,CAAO;AAAA,cACD,GAAE,GAAI;AAAA,YACP,GAAE,GAAG;AAAA,UACN;AAAA,QACJ,OAAS;AACNA,wBAAA,MAAA,MAAA,SAAA,qCAAc,aAAa,IAAI,GAAG;AAAA,QAClC;AAAA,MACD,SAAQ,OAAO;AACfA,sBAAAA,0DAAc,aAAa,KAAK;AAAA,MAChC;AAAA,IACF;AAGAC,kBAAM,OAAC,CAAC,YAAY;AACnB,UAAI,QAAQ,SAAS;AACpB,gBAAQ,QAAQ,QAAQ;AACxB,sBAAc,QAAQ,QAAQ;AAG9B,uBAAgB;AAGhB,wBAAgB,YAAY,gBAAgB,GAAI;AAAA,MAClD,OAAQ;AACND,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACT,CAAG;AACD,mBAAW,MAAM;AAChBA,wBAAAA,MAAI,aAAc;AAAA,QAClB,GAAE,GAAI;AAAA,MACP;AAAA,IACF,CAAC;AAEDE,kBAAAA,UAAU,MAAM;AAAA,IAEhB,CAAC;AAEDC,kBAAAA,YAAY,MAAM;AAEjB,UAAI,eAAe;AAClB,sBAAc,aAAa;AAAA,MAC3B;AAAA,IACF,CAAC;;;;;;;;;;;;ACvHD,GAAG,WAAW,eAAe;"}