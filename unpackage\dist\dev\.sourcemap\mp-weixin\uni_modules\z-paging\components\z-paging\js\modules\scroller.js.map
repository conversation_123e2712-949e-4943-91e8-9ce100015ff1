{"version": 3, "file": "scroller.js", "sources": ["uni_modules/z-paging/components/z-paging/js/modules/scroller.js"], "sourcesContent": ["// [z-paging]scroll相关模块\r\nimport u from '.././z-paging-utils'\r\nimport Enum from '.././z-paging-enum'\r\n\r\n// #ifdef APP-NVUE\r\nconst weexDom = weex.requireModule('dom');\r\n// #endif\r\n\r\nexport default {\r\n\tprops: {\r\n\t\t// 使用页面滚动，默认为否，当设置为是时则使用页面的滚动而非此组件内部的scroll-view的滚动，使用页面滚动时z-paging无需设置确定的高度且对于长列表展示性能更高，但配置会略微繁琐\r\n\t\tusePageScroll: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('usePageScroll', false)\r\n\t\t},\r\n\t\t// 是否可以滚动，使用内置scroll-view和nvue时有效，默认为是\r\n\t\tscrollable: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('scrollable', true)\r\n\t\t},\r\n\t\t// 控制是否出现滚动条，默认为是\r\n\t\tshowScrollbar: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('showScrollbar', true)\r\n\t\t},\r\n\t\t// 是否允许横向滚动，默认为否\r\n\t\tscrollX: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('scrollX', false)\r\n\t\t},\r\n\t\t// iOS设备上滚动到顶部时是否允许回弹效果，默认为否。关闭回弹效果后可使滚动到顶部与下拉刷新更连贯，但是有吸顶view时滚动到顶部时可能出现抖动。\r\n\t\tscrollToTopBounceEnabled: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('scrollToTopBounceEnabled', false)\r\n\t\t},\r\n\t\t// iOS设备上滚动到底部时是否允许回弹效果，默认为是。\r\n\t\tscrollToBottomBounceEnabled: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('scrollToBottomBounceEnabled', true)\r\n\t\t},\r\n\t\t// 在设置滚动条位置时使用动画过渡，默认为否\r\n\t\tscrollWithAnimation: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('scrollWithAnimation', false)\r\n\t\t},\r\n\t\t// 值应为某子元素id（id不能以数字开头）。设置哪个方向可滚动，则在哪个方向滚动到该元素\r\n\t\tscrollIntoView: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: u.gc('scrollIntoView', '')\r\n\t\t},\r\n\t},\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tscrollTop: 0,\r\n\t\t\toldScrollTop: 0,\r\n\t\t\tscrollViewStyle: {},\r\n\t\t\tscrollViewContainerStyle: {},\r\n\t\t\tscrollViewInStyle: {},\r\n\t\t\tpageScrollTop: -1,\r\n\t\t\tscrollEnable: true,\r\n\t\t\tprivateScrollWithAnimation: -1,\r\n\t\t\tcacheScrollNodeHeight: -1,\r\n\t\t\tsuperContentHeight: 0,\r\n\t\t}\r\n\t},\r\n\twatch: {\r\n\t\toldScrollTop(newVal) {\r\n\t\t\t!this.usePageScroll && this._scrollTopChange(newVal,false);\r\n\t\t},\r\n\t\tpageScrollTop(newVal) {\r\n\t\t\tthis.usePageScroll && this._scrollTopChange(newVal,true);\r\n\t\t},\r\n\t\tusePageScroll: {\r\n\t\t\thandler(newVal) {\r\n\t\t\t\tthis.loaded && this.autoHeight && this._setAutoHeight(!newVal);\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\tif (newVal) {\r\n\t\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\t\tconst mainScrollRef = this.$refs['zp-scroll-view'].$refs.main;\r\n\t\t\t\t\t\tif (mainScrollRef) {\r\n\t\t\t\t\t\t\tmainScrollRef.style = {};\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\timmediate: true\r\n\t\t},\r\n\t\tfinalScrollTop(newVal) {\r\n\t\t\tthis.renderPropScrollTop = newVal < 6 ? 0 : 10;\r\n\t\t}\r\n\t},\r\n\tcomputed: {\r\n\t\tfinalScrollWithAnimation() {\r\n\t\t\tif (this.privateScrollWithAnimation !== -1) {\r\n\t\t\t\treturn this.privateScrollWithAnimation === 1;\r\n\t\t\t}\r\n\t\t\treturn this.scrollWithAnimation;\r\n\t\t},\r\n\t\tfinalScrollViewStyle() {\r\n\t\t\tif (this.superContentZIndex != 1) {\r\n\t\t\t\tthis.scrollViewStyle['z-index'] = this.superContentZIndex;\r\n\t\t\t\tthis.scrollViewStyle['position'] = 'relative';\r\n\t\t\t}\r\n\t\t\treturn this.scrollViewStyle;\r\n\t\t},\r\n\t\tfinalScrollTop() {\r\n\t\t\treturn this.usePageScroll ? this.pageScrollTop : this.oldScrollTop;\r\n\t\t},\r\n\t\t// 当前是否是旧版webview\r\n\t\tfinalIsOldWebView() {\r\n\t\t\treturn this.isOldWebView && !this.usePageScroll;\r\n\t\t},\r\n\t\t// 当前scroll-view/list-view是否允许滚动\r\n\t\tfinalScrollable() {\r\n\t\t\treturn this.scrollable && !this.usePageScroll && this.scrollEnable \r\n\t\t\t&& (this.refresherCompleteScrollable ? true : this.refresherStatus !== Enum.Refresher.Complete)\r\n\t\t\t&& (this.refresherRefreshingScrollable ? true : this.refresherStatus !== Enum.Refresher.Loading);\r\n\t\t}\r\n\t},\r\n\tmethods: {\r\n\t\t// 滚动到顶部，animate为是否展示滚动动画，默认为是\r\n\t\tscrollToTop(animate, checkReverse = true) {\r\n\t\t\t// 如果是聊天记录模式并且列表倒置了，则滚动到顶部实际上是滚动到底部\r\n\t\t\tif (this.useChatRecordMode && checkReverse && !this.isChatRecordModeAndNotInversion) {\r\n\t\t\t\tthis.scrollToBottom(animate, false);\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\tthis._scrollToTop(animate, false);\r\n\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\tif (this.nvueFastScroll && animate) {\r\n\t\t\t\t\tu.delay(() => {\r\n\t\t\t\t\t\tthis._scrollToTop(false, false);\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t})\r\n\t\t},\r\n\t\t// 滚动到底部，animate为是否展示滚动动画，默认为是\r\n\t\tscrollToBottom(animate, checkReverse = true) {\r\n\t\t\t// 如果是聊天记录模式并且列表倒置了，则滚动到底部实际上是滚动到顶部\r\n\t\t\tif (this.useChatRecordMode && checkReverse && !this.isChatRecordModeAndNotInversion) {\r\n\t\t\t\tthis.scrollToTop(animate, false);\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\tthis._scrollToBottom(animate);\r\n\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\tif (this.nvueFastScroll && animate) {\r\n\t\t\t\t\tu.delay(() => {\r\n\t\t\t\t\t\tthis._scrollToBottom(false);\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t})\r\n\t\t},\r\n\t\t// 滚动到指定view(vue中有效)。sel为需要滚动的view的id值，不包含\"#\"；offset为偏移量，单位为px；animate为是否展示滚动动画，默认为否\r\n\t\tscrollIntoViewById(sel, offset, animate) {\r\n\t\t\tthis._scrollIntoView(sel, offset, animate);\r\n\t\t},\r\n\t\t// 滚动到指定view(vue中有效)。nodeTop为需要滚动的view的top值(通过uni.createSelectorQuery()获取)；offset为偏移量，单位为px；animate为是否展示滚动动画，默认为否\r\n\t\tscrollIntoViewByNodeTop(nodeTop, offset, animate) {\r\n\t\t\tthis.scrollTop = this.oldScrollTop;\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\tthis._scrollIntoViewByNodeTop(nodeTop, offset, animate);\r\n\t\t\t})\r\n\t\t},\r\n\t\t// 滚动到指定位置(vue中有效)。y为与顶部的距离，单位为px；offset为偏移量，单位为px；animate为是否展示滚动动画，默认为否\r\n\t\tscrollToY(y, offset, animate) {\r\n\t\t\tthis.scrollTop = this.oldScrollTop;\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\tthis._scrollToY(y, offset, animate);\r\n\t\t\t})\r\n\t\t},\r\n\t\t// 滚动到指定view(nvue中和虚拟列表中有效)。index为需要滚动的view的index(第几个，从0开始)；offset为偏移量，单位为px；animate为是否展示滚动动画，默认为否\r\n\t\tscrollIntoViewByIndex(index, offset, animate) {\r\n\t\t\tif (index >= this.realTotalData.length) {\r\n\t\t\t\tu.consoleErr('当前滚动的index超出已渲染列表长度，请先通过refreshToPage加载到对应index页并等待渲染成功后再调用此方法！')\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\t// 在nvue中，根据index获取对应节点信息并滚动到此节点位置\r\n\t\t\t\tthis._scrollIntoView(index, offset, animate);\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifndef APP-NVUE\r\n\t\t\t\tif (this.finalUseVirtualList) {\r\n\t\t\t\t\tconst isCellFixed = this.cellHeightMode === Enum.CellHeightMode.Fixed;\r\n\t\t\t\t\tu.delay(() => {\r\n\t\t\t\t\t\tif (this.finalUseVirtualList) {\r\n\t\t\t\t\t\t\t// 虚拟列表 + 每个cell高度完全相同模式下，此时滚动到对应index的cell就是滚动到scrollTop = cellHeight * index的位置\r\n\t\t\t\t\t\t\t// 虚拟列表 + 高度是动态非固定的模式下，此时滚动到对应index的cell就是滚动到scrollTop = 缓存的cell高度数组中第index个的lastTotalHeight的位置\r\n\t\t\t\t\t\t\tconst scrollTop = isCellFixed ? this.virtualCellHeight * index : this.virtualHeightCacheList[index].lastTotalHeight;\r\n\t\t\t\t\t\t\tthis.scrollToY(scrollTop, offset, animate);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}, isCellFixed ? 0 : 100)\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t})\r\n\t\t},\r\n\t\t// 滚动到指定view(nvue中有效)。view为需要滚动的view(通过`this.$refs.xxx`获取)，不包含\"#\"；offset为偏移量，单位为px；animate为是否展示滚动动画，默认为否\r\n\t\tscrollIntoViewByView(view, offset, animate) {\r\n\t\t\tthis._scrollIntoView(view, offset, animate);\r\n\t\t},\r\n\t\t// 当使用页面滚动并且自定义下拉刷新时，请在页面的onPageScroll中调用此方法，告知z-paging当前的pageScrollTop，否则会导致在任意位置都可以下拉刷新\r\n\t\tupdatePageScrollTop(value) {\r\n\t\t\tthis.pageScrollTop = value;\r\n\t\t},\r\n\t\t// 当使用页面滚动并且设置了slot=\"top\"时，默认初次加载会自动获取其高度，并使内部容器下移，当slot=\"top\"的view高度动态改变时，在其高度需要更新时调用此方法\r\n\t\tupdatePageScrollTopHeight() {\r\n\t\t\tthis._updatePageScrollTopOrBottomHeight('top');\r\n\t\t},\r\n\t\t// 当使用页面滚动并且设置了slot=\"bottom\"时，默认初次加载会自动获取其高度，并使内部容器下移，当slot=\"bottom\"的view高度动态改变时，在其高度需要更新时调用此方法\r\n\t\tupdatePageScrollBottomHeight() {\r\n\t\t\tthis._updatePageScrollTopOrBottomHeight('bottom');\r\n\t\t},\r\n\t\t// 更新slot=\"left\"和slot=\"right\"宽度，当slot=\"left\"或slot=\"right\"宽度动态改变时调用\r\n\t\tupdateLeftAndRightWidth() {\r\n\t\t\tif (!this.finalIsOldWebView) return;\r\n\t\t\tthis.$nextTick(() => this._updateLeftAndRightWidth(this.scrollViewContainerStyle, 'zp-page'));\r\n\t\t},\r\n\t\t// 更新z-paging内置scroll-view的scrollTop\r\n\t\tupdateScrollViewScrollTop(scrollTop, animate = true) {\r\n\t\t\tthis._updatePrivateScrollWithAnimation(animate);\r\n\t\t\tthis.scrollTop = this.oldScrollTop;\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\tthis.scrollTop = scrollTop;\r\n\t\t\t\tthis.oldScrollTop = this.scrollTop;\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 当滚动到顶部时\r\n\t\t_onScrollToUpper() {\r\n\t\t\tthis.$emit('scrolltoupper');\r\n\t\t\tthis.$emit('scrollTopChange', 0);\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\tthis.oldScrollTop = 0;\r\n\t\t\t})\r\n\t\t},\r\n\t\t// 当滚动到底部时\r\n\t\t_onScrollToLower(e) {\r\n\t\t\t(!e.detail || !e.detail.direction || e.detail.direction === 'bottom') && this._onLoadingMore(this.useChatRecordMode ? 'click' : 'toBottom')\r\n\t\t},\r\n\t\t// 滚动到顶部\r\n\t\t_scrollToTop(animate = true, isPrivate = true) {\r\n\t\t\t// #ifdef APP-NVUE\r\n\t\t\t// 在nvue中需要通过weex.scrollToElement滚动到顶部，此时在顶部插入了一个view，使得滚动到这个view位置\r\n\t\t\tconst el = this.$refs['zp-n-list-top-tag'];\r\n\t\t\tif (this.usePageScroll) {\r\n\t\t\t\tthis._getNodeClientRect('zp-page-scroll-top', false).then(node => {\r\n\t\t\t\t\tconst nodeHeight = node ? node[0].height : 0;\r\n\t\t\t\t\tweexDom.scrollToElement(el, {\r\n\t\t\t\t\t\toffset: -nodeHeight,\r\n\t\t\t\t\t\tanimated: animate\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t} else {\r\n\t\t\t\tif (!this.isIos && this.nvueListIs === 'scroller') {\r\n\t\t\t\t\tthis._getNodeClientRect('zp-n-refresh-container', false).then(node => {\r\n\t\t\t\t\t\tconst nodeHeight = node ? node[0].height : 0;\r\n\t\t\t\t\t\tweexDom.scrollToElement(el, {\r\n\t\t\t\t\t\t\toffset: -nodeHeight,\r\n\t\t\t\t\t\t\tanimated: animate\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\tweexDom.scrollToElement(el, {\r\n\t\t\t\t\t\toffset: 0,\r\n\t\t\t\t\t\tanimated: animate\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\treturn;\r\n\t\t\t// #endif\r\n\t\t\tif (this.usePageScroll) {\r\n\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\tuni.pageScrollTo({\r\n\t\t\t\t\t\tscrollTop: 0,\r\n\t\t\t\t\t\tduration: animate ? 100 : 0,\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\tthis._updatePrivateScrollWithAnimation(animate);\r\n\t\t\tthis.scrollTop = this.oldScrollTop;\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\tthis.scrollTop = 0;\r\n\t\t\t\tthis.oldScrollTop = this.scrollTop;\r\n\t\t\t});\r\n\t\t},\r\n\t\t// 滚动到底部\r\n\t\tasync _scrollToBottom(animate = true) {\r\n\t\t\t// #ifdef APP-NVUE\r\n\t\t\t// 在nvue中需要通过weex.scrollToElement滚动到顶部，此时在底部插入了一个view，使得滚动到这个view位置\r\n\t\t\tconst el = this.$refs['zp-n-list-bottom-tag'];\r\n\t\t\tif (el) {\r\n\t\t\t\tweexDom.scrollToElement(el, {\r\n\t\t\t\t\toffset: 0,\r\n\t\t\t\t\tanimated: animate\r\n\t\t\t\t});\r\n\t\t\t} else {\r\n\t\t\t\tu.consoleErr('滚动到底部失败，因为您设置了hideNvueBottomTag为true');\r\n\t\t\t}\r\n\t\t\treturn;\r\n\t\t\t// #endif\r\n\t\t\tif (this.usePageScroll) {\r\n\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\tuni.pageScrollTo({\r\n\t\t\t\t\t\tscrollTop: Number.MAX_VALUE,\r\n\t\t\t\t\t\tduration: animate ? 100 : 0,\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\ttry {\r\n\t\t\t\tthis._updatePrivateScrollWithAnimation(animate);\r\n\t\t\t\tconst pagingContainerNode = await this._getNodeClientRect('.zp-paging-container');\r\n\t\t\t\tconst scrollViewNode = await this._getNodeClientRect('.zp-scroll-view');\r\n\t\t\t\tconst pagingContainerH = pagingContainerNode ? pagingContainerNode[0].height : 0;\r\n\t\t\t\tconst scrollViewH = scrollViewNode ? scrollViewNode[0].height : 0;\r\n\t\t\t\tif (pagingContainerH > scrollViewH) {\r\n\t\t\t\t\tthis.scrollTop = this.oldScrollTop;\r\n\t\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\t\tthis.scrollTop = pagingContainerH - scrollViewH + this.virtualPlaceholderTopHeight;\r\n\t\t\t\t\t\tthis.oldScrollTop = this.scrollTop;\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t} catch (e) {}\r\n\t\t},\r\n\t\t// 滚动到指定view\r\n\t\t_scrollIntoView(sel, offset = 0, animate = false, finishCallback) {\r\n\t\t\ttry {\r\n\t\t\t\tthis.scrollTop = this.oldScrollTop;\r\n\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\t\tconst refs = this.$parent.$refs;\r\n\t\t\t\t\tif (!refs) return;\r\n\t\t\t\t\tconst dataType = Object.prototype.toString.call(sel);\r\n\t\t\t\t\tlet el = null;\r\n\t\t\t\t\tif (dataType === '[object Number]') {\r\n\t\t\t\t\t\tconst els = refs[`z-paging-${sel}`];\r\n\t\t\t\t\t\tel = els ? els[0] : null;\r\n\t\t\t\t\t} else if (dataType === '[object Array]') {\r\n\t\t\t\t\t\tel = sel[0];\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tel = sel;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (el) {\r\n\t\t\t\t\t\tweexDom.scrollToElement(el, {\r\n\t\t\t\t\t\t\toffset: -offset,\r\n\t\t\t\t\t\t\tanimated: animate\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tu.consoleErr('在nvue中滚动到指定位置，cell必须设置 :ref=\"`z-paging-${index}`\"');\r\n\t\t\t\t\t}\r\n\t\t\t\t\treturn;\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\tthis._getNodeClientRect('#' + sel.replace('#', ''), this.$parent).then((node) => {\r\n\t\t\t\t\t\tif (node) {\r\n\t\t\t\t\t\t\tlet nodeTop = node[0].top;\r\n\t\t\t\t\t\t\tthis._scrollIntoViewByNodeTop(nodeTop, offset, animate);\r\n\t\t\t\t\t\t\tfinishCallback && finishCallback();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t} catch (e) {}\r\n\t\t},\r\n\t\t// 通过nodeTop滚动到指定view\r\n\t\t_scrollIntoViewByNodeTop(nodeTop, offset = 0, animate = false) {\r\n\t\t\t// 如果是聊天记录模式并且列表倒置了，此时nodeTop需要等于scroll-view高度 - nodeTop\r\n\t\t\tif (this.isChatRecordModeAndInversion) {\r\n\t\t\t\tthis._getNodeClientRect('.zp-scroll-view').then(sNode => {\r\n\t\t\t\t\tif (sNode) {\r\n\t\t\t\t\t\tthis._scrollToY(sNode[0].height - nodeTop, offset, animate, true);\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t} else {\r\n\t\t\t\tthis._scrollToY(nodeTop, offset, animate, true);\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 滚动到指定位置\r\n\t\t_scrollToY(y, offset = 0, animate = false, addScrollTop = false) {\r\n\t\t\tthis._updatePrivateScrollWithAnimation(animate);\r\n\t\t\tu.delay(() => {\r\n\t\t\t\tif (this.usePageScroll) {\r\n\t\t\t\t\tif (addScrollTop && this.pageScrollTop !== -1) {\r\n\t\t\t\t\t   y += this.pageScrollTop; \r\n\t\t\t\t\t}\r\n\t\t\t\t\tconst scrollTop = y - offset;\r\n\t\t\t\t\tuni.pageScrollTo({\r\n\t\t\t\t\t\tscrollTop,\r\n\t\t\t\t\t\tduration: animate ? 100 : 0\r\n\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\tif (addScrollTop) {\r\n\t\t\t\t\t   y += this.oldScrollTop; \r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.scrollTop = y - offset;\r\n\t\t\t\t}\r\n\t\t\t}, 10)\r\n\t\t},\r\n\t\t// scroll-view滚动中\r\n\t\t_scroll(e) {\r\n\t\t\tthis.$emit('scroll', e);\r\n\t\t\tconst scrollTop = e.detail.scrollTop;\r\n\t\t\t// #ifndef APP-NVUE\r\n\t\t\tthis.finalUseVirtualList && this._updateVirtualScroll(scrollTop, this.oldScrollTop - scrollTop);\r\n\t\t\t// #endif\r\n\t\t\tthis.oldScrollTop = scrollTop;\r\n\t\t\t// 滚动区域内容的总高度 - 当前滚动的scrollTop = 当前滚动区域的顶部与内容底部的距离\r\n\t\t\tconst scrollDiff = e.detail.scrollHeight - this.oldScrollTop;\r\n\t\t\t// 在非ios平台滚动中，再次验证一下是否滚动到了底部。因为在一些安卓设备中，有概率滚动到底部不触发@scrolltolower事件，因此添加双重检测逻辑\r\n\t\t\t!this.isIos && this._checkScrolledToBottom(scrollDiff);\r\n\t\t},\r\n\t\t// 更新内置的scroll-view是否启用滚动动画\r\n\t\t_updatePrivateScrollWithAnimation(animate) {\r\n\t\t\tthis.privateScrollWithAnimation = animate ? 1 : 0;\r\n\t\t\tu.delay(() => this.$nextTick(() => {\r\n\t\t\t\t// 在滚动结束后将滚动动画状态设置回初始状态\r\n\t\t\t\tthis.privateScrollWithAnimation = -1;\r\n\t\t\t}), 100, 'updateScrollWithAnimationDelay')\r\n\t\t},\r\n\t\t// 检测scrollView是否要铺满屏幕\r\n\t\t_doCheckScrollViewShouldFullHeight(totalData) {\r\n\t\t\tif (this.autoFullHeight && this.usePageScroll && this.isTotalChangeFromAddData) {\r\n\t\t\t\t// #ifndef APP-NVUE\r\n\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\tthis._checkScrollViewShouldFullHeight((scrollViewNode, pagingContainerNode) => {\r\n\t\t\t\t\t\tthis._preCheckShowNoMoreInside(totalData, scrollViewNode, pagingContainerNode)\r\n\t\t\t\t\t});\r\n\t\t\t\t})\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\tthis._preCheckShowNoMoreInside(totalData)\r\n\t\t\t\t// #endif\r\n\t\t\t} else {\r\n\t\t\t\tthis._preCheckShowNoMoreInside(totalData)\r\n\t\t\t} \r\n\t\t},\r\n\t\t// 检测z-paging是否要全屏覆盖(当使用页面滚动并且不满全屏时，默认z-paging需要铺满全屏，避免数据过少时内部的empty-view无法正确展示)\r\n\t\tasync _checkScrollViewShouldFullHeight(callback) {\r\n\t\t\ttry {\r\n\t\t\t\tconst scrollViewNode = await this._getNodeClientRect('.zp-scroll-view');\r\n\t\t\t\tconst pagingContainerNode = await this._getNodeClientRect('.zp-paging-container-content');\r\n\t\t\t\tif (!scrollViewNode || !pagingContainerNode) return;\r\n\t\t\t\tconst scrollViewHeight = pagingContainerNode[0].height;\r\n\t\t\t\tconst scrollViewTop = scrollViewNode[0].top;\r\n\t\t\t\tif (this.isAddedData && scrollViewHeight + scrollViewTop <= this.windowHeight) {\r\n\t\t\t\t\tthis._setAutoHeight(true, scrollViewNode);\r\n\t\t\t\t\tcallback(scrollViewNode, pagingContainerNode);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis._setAutoHeight(false);\r\n\t\t\t\t\tcallback(null, null);\r\n\t\t\t\t}\r\n\t\t\t} catch (e) {\r\n\t\t\t\tcallback(null, null);\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 更新缓存中z-paging整个内容容器高度\r\n\t\tasync _updateCachedSuperContentHeight() {\r\n\t\t\tconst superContentNode = await this._getNodeClientRect('.z-paging-content');\r\n\t\t\tif (superContentNode) {\r\n\t\t\t\tthis.superContentHeight = superContentNode[0].height;\r\n\t\t\t}\r\n\t\t},\r\n\t\t// scrollTop改变时触发\r\n\t\t_scrollTopChange(newVal, isPageScrollTop){\r\n\t\t\tthis.$emit('scrollTopChange', newVal);\r\n\t\t\tthis.$emit('update:scrollTop', newVal);\r\n\t\t\tthis._checkShouldShowBackToTop(newVal);\r\n\t\t\t// 之前在安卓中scroll-view有概率滚动到顶部时scrollTop不为0导致下拉刷新判断异常，因此判断scrollTop在105之内都允许下拉刷新，但此方案会导致某些情况（例如滚动到距离顶部10px处）下拉抖动，因此改为通过获取zp-scroll-view的节点信息中的scrollTop进行验证的方案\r\n\t\t\t// const scrollTop = this.isIos ? (newVal > 5 ? 6 : 0) : (newVal > 105 ? 106 : (newVal > 5 ? 6 : 0));\r\n\t\t\tconst scrollTop = newVal > 5 ? 6 : 0;\r\n\t\t\tif (isPageScrollTop && this.wxsPageScrollTop !== scrollTop) {\r\n\t\t\t\tthis.wxsPageScrollTop = scrollTop;\r\n\t\t\t} else if (!isPageScrollTop && this.wxsScrollTop !== scrollTop) {\r\n\t\t\t\tthis.wxsScrollTop = scrollTop;\r\n\t\t\t\tif (scrollTop > 6) {\r\n\t\t\t\t\tthis.scrollEnable = true;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 更新使用页面滚动时slot=\"top\"或\"bottom\"插入view的高度\r\n\t\t_updatePageScrollTopOrBottomHeight(type) {\r\n\t\t\t// #ifndef APP-NVUE\r\n\t\t\tif (!this.usePageScroll) return;\r\n\t\t\t// #endif\r\n\t\t\tthis._doCheckScrollViewShouldFullHeight(this.realTotalData);\r\n\t\t\tconst node = `.zp-page-${type}`;\r\n\t\t\tconst marginText = `margin${type.slice(0,1).toUpperCase() + type.slice(1)}`;\r\n\t\t\tlet safeAreaInsetBottomAdd = this.safeAreaInsetBottom;\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\tlet delayTime = 0;\r\n\t\t\t\t// #ifdef MP-BAIDU || APP-NVUE\r\n\t\t\t\tdelayTime = 50;\r\n\t\t\t\t// #endif\r\n\t\t\t\tu.delay(() => {\r\n\t\t\t\t\tthis._getNodeClientRect(node).then((res) => {\r\n\t\t\t\t\t\tif (res) {\r\n\t\t\t\t\t\t\tlet pageScrollNodeHeight = res[0].height;\r\n\t\t\t\t\t\t\tif (type === 'bottom') {\r\n\t\t\t\t\t\t\t\tif (safeAreaInsetBottomAdd) {\r\n\t\t\t\t\t\t\t\t\tpageScrollNodeHeight += this.safeAreaBottom;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tthis.cacheTopHeight = pageScrollNodeHeight;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tthis.$set(this.scrollViewStyle, marginText, `${pageScrollNodeHeight}px`);\r\n\t\t\t\t\t\t} else if (safeAreaInsetBottomAdd) {\r\n\t\t\t\t\t\t\tthis.$set(this.scrollViewStyle, marginText, `${this.safeAreaBottom}px`);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t}, delayTime)\r\n\t\t\t})\r\n\t\t},\r\n\t}\r\n}\r\n"], "names": ["u", "Enum", "uni"], "mappings": ";;;;AAQA,MAAe,iBAAA;AAAA,EACd,OAAO;AAAA;AAAA,IAEN,eAAe;AAAA,MACd,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,iBAAiB,KAAK;AAAA,IACpC;AAAA;AAAA,IAED,YAAY;AAAA,MACX,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,cAAc,IAAI;AAAA,IAChC;AAAA;AAAA,IAED,eAAe;AAAA,MACd,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,iBAAiB,IAAI;AAAA,IACnC;AAAA;AAAA,IAED,SAAS;AAAA,MACR,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,WAAW,KAAK;AAAA,IAC9B;AAAA;AAAA,IAED,0BAA0B;AAAA,MACzB,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,4BAA4B,KAAK;AAAA,IAC/C;AAAA;AAAA,IAED,6BAA6B;AAAA,MAC5B,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,+BAA+B,IAAI;AAAA,IACjD;AAAA;AAAA,IAED,qBAAqB;AAAA,MACpB,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,uBAAuB,KAAK;AAAA,IAC1C;AAAA;AAAA,IAED,gBAAgB;AAAA,MACf,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,kBAAkB,EAAE;AAAA,IAClC;AAAA,EACD;AAAA,EACD,OAAO;AACN,WAAO;AAAA,MACN,WAAW;AAAA,MACX,cAAc;AAAA,MACd,iBAAiB,CAAE;AAAA,MACnB,0BAA0B,CAAE;AAAA,MAC5B,mBAAmB,CAAE;AAAA,MACrB,eAAe;AAAA,MACf,cAAc;AAAA,MACd,4BAA4B;AAAA,MAC5B,uBAAuB;AAAA,MACvB,oBAAoB;AAAA,IACpB;AAAA,EACD;AAAA,EACD,OAAO;AAAA,IACN,aAAa,QAAQ;AACpB,OAAC,KAAK,iBAAiB,KAAK,iBAAiB,QAAO,KAAK;AAAA,IACzD;AAAA,IACD,cAAc,QAAQ;AACrB,WAAK,iBAAiB,KAAK,iBAAiB,QAAO,IAAI;AAAA,IACvD;AAAA,IACD,eAAe;AAAA,MACd,QAAQ,QAAQ;AACf,aAAK,UAAU,KAAK,cAAc,KAAK,eAAe,CAAC,MAAM;AAAA,MAW7D;AAAA,MACD,WAAW;AAAA,IACX;AAAA,IACD,eAAe,QAAQ;AACtB,WAAK,sBAAsB,SAAS,IAAI,IAAI;AAAA,IAC5C;AAAA,EACD;AAAA,EACD,UAAU;AAAA,IACT,2BAA2B;AAC1B,UAAI,KAAK,+BAA+B,IAAI;AAC3C,eAAO,KAAK,+BAA+B;AAAA,MAC3C;AACD,aAAO,KAAK;AAAA,IACZ;AAAA,IACD,uBAAuB;AACtB,UAAI,KAAK,sBAAsB,GAAG;AACjC,aAAK,gBAAgB,SAAS,IAAI,KAAK;AACvC,aAAK,gBAAgB,UAAU,IAAI;AAAA,MACnC;AACD,aAAO,KAAK;AAAA,IACZ;AAAA,IACD,iBAAiB;AAChB,aAAO,KAAK,gBAAgB,KAAK,gBAAgB,KAAK;AAAA,IACtD;AAAA;AAAA,IAED,oBAAoB;AACnB,aAAO,KAAK,gBAAgB,CAAC,KAAK;AAAA,IAClC;AAAA;AAAA,IAED,kBAAkB;AACjB,aAAO,KAAK,cAAc,CAAC,KAAK,iBAAiB,KAAK,iBAClD,KAAK,8BAA8B,OAAO,KAAK,oBAAoBC,sDAAI,KAAC,UAAU,cAClF,KAAK,gCAAgC,OAAO,KAAK,oBAAoBA,2DAAK,UAAU;AAAA,IACxF;AAAA,EACD;AAAA,EACD,SAAS;AAAA;AAAA,IAER,YAAY,SAAS,eAAe,MAAM;AAEzC,UAAI,KAAK,qBAAqB,gBAAgB,CAAC,KAAK,iCAAiC;AACpF,aAAK,eAAe,SAAS,KAAK;AAClC;AAAA,MACA;AACD,WAAK,UAAU,MAAM;AACpB,aAAK,aAAa,SAAS,KAAK;AAAA,MAQpC,CAAI;AAAA,IACD;AAAA;AAAA,IAED,eAAe,SAAS,eAAe,MAAM;AAE5C,UAAI,KAAK,qBAAqB,gBAAgB,CAAC,KAAK,iCAAiC;AACpF,aAAK,YAAY,SAAS,KAAK;AAC/B;AAAA,MACA;AACD,WAAK,UAAU,MAAM;AACpB,aAAK,gBAAgB,OAAO;AAAA,MAQhC,CAAI;AAAA,IACD;AAAA;AAAA,IAED,mBAAmB,KAAK,QAAQ,SAAS;AACxC,WAAK,gBAAgB,KAAK,QAAQ,OAAO;AAAA,IACzC;AAAA;AAAA,IAED,wBAAwB,SAAS,QAAQ,SAAS;AACjD,WAAK,YAAY,KAAK;AACtB,WAAK,UAAU,MAAM;AACpB,aAAK,yBAAyB,SAAS,QAAQ,OAAO;AAAA,MAC1D,CAAI;AAAA,IACD;AAAA;AAAA,IAED,UAAU,GAAG,QAAQ,SAAS;AAC7B,WAAK,YAAY,KAAK;AACtB,WAAK,UAAU,MAAM;AACpB,aAAK,WAAW,GAAG,QAAQ,OAAO;AAAA,MACtC,CAAI;AAAA,IACD;AAAA;AAAA,IAED,sBAAsB,OAAO,QAAQ,SAAS;AAC7C,UAAI,SAAS,KAAK,cAAc,QAAQ;AACvCD,+DAAC,EAAC,WAAW,iEAAiE;AAC9E;AAAA,MACA;AACD,WAAK,UAAU,MAAM;AAMpB,YAAI,KAAK,qBAAqB;AAC7B,gBAAM,cAAc,KAAK,mBAAmBC,sDAAAA,KAAK,eAAe;AAChED,iEAAC,EAAC,MAAM,MAAM;AACb,gBAAI,KAAK,qBAAqB;AAG7B,oBAAM,YAAY,cAAc,KAAK,oBAAoB,QAAQ,KAAK,uBAAuB,KAAK,EAAE;AACpG,mBAAK,UAAU,WAAW,QAAQ,OAAO;AAAA,YACzC;AAAA,UACP,GAAQ,cAAc,IAAI,GAAG;AAAA,QACxB;AAAA,MAEL,CAAI;AAAA,IACD;AAAA;AAAA,IAED,qBAAqB,MAAM,QAAQ,SAAS;AAC3C,WAAK,gBAAgB,MAAM,QAAQ,OAAO;AAAA,IAC1C;AAAA;AAAA,IAED,oBAAoB,OAAO;AAC1B,WAAK,gBAAgB;AAAA,IACrB;AAAA;AAAA,IAED,4BAA4B;AAC3B,WAAK,mCAAmC,KAAK;AAAA,IAC7C;AAAA;AAAA,IAED,+BAA+B;AAC9B,WAAK,mCAAmC,QAAQ;AAAA,IAChD;AAAA;AAAA,IAED,0BAA0B;AACzB,UAAI,CAAC,KAAK;AAAmB;AAC7B,WAAK,UAAU,MAAM,KAAK,yBAAyB,KAAK,0BAA0B,SAAS,CAAC;AAAA,IAC5F;AAAA;AAAA,IAED,0BAA0B,WAAW,UAAU,MAAM;AACpD,WAAK,kCAAkC,OAAO;AAC9C,WAAK,YAAY,KAAK;AACtB,WAAK,UAAU,MAAM;AACpB,aAAK,YAAY;AACjB,aAAK,eAAe,KAAK;AAAA,MAC7B,CAAI;AAAA,IACD;AAAA;AAAA,IAGD,mBAAmB;AAClB,WAAK,MAAM,eAAe;AAC1B,WAAK,MAAM,mBAAmB,CAAC;AAC/B,WAAK,UAAU,MAAM;AACpB,aAAK,eAAe;AAAA,MACxB,CAAI;AAAA,IACD;AAAA;AAAA,IAED,iBAAiB,GAAG;AACnB,OAAC,CAAC,EAAE,UAAU,CAAC,EAAE,OAAO,aAAa,EAAE,OAAO,cAAc,aAAa,KAAK,eAAe,KAAK,oBAAoB,UAAU,UAAU;AAAA,IAC1I;AAAA;AAAA,IAED,aAAa,UAAU,MAAM,YAAY,MAAM;AA8B9C,UAAI,KAAK,eAAe;AACvB,aAAK,UAAU,MAAM;AACpBE,wBAAAA,MAAI,aAAa;AAAA,YAChB,WAAW;AAAA,YACX,UAAU,UAAU,MAAM;AAAA,UAChC,CAAM;AAAA,QACN,CAAK;AACD;AAAA,MACA;AACD,WAAK,kCAAkC,OAAO;AAC9C,WAAK,YAAY,KAAK;AACtB,WAAK,UAAU,MAAM;AACpB,aAAK,YAAY;AACjB,aAAK,eAAe,KAAK;AAAA,MAC7B,CAAI;AAAA,IACD;AAAA;AAAA,IAED,MAAM,gBAAgB,UAAU,MAAM;AAcrC,UAAI,KAAK,eAAe;AACvB,aAAK,UAAU,MAAM;AACpBA,wBAAAA,MAAI,aAAa;AAAA,YAChB,WAAW,OAAO;AAAA,YAClB,UAAU,UAAU,MAAM;AAAA,UAChC,CAAM;AAAA,QACN,CAAK;AACD;AAAA,MACA;AACD,UAAI;AACH,aAAK,kCAAkC,OAAO;AAC9C,cAAM,sBAAsB,MAAM,KAAK,mBAAmB,sBAAsB;AAChF,cAAM,iBAAiB,MAAM,KAAK,mBAAmB,iBAAiB;AACtE,cAAM,mBAAmB,sBAAsB,oBAAoB,CAAC,EAAE,SAAS;AAC/E,cAAM,cAAc,iBAAiB,eAAe,CAAC,EAAE,SAAS;AAChE,YAAI,mBAAmB,aAAa;AACnC,eAAK,YAAY,KAAK;AACtB,eAAK,UAAU,MAAM;AACpB,iBAAK,YAAY,mBAAmB,cAAc,KAAK;AACvD,iBAAK,eAAe,KAAK;AAAA,UAC/B,CAAM;AAAA,QACD;AAAA,MACL,SAAY,GAAG;AAAA,MAAE;AAAA,IACd;AAAA;AAAA,IAED,gBAAgB,KAAK,SAAS,GAAG,UAAU,OAAO,gBAAgB;AACjE,UAAI;AACH,aAAK,YAAY,KAAK;AACtB,aAAK,UAAU,MAAM;AAwBpB,eAAK,mBAAmB,MAAM,IAAI,QAAQ,KAAK,EAAE,GAAG,KAAK,OAAO,EAAE,KAAK,CAAC,SAAS;AAChF,gBAAI,MAAM;AACT,kBAAI,UAAU,KAAK,CAAC,EAAE;AACtB,mBAAK,yBAAyB,SAAS,QAAQ,OAAO;AACtD,gCAAkB,eAAc;AAAA,YAChC;AAAA,UACP,CAAM;AAAA,QACN,CAAK;AAAA,MACL,SAAY,GAAG;AAAA,MAAE;AAAA,IACd;AAAA;AAAA,IAED,yBAAyB,SAAS,SAAS,GAAG,UAAU,OAAO;AAE9D,UAAI,KAAK,8BAA8B;AACtC,aAAK,mBAAmB,iBAAiB,EAAE,KAAK,WAAS;AACxD,cAAI,OAAO;AACV,iBAAK,WAAW,MAAM,CAAC,EAAE,SAAS,SAAS,QAAQ,SAAS,IAAI;AAAA,UAChE;AAAA,QACN,CAAK;AAAA,MACL,OAAU;AACN,aAAK,WAAW,SAAS,QAAQ,SAAS,IAAI;AAAA,MAC9C;AAAA,IACD;AAAA;AAAA,IAED,WAAW,GAAG,SAAS,GAAG,UAAU,OAAO,eAAe,OAAO;AAChE,WAAK,kCAAkC,OAAO;AAC9CF,6DAAC,EAAC,MAAM,MAAM;AACb,YAAI,KAAK,eAAe;AACvB,cAAI,gBAAgB,KAAK,kBAAkB,IAAI;AAC5C,iBAAK,KAAK;AAAA,UACZ;AACD,gBAAM,YAAY,IAAI;AACtBE,wBAAAA,MAAI,aAAa;AAAA,YAChB;AAAA,YACA,UAAU,UAAU,MAAM;AAAA,UAChC,CAAM;AAAA,QACN,OAAW;AACN,cAAI,cAAc;AACf,iBAAK,KAAK;AAAA,UACZ;AACD,eAAK,YAAY,IAAI;AAAA,QACrB;AAAA,MACD,GAAE,EAAE;AAAA,IACL;AAAA;AAAA,IAED,QAAQ,GAAG;AACV,WAAK,MAAM,UAAU,CAAC;AACtB,YAAM,YAAY,EAAE,OAAO;AAE3B,WAAK,uBAAuB,KAAK,qBAAqB,WAAW,KAAK,eAAe,SAAS;AAE9F,WAAK,eAAe;AAEpB,YAAM,aAAa,EAAE,OAAO,eAAe,KAAK;AAEhD,OAAC,KAAK,SAAS,KAAK,uBAAuB,UAAU;AAAA,IACrD;AAAA;AAAA,IAED,kCAAkC,SAAS;AAC1C,WAAK,6BAA6B,UAAU,IAAI;AAChDF,6DAAAA,EAAE,MAAM,MAAM,KAAK,UAAU,MAAM;AAElC,aAAK,6BAA6B;AAAA,MACtC,CAAI,GAAG,KAAK,gCAAgC;AAAA,IACzC;AAAA;AAAA,IAED,mCAAmC,WAAW;AAC7C,UAAI,KAAK,kBAAkB,KAAK,iBAAiB,KAAK,0BAA0B;AAE/E,aAAK,UAAU,MAAM;AACpB,eAAK,iCAAiC,CAAC,gBAAgB,wBAAwB;AAC9E,iBAAK,0BAA0B,WAAW,gBAAgB,mBAAmB;AAAA,UACnF,CAAM;AAAA,QACN,CAAK;AAAA,MAKL,OAAU;AACN,aAAK,0BAA0B,SAAS;AAAA,MACxC;AAAA,IACD;AAAA;AAAA,IAED,MAAM,iCAAiC,UAAU;AAChD,UAAI;AACH,cAAM,iBAAiB,MAAM,KAAK,mBAAmB,iBAAiB;AACtE,cAAM,sBAAsB,MAAM,KAAK,mBAAmB,8BAA8B;AACxF,YAAI,CAAC,kBAAkB,CAAC;AAAqB;AAC7C,cAAM,mBAAmB,oBAAoB,CAAC,EAAE;AAChD,cAAM,gBAAgB,eAAe,CAAC,EAAE;AACxC,YAAI,KAAK,eAAe,mBAAmB,iBAAiB,KAAK,cAAc;AAC9E,eAAK,eAAe,MAAM,cAAc;AACxC,mBAAS,gBAAgB,mBAAmB;AAAA,QACjD,OAAW;AACN,eAAK,eAAe,KAAK;AACzB,mBAAS,MAAM,IAAI;AAAA,QACnB;AAAA,MACD,SAAQ,GAAG;AACX,iBAAS,MAAM,IAAI;AAAA,MACnB;AAAA,IACD;AAAA;AAAA,IAED,MAAM,kCAAkC;AACvC,YAAM,mBAAmB,MAAM,KAAK,mBAAmB,mBAAmB;AAC1E,UAAI,kBAAkB;AACrB,aAAK,qBAAqB,iBAAiB,CAAC,EAAE;AAAA,MAC9C;AAAA,IACD;AAAA;AAAA,IAED,iBAAiB,QAAQ,iBAAgB;AACxC,WAAK,MAAM,mBAAmB,MAAM;AACpC,WAAK,MAAM,oBAAoB,MAAM;AACrC,WAAK,0BAA0B,MAAM;AAGrC,YAAM,YAAY,SAAS,IAAI,IAAI;AACnC,UAAI,mBAAmB,KAAK,qBAAqB,WAAW;AAC3D,aAAK,mBAAmB;AAAA,MACxB,WAAU,CAAC,mBAAmB,KAAK,iBAAiB,WAAW;AAC/D,aAAK,eAAe;AACpB,YAAI,YAAY,GAAG;AAClB,eAAK,eAAe;AAAA,QACpB;AAAA,MACD;AAAA,IACD;AAAA;AAAA,IAED,mCAAmC,MAAM;AAExC,UAAI,CAAC,KAAK;AAAe;AAEzB,WAAK,mCAAmC,KAAK,aAAa;AAC1D,YAAM,OAAO,YAAY,IAAI;AAC7B,YAAM,aAAa,SAAS,KAAK,MAAM,GAAE,CAAC,EAAE,YAAa,IAAG,KAAK,MAAM,CAAC,CAAC;AACzE,UAAI,yBAAyB,KAAK;AAClC,WAAK,UAAU,MAAM;AACpB,YAAI,YAAY;AAIhBA,+DAAC,EAAC,MAAM,MAAM;AACb,eAAK,mBAAmB,IAAI,EAAE,KAAK,CAAC,QAAQ;AAC3C,gBAAI,KAAK;AACR,kBAAI,uBAAuB,IAAI,CAAC,EAAE;AAClC,kBAAI,SAAS,UAAU;AACtB,oBAAI,wBAAwB;AAC3B,0CAAwB,KAAK;AAAA,gBAC7B;AAAA,cACT,OAAc;AACN,qBAAK,iBAAiB;AAAA,cACtB;AACD,mBAAK,KAAK,KAAK,iBAAiB,YAAY,GAAG,oBAAoB,IAAI;AAAA,YACvE,WAAU,wBAAwB;AAClC,mBAAK,KAAK,KAAK,iBAAiB,YAAY,GAAG,KAAK,cAAc,IAAI;AAAA,YACtE;AAAA,UACP,CAAM;AAAA,QACD,GAAE,SAAS;AAAA,MAChB,CAAI;AAAA,IACD;AAAA,EACD;AACF;;"}