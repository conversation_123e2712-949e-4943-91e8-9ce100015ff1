"use strict";const e=require("../../common/vendor.js"),o=require("../../common/assets.js"),s=require("../../api/index.js"),t=require("../../stores/user.js"),i={__name:"video-complete",setup(i){const n=t.useUserStore(),a=e.ref(""),r=e.ref(""),d=e.ref(""),c=async()=>{if(a.value){e.index.showLoading({title:"正在保存..."});try{const o=await new Promise(((o,s)=>{e.index.downloadFile({url:a.value,success:e=>{200===e.statusCode?o(e.tempFilePath):s(new Error("下载失败"))},fail:e=>{s(e)}})}));await new Promise(((s,t)=>{e.index.saveVideoToPhotosAlbum({filePath:o,success:()=>{s()},fail:o=>{o.errMsg.includes("auth")&&e.index.showModal({title:"提示",content:"需要您授权访问相册才能保存视频，请在设置中开启相册权限",showCancel:!1}),t(o)}})})),e.index.hideLoading(),e.index.showToast({title:"保存成功",icon:"success",duration:2e3})}catch(o){e.index.hideLoading(),console.error("保存视频失败:",o),e.index.showToast({title:"保存失败，请重试",icon:"none",duration:2e3})}}else e.index.showToast({title:"视频还未加载完成",icon:"none"})},l=()=>{a.value?e.index.showActionSheet({itemList:["微信好友","朋友圈"],success:o=>{e.index.showToast({title:`分享到${["微信好友","朋友圈"][o.tapIndex]}`,icon:"success",duration:2e3})}}):e.index.showToast({title:"视频还未加载完成",icon:"none"})};return e.onLoad((o=>{o.orderNo?(d.value=o.orderNo,(async()=>{try{const o=await s.getVideoDetailApi({merchantGuid:n.merchantGuid,orderNo:d.value});if(0===o.code){const{videoUrl:e,previewUrl:s}=o.data;e&&(a.value=e),s&&(r.value=s)}else console.error("获取视频详情失败:",o.msg),e.index.showToast({title:"获取视频失败",icon:"none"})}catch(o){console.error("获取视频详情失败:",o),e.index.showToast({title:"获取视频失败",icon:"none"})}})()):a.value="https://vd3.bdstatic.com/mda-ka0x6301f525mw5e/mda-ka0x6301f525mw5e.mp4?playlist=%5B%22hd%22%2C%22sc%22%5D"})),e.onMounted((()=>{})),(s,t)=>({a:a.value,b:r.value,c:o._imports_0$7,d:e.o(c),e:o._imports_1$4,f:e.o(l)})}},n=e._export_sfc(i,[["__scopeId","data-v-02029c32"]]);wx.createPage(n);
