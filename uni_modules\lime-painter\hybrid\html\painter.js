!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).Painter={})}(this,(function(t){"use strict";var e=function(){return e=Object.assign||function(t){for(var e,i=1,n=arguments.length;i<n;i++)for(var r in e=arguments[i])Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t},e.apply(this,arguments)};function i(t,e,i,n){return new(i||(i=Promise))((function(r,o){function s(t){try{h(n.next(t))}catch(t){o(t)}}function a(t){try{h(n.throw(t))}catch(t){o(t)}}function h(t){var e;t.done?r(t.value):(e=t.value,e instanceof i?e:new i((function(t){t(e)}))).then(s,a)}h((n=n.apply(t,e||[])).next())}))}function n(t,e){var i,n,r,o,s={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]};return o={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(o){return function(a){return function(o){if(i)throw new TypeError("Generator is already executing.");for(;s;)try{if(i=1,n&&(r=2&o[0]?n.return:o[0]?n.throw||((r=n.return)&&r.call(n),0):n.next)&&!(r=r.call(n,o[1])).done)return r;switch(n=0,r&&(o=[2&o[0],r.value]),o[0]){case 0:case 1:r=o;break;case 4:return s.label++,{value:o[1],done:!1};case 5:s.label++,n=o[1],o=[0];continue;case 7:o=s.ops.pop(),s.trys.pop();continue;default:if(!(r=s.trys,(r=r.length>0&&r[r.length-1])||6!==o[0]&&2!==o[0])){s=0;continue}if(3===o[0]&&(!r||o[1]>r[0]&&o[1]<r[3])){s.label=o[1];break}if(6===o[0]&&s.label<r[1]){s.label=r[1],r=o;break}if(r&&s.label<r[2]){s.label=r[2],s.ops.push(o);break}r[2]&&s.ops.pop(),s.trys.pop();continue}o=e.call(t,s)}catch(t){o=[6,t],n=0}finally{i=r=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,a])}}}var r={MP_WEIXIN:"mp-weixin",MP_QQ:"mp-qq",MP_ALIPAY:"mp-alipay",MP_BAIDU:"mp-baidu",MP_TOUTIAO:"mp-toutiao",MP_DINGDING:"mp-dingding",H5:"h5",WEB:"web",PLUS:"plus"},o="width",s="height",a="margin",h="center",c="flex-start",f="flex-end",d=["Top","Right","Bottom","Left"],l="left",u="right",p="top",g="bottom",v="middle",y=["contentSize","clientSize","borderSize","offsetSize"],b="row",x="column",w={TOP:p,MIDDLE:v,BOTTOM:g},m={LEFT:l,CENTER:h,RIGHT:u},S="view",z="text",I="image",M="qrcode",k="block",B="inline-block",W="none",P="flex",O="absolute",T="fixed",L="transparent",R="fill",F={display:k,color:"#000000",lineHeight:"1.4em",fontSize:14,fontWeight:400,fontFamily:"sans-serif",lineCap:"butt",flexDirection:b,flexWrap:"nowrap",textAlign:l,alignItems:c,justifyContent:c,position:"static",transformOrigin:"".concat(h," ").concat(h)},A={upx2px:function(t){return window.innerWidth/750*t},getSystemInfoSync:function(){return{screenWidth:window.innerWidth,screenHeight:window.innerHeight}},getImageInfo:function(t){var e=t.src,i=t.success,n=t.fail,r=new Image;r.onload=function(){i({width:r.naturalWidth,height:r.naturalHeight,path:r.src,src:e})},r.onerror=n,r.src=e}},E="object",j="undefined",C=typeof window==E?typeof uni==j||typeof uni!==j&&!uni.addInterceptor?r.WEB:r.H5:typeof swan==E?r.MP_BAIDU:typeof tt==E?r.MP_TOUTIAO:typeof plus===E?r.PLUS:typeof wx==E?r.MP_WEIXIN:void 0,H=C==r.MP_WEIXIN?wx:typeof uni!=j?uni.getImageInfo?{upx2px:function(t){return uni.upx2px(t)},getSystemInfoSync:function(){return uni.getSystemInfoSync()},getImageInfo:function(t){return uni.getImageInfo(t)},downloadFile:function(t){return uni.downloadFile(t)}}:Object.assign(uni,A):typeof window!=j?A:uni;if(!H.upx2px){var D=((H.getSystemInfoSync&&H.getSystemInfoSync()).screenWidth||375)/750;H.upx2px=function(t){return D*t}}function $(t){return/^-?\d+(\.\d+)?$/.test(t)}function Y(t,e,i){if($(t))return 1*t;if("string"==typeof t){var n=/^-?([0-9]+)?([.]{1}[0-9]+){0,1}(em|rpx|vw|vh|px|%)$/g.exec(t);if(!t||!n)return 0;var r=n[3];t=parseFloat(t);var o=0;if("rpx"===r)o=H.upx2px(t);else if("px"===r)o=1*t;else if("%"===r&&e)o=t*Y(e)/100;else if("em"===r&&e)o=t*Y(e||14);else if(["vw","vh"].includes(r)){var s=H.getSystemInfoSync(),a=s.screenWidth,h=s.screenHeight;o=t*("vw"==r?a:h)/100}return 1*o.toFixed(2)}return 0}function U(t){return/%$/.test(t)}function N(t){return/^-?([0-9]+)?([.]{1}[0-9]+){0,1}(rpx|px)$/.test(t)}var _=function(t){return!(!t||!t.startsWith("linear")&&!t.startsWith("radial"))},X=function(t,e,i,n,r,o){t.startsWith("linear")?function(t,e,i,n,r,o){for(var s=function(t,e,i,n,r){void 0===n&&(n=0);void 0===r&&(r=0);var o=t.match(/([-]?\d{1,3})deg/),s=o&&o[1]?parseFloat(o[1]):0;s>=360&&(s-=360);s<0&&(s+=360);if(0===(s=Math.round(s)))return{x0:Math.round(e/2)+n,y0:i+r,x1:Math.round(e/2)+n,y1:r};if(180===s)return{x0:Math.round(e/2)+n,y0:r,x1:Math.round(e/2)+n,y1:i+r};if(90===s)return{x0:n,y0:Math.round(i/2)+r,x1:e+n,y1:Math.round(i/2)+r};if(270===s)return{x0:e+n,y0:Math.round(i/2)+r,x1:n,y1:Math.round(i/2)+r};var a=Math.round(180*Math.asin(e/Math.sqrt(Math.pow(e,2)+Math.pow(i,2)))/Math.PI);if(s===a)return{x0:n,y0:i+r,x1:e+n,y1:r};if(s===180-a)return{x0:n,y0:r,x1:e+n,y1:i+r};if(s===180+a)return{x0:e+n,y0:r,x1:n,y1:i+r};if(s===360-a)return{x0:e+n,y0:i+r,x1:n,y1:r};var h=0,c=0,f=0,d=0;if(s<a||s>180-a&&s<180||s>180&&s<180+a||s>360-a){var l=s*Math.PI/180,u=s<a||s>360-a?i/2:-i/2,p=Math.tan(l)*u,g=s<a||s>180-a&&s<180?e/2-p:-e/2-p;h=-(f=p+(v=Math.pow(Math.sin(l),2)*g)),c=-(d=u+v/Math.tan(l))}if(s>a&&s<90||s>90&&s<90+a||s>180+a&&s<270||s>270&&s<360-a){var v;l=(90-s)*Math.PI/180,p=s>a&&s<90||s>90&&s<90+a?e/2:-e/2,u=Math.tan(l)*p,g=s>a&&s<90||s>270&&s<360-a?i/2-u:-i/2-u;h=-(f=p+(v=Math.pow(Math.sin(l),2)*g)/Math.tan(l)),c=-(d=u+v)}return h=Math.round(h+e/2)+n,c=Math.round(i/2-c)+r,f=Math.round(f+e/2)+n,d=Math.round(i/2-d)+r,{x0:h,y0:c,x1:f,y1:d}}(r,t,e,i,n),a=s.x0,h=s.y0,c=s.x1,f=s.y1,d=o.createLinearGradient(a,h,c,f),l=r.match(/linear-gradient\((.+)\)/)[1],u=q(l.substring(l.indexOf(",")+1)),p=0;p<u.colors.length;p++)d.addColorStop(u.percents[p],u.colors[p]);o.setFillStyle(d)}(e,i,n,r,t,o):t.startsWith("radial")&&function(t,e,i,n,r,o){for(var s=q(r.match(/radial-gradient\((.+)\)/)[1]),a=Math.round(t/2)+i,h=Math.round(e/2)+n,c=o.createRadialGradient(a,h,0,a,h,Math.max(t,e)/2),f=0;f<s.colors.length;f++)c.addColorStop(s.percents[f],s.colors[f]);o.setFillStyle(c)}(e,i,n,r,t,o)};function q(t){for(var e=[],i=[],n=0,r=t.substring(0,t.length-1).split("%,");n<r.length;n++){var o=r[n];e.push(o.substring(0,o.lastIndexOf(" ")).trim()),i.push(o.substring(o.lastIndexOf(" "),o.length)/100)}return{colors:e,percents:i}}function G(t,e,i){return e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}function V(){return V=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var i=arguments[e];for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(t[n]=i[n])}return t},V.apply(this,arguments)}function J(t,e){return J=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},J(t,e)}function Q(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,n=new Array(e);i<e;i++)n[i]=t[i];return n}function Z(t,e){var i="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(i)return(i=i.call(t)).next.bind(i);if(Array.isArray(t)||(i=function(t,e){if(t){if("string"==typeof t)return Q(t,e);var i=Object.prototype.toString.call(t).slice(8,-1);return"Object"===i&&t.constructor&&(i=t.constructor.name),"Map"===i||"Set"===i?Array.from(t):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?Q(t,e):void 0}}(t))||e&&t&&"number"==typeof t.length){i&&(t=i);var n=0;return function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function K(t){return"number"==typeof t}function et(t){return"auto"===t||null===t}function it(t){return/%$/.test(t)}var nt=I,rt=z,ot=S,st=M,at=B,ht=O,ct=T;function ft(t){return t.replace(/-([a-z])/g,(function(t,e){return e.toUpperCase()}))}function dt(t,e){var i,n,r=function(t){var e=t.match(/([a-z]+)/)[1];return[e,ft(t.split(e)[1])]}(t),o=r[0],s=r[1],a=e.split(" ");if(s)return(i={})[o+s]=e,i;if(a.length&&!s){var h=a[0],c=a[1],f=a[2],l=a[3];return(n={})[o+d[0]]=h,n[o+d[1]]=c||h,n[o+d[2]]=f||h,n[o+d[3]]=l||c||h,n}}function lt(t){t=t.trim();for(var e=new Array,i="+",n="",r=t.length,o=0;o<r;++o){if("."===t[o]||!isNaN(Number(t[o]))&&" "!==t[o])n+=t[o];else if("("===t[o]){for(var s=1,a=o;s>0;)"("===t[a+=1]&&(s+=1),")"===t[a]&&(s-=1);n="".concat(lt(t.slice(o+1,a))),o=a}if(isNaN(Number(t[o]))&&"."!==t[o]||o===r-1){var h=parseFloat(n);switch(i){case"+":e.push(h);break;case"-":e.push(-h);break;case"*":e.push(e.pop()*h);break;case"/":e.push(e.pop()/h)}i=t[o],n=""}}for(var c=0;e.length;)c+=e.pop();return c}var ut,pt=0,gt=function(){function t(){G(this,"elements",[]),G(this,"afterElements",[]),G(this,"beforeElements",[]),G(this,"ids",[]),G(this,"width",0),G(this,"height",0),G(this,"top",0),G(this,"left",0),G(this,"pre",null),G(this,"offsetX",0),G(this,"offsetY",0),pt++,this.id=pt}var e=t.prototype;return e.fixedBind=function(t,e){void 0===e&&(e=0),this.container=e?t.parent:t.root,this.container.fixedLine=this,this.fixedAdd(t)},e.fixedAdd=function(t){if(!this.ids.includes(t.id)){this.ids.push(t.id),this.elements.push(t);var e=t.computedStyle.zIndex;(void 0===e?0:e)>=0?this.afterElements.push(t):this.beforeElements.push(t),this.refreshLayout()}},e.bind=function(t){this.container=t.parent,this.container.line=null,this.container.lines?(this.container.lines.push(this),this.pre=this.getPreLine(),this.top=this.pre.top+this.pre.height,this.left=this.container.contentSize.left):(this.top=this.container.contentSize.top,this.left=this.container.contentSize.left,this.container.lines=[this]),this.isInline=t.isInline(),this.container.line=this,this.outerWidth=t.parent&&t.parent.contentSize.width?t.parent.contentSize.width:1/0,this.add(t)},e.getPreLine=function(){return this.container.lines[this.container.lines.length-2]},e.canIEnter=function(t){return!((100*t.offsetSize.width+100*this.width)/100>this.outerWidth)||(this.closeLine(),!1)},e.closeLine=function(){delete this.container.line},e.add=function(t){this.ids.includes(t.id)||(this.ids.push(t.id),this.elements.push(t),this.refreshWidthHeight(t))},e.refreshWidthHeight=function(t){t.offsetSize.height>this.height&&(this.height=t.offsetSize.height),this.width+=t.offsetSize.width||0,(this.container.lineMaxWidth||0)<this.width&&(this.container.lineMaxWidth=this.width)},e.refreshXAlign=function(){if(this.isInline){var t=this.container.contentSize.width-this.width,e=this.container.style.textAlign;e===h?t/=2:e===l&&(t=0),this.offsetX=t}},e.getOffsetY=function(t){if(!t||!t.style)return 0;var e=(t.style||{}).verticalAlign;return e===g?this.height-t.contentSize.height:e===v?(this.height-t.contentSize.height)/2:0},e.setIndent=function(t){var e=t.style.textIndent;if(e&&/^calc/.test(e)){var i=/^calc\((.+)\)$/.exec(e);if(i&&i[1]){var n=i[1].replace(/([^\s\(\+\-\*\/]+)\.(left|right|bottom|top|width|height)/g,(function(e){var i=e.split("."),n=i[0],r=i[1],o=t.parent.querySelector(n);if(o&&o.offsetSize){var s={right:o.offsetSize.left+o.offsetSize.width,bottom:o.offsetSize.top+o.offsetSize.height};return o.offsetSize[r]||s[r]||0}})),r=lt(n.replace(new RegExp(/-?[0-9]+(\.[0-9]+)?(rpx|px|%)/,"g"),Y));t.style.textIndent=r}}},e.layout=function(t,e){var i=this;this.refreshXAlign(),this.pre?(this.top=this.pre.top+this.pre.height+this.offsetY,this.left=e+this.offsetX):(this.top=Math.max(this.top,this.container.contentSize.top,t)+this.offsetY,this.left=Math.max(this.left,this.container.contentSize.left,e)+this.offsetX),this.elements.forEach((function(t,e){i.setIndent(t);var n=i.elements[e-1],r=i.getOffsetY(t);t.style.top=i.top+r,t.style.left=n?n.offsetSize.left+n.offsetSize.width:i.left,t.getBoxPosition()}))},e.refreshLayout=function(){this.afterElements=this.afterElements.sort((function(t,e){return t.computedStyle.zIndex-e.computedStyle.zIndex})),this.beforeElements=this.beforeElements.sort((function(t,e){return t.computedStyle.zIndex-e.computedStyle.zIndex}))},t}(),vt="lineMaxHeight",yt="lineMaxWidth",bt=((ut={})[b]={width:o,contentWidth:o,lineMaxWidth:yt,left:l,top:p,height:s,lineMaxHeight:vt,marginLeft:"marginLeft"},ut[x]={width:s,contentWidth:s,lineMaxWidth:yt,left:p,top:l,height:o,lineMaxHeight:vt,marginLeft:"marginTop"},ut),xt=function(t){var e,i;function n(){var e;return G(function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(e=t.call(this)||this),"outerWidth",0),e.exactValue=0,e.flexTotal=0,e.width=0,e.key=null,e.flexDirection="row",e}i=t,(e=n).prototype=Object.create(i.prototype),e.prototype.constructor=e,J(e,i);var r=n.prototype;return r.bind=function(t){this.container=t.parent,this.container.line=this,this.container.lines?(this.container.lines.push(this),this.pre=this.getPreLine(),this.top=this.pre.top+this.pre.height,this.left=this.container.contentSize.left):(this.top=this.container.contentSize.top,this.left=this.container.contentSize.left,this.container.lines=[this]),t.parent&&(this.flexDirection=t.parent.style.flexDirection,this.key=bt[this.flexDirection]),this.initHeight(t),this.outerWidth=t.parent&&t.parent.contentSize[this.key.contentWidth]?t.parent.contentSize[this.key.contentWidth]:1/0,this.add(t)},r.add=function(t){this.ids.push(t.id);var e=t.style.flex;K(e)?this.flexTotal+=e:K(this.getWidth(t.style))&&(this.exactValue+=this.getWidth(t.offsetSize)),this.elements.push(t),this.refreshWidthHeight(t),t.next||this.closeLine()},r.closeLine=function(){this.calcFlex()},r.initHeight=function(t){this[this.key.height]=0},r.getWidth=function(t){return t[this.key.width]||0},r.getHeight=function(t){return t[this.key.height]||0},r.setWidth=function(t,e){t[this.key.width]=e},r.setHeight=function(t,e){t[this.key.height]=e},r.calcFlex=function(){var t=this,e=this.container.contentSize[this.key.contentWidth],i=0;this.elements.forEach((function(n){var r=n.style,o=n.contentSize,s=t.getWidth(r)||t.getWidth(o);K(r.flex)&&(s=r.flex/t.flexTotal*(e-t.exactValue)),t.setWidth(n.computedStyle,s),n.isFlexCalc=!0,delete n.line,delete n.lines,delete n.lineMaxWidth,n.getBoxWidthHeight(),i=Math.max(i,t.getHeight(n.offsetSize))})),this.setHeight(this,i)},r.refreshWidthHeight=function(t){var e=this.container.style.alignItems;e&&!t.style.alignSelf&&(t.style.alignSelf=e);var i=this.getHeight(t.offsetSize);i>this[this.key.height]&&(this.container[this.key.lineMaxHeight]=this[this.key.height]=i),this[this.key.width]+=this.getWidth(t.offsetSize);var n=Math.min(this.getWidth(this),!this.getWidth(this.container.contentSize)&&1/0);(this.container[this.key.lineMaxWidth]||0)<n&&(this.container[this.key.lineMaxWidth]=n)},r.refreshXAlign=function(){var t=this,e=this.elements.reduce((function(e,i){return e+t.getWidth(i.offsetSize)}),0),i=(this.outerWidth==1/0?0:this.outerWidth-e)||0,n=this.container.style.justifyContent;n===h?i/=2:n===c?i=0:["space-between","space-around"].includes(n)&&(!function(e,i){void 0===i&&(i=0),i/=t.elements.length+(e?-1:1),t.elements.forEach((function(n,r){var o;e&&!r||(n.style.margin?n.style.margin[t.key.marginLeft]+=i:n.style.margin=((o={})[t.key.marginLeft]=i,o),n.getBoxPosition())})),i=0}("space-between"==n,i),i=0),this.offsetX=i||0,this.refreshYAlign()},r.refreshYAlign=function(){var t=this;if(1==this.container.lines.length)return 0;var e=this.container.lines.reduce((function(e,i){return e+t.getHeight(i)}),0),i=this.container.style.alignItems,n=this.getHeight(this.container.contentSize);if(i===h){var r=(n-e)/(this.container.lines.length+1);this.container.lines.forEach((function(t){t.offsetY=r}))}if(i===f){var o=n-e;this.container.lines[0].offsetY=o}},r.getOffsetY=function(t){if(this.container.lines.length>1)return 0;var e=t.style.alignSelf,i=this.getHeight(this.container.contentSize),n=i-this.getHeight(t.offsetSize);return e===f?n:e===h?n/2:"stretch"===e?(n&&t.name==S&&(t.style[this.key.width]=this.getWidth(t.offsetSize),t.style[this.key.height]=i,delete t.line,delete t.lines,t.getBoxWidthHeight()),0):0},r.layout=function(t,e){var i=this;this.refreshXAlign(),this.pre?(this.top=this.pre.top+this.pre.height+this.offsetY,this.left=e+this.offsetX):(this.top=Math.max(this.top,this.container.contentSize.top,t)+this.offsetY,this.left=Math.max(this.left,this.container.contentSize.left,e)+this.offsetX),this.elements.forEach((function(t,e){i.setIndent(t);var n=i.elements[e-1],r=i.getOffsetY(t);t.style[i.key.top]=i[i.key.top]+r,t.style[i.key.left]=n?n.offsetSize[i.key.left]+i.getWidth(n.offsetSize):i[i.key.left],t.getBoxPosition()}))},n}(gt),wt=I,mt=z,St=S,zt=k,It=B,Mt=P,kt=O,Bt=T,Wt=0,Pt={left:null,top:null,width:null,height:null},Ot=new Map,Tt=function(){function t(t,e,i,n){var r=this;G(this,"id",Wt++),G(this,"style",{left:null,top:null,width:null,height:null}),G(this,"computedStyle",{}),G(this,"originStyle",{}),G(this,"children",{}),G(this,"layoutBox",V({},Pt)),G(this,"contentSize",V({},Pt)),G(this,"clientSize",V({},Pt)),G(this,"borderSize",V({},Pt)),G(this,"offsetSize",V({},Pt)),this.ctx=n,this.root=i,e&&(this.parent=e),this.name=t.type||t.name,this.attributes=this.getAttributes(t);var o=function(t,e){var i,n=["color","fontSize","lineHeight","verticalAlign","fontWeight","textAlign"],r=t.type,o=void 0===r?ot:r,s=t.styles,h=void 0===s?{}:s,c=(e||{}).computedStyle,f=Object.assign({},F);if([rt,nt,st].includes(o)&&!h.display&&(f.display=at),c)for(var l=0;l<n.length;l++){var u=n[l];(h[u]||c[u])&&(h[u]=h[(i=u,i.replace(/([A-Z])/g,"-$1").toLowerCase())]||h[u]||c[u])}for(var p=function(t){var e,i,n,r,s=h[t];if(/-/.test(t)&&(t=ft(t),f[t]=s),/^(box|text)?shadow$/i.test(t)){var c=[];return s.replace(/((-?\d+(rpx|px|vw|vh)?\s+?){3})(.+)/,(function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];c=t[1].match(/-?\d+(rpx|px|vw|vh)?/g).map((function(t){return Y(t)})).concat(t[4])})),/^text/.test(t)?f.textShadow=c:f.boxShadow=c,"continue"}if(/^border/i.test(t)&&!/radius$/i.test(t)){var l=t.match(/^border([BTRLa-z]+)?/)[0],u=t.match(/[W|S|C][a-z]+/),p=s.replace(/([\(,])\s+|\s+([\),])/g,"$1$2").split(" ").map((function(t){return/^\d/.test(t)?Y(t,""):t}));return f[l]||(f[l]={}),1==p.length&&u?f[l][l+u[0]]=p[0]:f[l]=((e={})[l+"Width"]=$(p[0])?p[0]:0,e[l+"Style"]=p[1]||"solid",e[l+"Color"]=p[2]||"black",e),"continue"}if(/^background(color)?$/i.test(t))return f.backgroundColor=s,"continue";if(/^objectPosition$/i.test(t))return f[t]=s.split(" "),"continue";if(/^backgroundPosition$/i.test(t))return f[t]=s.split(" "),"continue";if(/padding|margin|radius/i.test(t)){var g=/radius$/i.test(t),v="borderRadius",y=g?v:t.match(/[a-z]+/)[0],b=function(t,e){return"border".concat(t).concat(e,"Radius")},x=[0,0,0,0].map((function(t,e){return g?[b(d[0],d[3]),b(d[0],d[1]),b(d[2],d[1]),b(d[2],d[3])][e]:y+d[e]}));if("padding"===t||t===a||/^(border)?radius$/i.test(t)){p="".concat(s).split(" ").map((function(e){return/^-?\d+(rpx|px|vh|vw)?$/.test(e)?Y(e):t!=a&&/auto/.test(e)?0:e}),[])||[0];var w=g?v:t,m=p[0],S=p[1],z=p[2],I=p[3];f[w]=((i={})[x[0]]=et(m)?0:m,i[x[1]]=$(S)||et(S)?S:m,i[x[2]]=et($(z)?z:m)?0:$(z)?z:m,i[x[3]]=$(I)?I:null!=S?S:m,i)}else"object"==typeof f[y]||(f[y]=((n={})[x[0]]=f[y]||0,n[x[1]]=f[y]||0,n[x[2]]=f[y]||0,n[x[3]]=f[y]||0,n)),f[y][t]=y==a&&et(s)||it(s)?s:Y(s);return"continue"}if(/^transform$/i.test(t))return f[t]={},s.replace(/([a-zA-Z]+)\(([0-9,-\.%rpxdeg\s]+)\)/g,(function(e,i,n){var r=n.split(",").map((function(t){return t.replace(/(^\s*)|(\s*$)/g,"")})),o=function(t,e){return t.includes("deg")?1*t:e&&!it(e)?Y(t,e):t};i.includes("matrix")?f[t][i]=r.map((function(t){return 1*t})):i.includes("rotate")?f[t][i]=1*n.match(/^-?\d+(\.\d+)?/)[0]:/[X, Y]/.test(i)?f[t][i]=/[X]/.test(i)?o(r[0],h.width):o(r[0],h.height):(f[t][i+"X"]=o(r[0],h.width),f[t][i+"Y"]=o(r[1]||r[0],h.height))})),"continue";if(/^font$/i.test(t)&&console.warn("font 不支持简写"),/^textindent/i.test(t)&&(f[t]=/^calc/.test(s)?s:Y(s)),/^textstroke/i.test(t)){var M=t.match(/color|width|type$/i),k=(l="textStroke",s.split(" ").map((function(t){return/^\d+(rpx|px|vh|vw)?$/.test(t)?Y(t):t})));return M?f[l]?f[l][M[0]]=k[0]:f[l]=((r={})[M[0]]=k[0],r):f[l]={width:k[0],color:k[1],type:k[2]},"continue"}/^left|top$/i.test(t)&&![ht,ct].includes(h.position)?f[t]=0:f[t]=/^-?[\d\.]+(px|rpx|vw|vh)?$/.test(s)?Y(s):/em$/.test(s)&&o==rt?Y(s,h.fontSize):s},g=0,v=Object.keys(h);g<v.length;g++)p(v[g]);return f}(t,e);this.isAbsolute=o.position==kt,this.isFixed=o.position==Bt,this.originStyle=o,this.styles=t.styles,Object.keys(o).forEach((function(t){Object.defineProperty(r.style,t,{configurable:!0,enumerable:!0,get:function(){return o[t]},set:function(e){o[t]=e}})}));var s={contentSize:V({},this.contentSize),clientSize:V({},this.clientSize),borderSize:V({},this.borderSize),offsetSize:V({},this.offsetSize)};Object.keys(s).forEach((function(t){Object.keys(r[t]).forEach((function(e){Object.defineProperty(r[t],e,{configurable:!0,enumerable:!0,get:function(){return s[t][e]},set:function(i){s[t][e]=i}})}))})),this.computedStyle=this.style}var e=t.prototype;return e.add=function(t){t.parent=this,this.children[t.id]=t},e.getChildren=function(){var t=this;return Object.keys(this.children).map((function(e){return t.children[e]}))},e.prev=function(t){void 0===t&&(t=this);var e=t.parent.getChildren();return e[e.findIndex((function(e){return e.id==t.id}))-1]},e.querySelector=function(t){var e=this.getChildren();if("string"!=typeof t)return null;var i=e.find((function(e){var i=e.id,n=e.attributes;return i==t||n&&n.uid==t}));return i||(this.parent&&this.parent.querySelector&&this.parent.querySelector(t)||null)},e.getLineRect=function(t,e){var i={width:0,height:0},n=e?e.lines:this.parent&&this.parent.lines;return n&&n.find((function(e){return e.ids.includes(t)}))||i},e.setPosition=function(t,e){var i={left:o,top:s,right:o,bottom:s};Object.keys(i).forEach((function(n){var r=n==u?l:p;[u,g].includes(n)&&void 0!==t.style[n]&&!$(t.originStyle[r])?t.style[r]=e[i[n]]-t.offsetSize[i[n]]-Y(t.style[n],e[i[n]]):t.style[n]=Y(t.style[n],e[i[n]])}))},e.getAttributes=function(t){var e=t.attributes,i=void 0===e?{}:e,n=t.uid,r=t.url,o=t.src,s=t.replace,a=t.text;return n&&(i.uid=n),(r||o)&&(i.src=i.src||r||o),s&&(i.replace=s),a&&(i.text=a),i},e.getOffsetSize=function(t,e,i){void 0===i&&(i=y[3]);var n=e||{},r=n.margin,o=(r=void 0===r?{}:r).marginLeft,s=void 0===o?0:o,a=r.marginTop,h=void 0===a?0:a,c=r.marginRight,f=void 0===c?0:c,d=r.marginBottom,l=void 0===d?0:d,u=n.padding,p=(u=void 0===u?{}:u).paddingLeft,g=void 0===p?0:p,v=u.paddingTop,b=void 0===v?0:v,x=u.paddingRight,w=void 0===x?0:x,m=u.paddingBottom,S=void 0===m?0:m,z=n.border,I=(z=void 0===z?{}:z).borderWidth,M=void 0===I?0:I,k=n.borderTop,B=(k=void 0===k?{}:k).borderTopWidth,W=void 0===B?M:B,P=n.borderBottom,O=(P=void 0===P?{}:P).borderBottomWidth,T=void 0===O?M:O,L=n.borderRight,R=(L=void 0===L?{}:L).borderRightWidth,F=void 0===R?M:R,A=n.borderLeft,E=(A=void 0===A?{}:A).borderLeftWidth,j=void 0===E?M:E,C=s<0&&f<0?Math.abs(s+f):0,H=h<0&&l<0?Math.abs(h+l):0,D=s>=0&&f<0,$=h>=0&&l<0;return i==y[0]&&(this[i].left=t.left+s+g+j+(D?2*-f:0),this[i].top=t.top+h+b+W+($?2*-l:0),this[i].width=t.width+(this[i].widthAdd?0:C),this[i].height=t.height+(this[i].heightAdd?0:H),this[i].widthAdd=C,this[i].heightAdd=H),i==y[1]&&(this[i].left=t.left+s+j+(D<0?-f:0),this[i].top=t.top+h+W+($?-l:0),this[i].width=t.width+g+w,this[i].height=t.height+b+S),i==y[2]&&(this[i].left=t.left+s+j/2+(D<0?-f:0),this[i].top=t.top+h+W/2+($?-l:0),this[i].width=t.width+g+w+j/2+F/2,this[i].height=t.height+b+S+T/2+W/2),i==y[3]&&(this[i].left=t.left+(D<0?-f:0),this[i].top=t.top+($?-l:0),this[i].width=t.width+g+w+j+F+s+f,this[i].height=t.height+b+S+T+W+l+h),this[i]},e.layoutBoxUpdate=function(t,e,i,n){var r=this;if(void 0===i&&(i=-1),"border-box"==e.boxSizing){var o=e||{},s=o.border,a=(s=void 0===s?{}:s).borderWidth,h=void 0===a?0:a,c=o.borderTop,f=(c=void 0===c?{}:c).borderTopWidth,d=void 0===f?h:f,l=o.borderBottom,u=(l=void 0===l?{}:l).borderBottomWidth,p=void 0===u?h:u,g=o.borderRight,v=(g=void 0===g?{}:g).borderRightWidth,b=void 0===v?h:v,x=o.borderLeft,w=(x=void 0===x?{}:x).borderLeftWidth,m=void 0===w?h:w,S=o.padding,z=(S=void 0===S?{}:S).paddingTop,I=void 0===z?0:z,M=S.paddingRight,k=void 0===M?0:M,B=S.paddingBottom,W=void 0===B?0:B,P=S.paddingLeft,O=void 0===P?0:P;i||(t.width-=O+k+b+m),1!==i||n||(t.height-=I+W+d+p)}this.layoutBox&&(y.forEach((function(i){return r.layoutBox[i]=r.getOffsetSize(t,e,i)})),this.layoutBox=Object.assign({},this.layoutBox,this.layoutBox.borderSize))},e.getBoxPosition=function(){var t=this.computedStyle,e=this.fixedLine,i=this.lines,n=t.left,r=void 0===n?0:n,o=t.top,s=void 0===o?0:o,a=V({},this.contentSize,{left:r,top:s}),h=this.contentSize.top-this.offsetSize.top,c=this.contentSize.left-this.offsetSize.left;if(this.root.fixedLine&&!this.root.isDone){this.root.isDone=!0;for(var f,d=Z(this.root.fixedLine.elements);!(f=d()).done;){var l=f.value;l.setPosition(l,this.root.offsetSize),l.getBoxPosition()}}if(e)for(var u,p=Z(e.elements);!(u=p()).done;){var g=u.value,v=V({},this.borderSize,{left:r,top:s});g.setPosition(g,v);var y=this.borderSize.top-this.offsetSize.top,b=this.borderSize.left-this.offsetSize.left;g.style.left+=r+b,g.style.top+=s+y,g.getBoxPosition()}if(i)for(var x,w=Z(i);!(x=w()).done;){x.value.layout(a.top+h,a.left+c)}return this.layoutBoxUpdate(a,t),this.layoutBox},e.getBoxState=function(t,e){return this.isBlock(t)||this.isBlock(e)},e.isBlock=function(t){return void 0===t&&(t=this),t&&t.style.display==zt},e.isFlex=function(t){return void 0===t&&(t=this),t&&t.style.display==Mt},e.isInFlow=function(){return!(this.isAbsolute||this.isFixed)},e.inFlexBox=function(t){return void 0===t&&(t=this),!!t.isInFlow()&&(!!t.parent&&(!(!t.parent||t.parent.style.display!==Mt)||void 0))},e.isInline=function(t){return void 0===t&&(t=this),t&&t.style.display==It},e.contrastSize=function(t,e,i){var n=t;return i&&(n=Math.min(n,i)),e&&(n=Math.max(n,e)),n},e.measureText=function(t,e){var i=this.ctx.measureText(t),n=i.width,r=i.actualBoundingBoxAscent,o=i.actualBoundingBoxDescent;return{ascent:r,descent:o,width:n,fontHeight:r+o||.7*e+1}},e.getParentSize=function(t,e){if(void 0===t&&(t=this),void 0===e&&(e=!1),t&&t.parent){if(t.parent.contentSize.width)return t.parent.contentSize;if(e)return this.getParentSize(t.parent,e)}return null},e.getBoxWidthHeight=function(){var t=this,e=this.name,i=this.computedStyle,n=this.attributes,r=this.parent,o=void 0===r?{}:r,s=this.ctx,a=this.getChildren(),h=i.left,c=void 0===h?0:h,f=i.top,d=void 0===f?0:f,l=i.bottom,u=i.right,p=i.width,g=void 0===p?0:p,v=i.minWidth,y=i.maxWidth,b=i.minHeight,x=i.maxHeight,w=i.height,m=void 0===w?0:w,S=i.fontSize,z=i.fontWeight,I=i.fontFamily,M=i.fontStyle,k=i.position;i.textIndent;var B=i.lineClamp,W=i.lineHeight,P=i.padding,O=void 0===P?{}:P,T=i.margin,L=void 0===T?{}:T,R=i.border,F=(R=void 0===R?{}:R).borderWidth,A=void 0===F?0:F,E=i.borderRight,j=(E=void 0===E?{}:E).borderRightWidth,C=void 0===j?A:j,H=i.borderLeft,D=(H=void 0===H?{}:H).borderLeftWidth,$=void 0===D?A:D,U=o.contentSize&&o.contentSize.width,N=o.contentSize&&o.contentSize.height;if(it(g)&&U&&(g=Y(g,U)),it(g)&&!U&&(g=null),it(m)&&N&&(m=Y(m,N)),it(m)&&!N&&(m=null),it(v)&&U&&(v=Y(v,U)),it(y)&&U&&(y=Y(y,U)),it(b)&&N&&(b=Y(b,N)),it(x)&&N&&(x=Y(x,N)),i.padding&&U)for(var _ in i.padding)Object.hasOwnProperty.call(O,_)&&(O[_]=Y(O[_],U));var X=O.paddingRight,q=void 0===X?0:X,G=O.paddingLeft,V=void 0===G?0:G;if(i.margin&&[L.marginLeft,L.marginRight].includes("auto"))if(g){var J=U&&U-g-q-V-$-C||0;L.marginLeft==L.marginRight?L.marginLeft=L.marginRight=J/2:et(L.marginLeft)?L.marginLeft=J:L.marginRight=J}else L.marginLeft=L.marginRight=0;var Q=L.marginRight,Z=void 0===Q?0:Q,K=L.marginLeft,tt={width:g,height:m,left:0,top:0},nt=V+q+$+C+(void 0===K?0:K)+Z;if(this.offsetWidth=nt,e==mt&&!this.attributes.widths){var rt=n.text||"";s.save(),s.setFonts({fontFamily:I,fontSize:S,fontWeight:z,fontStyle:M}),rt.length,"\n"==rt&&(rt="",this.isBr=!0),(""+rt).split("\n").map((function(e){var i=Array.from(e).map((function(e){var i=""+(/^[\u4e00-\u9fa5]+$/.test(e)?"cn":e)+I+S+z+M,n=Ot.get(i);if(n)return{width:n,text:e};var r=t.measureText(e,S).width;return Ot.set(i,r),{width:r,text:e}})),n=t.measureText(e,S),r=n.fontHeight,o=n.ascent,s=n.descent;t.attributes.fontHeight=r,t.attributes.ascent=o,t.attributes.descent=s,t.attributes.widths||(t.attributes.widths=[]),t.attributes.widths.push({widths:i,total:i.reduce((function(t,e){return t+e.width}),0)})})),s.restore()}if(e==wt&&null==g){var ot=n.width,st=n.height;tt.width=this.contrastSize(Math.round(ot*m/st)||0,v,y),this.layoutBoxUpdate(tt,i,0)}if(e==mt&&null==g){var at=this.attributes.widths,ht=Math.max.apply(Math,at.map((function(t){return t.total})));if(o&&U>0&&(ht>U||this.isBlock(this))&&!this.isAbsolute&&!this.isFixed)ht=U;tt.width=this.contrastSize(ht,v,y),this.layoutBoxUpdate(tt,i,0)}if(e==mt&&(o.style.flex||!this.attributes.lines)){var ct=this.attributes.widths.length;this.attributes.widths.forEach((function(t){return t.widths.reduce((function(t,e,i){return t+e.width>tt.width?(ct++,e.width):t+e.width}),0)})),ct=B&&ct>B?B:ct,this.attributes.lines=ct}if(e==wt&&null==m){var ft=n.width,dt=n.height;n.text,tt.height=this.contrastSize(Y(tt.width*dt/ft)||0,b,x),this.layoutBoxUpdate(tt,i,1)}e==mt&&null==m&&(W=Y(W,S),tt.height=this.contrastSize(Y(this.attributes.lines*W),b,x),this.layoutBoxUpdate(tt,i,1,!0)),!g&&o&&o.children&&U&&(!this.isFlex(o)||o.isFlexCalc)&&([St,mt].includes(e)&&this.isFlex()||e==St&&this.isBlock(this)&&this.isInFlow())&&(tt.width=this.contrastSize(U-(o.isFlexCalc?0:nt),v,y),this.layoutBoxUpdate(tt,i)),g&&!it(g)&&(tt.width=this.contrastSize(g,v,y),this.layoutBoxUpdate(tt,i,0)),m&&!it(m)&&(tt.height=this.contrastSize(tt.height,b,x),this.layoutBoxUpdate(tt,i,1));var lt=0;if(a.length){var ut=null,pt=!1;a.forEach((function(e,n){e.getBoxWidthHeight();var r=a[n+1];if(r&&r.isInFlow()&&(e.next=r),!t.line||!t.line.ids.includes(e.id))if(e.isInFlow()&&!e.inFlexBox()){var o=t.getBoxState(ut,e);if(e.isBr)return pt=!0;t.line&&t.line.canIEnter(e)&&!o&&!pt?t.line.add(e):(pt=!1,(new gt).bind(e)),ut=e}else e.inFlexBox()?t.line&&(t.line.canIEnter(e)||"nowrap"==i.flexWrap)?t.line.add(e):(new xt).bind(e):e.isFixed?t.root.fixedLine?t.root.fixedLine.fixedAdd(e):(new gt).fixedBind(e):t.fixedLine?t.fixedLine.fixedAdd(e):(new gt).fixedBind(e,1)})),this.lines&&(lt=this.lines.reduce((function(t,e){return t+e.height}),0))}var vt=0,yt=0;if(!g&&(this.isAbsolute||this.isFixed)&&U){var bt=k==kt?U:this.root.width,zt=bt-(it(c)?Y(c,bt):c)-(it(u)?Y(u,bt):u);vt=i.left?zt:this.lineMaxWidth}if(!m&&(null!=d?d:this.isAbsolute||this.isFixed&&N)){var It=k==kt?N:this.root.height,Mt=It-(it(d)?Y(d,It):d)-(it(l)?Y(l,It):l);yt=i.top?Mt:0}if(g&&!it(g)||tt.width||(tt.width=vt||this.contrastSize((this.isBlock(this)&&!this.isInFlow()?U||o.lineMaxWidth:this.lineMaxWidth)||this.lineMaxWidth,v,y),this.layoutBoxUpdate(tt,i,0)),m||!lt&&!yt||(tt.height=yt||this.contrastSize(lt,b,x),this.layoutBoxUpdate(tt,i)),i.borderRadius&&this.borderSize&&this.borderSize.width)for(var _ in i.borderRadius)Object.hasOwnProperty.call(i.borderRadius,_)&&(i.borderRadius[_]=Y(i.borderRadius[_],this.borderSize.width));return this.layoutBox},e.layout=function(){return this.getBoxWidthHeight(),this.root.offsetSize=this.offsetSize,this.root.contentSize=this.contentSize,this.getBoxPosition(),this.offsetSize},t}(),Lt=function(){var t,e,i,n,r,o,s=[0,11,15,19,23,27,31,16,18,20,22,24,26,28,20,22,24,24,26,28,28,22,24,24,26,26,28,28,24,24,26,26,26,28,28,24,26,26,26,28,28],a=[3220,1468,2713,1235,3062,1890,2119,1549,2344,2936,1117,2583,1330,2470,1667,2249,2028,3780,481,4011,142,3098,831,3445,592,2517,1776,2234,1951,2827,1070,2660,1345,3177],h=[30660,29427,32170,30877,26159,25368,27713,26998,21522,20773,24188,23371,17913,16590,20375,19104,13663,12392,16177,14854,9396,8579,11994,11245,5769,5054,7399,6608,1890,597,3340,2107],c=[1,0,19,7,1,0,16,10,1,0,13,13,1,0,9,17,1,0,34,10,1,0,28,16,1,0,22,22,1,0,16,28,1,0,55,15,1,0,44,26,2,0,17,18,2,0,13,22,1,0,80,20,2,0,32,18,2,0,24,26,4,0,9,16,1,0,108,26,2,0,43,24,2,2,15,18,2,2,11,22,2,0,68,18,4,0,27,16,4,0,19,24,4,0,15,28,2,0,78,20,4,0,31,18,2,4,14,18,4,1,13,26,2,0,97,24,2,2,38,22,4,2,18,22,4,2,14,26,2,0,116,30,3,2,36,22,4,4,16,20,4,4,12,24,2,2,68,18,4,1,43,26,6,2,19,24,6,2,15,28,4,0,81,20,1,4,50,30,4,4,22,28,3,8,12,24,2,2,92,24,6,2,36,22,4,6,20,26,7,4,14,28,4,0,107,26,8,1,37,22,8,4,20,24,12,4,11,22,3,1,115,30,4,5,40,24,11,5,16,20,11,5,12,24,5,1,87,22,5,5,41,24,5,7,24,30,11,7,12,24,5,1,98,24,7,3,45,28,15,2,19,24,3,13,15,30,1,5,107,28,10,1,46,28,1,15,22,28,2,17,14,28,5,1,120,30,9,4,43,26,17,1,22,28,2,19,14,28,3,4,113,28,3,11,44,26,17,4,21,26,9,16,13,26,3,5,107,28,3,13,41,26,15,5,24,30,15,10,15,28,4,4,116,28,17,0,42,26,17,6,22,28,19,6,16,30,2,7,111,28,17,0,46,28,7,16,24,30,34,0,13,24,4,5,121,30,4,14,47,28,11,14,24,30,16,14,15,30,6,4,117,30,6,14,45,28,11,16,24,30,30,2,16,30,8,4,106,26,8,13,47,28,7,22,24,30,22,13,15,30,10,2,114,28,19,4,46,28,28,6,22,28,33,4,16,30,8,4,122,30,22,3,45,28,8,26,23,30,12,28,15,30,3,10,117,30,3,23,45,28,4,31,24,30,11,31,15,30,7,7,116,30,21,7,45,28,1,37,23,30,19,26,15,30,5,10,115,30,19,10,47,28,15,25,24,30,23,25,15,30,13,3,115,30,2,29,46,28,42,1,24,30,23,28,15,30,17,0,115,30,10,23,46,28,10,35,24,30,19,35,15,30,17,1,115,30,14,21,46,28,29,19,24,30,11,46,15,30,13,6,115,30,14,23,46,28,44,7,24,30,59,1,16,30,12,7,121,30,12,26,47,28,39,14,24,30,22,41,15,30,6,14,121,30,6,34,47,28,46,10,24,30,2,64,15,30,17,4,122,30,29,14,46,28,49,10,24,30,24,46,15,30,4,18,122,30,13,32,46,28,48,14,24,30,42,32,15,30,20,4,117,30,40,7,47,28,43,22,24,30,10,67,15,30,19,6,118,30,18,31,47,28,34,34,24,30,20,61,15,30],f=[255,0,1,25,2,50,26,198,3,223,51,238,27,104,199,75,4,100,224,14,52,141,239,129,28,193,105,248,200,8,76,113,5,138,101,47,225,36,15,33,53,147,142,218,240,18,130,69,29,181,194,125,106,39,249,185,201,154,9,120,77,228,114,166,6,191,139,98,102,221,48,253,226,152,37,179,16,145,34,136,54,208,148,206,143,150,219,189,241,210,19,92,131,56,70,64,30,66,182,163,195,72,126,110,107,58,40,84,250,133,186,61,202,94,155,159,10,21,121,43,78,212,229,172,115,243,167,87,7,112,192,247,140,128,99,13,103,74,222,237,49,197,254,24,227,165,153,119,38,184,180,124,17,68,146,217,35,32,137,46,55,63,209,91,149,188,207,205,144,135,151,178,220,252,190,97,242,86,211,171,20,42,93,158,132,60,57,83,71,109,65,162,31,45,67,216,183,123,164,118,196,23,73,236,127,12,111,246,108,161,59,82,41,157,85,170,251,96,134,177,187,204,62,90,203,89,95,176,156,169,160,81,11,245,22,235,122,117,44,215,79,174,213,233,230,231,173,232,116,214,244,234,168,80,88,175],d=[1,2,4,8,16,32,64,128,29,58,116,232,205,135,19,38,76,152,45,90,180,117,234,201,143,3,6,12,24,48,96,192,157,39,78,156,37,74,148,53,106,212,181,119,238,193,159,35,70,140,5,10,20,40,80,160,93,186,105,210,185,111,222,161,95,190,97,194,153,47,94,188,101,202,137,15,30,60,120,240,253,231,211,187,107,214,177,127,254,225,223,163,91,182,113,226,217,175,67,134,17,34,68,136,13,26,52,104,208,189,103,206,129,31,62,124,248,237,199,147,59,118,236,197,151,51,102,204,133,23,46,92,184,109,218,169,79,158,33,66,132,21,42,84,168,77,154,41,82,164,85,170,73,146,57,114,228,213,183,115,230,209,191,99,198,145,63,126,252,229,215,179,123,246,241,255,227,219,171,75,150,49,98,196,149,55,110,220,165,87,174,65,130,25,50,100,200,141,7,14,28,56,112,224,221,167,83,166,81,162,89,178,121,242,249,239,195,155,43,86,172,69,138,9,18,36,72,144,61,122,244,245,247,243,251,235,203,139,11,22,44,88,176,125,250,233,207,131,27,54,108,216,173,71,142,0],l=[],u=[],p=[],g=[],v=[],y=2;function b(t,e){var i;t>e&&(i=t,t=e,e=i),i=e,i*=e,i+=e,i>>=1,g[i+=t]=1}function x(t,i){var n;for(p[t+e*i]=1,n=-2;n<2;n++)p[t+n+e*(i-2)]=1,p[t-2+e*(i+n+1)]=1,p[t+2+e*(i+n)]=1,p[t+n+1+e*(i+2)]=1;for(n=0;n<2;n++)b(t-1,i+n),b(t+1,i-n),b(t-n,i-1),b(t+n,i+1)}function w(t){for(;t>=255;)t=((t-=255)>>8)+(255&t);return t}var m=[];function S(t,e,i,n){var r,o,s;for(r=0;r<n;r++)l[i+r]=0;for(r=0;r<e;r++){if(255!=(s=f[l[t+r]^l[i]]))for(o=1;o<n;o++)l[i+o-1]=l[i+o]^d[w(s+m[n-o])];else for(o=i;o<i+n;o++)l[o]=l[o+1];l[i+n-1]=255==s?0:d[w(s+m[0])]}}function z(t,e){var i;return t>e&&(i=t,t=e,e=i),i=e,i+=e*e,i>>=1,g[i+=t]}function I(t){var i,n,r,o;switch(t){case 0:for(n=0;n<e;n++)for(i=0;i<e;i++)i+n&1||z(i,n)||(p[i+n*e]^=1);break;case 1:for(n=0;n<e;n++)for(i=0;i<e;i++)1&n||z(i,n)||(p[i+n*e]^=1);break;case 2:for(n=0;n<e;n++)for(r=0,i=0;i<e;i++,r++)3==r&&(r=0),r||z(i,n)||(p[i+n*e]^=1);break;case 3:for(o=0,n=0;n<e;n++,o++)for(3==o&&(o=0),r=o,i=0;i<e;i++,r++)3==r&&(r=0),r||z(i,n)||(p[i+n*e]^=1);break;case 4:for(n=0;n<e;n++)for(r=0,o=n>>1&1,i=0;i<e;i++,r++)3==r&&(r=0,o=!o),o||z(i,n)||(p[i+n*e]^=1);break;case 5:for(o=0,n=0;n<e;n++,o++)for(3==o&&(o=0),r=0,i=0;i<e;i++,r++)3==r&&(r=0),(i&n&1)+!(!r|!o)||z(i,n)||(p[i+n*e]^=1);break;case 6:for(o=0,n=0;n<e;n++,o++)for(3==o&&(o=0),r=0,i=0;i<e;i++,r++)3==r&&(r=0),(i&n&1)+(r&&r==o)&1||z(i,n)||(p[i+n*e]^=1);break;case 7:for(o=0,n=0;n<e;n++,o++)for(3==o&&(o=0),r=0,i=0;i<e;i++,r++)3==r&&(r=0),(r&&r==o)+(i+n&1)&1||z(i,n)||(p[i+n*e]^=1)}}function M(t){var e,i=0;for(e=0;e<=t;e++)v[e]>=5&&(i+=3+v[e]-5);for(e=3;e<t-1;e+=2)v[e-2]==v[e+2]&&v[e+2]==v[e-1]&&v[e-1]==v[e+1]&&3*v[e-1]==v[e]&&(0==v[e-3]||e+3>t||3*v[e-3]>=4*v[e]||3*v[e+3]>=4*v[e])&&(i+=40);return i}function k(){var t,i,n,r,o,s=0,a=0;for(i=0;i<e-1;i++)for(t=0;t<e-1;t++)(p[t+e*i]&&p[t+1+e*i]&&p[t+e*(i+1)]&&p[t+1+e*(i+1)]||!(p[t+e*i]||p[t+1+e*i]||p[t+e*(i+1)]||p[t+1+e*(i+1)]))&&(s+=3);for(i=0;i<e;i++){for(v[0]=0,n=r=t=0;t<e;t++)(o=p[t+e*i])==r?v[n]++:v[++n]=1,a+=(r=o)?1:-1;s+=M(n)}a<0&&(a=-a);var h=a,c=0;for(h+=h<<2,h<<=1;h>e*e;)h-=e*e,c++;for(s+=10*c,t=0;t<e;t++){for(v[0]=0,n=r=i=0;i<e;i++)(o=p[t+e*i])==r?v[n]++:v[++n]=1,r=o;s+=M(n)}return s}var B=null;return{api:{get ecclevel(){return y},set ecclevel(t){y=t},get size(){return _size},set size(t){_size=t},get canvas(){return B},set canvas(t){B=t},getFrame:function(v){return function(v){var M,B,W,P,O,T,L,R;P=v.length,t=0;do{if(t++,W=4*(y-1)+16*(t-1),i=c[W++],n=c[W++],r=c[W++],o=c[W],P<=(W=r*(i+n)+n-3+(t<=9)))break}while(t<40);for(e=17+4*t,O=r+(r+o)*(i+n)+n,P=0;P<O;P++)u[P]=0;for(l=v.slice(0),P=0;P<e*e;P++)p[P]=0;for(P=0;P<(e*(e+1)+1)/2;P++)g[P]=0;for(P=0;P<3;P++){for(W=0,B=0,1==P&&(W=e-7),2==P&&(B=e-7),p[B+3+e*(W+3)]=1,M=0;M<6;M++)p[B+M+e*W]=1,p[B+e*(W+M+1)]=1,p[B+6+e*(W+M)]=1,p[B+M+1+e*(W+6)]=1;for(M=1;M<5;M++)b(B+M,W+1),b(B+1,W+M+1),b(B+5,W+M),b(B+M+1,W+5);for(M=2;M<4;M++)p[B+M+e*(W+2)]=1,p[B+2+e*(W+M+1)]=1,p[B+4+e*(W+M)]=1,p[B+M+1+e*(W+4)]=1}if(t>1)for(P=s[t],B=e-7;;){for(M=e-7;M>P-3&&(x(M,B),!(M<P));)M-=P;if(B<=P+9)break;x(6,B-=P),x(B,6)}for(p[8+e*(e-8)]=1,B=0;B<7;B++)b(7,B),b(e-8,B),b(7,B+e-7);for(M=0;M<8;M++)b(M,7),b(M+e-8,7),b(M,e-8);for(M=0;M<9;M++)b(M,8);for(M=0;M<8;M++)b(M+e-8,8),b(8,M);for(B=0;B<7;B++)b(8,B+e-7);for(M=0;M<e-14;M++)1&M?(b(8+M,6),b(6,8+M)):(p[8+M+6*e]=1,p[6+e*(8+M)]=1);if(t>6)for(P=a[t-7],W=17,M=0;M<6;M++)for(B=0;B<3;B++,W--)1&(W>11?t>>W-12:P>>W)?(p[5-M+e*(2-B+e-11)]=1,p[2-B+e-11+e*(5-M)]=1):(b(5-M,2-B+e-11),b(2-B+e-11,5-M));for(B=0;B<e;B++)for(M=0;M<=B;M++)p[M+e*B]&&b(M,B);for(O=l.length,T=0;T<O;T++)u[T]=l.charCodeAt(T);if(l=u.slice(0),O>=(M=r*(i+n)+n)-2&&(O=M-2,t>9&&O--),T=O,t>9){for(l[T+2]=0,l[T+3]=0;T--;)P=l[T],l[T+3]|=255&P<<4,l[T+2]=P>>4;l[2]|=255&O<<4,l[1]=O>>4,l[0]=64|O>>12}else{for(l[T+1]=0,l[T+2]=0;T--;)P=l[T],l[T+2]|=255&P<<4,l[T+1]=P>>4;l[1]|=255&O<<4,l[0]=64|O>>4}for(T=O+3-(t<10);T<M;)l[T++]=236,l[T++]=17;for(m[0]=1,T=0;T<o;T++){for(m[T+1]=1,L=T;L>0;L--)m[L]=m[L]?m[L-1]^d[w(f[m[L]]+T)]:m[L-1];m[0]=d[w(f[m[0]]+T)]}for(T=0;T<=o;T++)m[T]=f[m[T]];for(W=M,B=0,T=0;T<i;T++)S(B,r,W,o),B+=r,W+=o;for(T=0;T<n;T++)S(B,r+1,W,o),B+=r+1,W+=o;for(B=0,T=0;T<r;T++){for(L=0;L<i;L++)u[B++]=l[T+L*r];for(L=0;L<n;L++)u[B++]=l[i*r+T+L*(r+1)]}for(L=0;L<n;L++)u[B++]=l[i*r+T+L*(r+1)];for(T=0;T<o;T++)for(L=0;L<i+n;L++)u[B++]=l[M+T+L*o];for(l=u,M=B=e-1,W=O=1,R=(r+o)*(i+n)+n,T=0;T<R;T++)for(P=l[T],L=0;L<8;L++,P<<=1){128&P&&(p[M+e*B]=1);do{O?M--:(M++,W?0!=B?B--:(W=!W,6==(M-=2)&&(M--,B=9)):B!=e-1?B++:(W=!W,6==(M-=2)&&(M--,B-=8))),O=!O}while(z(M,B))}for(l=p.slice(0),P=0,B=3e4,W=0;W<8&&(I(W),(M=k())<B&&(B=M,P=W),7!=P);W++)p=l.slice(0);for(P!=W&&I(P),B=h[P+(y-1<<3)],W=0;W<8;W++,B>>=1)1&B&&(p[e-1-W+8*e]=1,W<6?p[8+e*W]=1:p[8+e*(W+1)]=1);for(W=0;W<7;W++,B>>=1)1&B&&(p[8+e*(e-7+W)]=1,W?p[6-W+8*e]=1:p[7+8*e]=1);return p}(v)},utf16to8:function(t){var e,i,n,r;for(e="",n=t.length,i=0;i<n;i++)(r=t.charCodeAt(i))>=1&&r<=127?e+=t.charAt(i):r>2047?(e+=String.fromCharCode(224|r>>12&15),e+=String.fromCharCode(128|r>>6&63),e+=String.fromCharCode(128|r>>0&63)):(e+=String.fromCharCode(192|r>>6&31),e+=String.fromCharCode(128|r>>0&63));return e},draw:function(t,i,n,r,o){i.drawView(n,r);var s=i.ctx,a=n.contentSize,h=a.width,c=a.height,f=a.left,d=a.top;r.borderRadius,r.backgroundColor;var l=r.color,u=void 0===l?"#000000":l;r.border,n.contentSize.left,n.borderSize.left,n.contentSize.top,n.borderSize.top;if(y=o||y,s){s.save(),i.setOpacity(r),i.setTransform(n,r);var p=Math.min(h,c);t=this.utf16to8(t);var g=this.getFrame(t),v=p/e;s.setFillStyle(u);for(var b=0;b<e;b++)for(var x=0;x<e;x++)g[x*e+b]&&s.fillRect(f+v*b,d+v*x,v,v);s.restore(),i.setBorder(n,r)}else console.warn("No canvas provided to draw QR code in!")}}}}(),Rt=I,Ft=z,At=M,Et=S,jt=w.TOP,Ct=w.MIDDLE,Ht=w.BOTTOM,Dt=m.LEFT,$t=m.CENTER,Yt=m.RIGHT,Ut=function(){function t(t){var e,i,n=this;this.v="1.9.5.1",this.id=null,this.pixelRatio=1,this.width=0,this.height=0,this.sleep=1e3/30,this.count=0,this.isRate=!1,this.isDraw=!0,this.isCache=!0,this.fixed="",this.useCORS=!1,this.performance=!1,this.imageBus=[],this.createImage=function(t,e){return new Promise((function(i,r){var o=null;window||n.canvas.createImage?(o=n.canvas&&n.canvas.createImage?n.canvas.createImage():new Image,e&&o.setAttribute("crossOrigin","Anonymous"),o.src=t,o.onload=function(){i({width:o.naturalWidth||o.width,height:o.naturalHeight||o.height,path:o,src:this.src})},o.onerror=function(t){r(t)}):r({fail:"getImageInfo fail",src:t})}))},this.options=t,Object.assign(this,t),this.ctx=(e=t.context,i={get:function(t,i){if("setFonts"===i)return function(t){var i=t.fontFamily,n=void 0===i?"sans-serif":i,o=t.fontSize,s=void 0===o?14:o,a=t.fontWeight,h=void 0===a?"normal":a,c=t.fontStyle,f=void 0===c?"normal":c;C==r.MP_TOUTIAO&&(h="bold"==h?"bold":"",f="italic"==f?"italic":""),e.font="".concat(f," ").concat(h," ").concat(Math.round(s),"px ").concat(n)};if(!e.draw||!e.setFillStyle){if("setFillStyle"===i)return function(t){e.fillStyle=t};if("setStrokeStyle"===i)return function(t){e.strokeStyle=t};if("setLineWidth"===i)return function(t){e.lineWidth=t};if("setLineCap"===i)return function(t){e.lineCap=t};if("setFontSize"===i)return function(t){e.font="".concat(String(t),"px sans-serif")};if("setGlobalAlpha"===i)return function(t){e.globalAlpha=t};if("setLineJoin"===i)return function(t){e.lineJoin=t};if("setTextAlign"===i)return function(t){e.textAlign=t};if("setMiterLimit"===i)return function(t){e.miterLimit=t};if("setShadow"===i)return function(t,i,n,r){e.shadowOffsetX=t,e.shadowOffsetY=i,e.shadowBlur=n,e.shadowColor=r};if("setTextBaseline"===i)return function(t){e.textBaseline=t};if("createCircularGradient"===i)return function(){};if("draw"===i)return function(){};if("function"==typeof e[i])return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return e[i].apply(e,t)}}return t[i]},set:function(t,i,n){return e[i]=n,!0}},new Proxy(e,i)),this.progress=0,this.root={width:t.width,height:t.height,fontSizeRate:1,fixedLine:null},this.size=this.root;var o=0;Object.defineProperty(this,"progress",{configurable:!0,set:function(t){o=t,n.lifecycle("onProgress",t/n.count)},get:function(){return o||0}})}return t.prototype.lifecycle=function(t,e){this.options.listen&&this.options.listen[t]&&this.options.listen[t](e)},t.prototype.setContext=function(t){t&&(this.ctx=t)},t.prototype.init=function(){if(this.canvas.height||r.WEB==C){this.ctx.setTransform(1,0,0,1,0,0);var t=this.size.height*this.pixelRatio,e=this.size.width*this.pixelRatio;this.canvas.height=t,this.canvas.width=e,this.ctx.scale(this.pixelRatio,this.pixelRatio)}},t.prototype.clear=function(){this.ctx.clearRect(0,0,this.size.width,this.size.height)},t.prototype.clipPath=function(t,e,i,n,r,o,s){void 0===o&&(o=!1),void 0===s&&(s=!1);var a=this.ctx;if(/polygon/.test(r)){var h=r.match(/-?\d+(rpx|px|%)?\s+-?\d+(rpx|px|%)?/g)||[];a.beginPath(),h.map((function(r){var o=r.split(" "),s=o[0],a=o[1];return[Y(s,i)+t,Y(a,n)+e]})).forEach((function(t,e){0==e?a.moveTo(t[0],t[1]):a.lineTo(t[0],t[1])})),a.closePath(),s&&a.stroke(),o&&a.fill()}},t.prototype.roundRect=function(t,e,i,n,r,o,s){if(void 0===o&&(o=!1),void 0===s&&(s=!1),!(r<0)){var a=this.ctx;if(a.beginPath(),r){var h=r||{},c=h.borderTopLeftRadius,f=void 0===c?r||0:c,d=h.borderTopRightRadius,l=void 0===d?r||0:d,u=h.borderBottomRightRadius,p=void 0===u?r||0:u,g=h.borderBottomLeftRadius,v=void 0===g?r||0:g;a.arc(t+i-p,e+n-p,p,0,.5*Math.PI),a.lineTo(t+v,e+n),a.arc(t+v,e+n-v,v,.5*Math.PI,Math.PI),a.lineTo(t,e+f),a.arc(t+f,e+f,f,Math.PI,1.5*Math.PI),a.lineTo(t+i-l,e),a.arc(t+i-l,e+l,l,1.5*Math.PI,2*Math.PI),a.lineTo(t+i,e+n-p)}else a.rect(t,e,i,n);a.closePath(),s&&a.stroke(),o&&a.fill()}},t.prototype.setTransform=function(t,e){var i=e.transform,n=e.transformOrigin,r=this.ctx,o=i||{},s=o.scaleX,a=void 0===s?1:s,h=o.scaleY,c=void 0===h?1:h,f=o.translateX,d=void 0===f?0:f,l=o.translateY,u=void 0===l?0:l,p=o.rotate,g=void 0===p?0:p,v=o.skewX,y=void 0===v?0:v,b=o.skewY,x=void 0===b?0:b,w=t.left,m=t.top,S=t.width,z=t.height;d=Y(d,S)||0,u=Y(u,z)||0;var I=Y("0%",1),M=Y("50%",1),k=Y("100%",1),B={top:I,center:M,bottom:k},W={left:I,center:M,right:k};if(n=n.split(" ").filter((function(t,e){return e<2})).reduce((function(t,e){if(/\d+/.test(e)){var i=Y(e,1)/(/px|rpx$/.test(e)?$(t.x)?z:S:1);return $(t.x)?Object.assign(t,{y:i}):Object.assign(t,{x:i})}return $(W[e])&&!$(t.x)?Object.assign(t,{x:W[e]}):Object.assign(t,{y:B[e]||.5})}),{}),(d||u)&&r.translate(d,u),(a||c)&&r.scale(a,c),g){var P=w+S*n.x,O=m+z*n.y;r.translate(P,O),r.rotate(g*Math.PI/180),r.translate(-P,-O)}(y||x)&&r.transform(1,Math.tan(x*Math.PI/180),Math.tan(y*Math.PI/180),1,0,0)},t.prototype.setBackground=function(t,e,i,n,o){var s=this.ctx;t&&t!=L?_(t)?X(t,e,i,n,o,s):s.setFillStyle(t):[r.MP_TOUTIAO,r.MP_BAIDU].includes(C)?s.setFillStyle("rgba(0,0,0,0)"):s.setFillStyle(L)},t.prototype.setShadow=function(t){var e=t.boxShadow,i=void 0===e?[]:e,n=this.ctx;if(i.length){var r=i[0],o=i[1],s=i[2],a=i[3];n.setShadow(r,o,s,a)}},t.prototype.setBorder=function(t,e){var i=this.ctx,n=t.width,r=t.height,o=t.left,s=t.top,a=e.border,h=e.borderBottom,c=e.borderTop,f=e.borderRight,d=e.borderLeft,l=e.borderRadius,u=e.lineCap,p=a||{},g=p.borderWidth,v=void 0===g?0:g,y=p.borderStyle,b=p.borderColor,x=h||{},w=x.borderBottomWidth,m=void 0===w?v:w,S=x.borderBottomStyle,z=void 0===S?y:S,I=x.borderBottomColor,M=void 0===I?b:I,k=c||{},B=k.borderTopWidth,W=void 0===B?v:B,P=k.borderTopStyle,O=void 0===P?y:P,T=k.borderTopColor,L=void 0===T?b:T,R=f||{},F=R.borderRightWidth,A=void 0===F?v:F,E=R.borderRightStyle,j=void 0===E?y:E,H=R.borderRightColor,D=void 0===H?b:H,$=d||{},Y=$.borderLeftWidth,U=void 0===Y?v:Y,N=$.borderLeftStyle,_=void 0===N?y:N,X=$.borderLeftColor,q=void 0===X?b:X,G=l||{},V=G.borderTopLeftRadius,J=void 0===V?l||0:V,Q=G.borderTopRightRadius,Z=void 0===Q?l||0:Q,K=G.borderBottomRightRadius,tt=void 0===K?l||0:K,et=G.borderBottomLeftRadius,it=void 0===et?l||0:et;if(h||d||c||f||a){var nt=function(t,e,n){"dashed"==e?/mp/.test(C)?i.setLineDash([Math.ceil(4*t/3),Math.ceil(4*t/3)]):i.setLineDash([Math.ceil(6*t),Math.ceil(6*t)]):"dotted"==e&&i.setLineDash([t,t]),i.setStrokeStyle(n)},rt=function(t,e,n,r,o,s,a,h,c,f,d,l,p,g,v){i.save(),i.setLineCap(v?"square":u),i.setLineWidth(l),nt(l,p,g),i.beginPath(),i.arc(t,e,a,Math.PI*c,Math.PI*f),i.lineTo(n,r),i.arc(o,s,h,Math.PI*f,Math.PI*d),i.stroke(),i.restore()};if(i.save(),a&&!h&&!d&&!c&&!f)return i.setLineWidth(v),nt(v,y,b),this.roundRect(o,s,n,r,l,!1,!!b),void i.restore();m&&rt(o+n-tt,s+r-tt,o+it,s+r,o+it,s+r-it,tt,it,.25,.5,.75,m,z,M,U&&A),U&&rt(o+it,s+r-it,o,s+J,o+J,s+J,it,J,.75,1,1.25,U,_,q,W&&m),W&&rt(o+J,s+J,o+n-Z,s,o+n-Z,s+Z,J,Z,1.25,1.5,1.75,W,O,L,U&&A),A&&rt(o+n-Z,s+Z,o+n,s+r-tt,o+n-tt,s+r-tt,Z,tt,1.75,2,.25,A,j,D,W&&m)}},t.prototype.setOpacity=function(t){var e=t.opacity,i=void 0===e?1:e;this.ctx.setGlobalAlpha(i)},t.prototype.drawPattern=function(t,e,r){return i(this,void 0,void 0,(function(){var i=this;return n(this,(function(n){return[2,new Promise((function(n,o){i.drawView(e,r,!0,!1,!0);var s=i,a=s.ctx;s.canvas;var h=e.width,c=e.height,f=e.left,d=e.top,l=r||{},u=l.borderRadius,p=void 0===u?0:u,g=l.backgroundImage,v=l.backgroundRepeat,y=void 0===v?"repeat":v;g&&function(t){var o=a.createPattern(t.src,y);a.setFillStyle(o),i.roundRect(f,d,h,c,p,!0,!1),i.setBorder(e,r),n()}(t)}))]}))}))},t.prototype.drawView=function(t,e,i,n,r){void 0===i&&(i=!0),void 0===n&&(n=!0),void 0===r&&(r=!0);var o=this.ctx,s=t.width,a=t.height,h=t.left,c=t.top,f=e||{},d=f.borderRadius,l=void 0===d?0:d,u=f.backgroundColor,p=void 0===u?L:u,g=f.overflow;e.opacity&&this.setOpacity(e),this.setTransform(t,e),r&&(o.save(),this.setShadow(e)),i&&this.setBackground(p,s,a,h,c),e.clipPath?this.clipPath(h,c,s,a,e.clipPath,i,!1):this.roundRect(h,c,s,a,l,i,!1),r&&o.restore(),n&&this.setBorder(t,e),"hidden"==g&&o.clip()},t.prototype.drawImage=function(t,e,o,s){return void 0===e&&(e={}),void 0===o&&(o={}),void 0===s&&(s=!0),i(this,void 0,void 0,(function(){var a=this;return n(this,(function(h){switch(h.label){case 0:return[4,new Promise((function(h,c){return i(a,void 0,void 0,(function(){var i,a,c,f,d,l,u,p,g,v,y,b,x,w,m,S,z,I,M,k,B,W=this;return n(this,(function(n){return i=this.ctx,a=o.borderRadius,c=void 0===a?0:a,f=o.backgroundColor,d=void 0===f?L:f,l=o.objectFit,u=void 0===l?R:l,p=o.backgroundSize,g=void 0===p?R:p,v=o.objectPosition,y=o.backgroundPosition,b=o.boxShadow,o.backgroundImage&&(u=g,v=y),b&&this.drawView(e,Object.assign(o,{backgroundColor:d||b&&(d||"#ffffff")}),!0,!1,!0),x=e.width,w=e.height,m=e.left,S=e.top,i.save(),z=e.contentSize.left-e.borderSize.left,I=e.contentSize.top-e.borderSize.top,s||(this.setOpacity(o),this.setTransform(e,o),this.setBackground(d,x,w,m,S),this.roundRect(m,S,x,w,c,!!(c||!b&&d),!1)),m+=z,S+=I,i.clip(),M=function(t){if(u!==R){var n=function(t,e,i){var n=t.objectFit,r=t.objectPosition,o=e.width/e.height,s=i.width/i.height,a=1,h="contain",c="cover";n==h&&o>=s||n==c&&o<s?a=e.height/i.height:(n==h&&o<s||n==c&&o>=s)&&(a=e.width/i.width);var f=i.width*a,d=i.height*a,l=r||[],u=l[0],p=l[1],g=N(u)?Y(u,e.width):(e.width-f)*(U(u)?Y(u,1):{left:0,center:.5,right:1}[u||"center"]),v=N(p)?Y(p,e.height):(e.height-d)*(U(p)?Y(p,1):{top:0,center:.5,bottom:1}[p||"center"]),y=function(t,e){return[(t-g)/a,(e-v)/a]},b=y(0,0),x=b[0],w=b[1],m=y(e.width,e.height),S=m[0],z=m[1],I=Math.max,M=Math.min;return{sx:I(x,0),sy:I(w,0),sw:M(S-x,i.width),sh:M(z-w,i.height),dx:I(g,0),dy:I(v,0),dw:M(f,e.width),dh:M(d,e.height)}}({objectFit:u,objectPosition:v},e.contentSize,t),o=n.sx,s=n.sy,a=n.sh,h=n.sw,c=n.dx,f=n.dy,d=n.dh,l=n.dw;C==r.MP_BAIDU?i.drawImage(t.src,c+m,f+S,l,d,o,s,h,a):i.drawImage(t.src,o,s,h,a,c+m,f+S,l,d)}else i.drawImage(t.src,m,S,x,w)},k=function(){i.restore(),W.drawView(e,o,!1,!0,!1),h(1)},B=function(t){M(t),k()},B(t),[2]}))}))}))];case 1:return h.sent(),[2]}}))}))},t.prototype.drawText=function(t,e,i,n){var r=this,o=this.ctx,s=e.borderSize,a=e.contentSize,h=e.left,c=e.top,f=a.width,d=a.height,l=a.left-s.left||0,u=a.top-s.top||0,p=i.color,g=i.lineHeight,v=i.fontSize,y=i.fontWeight,b=i.fontFamily,x=i.fontStyle,w=i.textIndent,m=void 0===w?0:w,S=i.textAlign,z=i.textStroke,I=i.verticalAlign,M=void 0===I?Ct:I,k=i.backgroundColor,B=i.lineClamp,W=i.backgroundClip,P=i.textShadow,O=i.textDecoration;if(m=$(m)?m:0,this.drawView(e,i,W!=Ft),g=Y(g,v),t){o.save(),h+=l,c+=u;var T=n.fontHeight,L=n.descent,R=void 0===L?0:L,F=n.ascent,A=R+(void 0===F?0:F);switch(o.setFonts({fontFamily:b,fontSize:v,fontWeight:y,fontStyle:x}),o.setTextBaseline(Ct),o.setTextAlign(S),W?this.setBackground(k,f,d,h,c):o.setFillStyle(p),S){case Dt:break;case $t:h+=.5*f;break;case Yt:h+=f}var E=n.lines*g,j=Math.ceil((d-E)/2);switch(j<0&&(j=0),M){case jt:break;case Ct:c+=j;break;case Ht:c+=2*j}var C=(g-T)/2,H=g/2,D=function(t){var e=o.measureText(t),i=e.actualBoundingBoxDescent,n=void 0===i?0:i,r=e.actualBoundingBoxAscent;return M==jt?{fix:A?void 0===r?0:r:H-C/2,lineY:A?0:C-C/2}:M==Ct?{fix:A?H+n/4:H,lineY:A?0:C}:M==Ht?{fix:A?g-n:H+C/2,lineY:A?2*C:C+C/2}:{fix:0,height:0,lineY:0}},U=function(t,e,i){var r=t;switch(S){case Dt:r+=i;break;case $t:r=(t-=i/2)+i;break;case Yt:r=t,t-=i}if(O){o.setLineWidth(v/13),o.beginPath();var s=.1*n.fontHeight;/\bunderline\b/.test(O)&&(o.moveTo(t,e+n.fontHeight+s),o.lineTo(r,e+n.fontHeight+s)),/\boverline\b/.test(O)&&(o.moveTo(t,e-s),o.lineTo(r,e-s)),/\bline-through\b/.test(O)&&(o.moveTo(t,e+.5*n.fontHeight),o.lineTo(r,e+.5*n.fontHeight)),o.closePath(),o.setStrokeStyle(p),o.stroke()}},N=function(t,e,i){var n=function(){o.setLineWidth(z.width),o.setStrokeStyle(z.color),o.strokeText(t,e,i)},s="outset";z&&z.type!==s?(o.save(),r.setShadow({boxShadow:P}),o.fillText(t,e,i),o.restore(),n()):z&&z.type==s?(o.save(),r.setShadow({boxShadow:P}),n(),o.restore(),o.save(),o.fillText(t,e,i),o.restore()):(r.setShadow({boxShadow:P}),o.fillText(t,e,i))};if(!n.widths||1==n.widths.length&&n.widths[0].total+m<=a.width){var _=D(t),X=_.fix,q=void 0===X?0:X,G=_.lineY;return N(t,h+m,c+q),U(h+m,c+G,n&&n.widths&&n.widths[0].total||n.text),c+=g,o.restore(),void this.setBorder(e,i)}for(var V=c,J=h,Q="",Z=0,K=o.measureText("...").width,tt=n.widths,et=0;et<tt.length;et++){var it=tt[et].widths,nt=0;Q="",c+=1==(Z+=1)?0:g,1==Z&&m&&(nt=m,J=h+m);for(var rt=0;rt<it.length;rt++){1!==Z&&m&&(J=h);var ot=it[rt],st=ot.width,at=ot.text,ht=(it[rt+1]||{}).width;if(Q+=at,(nt+=st)+(void 0===ht?0:ht)+(0==Z?m:0)+(Z==B?K:0)>a.width){Z>=B&&(Q+="…"),Z++,nt=0;var ct=D(Q);q=ct.fix,G=ct.lineY;N(Q,J,c+q),U(J,c+G,nt),c+=g,Q=""}else if(rt==it.length-1){et!=tt.length-1&&Z==B&&K+nt<a.width&&(Q+="…");var ft=D(Q);q=ft.fix,G=ft.lineY;N(Q,J,c+q),U(J,c+G,nt)}if(c>V+d||Z>B)break}}o.restore()}},t.prototype.source=function(t){return i(this,void 0,void 0,(function(){var e,i,r,o,s=this;return n(this,(function(n){switch(n.label){case 0:if(this.node=null,e=+new Date,"{}"==JSON.stringify(t))return[2];if(t.styles=t.styles||t.css||{},!t.type)for(i in t.type=Et,t)["views","children","type","css","styles"].includes(i)||(t.styles[i]=t[i],delete t[i]);return t.styles.boxSizing||(t.styles.boxSizing="border-box"),[4,this.create(t)];case 1:return(r=n.sent())?(o=r.layout()||{},this.size=o,this.node=r,this.onEffectFinished().then((function(t){return s.lifecycle("onEffectSuccess",t)})).catch((function(t){return s.lifecycle("onEffectFail",t)})),this.performance&&console.log("布局用时："+(+new Date-e)+"ms"),[2,this.size]):[2,console.warn("no node")]}}))}))},t.prototype.getImageInfo=function(t){return this.imageBus[t]||(this.imageBus[t]=this.createImage(t,this.useCORS)),this.imageBus[t]},t.prototype.create=function(t,r){return i(this,void 0,void 0,(function(){function i(t,n,r){void 0===n&&(n={}),void 0===r&&(r=!0);var o=[];return t.forEach((function(t){var s=t.styles,a=void 0===s?{}:s,h=t.css,c=void 0===h?{}:h,f=t.children,d=void 0===f?[]:f,l=t.views,u=void 0===l?[]:l,p=t.text,g=void 0===p?"":p,v=t.type,y=void 0===v?"":v;!d&&u&&(t.children=d=u);var b={};b=e(e(r?e({},n):{},a),c);var x={},w={},m={};Object.keys(b).map((function(t){if(t.includes("padding")||t.includes("margin")){var e=dt(t,b[t]);Object.keys(e).map((function(t){t.includes("Left")?w[t]=e[t]:t.includes("Right")?m[t]=e[t]:x[t]=e[t]}))}}));if(b.textIndent&&(w.textIndent=b.textIndent,delete n.textIndent),""!==g){var S=Array.from(g);S.forEach((function(t,e){var i=Object.assign({},b,x);0===e?Object.assign(i,w):e==S.length-1&&Object.assign(i,m),delete i.padding,delete i.margin,o.push({type:"text",text:t,styles:i})}))}if(y==Rt||y==At)o.push(t);else if("block"===a.display&&d.length>0){var z=i(d,b,!1);t.children=z,t.flattened=!0,o.push(t)}else if(d.length>0){z=i(d,b,r);o=o.concat(z)}})),o}var o,s,a,h,c,f,d,l,u,p,g,v,y,b,x,w,m,S,z,I,M,k,B,P;return n(this,(function(n){switch(n.label){case 0:if(!t)return[2];if(t.styles||(t.styles=t.css||{}),o=t.type,s=t.show,a=void 0===s||s,h=o==Rt,c=[Ft,At].includes(o),f="textBox"==o,d=t.styles||{},l=d.backgroundImage,u=d.display,h&&!t.src&&!t.url)return[2];if(u==W||!a)return[2];if(c||f){if(p=t.children,g=t.views,!p&&g&&(t.children=p=g),!t.text&&(!p||p&&!p.length))return[2];p&&p.length&&!t.flattened&&(v=i(t.children||t.views),t.type="view",t.children=v)}if(!(h||t.type==Et&&l))return[3,4];y=h?t.src:"",b=/url\(['"]?(.*?)['"]?\)/.exec(l),l&&b&&b[1]&&(y=b[1]||""),n.label=1;case 1:return n.trys.push([1,3,,4]),[4,this.getImageInfo(y)];case 2:return x=n.sent(),w=x.width,m=x.height,!(S=x.path)&&h?[2]:(S&&(t.attributes=Object.assign(t.attributes||{},{width:w,height:m,path:S,src:S,naturalSrc:y})),[3,4]);case 3:return z=n.sent(),t.type!=Et?[2]:(this.lifecycle("onEffectFail",e(e({},z),{src:y})),[3,4]);case 4:if(this.count+=1,I=new Tt(t,r,this.root,this.ctx),!(M=t.children||t.views))return[3,8];k=0,n.label=5;case 5:return k<M.length?(B=M[k],[4,this.create(B,I)]):[3,8];case 6:(P=n.sent())&&I.add(P),n.label=7;case 7:return k++,[3,5];case 8:return[2,I]}}))}))},t.prototype.drawNode=function(t,e){return void 0===e&&(e=!1),i(this,void 0,void 0,(function(){var i,r,o,s,a,h,c,f,d,l,u,p,g,v,y,b,x,w,m,S,z,I,M;return n(this,(function(n){switch(n.label){case 0:return i=t.layoutBox,r=t.computedStyle,o=t.attributes,s=t.name,a=t.children,h=t.fixedLine,c=t.attributes,f=c.src,d=c.text,l=r.position,u=r.backgroundImage,p=r.backgroundRepeat,["fixed"].includes(l)&&!e?[2]:(this.ctx.save(),s!==Et?[3,7]:f&&u?p?[4,this.drawPattern(o,i,r)]:[3,2]:[3,5]);case 1:return n.sent(),[3,4];case 2:return[4,this.drawImage(o,i,r,!1)];case 3:n.sent(),n.label=4;case 4:return[3,6];case 5:this.drawView(i,r),n.label=6;case 6:return[3,10];case 7:return s===Rt&&f?[4,this.drawImage(o,i,r,!1)]:[3,9];case 8:return n.sent(),[3,10];case 9:s===Ft?this.drawText(d,i,r,o):s===At&&Lt.api&&Lt.api.draw(d,this,i,r),n.label=10;case 10:if(this.progress+=1,v=(g=h||{}).beforeElements,y=g.afterElements,!v)return[3,14];b=0,x=v,n.label=11;case 11:return b<x.length?(M=x[b],[4,this.drawNode(M)]):[3,14];case 12:n.sent(),n.label=13;case 13:return b++,[3,11];case 14:if(!a)return[3,18];w=Object.values?Object.values(a):Object.keys(a).map((function(t){return a[t]})),m=0,S=w,n.label=15;case 15:return m<S.length?"absolute"===(M=S[m]).computedStyle.position?[3,17]:[4,this.drawNode(M)]:[3,18];case 16:n.sent(),n.label=17;case 17:return m++,[3,15];case 18:if(!y)return[3,22];z=0,I=y,n.label=19;case 19:return z<I.length?(M=I[z],[4,this.drawNode(M)]):[3,22];case 20:n.sent(),n.label=21;case 21:return z++,[3,19];case 22:return this.ctx.restore(),[2]}}))}))},t.prototype.render=function(t){var e=this;return void 0===t&&(t=30),new Promise((function(r,o){return i(e,void 0,void 0,(function(){var e,i,s,a,h,c,f,d,l,u;return n(this,(function(n){switch(n.label){case 0:return e=+new Date,this.init(),[4,(p=t,void 0===p&&(p=0),new Promise((function(t){return setTimeout(t,p)})))];case 1:n.sent(),n.label=2;case 2:if(n.trys.push([2,14,,15]),!this.node)return[3,12];if(i=this.root.fixedLine||{},s=i.beforeElements,a=i.afterElements,!s)return[3,6];h=0,c=s,n.label=3;case 3:return h<c.length?(l=c[h],[4,this.drawNode(l,!0)]):[3,6];case 4:n.sent(),n.label=5;case 5:return h++,[3,3];case 6:return[4,this.drawNode(this.node)];case 7:if(n.sent(),!a)return[3,11];f=0,d=a,n.label=8;case 8:return f<d.length?(l=d[f],[4,this.drawNode(l,!0)]):[3,11];case 9:n.sent(),n.label=10;case 10:return f++,[3,8];case 11:return r(this.node),[3,13];case 12:this.lifecycle("onEffectFail","node is empty"),n.label=13;case 13:return[3,15];case 14:return u=n.sent(),this.lifecycle("onEffectFail",u),o(u),[3,15];case 15:return this.performance&&console.log("渲染用时："+(+new Date-e-30)+"ms"),[2]}var p}))}))}))},t.prototype.onEffectFinished=function(){var t=this,e=Object.keys(this.imageBus).map((function(e){return t.imageBus[e]}));return Promise.all(e)},t.prototype.destroy=function(){this.node=[]},t.prototype.save=function(t){try{var e=t||{},i=e.fileType,n=void 0===i?"png":i,r=e.quality,o=void 0===r?1:r;return this.canvas.toDataURL("image/".concat(n),o)}catch(t){return this.lifecycle("onEffectFail","image cross domain"),t}},t}();r.WEB==C&&(window.Painter=Ut),t.Painter=Ut,t.default=Ut,Object.defineProperty(t,"__esModule",{value:!0})}));
