{"version": 3, "file": "index.js", "sources": ["pages/raw-record/index.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvcmF3LXJlY29yZC9pbmRleC52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"withdraw-record-page\">\r\n    <!-- 记录列表 -->\r\n    <view class=\"record-list\">\r\n      <view class=\"record-item\" v-for=\"(item, index) in recordList\" :key=\"index\">\r\n        <view class=\"record-content\">\r\n          <view class=\"amount-section\">\r\n            <text class=\"amount\">{{ item.amount }}</text>\r\n            <text class=\"status\" :class=\"getStatusClass(item.status)\">{{ item.statusText }}</text>\r\n          </view>\r\n          <view class=\"time-section\">\r\n            <text class=\"time-label\">提现时间：</text>\r\n            <text class=\"time-value\">{{ item.withdrawTime }}</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 空状态 -->\r\n    <view class=\"empty-state\" v-if=\"recordList.length === 0\">\r\n      <image src=\"@/static/common/empty_icon.png\" class=\"empty-icon\" mode=\"aspectFit\" />\r\n      <text class=\"empty-text\">暂无提现记录</text>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref } from 'vue'\r\nimport { onLoad } from '@dcloudio/uni-app'\r\n\r\n// 提现记录列表\r\nconst recordList = ref([\r\n  // {\r\n  //   amount: '-2000.00',\r\n  //   status: 'success',\r\n  //   statusText: '成功',\r\n  //   withdrawTime: '2025-05-18 12:56:00'\r\n  // },\r\n  // {\r\n  //   amount: '-2000.00',\r\n  //   status: 'failed',\r\n  //   statusText: '打款失败，已退回余额',\r\n  //   withdrawTime: '2025-05-18 12:56:00'\r\n  // },\r\n  // {\r\n  //   amount: '-2000.00',\r\n  //   status: 'processing',\r\n  //   statusText: '打款中',\r\n  //   withdrawTime: '2025-05-18 12:56:00'\r\n  // }\r\n])\r\n\r\n// 获取状态样式类\r\nconst getStatusClass = (status) => {\r\n  switch (status) {\r\n    case 'success':\r\n      return 'status-success'\r\n    case 'failed':\r\n      return 'status-failed'\r\n    case 'processing':\r\n      return 'status-processing'\r\n    default:\r\n      return ''\r\n  }\r\n}\r\n\r\n\r\n\r\n// 加载提现记录列表\r\nconst loadRecordList = async () => {\r\n  try {\r\n    // 这里调用API获取提现记录\r\n    // const res = await getWithdrawRecordApi()\r\n    // recordList.value = res.data\r\n    console.log('加载提现记录')\r\n  } catch (error) {\r\n    console.error('加载提现记录失败:', error)\r\n    uni.showToast({\r\n      title: '加载失败',\r\n      icon: 'none'\r\n    })\r\n  }\r\n}\r\n\r\nonLoad(() => {\r\n  loadRecordList()\r\n})\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.withdraw-record-page {\r\n  background: #f5f5f5;\r\n  min-height: 100vh;\r\n\r\n  .record-list {\r\n    padding: 32rpx;\r\n\r\n    .record-item {\r\n      background: #ffffff;\r\n      border-radius: 16rpx;\r\n      margin-bottom: 16rpx;\r\n      overflow: hidden;\r\n\r\n      .record-content {\r\n        padding: 40rpx 32rpx;\r\n\r\n        .amount-section {\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: space-between;\r\n          margin-bottom: 20rpx;\r\n\r\n          .amount {\r\n            font-size: 40rpx;\r\n            font-weight: 600;\r\n            color: #3478f6;\r\n          }\r\n\r\n          .status {\r\n            font-size: 28rpx;\r\n            font-weight: 500;\r\n\r\n            &.status-success {\r\n              color: #52c41a;\r\n            }\r\n\r\n            &.status-failed {\r\n              color: #ff4d4f;\r\n            }\r\n\r\n            &.status-processing {\r\n              color: #52c41a;\r\n            }\r\n          }\r\n        }\r\n\r\n        .time-section {\r\n          display: flex;\r\n          align-items: center;\r\n\r\n          .time-label {\r\n            font-size: 28rpx;\r\n            color: #999999;\r\n          }\r\n\r\n          .time-value {\r\n            font-size: 28rpx;\r\n            color: #999999;\r\n            margin-left: 8rpx;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .empty-state {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    justify-content: center;\r\n    padding: 200rpx 0;\r\n\r\n    .empty-icon {\r\n      width: 200rpx;\r\n      height: 200rpx;\r\n      margin-bottom: 40rpx;\r\n      opacity: 0.6;\r\n    }\r\n\r\n    .empty-text {\r\n      font-size: 28rpx;\r\n      color: #999999;\r\n    }\r\n  }\r\n}\r\n</style>", "import MiniProgramPage from 'E:/youngProject/agent-mini-ui/pages/raw-record/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "uni", "onLoad"], "mappings": ";;;;;;AA+BA,UAAM,aAAaA,cAAAA,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAmBvB,CAAC;AAGD,UAAM,iBAAiB,CAAC,WAAW;AACjC,cAAQ,QAAM;AAAA,QACZ,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT;AACE,iBAAO;AAAA,MACV;AAAA,IACH;AAKA,UAAM,iBAAiB,YAAY;AACjC,UAAI;AAIFC,sBAAAA,MAAA,MAAA,OAAA,oCAAY,QAAQ;AAAA,MACrB,SAAQ,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,oCAAc,aAAa,KAAK;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACF;AAAA,IACH;AAEAC,kBAAAA,OAAO,MAAM;AACX,qBAAgB;AAAA,IAClB,CAAC;;;;;;;;;;;;;;;;;;;;ACrFD,GAAG,WAAW,eAAe;"}