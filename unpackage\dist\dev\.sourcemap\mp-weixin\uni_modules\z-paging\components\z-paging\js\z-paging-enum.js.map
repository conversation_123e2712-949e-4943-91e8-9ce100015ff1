{"version": 3, "file": "z-paging-enum.js", "sources": ["uni_modules/z-paging/components/z-paging/js/z-paging-enum.js"], "sourcesContent": ["// [z-paging]枚举\r\n\r\nexport default {\r\n\t// 当前加载类型 0.下拉刷新 1.上拉加载更多\r\n\tLoadingType: {\r\n\t\tRefresher: 0,\r\n\t\tLoadingMore: 1\r\n\t},\r\n\t// 下拉刷新状态 0.默认状态 1.松手立即刷新 2.刷新中 3.刷新结束 4.松手进入二楼\r\n\tRefresher: {\r\n\t\tDefault: 0,\r\n\t\tReleaseToRefresh: 1,\r\n\t\tLoading: 2,\r\n\t\tComplete: 3,\r\n\t\tGoF2: 4\r\n\t},\r\n\t// 底部加载更多状态 0.默认状态 1.加载中 2.没有更多数据 3.加载失败\r\n\tMore: {\r\n\t\tDefault: 0,\r\n\t\tLoading: 1,\r\n\t\tNoMore: 2,\r\n\t\tFail: 3\r\n\t},\r\n\t// @query触发来源 0.用户主动下拉刷新 1.通过reload触发 2.通过refresh触发 3.通过滚动到底部加载更多或点击底部加载更多触发\r\n\tQueryFrom: {\r\n\t\tUserPullDown: 0,\r\n\t\tReload: 1,\r\n\t\tRefresh: 2,\r\n\t\tLoadingMore: 3\r\n\t},\r\n\t// 虚拟列表cell高度模式\r\n\tCellHeightMode: {\r\n\t\t// 固定高度\r\n\t\tFixed: 'fixed',\r\n\t\t// 动态高度\r\n\t\tDynamic: 'dynamic'\r\n\t},\r\n\t// 列表缓存模式\r\n\tCacheMode: {\r\n\t\t// 默认模式，只会缓存一次\r\n\t\tDefault: 'default',\r\n\t\t// 总是缓存，每次列表刷新(下拉刷新、调用reload等)都会更新缓存\r\n\t\tAlways: 'always'\r\n\t}\r\n}"], "names": [], "mappings": ";AAEA,MAAe,OAAA;AAAA;AAAA,EAEd,aAAa;AAAA,IACZ,WAAW;AAAA,IACX,aAAa;AAAA,EACb;AAAA;AAAA,EAED,WAAW;AAAA,IACV,SAAS;AAAA,IACT,kBAAkB;AAAA,IAClB,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,EACN;AAAA;AAAA,EAED,MAAM;AAAA,IACL,SAAS;AAAA,IACT,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,MAAM;AAAA,EACN;AAAA;AAAA,EAED,WAAW;AAAA,IACV,cAAc;AAAA,IACd,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,aAAa;AAAA,EACb;AAAA;AAAA,EAED,gBAAgB;AAAA;AAAA,IAEf,OAAO;AAAA;AAAA,IAEP,SAAS;AAAA,EACT;AAAA;AAAA,EAED,WAAW;AAAA;AAAA,IAEV,SAAS;AAAA;AAAA,IAET,QAAQ;AAAA,EACR;AACF;;"}