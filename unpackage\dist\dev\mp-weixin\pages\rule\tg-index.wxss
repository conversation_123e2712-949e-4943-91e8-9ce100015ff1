/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.rule-page.data-v-fd97a914 {
  background: #ffffff;
  min-height: 100vh;
  position: relative;
}
.rule-page .background-image.data-v-fd97a914 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}
.rule-page .title-section.data-v-fd97a914 {
  position: relative;
  z-index: 2;
  padding: 40rpx 32rpx 20rpx;
}
.rule-page .title-section .title-image.data-v-fd97a914 {
  width: 560rpx;
  height: 60px;
}
.rule-page .content-section.data-v-fd97a914 {
  position: relative;
  z-index: 2;
  margin: 20rpx 32rpx;
  background: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx 32rpx 100px;
  margin-top: -20px;
}
.rule-page .content-section .rule-content .rule-item.data-v-fd97a914 {
  margin-bottom: 32rpx;
}
.rule-page .content-section .rule-content .rule-item.data-v-fd97a914:last-child {
  margin-bottom: 0;
}
.rule-page .content-section .rule-content .rule-item .rule-title.data-v-fd97a914 {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 16rpx;
  line-height: 1.4;
}
.rule-page .content-section .rule-content .rule-item .rule-text.data-v-fd97a914 {
  display: block;
  font-size: 28rpx;
  color: #333333;
  line-height: 1.6;
  margin-bottom: 12rpx;
}
.rule-page .content-section .rule-content .rule-item .rule-text.data-v-fd97a914:last-child {
  margin-bottom: 0;
}
.rule-page .action-section.data-v-fd97a914 {
  position: fixed;
  /* 为兼容某些Android版本微信小程序，添加最小bottom保障 */
  bottom: calc(env(safe-area-inset-bottom, 40rpx) + 40rpx);
  /* 降级方案：当env()不支持时使用固定值 */
  bottom: 80rpx;
  bottom: calc(env(safe-area-inset-bottom) + 40rpx);
  left: 50%;
  transform: translateX(-50%);
  z-index: 3;
}
.rule-page .action-section .create-btn.data-v-fd97a914 {
  width: 400rpx;
  height: 88rpx;
  background: #F6542A;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.rule-page .action-section .create-btn .create-text.data-v-fd97a914 {
  font-size: 32rpx;
  color: #fff;
  font-weight: 600;
}