"use strict";
const common_vendor = require("../common/vendor.js");
const config_config = require("../config/config.js");
const utils_requestUtil = require("../utils/requestUtil.js");
const stores_user = require("../stores/user.js");
let timeout = 6e4;
const baseUrl = config_config.base.baseUrl;
const toastTip = (title, icon, time) => {
  common_vendor.index.showToast({
    title: title || "网络连接超时,请稍后重试!",
    icon: icon || "none",
    duration: time || 1500
  });
};
const tansParams = (params) => {
  let result = "";
  for (const propName of (void 0).getKeys(params)) {
    const value = params[propName];
    let part = encodeURIComponent(propName) + "=";
    if (value !== null && value !== "" && typeof value !== "undefined") {
      if (typeof value === "object") {
        for (const key of (void 0).getKeys(value)) {
          if (value[key] !== null && value[key] !== "" && typeof value[key] !== "undefined") {
            let params2 = propName + "[" + key + "]";
            let subPart = encodeURIComponent(params2) + "=";
            result += subPart + encodeURIComponent(value[key]) + "&";
          }
        }
      } else {
        result += part + encodeURIComponent(value) + "&";
      }
    }
  }
  return result;
};
const queryStr = (obj) => {
  if (obj) {
    let query = "";
    for (let i in obj) {
      let value = obj[i];
      if (Array.isArray(value)) {
        value = value.join(",");
      }
      query += `&${i}=${value}`;
    }
    query = query.replace("&", "?");
    return query;
  }
  return "";
};
const request = (config) => {
  const userStore = stores_user.useUserStore();
  config.urlSuffix = {
    app_guid: "e108201b02ae42e686bcc4c302cbbd11",
    //应用唯一标识
    expires: parseInt(((/* @__PURE__ */ new Date()).getTime() / 1e3).toFixed(0)),
    //当前时间戳
    token: userStore.userToken || "notoken",
    noncestr: utils_requestUtil.randomStr(true, true, true, 32),
    merchantGuid: userStore.merchantGuid,
    app_type: "wechat"
  };
  config.urlSuffix.signature = encodeURIComponent(utils_requestUtil.autographFun(config));
  if (config.params) {
    let url = config.url + "?" + tansParams(config.params);
    url = url.slice(0, -1);
    config.url = url;
  }
  return new Promise((resolve, reject) => {
    common_vendor.index.request({
      method: config.method || "POST",
      timeout: config.timeout || timeout,
      url: baseUrl + config.url + queryStr(config.urlSuffix),
      data: config.data,
      header: config.header,
      dataType: "json",
      success(res) {
        if (res.data.code === 704001) {
          userStore.delete_user_info();
          return;
        }
        if (res.data.code === 0) {
          resolve(res.data);
        } else {
          common_vendor.index.__f__("log", "at request/request.js:126", res.data, "--------------------???");
          toastTip(res.data.msg);
          reject(res.data);
        }
      },
      fail: (error) => {
        common_vendor.index.__f__("log", "at request/request.js:132", error, "errorerrorerror");
        let {
          errMsg
        } = error;
        if (errMsg === "Network Error") {
          errMsg = "后端接口连接异常";
        } else if (errMsg.includes("timeout")) {
          errMsg = "AI算力繁忙";
        } else if (errMsg.includes("Request failed with status code")) {
          errMsg = "系统接口" + errMsg.substr(errMsg.length - 3) + "异常";
        }
        toastTip(errMsg, "none", 1500);
        reject(error);
      },
      complete(res) {
      }
    });
  });
};
exports.request = request;
//# sourceMappingURL=../../.sourcemap/mp-weixin/request/request.js.map
