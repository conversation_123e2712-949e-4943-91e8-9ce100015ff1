{"version": 3, "file": "mpwxs.js", "sources": ["uni_modules/uni-swipe-action/components/uni-swipe-action-item/mpwxs.js"], "sourcesContent": ["let mpMixins = {}\r\nlet is_pc = null\r\n// #ifdef H5\r\nimport {\r\n\tisPC\r\n} from \"./isPC\"\r\nis_pc = isPC()\r\n// #endif\r\n// #ifdef APP-VUE || APP-HARMONY || MP-WEIXIN || H5\r\n\r\nmpMixins = {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tis_show: 'none'\r\n\t\t}\r\n\t},\r\n\twatch: {\r\n\t\tshow(newVal) {\n\t\t\tthis.is_show = this.show\r\n\t\t}\r\n\t},\r\n\tcreated() {\n\t\tthis.swipeaction = this.getSwipeAction()\n\t\tif (this.swipeaction && Array.isArray(this.swipeaction.children)) {\r\n\t\t\tthis.swipeaction.children.push(this)\r\n\t\t}\r\n\t},\r\n\tmounted() {\r\n\t\tthis.is_show = this.show\r\n\t},\r\n\tmethods: {\r\n\t\t// wxs 中调用\r\n\t\tcloseSwipe(e) {\n\t\t\tif (this.autoClose && this.swipeaction) {\n\t\t\t\tthis.swipeaction.closeOther(this)\n\t\t\t}\r\n\t\t},\r\n\r\n\t\tchange(e) {\r\n\t\t\tthis.$emit('change', e.open)\r\n\t\t\tif (this.is_show !== e.open) {\r\n\t\t\t\tthis.is_show = e.open\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\tappTouchStart(e) {\r\n\t\t\tif (is_pc) return\r\n\t\t\tconst {\r\n\t\t\t\tclientX\r\n\t\t\t} = e.changedTouches[0]\r\n\t\t\tthis.clientX = clientX\r\n\t\t\tthis.timestamp = new Date().getTime()\r\n\t\t},\r\n\t\tappTouchEnd(e, index, item, position) {\r\n\t\t\tif (is_pc) return\r\n\t\t\tconst {\r\n\t\t\t\tclientX\r\n\t\t\t} = e.changedTouches[0]\r\n\t\t\t// fixed by xxxx 模拟点击事件，解决 ios 13 点击区域错位的问题\r\n\t\t\tlet diff = Math.abs(this.clientX - clientX)\r\n\t\t\tlet time = (new Date().getTime()) - this.timestamp\r\n\t\t\tif (diff < 40 && time < 300) {\r\n\t\t\t\tthis.$emit('click', {\r\n\t\t\t\t\tcontent: item,\r\n\t\t\t\t\tindex,\r\n\t\t\t\t\tposition\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\t\tonClickForPC(index, item, position) {\r\n\t\t\tif (!is_pc) return\r\n\t\t\t// #ifdef H5\r\n\t\t\tthis.$emit('click', {\r\n\t\t\t\tcontent: item,\r\n\t\t\t\tindex,\r\n\t\t\t\tposition\r\n\t\t\t})\r\n\t\t\t// #endif\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// #endif\r\nexport default mpMixins\n"], "names": [], "mappings": ";AAAA,IAAI,WAAW,CAAE;AAUjB,WAAW;AAAA,EACV,OAAO;AACN,WAAO;AAAA,MACN,SAAS;AAAA,IACT;AAAA,EACD;AAAA,EACD,OAAO;AAAA,IACN,KAAK,QAAQ;AACZ,WAAK,UAAU,KAAK;AAAA,IACpB;AAAA,EACD;AAAA,EACD,UAAU;AACT,SAAK,cAAc,KAAK,eAAgB;AACxC,QAAI,KAAK,eAAe,MAAM,QAAQ,KAAK,YAAY,QAAQ,GAAG;AACjE,WAAK,YAAY,SAAS,KAAK,IAAI;AAAA,IACnC;AAAA,EACD;AAAA,EACD,UAAU;AACT,SAAK,UAAU,KAAK;AAAA,EACpB;AAAA,EACD,SAAS;AAAA;AAAA,IAER,WAAW,GAAG;AACb,UAAI,KAAK,aAAa,KAAK,aAAa;AACvC,aAAK,YAAY,WAAW,IAAI;AAAA,MAChC;AAAA,IACD;AAAA,IAED,OAAO,GAAG;AACT,WAAK,MAAM,UAAU,EAAE,IAAI;AAC3B,UAAI,KAAK,YAAY,EAAE,MAAM;AAC5B,aAAK,UAAU,EAAE;AAAA,MACjB;AAAA,IACD;AAAA,IAED,cAAc,GAAG;AAEhB,YAAM;AAAA,QACL;AAAA,MACJ,IAAO,EAAE,eAAe,CAAC;AACtB,WAAK,UAAU;AACf,WAAK,aAAY,oBAAI,KAAI,GAAG,QAAS;AAAA,IACrC;AAAA,IACD,YAAY,GAAG,OAAO,MAAM,UAAU;AAErC,YAAM;AAAA,QACL;AAAA,MACJ,IAAO,EAAE,eAAe,CAAC;AAEtB,UAAI,OAAO,KAAK,IAAI,KAAK,UAAU,OAAO;AAC1C,UAAI,QAAQ,oBAAI,KAAM,GAAC,QAAO,IAAM,KAAK;AACzC,UAAI,OAAO,MAAM,OAAO,KAAK;AAC5B,aAAK,MAAM,SAAS;AAAA,UACnB,SAAS;AAAA,UACT;AAAA,UACA;AAAA,QACL,CAAK;AAAA,MACD;AAAA,IACD;AAAA,IACD,aAAa,OAAO,MAAM,UAAU;AACvB;AAAA,IAQZ;AAAA,EACD;AACF;AAGA,MAAe,QAAA;;"}