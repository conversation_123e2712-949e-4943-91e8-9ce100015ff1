"use strict";const e=require("../../common/vendor.js"),t=require("../../common/assets.js"),a=require("../../api/index.js"),o=require("../../stores/user.js");if(!Array){e.resolveComponent("z-paging")()}Math;const s={__name:"favorite-list",setup(s){const n=o.useUserStore(),i=e.ref([]),r=e.ref(null),l=e.ref(!1),c=e.ref(""),u=async(t,o)=>{try{let e=await a.getMyCollectionListApi({merchantGuid:n.merchantGuid,page:t,pageSize:o});r.value.complete(e.data.list||[])}catch(s){console.error("获取收藏列表失败:",s),e.index.showToast({title:"加载失败",icon:"none"}),r.value.complete(!1)}},d=()=>{l.value=!1,c.value=""};return(o,s)=>e.e({a:e.f(i.value,((t,o,s)=>({a:e.t(t.collectTime),b:e.t(t.contentPreview),c:e.o((a=>(t=>{e.index.setClipboardData({data:t.messageContent,success(){e.index.showToast({title:"复制成功",icon:"none"})}})})(t)),o),d:e.o((e=>(e=>{c.value=e.messageContent||"",l.value=!0})(t)),o),e:e.o((o=>(async t=>{try{await a.cancelCollectMessageApi({merchantGuid:n.merchantGuid,messageGuid:t.messageGuid}),e.index.showToast({title:"取消收藏成功",icon:"none"}),r.value&&r.value.reload()}catch(o){console.error("取消收藏失败:",o),e.index.showToast({title:"操作失败",icon:"none"})}})(t)),o),f:o}))),b:t._imports_0$2,c:t._imports_1$1,d:t._imports_2$2,e:e.sr(r,"0583d809-0",{k:"paging"}),f:e.o(u),g:e.o((e=>i.value=e)),h:e.p({auto:!0,"auto-clean-list-when-reload":!1,modelValue:i.value}),i:l.value},l.value?{j:e.o(d),k:c.value,l:e.o((()=>{})),m:e.o(d)}:{})}},n=e._export_sfc(s,[["__scopeId","data-v-0583d809"]]);wx.createPage(n);
