"use strict";
const common_vendor = require("../../common/vendor.js");
const api_index = require("../../api/index.js");
const stores_user = require("../../stores/user.js");
const _sfc_main = {
  __name: "detail",
  setup(__props) {
    const userStore = stores_user.useUserStore();
    const agentGuid = common_vendor.ref("");
    const sysId = common_vendor.ref("");
    const loading = common_vendor.ref(false);
    common_vendor.ref(false);
    common_vendor.ref(false);
    common_vendor.ref(false);
    const agentDetail = common_vendor.ref({
      agentName: "",
      agentDesc: "",
      agentAvatar: "",
      isPaid: 0,
      price: 0,
      isPublic: 1,
      isSubscribed: false
    });
    const getAgentDetail = async () => {
      if (!sysId.value) {
        common_vendor.index.showToast({
          title: "参数错误",
          icon: "none"
        });
        return;
      }
      try {
        loading.value = true;
        const res = await api_index.agentDetailApi({
          merchantGuid: userStore.merchantGuid,
          agentSysId: sysId.value
        });
        if (res.code === 0) {
          agentDetail.value = res.data;
          sysId.value = res.data.sysId;
          agentGuid.value = res.data.guid;
        } else {
          throw new Error(res.msg || "获取详情失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/square/detail.vue:103", "获取智能体详情失败:", error);
        common_vendor.index.showToast({
          title: error.message || "加载失败",
          icon: "none"
        });
      } finally {
        loading.value = false;
      }
    };
    const handleSubscribe = async () => {
      if (agentDetail.value.isSubscribed) {
        common_vendor.index.navigateTo({
          url: `/pages/msg/index?sessionGuid=${agentGuid.value}`
        });
        return;
      }
      try {
        common_vendor.index.showLoading({
          title: "订阅中...",
          mask: true
        });
        const res = await api_index.subscribeAgentApi({
          merchantGuid: userStore.merchantGuid,
          agentGuid: agentGuid.value
        });
        if (res.code === 0) {
          common_vendor.index.showToast({
            title: "订阅成功",
            icon: "success"
          });
          common_vendor.index.navigateTo({
            url: `/pages/msg/index?sessionGuid=${agentGuid.value}`
          });
        } else {
          throw new Error(res.msg || "订阅失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/square/detail.vue:147", "订阅失败:", error);
        common_vendor.index.showToast({
          title: error.message || "订阅失败",
          icon: "none"
        });
      } finally {
        common_vendor.index.hideLoading();
      }
    };
    const handleShare = async () => {
      if (!qrcode.value) {
        common_vendor.index.showToast({
          title: "正在生成二维码，请稍后...",
          icon: "none"
        });
        return;
      }
      const params = {
        agentName: agentDetail.value.agentName || "智能体名称",
        agentDesc: agentDetail.value.agentDesc || "智能体描述",
        agentAvatar: agentDetail.value.agentAvatar || "",
        qrcode: qrcode.value || ""
      };
      common_vendor.index.__f__("log", "at pages/square/detail.vue:175", "传递给分享页的参数:", params);
      common_vendor.index.navigateTo({
        url: `/pages/square/share?params=${encodeURIComponent(JSON.stringify(params))}`
      });
    };
    const handleSubscribeConfirm = async (agentInfo) => {
      try {
        common_vendor.index.showLoading({
          title: "订阅中...",
          mask: true
        });
        const res = await api_index.subscribeAgentApi({
          merchantGuid: userStore.merchantGuid,
          agentGuid: agentGuid.value
        });
        if (res.code === 0) {
          common_vendor.index.showToast({
            title: "订阅成功",
            icon: "success"
          });
          agentDetail.value.isSubscribed = true;
          setTimeout(() => {
            common_vendor.index.navigateTo({
              url: `/pages/msg/index?sessionGuid=${agentGuid.value}`
            });
          }, 1500);
        } else {
          throw new Error(res.msg || "订阅失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/square/detail.vue:220", "订阅失败:", error);
        common_vendor.index.showToast({
          title: error.message || "订阅失败",
          icon: "none"
        });
      } finally {
        common_vendor.index.hideLoading();
      }
    };
    common_vendor.onShareAppMessage(() => {
      return {
        title: userStore.appName || "智能体",
        path: `/pages/square/detail?invite=${invitationCode.value}`,
        success(res) {
          common_vendor.index.showToast({
            title: "分享成功"
          });
        },
        fail(res) {
          common_vendor.index.showToast({
            title: "分享失败",
            icon: "none"
          });
        }
      };
    });
    const qrcode = common_vendor.ref("");
    const generateMiniCode = async () => {
      let query = `sysId=${sysId.value}&invite=${userStore.invitationCode}`;
      let res = await api_index.generateMiniCodeApi({
        merchantGuid: userStore.merchantGuid,
        miniPath: "pages/square/detail",
        pathQuery: query
      });
      qrcode.value = res.data.miniCodeUrl;
    };
    const invitationCode = common_vendor.ref("");
    const getInvitationCode = async () => {
      let res = await api_index.getInvitationCodeApi({
        merchantGuid: userStore.merchantGuid
      });
      invitationCode.value = res.data.inviteCode;
      generateMiniCode();
    };
    common_vendor.onLoad(async (params) => {
      if (params.sysId) {
        sysId.value = params.sysId;
        if (userStore.userToken) {
          await getAgentDetail();
          await getInvitationCode();
        }
      } else {
        common_vendor.index.showToast({
          title: "参数错误",
          icon: "none"
        });
      }
    });
    common_vendor.watch(
      () => userStore.userToken,
      async (newValue, oldValue) => {
        if (newValue && oldValue === "") {
          await getAgentDetail();
          await getInvitationCode();
        }
      }
    );
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: agentDetail.value.agentName
      }, agentDetail.value.agentName ? common_vendor.e({
        b: agentDetail.value.agentAvatar,
        c: common_vendor.t(agentDetail.value.creator.nickname),
        d: common_vendor.t(agentDetail.value.agentName),
        e: common_vendor.t(agentDetail.value.agentDesc),
        f: !agentDetail.value.isSubscribed
      }, !agentDetail.value.isSubscribed ? {
        g: common_vendor.o(handleSubscribeConfirm)
      } : {}, {
        h: agentDetail.value.isSubscribed
      }, agentDetail.value.isSubscribed ? {
        i: common_vendor.o(handleSubscribe)
      } : {}, {
        j: common_vendor.o(handleShare)
      }) : loading.value ? {} : {}, {
        k: loading.value
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-efb7de88"]]);
_sfc_main.__runtimeHooks = 6;
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/square/detail.js.map
