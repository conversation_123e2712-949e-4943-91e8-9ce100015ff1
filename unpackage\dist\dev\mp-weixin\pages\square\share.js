"use strict";
const common_vendor = require("../../common/vendor.js");
if (!Array) {
  const _easycom_l_painter_image2 = common_vendor.resolveComponent("l-painter-image");
  const _easycom_l_painter_text2 = common_vendor.resolveComponent("l-painter-text");
  const _easycom_l_painter2 = common_vendor.resolveComponent("l-painter");
  (_easycom_l_painter_image2 + _easycom_l_painter_text2 + _easycom_l_painter2)();
}
const _easycom_l_painter_image = () => "../../uni_modules/lime-painter/components/l-painter-image/l-painter-image.js";
const _easycom_l_painter_text = () => "../../uni_modules/lime-painter/components/l-painter-text/l-painter-text.js";
const _easycom_l_painter = () => "../../uni_modules/lime-painter/components/l-painter/l-painter.js";
if (!Math) {
  (_easycom_l_painter_image + _easycom_l_painter_text + _easycom_l_painter)();
}
const _sfc_main = {
  __name: "share",
  setup(__props) {
    const path = common_vendor.ref("");
    const dataReady = common_vendor.ref(false);
    let agentDetail = common_vendor.reactive({
      agentName: "",
      agentDesc: "",
      agentAvatar: ""
    });
    const qrcode = common_vendor.ref("");
    common_vendor.ref(null);
    common_vendor.onLoad(async (options) => {
      common_vendor.index.__f__("log", "at pages/square/share.vue:53", "接收到的参数:", options);
      if (options && options.params) {
        try {
          const params = JSON.parse(decodeURIComponent(options.params));
          common_vendor.index.__f__("log", "at pages/square/share.vue:57", "params", params);
          agentDetail = Object.assign(agentDetail, {
            agentName: params.agentName && params.agentName.trim() || "智能体名称",
            agentDesc: params.agentDesc && params.agentDesc.trim() || "智能体描述",
            agentAvatar: params.agentAvatar && params.agentAvatar.trim() || ""
          });
          qrcode.value = params.qrcode && params.qrcode.trim() || "";
          common_vendor.index.__f__("log", "at pages/square/share.vue:67", "agentDetail", agentDetail);
          await common_vendor.nextTick$1();
          dataReady.value = true;
        } catch (error) {
          common_vendor.index.__f__("log", "at pages/square/share.vue:76", "error", error);
          Object.assign(agentDetail, {
            agentName: "智能体名称",
            agentDesc: "智能体描述",
            agentAvatar: ""
          });
          dataReady.value = true;
        }
      } else {
        common_vendor.index.__f__("log", "at pages/square/share.vue:86", "没有参数，使用默认配置");
        Object.assign(agentDetail, {
          agentName: "智能体名称",
          agentDesc: "智能体描述",
          agentAvatar: ""
        });
        dataReady.value = true;
      }
    });
    const downloadPoster = () => {
      common_vendor.index.__f__("log", "at pages/square/share.vue:100", "开始下载海报");
      common_vendor.index.saveImageToPhotosAlbum({
        filePath: path.value,
        success: () => {
          common_vendor.index.showToast({
            title: "保存成功",
            icon: "success"
          });
        },
        fail: (error) => {
          common_vendor.index.__f__("error", "at pages/square/share.vue:111", "保存失败:", error);
          common_vendor.index.showToast({
            title: "保存失败",
            icon: "none"
          });
        }
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: path.value,
        b: common_vendor.o(downloadPoster),
        c: dataReady.value
      }, dataReady.value ? {
        d: common_vendor.p({
          src: common_vendor.unref(agentDetail).agentAvatar,
          css: "width: 200rpx; height: 200rpx; display: block; border-radius: 50%; margin:80rpx auto 10rpx auto"
        }),
        e: common_vendor.p({
          text: common_vendor.unref(agentDetail).agentName,
          css: " width: 500rpx; text-align: center; font-size: 42rpx; font-weight: bold; color: #1a1a1a; margin: 45rpx auto 0 auto;"
        }),
        f: common_vendor.p({
          text: common_vendor.unref(agentDetail).agentDesc,
          css: " width: 450rpx; text-align: center; font-size: 26rpx; color: #666666; line-height: 38rpx;margin: 20rpx auto 0 auto"
        }),
        g: common_vendor.p({
          src: qrcode.value,
          css: "width: 200rpx; height: 200rpx; display: block; margin: 60rpx auto 0 auto; border-radius: 50%;"
        })
      } : {}, {
        h: common_vendor.o(($event) => path.value = $event),
        i: common_vendor.p({
          isCanvasToTempFilePath: true,
          pathType: "url",
          hidden: true,
          css: "width: 600rpx; height: 835rpx; background-image: url(https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/f567f2e6e0cb40c48b2510b888bd3b40.png); background-size: cover;"
        })
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-64980840"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/square/share.js.map
