{
	"pages": [ //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
		{
			"path": "pages/index/index",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false,
				"navigationBarBackgroundColor": "#FFFFFF"
			}
		},
		{
			"path": "pages/my/my",
			"style": {
				"navigationBarTitleText": "我的",
				"enablePullDownRefresh": false,
				"navigationBarBackgroundColor": "#FFFFFF"
			}
		},
		{
			"path": "pages/my/agent-list",
			"style": {
				"navigationBarTitleText": "我的智能体",
				"enablePullDownRefresh": false,
				"navigationBarBackgroundColor": "#FFFFFF"
			}
		},
		{
			"path": "pages/my/favorite-list",
			"style": {
				"navigationBarTitleText": "我的收藏",
				"enablePullDownRefresh": false,
				"navigationBarBackgroundColor": "#FFFFFF"
			}
		},
		{
			"path": "pages/my/vip-list",
			"style": {
				"navigationBarTitleText": "开通会员",
				"enablePullDownRefresh": false,
				"navigationBarBackgroundColor": "#FFFFFF"
			}
		},
		{
			"path": "pages/my/sub-list",
			"style": {
				"navigationBarTitleText": "专属订阅",
				"enablePullDownRefresh": false,
				"navigationBarBackgroundColor": "#FFFFFF"
			}
		},
		{
			"path": "pages/my/works-list",
			"style": {
				"navigationBarTitleText": "我的作品",
				"enablePullDownRefresh": false,
				"navigationBarBackgroundColor": "#FFFFFF"
			}
		},
		{
			"path": "pages/my/figure-list",
			"style": {
				"navigationBarTitleText": "我的IP形象",
				"enablePullDownRefresh": false,
				"navigationBarBackgroundColor": "#FFFFFF"
			}
		},
		{
			"path": "pages/my/video-create",
			"style": {
				"navigationBarTitleText": "创建视频",
				"enablePullDownRefresh": false,
				"navigationBarBackgroundColor": "#FFFFFF"
			}
		},
		{
			"path": "pages/my/video-progress",
			"style": {
				"navigationBarTitleText": "视频制作中",
				"enablePullDownRefresh": false,
				"navigationBarBackgroundColor": "#FFFFFF"
			}
		},
		{
			"path": "pages/my/video-complete",
			"style": {
				"navigationBarTitleText": "视频展示",
				"enablePullDownRefresh": false,
				"navigationBarBackgroundColor": "#FFFFFF"
			}
		},
		{
			"path": "pages/msg-list/msg",
			"style": {
				"navigationBarTitleText": "对话",
				"enablePullDownRefresh": false,
				"navigationBarBackgroundColor": "#FFFFFF"
			}
		},
		{
			"path": "pages/msg/index",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false,
				"navigationBarBackgroundColor": "#FFFFFF"
			}
		},
		{
			"path": "pages/square/square",
			"style": {
				"navigationBarTitleText": "广场",
				"enablePullDownRefresh": false,
				"navigationBarBackgroundColor": "#FFFFFF"
			}
		},
		{
			"path": "pages/square/search",
			"style": {
				"navigationBarTitleText": "搜索智能体",
				"enablePullDownRefresh": false,
				"navigationBarBackgroundColor": "#FFFFFF"
			}
		},
		{
			"path": "pages/square/detail",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false,
				"navigationBarBackgroundColor": "#FFFFFF"
			}
		},
		{
			"path": "pages/square/share",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false,
				"navigationBarBackgroundColor": "#FFFFFF"
			}
		},
		{
			"path": "pages/profile/index",
			"style": {
				"navigationBarTitleText": "个人资料",
				"enablePullDownRefresh": false,
				"navigationBarBackgroundColor": "#FFFFFF"
			}
		},
		{
			"path": "pages/create-agent/index",
			"style": {
				"navigationBarTitleText": "创建AI智能体",
				"enablePullDownRefresh": false,
				"navigationBarBackgroundColor": "#FFFFFF"
			}
		},
		{
			"path": "pages/create-agent/secret",
			"style": {
				"navigationBarTitleText": "秘钥管理",
				"enablePullDownRefresh": false,
				"navigationBarBackgroundColor": "#FFFFFF"
			}
		},
		{
			"path": "pages/rule/cz-index",
			"style": {
				"navigationBarTitleText": "创作规则",
				"enablePullDownRefresh": false,
				"navigationBarBackgroundColor": "#FFFFFF"
			}
		},
		{
			"path": "pages/rule/tg-index",
			"style": {
				"navigationBarTitleText": "推广规则",
				"enablePullDownRefresh": false,
				"navigationBarBackgroundColor": "#FFFFFF"
			}
		},
		{
			"path": "pages/rule/pt-index",
			"style": {
				"navigationBarTitleText": "平台规则",
				"enablePullDownRefresh": false,
				"navigationBarBackgroundColor": "#FFFFFF"
			}
		},
		{
			"path": "pages/raw-record/index",
			"style": {
				"navigationBarTitleText": "提现记录",
				"enablePullDownRefresh": false,
				"navigationBarBackgroundColor": "#FFFFFF"
			}
		},
		{
			"path": "pages/finance/index",
			"style": {
				"navigationBarTitleText": "财务流水",
				"enablePullDownRefresh": false,
				"navigationBarBackgroundColor": "#FFFFFF"
			}
		}
	],
	"tabBar": {
		"backgroundColor": "#ffffff",
		"color": "#999999",
		"selectedColor": "#333333",
		"list": [
			{
				"pagePath": "pages/index/index",
				"iconPath": "/static/tabbar/<EMAIL>",
				"selectedIconPath": "/static/tabbar/<EMAIL>",
				"text": "首页"
			},
			{
				"pagePath": "pages/msg-list/msg",
				"iconPath": "/static/tabbar/<EMAIL>",
				"selectedIconPath": "/static/tabbar/<EMAIL>",
				"text": "对话"
			},
			{
				"pagePath": "pages/square/square",
				"iconPath": "/static/tabbar/<EMAIL>",
				"selectedIconPath": "/static/tabbar/<EMAIL>",
				"text": "广场"
			},
			{
				"pagePath": "pages/my/my",
				"iconPath": "/static/tabbar/<EMAIL>",
				"selectedIconPath": "/static/tabbar/<EMAIL>",
				"text": "我的"
			}
		]
	},
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "思链IP智能体",
		"navigationBarBackgroundColor": "#F8F8F8",
		"backgroundColor": "#F8F8F8"
	},
	"uniIdRouter": {}
}