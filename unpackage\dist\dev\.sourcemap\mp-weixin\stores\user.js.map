{"version": 3, "file": "user.js", "sources": ["stores/user.js"], "sourcesContent": ["import {\r\n\tdefineStore\r\n} from 'pinia';\r\n\r\nexport const useUserStore = defineStore('user', {\r\n\tstate: () => {\r\n\t\treturn {\r\n\t\t\t//小程序APPid\r\n\t\t\twxappid: 'wx4658f2fe45d90c20',\r\n\t\t\t//商户guid\r\n\t\t\t// merchantGuid: 'e108201b02ae42e686bcc4c302cbbd11 正式2a5edd395c26499ab0d8966f8061573f', 测试938d1d20d13c4130a79f3d7590e62720\r\n\t\t\tmerchantGuid: '2a5edd395c26499ab0d8966f8061573f',\r\n\t\t\tuserToken: uni.getStorageSync('userToken') || '',\r\n\t\t\t// userToken: 'a87df0550b544c308772ec886461f5fc',\r\n\t\t\t//用户信息\r\n\t\t\tuserInfo: uni.getStorageSync('userInfo') || {\r\n\t\t\t\tchat_count: 0\r\n\t\t\t},\r\n\t\t\tmodalStatus: '',\r\n\t\t\tshareImg: '',\r\n\t\t\tappName: '思链IP智能体',\r\n\t\t\t// 临时存储要切换到的分类guid\r\n\t\t\ttargetCategoryGuid: '',\r\n\t\t\t//分佣邀请码\r\n\t\t\tinvitationCode: ''\r\n\t\t};\r\n\t},\r\n\t// 也可以这样定义\r\n\t// state: () => ({ count: 0 })\r\n\tactions: {\r\n\t\t//存储用户token\r\n\t\tset_user_token(token) {\r\n\t\t\tthis.userToken = token;\r\n\t\t\tuni.setStorageSync('userToken', token);\r\n\t\t},\r\n\t\t//存储用户信息\r\n\t\tset_user_info(userInfo) {\r\n\t\t\tthis.userInfo = userInfo;\r\n\t\t\tuni.setStorageSync('userInfo', userInfo);\r\n\t\t},\r\n\t\t//删除用户信息\r\n\t\tdelete_user_info() {\r\n\t\t\tthis.userToken = '';\r\n\t\t\tthis.userInfo = {};\r\n\t\t\tuni.removeStorageSync('userInfo');\r\n\t\t\tuni.removeStorageSync('userToken');\r\n\t\t},\r\n\t\t//设置目标分类guid\r\n\t\tset_target_category(categoryGuid) {\r\n\t\t\tthis.targetCategoryGuid = categoryGuid;\r\n\t\t},\r\n\t\t//清除目标分类guid\r\n\t\tclear_target_category() {\r\n\t\t\tthis.targetCategoryGuid = '';\r\n\t\t},\r\n\t\t//设置分佣邀请码\r\n\t\tset_invitation_code(invitationCode) {\r\n\t\t\tthis.invitationCode = invitationCode;\r\n\t\t}\r\n\t},\r\n});"], "names": ["defineStore", "uni"], "mappings": ";;AAIY,MAAC,eAAeA,cAAW,YAAC,QAAQ;AAAA,EAC/C,OAAO,MAAM;AACZ,WAAO;AAAA;AAAA,MAEN,SAAS;AAAA;AAAA;AAAA,MAGT,cAAc;AAAA,MACd,WAAWC,cAAG,MAAC,eAAe,WAAW,KAAK;AAAA;AAAA;AAAA,MAG9C,UAAUA,cAAG,MAAC,eAAe,UAAU,KAAK;AAAA,QAC3C,YAAY;AAAA,MACZ;AAAA,MACD,aAAa;AAAA,MACb,UAAU;AAAA,MACV,SAAS;AAAA;AAAA,MAET,oBAAoB;AAAA;AAAA,MAEpB,gBAAgB;AAAA,IACnB;AAAA,EACE;AAAA;AAAA;AAAA,EAGD,SAAS;AAAA;AAAA,IAER,eAAe,OAAO;AACrB,WAAK,YAAY;AACjBA,oBAAAA,MAAI,eAAe,aAAa,KAAK;AAAA,IACrC;AAAA;AAAA,IAED,cAAc,UAAU;AACvB,WAAK,WAAW;AAChBA,oBAAAA,MAAI,eAAe,YAAY,QAAQ;AAAA,IACvC;AAAA;AAAA,IAED,mBAAmB;AAClB,WAAK,YAAY;AACjB,WAAK,WAAW;AAChBA,0BAAI,kBAAkB,UAAU;AAChCA,0BAAI,kBAAkB,WAAW;AAAA,IACjC;AAAA;AAAA,IAED,oBAAoB,cAAc;AACjC,WAAK,qBAAqB;AAAA,IAC1B;AAAA;AAAA,IAED,wBAAwB;AACvB,WAAK,qBAAqB;AAAA,IAC1B;AAAA;AAAA,IAED,oBAAoB,gBAAgB;AACnC,WAAK,iBAAiB;AAAA,IACtB;AAAA,EACD;AACF,CAAC;;"}