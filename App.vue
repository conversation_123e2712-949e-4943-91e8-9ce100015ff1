<script>
import {
	mpLog<PERSON><PERSON><PERSON>,
	updateUserDefaultProfile<PERSON>pi,
	bindInvitation<PERSON><PERSON>,
	getInvitation<PERSON>ode<PERSON><PERSON>
} from '@/api/index.js';
import {
	useUserStore
} from '@/stores/user.js';

export default {
	globalData: {
		recorderManager: wx.getRecorderManager(),
		scene: 0,
	},
	onLaunch: function (options) {
		uni.getProvider({
			service: 'oauth',
			success: res => {
				try {
					const userStore = useUserStore();
					if (~res.provider.indexOf('weixin')) {
						uni.login({
							provider: 'weixin',
							success: async loginRes => {
								let code = loginRes.code;
								uni.showLoading({
									title: '登录中',
									mask: true,
								});
								try {
									mpLoginApi({
										jsCode: code,
										merchantGuid: userStore.merchantGuid,
										wxappid: userStore.wxappid,
									}).then(async (res) => {
										userStore.set_user_token(res.data.token);
										userStore.set_user_info(res.data.userInfo);
										uni.hideLoading();
										if (!userStore.invitationCode) {
											let invitRes = await getInvitationCode<PERSON>pi({
												merchantGuid: userStore.merchantGuid,
											});
											userStore.set_invitation_code(invitRes.data.inviteCode);
										}
										if (options.query.invite) {
											try {
												await bindInvitationApi({
													merchantGuid: userStore.merchantGuid,
													invitationCode: options.query.invite
												})
											} catch (error) {
											}
										}
										updateUserDefaultProfileApi()
									});

								} catch (error) {
									uni.hideLoading();
								}
							},
						});
					}
				} catch (error) {

				}

			},
		});
	},

	onShow: async function (options) {
		if (options.query.invite) {
			const userStore = useUserStore();
			if (userStore.userToken) {
				try {
					await bindInvitationApi({
						merchantGuid: userStore.merchantGuid,
						invitationCode: options.query.invite
					})
				} catch (error) {
				}
			}

		}
		// const userStore = useUserStore();
		// let res = await showConfigsApi({
		// 	merchantGuid: userStore.merchantGuid
		// })
		// userStore.shareImg = res.data.shareImg;
		// userStore.appName = res.data.zhanhuiName;
	},
	onHide: function () {
		console.log('App Hide');
	},
};
</script>

<style>
/*每个页面公共css */
</style>