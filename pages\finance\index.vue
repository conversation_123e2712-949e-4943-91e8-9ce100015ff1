<template>
  <view class="finance-flow-page">
    <!-- 记录列表 -->
    <view class="record-list">
      <view class="record-item" v-for="(item, index) in recordList" :key="index">
        <view class="record-content">
          <view class="main-info">
            <view class="amount-line">
              <text class="amount-label">变动金额：</text>
              <text class="amount" :class="getAmountClass(item.type)">{{ item.amount }}</text>
            </view>
            <view class="reason-line">
              <text class="reason-label">变动原因：</text>
              <text class="reason-text">{{ item.reason }}</text>
            </view>
            <view class="time-line" v-if="item.time">
              <text class="time-label">{{ item.timeLabel }}：</text>
              <text class="time-value">{{ item.time }}</text>
            </view>
          </view>
          <view class="balance-info">
            <text class="balance-text">变动后余额：{{ item.afterBalance }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" v-if="recordList.length === 0">
      <image src="@/static/common/empty_icon.png" class="empty-icon" mode="aspectFit" />
      <text class="empty-text">暂无财务流水</text>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'
import { onLoad } from '@dcloudio/uni-app'

// 财务流水列表
const recordList = ref([
  // {
  //   amount: '+2000.00',
  //   type: 'income', // income: 收入, expense: 支出
  //   reason: '用户订阅购买了您的智能体"我的修仙女友"',
  //   timeLabel: '',
  //   time: '',
  //   afterBalance: '2000'
  // },
  // {
  //   amount: '-2000.00',
  //   type: 'expense',
  //   reason: '用户订阅购买了您的智能体"我的修仙女友"',
  //   timeLabel: '变动时间',
  //   time: '2025-05-18 12:56:00',
  //   afterBalance: '0'
  // }
])

// 获取金额样式类
const getAmountClass = (type) => {
  return type === 'income' ? 'amount-income' : 'amount-expense'
}

// 加载财务流水列表
const loadRecordList = async () => {
  try {
    // 这里调用API获取财务流水
    // const res = await getFinanceFlowApi()
    // recordList.value = res.data
    console.log('加载财务流水')
  } catch (error) {
    console.error('加载财务流水失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    })
  }
}

onLoad(() => {
  loadRecordList()
})
</script>

<style lang="scss" scoped>
.finance-flow-page {
  background: #f5f5f5;
  min-height: 100vh;

  .record-list {
    padding: 32rpx;

    .record-item {
      background: #ffffff;
      border-radius: 16rpx;
      margin-bottom: 16rpx;
      overflow: hidden;

      .record-content {
        padding: 40rpx 32rpx;

        .main-info {
          margin-bottom: 24rpx;

          .amount-line {
            display: flex;
            align-items: center;
            margin-bottom: 16rpx;

            .amount-label {
              font-size: 28rpx;
              color: #999999;
              margin-right: 8rpx;
            }

            .amount {
              font-size: 32rpx;
              font-weight: 600;

              &.amount-income {
                color: #3478f6;
              }

              &.amount-expense {
                color: #3478f6;
              }
            }
          }

          .reason-line {
            display: flex;
            align-items: flex-start;
            margin-bottom: 16rpx;

            .reason-label {
              font-size: 28rpx;
              color: #999999;
              margin-right: 8rpx;
              flex-shrink: 0;
            }

            .reason-text {
              font-size: 28rpx;
              color: #333333;
              line-height: 1.4;
              flex: 1;
            }
          }

          .time-line {
            display: flex;
            align-items: center;

            .time-label {
              font-size: 28rpx;
              color: #999999;
              margin-right: 8rpx;
            }

            .time-value {
              font-size: 28rpx;
              color: #999999;
            }
          }
        }

        .balance-info {
          text-align: right;

          .balance-text {
            font-size: 28rpx;
            color: #999999;
          }
        }
      }
    }
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 200rpx 0;

    .empty-icon {
      width: 200rpx;
      height: 200rpx;
      margin-bottom: 40rpx;
      opacity: 0.6;
    }

    .empty-text {
      font-size: 28rpx;
      color: #999999;
    }
  }
}
</style>