<template>
	<view class="video-progress-page">
		<!-- 数字人形象 -->
		<view class="digital-person-container">
			<image class="person-image" :src="currentAvatar" mode="aspectFill" />
		</view>

		<!-- 相机图标 -->
		<view class="camera-container">
			<image class="camera-icon" src="/static/my/template_camera.png" mode="aspectFit" />
		</view>

		<!-- 进度文本 -->
		<view class="progress-text-container">
			<text class="progress-text">视频正在制作中<text class="progress-percent">{{ Math.round(progressPercent) }}%</text></text>
		</view>

		<!-- 进度条 -->
		<view class="progress-bar-container">
			<view class="progress-bar">
				<view class="progress-fill" :style="{ width: progressPercent + '%' }"></view>
			</view>
		</view>

		<!-- 底部提示文本 -->
		<view class="tip-text-container">
			<text class="tip-text">您的作品正在精心打磨中</text>
		</view>
	</view>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getVideoDetailApi } from '@/api/index.js'
import { useUserStore } from '@/stores/user.js'

// 用户store
const userStore = useUserStore()

// 当前显示的头像（从上一页传递过来）
const currentAvatar = ref('')

// 进度相关状态
const progressPercent = ref(0)
const videoStatus = ref(0)
const orderNo = ref('')

// 定时器
let progressTimer = null

// 获取视频详情
const getVideoDetail = async () => {
	try {
		const res = await getVideoDetailApi({
			merchantGuid: userStore.merchantGuid,
			orderNo: orderNo.value
		})

		if (res.code === 0) {
			const { progress, status } = res.data
			progressPercent.value = progress || 0
			videoStatus.value = status || 0

			// 当状态等于30或者进度大于等于100时表示创建成功
			if (status === 30 || progress >= 100) {
				clearInterval(progressTimer)
				setTimeout(() => {
					uni.showToast({
						title: '视频生成完成！',
						icon: 'success',
						duration: 2000
					})
					// 跳转到视频完成页面，传递orderNo参数
					setTimeout(() => {
						uni.redirectTo({
							url: `/pages/my/video-complete?orderNo=${orderNo.value}`
						})
					}, 2000)
				}, 500)
			}
		} else {
			console.error('获取视频详情失败:', res.msg)
		}
	} catch (error) {
		console.error('获取视频详情失败:', error)
	}
}

// 使用onLoad获取页面参数
onLoad((options) => {
	if (options.orderNo) {
		orderNo.value = options.orderNo
		currentAvatar.value = options.previewUrl

		// 立即获取一次视频详情
		getVideoDetail()

		// 启动轮询定时器，每3秒查询一次
		progressTimer = setInterval(getVideoDetail, 3000)
	} else {
		uni.showToast({
			title: '参数错误',
			icon: 'none'
		})
		setTimeout(() => {
			uni.navigateBack()
		}, 2000)
	}
})

onMounted(() => {
	// 页面挂载时的其他初始化操作
})

onUnmounted(() => {
	// 清理定时器
	if (progressTimer) {
		clearInterval(progressTimer)
	}
})
</script>

<style lang="scss" scoped>
.video-progress-page {
	background: #F5F5F5;
	min-height: 100vh;
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 0 32rpx;
	box-sizing: border-box;

	.digital-person-container {
		margin-top: 120rpx;
		width: 340rpx;
		height: 602rpx;
		border-radius: 20rpx;
		overflow: hidden;

		.person-image {
			width: 100%;
			height: 100%;
		}
	}

	.camera-container {
		margin-top: 94rpx;
		width: 110rpx;
		height: 110rpx;
		display: flex;
		align-items: center;
		justify-content: center;

		.camera-icon {
			width: 100%;
			height: 100%;
		}
	}

	.progress-text-container {
		margin-top: 15rpx;

		.progress-text {
			font-size: 28rpx;
			color: #333333;
			font-weight: 400;

			.progress-percent {
				color: #2A64F6;
			}
		}
	}

	.progress-bar-container {
		margin-top: 62rpx;
		width: 430rpx;

		.progress-bar {
			width: 100%;
			height: 8rpx;
			background: #ECECEC;
			border-radius: 4rpx;
			overflow: hidden;

			.progress-fill {
				height: 100%;
				background: #5380F2;
				border-radius: 4rpx;
				transition: width 0.3s ease;
			}
		}
	}

	.tip-text-container {
		margin-top: 40rpx;

		.tip-text {
			font-size: 28rpx;
			color: #999999;
		}
	}
}
</style>
