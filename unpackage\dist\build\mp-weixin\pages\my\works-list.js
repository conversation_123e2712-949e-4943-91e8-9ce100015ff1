"use strict";const e=require("../../common/vendor.js"),o=require("../../common/assets.js"),a=require("../../api/index.js"),r=require("../../stores/user.js");if(!Array){e.resolveComponent("z-paging")()}Math;const t={__name:"works-list",setup(t){const s=r.useUserStore(),n=e.ref([]),i=e.ref(null),l=e.ref(!1),u=e.ref([]),c=async(o,r)=>{try{const e=await a.worksListApi({merchantGuid:s.merchantGuid,page:o,pageSize:r});i.value.complete(e.data.list||[])}catch(t){console.error("获取作品列表失败:",t),e.index.showToast({title:"加载失败",icon:"none"}),i.value.complete(!1)}},d=()=>{l.value=!0,u.value=[]},v=()=>{l.value=!1,u.value=[]},p=()=>{0!==u.value.length?e.index.showModal({title:"确认删除",content:`确定要删除选中的${u.value.length}个作品吗？`,success:async o=>{if(o.confirm)try{const o=await a.deleteWorksApi({merchantGuid:s.merchantGuid,orderNos:u.value});u.value=[],l.value=!1,i.value.reload(),e.index.showToast({title:o.data.message,icon:"none"})}catch(r){console.error("删除作品失败:",r),e.index.showToast({title:"删除失败",icon:"none"})}}}):e.index.showToast({title:"请先选择要删除的作品",icon:"none"})};return(a,r)=>e.e({a:e.f(n.value,((a,r,t)=>e.e({a:a.previewUrl,b:l.value&&u.value.includes(a.orderNo)},l.value&&u.value.includes(a.orderNo)?{c:o._imports_1$2}:{},{d:a.orderNo,e:l.value&&u.value.includes(a.orderNo)?1:"",f:e.o((o=>(o=>{if(!l.value)return"fail"===o.workStatus?void e.index.showToast({title:"作品生成失败，无法查看",icon:"none"}):"doing"===o.workStatus?void e.index.showToast({title:"作品加速处理中，无法查看",icon:"none"}):void e.index.navigateTo({url:`/pages/my/video-complete?orderNo=${o.orderNo}`});const a=o.orderNo,r=u.value.indexOf(a);r>-1?u.value.splice(r,1):u.value.push(a)})(a)),a.orderNo)}))),b:e.sr(i,"256f4771-0",{k:"paging"}),c:e.o(c),d:e.o((e=>n.value=e)),e:e.p({"refresher-enabled":!0,auto:!0,modelValue:n.value}),f:!l.value},l.value?{i:e.o(v),j:e.o(p)}:{g:o._imports_3$1,h:e.o(d)})}},s=e._export_sfc(t,[["__scopeId","data-v-256f4771"]]);wx.createPage(s);
