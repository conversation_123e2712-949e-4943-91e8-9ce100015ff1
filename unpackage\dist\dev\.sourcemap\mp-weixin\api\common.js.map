{"version": 3, "file": "common.js", "sources": ["api/common.js"], "sourcesContent": ["import base from '@/config/config.js';\r\nexport const uploadTextFun = function(size = 20) {\r\n\tlet uploadFileSize = 1024 * 1024 * size;\r\n\treturn new Promise((resolve, reject) => {\r\n\t\t// #ifdef MP-WEIXIN\r\n\t\t//pptx ppt\r\n\t\twx.chooseMessageFile({\r\n\t\t\tcount: 1,\r\n\t\t\ttype: 'file',\r\n\t\t\textension: ['.word', '.pdf', '.md', '.txt', '.xlsx', '.docx', '.doc', '.xls', 'word', 'pdf', 'md',\r\n\t\t\t\t'txt', 'xlsx', 'docx', 'doc', 'xls'\r\n\t\t\t],\r\n\t\t\tsuccess: (res) => {\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '文件上传中',\r\n\t\t\t\t\tmask: true,\r\n\t\t\t\t});\r\n\t\t\t\tif (res.tempFiles[0].size > uploadFileSize) {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '上传文件过大',\r\n\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\tduration: 2000,\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}, 200)\r\n\t\t\t\t\treject();\r\n\t\t\t\t}\r\n\t\t\t\tlet txtSrc = res.tempFiles[0].path;\r\n\t\t\t\tlet url = `${base.baseUrl}user/api.userinfo/uploadTxt`;\r\n\t\t\t\tlet uploadFileRes = uni.uploadFile({\r\n\t\t\t\t\t// url: 'https://ai-api.deepcity.cn/user/api.userinfo/uploadTxt',\r\n\t\t\t\t\turl,\r\n\t\t\t\t\tname: 'txt',\r\n\t\t\t\t\tfilePath: txtSrc,\r\n\t\t\t\t\tsuccess: sucres => {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\tlet txt = JSON.parse(sucres.data);\r\n\t\t\t\t\t\tlet data = {\r\n\t\t\t\t\t\t\tname: res.tempFiles[0].name,\r\n\t\t\t\t\t\t\tpath: txt\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tresolve(data)\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: err => {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\treject(err);\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tfail: (err) => {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '取消选择',\r\n\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\tduration: 2000,\r\n\t\t\t\t});\r\n\t\t\t\treject(err);\r\n\t\t\t},\r\n\t\t});\r\n\t\t// #endif\r\n\t\t// #ifdef H5\r\n\t\tuni.chooseFile({\r\n\t\t\tcount: 1,\r\n\t\t\textension: ['.txt'],\r\n\t\t\tsuccess: (res) => {\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '文件上传中',\r\n\t\t\t\t\tmask: true,\r\n\t\t\t\t});\r\n\t\t\t\tlet txtSrc = res.tempFiles[0].path;\r\n\t\t\t\tlet url = `${base.baseUrl}user/api.userinfo/uploadTxt`;\r\n\t\t\t\tlet uploadFileRes = uni.uploadFile({\r\n\t\t\t\t\t// url: 'https://ai-api.deepcity.cn/user/api.userinfo/uploadTxt',\r\n\t\t\t\t\turl,\r\n\t\t\t\t\tname: 'txt',\r\n\t\t\t\t\tfilePath: txtSrc,\r\n\t\t\t\t\tsuccess: res => {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\tlet txt = JSON.parse(res.data);\r\n\t\t\t\t\t\tresolve(txt)\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: err => {\r\n\t\t\t\t\t\treturn reject(err);\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tfail: (err) => {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '取消选择',\r\n\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\tduration: 2000,\r\n\t\t\t\t});\r\n\t\t\t\treturn reject(err);\r\n\t\t\t},\r\n\t\t});\r\n\t\t// #endif\r\n\t})\r\n\r\n}\r\n// let uploadFileRes = uni.uploadFile({\r\n//           url: 'https://ai-api.deepcity.cn/user/api.userinfo/uploadVideo',\r\n//           name: 'video',\r\n//           fileType: 'video',\r\n//           filePath: videoSrc,\r\n//           success: res => {\r\n//             uni.hideLoading();\r\n//             uni.showToast({\r\n//               title: '上传成功',\r\n//               icon: 'success',\r\n//               duration: 2000\r\n//             });\r\n//             let video = JSON.parse(res.data);\r\n//             resolve(video)\r\n//           },\r\n//           fail: err => {\r\n//             return reject(err);\r\n//             uni.hideLoading();\r\n//           }\r\n//         });\r\nexport const miniPay = function(data) {\r\n\treturn new Promise((resolve, reject) => {\r\n\t\tconst miniPayData = {\r\n\t\t\tnonceStr: data.nonceStr,\r\n\t\t\tpackage: data.package,\r\n\t\t\tpaySign: data.paySign,\r\n\t\t\tsignType: data.signType,\r\n\t\t\ttimeStamp: data.timeStamp,\r\n\t\t};\r\n\t\tuni.requestPayment({\r\n\t\t\t...miniPayData,\r\n\t\t\tsuccess: function(wxPayRes) {\r\n\t\t\t\tresolve(wxPayRes);\r\n\t\t\t},\r\n\t\t\tfail: function(wxPayErr) {\r\n\t\t\t\treject({\r\n\t\t\t\t\tmsg: '支付失败',\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tcancel: function() {\r\n\t\t\t\treject({\r\n\t\t\t\t\tmsg: '取消支付',\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t});\r\n\t});\r\n};\r\n\r\nexport const updataVideoFun = async function(data) {\r\n\ttry {\r\n\t\tlet url = `${base.baseUrl}user/api.userinfo/uploadVideo`;\r\n\t\tlet uploadFileRes = uni.uploadFile({\r\n\t\t\t// url: 'https://ai-api.deepcity.cn/user/api.userinfo/uploadVideo',\r\n\t\t\turl,\r\n\t\t\tname: 'video',\r\n\t\t\tfileType: 'video',\r\n\t\t\tfilePath: data\r\n\t\t});\r\n\t\tif (uploadFileRes.statusCode < 200 || uploadFileRes.statusCode > 300) {\r\n\t\t\treturn Promise.reject({\r\n\t\t\t\terrMsg: 'statusCode is not 200 series',\r\n\t\t\t});\r\n\t\t} else {\r\n\t\t\treturn uploadFileRes;\r\n\t\t}\r\n\t} catch (e) {\r\n\t\tuni.hideLoading();\r\n\t\treturn Promise.reject(e);\r\n\t}\r\n};\r\n\r\nexport const updataFileFun = async function(data) {\r\n\ttry {\r\n\t\tlet url = `${base.baseUrl}user/api.userinfo/uploadImg`;\r\n\t\tlet uploadFileRes = uni.uploadFile({\r\n\t\t\t// url: 'https://ai-api.deepcity.cn/user/api.userinfo/uploadImg',\r\n\t\t\turl,\r\n\t\t\tname: 'img',\r\n\t\t\tfilePath: data,\r\n\t\t\theader: {\r\n\t\t\t\t'content-type': 'multipart/form-data',\r\n\t\t\t},\r\n\t\t});\r\n\t\tif (uploadFileRes.statusCode < 200 || uploadFileRes.statusCode > 300) {\r\n\t\t\treturn Promise.reject({\r\n\t\t\t\terrMsg: 'statusCode is not 200 series',\r\n\t\t\t});\r\n\t\t} else {\r\n\t\t\treturn uploadFileRes;\r\n\t\t}\r\n\t} catch (e) {\r\n\t\tuni.hideLoading();\r\n\t\treturn Promise.reject(e);\r\n\t}\r\n};\r\n\r\nexport const uploadImageMp = function(count = 1) {\r\n\treturn new Promise((resolve, reject) => {\r\n\t\tuni.chooseMedia({\r\n\t\t\tcount: count,\r\n\t\t\tmediaType: ['image'],\r\n\t\t\tsourceType: ['album'],\r\n\t\t\tsizeType: ['compressed'],\r\n\t\t\tsuccess: async (res) => {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tuni.showLoading({\r\n\t\t\t\t\t\ttitle: '上传中',\r\n\t\t\t\t\t\tmask: true,\r\n\t\t\t\t\t})\r\n\t\t\t\t\tlet MPres = await updataFileFun(res.tempFiles[0].tempFilePath);\r\n\t\t\t\t\tuni.hideLoading()\r\n\t\t\t\t\tlet data = JSON.parse(MPres.data);\r\n\t\t\t\t\tresolve(data)\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\t// uni.$u.toast(error.msg ? error.msg : '上传失败');\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: error.msg ? error.msg : '上传失败',\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\tduration: 2000,\r\n\t\t\t\t\t});\r\n\t\t\t\t\tuni.hideLoading()\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tfail(err) {\r\n\t\t\t\t// uni.$u.toast(err.msg ? err.msg : '上传失败');\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: error.msg ? error.msg : '上传失败',\r\n\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\tduration: 2000,\r\n\t\t\t\t});\r\n\t\t\t\treject(err)\r\n\t\t\t}\r\n\t\t});\r\n\t})\r\n}"], "names": ["uni", "base"], "mappings": ";;;AAwHY,MAAC,UAAU,SAAS,MAAM;AACrC,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACvC,UAAM,cAAc;AAAA,MACnB,UAAU,KAAK;AAAA,MACf,SAAS,KAAK;AAAA,MACd,SAAS,KAAK;AAAA,MACd,UAAU,KAAK;AAAA,MACf,WAAW,KAAK;AAAA,IACnB;AACEA,kBAAAA,MAAI,eAAe;AAAA,MAClB,GAAG;AAAA,MACH,SAAS,SAAS,UAAU;AAC3B,gBAAQ,QAAQ;AAAA,MAChB;AAAA,MACD,MAAM,SAAS,UAAU;AACxB,eAAO;AAAA,UACN,KAAK;AAAA,QACV,CAAK;AAAA,MACD;AAAA,MACD,QAAQ,WAAW;AAClB,eAAO;AAAA,UACN,KAAK;AAAA,QACV,CAAK;AAAA,MACD;AAAA,IACJ,CAAG;AAAA,EACH,CAAE;AACF;AAyBY,MAAC,gBAAgB,eAAe,MAAM;AACjD,MAAI;AACH,QAAI,MAAM,GAAGC,cAAI,KAAC,OAAO;AACzB,QAAI,gBAAgBD,cAAG,MAAC,WAAW;AAAA;AAAA,MAElC;AAAA,MACA,MAAM;AAAA,MACN,UAAU;AAAA,MACV,QAAQ;AAAA,QACP,gBAAgB;AAAA,MAChB;AAAA,IACJ,CAAG;AACD,QAAI,cAAc,aAAa,OAAO,cAAc,aAAa,KAAK;AACrE,aAAO,QAAQ,OAAO;AAAA,QACrB,QAAQ;AAAA,MACZ,CAAI;AAAA,IACJ,OAAS;AACN,aAAO;AAAA,IACP;AAAA,EACD,SAAQ,GAAG;AACXA,kBAAG,MAAC,YAAW;AACf,WAAO,QAAQ,OAAO,CAAC;AAAA,EACvB;AACF;;;"}