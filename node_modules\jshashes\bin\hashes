#!/usr/bin/env node

var Hashes = require('../hashes'),
    version = require('../package.json').version;

var args = process.argv,
    command = args[0],
    usage, options;

usage = [
    ' jshashes ' + version
  , ' '
  , ' Usage:'
  , '   hashes [option] [string]'
  , ' '
  , ' Options:'
  , '   md5-hex'
  , '   md5-b64'
  , '   sha1-hex'
  , '   sha1-b64'
  , '   sha256-hex'
  , '   sha256-b64'
  , '   sha512-hex'
  , '   sha512-b64'
  , '   rmd160-hex'
  , '   rmd160-b64'
  , '   b64-enc'
  , '   b64-dec'
  , '   crc32'
  , ' '
  , ' Help:'
  , '   -h , --help, help'
  , ' '
  , ' Current version:'
  , '   -v , --version, version'
  , ' '
  , ' Examples:'
  , '   $ hashes sha1-hex "sample text!"'
  , ' '
].join('\n');

options = [
   'md5-hex'
  ,'md5-b64'
  ,'sha1-hex'
  ,'sha1-b64'
  ,'sha256-hex'
  ,'sha256-b64'
  ,'sha512-hex'
  ,'sha512-b64'
  ,'rmd160-hex'
  ,'rmd160-b64'
  ,'b64-enc'
  ,'b64-dec'
  ,'crc32'
];

function die (str) {
  console.log(str);
  process.exit();
}

function procesAlgorithm() {
  var algorithm = args[0].split('-')[0].toUpperCase(),
      encoding = args[0].split('-')[1],
      string = args.slice(1).join(' '),
      instance, output;

  if (algorithm === 'B64') {
    algorithm = 'Base64';
    encoding = encoding === 'dec' ? 'decode' : 'encode';
  }

  if (Hashes.hasOwnProperty(algorithm)) {
    if (algorithm === 'CRC32') {
      output = Hashes[algorithm](string);
    } else {
      instance = new Hashes[algorithm];
      if (instance.hasOwnProperty(encoding)) {
        output = instance[encoding](string);
      }
    }
  } else {
    output = 'Algorithm not supported. Type help to see the list of available options.'
  }
  return output;
}

if (command && command.indexOf('node') !== -1) {
    args = args.slice(2);
    command = args[0];
}

if (command === '-v' || command === '--version' || command === 'version') {
  die(version);
}
if (command === '-h' || command === '--help' || command === 'help' || args.length < 2 || options.indexOf(command) === -1) {
  die(usage);
}

die(procesAlgorithm());
