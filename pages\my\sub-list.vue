<template>
  <view class="sub-page">
    <!-- 专属订阅说明 -->
    <view class="sub-intro">
      <text class="intro-title">专属订阅</text>

      <!-- 专属订阅1 -->
      <view class="intro-item">
        <view class="icon-box">
          <image class="icon" :src="icon1"></image>
        </view>
        <view class="intro-content">
          <text class="intro-label">专属订阅1:</text>
          <text class="intro-desc">{{ rule.rule1 }}</text>
        </view>
      </view>

      <!-- 专属订阅2 -->
      <view class="intro-item">
        <view class="icon-box">
          <image class="icon" :src="icon2"></image>
        </view>
        <view class="intro-content">
          <text class="intro-label">专属订阅2:</text>
          <text class="intro-desc">{{ rule.rule2 }}</text>
        </view>
      </view>

      <!-- 使用规则 -->
      <view class="intro-item">
        <view class="icon-box">
          <image class="icon" :src="icon3"></image>
        </view>
        <view class="intro-content">
          <text class="intro-label">使用规则:</text>
          <text class="intro-desc">{{ rule.rule_notice }}</text>
        </view>
      </view>
    </view>

    <!-- 用户信息卡片 -->
    <view class="user-card">
      <view class="user-avatar">
        <image class="avatar-img" :src="userInfo.headImgUrl || defaultAvatar" mode="aspectFill" />
      </view>
      <view class="user-info">
        <text class="user-name">{{ userInfo.nickname || '用户Aric' }}</text>
        <text class="user-points">我的通用AI点数：{{ userInfo.chat_count }}</text>
      </view>
      <!-- <view class="buy-points-btn">
        购买点数
      </view> -->
    </view>

    <!-- 创作者订阅 -->
    <view class="subscription-section">
      <view class="section-header">
        <text class="section-title">创作者订阅：{{ creatorSubscriptions.length }}个</text>
        <view class="subscribe-btn" @tap="showCreatorModal">
          订阅创作者
        </view>
      </view>
    </view>

    <!-- 订阅创作者弹窗 -->
    <view v-if="showSubscribeModal" class="modal-overlay" @tap="closeModal" catchtouchmove="true">
      <view class="modal-content" @tap.stop>
        <!-- 关闭按钮 -->
        <view class="modal-close" @tap="closeModal">
          <text class="close-icon">×</text>
        </view>

        <!-- 标题 -->
        <view class="modal-header">
          <text class="modal-title">专属订阅</text>
        </view>

        <!-- 创作者列表 -->
        <scroll-view class="creator-list" scroll-y>
          <view v-for="creator in creatorList" :key="creator.guid" class="creator-item">
            <!-- 创作者头像 -->
            <view class="creator-avatar">
              <image class="avatar-img" :src="creator.creatorAvatar" mode="aspectFill" />
            </view>

            <!-- 创作者信息 -->
            <view class="creator-info">
              <text class="creator-name">{{ creator.creatorName }}</text>
              <text class="creator-desc">{{ creator.creatorDesc }}</text>

              <!-- 统计信息 -->
              <view class="creator-stats">
                <text class="stats-text">{{ creator.agentCount }}个</text>
                <text class="stats-label">已上架智能体个数</text>
                <text class="stats-price">¥{{ creator.subscriptionPriceYuan }}</text>
                <text class="stats-label">订阅金额</text>
              </view>
            </view>
            <!-- 操作按钮 -->
            <view class="creator-actions">
              <view class="action-btn detail-btn" @tap="viewCreatorDetail(creator)">
                查看详情
              </view>
              <view class="action-btn subscribe-btn"
                :class="{ 'subscribed': creator.isSubscribed, 'ios-disabled': isIos }"
                @tap="handleCreatorSubscribe(creator)">
                <text class="btn-text">{{ creator.isSubscribed ? '已订阅' : (isIos ? 'IOS暂不支持' : '订阅') }}</text>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
    </view>

    <!-- 分类订阅 -->
    <!-- <view class="subscription-section">
      <view class="section-header">
        <text class="section-title">分类订阅：{{ categorySubscriptions.length }}个</text>
        <view class="subscribe-btn">
          订阅分类
        </view>
      </view>
    </view> -->
  </view>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { onLoad, onShow } from '@dcloudio/uni-app'
import { getSubscriptionListApi, querySubscriptionOrderApi, subscribeCreatorApi, getUserInfoApi, getMySubscriptionListApi, getSubscriptionRuleApi } from '@/api/index.js'
import { useUserStore } from '@/stores/user.js'
import { miniPay } from '@/api/common.js'

const userStore = useUserStore()

// 默认头像
const defaultAvatar = 'https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/c4d5b3ada53740d5b862f274ce48ef0c.png'

const icon3 = 'https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/46348d225bb54770a614dba856c5193e.png';
const icon2 = 'https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/77e643ee1cd3492ba370158addccd825.png';
const icon1 = 'https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/84d756efe28f4116a271e357f48d04e6.png'

// 用户信息
const userInfo = reactive({
  headImgUrl: '',
  nickname: '',
  chat_count: 0
})

// 创作者订阅列表
const creatorSubscriptions = ref([])

// 分类订阅列表
const categorySubscriptions = ref([])

// 弹窗相关状态
const showSubscribeModal = ref(false)
const creatorList = ref([])
const isIos = ref(false)
const queryStatusNum = ref(0)

// 获取用户信息
const getUserInfo = async () => {
  try {
    const res = await getUserInfoApi({
      merchantGuid: userStore.merchantGuid
    })
    if (res.code === 0) {
      Object.assign(userInfo, res.data)
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
  }
}

// 获取用户订阅信息
// const getUserSubscription = async () => {
//   try {
//     const res = await getSubscriptionListApi({
//       merchantGuid: userStore.merchantGuid
//     })
//     if (res.code === 0) {
//       creatorSubscriptions.value = res.data.creators || []
//       // categorySubscriptions.value = res.data.categorySubscriptions || []
//     }
//   } catch (error) {
//     console.error('获取订阅信息失败:', error)
//   }
// }

// 获取创作者列表
const getCreatorList = async () => {
  try {
    const res = await getSubscriptionListApi({
      merchantGuid: userStore.merchantGuid
    })
    if (res.code === 0) {
      creatorList.value = res.data.creators
    }
  } catch (error) {
    console.error('获取创作者列表失败:', error)
  }
}
//获取规则
const rule = reactive({
  rule1: '',
  rule2: '',
  rule_notice: ''
})
const getSubscriptionRule = async () => {
  try {
    const res = await getSubscriptionRuleApi({
      merchantGuid: userStore.merchantGuid
    })
    if (res.code === 0) {
      rule.rule1 = res.data.zhuanshu.rule1
      rule.rule2 = res.data.zhuanshu.rule2
      rule.rule_notice = res.data.zhuanshu.rule_notice
    }
  } catch (error) {
    console.error('获取规则失败:', error)
  }
}
// 显示订阅创作者弹窗
const showCreatorModal = async () => {
  uni.showToast({
    title: '暂未开放',
    icon: 'none'
  })
  return

  showSubscribeModal.value = true
  await getCreatorList()
}

// 关闭弹窗
const closeModal = () => {
  showSubscribeModal.value = false
}

// 查看创作者详情
const viewCreatorDetail = (creator) => {
  // 这里可以跳转到创作者详情页面或显示详情弹窗
  console.log('查看创作者详情:', creator)
}

// 订阅创作者
const handleCreatorSubscribe = async (creator) => {
  if (isIos.value) {
    uni.showToast({
      title: 'IOS暂不支持',
      icon: 'none'
    })
    return
  }

  if (creator.isSubscribed) {
    uni.showToast({
      title: '已订阅该创作者',
      icon: 'none'
    })
    return
  }

  if (!userStore.userToken) {
    uni.showToast({
      title: '请先登录',
      icon: 'none'
    })
    return
  }

  try {
    uni.showLoading({
      title: '正在创建订单...',
      mask: true
    })

    // 创建订阅订单
    const payInfo = await subscribeCreatorApi({
      merchantGuid: userStore.merchantGuid,
      creatorGuid: creator.guid,
      payEnv: 'xcx'
    })

    uni.hideLoading()

    // 调用微信支付
    miniPay(payInfo.data.payInfo).then(
      async () => {
        queryPayStatus(payInfo.data.orderNo, queryStatusNum.value)
      },
      (res) => {
        uni.showToast({
          title: res.msg || '支付失败',
          icon: 'none'
        })
      }
    )
  } catch (error) {
    uni.hideLoading()
    console.error('创建订单失败:', error)
    uni.showToast({
      title: error.message || '创建订单失败',
      icon: 'none'
    })
  }
}

// 查询支付状态
const queryPayStatus = async (orderNo, number) => {
  number++
  try {
    const orderInfo = await querySubscriptionOrderApi({
      orderNo
    })

    if (orderInfo.data.isPaid) {
      uni.showToast({
        title: '订阅成功',
        icon: 'success'
      })
      // 刷新数据
      // getUserSubscription()
      getCreatorList()
      closeModal()
    } else {
      if (number > 12) {
        uni.showToast({
          title: '支付超时',
          icon: 'none'
        })
      } else {
        setTimeout(() => {
          queryPayStatus(orderNo, number)
        }, 2000)
      }
    }
  } catch (error) {
    uni.showToast({
      title: error.msg || '查询支付状态失败',
      icon: 'none'
    })
  }
}

onLoad(() => {
  // 检测系统类型
  const systemInfo = uni.getSystemInfoSync()
  if (systemInfo.osName === 'ios') {
    isIos.value = true
  }

  if (userStore.userToken) {
    getUserInfo()
    getSubscriptionRule()
    // getUserSubscription()
  }
})

onShow(() => {
  if (userStore.userToken) {
    getUserInfo()
    getSubscriptionRule()
    // getUserSubscription()
  }
})
</script>

<style lang="scss" scoped>
.sub-page {
  background: #F8F9FA;
  padding: 32rpx 32rpx 0;
  min-height: 100vh;
}

.sub-intro {
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
}

.intro-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 32rpx;
  display: block;
}

.intro-item {
  display: flex;
  margin-bottom: 32rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.icon-box {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  margin-right: 24rpx;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;

  .icon {
    width: 60rpx;
    height: 60rpx;
    display: block;
  }

}

.intro-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.intro-label {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
}

.intro-desc {
  font-size: 24rpx;
  color: #666666;
  line-height: 1.5;
}

.user-card {
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  display: flex;
  align-items: center;
}

.user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.avatar-img {
  width: 100%;
  height: 100%;
}

.user-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.user-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
}

.user-points {
  font-size: 24rpx;
  color: #1E90FF;
}

.buy-points-btn {
  background: #1E90FF;
  border-radius: 40rpx;
  padding: 16rpx 32rpx;
  font-size: 24rpx;
  color: #FFFFFF;
  line-height: 1;
}

.subscription-section {
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}

.subscribe-btn {
  background: #333333;
  border-radius: 40rpx;
  padding: 16rpx 32rpx;
  font-size: 24rpx;
  color: #FFFFFF;
  line-height: 1;
}

// 弹窗样式
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
}

.modal-content {
  width: 90%;
  height: 600rpx;
  background: #FFFFFF;
  border-radius: 20rpx;
  position: relative;
  display: flex;
  flex-direction: column;
}

.modal-close {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.close-icon {
  font-size: 40rpx;
  color: #999999;
  font-weight: 300;
}

.modal-header {
  padding: 40rpx 32rpx 20rpx;
  text-align: center;
  border-bottom: 1rpx solid #F0F0F0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.creator-list {
  flex: 1;
  padding: 20rpx 0;
}

.creator-item {
  display: flex;
  padding: 32rpx;
  border-bottom: 1rpx solid #F8F9FA;

  &:last-child {
    border-bottom: none;
  }
}

.creator-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.avatar-img {
  width: 100%;
  height: 100%;
}

.creator-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.creator-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
}

.creator-desc {
  font-size: 24rpx;
  color: #666666;
  line-height: 1.4;
  margin-bottom: 16rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.creator-stats {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  flex-wrap: wrap;
}

.stats-text {
  font-size: 24rpx;
  font-weight: 600;
  color: #333333;
  margin-right: 8rpx;
}

.stats-label {
  font-size: 20rpx;
  color: #999999;
  margin-right: 32rpx;
}

.stats-price {
  font-size: 28rpx;
  font-weight: 600;
  color: #1E90FF;
  margin-right: 8rpx;
}

.creator-actions {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  padding: 12rpx 24rpx;
  border-radius: 40rpx;
  font-size: 24rpx;
  text-align: center;
  line-height: 1;
}

.detail-btn {
  background: #F8F9FA;
  color: #666666;
  border: 1rpx solid #E5E5E5;
}

.subscribe-btn {
  background: #1E90FF;
  color: #FFFFFF;

  &.subscribed {
    background: #E5E5E5;
    color: #999999;
  }

  &.ios-disabled {
    background: #999999;
    color: #FFFFFF;
  }
}

.btn-text {
  font-size: 24rpx;
}
</style>