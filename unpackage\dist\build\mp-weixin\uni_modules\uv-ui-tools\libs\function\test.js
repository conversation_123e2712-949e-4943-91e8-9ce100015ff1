"use strict";function t(t){return/^[\+-]?(\d+\.?\d*|\.\d+|\d\.\d+e\+\d+)$/.test(t)}function n(t){switch(typeof t){case"undefined":return!0;case"string":if(0==t.replace(/(^[ \t\n\r]*)|([ \t\n\r]*$)/g,"").length)return!0;break;case"boolean":if(!t)return!0;break;case"number":if(0===t||isNaN(t))return!0;break;case"object":if(null===t||0===t.length)return!0;for(const n in t)return!1;return!0}return!1}function e(t){return"function"==typeof Array.isArray?Array.isArray(t):"[object Array]"===Object.prototype.toString.call(t)}function r(t){return"[object Object]"===Object.prototype.toString.call(t)}function u(t){return"function"==typeof t}const o=Object.freeze(Object.defineProperty({__proto__:null,amount:function(t){return/^[1-9]\d*(,\d{3})*(\.\d{1,2})?$|^0\.\d{1,2}$/.test(t)},array:e,carNo:function(t){const n=/^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}(([0-9]{5}[DF]$)|([DF][A-HJ-NP-Z0-9][0-9]{4}$))/,e=/^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳]{1}$/;return 7===t.length?e.test(t):8===t.length&&n.test(t)},chinese:function(t){return/^[\u4e00-\u9fa5]+$/gi.test(t)},code:function(t,n=6){return new RegExp(`^\\d{${n}}$`).test(t)},contains:function(t,n){return t.indexOf(n)>=0},date:function(n){return!!n&&(t(n)&&(n=+n),!/Invalid|NaN/.test(new Date(n).toString()))},dateISO:function(t){return/^\d{4}[\/\-](0?[1-9]|1[012])[\/\-](0?[1-9]|[12][0-9]|3[01])$/.test(t)},digits:function(t){return/^\d+$/.test(t)},email:function(t){return/^\w+((-\w+)|(\.\w+))*\@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*\.[A-Za-z0-9]+$/.test(t)},empty:n,enOrNum:function(t){return/^[0-9a-zA-Z]*$/g.test(t)},func:u,idCard:function(t){return/^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}([0-9]|X)$/.test(t)},image:function(t){const n=t.split("?")[0];return/\.(jpeg|jpg|gif|png|svg|webp|jfif|bmp|dpg)/i.test(n)},jsonString:function(t){if("string"==typeof t)try{const n=JSON.parse(t);return!("object"!=typeof n||!n)}catch(n){return!1}return!1},landline:function(t){return/^\d{3,4}-\d{7,8}(-\d{3,4})?$/.test(t)},letter:function(t){return/^[a-zA-Z]*$/.test(t)},mobile:function(t){return/^1([3589]\d|4[5-9]|6[1-2,4-7]|7[0-8])\d{8}$/.test(t)},number:t,object:r,promise:function(t){return r(t)&&u(t.then)&&u(t.catch)},range:function(t,n){return t>=n[0]&&t<=n[1]},rangeLength:function(t,n){return t.length>=n[0]&&t.length<=n[1]},regExp:function(t){return t&&"[object RegExp]"===Object.prototype.toString.call(t)},string:function(t){return"string"==typeof t},url:function(t){return/^((https|http|ftp|rtsp|mms):\/\/)(([0-9a-zA-Z_!~*'().&=+$%-]+: )?[0-9a-zA-Z_!~*'().&=+$%-]+@)?(([0-9]{1,3}.){3}[0-9]{1,3}|([0-9a-zA-Z_!~*'()-]+.)*([0-9a-zA-Z][0-9a-zA-Z-]{0,61})?[0-9a-zA-Z].[a-zA-Z]{2,6})(:[0-9]{1,4})?((\/?)|(\/[0-9a-zA-Z_!~*'().;?:@&=+$,%#-]+)+\/?)$/.test(t)},video:function(t){return/\.(mp4|mpg|mpeg|dat|asf|avi|rm|rmvb|mov|wmv|flv|mkv|m3u8)/i.test(t)}},Symbol.toStringTag,{value:"Module"}));exports.array=e,exports.empty=n,exports.number=t,exports.test=o;
