"use strict";const e=require("../request/request.js");exports.agentDetailApi=t=>e.request({url:"useragent/api.AiAgent/agentDetail",method:"POST",data:t}),exports.bindInvitationApi=t=>e.request({url:"useragent/api.AiAgent/bindInvitation",method:"POST",data:t}),exports.buyChatGoodsApi=t=>e.request({url:"square/api.chatGoods/buy",method:"POST",data:t}),exports.calculatePointsApi=t=>e.request({url:"useragent/api.ChanjingDigitalHuman/calculatePoints",method:"POST",data:t}),exports.cancelCollectMessageApi=t=>e.request({url:"useragent/api.AiAgentChat/uncollectMessage",method:"POST",data:t}),exports.collectMessageApi=t=>e.request({url:"useragent/api.AiAgentChat/collectMessage",method:"POST",data:t}),exports.commonPersonListApi=t=>e.request({url:"useragent/api.ChanjingDigitalHuman/getPersonList",method:"POST",data:t}),exports.createAgentApi=t=>e.request({url:"useragent/api.AiAgent/create",method:"POST",data:t}),exports.createPersonkApi=t=>e.request({url:"useragent/api.ChanjingDigitalHuman/createPerson",method:"POST",data:t}),exports.createPurchaseOrderApi=t=>e.request({url:"useragent/api.AiAgentSquare/createPurchaseOrder",method:"POST",data:t}),exports.createVideoTaskApi=t=>e.request({url:"useragent/api.ChanjingDigitalHuman/createVideo",method:"POST",data:t}),exports.createVipOrderApi=t=>e.request({url:"useragent/api.AiAgentMembership/purchasePackage",method:"POST",data:t}),exports.deleteAgentApi=t=>e.request({url:"useragent/api.AiAgent/delete",method:"POST",data:t}),exports.deleteAllMessagesApi=t=>e.request({url:"useragent/api.AiAgentChat/deleteAllMessages",method:"POST",data:t}),exports.deletePersonApi=t=>e.request({url:"useragent/api.ChanjingDigitalHuman/deletePerson",method:"POST",data:t}),exports.deleteSessionApi=t=>e.request({url:"useragent/api.AiAgentChat/deleteSession",method:"POST",data:t}),exports.deleteWorksApi=t=>e.request({url:"useragent/api.ChanjingDigitalHuman/batchDeleteWorks",method:"POST",data:t}),exports.generateAvatarApi=t=>e.request({url:"useragent/api.AiAgent/generateAvatar",method:"POST",data:t}),exports.generateMiniCodeApi=t=>e.request({url:"useragent/api.AiAgent/generateMiniCode",method:"POST",data:t}),exports.getAgentListApi=t=>e.request({url:"useragent/api.AiAgentSquare/agentList",method:"POST",data:t}),exports.getBannerListApi=t=>e.request({url:"merchant/api.index/bannerList",method:"POST",data:t}),exports.getCategoryListApi=t=>e.request({url:"useragent/api.AiAgentPublic/categoryList",method:"POST",data:t}),exports.getChatGoodsApi=t=>e.request({url:"square/api.chatGoods/index",method:"POST",data:t}),exports.getHomeDataApi=t=>e.request({url:"useragent/api.AiAgentPublic/homeData",method:"POST",data:t}),exports.getInvitationCodeApi=t=>e.request({url:"user/api.userinfo/getInvitationCode",method:"POST",data:t}),exports.getMessageHistoryApi=t=>e.request({url:"useragent/api.AiAgentChat/messageHistory",method:"POST",data:t}),exports.getMyAgentListApi=t=>e.request({url:"useragent/api.AiAgent/myList",method:"POST",data:t}),exports.getMyCollectionListApi=t=>e.request({url:"useragent/api.AiAgentChat/myCollectionList",method:"POST",data:t}),exports.getMyDetailApi=t=>e.request({url:"useragent/api.AiAgent/myDetail",method:"POST",data:t}),exports.getMyEarningsApi=t=>e.request({url:"useragent/api.AiAgentFinance/myEarnings",method:"POST",data:t}),exports.getMyPersonListApi=t=>e.request({url:"useragent/api.ChanjingDigitalHuman/getMyPersonList",method:"POST",data:t}),exports.getMySessionListApi=t=>e.request({url:"useragent/api.AiAgentChat/mySessionList",method:"POST",data:t}),exports.getSecretKeyListApi=t=>e.request({url:"user/api.userinfo/getSecretKeyList",method:"POST",data:t}),exports.getSubscriptionListApi=t=>e.request({url:"useragent/api.AiAgentCreatorSubscription/creatorList",method:"POST",data:t}),exports.getSubscriptionRuleApi=t=>e.request({url:"useragent/api.AiAgentMembership/subscriptionRule",method:"POST",data:t}),exports.getUserInfoApi=t=>e.request({url:"user/api.userinfo/index",method:"POST",data:t}),exports.getUserVipInfoApi=t=>e.request({url:"useragent/api.AiAgentMembership/myMembership",method:"POST",data:t}),exports.getVideoDetailApi=t=>e.request({url:"useragent/api.ChanjingDigitalHuman/getVideoDetail",method:"POST",data:t}),exports.getVipPackageListApi=t=>e.request({url:"useragent/api.AiAgentMembership/packageList",method:"POST",data:t}),exports.mpLoginApi=t=>e.request({url:"user/api.user/xcxSilenceLogin",method:"POST",data:t}),exports.platformRulesApi=t=>e.request({url:"useragent/api.AiAgentPublic/platformRules",method:"POST",data:t}),exports.queryPayChatStautsApi=t=>e.request({url:"square/api.chatGoods/buyQuery",method:"POST",data:t}),exports.queryPurchaseOrderApi=t=>e.request({url:"useragent/api.AiAgentSquare/queryPurchaseOrder",method:"POST",data:t}),exports.querySubscriptionOrderApi=t=>e.request({url:"useragent/api.AiAgentCreatorSubscription/queryPayment",method:"POST",data:t}),exports.queryVipOrderApi=t=>e.request({url:"useragent/api.AiAgentMembership/queryPayment",method:"POST",data:t}),exports.saveMsgApi=t=>e.request({url:"useragent/api.AiAgentChat/saveMsg",method:"POST",data:t}),exports.saveSecretKeyApi=t=>e.request({url:"user/api.userinfo/saveSecretKey",method:"POST",data:t}),exports.setSessionTopApi=t=>e.request({url:"useragent/api.AiAgentChat/setSessionTop",method:"POST",data:t}),exports.showBannerUrlsApi=t=>e.request({url:"useragent/api.AiAgentPublic/showBannerUrls",method:"POST",data:t}),exports.subscribeAgentApi=t=>e.request({url:"useragent/api.AiAgentSquare/subscribeAgent",method:"POST",data:t}),exports.subscribeCreatorApi=t=>e.request({url:"useragent/api.AiAgentCreatorSubscription/purchaseCreatorSubscription",method:"POST",data:t}),exports.updateMyAgentApi=t=>e.request({url:"useragent/api.AiAgent/update",method:"POST",data:t}),exports.updateSessionTitleApi=t=>e.request({url:"useragent/api.AiAgentChat/updateSessionTitle",method:"POST",data:t}),exports.updateUserDefaultProfileApi=t=>e.request({url:"useragent/api.AiAgentSquare/updateUserDefaultProfile",method:"POST",data:t}),exports.updateUserInfoApi=t=>e.request({url:"user/api.userinfo/update",method:"POST",data:t}),exports.userVoicePrivacyApi=t=>e.request({url:"user/api.userWork/voicePrivacy",method:"POST",data:t}),exports.worksListApi=t=>e.request({url:"useragent/api.ChanjingDigitalHuman/getMyWorks",method:"POST",data:t});
