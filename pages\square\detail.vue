<template>
  <view class="agent-detail-page">
    <!-- 智能体信息 -->
    <view class="agent-info" v-if="agentDetail.agentName">
      <view class="avatar-section">
        <image class="agent-avatar" :src="agentDetail.agentAvatar" mode="aspectFill" />
        <!-- <view class="edit-icon" v-if="showEditIcon">
          <image src="@/static/common/edit_icon.png" class="edit-img" mode="aspectFit" />
        </view> -->
        <view class="nick-name">@{{ agentDetail.creator.nickname }}</view>
      </view>

      <view class="info-section">
        <text class="agent-name">{{ agentDetail.agentName }}</text>
        <text class="agent-desc">{{ agentDetail.agentDesc }}</text>
      </view>

      <view class="action-buttons">
        <view class="subscribe-btn" v-if="!agentDetail.isSubscribed" @click="handleSubscribeConfirm">
          <text class="btn-text">立即招募</text>
        </view>
        <view class="chat-btn" v-if="agentDetail.isSubscribed" @click="handleSubscribe">
          <text class="btn-text">去聊天</text>
        </view>
        <view class="share-btn" @click="handleShare">
          <text class="btn-text">生成分享海报</text>
        </view>
        <!-- <view class="promotion-link" @tap="handlePromotionPlan">
          <text class="link-text">《分享推广计划》</text>
        </view> -->
      </view>
    </view>

    <!-- 加载状态 -->
    <view class="loading-state" v-else-if="loading">
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" v-else>
      <text class="empty-text">智能体信息加载失败</text>
    </view>
  </view>


</template>

<script setup>
import { ref, watch } from 'vue'
import {
  onLoad, onShareAppMessage,
  onShareTimeline,
} from '@dcloudio/uni-app'
import { agentDetailApi, subscribeAgentApi, generateMiniCodeApi, getInvitationCodeApi, bindInvitationApi } from '@/api/index.js'
import { useUserStore } from '@/stores/user.js'
// import SubscribePopup from '@/components/subscribe-popup/subscribe-popup.vue'

const userStore = useUserStore()

// 页面参数
const agentGuid = ref('')
const sysId = ref('')
const loading = ref(false)
const showEditIcon = ref(false) // 根据设计图，这里可能需要显示编辑图标
const showSubscribeModal = ref(false) // 控制订阅弹窗显示
const showSharePoster = ref(false)
// 智能体详情数据
const agentDetail = ref({
  agentName: '',
  agentDesc: '',
  agentAvatar: '',
  isPaid: 0,
  price: 0,
  isPublic: 1,
  isSubscribed: false,
})

// 获取智能体详情
const getAgentDetail = async () => {
  if (!sysId.value) {
    uni.showToast({
      title: '参数错误',
      icon: 'none'
    })
    return
  }

  try {
    loading.value = true
    const res = await agentDetailApi({
      merchantGuid: userStore.merchantGuid,
      agentSysId: sysId.value
    })

    if (res.code === 0) {
      agentDetail.value = res.data;
      sysId.value = res.data.sysId;
      agentGuid.value = res.data.guid
    } else {
      throw new Error(res.msg || '获取详情失败')
    }
  } catch (error) {
    console.error('获取智能体详情失败:', error)
    uni.showToast({
      title: error.message || '加载失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 订阅智能体
const handleSubscribe = async () => {
  if (agentDetail.value.isSubscribed) {
    uni.navigateTo({
      url: `/pages/msg/index?sessionGuid=${agentGuid.value}`
    })
    return
  }
  try {
    uni.showLoading({
      title: '订阅中...',
      mask: true
    })

    const res = await subscribeAgentApi({
      merchantGuid: userStore.merchantGuid,
      agentGuid: agentGuid.value
    })

    if (res.code === 0) {
      uni.showToast({
        title: '订阅成功',
        icon: 'success'
      })
      // 跳转到对话页面
      // setTimeout(() => {
      uni.navigateTo({
        url: `/pages/msg/index?sessionGuid=${agentGuid.value}`
      })
      // }, 1500)
    } else {
      throw new Error(res.msg || '订阅失败')
    }
  } catch (error) {
    console.error('订阅失败:', error)
    uni.showToast({
      title: error.message || '订阅失败',
      icon: 'none'
    })
  } finally {
    uni.hideLoading()
  }
}

// 生成分享海报
const handleShare = async () => {
  // 确保二维码已生成
  if (!qrcode.value) {
    uni.showToast({
      title: '正在生成二维码，请稍后...',
      icon: 'none'
    })
    return
  }

  const params = {
    agentName: agentDetail.value.agentName || '智能体名称',
    agentDesc: agentDetail.value.agentDesc || '智能体描述',
    agentAvatar: agentDetail.value.agentAvatar || '',
    qrcode: qrcode.value || ''
  }

  console.log('传递给分享页的参数:', params)
  uni.navigateTo({
    url: `/pages/square/share?params=${encodeURIComponent(JSON.stringify(params))}`
  })
}

// 查看推广计划
const handlePromotionPlan = () => {
  uni.showToast({
    title: '功能开发中',
    icon: 'none'
  })
}


// 确认订阅
const handleSubscribeConfirm = async (agentInfo) => {
  try {
    uni.showLoading({
      title: '订阅中...',
      mask: true
    })

    const res = await subscribeAgentApi({
      merchantGuid: userStore.merchantGuid,
      agentGuid: agentGuid.value
    })

    if (res.code === 0) {
      uni.showToast({
        title: '订阅成功',
        icon: 'success'
      })
      // 更新智能体状态
      agentDetail.value.isSubscribed = true
      // 跳转到对话页面
      setTimeout(() => {
        uni.navigateTo({
          url: `/pages/msg/index?sessionGuid=${agentGuid.value}`
        })
      }, 1500)
    } else {
      throw new Error(res.msg || '订阅失败')
    }
  } catch (error) {
    console.error('订阅失败:', error)
    uni.showToast({
      title: error.message || '订阅失败',
      icon: 'none'
    })
  } finally {
    uni.hideLoading()
  }
}
onShareAppMessage(() => {
  return {
    title: userStore.appName || '智能体',
    path: `/pages/square/detail?invite=${invitationCode.value}`,
    success(res) {
      uni.showToast({
        title: '分享成功'
      })
    },
    fail(res) {
      uni.showToast({
        title: '分享失败',
        icon: 'none'
      })
    }
  }
})
// 分享到朋友圈功能
// onShareTimeline(() => {
//   return {
//     title: userStore.appName || '智能体',
//     path: `/pages/square/detail?invite=${userStore.invitationCode}`,
//     success(res) {
//       uni.showToast({
//         title: '分享成功'
//       })
//     },
//     fail(res) {
//       uni.showToast({
//         title: '分享失败',
//         icon: 'none'
//       })
//     }
//   }
// })
const qrcode = ref('')
const generateMiniCode = async () => {
  let query = `sysId=${sysId.value}&invite=${userStore.invitationCode}`
  let res = await generateMiniCodeApi({
    merchantGuid: userStore.merchantGuid,
    miniPath: 'pages/square/detail',
    pathQuery: query,
  })
  qrcode.value = res.data.miniCodeUrl
}
const invitationCode = ref('')
const getInvitationCode = async () => {
  let res = await getInvitationCodeApi({
    merchantGuid: userStore.merchantGuid,
  })
  invitationCode.value = res.data.inviteCode;
  generateMiniCode()
}

// let invite = ref('')
// const bindInvitation = async () => {
//   try {
//     await bindInvitationApi({
//       merchantGuid: userStore.merchantGuid,
//       invitationCode: invite.value
//     })
//   } catch (error) {
//     console.error('绑定邀请码失败detail:', error)
//   }
// }
onLoad(async (params) => {
  // if (params.invite) {
  //   invite.value = params.invite;
  //   if (userStore.userToken) {
  //     bindInvitation()
  //   }
  // }
  if (params.sysId) {
    // agentGuid.value = params.agentGuid;
    sysId.value = params.sysId
    if (userStore.userToken) {
      await getAgentDetail()
      await getInvitationCode()
    }
  } else {
    uni.showToast({
      title: '参数错误',
      icon: 'none'
    })
  }
})
watch(
  () => userStore.userToken,
  async (newValue, oldValue) => {
    if (newValue && oldValue === '') {
      await getAgentDetail()
      await getInvitationCode()
      // bindInvitation()
    }
  }
);
</script>

<style lang="scss" scoped>
.agent-detail-page {
  background: #f5f5f5;
  min-height: 100vh;
  padding: 60rpx 32rpx;

  .agent-info {
    background: #ffffff;
    border-radius: 24rpx;
    padding: 60rpx 40rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;

    .avatar-section {
      position: relative;
      margin-bottom: 40rpx;

      .agent-avatar {
        width: 200rpx;
        height: 200rpx;
        border-radius: 50%;
        background: #f0f0f0;
      }

      .edit-icon {
        position: absolute;
        right: 0;
        top: 0;
        width: 60rpx;
        height: 60rpx;
        background: #3478f6;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;

        .edit-img {
          width: 32rpx;
          height: 32rpx;
        }
      }

      .nick-name {
        font-size: 26rpx;
        color: #666666;
        text-align: center;
        margin-top: 10px;
      }
    }

    .info-section {
      margin-bottom: 60rpx;

      .agent-name {
        font-size: 48rpx;
        font-weight: 600;
        color: #1a1a1a;
        display: block;
        margin-bottom: 24rpx;
      }

      .agent-desc {
        font-size: 28rpx;
        color: #666666;
        line-height: 1.6;
        display: block;
      }
    }

    .action-buttons {
      width: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      .subscribe-btn {
        width: 400rpx;
        height: 90rpx;
        background: #3478f6;
        border-radius: 48rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 24rpx;


        .btn-text {
          font-size: 30rpx;
          color: #ffffff;
          font-weight: 600;
        }

        &.subscribed {
          background-color: #F2F2F7;
          border: none;

          .btn-text {
            color: #8E8E93;
          }
        }
      }

      .chat-btn {
        width: 400rpx;
        height: 90rpx;
        background: #40a266;
        border-radius: 48rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 24rpx;


        .btn-text {
          font-size: 30rpx;
          color: #ffffff;
          font-weight: 600;
        }
      }

      .share-btn {
        width: 400rpx;
        height: 90rpx;
        background: #3478f6;
        border-radius: 48rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 32rpx;

        .btn-text {
          font-size: 30rpx;
          color: #ffffff;
          font-weight: 600;
        }
      }

      .promotion-link {
        display: flex;
        justify-content: center;

        .link-text {
          font-size: 28rpx;
          color: #3478f6;
          text-decoration: underline;
        }
      }
    }
  }

  .loading-state {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 400rpx;

    .loading-text {
      font-size: 28rpx;
      color: #999999;
    }
  }

  .empty-state {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 400rpx;

    .empty-text {
      font-size: 28rpx;
      color: #999999;
    }
  }
}
</style>
