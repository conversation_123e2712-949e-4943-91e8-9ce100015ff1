{"version": 3, "file": "createAnimation.js", "sources": ["uni_modules/uni-transition/components/uni-transition/createAnimation.js"], "sourcesContent": ["// const defaultOption = {\n// \tduration: 300,\n// \ttimingFunction: 'linear',\n// \tdelay: 0,\n// \ttransformOrigin: '50% 50% 0'\n// }\n// #ifdef APP-NVUE\nconst nvueAnimation = uni.requireNativePlugin('animation')\n// #endif\nclass MPAnimation {\n\tconstructor(options, _this) {\n\t\tthis.options = options\n\t\t// 在iOS10+QQ小程序平台下，传给原生的对象一定是个普通对象而不是Proxy对象，否则会报parameter should be Object instead of ProxyObject的错误\n\t\tthis.animation = uni.createAnimation({\n\t\t\t...options\n\t\t})\n\t\tthis.currentStepAnimates = {}\n\t\tthis.next = 0\n\t\tthis.$ = _this\n\n\t}\n\n\t_nvuePushAnimates(type, args) {\n\t\tlet aniObj = this.currentStepAnimates[this.next]\n\t\tlet styles = {}\n\t\tif (!aniObj) {\n\t\t\tstyles = {\n\t\t\t\tstyles: {},\n\t\t\t\tconfig: {}\n\t\t\t}\n\t\t} else {\n\t\t\tstyles = aniObj\n\t\t}\n\t\tif (animateTypes1.includes(type)) {\n\t\t\tif (!styles.styles.transform) {\n\t\t\t\tstyles.styles.transform = ''\n\t\t\t}\n\t\t\tlet unit = ''\n\t\t\tif(type === 'rotate'){\n\t\t\t\tunit = 'deg'\n\t\t\t}\n\t\t\tstyles.styles.transform += `${type}(${args+unit}) `\n\t\t} else {\n\t\t\tstyles.styles[type] = `${args}`\n\t\t}\n\t\tthis.currentStepAnimates[this.next] = styles\n\t}\n\t_animateRun(styles = {}, config = {}) {\n\t\tlet ref = this.$.$refs['ani'].ref\n\t\tif (!ref) return\n\t\treturn new Promise((resolve, reject) => {\n\t\t\tnvueAnimation.transition(ref, {\n\t\t\t\tstyles,\n\t\t\t\t...config\n\t\t\t}, res => {\n\t\t\t\tresolve()\n\t\t\t})\n\t\t})\n\t}\n\n\t_nvueNextAnimate(animates, step = 0, fn) {\n\t\tlet obj = animates[step]\n\t\tif (obj) {\n\t\t\tlet {\n\t\t\t\tstyles,\n\t\t\t\tconfig\n\t\t\t} = obj\n\t\t\tthis._animateRun(styles, config).then(() => {\n\t\t\t\tstep += 1\n\t\t\t\tthis._nvueNextAnimate(animates, step, fn)\n\t\t\t})\n\t\t} else {\n\t\t\tthis.currentStepAnimates = {}\n\t\t\ttypeof fn === 'function' && fn()\n\t\t\tthis.isEnd = true\n\t\t}\n\t}\n\n\tstep(config = {}) {\n\t\t// #ifndef APP-NVUE\n\t\tthis.animation.step(config)\n\t\t// #endif\n\t\t// #ifdef APP-NVUE\n\t\tthis.currentStepAnimates[this.next].config = Object.assign({}, this.options, config)\n\t\tthis.currentStepAnimates[this.next].styles.transformOrigin = this.currentStepAnimates[this.next].config.transformOrigin\n\t\tthis.next++\n\t\t// #endif\n\t\treturn this\n\t}\n\n\trun(fn) {\n\t\t// #ifndef APP-NVUE\n\t\tthis.$.animationData = this.animation.export()\n\t\tthis.$.timer = setTimeout(() => {\n\t\t\ttypeof fn === 'function' && fn()\n\t\t}, this.$.durationTime)\n\t\t// #endif\n\t\t// #ifdef APP-NVUE\n\t\tthis.isEnd = false\n\t\tlet ref = this.$.$refs['ani'] && this.$.$refs['ani'].ref\n\t\tif(!ref) return\n\t\tthis._nvueNextAnimate(this.currentStepAnimates, 0, fn)\n\t\tthis.next = 0\n\t\t// #endif\n\t}\n}\n\n\nconst animateTypes1 = ['matrix', 'matrix3d', 'rotate', 'rotate3d', 'rotateX', 'rotateY', 'rotateZ', 'scale', 'scale3d',\n\t'scaleX', 'scaleY', 'scaleZ', 'skew', 'skewX', 'skewY', 'translate', 'translate3d', 'translateX', 'translateY',\n\t'translateZ'\n]\nconst animateTypes2 = ['opacity', 'backgroundColor']\nconst animateTypes3 = ['width', 'height', 'left', 'right', 'top', 'bottom']\nanimateTypes1.concat(animateTypes2, animateTypes3).forEach(type => {\n\tMPAnimation.prototype[type] = function(...args) {\n\t\t// #ifndef APP-NVUE\n\t\tthis.animation[type](...args)\n\t\t// #endif\n\t\t// #ifdef APP-NVUE\n\t\tthis._nvuePushAnimates(type, args)\n\t\t// #endif\n\t\treturn this\n\t}\n})\n\nexport function createAnimation(option, _this) {\n\tif(!_this) return\n\tclearTimeout(_this.timer)\n\treturn new MPAnimation(option, _this)\n}\n"], "names": ["uni"], "mappings": ";;AASA,MAAM,YAAY;AAAA,EACjB,YAAY,SAAS,OAAO;AAC3B,SAAK,UAAU;AAEf,SAAK,YAAYA,cAAG,MAAC,gBAAgB;AAAA,MACpC,GAAG;AAAA,IACN,CAAG;AACD,SAAK,sBAAsB,CAAE;AAC7B,SAAK,OAAO;AACZ,SAAK,IAAI;AAAA,EAET;AAAA,EAED,kBAAkB,MAAM,MAAM;AAC7B,QAAI,SAAS,KAAK,oBAAoB,KAAK,IAAI;AAC/C,QAAI,SAAS,CAAE;AACf,QAAI,CAAC,QAAQ;AACZ,eAAS;AAAA,QACR,QAAQ,CAAE;AAAA,QACV,QAAQ,CAAE;AAAA,MACV;AAAA,IACJ,OAAS;AACN,eAAS;AAAA,IACT;AACD,QAAI,cAAc,SAAS,IAAI,GAAG;AACjC,UAAI,CAAC,OAAO,OAAO,WAAW;AAC7B,eAAO,OAAO,YAAY;AAAA,MAC1B;AACD,UAAI,OAAO;AACX,UAAG,SAAS,UAAS;AACpB,eAAO;AAAA,MACP;AACD,aAAO,OAAO,aAAa,GAAG,IAAI,IAAI,OAAK,IAAI;AAAA,IAClD,OAAS;AACN,aAAO,OAAO,IAAI,IAAI,GAAG,IAAI;AAAA,IAC7B;AACD,SAAK,oBAAoB,KAAK,IAAI,IAAI;AAAA,EACtC;AAAA,EACD,YAAY,SAAS,IAAI,SAAS,CAAA,GAAI;AACrC,QAAI,MAAM,KAAK,EAAE,MAAM,KAAK,EAAE;AAC9B,QAAI,CAAC;AAAK;AACV,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACvC,oBAAc,WAAW,KAAK;AAAA,QAC7B;AAAA,QACA,GAAG;AAAA,MACH,GAAE,SAAO;AACT,gBAAS;AAAA,MACb,CAAI;AAAA,IACJ,CAAG;AAAA,EACD;AAAA,EAED,iBAAiB,UAAU,OAAO,GAAG,IAAI;AACxC,QAAI,MAAM,SAAS,IAAI;AACvB,QAAI,KAAK;AACR,UAAI;AAAA,QACH;AAAA,QACA;AAAA,MACJ,IAAO;AACJ,WAAK,YAAY,QAAQ,MAAM,EAAE,KAAK,MAAM;AAC3C,gBAAQ;AACR,aAAK,iBAAiB,UAAU,MAAM,EAAE;AAAA,MAC5C,CAAI;AAAA,IACJ,OAAS;AACN,WAAK,sBAAsB,CAAE;AAC7B,aAAO,OAAO,cAAc,GAAI;AAChC,WAAK,QAAQ;AAAA,IACb;AAAA,EACD;AAAA,EAED,KAAK,SAAS,IAAI;AAEjB,SAAK,UAAU,KAAK,MAAM;AAO1B,WAAO;AAAA,EACP;AAAA,EAED,IAAI,IAAI;AAEP,SAAK,EAAE,gBAAgB,KAAK,UAAU,OAAQ;AAC9C,SAAK,EAAE,QAAQ,WAAW,MAAM;AAC/B,aAAO,OAAO,cAAc,GAAI;AAAA,IACnC,GAAK,KAAK,EAAE,YAAY;AAAA,EAStB;AACF;AAGA,MAAM,gBAAgB;AAAA,EAAC;AAAA,EAAU;AAAA,EAAY;AAAA,EAAU;AAAA,EAAY;AAAA,EAAW;AAAA,EAAW;AAAA,EAAW;AAAA,EAAS;AAAA,EAC5G;AAAA,EAAU;AAAA,EAAU;AAAA,EAAU;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAS;AAAA,EAAa;AAAA,EAAe;AAAA,EAAc;AAAA,EAClG;AACD;AACA,MAAM,gBAAgB,CAAC,WAAW,iBAAiB;AACnD,MAAM,gBAAgB,CAAC,SAAS,UAAU,QAAQ,SAAS,OAAO,QAAQ;AAC1E,cAAc,OAAO,eAAe,aAAa,EAAE,QAAQ,UAAQ;AAClE,cAAY,UAAU,IAAI,IAAI,YAAY,MAAM;AAE/C,SAAK,UAAU,IAAI,EAAE,GAAG,IAAI;AAK5B,WAAO;AAAA,EACP;AACF,CAAC;AAEM,SAAS,gBAAgB,QAAQ,OAAO;AAC9C,MAAG,CAAC;AAAO;AACX,eAAa,MAAM,KAAK;AACxB,SAAO,IAAI,YAAY,QAAQ,KAAK;AACrC;;"}