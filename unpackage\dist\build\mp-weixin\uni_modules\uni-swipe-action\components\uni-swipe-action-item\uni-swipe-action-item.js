"use strict";const t=require("./mpwxs.js"),e=require("./bindingx.js"),o=require("./mpother.js"),s=require("../../../../common/vendor.js"),n=t=>{t.wxsCallMethods||(t.wxsCallMethods=[]),t.wxsCallMethods.push("closeSwipe","change")},i={},l={mixins:[t.mpwxs,e.bindIngXMixins,o.otherMixins],emits:["click","change"],props:{show:{type:String,default:"none"},disabled:{type:Boolean,default:!1},autoClose:{type:Boolean,default:!0},threshold:{type:Number,default:20},leftOptions:{type:Array,default:()=>[]},rightOptions:{type:Array,default:()=>[]}},unmounted(){this.__isUnmounted=!0,this.uninstall()},methods:{uninstall(){this.swipeaction&&this.swipeaction.children.forEach(((t,e)=>{t===this&&this.swipeaction.children.splice(e,1)}))},getSwipeAction(t="uniSwipeAction"){let e=this.$parent,o=e.$options.name;for(;o!==t;){if(e=e.$parent,!e)return!1;o=e.$options.name}return e}}};n(l),"function"==typeof i&&i(l);const r=s._export_sfc(l,[["render",function(t,e,o,n,i,l){return{a:s.f(o.leftOptions,((e,o,n)=>({a:s.t(e.text),b:e.style&&e.style.color?e.style.color:"#FFFFFF",c:e.style&&e.style.fontSize?e.style.fontSize:"16px",d:o,e:e.style&&e.style.backgroundColor?e.style.backgroundColor:"#C7C6CD",f:s.o(((...e)=>t.appTouchStart&&t.appTouchStart(...e)),o),g:s.o((s=>t.appTouchEnd(s,o,e,"left")),o),h:s.o((s=>t.onClickForPC(o,e,"left")),o)}))),b:s.f(o.rightOptions,((e,o,n)=>({a:s.t(e.text),b:e.style&&e.style.color?e.style.color:"#FFFFFF",c:e.style&&e.style.fontSize?e.style.fontSize:"16px",d:o,e:e.style&&e.style.backgroundColor?e.style.backgroundColor:"#C7C6CD",f:s.o(((...e)=>t.appTouchStart&&t.appTouchStart(...e)),o),g:s.o((s=>t.appTouchEnd(s,o,e,"right")),o),h:s.o((s=>t.onClickForPC(o,e,"right")),o)}))),c:t.is_show,d:o.threshold,e:o.disabled}}]]);wx.createComponent(r);
