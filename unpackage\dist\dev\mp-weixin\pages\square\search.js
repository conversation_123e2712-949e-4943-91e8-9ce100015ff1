"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const stores_user = require("../../stores/user.js");
const api_index = require("../../api/index.js");
const _sfc_main = {
  __name: "search",
  setup(__props) {
    const userStore = stores_user.useUserStore();
    const searchKeyword = common_vendor.ref("");
    const searchResults = common_vendor.ref([]);
    const loading = common_vendor.ref(false);
    const hasSearched = common_vendor.ref(false);
    const agentName = common_vendor.ref("");
    const currentPage = common_vendor.ref(1);
    const pageSize = common_vendor.ref(10);
    const hasMore = common_vendor.ref(true);
    const loadingMore = common_vendor.ref(false);
    const handleSearchInput = () => {
    };
    const handleSearch = async () => {
      if (!searchKeyword.value.trim()) {
        common_vendor.index.showToast({
          title: "请输入搜索关键词",
          icon: "none"
        });
        return;
      }
      currentPage.value = 1;
      hasMore.value = true;
      searchResults.value = [];
      await performSearch();
    };
    const performSearch = async () => {
      if (loading.value || loadingMore.value)
        return;
      try {
        if (currentPage.value === 1) {
          loading.value = true;
        } else {
          loadingMore.value = true;
        }
        const res = await api_index.getAgentListApi({
          merchantGuid: userStore.merchantGuid,
          agentName: searchKeyword.value.trim(),
          pageSize: pageSize.value,
          page: currentPage.value
        });
        if (res.code === 0) {
          const newData = res.data.data || [];
          if (currentPage.value === 1) {
            searchResults.value = newData;
            hasSearched.value = true;
          } else {
            searchResults.value = [...searchResults.value, ...newData];
          }
          hasMore.value = currentPage.value < res.data.last_page;
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/square/search.vue:153", "搜索失败:", error);
        common_vendor.index.showToast({
          title: "搜索失败，请重试",
          icon: "none"
        });
      } finally {
        loading.value = false;
        loadingMore.value = false;
      }
    };
    const loadMore = async () => {
      if (!hasMore.value || loadingMore.value)
        return;
      currentPage.value++;
      await performSearch();
    };
    const clearSearch = () => {
      searchKeyword.value = "";
      searchResults.value = [];
      hasSearched.value = false;
      currentPage.value = 1;
      hasMore.value = true;
    };
    const handleCancel = () => {
      common_vendor.index.navigateBack();
    };
    const handleAgentClick = (item) => {
      common_vendor.index.__f__("log", "at pages/square/search.vue:188", "点击智能体:", item.agentName);
      common_vendor.index.navigateTo({
        url: `/pages/square/detail?sysId=${item.sysId}`
      });
    };
    const onSub = async (item) => {
      const req = {
        merchantGuid: userStore.merchantGuid,
        agentGuid: item.guid
      };
      try {
        await api_index.subscribeAgentApi(req);
        const index = searchResults.value.findIndex((agent) => agent.guid === item.guid);
        if (index !== -1) {
          searchResults.value[index].isSubscribed = !searchResults.value[index].isSubscribed;
        }
        common_vendor.index.showToast({
          title: item.isSubscribed ? "取消订阅成功" : "订阅成功",
          icon: "none"
        });
      } catch (error) {
        common_vendor.index.showToast({
          title: "操作失败",
          icon: "none"
        });
      }
    };
    common_vendor.onLoad((options) => {
      if (options.agentName) {
        agentName.value = options.agentName;
        searchKeyword.value = options.agentName;
        performSearch();
      }
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$9,
        b: common_vendor.o([($event) => searchKeyword.value = $event.detail.value, handleSearchInput]),
        c: common_vendor.o(handleSearch),
        d: searchKeyword.value,
        e: searchKeyword.value
      }, searchKeyword.value ? {
        f: common_vendor.o(clearSearch)
      } : {}, {
        g: common_vendor.o(handleCancel),
        h: loading.value
      }, loading.value ? {} : !loading.value && searchResults.value.length === 0 && hasSearched.value ? {
        j: common_assets._imports_0$9
      } : searchResults.value.length > 0 ? common_vendor.e({
        l: common_vendor.f(searchResults.value, (item, k0, i0) => {
          return {
            a: item.agentAvatar,
            b: common_vendor.o(($event) => handleAgentClick(item), item.guid),
            c: common_vendor.t(item.agentName),
            d: common_vendor.t(item.agentDesc),
            e: common_vendor.t(item.creator.nickname),
            f: common_vendor.o(($event) => handleAgentClick(item), item.guid),
            g: common_vendor.t(item.isSubscribed ? "已订阅" : "订阅"),
            h: item.isSubscribed ? 1 : "",
            i: common_vendor.o(($event) => onSub(item), item.guid),
            j: item.guid
          };
        }),
        m: hasMore.value
      }, hasMore.value ? {
        n: common_vendor.t(loadingMore.value ? "加载中..." : "上拉加载更多")
      } : searchResults.value.length > 0 ? {} : {}, {
        o: searchResults.value.length > 0,
        p: common_vendor.o(loadMore)
      }) : {
        q: common_assets._imports_0$9
      }, {
        i: !loading.value && searchResults.value.length === 0 && hasSearched.value,
        k: searchResults.value.length > 0
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-ddee21bb"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/square/search.js.map
