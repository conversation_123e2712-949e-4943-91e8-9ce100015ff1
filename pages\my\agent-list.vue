<template>
  <view class="agent-list-page">
    <!-- 智能体列表 -->
    <z-paging ref="paging" v-model="agentList" @query="queryList" :auto="true" :auto-clean-list-when-reload="false">
      <view class="agents-content">
        <view class="agent-card" v-for="(agent, index) in agentList" :key="index">
          <image class="agent-avatar" :src="agent.agentAvatar" mode="aspectFill" />
          <view class="agent-info">
            <view class="agent-title">{{ agent.agentName }}</view>
            <text class="agent-desc">{{ agent.agentDesc }}</text>
            <view class="agent-tags">
              <view class="tag primary">
                {{agentTypes.find(item => item.value === agent.agentType).label}}
              </view>
              <view class="tag primary">
                {{ agent.priceText }}
              </view>
              <view class="tag primary">
                {{ agent.isPublicText }}
              </view>
            </view>
          </view>
          <view class="operate-box">
            <view class="edit" @click="handleEditAgent(agent)">编辑</view>
            <view class="delete" @click="handleDeleteAgent(agent)">删除</view>
          </view>
          <view class="status" v-if="agent.auditStatus != 2">{{ auditStatus[agent.auditStatus] }}</view>
          <view class="status success" v-if="agent.auditStatus === 2">{{ auditStatus[agent.auditStatus] }}</view>
        </view>
      </view>
    </z-paging>

    <!-- 创建智能体按钮 -->
    <view class="create-agent">
      <view class="create-btn" @tap="handleCreateAgent">
        <image src="@/static/msg/<EMAIL>" class="create-icon" mode="aspectFit" />
        <text class="create-text">创建AI智能体</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'
import { getMyAgentListApi, deleteAgentApi } from '@/api/index.js'
import { useUserStore } from '@/stores/user.js'

const userStore = useUserStore()
const agentList = ref([])
const paging = ref(null)

const agentTypes = ref([
  { label: '内部', value: 1 },
  { label: 'dify', value: 2 },
  { label: 'coze', value: 3 },
  { label: '阿里云百炼', value: 4 }
])

//审批状态：1-待审核；2-审核通过；3-审核拒绝
const auditStatus = {
  1: '待审核',
  2: '审核通过',
  3: '审核拒绝'
}
const handleDeleteAgent = async (agent) => {
  uni.showModal({
    title: '提示',
    content: '确定要删除该智能体吗？',
    success: async (res) => {
      if (res.confirm) {
        await deleteAgentApi({
          merchantGuid: userStore.merchantGuid,
          agentGuid: agent.guid
        })
        uni.showToast({
          title: '删除成功',
          icon: 'success'
        })
        getMyAgentList()
      }
    }
  })

}
// 分页查询 - 沿用my页面的数据格式
const queryList = async (page, pageSize) => {
  try {
    let res = await getMyAgentListApi({
      merchantGuid: userStore.merchantGuid,
      page: page,
      pageSize: pageSize
    })
    // 使用z-paging的complete方法处理数据
    paging.value.complete(res.data.data || [])
  } catch (error) {
    console.error('获取智能体列表失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    })
    paging.value.complete(false)
  }
}

// 事件处理

const handleCreateAgent = () => {
  uni.navigateTo({
    url: '/pages/create-agent/index'
  })
}

const handleEditAgent = (agent) => {
  uni.navigateTo({
    url: `/pages/create-agent/index?guid=${agent.guid}`
  })
}
</script>

<style lang="scss" scoped>
.agent-list-page {
  background: #F5F5F5;
  min-height: 100vh;

  .agents-content {
    padding: 32rpx;

    .agent-card {
      display: flex;
      align-items: center;
      background: #fff;
      border-radius: 24rpx;
      margin-bottom: 24rpx;
      padding: 32rpx;
      position: relative;

      .agent-avatar {
        width: 96rpx;
        height: 96rpx;
        border-radius: 50%;
        margin-right: 24rpx;
        flex-shrink: 0;
      }

      .agent-info {
        flex: 1;

        .agent-title {
          font-size: 32rpx;
          font-weight: 600;
          color: #222;
          margin-bottom: 8rpx;
        }

        .agent-desc {
          font-size: 24rpx;
          color: #999;
          line-height: 1.5;
          margin-bottom: 16rpx;
        }

        .agent-tags {
          display: flex;
          gap: 12rpx;
          flex-wrap: wrap;
          margin-top: 16rpx;

          .tag {
            border-radius: 16rpx;
            padding: 8rpx 16rpx;
            font-size: 22rpx;
            font-weight: 500;

            &.primary {
              background: #E6F0FF;
              color: #3478f6;
            }
          }
        }
      }

      .operate-box {
        display: flex;
        flex-direction: column;
        gap: 10px;

        .edit {
          width: 50px;
          height: 30px;
          font-size: 24rpx;
          background-color: #3478f6;
          color: #fff;
          display: flex;
          justify-content: center;
          align-items: center;
          border-radius: 16px;
        }

        .delete {
          width: 40px;
          height: 24px;
          font-size: 24rpx;
          background-color: #e60000;
          color: #fff;
          display: flex;
          justify-content: center;
          align-items: center;
          border-radius: 16px;
        }
      }

      .status {
        width: 140rpx;
        height: 40rpx;
        position: absolute;
        right: 0;
        top: 0;
        border-radius: 0rpx 30rpx 0rpx 30rpx;
        background-color: rgba(253, 141, 43, 0.12);
        font-size: 20rpx;
        color: #FD8D2B;
        text-align: center;
        line-height: 40rpx;

        &.success {
          background-color: #3478f6;
          color: #fff;
        }
      }
    }
  }

  .create-agent {
    position: fixed;
    width: 100%;
    bottom: 40px;
    left: 0;
    display: flex;
    justify-content: center;

    .create-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 300rpx;
      height: 90rpx;
      background: #3478f6;
      border-radius: 48rpx;
      border: none;

      .create-icon {
        width: 32rpx;
        height: 32rpx;
        margin-right: 12rpx;
      }

      .create-text {
        font-size: 32rpx;
        color: #ffffff;
        line-height: 1;
      }
    }
  }
}
</style>