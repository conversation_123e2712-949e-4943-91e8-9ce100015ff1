"use strict";const e=require("../../common/vendor.js");if(!Array){(e.resolveComponent("l-painter-image")+e.resolveComponent("l-painter-text")+e.resolveComponent("l-painter"))()}Math||((()=>"../../uni_modules/lime-painter/components/l-painter-image/l-painter-image.js")+(()=>"../../uni_modules/lime-painter/components/l-painter-text/l-painter-text.js")+(()=>"../../uni_modules/lime-painter/components/l-painter/l-painter.js"))();const a={__name:"share",setup(a){const t=e.ref(""),n=e.ref(!1);let o=e.reactive({agentName:"",agentDesc:"",agentAvatar:""});const r=e.ref("");e.ref(null),e.onLoad((async a=>{if(console.log("接收到的参数:",a),a&&a.params)try{const t=JSON.parse(decodeURIComponent(a.params));console.log("params",t),o=Object.assign(o,{agentName:t.agentName&&t.agentName.trim()||"智能体名称",agentDesc:t.agentDesc&&t.agentDesc.trim()||"智能体描述",agentAvatar:t.agentAvatar&&t.agentAvatar.trim()||""}),r.value=t.qrcode&&t.qrcode.trim()||"",console.log("agentDetail",o),await e.nextTick$1(),n.value=!0}catch(t){console.log("error",t),Object.assign(o,{agentName:"智能体名称",agentDesc:"智能体描述",agentAvatar:""}),n.value=!0}else console.log("没有参数，使用默认配置"),Object.assign(o,{agentName:"智能体名称",agentDesc:"智能体描述",agentAvatar:""}),n.value=!0}));const s=()=>{console.log("开始下载海报"),e.index.saveImageToPhotosAlbum({filePath:t.value,success:()=>{e.index.showToast({title:"保存成功",icon:"success"})},fail:a=>{console.error("保存失败:",a),e.index.showToast({title:"保存失败",icon:"none"})}})};return(a,i)=>e.e({a:t.value,b:e.o(s),c:n.value},n.value?{d:e.p({src:e.unref(o).agentAvatar,css:"width: 200rpx; height: 200rpx; display: block; border-radius: 50%; margin:80rpx auto 10rpx auto"}),e:e.p({text:e.unref(o).agentName,css:" width: 500rpx; text-align: center; font-size: 42rpx; font-weight: bold; color: #1a1a1a; margin: 45rpx auto 0 auto;"}),f:e.p({text:e.unref(o).agentDesc,css:" width: 450rpx; text-align: center; font-size: 26rpx; color: #666666; line-height: 38rpx;margin: 20rpx auto 0 auto"}),g:e.p({src:r.value,css:"width: 200rpx; height: 200rpx; display: block; margin: 60rpx auto 0 auto; border-radius: 50%;"})}:{},{h:e.o((e=>t.value=e)),i:e.p({isCanvasToTempFilePath:!0,pathType:"url",hidden:!0,css:"width: 600rpx; height: 835rpx; background-image: url(https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/f567f2e6e0cb40c48b2510b888bd3b40.png); background-size: cover;"})})}},t=e._export_sfc(a,[["__scopeId","data-v-58e1decf"]]);wx.createPage(t);
