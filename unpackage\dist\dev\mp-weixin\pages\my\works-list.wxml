<view class="my-works-container data-v-5332091b"><z-paging wx:if="{{e}}" class="r data-v-5332091b" u-s="{{['d']}}" u-r="paging" bindquery="{{c}}" u-i="5332091b-0" bind:__l="__l" bindupdateModelValue="{{d}}" u-p="{{e}}"><view class="works-content data-v-5332091b"><view class="avatar-grid data-v-5332091b"><view wx:for="{{a}}" wx:for-item="work" wx:key="d" class="{{['avatar-card', 'data-v-5332091b', work.e && 'selected']}}" bindtap="{{work.f}}"><image class="avatar-image data-v-5332091b" src="{{work.a}}" mode="aspectFill"/><view wx:if="{{work.b}}" class="check-mark data-v-5332091b"><image class="check-icon data-v-5332091b" src="{{work.c}}"/></view></view></view></view></z-paging><view class="bottom-actions data-v-5332091b"><block wx:if="{{f}}"><view class="action-button edit data-v-5332091b" bindtap="{{h}}"><image class="edit-icon data-v-5332091b" src="{{g}}" mode="aspectFit"/><text class="action-text data-v-5332091b">编辑</text></view></block><block wx:else><view class="action-button cancel data-v-5332091b" bindtap="{{i}}"><text class="action-text data-v-5332091b">取消</text></view><view class="action-button delete data-v-5332091b" bindtap="{{j}}"><text class="action-text data-v-5332091b">删除</text></view></block></view></view>