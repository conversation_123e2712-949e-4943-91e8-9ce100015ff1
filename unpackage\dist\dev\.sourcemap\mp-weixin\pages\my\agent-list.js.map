{"version": 3, "file": "agent-list.js", "sources": ["pages/my/agent-list.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvbXkvYWdlbnQtbGlzdC52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"agent-list-page\">\r\n    <!-- 智能体列表 -->\r\n    <z-paging ref=\"paging\" v-model=\"agentList\" @query=\"queryList\" :auto=\"true\" :auto-clean-list-when-reload=\"false\">\r\n      <view class=\"agents-content\">\r\n        <view class=\"agent-card\" v-for=\"(agent, index) in agentList\" :key=\"index\">\r\n          <image class=\"agent-avatar\" :src=\"agent.agentAvatar\" mode=\"aspectFill\" />\r\n          <view class=\"agent-info\">\r\n            <view class=\"agent-title\">{{ agent.agentName }}</view>\r\n            <text class=\"agent-desc\">{{ agent.agentDesc }}</text>\r\n            <view class=\"agent-tags\">\r\n              <view class=\"tag primary\">\r\n                {{agentTypes.find(item => item.value === agent.agentType).label}}\r\n              </view>\r\n              <view class=\"tag primary\">\r\n                {{ agent.priceText }}\r\n              </view>\r\n              <view class=\"tag primary\">\r\n                {{ agent.isPublicText }}\r\n              </view>\r\n            </view>\r\n          </view>\r\n          <view class=\"operate-box\">\r\n            <view class=\"edit\" @click=\"handleEditAgent(agent)\">编辑</view>\r\n            <view class=\"delete\" @click=\"handleDeleteAgent(agent)\">删除</view>\r\n          </view>\r\n          <view class=\"status\" v-if=\"agent.auditStatus != 2\">{{ auditStatus[agent.auditStatus] }}</view>\r\n          <view class=\"status success\" v-if=\"agent.auditStatus === 2\">{{ auditStatus[agent.auditStatus] }}</view>\r\n        </view>\r\n      </view>\r\n    </z-paging>\r\n\r\n    <!-- 创建智能体按钮 -->\r\n    <view class=\"create-agent\">\r\n      <view class=\"create-btn\" @tap=\"handleCreateAgent\">\r\n        <image src=\"@/static/msg/<EMAIL>\" class=\"create-icon\" mode=\"aspectFit\" />\r\n        <text class=\"create-text\">创建AI智能体</text>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref } from 'vue'\r\nimport { getMyAgentListApi, deleteAgentApi } from '@/api/index.js'\r\nimport { useUserStore } from '@/stores/user.js'\r\n\r\nconst userStore = useUserStore()\r\nconst agentList = ref([])\r\nconst paging = ref(null)\r\n\r\nconst agentTypes = ref([\r\n  { label: '内部', value: 1 },\r\n  { label: 'dify', value: 2 },\r\n  { label: 'coze', value: 3 },\r\n  { label: '阿里云百炼', value: 4 }\r\n])\r\n\r\n//审批状态：1-待审核；2-审核通过；3-审核拒绝\r\nconst auditStatus = {\r\n  1: '待审核',\r\n  2: '审核通过',\r\n  3: '审核拒绝'\r\n}\r\nconst handleDeleteAgent = async (agent) => {\r\n  uni.showModal({\r\n    title: '提示',\r\n    content: '确定要删除该智能体吗？',\r\n    success: async (res) => {\r\n      if (res.confirm) {\r\n        await deleteAgentApi({\r\n          merchantGuid: userStore.merchantGuid,\r\n          agentGuid: agent.guid\r\n        })\r\n        uni.showToast({\r\n          title: '删除成功',\r\n          icon: 'success'\r\n        })\r\n        getMyAgentList()\r\n      }\r\n    }\r\n  })\r\n\r\n}\r\n// 分页查询 - 沿用my页面的数据格式\r\nconst queryList = async (page, pageSize) => {\r\n  try {\r\n    let res = await getMyAgentListApi({\r\n      merchantGuid: userStore.merchantGuid,\r\n      page: page,\r\n      pageSize: pageSize\r\n    })\r\n    // 使用z-paging的complete方法处理数据\r\n    paging.value.complete(res.data.data || [])\r\n  } catch (error) {\r\n    console.error('获取智能体列表失败:', error)\r\n    uni.showToast({\r\n      title: '加载失败',\r\n      icon: 'none'\r\n    })\r\n    paging.value.complete(false)\r\n  }\r\n}\r\n\r\n// 事件处理\r\n\r\nconst handleCreateAgent = () => {\r\n  uni.navigateTo({\r\n    url: '/pages/create-agent/index'\r\n  })\r\n}\r\n\r\nconst handleEditAgent = (agent) => {\r\n  uni.navigateTo({\r\n    url: `/pages/create-agent/index?guid=${agent.guid}`\r\n  })\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.agent-list-page {\r\n  background: #F5F5F5;\r\n  min-height: 100vh;\r\n\r\n  .agents-content {\r\n    padding: 32rpx;\r\n\r\n    .agent-card {\r\n      display: flex;\r\n      align-items: center;\r\n      background: #fff;\r\n      border-radius: 24rpx;\r\n      margin-bottom: 24rpx;\r\n      padding: 32rpx;\r\n      position: relative;\r\n\r\n      .agent-avatar {\r\n        width: 96rpx;\r\n        height: 96rpx;\r\n        border-radius: 50%;\r\n        margin-right: 24rpx;\r\n        flex-shrink: 0;\r\n      }\r\n\r\n      .agent-info {\r\n        flex: 1;\r\n\r\n        .agent-title {\r\n          font-size: 32rpx;\r\n          font-weight: 600;\r\n          color: #222;\r\n          margin-bottom: 8rpx;\r\n        }\r\n\r\n        .agent-desc {\r\n          font-size: 24rpx;\r\n          color: #999;\r\n          line-height: 1.5;\r\n          margin-bottom: 16rpx;\r\n        }\r\n\r\n        .agent-tags {\r\n          display: flex;\r\n          gap: 12rpx;\r\n          flex-wrap: wrap;\r\n          margin-top: 16rpx;\r\n\r\n          .tag {\r\n            border-radius: 16rpx;\r\n            padding: 8rpx 16rpx;\r\n            font-size: 22rpx;\r\n            font-weight: 500;\r\n\r\n            &.primary {\r\n              background: #E6F0FF;\r\n              color: #3478f6;\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      .operate-box {\r\n        display: flex;\r\n        flex-direction: column;\r\n        gap: 10px;\r\n\r\n        .edit {\r\n          width: 50px;\r\n          height: 30px;\r\n          font-size: 24rpx;\r\n          background-color: #3478f6;\r\n          color: #fff;\r\n          display: flex;\r\n          justify-content: center;\r\n          align-items: center;\r\n          border-radius: 16px;\r\n        }\r\n\r\n        .delete {\r\n          width: 40px;\r\n          height: 24px;\r\n          font-size: 24rpx;\r\n          background-color: #e60000;\r\n          color: #fff;\r\n          display: flex;\r\n          justify-content: center;\r\n          align-items: center;\r\n          border-radius: 16px;\r\n        }\r\n      }\r\n\r\n      .status {\r\n        width: 140rpx;\r\n        height: 40rpx;\r\n        position: absolute;\r\n        right: 0;\r\n        top: 0;\r\n        border-radius: 0rpx 30rpx 0rpx 30rpx;\r\n        background-color: rgba(253, 141, 43, 0.12);\r\n        font-size: 20rpx;\r\n        color: #FD8D2B;\r\n        text-align: center;\r\n        line-height: 40rpx;\r\n\r\n        &.success {\r\n          background-color: #3478f6;\r\n          color: #fff;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .create-agent {\r\n    position: fixed;\r\n    width: 100%;\r\n    bottom: 40px;\r\n    left: 0;\r\n    display: flex;\r\n    justify-content: center;\r\n\r\n    .create-btn {\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      width: 300rpx;\r\n      height: 90rpx;\r\n      background: #3478f6;\r\n      border-radius: 48rpx;\r\n      border: none;\r\n\r\n      .create-icon {\r\n        width: 32rpx;\r\n        height: 32rpx;\r\n        margin-right: 12rpx;\r\n      }\r\n\r\n      .create-text {\r\n        font-size: 32rpx;\r\n        color: #ffffff;\r\n        line-height: 1;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>", "import MiniProgramPage from 'E:/youngProject/agent-mini-ui/pages/my/agent-list.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useUserStore", "ref", "uni", "deleteAgentApi", "getMyAgentListApi"], "mappings": ";;;;;;;;;;;;;;;;AA+CA,UAAM,YAAYA,YAAAA,aAAc;AAChC,UAAM,YAAYC,cAAG,IAAC,EAAE;AACxB,UAAM,SAASA,cAAG,IAAC,IAAI;AAEvB,UAAM,aAAaA,cAAAA,IAAI;AAAA,MACrB,EAAE,OAAO,MAAM,OAAO,EAAG;AAAA,MACzB,EAAE,OAAO,QAAQ,OAAO,EAAG;AAAA,MAC3B,EAAE,OAAO,QAAQ,OAAO,EAAG;AAAA,MAC3B,EAAE,OAAO,SAAS,OAAO,EAAG;AAAA,IAC9B,CAAC;AAGD,UAAM,cAAc;AAAA,MAClB,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AACA,UAAM,oBAAoB,OAAO,UAAU;AACzCC,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,OAAO,QAAQ;AACtB,cAAI,IAAI,SAAS;AACf,kBAAMC,yBAAe;AAAA,cACnB,cAAc,UAAU;AAAA,cACxB,WAAW,MAAM;AAAA,YAC3B,CAAS;AACDD,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YAChB,CAAS;AACD,2BAAgB;AAAA,UACjB;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IAEH;AAEA,UAAM,YAAY,OAAO,MAAM,aAAa;AAC1C,UAAI;AACF,YAAI,MAAM,MAAME,4BAAkB;AAAA,UAChC,cAAc,UAAU;AAAA,UACxB;AAAA,UACA;AAAA,QACN,CAAK;AAED,eAAO,MAAM,SAAS,IAAI,KAAK,QAAQ,EAAE;AAAA,MAC1C,SAAQ,OAAO;AACdF,sBAAAA,MAAc,MAAA,SAAA,iCAAA,cAAc,KAAK;AACjCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD,eAAO,MAAM,SAAS,KAAK;AAAA,MAC5B;AAAA,IACH;AAIA,UAAM,oBAAoB,MAAM;AAC9BA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACT,CAAG;AAAA,IACH;AAEA,UAAM,kBAAkB,CAAC,UAAU;AACjCA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,kCAAkC,MAAM,IAAI;AAAA,MACrD,CAAG;AAAA,IACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnHA,GAAG,WAAW,eAAe;"}