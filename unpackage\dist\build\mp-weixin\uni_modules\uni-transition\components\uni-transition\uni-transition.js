"use strict";const t=require("./createAnimation.js"),i=require("../../../../common/vendor.js"),s={name:"uniTransition",emits:["click","change"],props:{show:{type:<PERSON><PERSON><PERSON>,default:!1},modeClass:{type:[Array,String],default:()=>"fade"},duration:{type:Number,default:300},styles:{type:Object,default:()=>({})},customClass:{type:String,default:""},onceRender:{type:<PERSON>olean,default:!1}},data:()=>({isShow:!1,transform:"",opacity:0,animationData:{},durationTime:300,config:{}}),watch:{show:{handler(t){t?this.open():this.isShow&&this.close()},immediate:!0}},computed:{stylesObject(){let t={...this.styles,"transition-duration":this.duration/1e3+"s"},i="";for(let s in t){i+=this.toLine(s)+":"+t[s]+";"}return i},transformStyles(){return"transform:"+this.transform+";opacity:"+this.opacity+";"+this.stylesObject}},created(){this.config={duration:this.duration,timingFunction:"ease",transformOrigin:"50% 50%",delay:0},this.durationTime=this.duration},methods:{init(i={}){i.duration&&(this.durationTime=i.duration),this.animation=t.createAnimation(Object.assign(this.config,i),this)},onClick(){this.$emit("click",{detail:this.isShow})},step(t,i={}){return this.animation?(Object.keys(t).forEach((i=>{const s=t[i];"function"==typeof this.animation[i]&&(Array.isArray(s)?this.animation[i](...s):this.animation[i](s))})),this.animation.step(i),this):this},run(t){this.animation&&this.animation.run(t)},open(){clearTimeout(this.timer),this.isShow=!0,this.transform=this.styleInit(!1).transform||"",this.opacity=this.styleInit(!1).opacity||0,this.$nextTick((()=>{this.timer=setTimeout((()=>{this.animation=t.createAnimation(this.config,this),this.tranfromInit(!1).step(),this.animation.run((()=>{this.transform="",this.opacity=this.styleInit(!1).opacity||1,this.$emit("change",{detail:this.isShow})}))}),80)}))},close(t){this.animation&&this.tranfromInit(!0).step().run((()=>{this.isShow=!1,this.animationData=null,this.animation=null;let{opacity:t,transform:i}=this.styleInit(!1);this.opacity=t||1,this.transform=i,this.$emit("change",{detail:this.isShow})}))},styleInit(t){let i={transform:"",opacity:1};const s=(t,s)=>{const a=this.animationType(t)[s];s.startsWith("fade")?i.opacity=a:i.transform+=a+" "};return"string"==typeof this.modeClass?s(t,this.modeClass):this.modeClass.forEach((i=>s(t,i))),i},tranfromInit(t){let i=(t,i)=>{let s=null;"fade"===i?s=t?0:1:(s=t?"-100%":"0","zoom-in"===i&&(s=t?.8:1),"zoom-out"===i&&(s=t?1.2:1),"slide-right"===i&&(s=t?"100%":"0"),"slide-bottom"===i&&(s=t?"100%":"0")),this.animation[this.animationMode()[i]](s)};return"string"==typeof this.modeClass?i(t,this.modeClass):this.modeClass.forEach((s=>{i(t,s)})),this.animation},animationType:t=>({fade:t?1:0,"slide-top":`translateY(${t?"0":"-100%"})`,"slide-right":`translateX(${t?"0":"100%"})`,"slide-bottom":`translateY(${t?"0":"100%"})`,"slide-left":`translateX(${t?"0":"-100%"})`,"zoom-in":`scaleX(${t?1:.8}) scaleY(${t?1:.8})`,"zoom-out":`scaleX(${t?1:1.2}) scaleY(${t?1:1.2})`}),animationMode:()=>({fade:"opacity","slide-top":"translateY","slide-right":"translateX","slide-bottom":"translateY","slide-left":"translateX","zoom-in":"scale","zoom-out":"scale"}),toLine:t=>t.replace(/([A-Z])/g,"-$1").toLowerCase()}};const a=i._export_sfc(s,[["render",function(t,s,a,n,e,o){return{a:e.isShow,b:e.animationData,c:i.n(a.customClass),d:i.s(o.transformStyles),e:i.o(((...t)=>o.onClick&&o.onClick(...t)))}}]]);wx.createComponent(a);
