<view class="agent-list-page data-v-892ba0a0"><z-paging wx:if="{{e}}" class="r data-v-892ba0a0" u-s="{{['d']}}" u-r="paging" bindquery="{{c}}" u-i="892ba0a0-0" bind:__l="__l" bindupdateModelValue="{{d}}" u-p="{{e}}"><view class="agents-content data-v-892ba0a0"><view wx:for="{{a}}" wx:for-item="agent" wx:key="m" class="agent-card data-v-892ba0a0"><image class="agent-avatar data-v-892ba0a0" src="{{agent.a}}" mode="aspectFill"/><view class="agent-info data-v-892ba0a0"><view class="agent-title data-v-892ba0a0">{{agent.b}}</view><text class="agent-desc data-v-892ba0a0">{{agent.c}}</text><view class="agent-tags data-v-892ba0a0"><view class="tag primary data-v-892ba0a0">{{agent.d}}</view><view class="tag primary data-v-892ba0a0">{{agent.e}}</view><view class="tag primary data-v-892ba0a0">{{agent.f}}</view></view></view><view class="operate-box data-v-892ba0a0"><view class="edit data-v-892ba0a0" bindtap="{{agent.g}}">编辑</view><view class="delete data-v-892ba0a0" bindtap="{{agent.h}}">删除</view></view><view wx:if="{{agent.i}}" class="status data-v-892ba0a0">{{agent.j}}</view><view wx:if="{{agent.k}}" class="status success data-v-892ba0a0">{{agent.l}}</view></view></view></z-paging><view class="create-agent data-v-892ba0a0"><view class="create-btn data-v-892ba0a0" bindtap="{{g}}"><image src="{{f}}" class="create-icon data-v-892ba0a0" mode="aspectFit"/><text class="create-text data-v-892ba0a0">创建AI智能体</text></view></view></view>