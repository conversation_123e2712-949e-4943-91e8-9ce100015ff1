{"version": 3, "file": "props.js", "sources": ["uni_modules/uv-tabs/components/uv-tabs/props.js"], "sourcesContent": ["export default {\r\n\tprops: {\r\n\t\t// 滑块的移动过渡时间，单位ms\r\n\t\tduration: {\r\n\t\t\ttype: Number,\r\n\t\t\tdefault: 300\r\n\t\t},\r\n\t\t// tabs标签数组\r\n\t\tlist: {\r\n\t\t\ttype: Array,\r\n\t\t\tdefault: () => []\r\n\t\t},\r\n\t\t// 滑块颜色\r\n\t\tlineColor: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: '#3c9cff'\r\n\t\t},\r\n\t\t// 菜单选择中时的样式\r\n\t\tactiveStyle: {\r\n\t\t\ttype: [String, Object],\r\n\t\t\tdefault: () => ({\r\n\t\t\t\tcolor: '#303133'\r\n\t\t\t})\r\n\t\t},\r\n\t\t// 菜单非选中时的样式\r\n\t\tinactiveStyle: {\r\n\t\t\ttype: [String, Object],\r\n\t\t\tdefault: () => ({\r\n\t\t\t\tcolor: '#606266'\r\n\t\t\t})\r\n\t\t},\r\n\t\t// 滑块长度\r\n\t\tlineWidth: {\r\n\t\t\ttype: [String, Number],\r\n\t\t\tdefault: 20\r\n\t\t},\r\n\t\t// 滑块高度\r\n\t\tlineHeight: {\r\n\t\t\ttype: [String, Number],\r\n\t\t\tdefault: 3\r\n\t\t},\r\n\t\t// 滑块背景显示大小，当滑块背景设置为图片时使用\r\n\t\tlineBgSize: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: 'cover'\r\n\t\t},\r\n\t\t// 菜单item的样式\r\n\t\titemStyle: {\r\n\t\t\ttype: [String, Object],\r\n\t\t\tdefault: () => ({\r\n\t\t\t\theight: '44px'\r\n\t\t\t})\r\n\t\t},\r\n\t\t// 菜单是否可滚动\r\n\t\tscrollable: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: true\r\n\t\t},\r\n\t\t// 当前选中标签的索引\r\n\t\tcurrent: {\r\n\t\t\ttype: [Number, String],\r\n\t\t\tdefault: 0\r\n\t\t},\r\n\t\t// 默认读取的键名\r\n\t\tkeyName: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: 'name'\r\n\t\t},\r\n\t\t...uni.$uv?.props?.tabs\r\n\t}\r\n}"], "names": ["uni"], "mappings": ";;;AAAA,MAAe,QAAA;AAAA,EACd,OAAO;AAAA;AAAA,IAEN,UAAU;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA;AAAA,IAED,MAAM;AAAA,MACL,MAAM;AAAA,MACN,SAAS,MAAM,CAAE;AAAA,IACjB;AAAA;AAAA,IAED,WAAW;AAAA,MACV,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA;AAAA,IAED,aAAa;AAAA,MACZ,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,OAAO;AAAA,QACf,OAAO;AAAA,MACX;AAAA,IACG;AAAA;AAAA,IAED,eAAe;AAAA,MACd,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,OAAO;AAAA,QACf,OAAO;AAAA,MACX;AAAA,IACG;AAAA;AAAA,IAED,WAAW;AAAA,MACV,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS;AAAA,IACT;AAAA;AAAA,IAED,YAAY;AAAA,MACX,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS;AAAA,IACT;AAAA;AAAA,IAED,YAAY;AAAA,MACX,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA;AAAA,IAED,WAAW;AAAA,MACV,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,OAAO;AAAA,QACf,QAAQ;AAAA,MACZ;AAAA,IACG;AAAA;AAAA,IAED,YAAY;AAAA,MACX,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA;AAAA,IAED,SAAS;AAAA,MACR,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS;AAAA,IACT;AAAA;AAAA,IAED,SAAS;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA,IACD,IAAGA,yBAAG,MAAC,QAAJA,mBAAS,UAATA,mBAAgB;AAAA,EACnB;AACF;;"}