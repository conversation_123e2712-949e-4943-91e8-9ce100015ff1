{"version": 3, "file": "favorite-list.js", "sources": ["pages/my/favorite-list.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvbXkvZmF2b3JpdGUtbGlzdC52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"favorite-list-page\">\r\n    <!-- 收藏列表 -->\r\n    <z-paging ref=\"paging\" v-model=\"favoritesList\" @query=\"queryList\" :auto=\"true\" :auto-clean-list-when-reload=\"false\">\r\n      <view class=\"favorites-content\">\r\n        <view class=\"favorite-card\" v-for=\"(item, index) in favoritesList\" :key=\"index\">\r\n          <view class=\"favorite-header\">\r\n            <text class=\"favorite-date\">{{ item.collectTime }}</text>\r\n          </view>\r\n          <view class=\"favorite-content\">\r\n            <text class=\"content-text\">{{ item.contentPreview }}</text>\r\n          </view>\r\n          <view class=\"favorite-actions\">\r\n            <view class=\"action-btn\" @tap=\"handleCopy(item)\">\r\n              <image src=\"@/static/my/<EMAIL>\" class=\"action-icon\" mode=\"aspectFit\" />\r\n              <text class=\"action-text\">复制</text>\r\n            </view>\r\n            <view class=\"action-btn\" @tap=\"handleViewAll(item)\">\r\n              <image src=\"@/static/my/<EMAIL>\" class=\"action-icon\" mode=\"aspectFit\" />\r\n              <text class=\"action-text\">查看全部</text>\r\n            </view>\r\n            <view class=\"action-btn\" @tap=\"handleUnfavorite(item)\">\r\n              <image src=\"@/static/my/<EMAIL>\" class=\"action-icon\" mode=\"aspectFit\" />\r\n              <text class=\"action-text\">取消收藏</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </z-paging>\r\n\r\n    <!-- 查看全部弹窗 -->\r\n    <view v-if=\"showContentModal\" class=\"modal-overlay\" @tap=\"closeContentModal\" catchtouchmove=\"true\">\r\n      <view class=\"modal-content\" @tap.stop>\r\n        <view class=\"modal-header\">\r\n          <text class=\"modal-title\">消息详情</text>\r\n          <view class=\"close-btn\" @tap=\"closeContentModal\">\r\n            <text class=\"close-text\">×</text>\r\n          </view>\r\n        </view>\r\n        <scroll-view scroll-y=\"true\" class=\"modal-body\" show-scrollbar=\"false\">\r\n          <view class=\"rich-content\">\r\n            <rich-text :nodes=\"currentContent\"></rich-text>\r\n          </view>\r\n        </scroll-view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref } from 'vue'\r\nimport { getMyCollectionListApi, cancelCollectMessageApi } from '@/api/index.js'\r\nimport { useUserStore } from '@/stores/user.js'\r\n\r\nconst userStore = useUserStore()\r\nconst favoritesList = ref([])\r\nconst paging = ref(null)\r\n\r\n// 弹窗相关状态\r\nconst showContentModal = ref(false)\r\nconst currentContent = ref('')\r\n\r\n// 分页查询 - 沿用my页面的数据格式\r\nconst queryList = async (page, pageSize) => {\r\n  try {\r\n    let res = await getMyCollectionListApi({\r\n      merchantGuid: userStore.merchantGuid,\r\n      page: page,\r\n      pageSize: pageSize\r\n    })\r\n    // 使用z-paging的complete方法处理数据\r\n    paging.value.complete(res.data.list || [])\r\n  } catch (error) {\r\n    console.error('获取收藏列表失败:', error)\r\n    uni.showToast({\r\n      title: '加载失败',\r\n      icon: 'none'\r\n    })\r\n    paging.value.complete(false)\r\n  }\r\n}\r\n\r\n// 事件处理\r\n\r\nconst handleCopy = (item) => {\r\n  uni.setClipboardData({\r\n    data: item.messageContent,\r\n    success() {\r\n      uni.showToast({\r\n        title: '复制成功',\r\n        icon: 'none'\r\n      })\r\n    }\r\n  })\r\n}\r\n\r\nconst handleViewAll = (item) => {\r\n  currentContent.value = item.messageContent || ''\r\n  showContentModal.value = true\r\n}\r\n\r\nconst closeContentModal = () => {\r\n  showContentModal.value = false\r\n  currentContent.value = ''\r\n}\r\n\r\nconst handleUnfavorite = async (item) => {\r\n  try {\r\n    await cancelCollectMessageApi({\r\n      merchantGuid: userStore.merchantGuid,\r\n      messageGuid: item.messageGuid\r\n    })\r\n    uni.showToast({\r\n      title: '取消收藏成功',\r\n      icon: 'none'\r\n    })\r\n    // 刷新列表\r\n    if (paging.value) {\r\n      paging.value.reload()\r\n    }\r\n  } catch (error) {\r\n    console.error('取消收藏失败:', error)\r\n    uni.showToast({\r\n      title: '操作失败',\r\n      icon: 'none'\r\n    })\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.modal-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.5);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  z-index: 1000;\r\n  overflow: hidden;\r\n  touch-action: none;\r\n\r\n  .modal-content {\r\n    background: #ffffff;\r\n    width: 90%;\r\n    height: 600px;\r\n    border-radius: 24rpx;\r\n    display: flex;\r\n    flex-direction: column;\r\n    overflow: hidden;\r\n\r\n    .modal-header {\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n      padding: 32rpx;\r\n      border-bottom: 1px solid #F0F0F0;\r\n      flex-shrink: 0;\r\n\r\n      .modal-title {\r\n        font-size: 32rpx;\r\n        font-weight: 600;\r\n        color: #1a1a1a;\r\n      }\r\n\r\n      .close-btn {\r\n        width: 48rpx;\r\n        height: 48rpx;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        border-radius: 50%;\r\n        background: #F5F5F5;\r\n\r\n        .close-text {\r\n          font-size: 36rpx;\r\n          color: #666666;\r\n          line-height: 1;\r\n        }\r\n      }\r\n    }\r\n\r\n    .modal-body {\r\n      flex: 1;\r\n      height: 0;\r\n      overflow: hidden;\r\n\r\n      .rich-content {\r\n        font-size: 28rpx;\r\n        line-height: 1.6;\r\n        color: #1a1a1a;\r\n        word-break: break-all;\r\n        padding: 32rpx;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.favorite-list-page {\r\n  background: #F5F5F5;\r\n  min-height: 100vh;\r\n\r\n  .favorites-content {\r\n    padding: 32rpx;\r\n\r\n    .favorite-card {\r\n      background: #fff;\r\n      border-radius: 24rpx;\r\n      margin-bottom: 24rpx;\r\n      padding: 32rpx;\r\n\r\n      .favorite-header {\r\n        margin-bottom: 16rpx;\r\n\r\n        .favorite-date {\r\n          font-size: 24rpx;\r\n          color: #999999;\r\n          display: block;\r\n        }\r\n      }\r\n\r\n      .favorite-content {\r\n        margin-bottom: 24rpx;\r\n\r\n        .content-text {\r\n          font-size: 28rpx;\r\n          color: #1a1a1a;\r\n          line-height: 1.6;\r\n          display: block;\r\n        }\r\n      }\r\n\r\n      .favorite-actions {\r\n        display: flex;\r\n        gap: 16rpx;\r\n        flex-wrap: wrap;\r\n\r\n        .action-btn {\r\n          display: flex;\r\n          align-items: center;\r\n          background: #E6F0FF;\r\n          border-radius: 20rpx;\r\n          padding: 12rpx 20rpx;\r\n          border: none;\r\n          flex-shrink: 0;\r\n          min-width: 0;\r\n\r\n          .action-icon {\r\n            width: 38rpx;\r\n            height: 38rpx;\r\n            margin-right: 8rpx;\r\n            flex-shrink: 0;\r\n          }\r\n\r\n          .action-text {\r\n            font-size: 24rpx;\r\n            color: #222222;\r\n            font-weight: 500;\r\n            white-space: nowrap;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>", "import MiniProgramPage from 'E:/youngProject/agent-mini-ui/pages/my/favorite-list.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useUserStore", "ref", "getMyCollectionListApi", "uni", "cancelCollectMessageApi"], "mappings": ";;;;;;;;;;;;;;;;AAsDA,UAAM,YAAYA,YAAAA,aAAc;AAChC,UAAM,gBAAgBC,cAAG,IAAC,EAAE;AAC5B,UAAM,SAASA,cAAG,IAAC,IAAI;AAGvB,UAAM,mBAAmBA,cAAG,IAAC,KAAK;AAClC,UAAM,iBAAiBA,cAAG,IAAC,EAAE;AAG7B,UAAM,YAAY,OAAO,MAAM,aAAa;AAC1C,UAAI;AACF,YAAI,MAAM,MAAMC,iCAAuB;AAAA,UACrC,cAAc,UAAU;AAAA,UACxB;AAAA,UACA;AAAA,QACN,CAAK;AAED,eAAO,MAAM,SAAS,IAAI,KAAK,QAAQ,EAAE;AAAA,MAC1C,SAAQ,OAAO;AACdC,sBAAAA,MAAA,MAAA,SAAA,oCAAc,aAAa,KAAK;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD,eAAO,MAAM,SAAS,KAAK;AAAA,MAC5B;AAAA,IACH;AAIA,UAAM,aAAa,CAAC,SAAS;AAC3BA,oBAAAA,MAAI,iBAAiB;AAAA,QACnB,MAAM,KAAK;AAAA,QACX,UAAU;AACRA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAEA,UAAM,gBAAgB,CAAC,SAAS;AAC9B,qBAAe,QAAQ,KAAK,kBAAkB;AAC9C,uBAAiB,QAAQ;AAAA,IAC3B;AAEA,UAAM,oBAAoB,MAAM;AAC9B,uBAAiB,QAAQ;AACzB,qBAAe,QAAQ;AAAA,IACzB;AAEA,UAAM,mBAAmB,OAAO,SAAS;AACvC,UAAI;AACF,cAAMC,kCAAwB;AAAA,UAC5B,cAAc,UAAU;AAAA,UACxB,aAAa,KAAK;AAAA,QACxB,CAAK;AACDD,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAED,YAAI,OAAO,OAAO;AAChB,iBAAO,MAAM,OAAQ;AAAA,QACtB;AAAA,MACF,SAAQ,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,qCAAc,WAAW,KAAK;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACF;AAAA,IACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC9HA,GAAG,WAAW,eAAe;"}