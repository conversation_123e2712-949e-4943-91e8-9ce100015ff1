/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.container.data-v-ddee21bb {
  background-color: #ffffff;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 搜索容器 */
.search-container.data-v-ddee21bb {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #ffffff;
  border-bottom: 1px solid #f0f0f0;
}
.search-box.data-v-ddee21bb {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #f8f9fa;
  border-radius: 40rpx;
  padding: 0 24rpx;
  height: 80rpx;
  margin-right: 20rpx;
}
.search-icon.data-v-ddee21bb {
  width: 32rpx;
  height: 32rpx;
  margin-right: 16rpx;
}
.search-input.data-v-ddee21bb {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  height: 100%;
}
.placeholder.data-v-ddee21bb {
  color: #999;
}
.clear-btn.data-v-ddee21bb {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #ccc;
  border-radius: 50%;
}
.clear-text.data-v-ddee21bb {
  font-size: 24rpx;
  color: #fff;
  line-height: 1;
}
.cancel-btn.data-v-ddee21bb {
  padding: 0 10rpx;
}
.cancel-text.data-v-ddee21bb {
  font-size: 28rpx;
  color: #222222;
}

/* 内容容器 */
.content-container.data-v-ddee21bb {
  flex: 1;
  overflow: hidden;
  background-color: #ffffff;
}
.scroll-view.data-v-ddee21bb {
  height: 100%;
}

/* 状态容器 */
.loading-container.data-v-ddee21bb,
.empty-container.data-v-ddee21bb,
.default-container.data-v-ddee21bb {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
}
.loading-text.data-v-ddee21bb {
  font-size: 28rpx;
  color: #999;
}
.empty-icon.data-v-ddee21bb,
.default-icon.data-v-ddee21bb {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 24rpx;
}
.empty-text.data-v-ddee21bb,
.default-text.data-v-ddee21bb {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 12rpx;
}
.empty-desc.data-v-ddee21bb {
  font-size: 24rpx;
  color: #999;
}

/* 智能体列表 */
.agent-list.data-v-ddee21bb {
  padding: 0 20px;
  background-color: #ffffff;
}
.agent-item.data-v-ddee21bb {
  display: flex;
  align-items: center;
  padding: 20px 0;
}
.agent-item.data-v-ddee21bb:last-child {
  border-bottom: none;
}
.avatar.data-v-ddee21bb {
  width: 48px;
  height: 48px;
  margin-right: 16px;
  flex-shrink: 0;
}
.avatar .avatar-img.data-v-ddee21bb {
  width: 100%;
  height: 100%;
  border-radius: 24px;
}
.content.data-v-ddee21bb {
  flex: 1;
  margin-right: 16px;
}
.content .title.data-v-ddee21bb {
  font-size: 32rpx;
  font-weight: 600;
  color: #222;
  margin-bottom: 6px;
  line-height: 1.3;
}
.content .description.data-v-ddee21bb {
  font-size: 24rpx;
  color: #333;
  line-height: 1.4;
  margin-bottom: 6px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}
.content .author.data-v-ddee21bb {
  font-size: 24rpx;
  color: #999999;
}
.action-btn.data-v-ddee21bb {
  flex-shrink: 0;
  height: 32px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 116rpx;
  background-color: #007AFF;
}
.action-btn .btn-text.data-v-ddee21bb {
  font-size: 28rpx;
  font-weight: 500;
  color: #ffffff;
}
.action-btn.subscribed.data-v-ddee21bb {
  background-color: #F2F2F7;
  border: none;
}
.action-btn.subscribed .btn-text.data-v-ddee21bb {
  color: #8E8E93;
}

/* 加载更多 */
.load-more.data-v-ddee21bb,
.no-more.data-v-ddee21bb {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}
.load-more-text.data-v-ddee21bb,
.no-more-text.data-v-ddee21bb {
  font-size: 24rpx;
  color: #999;
}