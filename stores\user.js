import {
	defineStore
} from 'pinia';

export const useUserStore = defineStore('user', {
	state: () => {
		return {
			//小程序APPid
			wxappid: 'wx4658f2fe45d90c20',
			//商户guid
			// merchantGuid: 'e108201b02ae42e686bcc4c302cbbd11 正式2a5edd395c26499ab0d8966f8061573f', 测试938d1d20d13c4130a79f3d7590e62720
			merchantGuid: '2a5edd395c26499ab0d8966f8061573f',
			userToken: uni.getStorageSync('userToken') || '',
			// userToken: 'a87df0550b544c308772ec886461f5fc',
			//用户信息
			userInfo: uni.getStorageSync('userInfo') || {
				chat_count: 0
			},
			modalStatus: '',
			shareImg: '',
			appName: '思链IP智能体',
			// 临时存储要切换到的分类guid
			targetCategoryGuid: '',
			//分佣邀请码
			invitationCode: ''
		};
	},
	// 也可以这样定义
	// state: () => ({ count: 0 })
	actions: {
		//存储用户token
		set_user_token(token) {
			this.userToken = token;
			uni.setStorageSync('userToken', token);
		},
		//存储用户信息
		set_user_info(userInfo) {
			this.userInfo = userInfo;
			uni.setStorageSync('userInfo', userInfo);
		},
		//删除用户信息
		delete_user_info() {
			this.userToken = '';
			this.userInfo = {};
			uni.removeStorageSync('userInfo');
			uni.removeStorageSync('userToken');
		},
		//设置目标分类guid
		set_target_category(categoryGuid) {
			this.targetCategoryGuid = categoryGuid;
		},
		//清除目标分类guid
		clear_target_category() {
			this.targetCategoryGuid = '';
		},
		//设置分佣邀请码
		set_invitation_code(invitationCode) {
			this.invitationCode = invitationCode;
		}
	},
});