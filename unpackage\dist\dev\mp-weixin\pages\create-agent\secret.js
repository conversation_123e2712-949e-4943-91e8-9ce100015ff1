"use strict";
const common_vendor = require("../../common/vendor.js");
const api_index = require("../../api/index.js");
const stores_user = require("../../stores/user.js");
const _sfc_main = {
  __name: "secret",
  setup(__props) {
    const userStore = stores_user.useUserStore();
    let secretForm = common_vendor.reactive({
      secretKey: "",
      secretKeyType: "coze_api_key"
    });
    const getSecretKeyList = async () => {
      await api_index.getSecretKeyListApi({
        merchantGuid: userStore.merchantGuid
      });
    };
    const saving = common_vendor.ref(false);
    const handleSave = () => {
    };
    common_vendor.onMounted(() => {
      getSecretKeyList();
    });
    return (_ctx, _cache) => {
      return {
        a: common_vendor.unref(secretForm).secretKey,
        b: common_vendor.o(($event) => common_vendor.unref(secretForm).secretKey = $event.detail.value),
        c: common_vendor.t(saving.value ? "保存中..." : "保存修改"),
        d: common_vendor.o(handleSave)
      };
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-358c5fc5"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/create-agent/secret.js.map
