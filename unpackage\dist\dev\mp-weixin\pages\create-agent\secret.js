"use strict";
const common_vendor = require("../../common/vendor.js");
const api_index = require("../../api/index.js");
const stores_user = require("../../stores/user.js");
const _sfc_main = {
  __name: "secret",
  setup(__props) {
    const userStore = stores_user.useUserStore();
    let secretForm = common_vendor.reactive({
      secretKey: "",
      secretKeyType: "coze_api_key"
    });
    const getSecretKeyList = async () => {
      try {
        let res = await api_index.getSecretKeyListApi({
          merchantGuid: userStore.merchantGuid
        });
        if (res.code === 0 && res.data && res.data.length > 0) {
          const cozeKey = res.data.find((item) => item.secretKeyType === "coze_api_key");
          if (cozeKey) {
            secretForm.secretKey = cozeKey.secretKey;
          }
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/create-agent/secret.vue:48", "获取秘钥列表失败:", error);
        common_vendor.index.showToast({
          title: "获取秘钥失败",
          icon: "none"
        });
      }
    };
    const saving = common_vendor.ref(false);
    const handleSave = async () => {
      if (!secretForm.secretKey.trim()) {
        common_vendor.index.showToast({
          title: "请输入秘钥",
          icon: "none"
        });
        return;
      }
      saving.value = true;
      try {
        const saveData = {
          merchantGuid: userStore.merchantGuid,
          saveSecretKeyList: [
            {
              secretKeyType: secretForm.secretKeyType,
              secretKey: secretForm.secretKey.trim()
            }
          ]
        };
        let res = await api_index.saveSecretKeyApi(saveData);
        if (res.code === 0) {
          common_vendor.index.showToast({
            title: "保存成功",
            icon: "success"
          });
          setTimeout(() => {
            common_vendor.index.navigateBack();
          }, 1500);
        } else {
          common_vendor.index.showToast({
            title: res.msg || "保存失败",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/create-agent/secret.vue:100", "保存秘钥失败:", error);
        common_vendor.index.showToast({
          title: "保存失败",
          icon: "none"
        });
      } finally {
        saving.value = false;
      }
    };
    common_vendor.onMounted(() => {
      getSecretKeyList();
    });
    return (_ctx, _cache) => {
      return {
        a: common_vendor.unref(secretForm).secretKey,
        b: common_vendor.o(($event) => common_vendor.unref(secretForm).secretKey = $event.detail.value),
        c: common_vendor.t(saving.value ? "保存中..." : "保存修改"),
        d: saving.value ? 1 : "",
        e: common_vendor.o(handleSave)
      };
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-358c5fc5"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/create-agent/secret.js.map
