"use strict";const e=require("../../common/vendor.js"),t=require("../../common/assets.js"),a=require("../../api/index.js"),i=require("../../stores/user.js");if(!Array){e.resolveComponent("z-paging")()}Math;const n={__name:"agent-list",setup(n){const s=i.useUserStore(),u=e.ref([]),o=e.ref(null),l=e.ref([{label:"内部",value:1},{label:"dify",value:2},{label:"coze",value:3},{label:"阿里云百炼",value:4}]),r={1:"待审核",2:"审核通过",3:"审核拒绝"},c=async(t,i)=>{try{let e=await a.getMyAgentListApi({merchantGuid:s.merchantGuid,page:t,pageSize:i});o.value.complete(e.data.data||[])}catch(n){console.error("获取智能体列表失败:",n),e.index.showToast({title:"加载失败",icon:"none"}),o.value.complete(!1)}},d=()=>{e.index.navigateTo({url:"/pages/create-agent/index"})};return(i,n)=>({a:e.f(u.value,((t,i,n)=>e.e({a:t.agentAvatar,b:e.t(t.agentName),c:e.t(t.agentDesc),d:e.t(l.value.find((e=>e.value===t.agentType)).label),e:e.t(t.priceText),f:e.t(t.isPublicText),g:e.o((a=>(t=>{e.index.navigateTo({url:`/pages/create-agent/index?guid=${t.guid}`})})(t)),i),h:e.o((i=>(async t=>{e.index.showModal({title:"提示",content:"确定要删除该智能体吗？",success:async i=>{i.confirm&&(await a.deleteAgentApi({merchantGuid:s.merchantGuid,agentGuid:t.guid}),e.index.showToast({title:"删除成功",icon:"success"}),getMyAgentList())}})})(t)),i),i:2!=t.auditStatus},2!=t.auditStatus?{j:e.t(r[t.auditStatus])}:{},{k:2===t.auditStatus},2===t.auditStatus?{l:e.t(r[t.auditStatus])}:{},{m:i}))),b:e.sr(o,"892ba0a0-0",{k:"paging"}),c:e.o(c),d:e.o((e=>u.value=e)),e:e.p({auto:!0,"auto-clean-list-when-reload":!1,modelValue:u.value}),f:t._imports_0$1,g:e.o(d)})}},s=e._export_sfc(n,[["__scopeId","data-v-892ba0a0"]]);wx.createPage(s);
