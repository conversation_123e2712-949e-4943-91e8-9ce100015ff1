{"version": 3, "file": "ua-markdown.js", "sources": ["components/ua-markdown/ua-markdown.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RToveW91bmdQcm9qZWN0L2FnZW50LW1pbmktdWkvY29tcG9uZW50cy91YS1tYXJrZG93bi91YS1tYXJrZG93bi52dWU"], "sourcesContent": ["<!-- uniapp vue3 markdown解析 -->\r\n<template>\r\n\t<view class=\"ua__markdown\"><rich-text space=\"nbsp\" :user-select=\"true\" :nodes=\"parseNodes(source)\"\r\n\t\t\t@itemclick=\"handleItemClick\"></rich-text></view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, computed } from 'vue'\r\nimport MarkdownIt from './lib/markdown-it.min.js'\r\nimport hljs from './lib/highlight/uni-highlight.min.js'\r\nimport './lib/highlight/atom-one-dark.css'\r\nimport parseHtml from './lib/html-parser.js'\r\nconst props = defineProps({\r\n\t// 解析内容\r\n\tsource: String,\r\n\tshowLine: { type: [Boolean, String], default: true }\r\n})\r\n\r\nlet copyCodeData = []\r\nconst markdown = MarkdownIt({\r\n\thtml: true,\r\n\thighlight: function (str, lang) {\r\n\t\tlet preCode = \"\"\r\n\t\ttry {\r\n\t\t\tpreCode = hljs.highlightAuto(str).value\r\n\t\t} catch (err) {\r\n\t\t\tpreCode = markdown.utils.escapeHtml(str);\r\n\t\t}\r\n\t\tconst lines = preCode.split(/\\n/).slice(0, -1)\r\n\t\t// 添加自定义行号\r\n\t\tlet html = lines.map((item, index) => {\r\n\t\t\tif (item == '') {\r\n\t\t\t\treturn ''\r\n\t\t\t}\r\n\t\t\treturn '<li><span class=\"line-num\" data-line=\"' + (index + 1) + '\"></span>' + item + '</li>'\r\n\t\t}).join('')\r\n\t\tif (props.showLine) {\r\n\t\t\thtml = '<ol style=\"padding: 0px 30px;\">' + html + '</ol>'\r\n\t\t} else {\r\n\t\t\thtml = '<ol style=\"padding: 0px 7px;list-style:none;\">' + html + '</ol>'\r\n\t\t}\r\n\t\tcopyCodeData.push(str)\r\n\t\tlet htmlCode = `<div class=\"markdown-wrap\">`\r\n\t\t// #ifndef MP-WEIXIN\r\n\t\thtmlCode += `<div style=\"color: #aaa;text-align: right;font-size: 12px;padding:8px;\">`\r\n\t\thtmlCode += `${lang}<a class=\"copy-btn\" code-data-index=\"${copyCodeData.length - 1}\" style=\"margin-left: 8px;\">复制代码</a>`\r\n\t\thtmlCode += `</div>`\r\n\t\t// #endif\r\n\t\thtmlCode += `<pre class=\"hljs\" style=\"padding:10px 8px 0;margin-bottom:5px;overflow: auto;display: block;border-radius: 5px;\"><code>${html}</code></pre>`;\r\n\t\thtmlCode += '</div>'\r\n\t\treturn htmlCode\r\n\t}\r\n})\r\nconst parseNodes = (value) => {\r\n\tif (!value) return\r\n\t// 解析<br />到\\n\r\n\tvalue = value.replace(/<br>|<br\\/>|<br \\/>/g, \"\\n\")\r\n\tvalue = value.replace(/&nbsp;/g, \" \")\r\n\tlet htmlString = ''\r\n\tif (value.split(\"```\").length % 2) {\r\n\t\tlet mdtext = value\r\n\t\tif (mdtext[mdtext.length - 1] != '\\n') {\r\n\t\t\tmdtext += '\\n'\r\n\t\t}\r\n\t\thtmlString = markdown.render(mdtext)\r\n\t} else {\r\n\t\thtmlString = markdown.render(value)\r\n\t}\r\n\t// 解决小程序表格边框型失效问题\r\n\thtmlString = htmlString.replace(/<table/g, `<table class=\"table\"`)\r\n\thtmlString = htmlString.replace(/<tr/g, `<tr class=\"tr\"`)\r\n\thtmlString = htmlString.replace(/<th>/g, `<th class=\"th\">`)\r\n\thtmlString = htmlString.replace(/<td/g, `<td class=\"td\"`)\r\n\thtmlString = htmlString.replace(/<hr>|<hr\\/>|<hr \\/>/g, `<hr class=\"hr\">`)\r\n\r\n\t// #ifndef APP-NVUE\r\n\treturn htmlString\r\n\t// #endif\r\n\r\n\t// 将htmlString转成htmlArray，反之使用rich-text解析\r\n\t// #ifdef APP-NVUE\r\n\treturn parseHtml(htmlString)\r\n\t// #endif\r\n}\r\n\r\n// 复制代码\r\nconst handleItemClick = (e) => {\r\n\tlet { attrs } = e.detail.node\r\n\tlet { \"code-data-index\": codeDataIndex, \"class\": className } = attrs\r\n\tif (className == 'copy-btn') {\r\n\t\tuni.setClipboardData({\r\n\t\t\tdata: copyCodeData[codeDataIndex], showToast: false,\r\n\t\t\tsuccess() {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '复制成功', icon: 'none'\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t})\r\n\t}\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.ua__markdown {\r\n\tfont-size: 16px;\r\n\tline-height: 1.5;\r\n\tword-break: break-all;\r\n\r\n\th1,\r\n\th2,\r\n\th3,\r\n\th4,\r\n\th5,\r\n\th6 {\r\n\t\tfont-family: inherit;\r\n\t\tfont-weight: 500;\r\n\t\tline-height: 1.1;\r\n\t\tcolor: inherit;\r\n\t}\r\n\r\n\th1,\r\n\th2,\r\n\th3 {\r\n\t\tmargin-top: 20px;\r\n\t\tmargin-bottom: 10px\r\n\t}\r\n\r\n\th4,\r\n\th5,\r\n\th6 {\r\n\t\tmargin-top: 10px;\r\n\t\tmargin-bottom: 10px\r\n\t}\r\n\r\n\t.h1,\r\n\th1 {\r\n\t\tfont-size: 36px\r\n\t}\r\n\r\n\t.h2,\r\n\th2 {\r\n\t\tfont-size: 30px\r\n\t}\r\n\r\n\t.h3,\r\n\th3 {\r\n\t\tfont-size: 24px\r\n\t}\r\n\r\n\t.h4,\r\n\th4 {\r\n\t\tfont-size: 18px\r\n\t}\r\n\r\n\t.h5,\r\n\th5 {\r\n\t\tfont-size: 14px\r\n\t}\r\n\r\n\t.h6,\r\n\th6 {\r\n\t\tfont-size: 12px\r\n\t}\r\n\r\n\ta {\r\n\t\tbackground-color: transparent;\r\n\t\tcolor: #2196f3;\r\n\t\ttext-decoration: none;\r\n\t}\r\n\r\n\thr,\r\n\t::v-deep .hr {\r\n\t\tmargin-top: 20px;\r\n\t\tmargin-bottom: 20px;\r\n\t\tborder: 0;\r\n\t\tborder-top: 1px solid #e5e5e5;\r\n\t}\r\n\r\n\timg {\r\n\t\tmax-width: 35%;\r\n\t}\r\n\r\n\tp {\r\n\t\tmargin: 0 0 10px\r\n\t}\r\n\r\n\tem {\r\n\t\tfont-style: italic;\r\n\t\tfont-weight: inherit;\r\n\t}\r\n\r\n\tol,\r\n\tul {\r\n\t\tmargin-top: 0;\r\n\t\tmargin-bottom: 10px;\r\n\t\tpadding-left: 40px;\r\n\t}\r\n\r\n\tol ol,\r\n\tol ul,\r\n\tul ol,\r\n\tul ul {\r\n\t\tmargin-bottom: 0;\r\n\t}\r\n\r\n\tol ol,\r\n\tul ol {\r\n\t\tlist-style-type: lower-roman;\r\n\t}\r\n\r\n\tol ol ol,\r\n\tul ul ol {\r\n\t\tlist-style-type: lower-alpha;\r\n\t}\r\n\r\n\tdl {\r\n\t\tmargin-top: 0;\r\n\t\tmargin-bottom: 20px;\r\n\t}\r\n\r\n\tdt {\r\n\t\tfont-weight: 600;\r\n\t}\r\n\r\n\tdt,\r\n\tdd {\r\n\t\tline-height: 1.4;\r\n\t}\r\n\r\n\t.task-list-item {\r\n\t\tlist-style-type: none;\r\n\t}\r\n\r\n\t.task-list-item input {\r\n\t\tmargin: 0 .2em .25em -1.6em;\r\n\t\tvertical-align: middle;\r\n\t}\r\n\r\n\tpre {\r\n\t\tposition: relative;\r\n\t\tz-index: 11;\r\n\t}\r\n\r\n\tcode,\r\n\tkbd,\r\n\tpre,\r\n\tsamp {\r\n\t\tfont-family: Menlo, Monaco, Consolas, \"Courier New\", monospace;\r\n\t}\r\n\r\n\tcode:not(.hljs) {\r\n\t\tpadding: 2px 4px;\r\n\t\tfont-size: 90%;\r\n\t\tcolor: #c7254e;\r\n\t\tbackground-color: #ffe7ee;\r\n\t\tborder-radius: 4px;\r\n\t}\r\n\r\n\tcode:empty {\r\n\t\tdisplay: none;\r\n\t}\r\n\r\n\tpre code.hljs {\r\n\t\tcolor: var(--vg__text-1);\r\n\t\tborder-radius: 16px;\r\n\t\tbackground: var(--vg__bg-1);\r\n\t\tfont-size: 12px;\r\n\t}\r\n\r\n\t.markdown-wrap {\r\n\t\tfont-size: 12px;\r\n\t\tmargin-bottom: 10px;\r\n\t}\r\n\r\n\tpre.code-block-wrapper {\r\n\t\tbackground: #2b2b2b;\r\n\t\tcolor: #f8f8f2;\r\n\t\tborder-radius: 4px;\r\n\t\toverflow-x: auto;\r\n\t\tpadding: 1em;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\tpre.code-block-wrapper code {\r\n\t\tpadding: auto;\r\n\t\tfont-size: inherit;\r\n\t\tcolor: inherit;\r\n\t\tbackground-color: inherit;\r\n\t\tborder-radius: 0;\r\n\t}\r\n\r\n\t.code-block-header__copy {\r\n\t\tfont-size: 16px;\r\n\t\tmargin-left: 5px;\r\n\t}\r\n\r\n\tabbr[data-original-title],\r\n\tabbr[title] {\r\n\t\tcursor: help;\r\n\t\tborder-bottom: 1px dotted #777;\r\n\t}\r\n\r\n\tblockquote {\r\n\t\tpadding: 10px 20px;\r\n\t\tmargin: 0 0 20px;\r\n\t\tfont-size: 17.5px;\r\n\t\tborder-left: 5px solid #e5e5e5;\r\n\t}\r\n\r\n\tblockquote ol:last-child,\r\n\tblockquote p:last-child,\r\n\tblockquote ul:last-child {\r\n\t\tmargin-bottom: 0\r\n\t}\r\n\r\n\tblockquote .small,\r\n\tblockquote footer,\r\n\tblockquote small {\r\n\t\tdisplay: block;\r\n\t\tfont-size: 80%;\r\n\t\tline-height: 1.42857143;\r\n\t\tcolor: #777\r\n\t}\r\n\r\n\tblockquote .small:before,\r\n\tblockquote footer:before,\r\n\tblockquote small:before {\r\n\t\tcontent: '\\2014 \\00A0'\r\n\t}\r\n\r\n\t.blockquote-reverse,\r\n\tblockquote.pull-right {\r\n\t\tpadding-right: 15px;\r\n\t\tpadding-left: 0;\r\n\t\ttext-align: right;\r\n\t\tborder-right: 5px solid #eee;\r\n\t\tborder-left: 0\r\n\t}\r\n\r\n\t.blockquote-reverse .small:before,\r\n\t.blockquote-reverse footer:before,\r\n\t.blockquote-reverse small:before,\r\n\tblockquote.pull-right .small:before,\r\n\tblockquote.pull-right footer:before,\r\n\tblockquote.pull-right small:before {\r\n\t\tcontent: ''\r\n\t}\r\n\r\n\t.blockquote-reverse .small:after,\r\n\t.blockquote-reverse footer:after,\r\n\t.blockquote-reverse small:after,\r\n\tblockquote.pull-right .small:after,\r\n\tblockquote.pull-right footer:after,\r\n\tblockquote.pull-right small:after {\r\n\t\tcontent: '\\00A0 \\2014'\r\n\t}\r\n\r\n\t.footnotes {\r\n\t\t-moz-column-count: 2;\r\n\t\t-webkit-column-count: 2;\r\n\t\tcolumn-count: 2\r\n\t}\r\n\r\n\t.footnotes-list {\r\n\t\tpadding-left: 2em\r\n\t}\r\n\r\n\ttable,\r\n\t::v-deep .table {\r\n\t\tborder-spacing: 0;\r\n\t\tborder-collapse: collapse;\r\n\t\twidth: 100%;\r\n\t\tmax-width: 65em;\r\n\t\toverflow: auto;\r\n\t\tmargin-top: 0;\r\n\t\tmargin-bottom: 16px;\r\n\t}\r\n\r\n\ttable tr,\r\n\t::v-deep .table .tr {\r\n\t\tborder-top: 1px solid #e5e5e5;\r\n\t}\r\n\r\n\ttable th,\r\n\ttable td,\r\n\t::v-deep .table .th,\r\n\t::v-deep .table .td {\r\n\t\tpadding: 6px 13px;\r\n\t\tborder: 1px solid #e5e5e5;\r\n\t}\r\n\r\n\ttable th,\r\n\t::v-deep .table .th {\r\n\t\tfont-weight: 600;\r\n\t\tbackground-color: #eee;\r\n\t}\r\n\r\n\t.hljs[class*=language-]:before {\r\n\t\tposition: absolute;\r\n\t\tz-index: 3;\r\n\t\ttop: .8em;\r\n\t\tright: 1em;\r\n\t\tfont-size: .8em;\r\n\t\tcolor: #999;\r\n\t}\r\n\r\n\t.hljs[class~=language-js]:before {\r\n\t\tcontent: \"js\"\r\n\t}\r\n\r\n\t.hljs[class~=language-ts]:before {\r\n\t\tcontent: \"ts\"\r\n\t}\r\n\r\n\t.hljs[class~=language-html]:before {\r\n\t\tcontent: \"html\"\r\n\t}\r\n\r\n\t.hljs[class~=language-md]:before {\r\n\t\tcontent: \"md\"\r\n\t}\r\n\r\n\t.hljs[class~=language-vue]:before {\r\n\t\tcontent: \"vue\"\r\n\t}\r\n\r\n\t.hljs[class~=language-css]:before {\r\n\t\tcontent: \"css\"\r\n\t}\r\n\r\n\t.hljs[class~=language-sass]:before {\r\n\t\tcontent: \"sass\"\r\n\t}\r\n\r\n\t.hljs[class~=language-scss]:before {\r\n\t\tcontent: \"scss\"\r\n\t}\r\n\r\n\t.hljs[class~=language-less]:before {\r\n\t\tcontent: \"less\"\r\n\t}\r\n\r\n\t.hljs[class~=language-stylus]:before {\r\n\t\tcontent: \"stylus\"\r\n\t}\r\n\r\n\t.hljs[class~=language-go]:before {\r\n\t\tcontent: \"go\"\r\n\t}\r\n\r\n\t.hljs[class~=language-java]:before {\r\n\t\tcontent: \"java\"\r\n\t}\r\n\r\n\t.hljs[class~=language-c]:before {\r\n\t\tcontent: \"c\"\r\n\t}\r\n\r\n\t.hljs[class~=language-sh]:before {\r\n\t\tcontent: \"sh\"\r\n\t}\r\n\r\n\t.hljs[class~=language-yaml]:before {\r\n\t\tcontent: \"yaml\"\r\n\t}\r\n\r\n\t.hljs[class~=language-py]:before {\r\n\t\tcontent: \"py\"\r\n\t}\r\n\r\n\t.hljs[class~=language-docker]:before {\r\n\t\tcontent: \"docker\"\r\n\t}\r\n\r\n\t.hljs[class~=language-dockerfile]:before {\r\n\t\tcontent: \"dockerfile\"\r\n\t}\r\n\r\n\t.hljs[class~=language-makefile]:before {\r\n\t\tcontent: \"makefile\"\r\n\t}\r\n\r\n\t.hljs[class~=language-javascript]:before {\r\n\t\tcontent: \"js\"\r\n\t}\r\n\r\n\t.hljs[class~=language-typescript]:before {\r\n\t\tcontent: \"ts\"\r\n\t}\r\n\r\n\t.hljs[class~=language-markup]:before {\r\n\t\tcontent: \"html\"\r\n\t}\r\n\r\n\t.hljs[class~=language-markdown]:before {\r\n\t\tcontent: \"md\"\r\n\t}\r\n\r\n\t.hljs[class~=language-json]:before {\r\n\t\tcontent: \"json\"\r\n\t}\r\n\r\n\t.hljs[class~=language-ruby]:before {\r\n\t\tcontent: \"rb\"\r\n\t}\r\n\r\n\t.hljs[class~=language-python]:before {\r\n\t\tcontent: \"py\"\r\n\t}\r\n\r\n\t.hljs[class~=language-bash]:before {\r\n\t\tcontent: \"sh\"\r\n\t}\r\n\r\n\t.hljs[class~=language-php]:before {\r\n\t\tcontent: \"php\"\r\n\t}\r\n}\r\n</style>\r\n", "import Component from 'E:/youngProject/agent-mini-ui/components/ua-markdown/ua-markdown.vue'\nwx.createComponent(Component)"], "names": ["MarkdownIt", "hljs", "uni"], "mappings": ";;;;;;;;;;;;;AAYA,UAAM,QAAQ;AAMd,QAAI,eAAe,CAAE;AACrB,UAAM,WAAWA,yCAAAA,GAAW;AAAA,MAC3B,MAAM;AAAA,MACN,WAAW,SAAU,KAAK,MAAM;AAC/B,YAAI,UAAU;AACd,YAAI;AACH,oBAAUC,qDAAI,GAAC,cAAc,GAAG,EAAE;AAAA,QAClC,SAAQ,KAAK;AACb,oBAAU,SAAS,MAAM,WAAW,GAAG;AAAA,QACvC;AACD,cAAM,QAAQ,QAAQ,MAAM,IAAI,EAAE,MAAM,GAAG,EAAE;AAE7C,YAAI,OAAO,MAAM,IAAI,CAAC,MAAM,UAAU;AACrC,cAAI,QAAQ,IAAI;AACf,mBAAO;AAAA,UACP;AACD,iBAAO,4CAA4C,QAAQ,KAAK,cAAc,OAAO;AAAA,QACxF,CAAG,EAAE,KAAK,EAAE;AACV,YAAI,MAAM,UAAU;AACnB,iBAAO,oCAAoC,OAAO;AAAA,QACrD,OAAS;AACN,iBAAO,mDAAmD,OAAO;AAAA,QACjE;AACD,qBAAa,KAAK,GAAG;AACrB,YAAI,WAAW;AAMf,oBAAY,0HAA0H,IAAI;AAC1I,oBAAY;AACZ,eAAO;AAAA,MACP;AAAA,IACF,CAAC;AACD,UAAM,aAAa,CAAC,UAAU;AAC7B,UAAI,CAAC;AAAO;AAEZ,cAAQ,MAAM,QAAQ,wBAAwB,IAAI;AAClD,cAAQ,MAAM,QAAQ,WAAW,GAAG;AACpC,UAAI,aAAa;AACjB,UAAI,MAAM,MAAM,KAAK,EAAE,SAAS,GAAG;AAClC,YAAI,SAAS;AACb,YAAI,OAAO,OAAO,SAAS,CAAC,KAAK,MAAM;AACtC,oBAAU;AAAA,QACV;AACD,qBAAa,SAAS,OAAO,MAAM;AAAA,MACrC,OAAQ;AACN,qBAAa,SAAS,OAAO,KAAK;AAAA,MAClC;AAED,mBAAa,WAAW,QAAQ,WAAW,sBAAsB;AACjE,mBAAa,WAAW,QAAQ,QAAQ,gBAAgB;AACxD,mBAAa,WAAW,QAAQ,SAAS,iBAAiB;AAC1D,mBAAa,WAAW,QAAQ,QAAQ,gBAAgB;AACxD,mBAAa,WAAW,QAAQ,wBAAwB,iBAAiB;AAGzE,aAAO;AAAA,IAOR;AAGA,UAAM,kBAAkB,CAAC,MAAM;AAC9B,UAAI,EAAE,MAAK,IAAK,EAAE,OAAO;AACzB,UAAI,EAAE,mBAAmB,eAAe,SAAS,UAAW,IAAG;AAC/D,UAAI,aAAa,YAAY;AAC5BC,sBAAAA,MAAI,iBAAiB;AAAA,UACpB,MAAM,aAAa,aAAa;AAAA,UAAG,WAAW;AAAA,UAC9C,UAAU;AACTA,0BAAAA,MAAI,UAAU;AAAA,cACb,OAAO;AAAA,cAAQ,MAAM;AAAA,YAC1B,CAAK;AAAA,UACD;AAAA,QACJ,CAAG;AAAA,MACD;AAAA,IACF;;;;;;;;;;AClGA,GAAG,gBAAgB,SAAS;"}