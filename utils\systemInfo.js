/**
 * 此js文件管理关于当前设备的机型系统信息
 */
const systemInfo = function () {
  /****************** 所有平台共有的系统信息 ********************/
  // 设备系统信息
  let systemInfomations = uni.getSystemInfoSync()
  let safeHeight = 0;
  if (systemInfomations.osName === 'ios') {
    safeHeight = systemInfomations.screenHeight - systemInfomations.safeArea.bottom;
  } else if (systemInfomations.osName === 'android') {
    safeHeight = 36;
  }
  //当前机型系统
  let osName = systemInfomations.osName;
  // 机型适配比例系数
  let scaleFactor = 750 / systemInfomations.windowWidth
  // 当前机型-屏幕高度
  let windowHeight = systemInfomations.windowHeight * scaleFactor //rpx
  // 当前机型-屏幕宽度
  let windowWidth = systemInfomations.windowWidth * scaleFactor //rpx
  // 当前机型-屏幕高度px
  let windowHeightpx = systemInfomations.windowHeight //px
  // 当前机型-屏幕宽度px
  let windowWidthpx = systemInfomations.windowWidth  //px
  // 状态栏高度
  // let statusBarHeight = (systemInfomations.statusBarHeight) * scaleFactor //rpx
  let statusBarHeight = systemInfomations.statusBarHeight;//px

  // 导航栏高度  注意：此导航栏高度只针对微信小程序有效 其他平台如自定义导航栏请使用：状态栏高度+自定义文本高度
  let navHeight = 0 //rpx

  /****************** 微信小程序头部胶囊信息 ********************/
  // #ifdef MP-WEIXIN
  const menuButtonInfo = wx.getMenuButtonBoundingClientRect()
  // 胶囊高度
  // let menuButtonHeight = menuButtonInfo.height * scaleFactor //rpx
  let menuButtonHeight = menuButtonInfo.height;//px
  // // 胶囊宽度
  // let menuButtonWidth = menuButtonInfo.width * scaleFactor //rpx
  let menuButtonWidth = menuButtonInfo.width;//px
  // // 胶囊上边界的坐标
  // let menuButtonTop = menuButtonInfo.top * scaleFactor //rpx
  let menuButtonTop = menuButtonInfo.top;//px
  // // 胶囊右边界的坐标
  // let menuButtonRight = menuButtonInfo.right * scaleFactor //rpx
  let menuButtonRight = menuButtonInfo.right;//px
  // // 胶囊下边界的坐标
  // let menuButtonBottom = menuButtonInfo.bottom * scaleFactor //rpx
  let menuButtonBottom = menuButtonInfo.bottom;//px
  // // 胶囊左边界的坐标
  // let menuButtonLeft = menuButtonInfo.left * scaleFactor //rpx
  let menuButtonLeft = menuButtonInfo.left;//px

  // 微信小程序中导航栏高度 = 胶囊高度 + (顶部距离 - 状态栏高度) * 2
  let countStatusBarHeight = 0
  if (statusBarHeight === 0) {
    countStatusBarHeight = menuButtonTop - 4;
  } else {
    countStatusBarHeight = statusBarHeight
  }
  navHeight = menuButtonHeight + (menuButtonTop - countStatusBarHeight) * 2;//px
  // #endif

  // #ifdef MP-WEIXIN
  return {
    scaleFactor,
    windowHeight,
    windowWidth,
    windowWidthpx,
    windowHeightpx,
    statusBarHeight,
    menuButtonHeight,
    menuButtonWidth,
    menuButtonTop,
    menuButtonRight,
    menuButtonBottom,
    menuButtonLeft,
    navHeight,
    safeHeight,
    osName
  }
  // #endif

  // #ifndef MP-WEIXIN
  return {
    scaleFactor,
    windowHeight,
    windowWidth,
    windowWidthpx,
    windowHeightpx,
    statusBarHeight,
    safeHeight,
    osName
  }
  // #endif
}

export {
  systemInfo
}