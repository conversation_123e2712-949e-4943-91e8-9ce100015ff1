<view class="agent-list-page data-v-e7ab54ac"><z-paging wx:if="{{e}}" class="r data-v-e7ab54ac" u-s="{{['d']}}" u-r="paging" bindquery="{{c}}" u-i="e7ab54ac-0" bind:__l="__l" bindupdateModelValue="{{d}}" u-p="{{e}}"><view class="agents-content data-v-e7ab54ac"><view wx:for="{{a}}" wx:for-item="agent" wx:key="m" class="agent-card data-v-e7ab54ac"><image class="agent-avatar data-v-e7ab54ac" src="{{agent.a}}" mode="aspectFill"/><view class="agent-info data-v-e7ab54ac"><view class="agent-title data-v-e7ab54ac">{{agent.b}}</view><text class="agent-desc data-v-e7ab54ac">{{agent.c}}</text><view class="agent-tags data-v-e7ab54ac"><view class="tag primary data-v-e7ab54ac">{{agent.d}}</view><view class="tag primary data-v-e7ab54ac">{{agent.e}}</view><view class="tag primary data-v-e7ab54ac">{{agent.f}}</view></view></view><view class="operate-box data-v-e7ab54ac"><view class="edit data-v-e7ab54ac" bindtap="{{agent.g}}">编辑</view><view class="delete data-v-e7ab54ac" bindtap="{{agent.h}}">删除</view></view><view wx:if="{{agent.i}}" class="status data-v-e7ab54ac">{{agent.j}}</view><view wx:if="{{agent.k}}" class="status success data-v-e7ab54ac">{{agent.l}}</view></view></view></z-paging><view class="create-agent data-v-e7ab54ac"><view class="create-btn data-v-e7ab54ac" bindtap="{{g}}"><image src="{{f}}" class="create-icon data-v-e7ab54ac" mode="aspectFit"/><text class="create-text data-v-e7ab54ac">创建AI智能体</text></view></view></view>