<template>
	<view class="video-complete-page">
		<!-- 视频播放区域 -->
		<view class="video-container">
			<video 
				class="video-player"
				:src="videoSrc"
				:poster="videoPoster"
				:autoplay="true"
				controls
				:show-fullscreen-btn="true"
				:show-play-btn="true"
				:show-center-play-btn="true"
				:enable-progress-gesture="true"
				:object-fit="'contain'"
			>
			</video>
		</view>

		<!-- 底部操作按钮 -->
		<view class="bottom-actions">
			<view class="action-btn save-btn" @tap="handleSave">
				<image class="action-icon" src="/static/my/video_download.png" mode="aspectFit" />
				<text class="action-text">保存作品</text>
			</view>
			<view class="action-btn share-btn" @tap="handleShare">
				<image class="action-icon" src="/static/my/video_share.png" mode="aspectFit" />
				<text class="action-text">分享好友</text>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getVideoDetailApi } from '@/api/index.js'
import { useUserStore } from '@/stores/user.js'

// 用户store
const userStore = useUserStore()

// 视频相关数据
const videoSrc = ref('')
const videoPoster = ref('')
const orderNo = ref('')

// 获取视频详情
const getVideoDetail = async () => {
	try {
		const res = await getVideoDetailApi({
			merchantGuid: userStore.merchantGuid,
			orderNo: orderNo.value
		})

		if (res.code === 0) {
			const { videoUrl, previewUrl } = res.data
			if (videoUrl) {
				videoSrc.value = videoUrl
			}
			if (previewUrl) {
				videoPoster.value = previewUrl
			}
		} else {
			console.error('获取视频详情失败:', res.msg)
			uni.showToast({
				title: '获取视频失败',
				icon: 'none'
			})
		}
	} catch (error) {
		console.error('获取视频详情失败:', error)
		uni.showToast({
			title: '获取视频失败',
			icon: 'none'
		})
	}
}

// 处理保存作品
const handleSave = async () => {
	if (!videoSrc.value) {
		uni.showToast({
			title: '视频还未加载完成',
			icon: 'none'
		})
		return
	}

	// 显示加载提示
	uni.showLoading({
		title: '正在保存...'
	})

	try {
		// 下载视频文件
		const downloadResult = await new Promise((resolve, reject) => {
			uni.downloadFile({
				url: videoSrc.value,
				success: (res) => {
					if (res.statusCode === 200) {
						resolve(res.tempFilePath)
					} else {
						reject(new Error('下载失败'))
					}
				},
				fail: (err) => {
					reject(err)
				}
			})
		})

		// 保存视频到相册
		await new Promise((resolve, reject) => {
			uni.saveVideoToPhotosAlbum({
				filePath: downloadResult,
				success: () => {
					resolve()
				},
				fail: (err) => {
					// 如果是权限问题，提示用户授权
					if (err.errMsg.includes('auth')) {
						uni.showModal({
							title: '提示',
							content: '需要您授权访问相册才能保存视频，请在设置中开启相册权限',
							showCancel: false
						})
					}
					reject(err)
				}
			})
		})

		uni.hideLoading()
		uni.showToast({
			title: '保存成功',
			icon: 'success',
			duration: 2000
		})

	} catch (error) {
		uni.hideLoading()
		console.error('保存视频失败:', error)
		uni.showToast({
			title: '保存失败，请重试',
			icon: 'none',
			duration: 2000
		})
	}
}

// 处理分享好友
const handleShare = () => {
	if (!videoSrc.value) {
		uni.showToast({
			title: '视频还未加载完成',
			icon: 'none'
		})
		return
	}

	uni.showActionSheet({
		itemList: ['微信好友', '朋友圈'],
		success: (res) => {
			const actions = ['微信好友', '朋友圈']
			uni.showToast({
				title: `分享到${actions[res.tapIndex]}`,
				icon: 'success',
				duration: 2000
			})
		}
	})
}

// 使用onLoad获取页面参数
onLoad((options) => {
	// getVideoDetail()
	// return

	if (options.orderNo) {
		orderNo.value = options.orderNo
		// 获取视频详情
		getVideoDetail()
	} else {
		// 如果没有orderNo参数，使用示例数据
		videoSrc.value = 'https://vd3.bdstatic.com/mda-ka0x6301f525mw5e/mda-ka0x6301f525mw5e.mp4?playlist=%5B%22hd%22%2C%22sc%22%5D'
	}
})

onMounted(() => {
	// 页面挂载时的其他初始化操作
})
</script>

<style lang="scss" scoped>
.video-complete-page {
	background: #F5F5F5;
	min-height: 100vh;
	display: flex;
	flex-direction: column;
	padding: 0;
	box-sizing: border-box;

	.video-container {
		height: calc(100vh - 180rpx);
		background: #000000;
		display: flex;
		align-items: center;
		justify-content: center;

		.video-player {
			width: 100%;
			height: 100%;
			background: #000000;
		}
	}

	.bottom-actions {
		background: #FFFFFF;
		padding: 0 30rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		height: 180rpx;

		.action-btn {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 330rpx;
			height: 90rpx;
			font-size: 32rpx;
			font-weight: 500;
			border-radius: 50rpx;

			&:active {
				opacity: 0.8;
			}

			.action-icon {
				width: 40rpx;
				height: 40rpx;
				margin-right: 14rpx;
			}

			.action-text {
				font-weight: 500;
			}

			&.save-btn {
				background: #ECECEC;
				border: 2rpx solid #E9ECEF;

				.action-text {
					color: #333333;
				}
			}

			&.share-btn {
				background: #2A64F6;

				.action-text {
					color: #FFFFFF;
				}
			}
		}
	}
}
</style>
