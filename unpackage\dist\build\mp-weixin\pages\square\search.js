"use strict";const e=require("../../common/vendor.js"),a=require("../../common/assets.js"),u=require("../../stores/user.js"),t=require("../../api/index.js"),l={__name:"search",setup(l){const i=u.useUserStore(),n=e.ref(""),s=e.ref([]),v=e.ref(!1),o=e.ref(!1),r=e.ref(""),c=e.ref(1),d=e.ref(10),g=e.ref(!0),m=e.ref(!1),h=()=>{},f=async()=>{n.value.trim()?(c.value=1,g.value=!0,s.value=[],await b()):e.index.showToast({title:"请输入搜索关键词",icon:"none"})},b=async()=>{if(!v.value&&!m.value)try{1===c.value?v.value=!0:m.value=!0;const e=await t.getAgentListApi({merchantGuid:i.merchantGuid,agentName:n.value.trim(),pageSize:d.value,page:c.value});if(0===e.code){const a=e.data.data||[];1===c.value?(s.value=a,o.value=!0):s.value=[...s.value,...a],g.value=c.value<e.data.last_page}}catch(a){console.error("搜索失败:",a),e.index.showToast({title:"搜索失败，请重试",icon:"none"})}finally{v.value=!1,m.value=!1}},p=async()=>{g.value&&!m.value&&(c.value++,await b())},_=()=>{n.value="",s.value=[],o.value=!1,c.value=1,g.value=!0},x=()=>{e.index.navigateBack()},w=a=>{console.log("点击智能体:",a.agentName),e.index.navigateTo({url:`/pages/square/detail?sysId=${a.sysId}`})};return e.onLoad((e=>{e.agentName&&(r.value=e.agentName,n.value=e.agentName,b())})),(u,l)=>e.e({a:a._imports_0$9,b:e.o([e=>n.value=e.detail.value,h]),c:e.o(f),d:n.value,e:n.value},n.value?{f:e.o(_)}:{},{g:e.o(x),h:v.value},v.value?{}:!v.value&&0===s.value.length&&o.value?{j:a._imports_0$9}:s.value.length>0?e.e({l:e.f(s.value,((a,u,l)=>({a:a.agentAvatar,b:e.o((e=>w(a)),a.guid),c:e.t(a.agentName),d:e.t(a.agentDesc),e:e.t(a.creator.nickname),f:e.o((e=>w(a)),a.guid),g:e.t(a.isSubscribed?"已订阅":"订阅"),h:a.isSubscribed?1:"",i:e.o((u=>(async a=>{const u={merchantGuid:i.merchantGuid,agentGuid:a.guid};try{await t.subscribeAgentApi(u);const l=s.value.findIndex((e=>e.guid===a.guid));-1!==l&&(s.value[l].isSubscribed=!s.value[l].isSubscribed),e.index.showToast({title:a.isSubscribed?"取消订阅成功":"订阅成功",icon:"none"})}catch(l){e.index.showToast({title:"操作失败",icon:"none"})}})(a)),a.guid),j:a.guid}))),m:g.value},g.value?{n:e.t(m.value?"加载中...":"上拉加载更多")}:(s.value.length,{}),{o:s.value.length>0,p:e.o(p)}):{q:a._imports_0$9},{i:!v.value&&0===s.value.length&&o.value,k:s.value.length>0})}},i=e._export_sfc(l,[["__scopeId","data-v-59b7fb21"]]);wx.createPage(i);
