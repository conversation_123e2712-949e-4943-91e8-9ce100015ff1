"use strict";
function e(e2) {
  if (e2.__esModule)
    return e2;
  var r2 = Object.defineProperty({}, "__esModule", { value: true });
  return Object.keys(e2).forEach(function(t2) {
    var n2 = Object.getOwnPropertyDescriptor(e2, t2);
    Object.defineProperty(r2, t2, n2.get ? n2 : { enumerable: true, get: function() {
      return e2[t2];
    } });
  }), r2;
}
var r = {}, t = { Aacute: "Á", aacute: "á", Abreve: "Ă", abreve: "ă", ac: "∾", acd: "∿", acE: "∾̳", Acirc: "Â", acirc: "â", acute: "´", Acy: "А", acy: "а", AElig: "Æ", aelig: "æ", af: "⁡", Afr: "𝔄", afr: "𝔞", Agrave: "À", agrave: "à", alefsym: "ℵ", aleph: "ℵ", Alpha: "Α", alpha: "α", Amacr: "Ā", amacr: "ā", amalg: "⨿", amp: "&", AMP: "&", andand: "⩕", And: "⩓", and: "∧", andd: "⩜", andslope: "⩘", andv: "⩚", ang: "∠", ange: "⦤", angle: "∠", angmsdaa: "⦨", angmsdab: "⦩", angmsdac: "⦪", angmsdad: "⦫", angmsdae: "⦬", angmsdaf: "⦭", angmsdag: "⦮", angmsdah: "⦯", angmsd: "∡", angrt: "∟", angrtvb: "⊾", angrtvbd: "⦝", angsph: "∢", angst: "Å", angzarr: "⍼", Aogon: "Ą", aogon: "ą", Aopf: "𝔸", aopf: "𝕒", apacir: "⩯", ap: "≈", apE: "⩰", ape: "≊", apid: "≋", apos: "'", ApplyFunction: "⁡", approx: "≈", approxeq: "≊", Aring: "Å", aring: "å", Ascr: "𝒜", ascr: "𝒶", Assign: "≔", ast: "*", asymp: "≈", asympeq: "≍", Atilde: "Ã", atilde: "ã", Auml: "Ä", auml: "ä", awconint: "∳", awint: "⨑", backcong: "≌", backepsilon: "϶", backprime: "‵", backsim: "∽", backsimeq: "⋍", Backslash: "∖", Barv: "⫧", barvee: "⊽", barwed: "⌅", Barwed: "⌆", barwedge: "⌅", bbrk: "⎵", bbrktbrk: "⎶", bcong: "≌", Bcy: "Б", bcy: "б", bdquo: "„", becaus: "∵", because: "∵", Because: "∵", bemptyv: "⦰", bepsi: "϶", bernou: "ℬ", Bernoullis: "ℬ", Beta: "Β", beta: "β", beth: "ℶ", between: "≬", Bfr: "𝔅", bfr: "𝔟", bigcap: "⋂", bigcirc: "◯", bigcup: "⋃", bigodot: "⨀", bigoplus: "⨁", bigotimes: "⨂", bigsqcup: "⨆", bigstar: "★", bigtriangledown: "▽", bigtriangleup: "△", biguplus: "⨄", bigvee: "⋁", bigwedge: "⋀", bkarow: "⤍", blacklozenge: "⧫", blacksquare: "▪", blacktriangle: "▴", blacktriangledown: "▾", blacktriangleleft: "◂", blacktriangleright: "▸", blank: "␣", blk12: "▒", blk14: "░", blk34: "▓", block: "█", bne: "=⃥", bnequiv: "≡⃥", bNot: "⫭", bnot: "⌐", Bopf: "𝔹", bopf: "𝕓", bot: "⊥", bottom: "⊥", bowtie: "⋈", boxbox: "⧉", boxdl: "┐", boxdL: "╕", boxDl: "╖", boxDL: "╗", boxdr: "┌", boxdR: "╒", boxDr: "╓", boxDR: "╔", boxh: "─", boxH: "═", boxhd: "┬", boxHd: "╤", boxhD: "╥", boxHD: "╦", boxhu: "┴", boxHu: "╧", boxhU: "╨", boxHU: "╩", boxminus: "⊟", boxplus: "⊞", boxtimes: "⊠", boxul: "┘", boxuL: "╛", boxUl: "╜", boxUL: "╝", boxur: "└", boxuR: "╘", boxUr: "╙", boxUR: "╚", boxv: "│", boxV: "║", boxvh: "┼", boxvH: "╪", boxVh: "╫", boxVH: "╬", boxvl: "┤", boxvL: "╡", boxVl: "╢", boxVL: "╣", boxvr: "├", boxvR: "╞", boxVr: "╟", boxVR: "╠", bprime: "‵", breve: "˘", Breve: "˘", brvbar: "¦", bscr: "𝒷", Bscr: "ℬ", bsemi: "⁏", bsim: "∽", bsime: "⋍", bsolb: "⧅", bsol: "\\", bsolhsub: "⟈", bull: "•", bullet: "•", bump: "≎", bumpE: "⪮", bumpe: "≏", Bumpeq: "≎", bumpeq: "≏", Cacute: "Ć", cacute: "ć", capand: "⩄", capbrcup: "⩉", capcap: "⩋", cap: "∩", Cap: "⋒", capcup: "⩇", capdot: "⩀", CapitalDifferentialD: "ⅅ", caps: "∩︀", caret: "⁁", caron: "ˇ", Cayleys: "ℭ", ccaps: "⩍", Ccaron: "Č", ccaron: "č", Ccedil: "Ç", ccedil: "ç", Ccirc: "Ĉ", ccirc: "ĉ", Cconint: "∰", ccups: "⩌", ccupssm: "⩐", Cdot: "Ċ", cdot: "ċ", cedil: "¸", Cedilla: "¸", cemptyv: "⦲", cent: "¢", centerdot: "·", CenterDot: "·", cfr: "𝔠", Cfr: "ℭ", CHcy: "Ч", chcy: "ч", check: "✓", checkmark: "✓", Chi: "Χ", chi: "χ", circ: "ˆ", circeq: "≗", circlearrowleft: "↺", circlearrowright: "↻", circledast: "⊛", circledcirc: "⊚", circleddash: "⊝", CircleDot: "⊙", circledR: "®", circledS: "Ⓢ", CircleMinus: "⊖", CirclePlus: "⊕", CircleTimes: "⊗", cir: "○", cirE: "⧃", cire: "≗", cirfnint: "⨐", cirmid: "⫯", cirscir: "⧂", ClockwiseContourIntegral: "∲", CloseCurlyDoubleQuote: "”", CloseCurlyQuote: "’", clubs: "♣", clubsuit: "♣", colon: ":", Colon: "∷", Colone: "⩴", colone: "≔", coloneq: "≔", comma: ",", commat: "@", comp: "∁", compfn: "∘", complement: "∁", complexes: "ℂ", cong: "≅", congdot: "⩭", Congruent: "≡", conint: "∮", Conint: "∯", ContourIntegral: "∮", copf: "𝕔", Copf: "ℂ", coprod: "∐", Coproduct: "∐", copy: "©", COPY: "©", copysr: "℗", CounterClockwiseContourIntegral: "∳", crarr: "↵", cross: "✗", Cross: "⨯", Cscr: "𝒞", cscr: "𝒸", csub: "⫏", csube: "⫑", csup: "⫐", csupe: "⫒", ctdot: "⋯", cudarrl: "⤸", cudarrr: "⤵", cuepr: "⋞", cuesc: "⋟", cularr: "↶", cularrp: "⤽", cupbrcap: "⩈", cupcap: "⩆", CupCap: "≍", cup: "∪", Cup: "⋓", cupcup: "⩊", cupdot: "⊍", cupor: "⩅", cups: "∪︀", curarr: "↷", curarrm: "⤼", curlyeqprec: "⋞", curlyeqsucc: "⋟", curlyvee: "⋎", curlywedge: "⋏", curren: "¤", curvearrowleft: "↶", curvearrowright: "↷", cuvee: "⋎", cuwed: "⋏", cwconint: "∲", cwint: "∱", cylcty: "⌭", dagger: "†", Dagger: "‡", daleth: "ℸ", darr: "↓", Darr: "↡", dArr: "⇓", dash: "‐", Dashv: "⫤", dashv: "⊣", dbkarow: "⤏", dblac: "˝", Dcaron: "Ď", dcaron: "ď", Dcy: "Д", dcy: "д", ddagger: "‡", ddarr: "⇊", DD: "ⅅ", dd: "ⅆ", DDotrahd: "⤑", ddotseq: "⩷", deg: "°", Del: "∇", Delta: "Δ", delta: "δ", demptyv: "⦱", dfisht: "⥿", Dfr: "𝔇", dfr: "𝔡", dHar: "⥥", dharl: "⇃", dharr: "⇂", DiacriticalAcute: "´", DiacriticalDot: "˙", DiacriticalDoubleAcute: "˝", DiacriticalGrave: "`", DiacriticalTilde: "˜", diam: "⋄", diamond: "⋄", Diamond: "⋄", diamondsuit: "♦", diams: "♦", die: "¨", DifferentialD: "ⅆ", digamma: "ϝ", disin: "⋲", div: "÷", divide: "÷", divideontimes: "⋇", divonx: "⋇", DJcy: "Ђ", djcy: "ђ", dlcorn: "⌞", dlcrop: "⌍", dollar: "$", Dopf: "𝔻", dopf: "𝕕", Dot: "¨", dot: "˙", DotDot: "⃜", doteq: "≐", doteqdot: "≑", DotEqual: "≐", dotminus: "∸", dotplus: "∔", dotsquare: "⊡", doublebarwedge: "⌆", DoubleContourIntegral: "∯", DoubleDot: "¨", DoubleDownArrow: "⇓", DoubleLeftArrow: "⇐", DoubleLeftRightArrow: "⇔", DoubleLeftTee: "⫤", DoubleLongLeftArrow: "⟸", DoubleLongLeftRightArrow: "⟺", DoubleLongRightArrow: "⟹", DoubleRightArrow: "⇒", DoubleRightTee: "⊨", DoubleUpArrow: "⇑", DoubleUpDownArrow: "⇕", DoubleVerticalBar: "∥", DownArrowBar: "⤓", downarrow: "↓", DownArrow: "↓", Downarrow: "⇓", DownArrowUpArrow: "⇵", DownBreve: "̑", downdownarrows: "⇊", downharpoonleft: "⇃", downharpoonright: "⇂", DownLeftRightVector: "⥐", DownLeftTeeVector: "⥞", DownLeftVectorBar: "⥖", DownLeftVector: "↽", DownRightTeeVector: "⥟", DownRightVectorBar: "⥗", DownRightVector: "⇁", DownTeeArrow: "↧", DownTee: "⊤", drbkarow: "⤐", drcorn: "⌟", drcrop: "⌌", Dscr: "𝒟", dscr: "𝒹", DScy: "Ѕ", dscy: "ѕ", dsol: "⧶", Dstrok: "Đ", dstrok: "đ", dtdot: "⋱", dtri: "▿", dtrif: "▾", duarr: "⇵", duhar: "⥯", dwangle: "⦦", DZcy: "Џ", dzcy: "џ", dzigrarr: "⟿", Eacute: "É", eacute: "é", easter: "⩮", Ecaron: "Ě", ecaron: "ě", Ecirc: "Ê", ecirc: "ê", ecir: "≖", ecolon: "≕", Ecy: "Э", ecy: "э", eDDot: "⩷", Edot: "Ė", edot: "ė", eDot: "≑", ee: "ⅇ", efDot: "≒", Efr: "𝔈", efr: "𝔢", eg: "⪚", Egrave: "È", egrave: "è", egs: "⪖", egsdot: "⪘", el: "⪙", Element: "∈", elinters: "⏧", ell: "ℓ", els: "⪕", elsdot: "⪗", Emacr: "Ē", emacr: "ē", empty: "∅", emptyset: "∅", EmptySmallSquare: "◻", emptyv: "∅", EmptyVerySmallSquare: "▫", emsp13: " ", emsp14: " ", emsp: " ", ENG: "Ŋ", eng: "ŋ", ensp: " ", Eogon: "Ę", eogon: "ę", Eopf: "𝔼", eopf: "𝕖", epar: "⋕", eparsl: "⧣", eplus: "⩱", epsi: "ε", Epsilon: "Ε", epsilon: "ε", epsiv: "ϵ", eqcirc: "≖", eqcolon: "≕", eqsim: "≂", eqslantgtr: "⪖", eqslantless: "⪕", Equal: "⩵", equals: "=", EqualTilde: "≂", equest: "≟", Equilibrium: "⇌", equiv: "≡", equivDD: "⩸", eqvparsl: "⧥", erarr: "⥱", erDot: "≓", escr: "ℯ", Escr: "ℰ", esdot: "≐", Esim: "⩳", esim: "≂", Eta: "Η", eta: "η", ETH: "Ð", eth: "ð", Euml: "Ë", euml: "ë", euro: "€", excl: "!", exist: "∃", Exists: "∃", expectation: "ℰ", exponentiale: "ⅇ", ExponentialE: "ⅇ", fallingdotseq: "≒", Fcy: "Ф", fcy: "ф", female: "♀", ffilig: "ﬃ", fflig: "ﬀ", ffllig: "ﬄ", Ffr: "𝔉", ffr: "𝔣", filig: "ﬁ", FilledSmallSquare: "◼", FilledVerySmallSquare: "▪", fjlig: "fj", flat: "♭", fllig: "ﬂ", fltns: "▱", fnof: "ƒ", Fopf: "𝔽", fopf: "𝕗", forall: "∀", ForAll: "∀", fork: "⋔", forkv: "⫙", Fouriertrf: "ℱ", fpartint: "⨍", frac12: "½", frac13: "⅓", frac14: "¼", frac15: "⅕", frac16: "⅙", frac18: "⅛", frac23: "⅔", frac25: "⅖", frac34: "¾", frac35: "⅗", frac38: "⅜", frac45: "⅘", frac56: "⅚", frac58: "⅝", frac78: "⅞", frasl: "⁄", frown: "⌢", fscr: "𝒻", Fscr: "ℱ", gacute: "ǵ", Gamma: "Γ", gamma: "γ", Gammad: "Ϝ", gammad: "ϝ", gap: "⪆", Gbreve: "Ğ", gbreve: "ğ", Gcedil: "Ģ", Gcirc: "Ĝ", gcirc: "ĝ", Gcy: "Г", gcy: "г", Gdot: "Ġ", gdot: "ġ", ge: "≥", gE: "≧", gEl: "⪌", gel: "⋛", geq: "≥", geqq: "≧", geqslant: "⩾", gescc: "⪩", ges: "⩾", gesdot: "⪀", gesdoto: "⪂", gesdotol: "⪄", gesl: "⋛︀", gesles: "⪔", Gfr: "𝔊", gfr: "𝔤", gg: "≫", Gg: "⋙", ggg: "⋙", gimel: "ℷ", GJcy: "Ѓ", gjcy: "ѓ", gla: "⪥", gl: "≷", glE: "⪒", glj: "⪤", gnap: "⪊", gnapprox: "⪊", gne: "⪈", gnE: "≩", gneq: "⪈", gneqq: "≩", gnsim: "⋧", Gopf: "𝔾", gopf: "𝕘", grave: "`", GreaterEqual: "≥", GreaterEqualLess: "⋛", GreaterFullEqual: "≧", GreaterGreater: "⪢", GreaterLess: "≷", GreaterSlantEqual: "⩾", GreaterTilde: "≳", Gscr: "𝒢", gscr: "ℊ", gsim: "≳", gsime: "⪎", gsiml: "⪐", gtcc: "⪧", gtcir: "⩺", gt: ">", GT: ">", Gt: "≫", gtdot: "⋗", gtlPar: "⦕", gtquest: "⩼", gtrapprox: "⪆", gtrarr: "⥸", gtrdot: "⋗", gtreqless: "⋛", gtreqqless: "⪌", gtrless: "≷", gtrsim: "≳", gvertneqq: "≩︀", gvnE: "≩︀", Hacek: "ˇ", hairsp: " ", half: "½", hamilt: "ℋ", HARDcy: "Ъ", hardcy: "ъ", harrcir: "⥈", harr: "↔", hArr: "⇔", harrw: "↭", Hat: "^", hbar: "ℏ", Hcirc: "Ĥ", hcirc: "ĥ", hearts: "♥", heartsuit: "♥", hellip: "…", hercon: "⊹", hfr: "𝔥", Hfr: "ℌ", HilbertSpace: "ℋ", hksearow: "⤥", hkswarow: "⤦", hoarr: "⇿", homtht: "∻", hookleftarrow: "↩", hookrightarrow: "↪", hopf: "𝕙", Hopf: "ℍ", horbar: "―", HorizontalLine: "─", hscr: "𝒽", Hscr: "ℋ", hslash: "ℏ", Hstrok: "Ħ", hstrok: "ħ", HumpDownHump: "≎", HumpEqual: "≏", hybull: "⁃", hyphen: "‐", Iacute: "Í", iacute: "í", ic: "⁣", Icirc: "Î", icirc: "î", Icy: "И", icy: "и", Idot: "İ", IEcy: "Е", iecy: "е", iexcl: "¡", iff: "⇔", ifr: "𝔦", Ifr: "ℑ", Igrave: "Ì", igrave: "ì", ii: "ⅈ", iiiint: "⨌", iiint: "∭", iinfin: "⧜", iiota: "℩", IJlig: "Ĳ", ijlig: "ĳ", Imacr: "Ī", imacr: "ī", image: "ℑ", ImaginaryI: "ⅈ", imagline: "ℐ", imagpart: "ℑ", imath: "ı", Im: "ℑ", imof: "⊷", imped: "Ƶ", Implies: "⇒", incare: "℅", in: "∈", infin: "∞", infintie: "⧝", inodot: "ı", intcal: "⊺", int: "∫", Int: "∬", integers: "ℤ", Integral: "∫", intercal: "⊺", Intersection: "⋂", intlarhk: "⨗", intprod: "⨼", InvisibleComma: "⁣", InvisibleTimes: "⁢", IOcy: "Ё", iocy: "ё", Iogon: "Į", iogon: "į", Iopf: "𝕀", iopf: "𝕚", Iota: "Ι", iota: "ι", iprod: "⨼", iquest: "¿", iscr: "𝒾", Iscr: "ℐ", isin: "∈", isindot: "⋵", isinE: "⋹", isins: "⋴", isinsv: "⋳", isinv: "∈", it: "⁢", Itilde: "Ĩ", itilde: "ĩ", Iukcy: "І", iukcy: "і", Iuml: "Ï", iuml: "ï", Jcirc: "Ĵ", jcirc: "ĵ", Jcy: "Й", jcy: "й", Jfr: "𝔍", jfr: "𝔧", jmath: "ȷ", Jopf: "𝕁", jopf: "𝕛", Jscr: "𝒥", jscr: "𝒿", Jsercy: "Ј", jsercy: "ј", Jukcy: "Є", jukcy: "є", Kappa: "Κ", kappa: "κ", kappav: "ϰ", Kcedil: "Ķ", kcedil: "ķ", Kcy: "К", kcy: "к", Kfr: "𝔎", kfr: "𝔨", kgreen: "ĸ", KHcy: "Х", khcy: "х", KJcy: "Ќ", kjcy: "ќ", Kopf: "𝕂", kopf: "𝕜", Kscr: "𝒦", kscr: "𝓀", lAarr: "⇚", Lacute: "Ĺ", lacute: "ĺ", laemptyv: "⦴", lagran: "ℒ", Lambda: "Λ", lambda: "λ", lang: "⟨", Lang: "⟪", langd: "⦑", langle: "⟨", lap: "⪅", Laplacetrf: "ℒ", laquo: "«", larrb: "⇤", larrbfs: "⤟", larr: "←", Larr: "↞", lArr: "⇐", larrfs: "⤝", larrhk: "↩", larrlp: "↫", larrpl: "⤹", larrsim: "⥳", larrtl: "↢", latail: "⤙", lAtail: "⤛", lat: "⪫", late: "⪭", lates: "⪭︀", lbarr: "⤌", lBarr: "⤎", lbbrk: "❲", lbrace: "{", lbrack: "[", lbrke: "⦋", lbrksld: "⦏", lbrkslu: "⦍", Lcaron: "Ľ", lcaron: "ľ", Lcedil: "Ļ", lcedil: "ļ", lceil: "⌈", lcub: "{", Lcy: "Л", lcy: "л", ldca: "⤶", ldquo: "“", ldquor: "„", ldrdhar: "⥧", ldrushar: "⥋", ldsh: "↲", le: "≤", lE: "≦", LeftAngleBracket: "⟨", LeftArrowBar: "⇤", leftarrow: "←", LeftArrow: "←", Leftarrow: "⇐", LeftArrowRightArrow: "⇆", leftarrowtail: "↢", LeftCeiling: "⌈", LeftDoubleBracket: "⟦", LeftDownTeeVector: "⥡", LeftDownVectorBar: "⥙", LeftDownVector: "⇃", LeftFloor: "⌊", leftharpoondown: "↽", leftharpoonup: "↼", leftleftarrows: "⇇", leftrightarrow: "↔", LeftRightArrow: "↔", Leftrightarrow: "⇔", leftrightarrows: "⇆", leftrightharpoons: "⇋", leftrightsquigarrow: "↭", LeftRightVector: "⥎", LeftTeeArrow: "↤", LeftTee: "⊣", LeftTeeVector: "⥚", leftthreetimes: "⋋", LeftTriangleBar: "⧏", LeftTriangle: "⊲", LeftTriangleEqual: "⊴", LeftUpDownVector: "⥑", LeftUpTeeVector: "⥠", LeftUpVectorBar: "⥘", LeftUpVector: "↿", LeftVectorBar: "⥒", LeftVector: "↼", lEg: "⪋", leg: "⋚", leq: "≤", leqq: "≦", leqslant: "⩽", lescc: "⪨", les: "⩽", lesdot: "⩿", lesdoto: "⪁", lesdotor: "⪃", lesg: "⋚︀", lesges: "⪓", lessapprox: "⪅", lessdot: "⋖", lesseqgtr: "⋚", lesseqqgtr: "⪋", LessEqualGreater: "⋚", LessFullEqual: "≦", LessGreater: "≶", lessgtr: "≶", LessLess: "⪡", lesssim: "≲", LessSlantEqual: "⩽", LessTilde: "≲", lfisht: "⥼", lfloor: "⌊", Lfr: "𝔏", lfr: "𝔩", lg: "≶", lgE: "⪑", lHar: "⥢", lhard: "↽", lharu: "↼", lharul: "⥪", lhblk: "▄", LJcy: "Љ", ljcy: "љ", llarr: "⇇", ll: "≪", Ll: "⋘", llcorner: "⌞", Lleftarrow: "⇚", llhard: "⥫", lltri: "◺", Lmidot: "Ŀ", lmidot: "ŀ", lmoustache: "⎰", lmoust: "⎰", lnap: "⪉", lnapprox: "⪉", lne: "⪇", lnE: "≨", lneq: "⪇", lneqq: "≨", lnsim: "⋦", loang: "⟬", loarr: "⇽", lobrk: "⟦", longleftarrow: "⟵", LongLeftArrow: "⟵", Longleftarrow: "⟸", longleftrightarrow: "⟷", LongLeftRightArrow: "⟷", Longleftrightarrow: "⟺", longmapsto: "⟼", longrightarrow: "⟶", LongRightArrow: "⟶", Longrightarrow: "⟹", looparrowleft: "↫", looparrowright: "↬", lopar: "⦅", Lopf: "𝕃", lopf: "𝕝", loplus: "⨭", lotimes: "⨴", lowast: "∗", lowbar: "_", LowerLeftArrow: "↙", LowerRightArrow: "↘", loz: "◊", lozenge: "◊", lozf: "⧫", lpar: "(", lparlt: "⦓", lrarr: "⇆", lrcorner: "⌟", lrhar: "⇋", lrhard: "⥭", lrm: "‎", lrtri: "⊿", lsaquo: "‹", lscr: "𝓁", Lscr: "ℒ", lsh: "↰", Lsh: "↰", lsim: "≲", lsime: "⪍", lsimg: "⪏", lsqb: "[", lsquo: "‘", lsquor: "‚", Lstrok: "Ł", lstrok: "ł", ltcc: "⪦", ltcir: "⩹", lt: "<", LT: "<", Lt: "≪", ltdot: "⋖", lthree: "⋋", ltimes: "⋉", ltlarr: "⥶", ltquest: "⩻", ltri: "◃", ltrie: "⊴", ltrif: "◂", ltrPar: "⦖", lurdshar: "⥊", luruhar: "⥦", lvertneqq: "≨︀", lvnE: "≨︀", macr: "¯", male: "♂", malt: "✠", maltese: "✠", Map: "⤅", map: "↦", mapsto: "↦", mapstodown: "↧", mapstoleft: "↤", mapstoup: "↥", marker: "▮", mcomma: "⨩", Mcy: "М", mcy: "м", mdash: "—", mDDot: "∺", measuredangle: "∡", MediumSpace: " ", Mellintrf: "ℳ", Mfr: "𝔐", mfr: "𝔪", mho: "℧", micro: "µ", midast: "*", midcir: "⫰", mid: "∣", middot: "·", minusb: "⊟", minus: "−", minusd: "∸", minusdu: "⨪", MinusPlus: "∓", mlcp: "⫛", mldr: "…", mnplus: "∓", models: "⊧", Mopf: "𝕄", mopf: "𝕞", mp: "∓", mscr: "𝓂", Mscr: "ℳ", mstpos: "∾", Mu: "Μ", mu: "μ", multimap: "⊸", mumap: "⊸", nabla: "∇", Nacute: "Ń", nacute: "ń", nang: "∠⃒", nap: "≉", napE: "⩰̸", napid: "≋̸", napos: "ŉ", napprox: "≉", natural: "♮", naturals: "ℕ", natur: "♮", nbsp: " ", nbump: "≎̸", nbumpe: "≏̸", ncap: "⩃", Ncaron: "Ň", ncaron: "ň", Ncedil: "Ņ", ncedil: "ņ", ncong: "≇", ncongdot: "⩭̸", ncup: "⩂", Ncy: "Н", ncy: "н", ndash: "–", nearhk: "⤤", nearr: "↗", neArr: "⇗", nearrow: "↗", ne: "≠", nedot: "≐̸", NegativeMediumSpace: "​", NegativeThickSpace: "​", NegativeThinSpace: "​", NegativeVeryThinSpace: "​", nequiv: "≢", nesear: "⤨", nesim: "≂̸", NestedGreaterGreater: "≫", NestedLessLess: "≪", NewLine: "\n", nexist: "∄", nexists: "∄", Nfr: "𝔑", nfr: "𝔫", ngE: "≧̸", nge: "≱", ngeq: "≱", ngeqq: "≧̸", ngeqslant: "⩾̸", nges: "⩾̸", nGg: "⋙̸", ngsim: "≵", nGt: "≫⃒", ngt: "≯", ngtr: "≯", nGtv: "≫̸", nharr: "↮", nhArr: "⇎", nhpar: "⫲", ni: "∋", nis: "⋼", nisd: "⋺", niv: "∋", NJcy: "Њ", njcy: "њ", nlarr: "↚", nlArr: "⇍", nldr: "‥", nlE: "≦̸", nle: "≰", nleftarrow: "↚", nLeftarrow: "⇍", nleftrightarrow: "↮", nLeftrightarrow: "⇎", nleq: "≰", nleqq: "≦̸", nleqslant: "⩽̸", nles: "⩽̸", nless: "≮", nLl: "⋘̸", nlsim: "≴", nLt: "≪⃒", nlt: "≮", nltri: "⋪", nltrie: "⋬", nLtv: "≪̸", nmid: "∤", NoBreak: "⁠", NonBreakingSpace: " ", nopf: "𝕟", Nopf: "ℕ", Not: "⫬", not: "¬", NotCongruent: "≢", NotCupCap: "≭", NotDoubleVerticalBar: "∦", NotElement: "∉", NotEqual: "≠", NotEqualTilde: "≂̸", NotExists: "∄", NotGreater: "≯", NotGreaterEqual: "≱", NotGreaterFullEqual: "≧̸", NotGreaterGreater: "≫̸", NotGreaterLess: "≹", NotGreaterSlantEqual: "⩾̸", NotGreaterTilde: "≵", NotHumpDownHump: "≎̸", NotHumpEqual: "≏̸", notin: "∉", notindot: "⋵̸", notinE: "⋹̸", notinva: "∉", notinvb: "⋷", notinvc: "⋶", NotLeftTriangleBar: "⧏̸", NotLeftTriangle: "⋪", NotLeftTriangleEqual: "⋬", NotLess: "≮", NotLessEqual: "≰", NotLessGreater: "≸", NotLessLess: "≪̸", NotLessSlantEqual: "⩽̸", NotLessTilde: "≴", NotNestedGreaterGreater: "⪢̸", NotNestedLessLess: "⪡̸", notni: "∌", notniva: "∌", notnivb: "⋾", notnivc: "⋽", NotPrecedes: "⊀", NotPrecedesEqual: "⪯̸", NotPrecedesSlantEqual: "⋠", NotReverseElement: "∌", NotRightTriangleBar: "⧐̸", NotRightTriangle: "⋫", NotRightTriangleEqual: "⋭", NotSquareSubset: "⊏̸", NotSquareSubsetEqual: "⋢", NotSquareSuperset: "⊐̸", NotSquareSupersetEqual: "⋣", NotSubset: "⊂⃒", NotSubsetEqual: "⊈", NotSucceeds: "⊁", NotSucceedsEqual: "⪰̸", NotSucceedsSlantEqual: "⋡", NotSucceedsTilde: "≿̸", NotSuperset: "⊃⃒", NotSupersetEqual: "⊉", NotTilde: "≁", NotTildeEqual: "≄", NotTildeFullEqual: "≇", NotTildeTilde: "≉", NotVerticalBar: "∤", nparallel: "∦", npar: "∦", nparsl: "⫽⃥", npart: "∂̸", npolint: "⨔", npr: "⊀", nprcue: "⋠", nprec: "⊀", npreceq: "⪯̸", npre: "⪯̸", nrarrc: "⤳̸", nrarr: "↛", nrArr: "⇏", nrarrw: "↝̸", nrightarrow: "↛", nRightarrow: "⇏", nrtri: "⋫", nrtrie: "⋭", nsc: "⊁", nsccue: "⋡", nsce: "⪰̸", Nscr: "𝒩", nscr: "𝓃", nshortmid: "∤", nshortparallel: "∦", nsim: "≁", nsime: "≄", nsimeq: "≄", nsmid: "∤", nspar: "∦", nsqsube: "⋢", nsqsupe: "⋣", nsub: "⊄", nsubE: "⫅̸", nsube: "⊈", nsubset: "⊂⃒", nsubseteq: "⊈", nsubseteqq: "⫅̸", nsucc: "⊁", nsucceq: "⪰̸", nsup: "⊅", nsupE: "⫆̸", nsupe: "⊉", nsupset: "⊃⃒", nsupseteq: "⊉", nsupseteqq: "⫆̸", ntgl: "≹", Ntilde: "Ñ", ntilde: "ñ", ntlg: "≸", ntriangleleft: "⋪", ntrianglelefteq: "⋬", ntriangleright: "⋫", ntrianglerighteq: "⋭", Nu: "Ν", nu: "ν", num: "#", numero: "№", numsp: " ", nvap: "≍⃒", nvdash: "⊬", nvDash: "⊭", nVdash: "⊮", nVDash: "⊯", nvge: "≥⃒", nvgt: ">⃒", nvHarr: "⤄", nvinfin: "⧞", nvlArr: "⤂", nvle: "≤⃒", nvlt: "<⃒", nvltrie: "⊴⃒", nvrArr: "⤃", nvrtrie: "⊵⃒", nvsim: "∼⃒", nwarhk: "⤣", nwarr: "↖", nwArr: "⇖", nwarrow: "↖", nwnear: "⤧", Oacute: "Ó", oacute: "ó", oast: "⊛", Ocirc: "Ô", ocirc: "ô", ocir: "⊚", Ocy: "О", ocy: "о", odash: "⊝", Odblac: "Ő", odblac: "ő", odiv: "⨸", odot: "⊙", odsold: "⦼", OElig: "Œ", oelig: "œ", ofcir: "⦿", Ofr: "𝔒", ofr: "𝔬", ogon: "˛", Ograve: "Ò", ograve: "ò", ogt: "⧁", ohbar: "⦵", ohm: "Ω", oint: "∮", olarr: "↺", olcir: "⦾", olcross: "⦻", oline: "‾", olt: "⧀", Omacr: "Ō", omacr: "ō", Omega: "Ω", omega: "ω", Omicron: "Ο", omicron: "ο", omid: "⦶", ominus: "⊖", Oopf: "𝕆", oopf: "𝕠", opar: "⦷", OpenCurlyDoubleQuote: "“", OpenCurlyQuote: "‘", operp: "⦹", oplus: "⊕", orarr: "↻", Or: "⩔", or: "∨", ord: "⩝", order: "ℴ", orderof: "ℴ", ordf: "ª", ordm: "º", origof: "⊶", oror: "⩖", orslope: "⩗", orv: "⩛", oS: "Ⓢ", Oscr: "𝒪", oscr: "ℴ", Oslash: "Ø", oslash: "ø", osol: "⊘", Otilde: "Õ", otilde: "õ", otimesas: "⨶", Otimes: "⨷", otimes: "⊗", Ouml: "Ö", ouml: "ö", ovbar: "⌽", OverBar: "‾", OverBrace: "⏞", OverBracket: "⎴", OverParenthesis: "⏜", para: "¶", parallel: "∥", par: "∥", parsim: "⫳", parsl: "⫽", part: "∂", PartialD: "∂", Pcy: "П", pcy: "п", percnt: "%", period: ".", permil: "‰", perp: "⊥", pertenk: "‱", Pfr: "𝔓", pfr: "𝔭", Phi: "Φ", phi: "φ", phiv: "ϕ", phmmat: "ℳ", phone: "☎", Pi: "Π", pi: "π", pitchfork: "⋔", piv: "ϖ", planck: "ℏ", planckh: "ℎ", plankv: "ℏ", plusacir: "⨣", plusb: "⊞", pluscir: "⨢", plus: "+", plusdo: "∔", plusdu: "⨥", pluse: "⩲", PlusMinus: "±", plusmn: "±", plussim: "⨦", plustwo: "⨧", pm: "±", Poincareplane: "ℌ", pointint: "⨕", popf: "𝕡", Popf: "ℙ", pound: "£", prap: "⪷", Pr: "⪻", pr: "≺", prcue: "≼", precapprox: "⪷", prec: "≺", preccurlyeq: "≼", Precedes: "≺", PrecedesEqual: "⪯", PrecedesSlantEqual: "≼", PrecedesTilde: "≾", preceq: "⪯", precnapprox: "⪹", precneqq: "⪵", precnsim: "⋨", pre: "⪯", prE: "⪳", precsim: "≾", prime: "′", Prime: "″", primes: "ℙ", prnap: "⪹", prnE: "⪵", prnsim: "⋨", prod: "∏", Product: "∏", profalar: "⌮", profline: "⌒", profsurf: "⌓", prop: "∝", Proportional: "∝", Proportion: "∷", propto: "∝", prsim: "≾", prurel: "⊰", Pscr: "𝒫", pscr: "𝓅", Psi: "Ψ", psi: "ψ", puncsp: " ", Qfr: "𝔔", qfr: "𝔮", qint: "⨌", qopf: "𝕢", Qopf: "ℚ", qprime: "⁗", Qscr: "𝒬", qscr: "𝓆", quaternions: "ℍ", quatint: "⨖", quest: "?", questeq: "≟", quot: '"', QUOT: '"', rAarr: "⇛", race: "∽̱", Racute: "Ŕ", racute: "ŕ", radic: "√", raemptyv: "⦳", rang: "⟩", Rang: "⟫", rangd: "⦒", range: "⦥", rangle: "⟩", raquo: "»", rarrap: "⥵", rarrb: "⇥", rarrbfs: "⤠", rarrc: "⤳", rarr: "→", Rarr: "↠", rArr: "⇒", rarrfs: "⤞", rarrhk: "↪", rarrlp: "↬", rarrpl: "⥅", rarrsim: "⥴", Rarrtl: "⤖", rarrtl: "↣", rarrw: "↝", ratail: "⤚", rAtail: "⤜", ratio: "∶", rationals: "ℚ", rbarr: "⤍", rBarr: "⤏", RBarr: "⤐", rbbrk: "❳", rbrace: "}", rbrack: "]", rbrke: "⦌", rbrksld: "⦎", rbrkslu: "⦐", Rcaron: "Ř", rcaron: "ř", Rcedil: "Ŗ", rcedil: "ŗ", rceil: "⌉", rcub: "}", Rcy: "Р", rcy: "р", rdca: "⤷", rdldhar: "⥩", rdquo: "”", rdquor: "”", rdsh: "↳", real: "ℜ", realine: "ℛ", realpart: "ℜ", reals: "ℝ", Re: "ℜ", rect: "▭", reg: "®", REG: "®", ReverseElement: "∋", ReverseEquilibrium: "⇋", ReverseUpEquilibrium: "⥯", rfisht: "⥽", rfloor: "⌋", rfr: "𝔯", Rfr: "ℜ", rHar: "⥤", rhard: "⇁", rharu: "⇀", rharul: "⥬", Rho: "Ρ", rho: "ρ", rhov: "ϱ", RightAngleBracket: "⟩", RightArrowBar: "⇥", rightarrow: "→", RightArrow: "→", Rightarrow: "⇒", RightArrowLeftArrow: "⇄", rightarrowtail: "↣", RightCeiling: "⌉", RightDoubleBracket: "⟧", RightDownTeeVector: "⥝", RightDownVectorBar: "⥕", RightDownVector: "⇂", RightFloor: "⌋", rightharpoondown: "⇁", rightharpoonup: "⇀", rightleftarrows: "⇄", rightleftharpoons: "⇌", rightrightarrows: "⇉", rightsquigarrow: "↝", RightTeeArrow: "↦", RightTee: "⊢", RightTeeVector: "⥛", rightthreetimes: "⋌", RightTriangleBar: "⧐", RightTriangle: "⊳", RightTriangleEqual: "⊵", RightUpDownVector: "⥏", RightUpTeeVector: "⥜", RightUpVectorBar: "⥔", RightUpVector: "↾", RightVectorBar: "⥓", RightVector: "⇀", ring: "˚", risingdotseq: "≓", rlarr: "⇄", rlhar: "⇌", rlm: "‏", rmoustache: "⎱", rmoust: "⎱", rnmid: "⫮", roang: "⟭", roarr: "⇾", robrk: "⟧", ropar: "⦆", ropf: "𝕣", Ropf: "ℝ", roplus: "⨮", rotimes: "⨵", RoundImplies: "⥰", rpar: ")", rpargt: "⦔", rppolint: "⨒", rrarr: "⇉", Rrightarrow: "⇛", rsaquo: "›", rscr: "𝓇", Rscr: "ℛ", rsh: "↱", Rsh: "↱", rsqb: "]", rsquo: "’", rsquor: "’", rthree: "⋌", rtimes: "⋊", rtri: "▹", rtrie: "⊵", rtrif: "▸", rtriltri: "⧎", RuleDelayed: "⧴", ruluhar: "⥨", rx: "℞", Sacute: "Ś", sacute: "ś", sbquo: "‚", scap: "⪸", Scaron: "Š", scaron: "š", Sc: "⪼", sc: "≻", sccue: "≽", sce: "⪰", scE: "⪴", Scedil: "Ş", scedil: "ş", Scirc: "Ŝ", scirc: "ŝ", scnap: "⪺", scnE: "⪶", scnsim: "⋩", scpolint: "⨓", scsim: "≿", Scy: "С", scy: "с", sdotb: "⊡", sdot: "⋅", sdote: "⩦", searhk: "⤥", searr: "↘", seArr: "⇘", searrow: "↘", sect: "§", semi: ";", seswar: "⤩", setminus: "∖", setmn: "∖", sext: "✶", Sfr: "𝔖", sfr: "𝔰", sfrown: "⌢", sharp: "♯", SHCHcy: "Щ", shchcy: "щ", SHcy: "Ш", shcy: "ш", ShortDownArrow: "↓", ShortLeftArrow: "←", shortmid: "∣", shortparallel: "∥", ShortRightArrow: "→", ShortUpArrow: "↑", shy: "­", Sigma: "Σ", sigma: "σ", sigmaf: "ς", sigmav: "ς", sim: "∼", simdot: "⩪", sime: "≃", simeq: "≃", simg: "⪞", simgE: "⪠", siml: "⪝", simlE: "⪟", simne: "≆", simplus: "⨤", simrarr: "⥲", slarr: "←", SmallCircle: "∘", smallsetminus: "∖", smashp: "⨳", smeparsl: "⧤", smid: "∣", smile: "⌣", smt: "⪪", smte: "⪬", smtes: "⪬︀", SOFTcy: "Ь", softcy: "ь", solbar: "⌿", solb: "⧄", sol: "/", Sopf: "𝕊", sopf: "𝕤", spades: "♠", spadesuit: "♠", spar: "∥", sqcap: "⊓", sqcaps: "⊓︀", sqcup: "⊔", sqcups: "⊔︀", Sqrt: "√", sqsub: "⊏", sqsube: "⊑", sqsubset: "⊏", sqsubseteq: "⊑", sqsup: "⊐", sqsupe: "⊒", sqsupset: "⊐", sqsupseteq: "⊒", square: "□", Square: "□", SquareIntersection: "⊓", SquareSubset: "⊏", SquareSubsetEqual: "⊑", SquareSuperset: "⊐", SquareSupersetEqual: "⊒", SquareUnion: "⊔", squarf: "▪", squ: "□", squf: "▪", srarr: "→", Sscr: "𝒮", sscr: "𝓈", ssetmn: "∖", ssmile: "⌣", sstarf: "⋆", Star: "⋆", star: "☆", starf: "★", straightepsilon: "ϵ", straightphi: "ϕ", strns: "¯", sub: "⊂", Sub: "⋐", subdot: "⪽", subE: "⫅", sube: "⊆", subedot: "⫃", submult: "⫁", subnE: "⫋", subne: "⊊", subplus: "⪿", subrarr: "⥹", subset: "⊂", Subset: "⋐", subseteq: "⊆", subseteqq: "⫅", SubsetEqual: "⊆", subsetneq: "⊊", subsetneqq: "⫋", subsim: "⫇", subsub: "⫕", subsup: "⫓", succapprox: "⪸", succ: "≻", succcurlyeq: "≽", Succeeds: "≻", SucceedsEqual: "⪰", SucceedsSlantEqual: "≽", SucceedsTilde: "≿", succeq: "⪰", succnapprox: "⪺", succneqq: "⪶", succnsim: "⋩", succsim: "≿", SuchThat: "∋", sum: "∑", Sum: "∑", sung: "♪", sup1: "¹", sup2: "²", sup3: "³", sup: "⊃", Sup: "⋑", supdot: "⪾", supdsub: "⫘", supE: "⫆", supe: "⊇", supedot: "⫄", Superset: "⊃", SupersetEqual: "⊇", suphsol: "⟉", suphsub: "⫗", suplarr: "⥻", supmult: "⫂", supnE: "⫌", supne: "⊋", supplus: "⫀", supset: "⊃", Supset: "⋑", supseteq: "⊇", supseteqq: "⫆", supsetneq: "⊋", supsetneqq: "⫌", supsim: "⫈", supsub: "⫔", supsup: "⫖", swarhk: "⤦", swarr: "↙", swArr: "⇙", swarrow: "↙", swnwar: "⤪", szlig: "ß", Tab: "	", target: "⌖", Tau: "Τ", tau: "τ", tbrk: "⎴", Tcaron: "Ť", tcaron: "ť", Tcedil: "Ţ", tcedil: "ţ", Tcy: "Т", tcy: "т", tdot: "⃛", telrec: "⌕", Tfr: "𝔗", tfr: "𝔱", there4: "∴", therefore: "∴", Therefore: "∴", Theta: "Θ", theta: "θ", thetasym: "ϑ", thetav: "ϑ", thickapprox: "≈", thicksim: "∼", ThickSpace: "  ", ThinSpace: " ", thinsp: " ", thkap: "≈", thksim: "∼", THORN: "Þ", thorn: "þ", tilde: "˜", Tilde: "∼", TildeEqual: "≃", TildeFullEqual: "≅", TildeTilde: "≈", timesbar: "⨱", timesb: "⊠", times: "×", timesd: "⨰", tint: "∭", toea: "⤨", topbot: "⌶", topcir: "⫱", top: "⊤", Topf: "𝕋", topf: "𝕥", topfork: "⫚", tosa: "⤩", tprime: "‴", trade: "™", TRADE: "™", triangle: "▵", triangledown: "▿", triangleleft: "◃", trianglelefteq: "⊴", triangleq: "≜", triangleright: "▹", trianglerighteq: "⊵", tridot: "◬", trie: "≜", triminus: "⨺", TripleDot: "⃛", triplus: "⨹", trisb: "⧍", tritime: "⨻", trpezium: "⏢", Tscr: "𝒯", tscr: "𝓉", TScy: "Ц", tscy: "ц", TSHcy: "Ћ", tshcy: "ћ", Tstrok: "Ŧ", tstrok: "ŧ", twixt: "≬", twoheadleftarrow: "↞", twoheadrightarrow: "↠", Uacute: "Ú", uacute: "ú", uarr: "↑", Uarr: "↟", uArr: "⇑", Uarrocir: "⥉", Ubrcy: "Ў", ubrcy: "ў", Ubreve: "Ŭ", ubreve: "ŭ", Ucirc: "Û", ucirc: "û", Ucy: "У", ucy: "у", udarr: "⇅", Udblac: "Ű", udblac: "ű", udhar: "⥮", ufisht: "⥾", Ufr: "𝔘", ufr: "𝔲", Ugrave: "Ù", ugrave: "ù", uHar: "⥣", uharl: "↿", uharr: "↾", uhblk: "▀", ulcorn: "⌜", ulcorner: "⌜", ulcrop: "⌏", ultri: "◸", Umacr: "Ū", umacr: "ū", uml: "¨", UnderBar: "_", UnderBrace: "⏟", UnderBracket: "⎵", UnderParenthesis: "⏝", Union: "⋃", UnionPlus: "⊎", Uogon: "Ų", uogon: "ų", Uopf: "𝕌", uopf: "𝕦", UpArrowBar: "⤒", uparrow: "↑", UpArrow: "↑", Uparrow: "⇑", UpArrowDownArrow: "⇅", updownarrow: "↕", UpDownArrow: "↕", Updownarrow: "⇕", UpEquilibrium: "⥮", upharpoonleft: "↿", upharpoonright: "↾", uplus: "⊎", UpperLeftArrow: "↖", UpperRightArrow: "↗", upsi: "υ", Upsi: "ϒ", upsih: "ϒ", Upsilon: "Υ", upsilon: "υ", UpTeeArrow: "↥", UpTee: "⊥", upuparrows: "⇈", urcorn: "⌝", urcorner: "⌝", urcrop: "⌎", Uring: "Ů", uring: "ů", urtri: "◹", Uscr: "𝒰", uscr: "𝓊", utdot: "⋰", Utilde: "Ũ", utilde: "ũ", utri: "▵", utrif: "▴", uuarr: "⇈", Uuml: "Ü", uuml: "ü", uwangle: "⦧", vangrt: "⦜", varepsilon: "ϵ", varkappa: "ϰ", varnothing: "∅", varphi: "ϕ", varpi: "ϖ", varpropto: "∝", varr: "↕", vArr: "⇕", varrho: "ϱ", varsigma: "ς", varsubsetneq: "⊊︀", varsubsetneqq: "⫋︀", varsupsetneq: "⊋︀", varsupsetneqq: "⫌︀", vartheta: "ϑ", vartriangleleft: "⊲", vartriangleright: "⊳", vBar: "⫨", Vbar: "⫫", vBarv: "⫩", Vcy: "В", vcy: "в", vdash: "⊢", vDash: "⊨", Vdash: "⊩", VDash: "⊫", Vdashl: "⫦", veebar: "⊻", vee: "∨", Vee: "⋁", veeeq: "≚", vellip: "⋮", verbar: "|", Verbar: "‖", vert: "|", Vert: "‖", VerticalBar: "∣", VerticalLine: "|", VerticalSeparator: "❘", VerticalTilde: "≀", VeryThinSpace: " ", Vfr: "𝔙", vfr: "𝔳", vltri: "⊲", vnsub: "⊂⃒", vnsup: "⊃⃒", Vopf: "𝕍", vopf: "𝕧", vprop: "∝", vrtri: "⊳", Vscr: "𝒱", vscr: "𝓋", vsubnE: "⫋︀", vsubne: "⊊︀", vsupnE: "⫌︀", vsupne: "⊋︀", Vvdash: "⊪", vzigzag: "⦚", Wcirc: "Ŵ", wcirc: "ŵ", wedbar: "⩟", wedge: "∧", Wedge: "⋀", wedgeq: "≙", weierp: "℘", Wfr: "𝔚", wfr: "𝔴", Wopf: "𝕎", wopf: "𝕨", wp: "℘", wr: "≀", wreath: "≀", Wscr: "𝒲", wscr: "𝓌", xcap: "⋂", xcirc: "◯", xcup: "⋃", xdtri: "▽", Xfr: "𝔛", xfr: "𝔵", xharr: "⟷", xhArr: "⟺", Xi: "Ξ", xi: "ξ", xlarr: "⟵", xlArr: "⟸", xmap: "⟼", xnis: "⋻", xodot: "⨀", Xopf: "𝕏", xopf: "𝕩", xoplus: "⨁", xotime: "⨂", xrarr: "⟶", xrArr: "⟹", Xscr: "𝒳", xscr: "𝓍", xsqcup: "⨆", xuplus: "⨄", xutri: "△", xvee: "⋁", xwedge: "⋀", Yacute: "Ý", yacute: "ý", YAcy: "Я", yacy: "я", Ycirc: "Ŷ", ycirc: "ŷ", Ycy: "Ы", ycy: "ы", yen: "¥", Yfr: "𝔜", yfr: "𝔶", YIcy: "Ї", yicy: "ї", Yopf: "𝕐", yopf: "𝕪", Yscr: "𝒴", yscr: "𝓎", YUcy: "Ю", yucy: "ю", yuml: "ÿ", Yuml: "Ÿ", Zacute: "Ź", zacute: "ź", Zcaron: "Ž", zcaron: "ž", Zcy: "З", zcy: "з", Zdot: "Ż", zdot: "ż", zeetrf: "ℨ", ZeroWidthSpace: "​", Zeta: "Ζ", zeta: "ζ", zfr: "𝔷", Zfr: "ℨ", ZHcy: "Ж", zhcy: "ж", zigrarr: "⇝", zopf: "𝕫", Zopf: "ℤ", Zscr: "𝒵", zscr: "𝓏", zwj: "‍", zwnj: "‌" }, n = /[!-#%-\*,-\/:;\?@\[-\]_\{\}\xA1\xA7\xAB\xB6\xB7\xBB\xBF\u037E\u0387\u055A-\u055F\u0589\u058A\u05BE\u05C0\u05C3\u05C6\u05F3\u05F4\u0609\u060A\u060C\u060D\u061B\u061E\u061F\u066A-\u066D\u06D4\u0700-\u070D\u07F7-\u07F9\u0830-\u083E\u085E\u0964\u0965\u0970\u09FD\u0A76\u0AF0\u0C84\u0DF4\u0E4F\u0E5A\u0E5B\u0F04-\u0F12\u0F14\u0F3A-\u0F3D\u0F85\u0FD0-\u0FD4\u0FD9\u0FDA\u104A-\u104F\u10FB\u1360-\u1368\u1400\u166D\u166E\u169B\u169C\u16EB-\u16ED\u1735\u1736\u17D4-\u17D6\u17D8-\u17DA\u1800-\u180A\u1944\u1945\u1A1E\u1A1F\u1AA0-\u1AA6\u1AA8-\u1AAD\u1B5A-\u1B60\u1BFC-\u1BFF\u1C3B-\u1C3F\u1C7E\u1C7F\u1CC0-\u1CC7\u1CD3\u2010-\u2027\u2030-\u2043\u2045-\u2051\u2053-\u205E\u207D\u207E\u208D\u208E\u2308-\u230B\u2329\u232A\u2768-\u2775\u27C5\u27C6\u27E6-\u27EF\u2983-\u2998\u29D8-\u29DB\u29FC\u29FD\u2CF9-\u2CFC\u2CFE\u2CFF\u2D70\u2E00-\u2E2E\u2E30-\u2E4E\u3001-\u3003\u3008-\u3011\u3014-\u301F\u3030\u303D\u30A0\u30FB\uA4FE\uA4FF\uA60D-\uA60F\uA673\uA67E\uA6F2-\uA6F7\uA874-\uA877\uA8CE\uA8CF\uA8F8-\uA8FA\uA8FC\uA92E\uA92F\uA95F\uA9C1-\uA9CD\uA9DE\uA9DF\uAA5C-\uAA5F\uAADE\uAADF\uAAF0\uAAF1\uABEB\uFD3E\uFD3F\uFE10-\uFE19\uFE30-\uFE52\uFE54-\uFE61\uFE63\uFE68\uFE6A\uFE6B\uFF01-\uFF03\uFF05-\uFF0A\uFF0C-\uFF0F\uFF1A\uFF1B\uFF1F\uFF20\uFF3B-\uFF3D\uFF3F\uFF5B\uFF5D\uFF5F-\uFF65]|\uD800[\uDD00-\uDD02\uDF9F\uDFD0]|\uD801\uDD6F|\uD802[\uDC57\uDD1F\uDD3F\uDE50-\uDE58\uDE7F\uDEF0-\uDEF6\uDF39-\uDF3F\uDF99-\uDF9C]|\uD803[\uDF55-\uDF59]|\uD804[\uDC47-\uDC4D\uDCBB\uDCBC\uDCBE-\uDCC1\uDD40-\uDD43\uDD74\uDD75\uDDC5-\uDDC8\uDDCD\uDDDB\uDDDD-\uDDDF\uDE38-\uDE3D\uDEA9]|\uD805[\uDC4B-\uDC4F\uDC5B\uDC5D\uDCC6\uDDC1-\uDDD7\uDE41-\uDE43\uDE60-\uDE6C\uDF3C-\uDF3E]|\uD806[\uDC3B\uDE3F-\uDE46\uDE9A-\uDE9C\uDE9E-\uDEA2]|\uD807[\uDC41-\uDC45\uDC70\uDC71\uDEF7\uDEF8]|\uD809[\uDC70-\uDC74]|\uD81A[\uDE6E\uDE6F\uDEF5\uDF37-\uDF3B\uDF44]|\uD81B[\uDE97-\uDE9A]|\uD82F\uDC9F|\uD836[\uDE87-\uDE8B]|\uD83A[\uDD5E\uDD5F]/, s = {}, o = {};
function i(e2, r2, t2) {
  var n2, s2, a2, c2, l2, u2 = "";
  for ("string" != typeof r2 && (t2 = r2, r2 = i.defaultChars), void 0 === t2 && (t2 = true), l2 = function(e3) {
    var r3, t3, n3 = o[e3];
    if (n3)
      return n3;
    for (n3 = o[e3] = [], r3 = 0; r3 < 128; r3++)
      t3 = String.fromCharCode(r3), /^[0-9a-z]$/i.test(t3) ? n3.push(t3) : n3.push("%" + ("0" + r3.toString(16).toUpperCase()).slice(-2));
    for (r3 = 0; r3 < e3.length; r3++)
      n3[e3.charCodeAt(r3)] = e3[r3];
    return n3;
  }(r2), n2 = 0, s2 = e2.length; n2 < s2; n2++)
    if (a2 = e2.charCodeAt(n2), t2 && 37 === a2 && n2 + 2 < s2 && /^[0-9a-f]{2}$/i.test(e2.slice(n2 + 1, n2 + 3)))
      u2 += e2.slice(n2, n2 + 3), n2 += 2;
    else if (a2 < 128)
      u2 += l2[a2];
    else if (a2 >= 55296 && a2 <= 57343) {
      if (a2 >= 55296 && a2 <= 56319 && n2 + 1 < s2 && (c2 = e2.charCodeAt(n2 + 1)) >= 56320 && c2 <= 57343) {
        u2 += encodeURIComponent(e2[n2] + e2[n2 + 1]), n2++;
        continue;
      }
      u2 += "%EF%BF%BD";
    } else
      u2 += encodeURIComponent(e2[n2]);
  return u2;
}
i.defaultChars = ";/?:@&=+$,-_.!~*'()#", i.componentChars = "-_.!~*'()";
var a = i, c = {};
function l(e2, r2) {
  var t2;
  return "string" != typeof r2 && (r2 = l.defaultChars), t2 = function(e3) {
    var r3, t3, n2 = c[e3];
    if (n2)
      return n2;
    for (n2 = c[e3] = [], r3 = 0; r3 < 128; r3++)
      t3 = String.fromCharCode(r3), n2.push(t3);
    for (r3 = 0; r3 < e3.length; r3++)
      n2[t3 = e3.charCodeAt(r3)] = "%" + ("0" + t3.toString(16).toUpperCase()).slice(-2);
    return n2;
  }(r2), e2.replace(/(%[a-f0-9]{2})+/gi, function(e3) {
    var r3, n2, s2, o2, i2, a2, c2, l2 = "";
    for (r3 = 0, n2 = e3.length; r3 < n2; r3 += 3)
      (s2 = parseInt(e3.slice(r3 + 1, r3 + 3), 16)) < 128 ? l2 += t2[s2] : 192 == (224 & s2) && r3 + 3 < n2 && 128 == (192 & (o2 = parseInt(e3.slice(r3 + 4, r3 + 6), 16))) ? (l2 += (c2 = s2 << 6 & 1984 | 63 & o2) < 128 ? "��" : String.fromCharCode(c2), r3 += 3) : 224 == (240 & s2) && r3 + 6 < n2 && (o2 = parseInt(e3.slice(r3 + 4, r3 + 6), 16), i2 = parseInt(e3.slice(r3 + 7, r3 + 9), 16), 128 == (192 & o2) && 128 == (192 & i2)) ? (l2 += (c2 = s2 << 12 & 61440 | o2 << 6 & 4032 | 63 & i2) < 2048 || c2 >= 55296 && c2 <= 57343 ? "���" : String.fromCharCode(c2), r3 += 6) : 240 == (248 & s2) && r3 + 9 < n2 && (o2 = parseInt(e3.slice(r3 + 4, r3 + 6), 16), i2 = parseInt(e3.slice(r3 + 7, r3 + 9), 16), a2 = parseInt(e3.slice(r3 + 10, r3 + 12), 16), 128 == (192 & o2) && 128 == (192 & i2) && 128 == (192 & a2)) ? ((c2 = s2 << 18 & 1835008 | o2 << 12 & 258048 | i2 << 6 & 4032 | 63 & a2) < 65536 || c2 > 1114111 ? l2 += "����" : (c2 -= 65536, l2 += String.fromCharCode(55296 + (c2 >> 10), 56320 + (1023 & c2))), r3 += 9) : l2 += "�";
    return l2;
  });
}
l.defaultChars = ";/?:@&=+$,#", l.componentChars = "";
var u = l;
function p() {
  this.protocol = null, this.slashes = null, this.auth = null, this.port = null, this.hostname = null, this.hash = null, this.search = null, this.pathname = null;
}
var h = /^([a-z0-9.+-]+:)/i, f = /:[0-9]*$/, d = /^(\/\/?(?!\/)[^\?\s]*)(\?[^\s]*)?$/, m = ["{", "}", "|", "\\", "^", "`"].concat(["<", ">", '"', "`", " ", "\r", "\n", "	"]), g = ["'"].concat(m), _ = ["%", "/", "?", ";", "#"].concat(g), k = ["/", "?", "#"], b = /^[+a-z0-9A-Z_-]{0,63}$/, v = /^([+a-z0-9A-Z_-]{0,63})(.*)$/, C = { javascript: true, "javascript:": true }, y = { http: true, https: true, ftp: true, gopher: true, file: true, "http:": true, "https:": true, "ftp:": true, "gopher:": true, "file:": true };
p.prototype.parse = function(e2, r2) {
  var t2, n2, s2, o2, i2, a2 = e2;
  if (a2 = a2.trim(), !r2 && 1 === e2.split("#").length) {
    var c2 = d.exec(a2);
    if (c2)
      return this.pathname = c2[1], c2[2] && (this.search = c2[2]), this;
  }
  var l2 = h.exec(a2);
  if (l2 && (s2 = (l2 = l2[0]).toLowerCase(), this.protocol = l2, a2 = a2.substr(l2.length)), (r2 || l2 || a2.match(/^\/\/[^@\/]+@[^@\/]+/)) && (!(i2 = "//" === a2.substr(0, 2)) || l2 && C[l2] || (a2 = a2.substr(2), this.slashes = true)), !C[l2] && (i2 || l2 && !y[l2])) {
    var u2, p2, f2 = -1;
    for (t2 = 0; t2 < k.length; t2++)
      -1 !== (o2 = a2.indexOf(k[t2])) && (-1 === f2 || o2 < f2) && (f2 = o2);
    for (-1 !== (p2 = -1 === f2 ? a2.lastIndexOf("@") : a2.lastIndexOf("@", f2)) && (u2 = a2.slice(0, p2), a2 = a2.slice(p2 + 1), this.auth = u2), f2 = -1, t2 = 0; t2 < _.length; t2++)
      -1 !== (o2 = a2.indexOf(_[t2])) && (-1 === f2 || o2 < f2) && (f2 = o2);
    -1 === f2 && (f2 = a2.length), ":" === a2[f2 - 1] && f2--;
    var m2 = a2.slice(0, f2);
    a2 = a2.slice(f2), this.parseHost(m2), this.hostname = this.hostname || "";
    var g2 = "[" === this.hostname[0] && "]" === this.hostname[this.hostname.length - 1];
    if (!g2) {
      var A2 = this.hostname.split(/\./);
      for (t2 = 0, n2 = A2.length; t2 < n2; t2++) {
        var x2 = A2[t2];
        if (x2 && !x2.match(b)) {
          for (var D2 = "", w2 = 0, E2 = x2.length; w2 < E2; w2++)
            x2.charCodeAt(w2) > 127 ? D2 += "x" : D2 += x2[w2];
          if (!D2.match(b)) {
            var q2 = A2.slice(0, t2), S2 = A2.slice(t2 + 1), F2 = x2.match(v);
            F2 && (q2.push(F2[1]), S2.unshift(F2[2])), S2.length && (a2 = S2.join(".") + a2), this.hostname = q2.join(".");
            break;
          }
        }
      }
    }
    this.hostname.length > 255 && (this.hostname = ""), g2 && (this.hostname = this.hostname.substr(1, this.hostname.length - 2));
  }
  var L2 = a2.indexOf("#");
  -1 !== L2 && (this.hash = a2.substr(L2), a2 = a2.slice(0, L2));
  var z2 = a2.indexOf("?");
  return -1 !== z2 && (this.search = a2.substr(z2), a2 = a2.slice(0, z2)), a2 && (this.pathname = a2), y[s2] && this.hostname && !this.pathname && (this.pathname = ""), this;
}, p.prototype.parseHost = function(e2) {
  var r2 = f.exec(e2);
  r2 && (":" !== (r2 = r2[0]) && (this.port = r2.substr(1)), e2 = e2.substr(0, e2.length - r2.length)), e2 && (this.hostname = e2);
};
var A = function(e2, r2) {
  if (e2 && e2 instanceof p)
    return e2;
  var t2 = new p();
  return t2.parse(e2, r2), t2;
};
s.encode = a, s.decode = u, s.format = function(e2) {
  var r2 = "";
  return r2 += e2.protocol || "", r2 += e2.slashes ? "//" : "", r2 += e2.auth ? e2.auth + "@" : "", e2.hostname && -1 !== e2.hostname.indexOf(":") ? r2 += "[" + e2.hostname + "]" : r2 += e2.hostname || "", r2 += e2.port ? ":" + e2.port : "", r2 += e2.pathname || "", r2 += e2.search || "", r2 += e2.hash || "";
}, s.parse = A;
var x = {}, D = /[\0-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]/, w = /[\0-\x1F\x7F-\x9F]/, E = /[ \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000]/;
x.Any = D, x.Cc = w, x.Cf = /[\xAD\u0600-\u0605\u061C\u06DD\u070F\u08E2\u180E\u200B-\u200F\u202A-\u202E\u2060-\u2064\u2066-\u206F\uFEFF\uFFF9-\uFFFB]|\uD804[\uDCBD\uDCCD]|\uD82F[\uDCA0-\uDCA3]|\uD834[\uDD73-\uDD7A]|\uDB40[\uDC01\uDC20-\uDC7F]/, x.P = n, x.Z = E, function(e2) {
  var r2 = Object.prototype.hasOwnProperty;
  function o2(e3, t2) {
    return r2.call(e3, t2);
  }
  function i2(e3) {
    return !(e3 >= 55296 && e3 <= 57343) && (!(e3 >= 64976 && e3 <= 65007) && (65535 != (65535 & e3) && 65534 != (65535 & e3) && (!(e3 >= 0 && e3 <= 8) && (11 !== e3 && (!(e3 >= 14 && e3 <= 31) && (!(e3 >= 127 && e3 <= 159) && !(e3 > 1114111)))))));
  }
  function a2(e3) {
    if (e3 > 65535) {
      var r3 = 55296 + ((e3 -= 65536) >> 10), t2 = 56320 + (1023 & e3);
      return String.fromCharCode(r3, t2);
    }
    return String.fromCharCode(e3);
  }
  var c2 = /\\([!"#$%&'()*+,\-.\/:;<=>?@[\\\]^_`{|}~])/g, l2 = new RegExp(c2.source + "|" + /&([a-z#][a-z0-9]{1,31});/gi.source, "gi"), u2 = /^#((?:x[a-f0-9]{1,8}|[0-9]{1,8}))/i, p2 = t;
  var h2 = /[&<>"]/, f2 = /[&<>"]/g, d2 = { "&": "&amp;", "<": "&lt;", ">": "&gt;", '"': "&quot;" };
  function m2(e3) {
    return d2[e3];
  }
  var g2 = /[.?*+^$[\]\\(){}|-]/g;
  var _2 = n;
  e2.lib = {}, e2.lib.mdurl = s, e2.lib.ucmicro = x, e2.assign = function(e3) {
    var r3 = Array.prototype.slice.call(arguments, 1);
    return r3.forEach(function(r4) {
      if (r4) {
        if ("object" != typeof r4)
          throw new TypeError(r4 + "must be object");
        Object.keys(r4).forEach(function(t2) {
          e3[t2] = r4[t2];
        });
      }
    }), e3;
  }, e2.isString = function(e3) {
    return "[object String]" === function(e4) {
      return Object.prototype.toString.call(e4);
    }(e3);
  }, e2.has = o2, e2.unescapeMd = function(e3) {
    return e3.indexOf("\\") < 0 ? e3 : e3.replace(c2, "$1");
  }, e2.unescapeAll = function(e3) {
    return e3.indexOf("\\") < 0 && e3.indexOf("&") < 0 ? e3 : e3.replace(l2, function(e4, r3, t2) {
      return r3 || function(e5, r4) {
        var t3 = 0;
        return o2(p2, r4) ? p2[r4] : 35 === r4.charCodeAt(0) && u2.test(r4) && i2(t3 = "x" === r4[1].toLowerCase() ? parseInt(r4.slice(2), 16) : parseInt(r4.slice(1), 10)) ? a2(t3) : e5;
      }(e4, t2);
    });
  }, e2.isValidEntityCode = i2, e2.fromCodePoint = a2, e2.escapeHtml = function(e3) {
    return h2.test(e3) ? e3.replace(f2, m2) : e3;
  }, e2.arrayReplaceAt = function(e3, r3, t2) {
    return [].concat(e3.slice(0, r3), t2, e3.slice(r3 + 1));
  }, e2.isSpace = function(e3) {
    switch (e3) {
      case 9:
      case 32:
        return true;
    }
    return false;
  }, e2.isWhiteSpace = function(e3) {
    if (e3 >= 8192 && e3 <= 8202)
      return true;
    switch (e3) {
      case 9:
      case 10:
      case 11:
      case 12:
      case 13:
      case 32:
      case 160:
      case 5760:
      case 8239:
      case 8287:
      case 12288:
        return true;
    }
    return false;
  }, e2.isMdAsciiPunct = function(e3) {
    switch (e3) {
      case 33:
      case 34:
      case 35:
      case 36:
      case 37:
      case 38:
      case 39:
      case 40:
      case 41:
      case 42:
      case 43:
      case 44:
      case 45:
      case 46:
      case 47:
      case 58:
      case 59:
      case 60:
      case 61:
      case 62:
      case 63:
      case 64:
      case 91:
      case 92:
      case 93:
      case 94:
      case 95:
      case 96:
      case 123:
      case 124:
      case 125:
      case 126:
        return true;
      default:
        return false;
    }
  }, e2.isPunctChar = function(e3) {
    return _2.test(e3);
  }, e2.escapeRE = function(e3) {
    return e3.replace(g2, "\\$&");
  }, e2.normalizeReference = function(e3) {
    return e3 = e3.trim().replace(/\s+/g, " "), "Ṿ" === "ẞ".toLowerCase() && (e3 = e3.replace(/ẞ/g, "ß")), e3.toLowerCase().toUpperCase();
  };
}(r);
var q = {}, S = r.unescapeAll, F = r.unescapeAll;
q.parseLinkLabel = function(e2, r2, t2) {
  var n2, s2, o2, i2, a2 = -1, c2 = e2.posMax, l2 = e2.pos;
  for (e2.pos = r2 + 1, n2 = 1; e2.pos < c2; ) {
    if (93 === (o2 = e2.src.charCodeAt(e2.pos)) && 0 === --n2) {
      s2 = true;
      break;
    }
    if (i2 = e2.pos, e2.md.inline.skipToken(e2), 91 === o2) {
      if (i2 === e2.pos - 1)
        n2++;
      else if (t2)
        return e2.pos = l2, -1;
    }
  }
  return s2 && (a2 = e2.pos), e2.pos = l2, a2;
}, q.parseLinkDestination = function(e2, r2, t2) {
  var n2, s2, o2 = r2, i2 = { ok: false, pos: 0, lines: 0, str: "" };
  if (60 === e2.charCodeAt(r2)) {
    for (r2++; r2 < t2; ) {
      if (10 === (n2 = e2.charCodeAt(r2)))
        return i2;
      if (60 === n2)
        return i2;
      if (62 === n2)
        return i2.pos = r2 + 1, i2.str = S(e2.slice(o2 + 1, r2)), i2.ok = true, i2;
      92 === n2 && r2 + 1 < t2 ? r2 += 2 : r2++;
    }
    return i2;
  }
  for (s2 = 0; r2 < t2 && 32 !== (n2 = e2.charCodeAt(r2)) && !(n2 < 32 || 127 === n2); )
    if (92 === n2 && r2 + 1 < t2) {
      if (32 === e2.charCodeAt(r2 + 1))
        break;
      r2 += 2;
    } else {
      if (40 === n2 && ++s2 > 32)
        return i2;
      if (41 === n2) {
        if (0 === s2)
          break;
        s2--;
      }
      r2++;
    }
  return o2 === r2 || 0 !== s2 || (i2.str = S(e2.slice(o2, r2)), i2.lines = 0, i2.pos = r2, i2.ok = true), i2;
}, q.parseLinkTitle = function(e2, r2, t2) {
  var n2, s2, o2 = 0, i2 = r2, a2 = { ok: false, pos: 0, lines: 0, str: "" };
  if (r2 >= t2)
    return a2;
  if (34 !== (s2 = e2.charCodeAt(r2)) && 39 !== s2 && 40 !== s2)
    return a2;
  for (r2++, 40 === s2 && (s2 = 41); r2 < t2; ) {
    if ((n2 = e2.charCodeAt(r2)) === s2)
      return a2.pos = r2 + 1, a2.lines = o2, a2.str = F(e2.slice(i2 + 1, r2)), a2.ok = true, a2;
    if (40 === n2 && 41 === s2)
      return a2;
    10 === n2 ? o2++ : 92 === n2 && r2 + 1 < t2 && (r2++, 10 === e2.charCodeAt(r2) && o2++), r2++;
  }
  return a2;
};
var L = r.assign, z = r.unescapeAll, T = r.escapeHtml, I = {};
function M() {
  this.rules = L({}, I);
}
I.code_inline = function(e2, r2, t2, n2, s2) {
  var o2 = e2[r2];
  return "<code" + s2.renderAttrs(o2) + ">" + T(e2[r2].content) + "</code>";
}, I.code_block = function(e2, r2, t2, n2, s2) {
  var o2 = e2[r2];
  return "<pre" + s2.renderAttrs(o2) + "><code>" + T(e2[r2].content) + "</code></pre>\n";
}, I.fence = function(e2, r2, t2, n2, s2) {
  var o2, i2, a2, c2, l2, u2 = e2[r2], p2 = u2.info ? z(u2.info).trim() : "", h2 = "", f2 = "";
  return p2 && (h2 = (a2 = p2.split(/(\s+)/g))[0], f2 = a2.slice(2).join("")), 0 === (o2 = t2.highlight && t2.highlight(u2.content, h2, f2) || T(u2.content)).indexOf("<pre") ? o2 + "\n" : p2 ? (i2 = u2.attrIndex("class"), c2 = u2.attrs ? u2.attrs.slice() : [], i2 < 0 ? c2.push(["class", t2.langPrefix + h2]) : (c2[i2] = c2[i2].slice(), c2[i2][1] += " " + t2.langPrefix + h2), l2 = { attrs: c2 }, "<pre><code" + s2.renderAttrs(l2) + ">" + o2 + "</code></pre>\n") : "<pre><code" + s2.renderAttrs(u2) + ">" + o2 + "</code></pre>\n";
}, I.image = function(e2, r2, t2, n2, s2) {
  var o2 = e2[r2];
  return o2.attrs[o2.attrIndex("alt")][1] = s2.renderInlineAsText(o2.children, t2, n2), s2.renderToken(e2, r2, t2);
}, I.hardbreak = function(e2, r2, t2) {
  return t2.xhtmlOut ? "<br />\n" : "<br>\n";
}, I.softbreak = function(e2, r2, t2) {
  return t2.breaks ? t2.xhtmlOut ? "<br />\n" : "<br>\n" : "\n";
}, I.text = function(e2, r2) {
  return T(e2[r2].content);
}, I.html_block = function(e2, r2) {
  return e2[r2].content;
}, I.html_inline = function(e2, r2) {
  return e2[r2].content;
}, M.prototype.renderAttrs = function(e2) {
  var r2, t2, n2;
  if (!e2.attrs)
    return "";
  for (n2 = "", r2 = 0, t2 = e2.attrs.length; r2 < t2; r2++)
    n2 += " " + T(e2.attrs[r2][0]) + '="' + T(e2.attrs[r2][1]) + '"';
  return n2;
}, M.prototype.renderToken = function(e2, r2, t2) {
  var n2, s2 = "", o2 = false, i2 = e2[r2];
  return i2.hidden ? "" : (i2.block && -1 !== i2.nesting && r2 && e2[r2 - 1].hidden && (s2 += "\n"), s2 += (-1 === i2.nesting ? "</" : "<") + i2.tag, s2 += this.renderAttrs(i2), 0 === i2.nesting && t2.xhtmlOut && (s2 += " /"), i2.block && (o2 = true, 1 === i2.nesting && r2 + 1 < e2.length && ("inline" === (n2 = e2[r2 + 1]).type || n2.hidden || -1 === n2.nesting && n2.tag === i2.tag) && (o2 = false)), s2 += o2 ? ">\n" : ">");
}, M.prototype.renderInline = function(e2, r2, t2) {
  for (var n2, s2 = "", o2 = this.rules, i2 = 0, a2 = e2.length; i2 < a2; i2++)
    void 0 !== o2[n2 = e2[i2].type] ? s2 += o2[n2](e2, i2, r2, t2, this) : s2 += this.renderToken(e2, i2, r2);
  return s2;
}, M.prototype.renderInlineAsText = function(e2, r2, t2) {
  for (var n2 = "", s2 = 0, o2 = e2.length; s2 < o2; s2++)
    "text" === e2[s2].type ? n2 += e2[s2].content : "image" === e2[s2].type ? n2 += this.renderInlineAsText(e2[s2].children, r2, t2) : "softbreak" === e2[s2].type && (n2 += "\n");
  return n2;
}, M.prototype.render = function(e2, r2, t2) {
  var n2, s2, o2, i2 = "", a2 = this.rules;
  for (n2 = 0, s2 = e2.length; n2 < s2; n2++)
    "inline" === (o2 = e2[n2].type) ? i2 += this.renderInline(e2[n2].children, r2, t2) : void 0 !== a2[o2] ? i2 += a2[e2[n2].type](e2, n2, r2, t2, this) : i2 += this.renderToken(e2, n2, r2, t2);
  return i2;
};
var R = M;
function B() {
  this.__rules__ = [], this.__cache__ = null;
}
B.prototype.__find__ = function(e2) {
  for (var r2 = 0; r2 < this.__rules__.length; r2++)
    if (this.__rules__[r2].name === e2)
      return r2;
  return -1;
}, B.prototype.__compile__ = function() {
  var e2 = this, r2 = [""];
  e2.__rules__.forEach(function(e3) {
    e3.enabled && e3.alt.forEach(function(e4) {
      r2.indexOf(e4) < 0 && r2.push(e4);
    });
  }), e2.__cache__ = {}, r2.forEach(function(r3) {
    e2.__cache__[r3] = [], e2.__rules__.forEach(function(t2) {
      t2.enabled && (r3 && t2.alt.indexOf(r3) < 0 || e2.__cache__[r3].push(t2.fn));
    });
  });
}, B.prototype.at = function(e2, r2, t2) {
  var n2 = this.__find__(e2), s2 = t2 || {};
  if (-1 === n2)
    throw new Error("Parser rule not found: " + e2);
  this.__rules__[n2].fn = r2, this.__rules__[n2].alt = s2.alt || [], this.__cache__ = null;
}, B.prototype.before = function(e2, r2, t2, n2) {
  var s2 = this.__find__(e2), o2 = n2 || {};
  if (-1 === s2)
    throw new Error("Parser rule not found: " + e2);
  this.__rules__.splice(s2, 0, { name: r2, enabled: true, fn: t2, alt: o2.alt || [] }), this.__cache__ = null;
}, B.prototype.after = function(e2, r2, t2, n2) {
  var s2 = this.__find__(e2), o2 = n2 || {};
  if (-1 === s2)
    throw new Error("Parser rule not found: " + e2);
  this.__rules__.splice(s2 + 1, 0, { name: r2, enabled: true, fn: t2, alt: o2.alt || [] }), this.__cache__ = null;
}, B.prototype.push = function(e2, r2, t2) {
  var n2 = t2 || {};
  this.__rules__.push({ name: e2, enabled: true, fn: r2, alt: n2.alt || [] }), this.__cache__ = null;
}, B.prototype.enable = function(e2, r2) {
  Array.isArray(e2) || (e2 = [e2]);
  var t2 = [];
  return e2.forEach(function(e3) {
    var n2 = this.__find__(e3);
    if (n2 < 0) {
      if (r2)
        return;
      throw new Error("Rules manager: invalid rule name " + e3);
    }
    this.__rules__[n2].enabled = true, t2.push(e3);
  }, this), this.__cache__ = null, t2;
}, B.prototype.enableOnly = function(e2, r2) {
  Array.isArray(e2) || (e2 = [e2]), this.__rules__.forEach(function(e3) {
    e3.enabled = false;
  }), this.enable(e2, r2);
}, B.prototype.disable = function(e2, r2) {
  Array.isArray(e2) || (e2 = [e2]);
  var t2 = [];
  return e2.forEach(function(e3) {
    var n2 = this.__find__(e3);
    if (n2 < 0) {
      if (r2)
        return;
      throw new Error("Rules manager: invalid rule name " + e3);
    }
    this.__rules__[n2].enabled = false, t2.push(e3);
  }, this), this.__cache__ = null, t2;
}, B.prototype.getRules = function(e2) {
  return null === this.__cache__ && this.__compile__(), this.__cache__[e2] || [];
};
var N = B, O = /\r\n?|\n/g, P = /\0/g, j = r.arrayReplaceAt;
function U(e2) {
  return /^<\/a\s*>/i.test(e2);
}
var V = /\+-|\.\.|\?\?\?\?|!!!!|,,|--/, Z = /\((c|tm|r)\)/i, $ = /\((c|tm|r)\)/gi, G = { c: "©", r: "®", tm: "™" };
function H(e2, r2) {
  return G[r2.toLowerCase()];
}
function J(e2) {
  var r2, t2, n2 = 0;
  for (r2 = e2.length - 1; r2 >= 0; r2--)
    "text" !== (t2 = e2[r2]).type || n2 || (t2.content = t2.content.replace($, H)), "link_open" === t2.type && "auto" === t2.info && n2--, "link_close" === t2.type && "auto" === t2.info && n2++;
}
function W(e2) {
  var r2, t2, n2 = 0;
  for (r2 = e2.length - 1; r2 >= 0; r2--)
    "text" !== (t2 = e2[r2]).type || n2 || V.test(t2.content) && (t2.content = t2.content.replace(/\+-/g, "±").replace(/\.{2,}/g, "…").replace(/([?!])…/g, "$1..").replace(/([?!]){4,}/g, "$1$1$1").replace(/,{2,}/g, ",").replace(/(^|[^-])---(?=[^-]|$)/gm, "$1—").replace(/(^|\s)--(?=\s|$)/gm, "$1–").replace(/(^|[^-\s])--(?=[^-\s]|$)/gm, "$1–")), "link_open" === t2.type && "auto" === t2.info && n2--, "link_close" === t2.type && "auto" === t2.info && n2++;
}
var Y = r.isWhiteSpace, K = r.isPunctChar, Q = r.isMdAsciiPunct, X = /['"]/, ee = /['"]/g;
function re(e2, r2, t2) {
  return e2.slice(0, r2) + t2 + e2.slice(r2 + 1);
}
function te(e2, r2) {
  var t2, n2, s2, o2, i2, a2, c2, l2, u2, p2, h2, f2, d2, m2, g2, _2, k2, b2, v2, C2, y2;
  for (v2 = [], t2 = 0; t2 < e2.length; t2++) {
    for (n2 = e2[t2], c2 = e2[t2].level, k2 = v2.length - 1; k2 >= 0 && !(v2[k2].level <= c2); k2--)
      ;
    if (v2.length = k2 + 1, "text" === n2.type) {
      i2 = 0, a2 = (s2 = n2.content).length;
      e:
        for (; i2 < a2 && (ee.lastIndex = i2, o2 = ee.exec(s2)); ) {
          if (g2 = _2 = true, i2 = o2.index + 1, b2 = "'" === o2[0], u2 = 32, o2.index - 1 >= 0)
            u2 = s2.charCodeAt(o2.index - 1);
          else
            for (k2 = t2 - 1; k2 >= 0 && ("softbreak" !== e2[k2].type && "hardbreak" !== e2[k2].type); k2--)
              if (e2[k2].content) {
                u2 = e2[k2].content.charCodeAt(e2[k2].content.length - 1);
                break;
              }
          if (p2 = 32, i2 < a2)
            p2 = s2.charCodeAt(i2);
          else
            for (k2 = t2 + 1; k2 < e2.length && ("softbreak" !== e2[k2].type && "hardbreak" !== e2[k2].type); k2++)
              if (e2[k2].content) {
                p2 = e2[k2].content.charCodeAt(0);
                break;
              }
          if (h2 = Q(u2) || K(String.fromCharCode(u2)), f2 = Q(p2) || K(String.fromCharCode(p2)), d2 = Y(u2), (m2 = Y(p2)) ? g2 = false : f2 && (d2 || h2 || (g2 = false)), d2 ? _2 = false : h2 && (m2 || f2 || (_2 = false)), 34 === p2 && '"' === o2[0] && u2 >= 48 && u2 <= 57 && (_2 = g2 = false), g2 && _2 && (g2 = h2, _2 = f2), g2 || _2) {
            if (_2) {
              for (k2 = v2.length - 1; k2 >= 0 && (l2 = v2[k2], !(v2[k2].level < c2)); k2--)
                if (l2.single === b2 && v2[k2].level === c2) {
                  l2 = v2[k2], b2 ? (C2 = r2.md.options.quotes[2], y2 = r2.md.options.quotes[3]) : (C2 = r2.md.options.quotes[0], y2 = r2.md.options.quotes[1]), n2.content = re(n2.content, o2.index, y2), e2[l2.token].content = re(e2[l2.token].content, l2.pos, C2), i2 += y2.length - 1, l2.token === t2 && (i2 += C2.length - 1), a2 = (s2 = n2.content).length, v2.length = k2;
                  continue e;
                }
            }
            g2 ? v2.push({ token: t2, pos: o2.index, single: b2, level: c2 }) : _2 && b2 && (n2.content = re(n2.content, o2.index, "’"));
          } else
            b2 && (n2.content = re(n2.content, o2.index, "’"));
        }
    }
  }
}
function ne(e2, r2, t2) {
  this.type = e2, this.tag = r2, this.attrs = null, this.map = null, this.nesting = t2, this.level = 0, this.children = null, this.content = "", this.markup = "", this.info = "", this.meta = null, this.block = false, this.hidden = false;
}
ne.prototype.attrIndex = function(e2) {
  var r2, t2, n2;
  if (!this.attrs)
    return -1;
  for (t2 = 0, n2 = (r2 = this.attrs).length; t2 < n2; t2++)
    if (r2[t2][0] === e2)
      return t2;
  return -1;
}, ne.prototype.attrPush = function(e2) {
  this.attrs ? this.attrs.push(e2) : this.attrs = [e2];
}, ne.prototype.attrSet = function(e2, r2) {
  var t2 = this.attrIndex(e2), n2 = [e2, r2];
  t2 < 0 ? this.attrPush(n2) : this.attrs[t2] = n2;
}, ne.prototype.attrGet = function(e2) {
  var r2 = this.attrIndex(e2), t2 = null;
  return r2 >= 0 && (t2 = this.attrs[r2][1]), t2;
}, ne.prototype.attrJoin = function(e2, r2) {
  var t2 = this.attrIndex(e2);
  t2 < 0 ? this.attrPush([e2, r2]) : this.attrs[t2][1] = this.attrs[t2][1] + " " + r2;
};
var se = ne, oe = se;
function ie(e2, r2, t2) {
  this.src = e2, this.env = t2, this.tokens = [], this.inlineMode = false, this.md = r2;
}
ie.prototype.Token = oe;
var ae = ie, ce = N, le = [["normalize", function(e2) {
  var r2;
  r2 = (r2 = e2.src.replace(O, "\n")).replace(P, "�"), e2.src = r2;
}], ["block", function(e2) {
  var r2;
  e2.inlineMode ? ((r2 = new e2.Token("inline", "", 0)).content = e2.src, r2.map = [0, 1], r2.children = [], e2.tokens.push(r2)) : e2.md.block.parse(e2.src, e2.md, e2.env, e2.tokens);
}], ["inline", function(e2) {
  var r2, t2, n2, s2 = e2.tokens;
  for (t2 = 0, n2 = s2.length; t2 < n2; t2++)
    "inline" === (r2 = s2[t2]).type && e2.md.inline.parse(r2.content, e2.md, e2.env, r2.children);
}], ["linkify", function(e2) {
  var r2, t2, n2, s2, o2, i2, a2, c2, l2, u2, p2, h2, f2, d2, m2, g2, _2, k2, b2 = e2.tokens;
  if (e2.md.options.linkify) {
    for (t2 = 0, n2 = b2.length; t2 < n2; t2++)
      if ("inline" === b2[t2].type && e2.md.linkify.pretest(b2[t2].content))
        for (f2 = 0, r2 = (s2 = b2[t2].children).length - 1; r2 >= 0; r2--)
          if ("link_close" !== (i2 = s2[r2]).type) {
            if ("html_inline" === i2.type && (k2 = i2.content, /^<a[>\s]/i.test(k2) && f2 > 0 && f2--, U(i2.content) && f2++), !(f2 > 0) && "text" === i2.type && e2.md.linkify.test(i2.content)) {
              for (l2 = i2.content, _2 = e2.md.linkify.match(l2), a2 = [], h2 = i2.level, p2 = 0, _2.length > 0 && 0 === _2[0].index && r2 > 0 && "text_special" === s2[r2 - 1].type && (_2 = _2.slice(1)), c2 = 0; c2 < _2.length; c2++)
                d2 = _2[c2].url, m2 = e2.md.normalizeLink(d2), e2.md.validateLink(m2) && (g2 = _2[c2].text, g2 = _2[c2].schema ? "mailto:" !== _2[c2].schema || /^mailto:/i.test(g2) ? e2.md.normalizeLinkText(g2) : e2.md.normalizeLinkText("mailto:" + g2).replace(/^mailto:/, "") : e2.md.normalizeLinkText("http://" + g2).replace(/^http:\/\//, ""), (u2 = _2[c2].index) > p2 && ((o2 = new e2.Token("text", "", 0)).content = l2.slice(p2, u2), o2.level = h2, a2.push(o2)), (o2 = new e2.Token("link_open", "a", 1)).attrs = [["href", m2]], o2.level = h2++, o2.markup = "linkify", o2.info = "auto", a2.push(o2), (o2 = new e2.Token("text", "", 0)).content = g2, o2.level = h2, a2.push(o2), (o2 = new e2.Token("link_close", "a", -1)).level = --h2, o2.markup = "linkify", o2.info = "auto", a2.push(o2), p2 = _2[c2].lastIndex);
              p2 < l2.length && ((o2 = new e2.Token("text", "", 0)).content = l2.slice(p2), o2.level = h2, a2.push(o2)), b2[t2].children = s2 = j(s2, r2, a2);
            }
          } else
            for (r2--; s2[r2].level !== i2.level && "link_open" !== s2[r2].type; )
              r2--;
  }
}], ["replacements", function(e2) {
  var r2;
  if (e2.md.options.typographer)
    for (r2 = e2.tokens.length - 1; r2 >= 0; r2--)
      "inline" === e2.tokens[r2].type && (Z.test(e2.tokens[r2].content) && J(e2.tokens[r2].children), V.test(e2.tokens[r2].content) && W(e2.tokens[r2].children));
}], ["smartquotes", function(e2) {
  var r2;
  if (e2.md.options.typographer)
    for (r2 = e2.tokens.length - 1; r2 >= 0; r2--)
      "inline" === e2.tokens[r2].type && X.test(e2.tokens[r2].content) && te(e2.tokens[r2].children, e2);
}], ["text_join", function(e2) {
  var r2, t2, n2, s2, o2, i2, a2 = e2.tokens;
  for (r2 = 0, t2 = a2.length; r2 < t2; r2++)
    if ("inline" === a2[r2].type) {
      for (o2 = (n2 = a2[r2].children).length, s2 = 0; s2 < o2; s2++)
        "text_special" === n2[s2].type && (n2[s2].type = "text");
      for (s2 = i2 = 0; s2 < o2; s2++)
        "text" === n2[s2].type && s2 + 1 < o2 && "text" === n2[s2 + 1].type ? n2[s2 + 1].content = n2[s2].content + n2[s2 + 1].content : (s2 !== i2 && (n2[i2] = n2[s2]), i2++);
      s2 !== i2 && (n2.length = i2);
    }
}]];
function ue() {
  this.ruler = new ce();
  for (var e2 = 0; e2 < le.length; e2++)
    this.ruler.push(le[e2][0], le[e2][1]);
}
ue.prototype.process = function(e2) {
  var r2, t2, n2;
  for (r2 = 0, t2 = (n2 = this.ruler.getRules("")).length; r2 < t2; r2++)
    n2[r2](e2);
}, ue.prototype.State = ae;
var pe = ue, he = r.isSpace;
function fe(e2, r2) {
  var t2 = e2.bMarks[r2] + e2.tShift[r2], n2 = e2.eMarks[r2];
  return e2.src.slice(t2, n2);
}
function de(e2) {
  var r2, t2 = [], n2 = 0, s2 = e2.length, o2 = false, i2 = 0, a2 = "";
  for (r2 = e2.charCodeAt(n2); n2 < s2; )
    124 === r2 && (o2 ? (a2 += e2.substring(i2, n2 - 1), i2 = n2) : (t2.push(a2 + e2.substring(i2, n2)), a2 = "", i2 = n2 + 1)), o2 = 92 === r2, n2++, r2 = e2.charCodeAt(n2);
  return t2.push(a2 + e2.substring(i2)), t2;
}
var me = r.isSpace, ge = r.isSpace, _e = r.isSpace;
function ke(e2, r2) {
  var t2, n2, s2, o2;
  return n2 = e2.bMarks[r2] + e2.tShift[r2], s2 = e2.eMarks[r2], 42 !== (t2 = e2.src.charCodeAt(n2++)) && 45 !== t2 && 43 !== t2 || n2 < s2 && (o2 = e2.src.charCodeAt(n2), !_e(o2)) ? -1 : n2;
}
function be(e2, r2) {
  var t2, n2 = e2.bMarks[r2] + e2.tShift[r2], s2 = n2, o2 = e2.eMarks[r2];
  if (s2 + 1 >= o2)
    return -1;
  if ((t2 = e2.src.charCodeAt(s2++)) < 48 || t2 > 57)
    return -1;
  for (; ; ) {
    if (s2 >= o2)
      return -1;
    if (!((t2 = e2.src.charCodeAt(s2++)) >= 48 && t2 <= 57)) {
      if (41 === t2 || 46 === t2)
        break;
      return -1;
    }
    if (s2 - n2 >= 10)
      return -1;
  }
  return s2 < o2 && (t2 = e2.src.charCodeAt(s2), !_e(t2)) ? -1 : s2;
}
var ve = r.normalizeReference, Ce = r.isSpace, ye = {}, Ae = `<[A-Za-z][A-Za-z0-9\\-]*(?:\\s+[a-zA-Z_:][a-zA-Z0-9:._-]*(?:\\s*=\\s*(?:[^"'=<>\`\\x00-\\x20]+|'[^']*'|"[^"]*"))?)*\\s*\\/?>`, xe = "<\\/[A-Za-z][A-Za-z0-9\\-]*\\s*>", De = new RegExp("^(?:" + Ae + "|" + xe + "|<!---->|<!--(?:-?[^>-])(?:-?[^-])*-->|<[?][\\s\\S]*?[?]>|<![A-Z]+\\s+[^>]*>|<!\\[CDATA\\[[\\s\\S]*?\\]\\]>)"), we = new RegExp("^(?:" + Ae + "|" + xe + ")");
ye.HTML_TAG_RE = De, ye.HTML_OPEN_CLOSE_TAG_RE = we;
var Ee = ["address", "article", "aside", "base", "basefont", "blockquote", "body", "caption", "center", "col", "colgroup", "dd", "details", "dialog", "dir", "div", "dl", "dt", "fieldset", "figcaption", "figure", "footer", "form", "frame", "frameset", "h1", "h2", "h3", "h4", "h5", "h6", "head", "header", "hr", "html", "iframe", "legend", "li", "link", "main", "menu", "menuitem", "nav", "noframes", "ol", "optgroup", "option", "p", "param", "section", "source", "summary", "table", "tbody", "td", "tfoot", "th", "thead", "title", "tr", "track", "ul"], qe = ye.HTML_OPEN_CLOSE_TAG_RE, Se = [[/^<(script|pre|style|textarea)(?=(\s|>|$))/i, /<\/(script|pre|style|textarea)>/i, true], [/^<!--/, /-->/, true], [/^<\?/, /\?>/, true], [/^<![A-Z]/, />/, true], [/^<!\[CDATA\[/, /\]\]>/, true], [new RegExp("^</?(" + Ee.join("|") + ")(?=(\\s|/?>|$))", "i"), /^$/, true], [new RegExp(qe.source + "\\s*$"), /^$/, false]], Fe = r.isSpace, Le = se, ze = r.isSpace;
function Te(e2, r2, t2, n2) {
  var s2, o2, i2, a2, c2, l2, u2, p2;
  for (this.src = e2, this.md = r2, this.env = t2, this.tokens = n2, this.bMarks = [], this.eMarks = [], this.tShift = [], this.sCount = [], this.bsCount = [], this.blkIndent = 0, this.line = 0, this.lineMax = 0, this.tight = false, this.ddIndent = -1, this.listIndent = -1, this.parentType = "root", this.level = 0, this.result = "", p2 = false, i2 = a2 = l2 = u2 = 0, c2 = (o2 = this.src).length; a2 < c2; a2++) {
    if (s2 = o2.charCodeAt(a2), !p2) {
      if (ze(s2)) {
        l2++, 9 === s2 ? u2 += 4 - u2 % 4 : u2++;
        continue;
      }
      p2 = true;
    }
    10 !== s2 && a2 !== c2 - 1 || (10 !== s2 && a2++, this.bMarks.push(i2), this.eMarks.push(a2), this.tShift.push(l2), this.sCount.push(u2), this.bsCount.push(0), p2 = false, l2 = 0, u2 = 0, i2 = a2 + 1);
  }
  this.bMarks.push(o2.length), this.eMarks.push(o2.length), this.tShift.push(0), this.sCount.push(0), this.bsCount.push(0), this.lineMax = this.bMarks.length - 1;
}
Te.prototype.push = function(e2, r2, t2) {
  var n2 = new Le(e2, r2, t2);
  return n2.block = true, t2 < 0 && this.level--, n2.level = this.level, t2 > 0 && this.level++, this.tokens.push(n2), n2;
}, Te.prototype.isEmpty = function(e2) {
  return this.bMarks[e2] + this.tShift[e2] >= this.eMarks[e2];
}, Te.prototype.skipEmptyLines = function(e2) {
  for (var r2 = this.lineMax; e2 < r2 && !(this.bMarks[e2] + this.tShift[e2] < this.eMarks[e2]); e2++)
    ;
  return e2;
}, Te.prototype.skipSpaces = function(e2) {
  for (var r2, t2 = this.src.length; e2 < t2 && (r2 = this.src.charCodeAt(e2), ze(r2)); e2++)
    ;
  return e2;
}, Te.prototype.skipSpacesBack = function(e2, r2) {
  if (e2 <= r2)
    return e2;
  for (; e2 > r2; )
    if (!ze(this.src.charCodeAt(--e2)))
      return e2 + 1;
  return e2;
}, Te.prototype.skipChars = function(e2, r2) {
  for (var t2 = this.src.length; e2 < t2 && this.src.charCodeAt(e2) === r2; e2++)
    ;
  return e2;
}, Te.prototype.skipCharsBack = function(e2, r2, t2) {
  if (e2 <= t2)
    return e2;
  for (; e2 > t2; )
    if (r2 !== this.src.charCodeAt(--e2))
      return e2 + 1;
  return e2;
}, Te.prototype.getLines = function(e2, r2, t2, n2) {
  var s2, o2, i2, a2, c2, l2, u2, p2 = e2;
  if (e2 >= r2)
    return "";
  for (l2 = new Array(r2 - e2), s2 = 0; p2 < r2; p2++, s2++) {
    for (o2 = 0, u2 = a2 = this.bMarks[p2], c2 = p2 + 1 < r2 || n2 ? this.eMarks[p2] + 1 : this.eMarks[p2]; a2 < c2 && o2 < t2; ) {
      if (i2 = this.src.charCodeAt(a2), ze(i2))
        9 === i2 ? o2 += 4 - (o2 + this.bsCount[p2]) % 4 : o2++;
      else {
        if (!(a2 - u2 < this.tShift[p2]))
          break;
        o2++;
      }
      a2++;
    }
    l2[s2] = o2 > t2 ? new Array(o2 - t2 + 1).join(" ") + this.src.slice(a2, c2) : this.src.slice(a2, c2);
  }
  return l2.join("");
}, Te.prototype.Token = Le;
var Ie = Te, Me = N, Re = [["table", function(e2, r2, t2, n2) {
  var s2, o2, i2, a2, c2, l2, u2, p2, h2, f2, d2, m2, g2, _2, k2, b2, v2, C2;
  if (r2 + 2 > t2)
    return false;
  if (l2 = r2 + 1, e2.sCount[l2] < e2.blkIndent)
    return false;
  if (e2.sCount[l2] - e2.blkIndent >= 4)
    return false;
  if ((i2 = e2.bMarks[l2] + e2.tShift[l2]) >= e2.eMarks[l2])
    return false;
  if (124 !== (v2 = e2.src.charCodeAt(i2++)) && 45 !== v2 && 58 !== v2)
    return false;
  if (i2 >= e2.eMarks[l2])
    return false;
  if (124 !== (C2 = e2.src.charCodeAt(i2++)) && 45 !== C2 && 58 !== C2 && !he(C2))
    return false;
  if (45 === v2 && he(C2))
    return false;
  for (; i2 < e2.eMarks[l2]; ) {
    if (124 !== (s2 = e2.src.charCodeAt(i2)) && 45 !== s2 && 58 !== s2 && !he(s2))
      return false;
    i2++;
  }
  for (u2 = (o2 = fe(e2, r2 + 1)).split("|"), f2 = [], a2 = 0; a2 < u2.length; a2++) {
    if (!(d2 = u2[a2].trim())) {
      if (0 === a2 || a2 === u2.length - 1)
        continue;
      return false;
    }
    if (!/^:?-+:?$/.test(d2))
      return false;
    58 === d2.charCodeAt(d2.length - 1) ? f2.push(58 === d2.charCodeAt(0) ? "center" : "right") : 58 === d2.charCodeAt(0) ? f2.push("left") : f2.push("");
  }
  if (-1 === (o2 = fe(e2, r2).trim()).indexOf("|"))
    return false;
  if (e2.sCount[r2] - e2.blkIndent >= 4)
    return false;
  if ((u2 = de(o2)).length && "" === u2[0] && u2.shift(), u2.length && "" === u2[u2.length - 1] && u2.pop(), 0 === (p2 = u2.length) || p2 !== f2.length)
    return false;
  if (n2)
    return true;
  for (_2 = e2.parentType, e2.parentType = "table", b2 = e2.md.block.ruler.getRules("blockquote"), (h2 = e2.push("table_open", "table", 1)).map = m2 = [r2, 0], (h2 = e2.push("thead_open", "thead", 1)).map = [r2, r2 + 1], (h2 = e2.push("tr_open", "tr", 1)).map = [r2, r2 + 1], a2 = 0; a2 < u2.length; a2++)
    h2 = e2.push("th_open", "th", 1), f2[a2] && (h2.attrs = [["style", "text-align:" + f2[a2]]]), (h2 = e2.push("inline", "", 0)).content = u2[a2].trim(), h2.children = [], h2 = e2.push("th_close", "th", -1);
  for (h2 = e2.push("tr_close", "tr", -1), h2 = e2.push("thead_close", "thead", -1), l2 = r2 + 2; l2 < t2 && !(e2.sCount[l2] < e2.blkIndent); l2++) {
    for (k2 = false, a2 = 0, c2 = b2.length; a2 < c2; a2++)
      if (b2[a2](e2, l2, t2, true)) {
        k2 = true;
        break;
      }
    if (k2)
      break;
    if (!(o2 = fe(e2, l2).trim()))
      break;
    if (e2.sCount[l2] - e2.blkIndent >= 4)
      break;
    for ((u2 = de(o2)).length && "" === u2[0] && u2.shift(), u2.length && "" === u2[u2.length - 1] && u2.pop(), l2 === r2 + 2 && ((h2 = e2.push("tbody_open", "tbody", 1)).map = g2 = [r2 + 2, 0]), (h2 = e2.push("tr_open", "tr", 1)).map = [l2, l2 + 1], a2 = 0; a2 < p2; a2++)
      h2 = e2.push("td_open", "td", 1), f2[a2] && (h2.attrs = [["style", "text-align:" + f2[a2]]]), (h2 = e2.push("inline", "", 0)).content = u2[a2] ? u2[a2].trim() : "", h2.children = [], h2 = e2.push("td_close", "td", -1);
    h2 = e2.push("tr_close", "tr", -1);
  }
  return g2 && (h2 = e2.push("tbody_close", "tbody", -1), g2[1] = l2), h2 = e2.push("table_close", "table", -1), m2[1] = l2, e2.parentType = _2, e2.line = l2, true;
}, ["paragraph", "reference"]], ["code", function(e2, r2, t2) {
  var n2, s2, o2;
  if (e2.sCount[r2] - e2.blkIndent < 4)
    return false;
  for (s2 = n2 = r2 + 1; n2 < t2; )
    if (e2.isEmpty(n2))
      n2++;
    else {
      if (!(e2.sCount[n2] - e2.blkIndent >= 4))
        break;
      s2 = ++n2;
    }
  return e2.line = s2, (o2 = e2.push("code_block", "code", 0)).content = e2.getLines(r2, s2, 4 + e2.blkIndent, false) + "\n", o2.map = [r2, e2.line], true;
}], ["fence", function(e2, r2, t2, n2) {
  var s2, o2, i2, a2, c2, l2, u2, p2 = false, h2 = e2.bMarks[r2] + e2.tShift[r2], f2 = e2.eMarks[r2];
  if (e2.sCount[r2] - e2.blkIndent >= 4)
    return false;
  if (h2 + 3 > f2)
    return false;
  if (126 !== (s2 = e2.src.charCodeAt(h2)) && 96 !== s2)
    return false;
  if (c2 = h2, (o2 = (h2 = e2.skipChars(h2, s2)) - c2) < 3)
    return false;
  if (u2 = e2.src.slice(c2, h2), i2 = e2.src.slice(h2, f2), 96 === s2 && i2.indexOf(String.fromCharCode(s2)) >= 0)
    return false;
  if (n2)
    return true;
  for (a2 = r2; !(++a2 >= t2) && !((h2 = c2 = e2.bMarks[a2] + e2.tShift[a2]) < (f2 = e2.eMarks[a2]) && e2.sCount[a2] < e2.blkIndent); )
    if (e2.src.charCodeAt(h2) === s2 && !(e2.sCount[a2] - e2.blkIndent >= 4 || (h2 = e2.skipChars(h2, s2)) - c2 < o2 || (h2 = e2.skipSpaces(h2)) < f2)) {
      p2 = true;
      break;
    }
  return o2 = e2.sCount[r2], e2.line = a2 + (p2 ? 1 : 0), (l2 = e2.push("fence", "code", 0)).info = i2, l2.content = e2.getLines(r2 + 1, a2, o2, true), l2.markup = u2, l2.map = [r2, e2.line], true;
}, ["paragraph", "reference", "blockquote", "list"]], ["blockquote", function(e2, r2, t2, n2) {
  var s2, o2, i2, a2, c2, l2, u2, p2, h2, f2, d2, m2, g2, _2, k2, b2, v2, C2, y2, A2, x2 = e2.lineMax, D2 = e2.bMarks[r2] + e2.tShift[r2], w2 = e2.eMarks[r2];
  if (e2.sCount[r2] - e2.blkIndent >= 4)
    return false;
  if (62 !== e2.src.charCodeAt(D2++))
    return false;
  if (n2)
    return true;
  for (a2 = h2 = e2.sCount[r2] + 1, 32 === e2.src.charCodeAt(D2) ? (D2++, a2++, h2++, s2 = false, b2 = true) : 9 === e2.src.charCodeAt(D2) ? (b2 = true, (e2.bsCount[r2] + h2) % 4 == 3 ? (D2++, a2++, h2++, s2 = false) : s2 = true) : b2 = false, f2 = [e2.bMarks[r2]], e2.bMarks[r2] = D2; D2 < w2 && (o2 = e2.src.charCodeAt(D2), me(o2)); )
    9 === o2 ? h2 += 4 - (h2 + e2.bsCount[r2] + (s2 ? 1 : 0)) % 4 : h2++, D2++;
  for (d2 = [e2.bsCount[r2]], e2.bsCount[r2] = e2.sCount[r2] + 1 + (b2 ? 1 : 0), l2 = D2 >= w2, _2 = [e2.sCount[r2]], e2.sCount[r2] = h2 - a2, k2 = [e2.tShift[r2]], e2.tShift[r2] = D2 - e2.bMarks[r2], C2 = e2.md.block.ruler.getRules("blockquote"), g2 = e2.parentType, e2.parentType = "blockquote", p2 = r2 + 1; p2 < t2 && (A2 = e2.sCount[p2] < e2.blkIndent, !((D2 = e2.bMarks[p2] + e2.tShift[p2]) >= (w2 = e2.eMarks[p2]))); p2++)
    if (62 !== e2.src.charCodeAt(D2++) || A2) {
      if (l2)
        break;
      for (v2 = false, i2 = 0, c2 = C2.length; i2 < c2; i2++)
        if (C2[i2](e2, p2, t2, true)) {
          v2 = true;
          break;
        }
      if (v2) {
        e2.lineMax = p2, 0 !== e2.blkIndent && (f2.push(e2.bMarks[p2]), d2.push(e2.bsCount[p2]), k2.push(e2.tShift[p2]), _2.push(e2.sCount[p2]), e2.sCount[p2] -= e2.blkIndent);
        break;
      }
      f2.push(e2.bMarks[p2]), d2.push(e2.bsCount[p2]), k2.push(e2.tShift[p2]), _2.push(e2.sCount[p2]), e2.sCount[p2] = -1;
    } else {
      for (a2 = h2 = e2.sCount[p2] + 1, 32 === e2.src.charCodeAt(D2) ? (D2++, a2++, h2++, s2 = false, b2 = true) : 9 === e2.src.charCodeAt(D2) ? (b2 = true, (e2.bsCount[p2] + h2) % 4 == 3 ? (D2++, a2++, h2++, s2 = false) : s2 = true) : b2 = false, f2.push(e2.bMarks[p2]), e2.bMarks[p2] = D2; D2 < w2 && (o2 = e2.src.charCodeAt(D2), me(o2)); )
        9 === o2 ? h2 += 4 - (h2 + e2.bsCount[p2] + (s2 ? 1 : 0)) % 4 : h2++, D2++;
      l2 = D2 >= w2, d2.push(e2.bsCount[p2]), e2.bsCount[p2] = e2.sCount[p2] + 1 + (b2 ? 1 : 0), _2.push(e2.sCount[p2]), e2.sCount[p2] = h2 - a2, k2.push(e2.tShift[p2]), e2.tShift[p2] = D2 - e2.bMarks[p2];
    }
  for (m2 = e2.blkIndent, e2.blkIndent = 0, (y2 = e2.push("blockquote_open", "blockquote", 1)).markup = ">", y2.map = u2 = [r2, 0], e2.md.block.tokenize(e2, r2, p2), (y2 = e2.push("blockquote_close", "blockquote", -1)).markup = ">", e2.lineMax = x2, e2.parentType = g2, u2[1] = e2.line, i2 = 0; i2 < k2.length; i2++)
    e2.bMarks[i2 + r2] = f2[i2], e2.tShift[i2 + r2] = k2[i2], e2.sCount[i2 + r2] = _2[i2], e2.bsCount[i2 + r2] = d2[i2];
  return e2.blkIndent = m2, true;
}, ["paragraph", "reference", "blockquote", "list"]], ["hr", function(e2, r2, t2, n2) {
  var s2, o2, i2, a2, c2 = e2.bMarks[r2] + e2.tShift[r2], l2 = e2.eMarks[r2];
  if (e2.sCount[r2] - e2.blkIndent >= 4)
    return false;
  if (42 !== (s2 = e2.src.charCodeAt(c2++)) && 45 !== s2 && 95 !== s2)
    return false;
  for (o2 = 1; c2 < l2; ) {
    if ((i2 = e2.src.charCodeAt(c2++)) !== s2 && !ge(i2))
      return false;
    i2 === s2 && o2++;
  }
  return !(o2 < 3) && (n2 || (e2.line = r2 + 1, (a2 = e2.push("hr", "hr", 0)).map = [r2, e2.line], a2.markup = Array(o2 + 1).join(String.fromCharCode(s2))), true);
}, ["paragraph", "reference", "blockquote", "list"]], ["list", function(e2, r2, t2, n2) {
  var s2, o2, i2, a2, c2, l2, u2, p2, h2, f2, d2, m2, g2, _2, k2, b2, v2, C2, y2, A2, x2, D2, w2, E2, q2, S2, F2, L2, z2 = false, T2 = true;
  if (e2.sCount[r2] - e2.blkIndent >= 4)
    return false;
  if (e2.listIndent >= 0 && e2.sCount[r2] - e2.listIndent >= 4 && e2.sCount[r2] < e2.blkIndent)
    return false;
  if (n2 && "paragraph" === e2.parentType && e2.sCount[r2] >= e2.blkIndent && (z2 = true), (w2 = be(e2, r2)) >= 0) {
    if (u2 = true, q2 = e2.bMarks[r2] + e2.tShift[r2], g2 = Number(e2.src.slice(q2, w2 - 1)), z2 && 1 !== g2)
      return false;
  } else {
    if (!((w2 = ke(e2, r2)) >= 0))
      return false;
    u2 = false;
  }
  if (z2 && e2.skipSpaces(w2) >= e2.eMarks[r2])
    return false;
  if (m2 = e2.src.charCodeAt(w2 - 1), n2)
    return true;
  for (d2 = e2.tokens.length, u2 ? (L2 = e2.push("ordered_list_open", "ol", 1), 1 !== g2 && (L2.attrs = [["start", g2]])) : L2 = e2.push("bullet_list_open", "ul", 1), L2.map = f2 = [r2, 0], L2.markup = String.fromCharCode(m2), k2 = r2, E2 = false, F2 = e2.md.block.ruler.getRules("list"), C2 = e2.parentType, e2.parentType = "list"; k2 < t2; ) {
    for (D2 = w2, _2 = e2.eMarks[k2], l2 = b2 = e2.sCount[k2] + w2 - (e2.bMarks[r2] + e2.tShift[r2]); D2 < _2; ) {
      if (9 === (s2 = e2.src.charCodeAt(D2)))
        b2 += 4 - (b2 + e2.bsCount[k2]) % 4;
      else {
        if (32 !== s2)
          break;
        b2++;
      }
      D2++;
    }
    if ((c2 = (o2 = D2) >= _2 ? 1 : b2 - l2) > 4 && (c2 = 1), a2 = l2 + c2, (L2 = e2.push("list_item_open", "li", 1)).markup = String.fromCharCode(m2), L2.map = p2 = [r2, 0], u2 && (L2.info = e2.src.slice(q2, w2 - 1)), x2 = e2.tight, A2 = e2.tShift[r2], y2 = e2.sCount[r2], v2 = e2.listIndent, e2.listIndent = e2.blkIndent, e2.blkIndent = a2, e2.tight = true, e2.tShift[r2] = o2 - e2.bMarks[r2], e2.sCount[r2] = b2, o2 >= _2 && e2.isEmpty(r2 + 1) ? e2.line = Math.min(e2.line + 2, t2) : e2.md.block.tokenize(e2, r2, t2, true), e2.tight && !E2 || (T2 = false), E2 = e2.line - r2 > 1 && e2.isEmpty(e2.line - 1), e2.blkIndent = e2.listIndent, e2.listIndent = v2, e2.tShift[r2] = A2, e2.sCount[r2] = y2, e2.tight = x2, (L2 = e2.push("list_item_close", "li", -1)).markup = String.fromCharCode(m2), k2 = r2 = e2.line, p2[1] = k2, o2 = e2.bMarks[r2], k2 >= t2)
      break;
    if (e2.sCount[k2] < e2.blkIndent)
      break;
    if (e2.sCount[r2] - e2.blkIndent >= 4)
      break;
    for (S2 = false, i2 = 0, h2 = F2.length; i2 < h2; i2++)
      if (F2[i2](e2, k2, t2, true)) {
        S2 = true;
        break;
      }
    if (S2)
      break;
    if (u2) {
      if ((w2 = be(e2, k2)) < 0)
        break;
      q2 = e2.bMarks[k2] + e2.tShift[k2];
    } else if ((w2 = ke(e2, k2)) < 0)
      break;
    if (m2 !== e2.src.charCodeAt(w2 - 1))
      break;
  }
  return (L2 = u2 ? e2.push("ordered_list_close", "ol", -1) : e2.push("bullet_list_close", "ul", -1)).markup = String.fromCharCode(m2), f2[1] = k2, e2.line = k2, e2.parentType = C2, T2 && function(e3, r3) {
    var t3, n3, s3 = e3.level + 2;
    for (t3 = r3 + 2, n3 = e3.tokens.length - 2; t3 < n3; t3++)
      e3.tokens[t3].level === s3 && "paragraph_open" === e3.tokens[t3].type && (e3.tokens[t3 + 2].hidden = true, e3.tokens[t3].hidden = true, t3 += 2);
  }(e2, d2), true;
}, ["paragraph", "reference", "blockquote"]], ["reference", function(e2, r2, t2, n2) {
  var s2, o2, i2, a2, c2, l2, u2, p2, h2, f2, d2, m2, g2, _2, k2, b2, v2 = 0, C2 = e2.bMarks[r2] + e2.tShift[r2], y2 = e2.eMarks[r2], A2 = r2 + 1;
  if (e2.sCount[r2] - e2.blkIndent >= 4)
    return false;
  if (91 !== e2.src.charCodeAt(C2))
    return false;
  for (; ++C2 < y2; )
    if (93 === e2.src.charCodeAt(C2) && 92 !== e2.src.charCodeAt(C2 - 1)) {
      if (C2 + 1 === y2)
        return false;
      if (58 !== e2.src.charCodeAt(C2 + 1))
        return false;
      break;
    }
  for (a2 = e2.lineMax, k2 = e2.md.block.ruler.getRules("reference"), f2 = e2.parentType, e2.parentType = "reference"; A2 < a2 && !e2.isEmpty(A2); A2++)
    if (!(e2.sCount[A2] - e2.blkIndent > 3 || e2.sCount[A2] < 0)) {
      for (_2 = false, l2 = 0, u2 = k2.length; l2 < u2; l2++)
        if (k2[l2](e2, A2, a2, true)) {
          _2 = true;
          break;
        }
      if (_2)
        break;
    }
  for (y2 = (g2 = e2.getLines(r2, A2, e2.blkIndent, false).trim()).length, C2 = 1; C2 < y2; C2++) {
    if (91 === (s2 = g2.charCodeAt(C2)))
      return false;
    if (93 === s2) {
      h2 = C2;
      break;
    }
    (10 === s2 || 92 === s2 && ++C2 < y2 && 10 === g2.charCodeAt(C2)) && v2++;
  }
  if (h2 < 0 || 58 !== g2.charCodeAt(h2 + 1))
    return false;
  for (C2 = h2 + 2; C2 < y2; C2++)
    if (10 === (s2 = g2.charCodeAt(C2)))
      v2++;
    else if (!Ce(s2))
      break;
  if (!(d2 = e2.md.helpers.parseLinkDestination(g2, C2, y2)).ok)
    return false;
  if (c2 = e2.md.normalizeLink(d2.str), !e2.md.validateLink(c2))
    return false;
  for (o2 = C2 = d2.pos, i2 = v2 += d2.lines, m2 = C2; C2 < y2; C2++)
    if (10 === (s2 = g2.charCodeAt(C2)))
      v2++;
    else if (!Ce(s2))
      break;
  for (d2 = e2.md.helpers.parseLinkTitle(g2, C2, y2), C2 < y2 && m2 !== C2 && d2.ok ? (b2 = d2.str, C2 = d2.pos, v2 += d2.lines) : (b2 = "", C2 = o2, v2 = i2); C2 < y2 && (s2 = g2.charCodeAt(C2), Ce(s2)); )
    C2++;
  if (C2 < y2 && 10 !== g2.charCodeAt(C2) && b2)
    for (b2 = "", C2 = o2, v2 = i2; C2 < y2 && (s2 = g2.charCodeAt(C2), Ce(s2)); )
      C2++;
  return !(C2 < y2 && 10 !== g2.charCodeAt(C2)) && (!!(p2 = ve(g2.slice(1, h2))) && (n2 || (void 0 === e2.env.references && (e2.env.references = {}), void 0 === e2.env.references[p2] && (e2.env.references[p2] = { title: b2, href: c2 }), e2.parentType = f2, e2.line = r2 + v2 + 1), true));
}], ["html_block", function(e2, r2, t2, n2) {
  var s2, o2, i2, a2, c2 = e2.bMarks[r2] + e2.tShift[r2], l2 = e2.eMarks[r2];
  if (e2.sCount[r2] - e2.blkIndent >= 4)
    return false;
  if (!e2.md.options.html)
    return false;
  if (60 !== e2.src.charCodeAt(c2))
    return false;
  for (a2 = e2.src.slice(c2, l2), s2 = 0; s2 < Se.length && !Se[s2][0].test(a2); s2++)
    ;
  if (s2 === Se.length)
    return false;
  if (n2)
    return Se[s2][2];
  if (o2 = r2 + 1, !Se[s2][1].test(a2)) {
    for (; o2 < t2 && !(e2.sCount[o2] < e2.blkIndent); o2++)
      if (c2 = e2.bMarks[o2] + e2.tShift[o2], l2 = e2.eMarks[o2], a2 = e2.src.slice(c2, l2), Se[s2][1].test(a2)) {
        0 !== a2.length && o2++;
        break;
      }
  }
  return e2.line = o2, (i2 = e2.push("html_block", "", 0)).map = [r2, o2], i2.content = e2.getLines(r2, o2, e2.blkIndent, true), true;
}, ["paragraph", "reference", "blockquote"]], ["heading", function(e2, r2, t2, n2) {
  var s2, o2, i2, a2, c2 = e2.bMarks[r2] + e2.tShift[r2], l2 = e2.eMarks[r2];
  if (e2.sCount[r2] - e2.blkIndent >= 4)
    return false;
  if (35 !== (s2 = e2.src.charCodeAt(c2)) || c2 >= l2)
    return false;
  for (o2 = 1, s2 = e2.src.charCodeAt(++c2); 35 === s2 && c2 < l2 && o2 <= 6; )
    o2++, s2 = e2.src.charCodeAt(++c2);
  return !(o2 > 6 || c2 < l2 && !Fe(s2)) && (n2 || (l2 = e2.skipSpacesBack(l2, c2), (i2 = e2.skipCharsBack(l2, 35, c2)) > c2 && Fe(e2.src.charCodeAt(i2 - 1)) && (l2 = i2), e2.line = r2 + 1, (a2 = e2.push("heading_open", "h" + String(o2), 1)).markup = "########".slice(0, o2), a2.map = [r2, e2.line], (a2 = e2.push("inline", "", 0)).content = e2.src.slice(c2, l2).trim(), a2.map = [r2, e2.line], a2.children = [], (a2 = e2.push("heading_close", "h" + String(o2), -1)).markup = "########".slice(0, o2)), true);
}, ["paragraph", "reference", "blockquote"]], ["lheading", function(e2, r2, t2) {
  var n2, s2, o2, i2, a2, c2, l2, u2, p2, h2, f2 = r2 + 1, d2 = e2.md.block.ruler.getRules("paragraph");
  if (e2.sCount[r2] - e2.blkIndent >= 4)
    return false;
  for (h2 = e2.parentType, e2.parentType = "paragraph"; f2 < t2 && !e2.isEmpty(f2); f2++)
    if (!(e2.sCount[f2] - e2.blkIndent > 3)) {
      if (e2.sCount[f2] >= e2.blkIndent && (c2 = e2.bMarks[f2] + e2.tShift[f2]) < (l2 = e2.eMarks[f2]) && (45 === (p2 = e2.src.charCodeAt(c2)) || 61 === p2) && (c2 = e2.skipChars(c2, p2), (c2 = e2.skipSpaces(c2)) >= l2)) {
        u2 = 61 === p2 ? 1 : 2;
        break;
      }
      if (!(e2.sCount[f2] < 0)) {
        for (s2 = false, o2 = 0, i2 = d2.length; o2 < i2; o2++)
          if (d2[o2](e2, f2, t2, true)) {
            s2 = true;
            break;
          }
        if (s2)
          break;
      }
    }
  return !!u2 && (n2 = e2.getLines(r2, f2, e2.blkIndent, false).trim(), e2.line = f2 + 1, (a2 = e2.push("heading_open", "h" + String(u2), 1)).markup = String.fromCharCode(p2), a2.map = [r2, e2.line], (a2 = e2.push("inline", "", 0)).content = n2, a2.map = [r2, e2.line - 1], a2.children = [], (a2 = e2.push("heading_close", "h" + String(u2), -1)).markup = String.fromCharCode(p2), e2.parentType = h2, true);
}], ["paragraph", function(e2, r2) {
  var t2, n2, s2, o2, i2, a2, c2 = r2 + 1, l2 = e2.md.block.ruler.getRules("paragraph"), u2 = e2.lineMax;
  for (a2 = e2.parentType, e2.parentType = "paragraph"; c2 < u2 && !e2.isEmpty(c2); c2++)
    if (!(e2.sCount[c2] - e2.blkIndent > 3 || e2.sCount[c2] < 0)) {
      for (n2 = false, s2 = 0, o2 = l2.length; s2 < o2; s2++)
        if (l2[s2](e2, c2, u2, true)) {
          n2 = true;
          break;
        }
      if (n2)
        break;
    }
  return t2 = e2.getLines(r2, c2, e2.blkIndent, false).trim(), e2.line = c2, (i2 = e2.push("paragraph_open", "p", 1)).map = [r2, e2.line], (i2 = e2.push("inline", "", 0)).content = t2, i2.map = [r2, e2.line], i2.children = [], i2 = e2.push("paragraph_close", "p", -1), e2.parentType = a2, true;
}]];
function Be() {
  this.ruler = new Me();
  for (var e2 = 0; e2 < Re.length; e2++)
    this.ruler.push(Re[e2][0], Re[e2][1], { alt: (Re[e2][2] || []).slice() });
}
Be.prototype.tokenize = function(e2, r2, t2) {
  for (var n2, s2 = this.ruler.getRules(""), o2 = s2.length, i2 = r2, a2 = false, c2 = e2.md.options.maxNesting; i2 < t2 && (e2.line = i2 = e2.skipEmptyLines(i2), !(i2 >= t2)) && !(e2.sCount[i2] < e2.blkIndent); ) {
    if (e2.level >= c2) {
      e2.line = t2;
      break;
    }
    for (n2 = 0; n2 < o2 && !s2[n2](e2, i2, t2, false); n2++)
      ;
    e2.tight = !a2, e2.isEmpty(e2.line - 1) && (a2 = true), (i2 = e2.line) < t2 && e2.isEmpty(i2) && (a2 = true, i2++, e2.line = i2);
  }
}, Be.prototype.parse = function(e2, r2, t2, n2) {
  var s2;
  e2 && (s2 = new this.State(e2, r2, t2, n2), this.tokenize(s2, s2.line, s2.lineMax));
}, Be.prototype.State = Ie;
var Ne = Be;
function Oe(e2) {
  switch (e2) {
    case 10:
    case 33:
    case 35:
    case 36:
    case 37:
    case 38:
    case 42:
    case 43:
    case 45:
    case 58:
    case 60:
    case 61:
    case 62:
    case 64:
    case 91:
    case 92:
    case 93:
    case 94:
    case 95:
    case 96:
    case 123:
    case 125:
    case 126:
      return true;
    default:
      return false;
  }
}
for (var Pe = /(?:^|[^a-z0-9.+-])([a-z][a-z0-9.+-]*)$/i, je = r.isSpace, Ue = r.isSpace, Ve = [], Ze = 0; Ze < 256; Ze++)
  Ve.push(0);
"\\!\"#$%&'()*+,./:;<=>?@[]^_`{|}~-".split("").forEach(function(e2) {
  Ve[e2.charCodeAt(0)] = 1;
});
var $e = {};
function Ge(e2, r2) {
  var t2, n2, s2, o2, i2, a2 = [], c2 = r2.length;
  for (t2 = 0; t2 < c2; t2++)
    126 === (s2 = r2[t2]).marker && -1 !== s2.end && (o2 = r2[s2.end], (i2 = e2.tokens[s2.token]).type = "s_open", i2.tag = "s", i2.nesting = 1, i2.markup = "~~", i2.content = "", (i2 = e2.tokens[o2.token]).type = "s_close", i2.tag = "s", i2.nesting = -1, i2.markup = "~~", i2.content = "", "text" === e2.tokens[o2.token - 1].type && "~" === e2.tokens[o2.token - 1].content && a2.push(o2.token - 1));
  for (; a2.length; ) {
    for (n2 = (t2 = a2.pop()) + 1; n2 < e2.tokens.length && "s_close" === e2.tokens[n2].type; )
      n2++;
    t2 !== --n2 && (i2 = e2.tokens[n2], e2.tokens[n2] = e2.tokens[t2], e2.tokens[t2] = i2);
  }
}
$e.tokenize = function(e2, r2) {
  var t2, n2, s2, o2, i2 = e2.pos, a2 = e2.src.charCodeAt(i2);
  if (r2)
    return false;
  if (126 !== a2)
    return false;
  if (s2 = (n2 = e2.scanDelims(e2.pos, true)).length, o2 = String.fromCharCode(a2), s2 < 2)
    return false;
  for (s2 % 2 && (e2.push("text", "", 0).content = o2, s2--), t2 = 0; t2 < s2; t2 += 2)
    e2.push("text", "", 0).content = o2 + o2, e2.delimiters.push({ marker: a2, length: 0, token: e2.tokens.length - 1, end: -1, open: n2.can_open, close: n2.can_close });
  return e2.pos += n2.length, true;
}, $e.postProcess = function(e2) {
  var r2, t2 = e2.tokens_meta, n2 = e2.tokens_meta.length;
  for (Ge(e2, e2.delimiters), r2 = 0; r2 < n2; r2++)
    t2[r2] && t2[r2].delimiters && Ge(e2, t2[r2].delimiters);
};
var He = {};
function Je(e2, r2) {
  var t2, n2, s2, o2, i2, a2;
  for (t2 = r2.length - 1; t2 >= 0; t2--)
    95 !== (n2 = r2[t2]).marker && 42 !== n2.marker || -1 !== n2.end && (s2 = r2[n2.end], a2 = t2 > 0 && r2[t2 - 1].end === n2.end + 1 && r2[t2 - 1].marker === n2.marker && r2[t2 - 1].token === n2.token - 1 && r2[n2.end + 1].token === s2.token + 1, i2 = String.fromCharCode(n2.marker), (o2 = e2.tokens[n2.token]).type = a2 ? "strong_open" : "em_open", o2.tag = a2 ? "strong" : "em", o2.nesting = 1, o2.markup = a2 ? i2 + i2 : i2, o2.content = "", (o2 = e2.tokens[s2.token]).type = a2 ? "strong_close" : "em_close", o2.tag = a2 ? "strong" : "em", o2.nesting = -1, o2.markup = a2 ? i2 + i2 : i2, o2.content = "", a2 && (e2.tokens[r2[t2 - 1].token].content = "", e2.tokens[r2[n2.end + 1].token].content = "", t2--));
}
He.tokenize = function(e2, r2) {
  var t2, n2, s2 = e2.pos, o2 = e2.src.charCodeAt(s2);
  if (r2)
    return false;
  if (95 !== o2 && 42 !== o2)
    return false;
  for (n2 = e2.scanDelims(e2.pos, 42 === o2), t2 = 0; t2 < n2.length; t2++)
    e2.push("text", "", 0).content = String.fromCharCode(o2), e2.delimiters.push({ marker: o2, length: n2.length, token: e2.tokens.length - 1, end: -1, open: n2.can_open, close: n2.can_close });
  return e2.pos += n2.length, true;
}, He.postProcess = function(e2) {
  var r2, t2 = e2.tokens_meta, n2 = e2.tokens_meta.length;
  for (Je(e2, e2.delimiters), r2 = 0; r2 < n2; r2++)
    t2[r2] && t2[r2].delimiters && Je(e2, t2[r2].delimiters);
};
var We = r.normalizeReference, Ye = r.isSpace, Ke = r.normalizeReference, Qe = r.isSpace, Xe = /^([a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*)$/, er = /^([a-zA-Z][a-zA-Z0-9+.\-]{1,31}):([^<>\x00-\x20]*)$/, rr = ye.HTML_TAG_RE;
var tr = t, nr = r.has, sr = r.isValidEntityCode, or = r.fromCodePoint, ir = /^&#((?:x[a-f0-9]{1,6}|[0-9]{1,7}));/i, ar = /^&([a-z][a-z0-9]{1,31});/i;
function cr(e2, r2) {
  var t2, n2, s2, o2, i2, a2, c2, l2, u2 = {}, p2 = r2.length;
  if (p2) {
    var h2 = 0, f2 = -2, d2 = [];
    for (t2 = 0; t2 < p2; t2++)
      if (s2 = r2[t2], d2.push(0), r2[h2].marker === s2.marker && f2 === s2.token - 1 || (h2 = t2), f2 = s2.token, s2.length = s2.length || 0, s2.close) {
        for (u2.hasOwnProperty(s2.marker) || (u2[s2.marker] = [-1, -1, -1, -1, -1, -1]), i2 = u2[s2.marker][(s2.open ? 3 : 0) + s2.length % 3], a2 = n2 = h2 - d2[h2] - 1; n2 > i2; n2 -= d2[n2] + 1)
          if ((o2 = r2[n2]).marker === s2.marker && o2.open && o2.end < 0 && (c2 = false, (o2.close || s2.open) && (o2.length + s2.length) % 3 == 0 && (o2.length % 3 == 0 && s2.length % 3 == 0 || (c2 = true)), !c2)) {
            l2 = n2 > 0 && !r2[n2 - 1].open ? d2[n2 - 1] + 1 : 0, d2[t2] = t2 - n2 + l2, d2[n2] = l2, s2.open = false, o2.end = t2, o2.close = false, a2 = -1, f2 = -2;
            break;
          }
        -1 !== a2 && (u2[s2.marker][(s2.open ? 3 : 0) + (s2.length || 0) % 3] = a2);
      }
  }
}
var lr = se, ur = r.isWhiteSpace, pr = r.isPunctChar, hr = r.isMdAsciiPunct;
function fr(e2, r2, t2, n2) {
  this.src = e2, this.env = t2, this.md = r2, this.tokens = n2, this.tokens_meta = Array(n2.length), this.pos = 0, this.posMax = this.src.length, this.level = 0, this.pending = "", this.pendingLevel = 0, this.cache = {}, this.delimiters = [], this._prev_delimiters = [], this.backticks = {}, this.backticksScanned = false, this.linkLevel = 0;
}
fr.prototype.pushPending = function() {
  var e2 = new lr("text", "", 0);
  return e2.content = this.pending, e2.level = this.pendingLevel, this.tokens.push(e2), this.pending = "", e2;
}, fr.prototype.push = function(e2, r2, t2) {
  this.pending && this.pushPending();
  var n2 = new lr(e2, r2, t2), s2 = null;
  return t2 < 0 && (this.level--, this.delimiters = this._prev_delimiters.pop()), n2.level = this.level, t2 > 0 && (this.level++, this._prev_delimiters.push(this.delimiters), this.delimiters = [], s2 = { delimiters: this.delimiters }), this.pendingLevel = this.level, this.tokens.push(n2), this.tokens_meta.push(s2), n2;
}, fr.prototype.scanDelims = function(e2, r2) {
  var t2, n2, s2, o2, i2, a2, c2, l2, u2, p2 = e2, h2 = true, f2 = true, d2 = this.posMax, m2 = this.src.charCodeAt(e2);
  for (t2 = e2 > 0 ? this.src.charCodeAt(e2 - 1) : 32; p2 < d2 && this.src.charCodeAt(p2) === m2; )
    p2++;
  return s2 = p2 - e2, n2 = p2 < d2 ? this.src.charCodeAt(p2) : 32, c2 = hr(t2) || pr(String.fromCharCode(t2)), u2 = hr(n2) || pr(String.fromCharCode(n2)), a2 = ur(t2), (l2 = ur(n2)) ? h2 = false : u2 && (a2 || c2 || (h2 = false)), a2 ? f2 = false : c2 && (l2 || u2 || (f2 = false)), r2 ? (o2 = h2, i2 = f2) : (o2 = h2 && (!f2 || c2), i2 = f2 && (!h2 || u2)), { can_open: o2, can_close: i2, length: s2 };
}, fr.prototype.Token = lr;
var dr = fr, mr = N, gr = [["text", function(e2, r2) {
  for (var t2 = e2.pos; t2 < e2.posMax && !Oe(e2.src.charCodeAt(t2)); )
    t2++;
  return t2 !== e2.pos && (r2 || (e2.pending += e2.src.slice(e2.pos, t2)), e2.pos = t2, true);
}], ["linkify", function(e2, r2) {
  var t2, n2, s2, o2, i2, a2, c2;
  return !!e2.md.options.linkify && (!(e2.linkLevel > 0) && (!((t2 = e2.pos) + 3 > e2.posMax) && (58 === e2.src.charCodeAt(t2) && (47 === e2.src.charCodeAt(t2 + 1) && (47 === e2.src.charCodeAt(t2 + 2) && (!!(n2 = e2.pending.match(Pe)) && (s2 = n2[1], !!(o2 = e2.md.linkify.matchAtStart(e2.src.slice(t2 - s2.length))) && (i2 = (i2 = o2.url).replace(/\*+$/, ""), a2 = e2.md.normalizeLink(i2), !!e2.md.validateLink(a2) && (r2 || (e2.pending = e2.pending.slice(0, -s2.length), (c2 = e2.push("link_open", "a", 1)).attrs = [["href", a2]], c2.markup = "linkify", c2.info = "auto", (c2 = e2.push("text", "", 0)).content = e2.md.normalizeLinkText(i2), (c2 = e2.push("link_close", "a", -1)).markup = "linkify", c2.info = "auto"), e2.pos += i2.length - s2.length, true)))))))));
}], ["newline", function(e2, r2) {
  var t2, n2, s2, o2 = e2.pos;
  if (10 !== e2.src.charCodeAt(o2))
    return false;
  if (t2 = e2.pending.length - 1, n2 = e2.posMax, !r2)
    if (t2 >= 0 && 32 === e2.pending.charCodeAt(t2))
      if (t2 >= 1 && 32 === e2.pending.charCodeAt(t2 - 1)) {
        for (s2 = t2 - 1; s2 >= 1 && 32 === e2.pending.charCodeAt(s2 - 1); )
          s2--;
        e2.pending = e2.pending.slice(0, s2), e2.push("hardbreak", "br", 0);
      } else
        e2.pending = e2.pending.slice(0, -1), e2.push("softbreak", "br", 0);
    else
      e2.push("softbreak", "br", 0);
  for (o2++; o2 < n2 && je(e2.src.charCodeAt(o2)); )
    o2++;
  return e2.pos = o2, true;
}], ["escape", function(e2, r2) {
  var t2, n2, s2, o2, i2, a2 = e2.pos, c2 = e2.posMax;
  if (92 !== e2.src.charCodeAt(a2))
    return false;
  if (++a2 >= c2)
    return false;
  if (10 === (t2 = e2.src.charCodeAt(a2))) {
    for (r2 || e2.push("hardbreak", "br", 0), a2++; a2 < c2 && (t2 = e2.src.charCodeAt(a2), Ue(t2)); )
      a2++;
    return e2.pos = a2, true;
  }
  return o2 = e2.src[a2], t2 >= 55296 && t2 <= 56319 && a2 + 1 < c2 && (n2 = e2.src.charCodeAt(a2 + 1)) >= 56320 && n2 <= 57343 && (o2 += e2.src[a2 + 1], a2++), s2 = "\\" + o2, r2 || (i2 = e2.push("text_special", "", 0), t2 < 256 && 0 !== Ve[t2] ? i2.content = o2 : i2.content = s2, i2.markup = s2, i2.info = "escape"), e2.pos = a2 + 1, true;
}], ["backticks", function(e2, r2) {
  var t2, n2, s2, o2, i2, a2, c2, l2, u2 = e2.pos;
  if (96 !== e2.src.charCodeAt(u2))
    return false;
  for (t2 = u2, u2++, n2 = e2.posMax; u2 < n2 && 96 === e2.src.charCodeAt(u2); )
    u2++;
  if (c2 = (s2 = e2.src.slice(t2, u2)).length, e2.backticksScanned && (e2.backticks[c2] || 0) <= t2)
    return r2 || (e2.pending += s2), e2.pos += c2, true;
  for (i2 = a2 = u2; -1 !== (i2 = e2.src.indexOf("`", a2)); ) {
    for (a2 = i2 + 1; a2 < n2 && 96 === e2.src.charCodeAt(a2); )
      a2++;
    if ((l2 = a2 - i2) === c2)
      return r2 || ((o2 = e2.push("code_inline", "code", 0)).markup = s2, o2.content = e2.src.slice(u2, i2).replace(/\n/g, " ").replace(/^ (.+) $/, "$1")), e2.pos = a2, true;
    e2.backticks[l2] = i2;
  }
  return e2.backticksScanned = true, r2 || (e2.pending += s2), e2.pos += c2, true;
}], ["strikethrough", $e.tokenize], ["emphasis", He.tokenize], ["link", function(e2, r2) {
  var t2, n2, s2, o2, i2, a2, c2, l2, u2 = "", p2 = "", h2 = e2.pos, f2 = e2.posMax, d2 = e2.pos, m2 = true;
  if (91 !== e2.src.charCodeAt(e2.pos))
    return false;
  if (i2 = e2.pos + 1, (o2 = e2.md.helpers.parseLinkLabel(e2, e2.pos, true)) < 0)
    return false;
  if ((a2 = o2 + 1) < f2 && 40 === e2.src.charCodeAt(a2)) {
    for (m2 = false, a2++; a2 < f2 && (n2 = e2.src.charCodeAt(a2), Ye(n2) || 10 === n2); a2++)
      ;
    if (a2 >= f2)
      return false;
    if (d2 = a2, (c2 = e2.md.helpers.parseLinkDestination(e2.src, a2, e2.posMax)).ok) {
      for (u2 = e2.md.normalizeLink(c2.str), e2.md.validateLink(u2) ? a2 = c2.pos : u2 = "", d2 = a2; a2 < f2 && (n2 = e2.src.charCodeAt(a2), Ye(n2) || 10 === n2); a2++)
        ;
      if (c2 = e2.md.helpers.parseLinkTitle(e2.src, a2, e2.posMax), a2 < f2 && d2 !== a2 && c2.ok)
        for (p2 = c2.str, a2 = c2.pos; a2 < f2 && (n2 = e2.src.charCodeAt(a2), Ye(n2) || 10 === n2); a2++)
          ;
    }
    (a2 >= f2 || 41 !== e2.src.charCodeAt(a2)) && (m2 = true), a2++;
  }
  if (m2) {
    if (void 0 === e2.env.references)
      return false;
    if (a2 < f2 && 91 === e2.src.charCodeAt(a2) ? (d2 = a2 + 1, (a2 = e2.md.helpers.parseLinkLabel(e2, a2)) >= 0 ? s2 = e2.src.slice(d2, a2++) : a2 = o2 + 1) : a2 = o2 + 1, s2 || (s2 = e2.src.slice(i2, o2)), !(l2 = e2.env.references[We(s2)]))
      return e2.pos = h2, false;
    u2 = l2.href, p2 = l2.title;
  }
  return r2 || (e2.pos = i2, e2.posMax = o2, e2.push("link_open", "a", 1).attrs = t2 = [["href", u2]], p2 && t2.push(["title", p2]), e2.linkLevel++, e2.md.inline.tokenize(e2), e2.linkLevel--, e2.push("link_close", "a", -1)), e2.pos = a2, e2.posMax = f2, true;
}], ["image", function(e2, r2) {
  var t2, n2, s2, o2, i2, a2, c2, l2, u2, p2, h2, f2, d2, m2 = "", g2 = e2.pos, _2 = e2.posMax;
  if (33 !== e2.src.charCodeAt(e2.pos))
    return false;
  if (91 !== e2.src.charCodeAt(e2.pos + 1))
    return false;
  if (a2 = e2.pos + 2, (i2 = e2.md.helpers.parseLinkLabel(e2, e2.pos + 1, false)) < 0)
    return false;
  if ((c2 = i2 + 1) < _2 && 40 === e2.src.charCodeAt(c2)) {
    for (c2++; c2 < _2 && (n2 = e2.src.charCodeAt(c2), Qe(n2) || 10 === n2); c2++)
      ;
    if (c2 >= _2)
      return false;
    for (d2 = c2, (u2 = e2.md.helpers.parseLinkDestination(e2.src, c2, e2.posMax)).ok && (m2 = e2.md.normalizeLink(u2.str), e2.md.validateLink(m2) ? c2 = u2.pos : m2 = ""), d2 = c2; c2 < _2 && (n2 = e2.src.charCodeAt(c2), Qe(n2) || 10 === n2); c2++)
      ;
    if (u2 = e2.md.helpers.parseLinkTitle(e2.src, c2, e2.posMax), c2 < _2 && d2 !== c2 && u2.ok)
      for (p2 = u2.str, c2 = u2.pos; c2 < _2 && (n2 = e2.src.charCodeAt(c2), Qe(n2) || 10 === n2); c2++)
        ;
    else
      p2 = "";
    if (c2 >= _2 || 41 !== e2.src.charCodeAt(c2))
      return e2.pos = g2, false;
    c2++;
  } else {
    if (void 0 === e2.env.references)
      return false;
    if (c2 < _2 && 91 === e2.src.charCodeAt(c2) ? (d2 = c2 + 1, (c2 = e2.md.helpers.parseLinkLabel(e2, c2)) >= 0 ? o2 = e2.src.slice(d2, c2++) : c2 = i2 + 1) : c2 = i2 + 1, o2 || (o2 = e2.src.slice(a2, i2)), !(l2 = e2.env.references[Ke(o2)]))
      return e2.pos = g2, false;
    m2 = l2.href, p2 = l2.title;
  }
  return r2 || (s2 = e2.src.slice(a2, i2), e2.md.inline.parse(s2, e2.md, e2.env, f2 = []), (h2 = e2.push("image", "img", 0)).attrs = t2 = [["src", m2], ["alt", ""]], h2.children = f2, h2.content = s2, p2 && t2.push(["title", p2])), e2.pos = c2, e2.posMax = _2, true;
}], ["autolink", function(e2, r2) {
  var t2, n2, s2, o2, i2, a2, c2 = e2.pos;
  if (60 !== e2.src.charCodeAt(c2))
    return false;
  for (i2 = e2.pos, a2 = e2.posMax; ; ) {
    if (++c2 >= a2)
      return false;
    if (60 === (o2 = e2.src.charCodeAt(c2)))
      return false;
    if (62 === o2)
      break;
  }
  return t2 = e2.src.slice(i2 + 1, c2), er.test(t2) ? (n2 = e2.md.normalizeLink(t2), !!e2.md.validateLink(n2) && (r2 || ((s2 = e2.push("link_open", "a", 1)).attrs = [["href", n2]], s2.markup = "autolink", s2.info = "auto", (s2 = e2.push("text", "", 0)).content = e2.md.normalizeLinkText(t2), (s2 = e2.push("link_close", "a", -1)).markup = "autolink", s2.info = "auto"), e2.pos += t2.length + 2, true)) : !!Xe.test(t2) && (n2 = e2.md.normalizeLink("mailto:" + t2), !!e2.md.validateLink(n2) && (r2 || ((s2 = e2.push("link_open", "a", 1)).attrs = [["href", n2]], s2.markup = "autolink", s2.info = "auto", (s2 = e2.push("text", "", 0)).content = e2.md.normalizeLinkText(t2), (s2 = e2.push("link_close", "a", -1)).markup = "autolink", s2.info = "auto"), e2.pos += t2.length + 2, true));
}], ["html_inline", function(e2, r2) {
  var t2, n2, s2, o2, i2, a2 = e2.pos;
  return !!e2.md.options.html && (s2 = e2.posMax, !(60 !== e2.src.charCodeAt(a2) || a2 + 2 >= s2) && (!(33 !== (t2 = e2.src.charCodeAt(a2 + 1)) && 63 !== t2 && 47 !== t2 && !function(e3) {
    var r3 = 32 | e3;
    return r3 >= 97 && r3 <= 122;
  }(t2)) && (!!(n2 = e2.src.slice(a2).match(rr)) && (r2 || ((o2 = e2.push("html_inline", "", 0)).content = e2.src.slice(a2, a2 + n2[0].length), i2 = o2.content, /^<a[>\s]/i.test(i2) && e2.linkLevel++, function(e3) {
    return /^<\/a\s*>/i.test(e3);
  }(o2.content) && e2.linkLevel--), e2.pos += n2[0].length, true))));
}], ["entity", function(e2, r2) {
  var t2, n2, s2, o2 = e2.pos, i2 = e2.posMax;
  if (38 !== e2.src.charCodeAt(o2))
    return false;
  if (o2 + 1 >= i2)
    return false;
  if (35 === e2.src.charCodeAt(o2 + 1)) {
    if (n2 = e2.src.slice(o2).match(ir))
      return r2 || (t2 = "x" === n2[1][0].toLowerCase() ? parseInt(n2[1].slice(1), 16) : parseInt(n2[1], 10), (s2 = e2.push("text_special", "", 0)).content = sr(t2) ? or(t2) : or(65533), s2.markup = n2[0], s2.info = "entity"), e2.pos += n2[0].length, true;
  } else if ((n2 = e2.src.slice(o2).match(ar)) && nr(tr, n2[1]))
    return r2 || ((s2 = e2.push("text_special", "", 0)).content = tr[n2[1]], s2.markup = n2[0], s2.info = "entity"), e2.pos += n2[0].length, true;
  return false;
}]], _r = [["balance_pairs", function(e2) {
  var r2, t2 = e2.tokens_meta, n2 = e2.tokens_meta.length;
  for (cr(0, e2.delimiters), r2 = 0; r2 < n2; r2++)
    t2[r2] && t2[r2].delimiters && cr(0, t2[r2].delimiters);
}], ["strikethrough", $e.postProcess], ["emphasis", He.postProcess], ["fragments_join", function(e2) {
  var r2, t2, n2 = 0, s2 = e2.tokens, o2 = e2.tokens.length;
  for (r2 = t2 = 0; r2 < o2; r2++)
    s2[r2].nesting < 0 && n2--, s2[r2].level = n2, s2[r2].nesting > 0 && n2++, "text" === s2[r2].type && r2 + 1 < o2 && "text" === s2[r2 + 1].type ? s2[r2 + 1].content = s2[r2].content + s2[r2 + 1].content : (r2 !== t2 && (s2[t2] = s2[r2]), t2++);
  r2 !== t2 && (s2.length = t2);
}]];
function kr() {
  var e2;
  for (this.ruler = new mr(), e2 = 0; e2 < gr.length; e2++)
    this.ruler.push(gr[e2][0], gr[e2][1]);
  for (this.ruler2 = new mr(), e2 = 0; e2 < _r.length; e2++)
    this.ruler2.push(_r[e2][0], _r[e2][1]);
}
kr.prototype.skipToken = function(e2) {
  var r2, t2, n2 = e2.pos, s2 = this.ruler.getRules(""), o2 = s2.length, i2 = e2.md.options.maxNesting, a2 = e2.cache;
  if (void 0 === a2[n2]) {
    if (e2.level < i2)
      for (t2 = 0; t2 < o2 && (e2.level++, r2 = s2[t2](e2, true), e2.level--, !r2); t2++)
        ;
    else
      e2.pos = e2.posMax;
    r2 || e2.pos++, a2[n2] = e2.pos;
  } else
    e2.pos = a2[n2];
}, kr.prototype.tokenize = function(e2) {
  for (var r2, t2, n2 = this.ruler.getRules(""), s2 = n2.length, o2 = e2.posMax, i2 = e2.md.options.maxNesting; e2.pos < o2; ) {
    if (e2.level < i2)
      for (t2 = 0; t2 < s2 && !(r2 = n2[t2](e2, false)); t2++)
        ;
    if (r2) {
      if (e2.pos >= o2)
        break;
    } else
      e2.pending += e2.src[e2.pos++];
  }
  e2.pending && e2.pushPending();
}, kr.prototype.parse = function(e2, r2, t2, n2) {
  var s2, o2, i2, a2 = new this.State(e2, r2, t2, n2);
  for (this.tokenize(a2), i2 = (o2 = this.ruler2.getRules("")).length, s2 = 0; s2 < i2; s2++)
    o2[s2](a2);
}, kr.prototype.State = dr;
var br = kr;
function vr(e2) {
  var r2 = Array.prototype.slice.call(arguments, 1);
  return r2.forEach(function(r3) {
    r3 && Object.keys(r3).forEach(function(t2) {
      e2[t2] = r3[t2];
    });
  }), e2;
}
function Cr(e2) {
  return Object.prototype.toString.call(e2);
}
function yr(e2) {
  return "[object Function]" === Cr(e2);
}
function Ar(e2) {
  return e2.replace(/[.?*+^$[\]\\(){}|-]/g, "\\$&");
}
var xr = { fuzzyLink: true, fuzzyEmail: true, fuzzyIP: false };
var Dr = { "http:": { validate: function(e2, r2, t2) {
  var n2 = e2.slice(r2);
  return t2.re.http || (t2.re.http = new RegExp("^\\/\\/" + t2.re.src_auth + t2.re.src_host_port_strict + t2.re.src_path, "i")), t2.re.http.test(n2) ? n2.match(t2.re.http)[0].length : 0;
} }, "https:": "http:", "ftp:": "http:", "//": { validate: function(e2, r2, t2) {
  var n2 = e2.slice(r2);
  return t2.re.no_http || (t2.re.no_http = new RegExp("^" + t2.re.src_auth + "(?:localhost|(?:(?:" + t2.re.src_domain + ")\\.)+" + t2.re.src_domain_root + ")" + t2.re.src_port + t2.re.src_host_terminator + t2.re.src_path, "i")), t2.re.no_http.test(n2) ? r2 >= 3 && ":" === e2[r2 - 3] || r2 >= 3 && "/" === e2[r2 - 3] ? 0 : n2.match(t2.re.no_http)[0].length : 0;
} }, "mailto:": { validate: function(e2, r2, t2) {
  var n2 = e2.slice(r2);
  return t2.re.mailto || (t2.re.mailto = new RegExp("^" + t2.re.src_email_name + "@" + t2.re.src_host_strict, "i")), t2.re.mailto.test(n2) ? n2.match(t2.re.mailto)[0].length : 0;
} } }, wr = "biz|com|edu|gov|net|org|pro|web|xxx|aero|asia|coop|info|museum|name|shop|рф".split("|");
function Er(e2) {
  var r2 = e2.re = function(e3) {
    var r3 = {};
    return e3 = e3 || {}, r3.src_Any = D.source, r3.src_Cc = w.source, r3.src_Z = E.source, r3.src_P = n.source, r3.src_ZPCc = [r3.src_Z, r3.src_P, r3.src_Cc].join("|"), r3.src_ZCc = [r3.src_Z, r3.src_Cc].join("|"), r3.src_pseudo_letter = "(?:(?![><｜]|" + r3.src_ZPCc + ")" + r3.src_Any + ")", r3.src_ip4 = "(?:(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)", r3.src_auth = "(?:(?:(?!" + r3.src_ZCc + "|[@/\\[\\]()]).)+@)?", r3.src_port = "(?::(?:6(?:[0-4]\\d{3}|5(?:[0-4]\\d{2}|5(?:[0-2]\\d|3[0-5])))|[1-5]?\\d{1,4}))?", r3.src_host_terminator = "(?=$|[><｜]|" + r3.src_ZPCc + ")(?!" + (e3["---"] ? "-(?!--)|" : "-|") + "_|:\\d|\\.-|\\.(?!$|" + r3.src_ZPCc + "))", r3.src_path = "(?:[/?#](?:(?!" + r3.src_ZCc + `|[><｜]|[()[\\]{}.,"'?!\\-;]).|\\[(?:(?!` + r3.src_ZCc + "|\\]).)*\\]|\\((?:(?!" + r3.src_ZCc + "|[)]).)*\\)|\\{(?:(?!" + r3.src_ZCc + '|[}]).)*\\}|\\"(?:(?!' + r3.src_ZCc + `|["]).)+\\"|\\'(?:(?!` + r3.src_ZCc + "|[']).)+\\'|\\'(?=" + r3.src_pseudo_letter + "|[-])|\\.{2,}[a-zA-Z0-9%/&]|\\.(?!" + r3.src_ZCc + "|[.]|$)|" + (e3["---"] ? "\\-(?!--(?:[^-]|$))(?:-*)|" : "\\-+|") + ",(?!" + r3.src_ZCc + "|$)|;(?!" + r3.src_ZCc + "|$)|\\!+(?!" + r3.src_ZCc + "|[!]|$)|\\?(?!" + r3.src_ZCc + "|[?]|$))+|\\/)?", r3.src_email_name = '[\\-;:&=\\+\\$,\\.a-zA-Z0-9_][\\-;:&=\\+\\$,\\"\\.a-zA-Z0-9_]*', r3.src_xn = "xn--[a-z0-9\\-]{1,59}", r3.src_domain_root = "(?:" + r3.src_xn + "|" + r3.src_pseudo_letter + "{1,63})", r3.src_domain = "(?:" + r3.src_xn + "|(?:" + r3.src_pseudo_letter + ")|(?:" + r3.src_pseudo_letter + "(?:-|" + r3.src_pseudo_letter + "){0,61}" + r3.src_pseudo_letter + "))", r3.src_host = "(?:(?:(?:(?:" + r3.src_domain + ")\\.)*" + r3.src_domain + "))", r3.tpl_host_fuzzy = "(?:" + r3.src_ip4 + "|(?:(?:(?:" + r3.src_domain + ")\\.)+(?:%TLDS%)))", r3.tpl_host_no_ip_fuzzy = "(?:(?:(?:" + r3.src_domain + ")\\.)+(?:%TLDS%))", r3.src_host_strict = r3.src_host + r3.src_host_terminator, r3.tpl_host_fuzzy_strict = r3.tpl_host_fuzzy + r3.src_host_terminator, r3.src_host_port_strict = r3.src_host + r3.src_port + r3.src_host_terminator, r3.tpl_host_port_fuzzy_strict = r3.tpl_host_fuzzy + r3.src_port + r3.src_host_terminator, r3.tpl_host_port_no_ip_fuzzy_strict = r3.tpl_host_no_ip_fuzzy + r3.src_port + r3.src_host_terminator, r3.tpl_host_fuzzy_test = "localhost|www\\.|\\.\\d{1,3}\\.|(?:\\.(?:%TLDS%)(?:" + r3.src_ZPCc + "|>|$))", r3.tpl_email_fuzzy = '(^|[><｜]|"|\\(|' + r3.src_ZCc + ")(" + r3.src_email_name + "@" + r3.tpl_host_fuzzy_strict + ")", r3.tpl_link_fuzzy = "(^|(?![.:/\\-_@])(?:[$+<=>^`|｜]|" + r3.src_ZPCc + "))((?![$+<=>^`|｜])" + r3.tpl_host_port_fuzzy_strict + r3.src_path + ")", r3.tpl_link_no_ip_fuzzy = "(^|(?![.:/\\-_@])(?:[$+<=>^`|｜]|" + r3.src_ZPCc + "))((?![$+<=>^`|｜])" + r3.tpl_host_port_no_ip_fuzzy_strict + r3.src_path + ")", r3;
  }(e2.__opts__), t2 = e2.__tlds__.slice();
  function s2(e3) {
    return e3.replace("%TLDS%", r2.src_tlds);
  }
  e2.onCompile(), e2.__tlds_replaced__ || t2.push("a[cdefgilmnoqrstuwxz]|b[abdefghijmnorstvwyz]|c[acdfghiklmnoruvwxyz]|d[ejkmoz]|e[cegrstu]|f[ijkmor]|g[abdefghilmnpqrstuwy]|h[kmnrtu]|i[delmnoqrst]|j[emop]|k[eghimnprwyz]|l[abcikrstuvy]|m[acdeghklmnopqrstuvwxyz]|n[acefgilopruz]|om|p[aefghklmnrstwy]|qa|r[eosuw]|s[abcdeghijklmnortuvxyz]|t[cdfghjklmnortvwz]|u[agksyz]|v[aceginu]|w[fs]|y[et]|z[amw]"), t2.push(r2.src_xn), r2.src_tlds = t2.join("|"), r2.email_fuzzy = RegExp(s2(r2.tpl_email_fuzzy), "i"), r2.link_fuzzy = RegExp(s2(r2.tpl_link_fuzzy), "i"), r2.link_no_ip_fuzzy = RegExp(s2(r2.tpl_link_no_ip_fuzzy), "i"), r2.host_fuzzy_test = RegExp(s2(r2.tpl_host_fuzzy_test), "i");
  var o2 = [];
  function i2(e3, r3) {
    throw new Error('(LinkifyIt) Invalid schema "' + e3 + '": ' + r3);
  }
  e2.__compiled__ = {}, Object.keys(e2.__schemas__).forEach(function(r3) {
    var t3 = e2.__schemas__[r3];
    if (null !== t3) {
      var n2 = { validate: null, link: null };
      if (e2.__compiled__[r3] = n2, "[object Object]" === Cr(t3))
        return !function(e3) {
          return "[object RegExp]" === Cr(e3);
        }(t3.validate) ? yr(t3.validate) ? n2.validate = t3.validate : i2(r3, t3) : n2.validate = /* @__PURE__ */ function(e3) {
          return function(r4, t4) {
            var n3 = r4.slice(t4);
            return e3.test(n3) ? n3.match(e3)[0].length : 0;
          };
        }(t3.validate), void (yr(t3.normalize) ? n2.normalize = t3.normalize : t3.normalize ? i2(r3, t3) : n2.normalize = function(e3, r4) {
          r4.normalize(e3);
        });
      !function(e3) {
        return "[object String]" === Cr(e3);
      }(t3) ? i2(r3, t3) : o2.push(r3);
    }
  }), o2.forEach(function(r3) {
    e2.__compiled__[e2.__schemas__[r3]] && (e2.__compiled__[r3].validate = e2.__compiled__[e2.__schemas__[r3]].validate, e2.__compiled__[r3].normalize = e2.__compiled__[e2.__schemas__[r3]].normalize);
  }), e2.__compiled__[""] = { validate: null, normalize: function(e3, r3) {
    r3.normalize(e3);
  } };
  var a2 = Object.keys(e2.__compiled__).filter(function(r3) {
    return r3.length > 0 && e2.__compiled__[r3];
  }).map(Ar).join("|");
  e2.re.schema_test = RegExp("(^|(?!_)(?:[><｜]|" + r2.src_ZPCc + "))(" + a2 + ")", "i"), e2.re.schema_search = RegExp("(^|(?!_)(?:[><｜]|" + r2.src_ZPCc + "))(" + a2 + ")", "ig"), e2.re.schema_at_start = RegExp("^" + e2.re.schema_search.source, "i"), e2.re.pretest = RegExp("(" + e2.re.schema_test.source + ")|(" + e2.re.host_fuzzy_test.source + ")|@", "i"), function(e3) {
    e3.__index__ = -1, e3.__text_cache__ = "";
  }(e2);
}
function qr(e2, r2) {
  var t2 = e2.__index__, n2 = e2.__last_index__, s2 = e2.__text_cache__.slice(t2, n2);
  this.schema = e2.__schema__.toLowerCase(), this.index = t2 + r2, this.lastIndex = n2 + r2, this.raw = s2, this.text = s2, this.url = s2;
}
function Sr(e2, r2) {
  var t2 = new qr(e2, r2);
  return e2.__compiled__[t2.schema].normalize(t2, e2), t2;
}
function Fr(e2, r2) {
  if (!(this instanceof Fr))
    return new Fr(e2, r2);
  var t2;
  r2 || (t2 = e2, Object.keys(t2 || {}).reduce(function(e3, r3) {
    return e3 || xr.hasOwnProperty(r3);
  }, false) && (r2 = e2, e2 = {})), this.__opts__ = vr({}, xr, r2), this.__index__ = -1, this.__last_index__ = -1, this.__schema__ = "", this.__text_cache__ = "", this.__schemas__ = vr({}, Dr, e2), this.__compiled__ = {}, this.__tlds__ = wr, this.__tlds_replaced__ = false, this.re = {}, Er(this);
}
Fr.prototype.add = function(e2, r2) {
  return this.__schemas__[e2] = r2, Er(this), this;
}, Fr.prototype.set = function(e2) {
  return this.__opts__ = vr(this.__opts__, e2), this;
}, Fr.prototype.test = function(e2) {
  if (this.__text_cache__ = e2, this.__index__ = -1, !e2.length)
    return false;
  var r2, t2, n2, s2, o2, i2, a2, c2;
  if (this.re.schema_test.test(e2)) {
    for ((a2 = this.re.schema_search).lastIndex = 0; null !== (r2 = a2.exec(e2)); )
      if (s2 = this.testSchemaAt(e2, r2[2], a2.lastIndex)) {
        this.__schema__ = r2[2], this.__index__ = r2.index + r2[1].length, this.__last_index__ = r2.index + r2[0].length + s2;
        break;
      }
  }
  return this.__opts__.fuzzyLink && this.__compiled__["http:"] && (c2 = e2.search(this.re.host_fuzzy_test)) >= 0 && (this.__index__ < 0 || c2 < this.__index__) && null !== (t2 = e2.match(this.__opts__.fuzzyIP ? this.re.link_fuzzy : this.re.link_no_ip_fuzzy)) && (o2 = t2.index + t2[1].length, (this.__index__ < 0 || o2 < this.__index__) && (this.__schema__ = "", this.__index__ = o2, this.__last_index__ = t2.index + t2[0].length)), this.__opts__.fuzzyEmail && this.__compiled__["mailto:"] && e2.indexOf("@") >= 0 && null !== (n2 = e2.match(this.re.email_fuzzy)) && (o2 = n2.index + n2[1].length, i2 = n2.index + n2[0].length, (this.__index__ < 0 || o2 < this.__index__ || o2 === this.__index__ && i2 > this.__last_index__) && (this.__schema__ = "mailto:", this.__index__ = o2, this.__last_index__ = i2)), this.__index__ >= 0;
}, Fr.prototype.pretest = function(e2) {
  return this.re.pretest.test(e2);
}, Fr.prototype.testSchemaAt = function(e2, r2, t2) {
  return this.__compiled__[r2.toLowerCase()] ? this.__compiled__[r2.toLowerCase()].validate(e2, t2, this) : 0;
}, Fr.prototype.match = function(e2) {
  var r2 = 0, t2 = [];
  this.__index__ >= 0 && this.__text_cache__ === e2 && (t2.push(Sr(this, r2)), r2 = this.__last_index__);
  for (var n2 = r2 ? e2.slice(r2) : e2; this.test(n2); )
    t2.push(Sr(this, r2)), n2 = n2.slice(this.__last_index__), r2 += this.__last_index__;
  return t2.length ? t2 : null;
}, Fr.prototype.matchAtStart = function(e2) {
  if (this.__text_cache__ = e2, this.__index__ = -1, !e2.length)
    return null;
  var r2 = this.re.schema_at_start.exec(e2);
  if (!r2)
    return null;
  var t2 = this.testSchemaAt(e2, r2[2], r2[0].length);
  return t2 ? (this.__schema__ = r2[2], this.__index__ = r2.index + r2[1].length, this.__last_index__ = r2.index + r2[0].length + t2, Sr(this, 0)) : null;
}, Fr.prototype.tlds = function(e2, r2) {
  return e2 = Array.isArray(e2) ? e2 : [e2], r2 ? (this.__tlds__ = this.__tlds__.concat(e2).sort().filter(function(e3, r3, t2) {
    return e3 !== t2[r3 - 1];
  }).reverse(), Er(this), this) : (this.__tlds__ = e2.slice(), this.__tlds_replaced__ = true, Er(this), this);
}, Fr.prototype.normalize = function(e2) {
  e2.schema || (e2.url = "http://" + e2.url), "mailto:" !== e2.schema || /^mailto:/i.test(e2.url) || (e2.url = "mailto:" + e2.url);
}, Fr.prototype.onCompile = function() {
};
var Lr = Fr, zr = 2147483647, Tr = /^xn--/, Ir = /[^\x20-\x7E]/, Mr = /[\x2E\u3002\uFF0E\uFF61]/g, Rr = { overflow: "Overflow: input needs wider integers to process", "not-basic": "Illegal input >= 0x80 (not a basic code point)", "invalid-input": "Invalid input" }, Br = Math.floor, Nr = String.fromCharCode;
/*! https://mths.be/punycode v1.4.1 by @mathias */
function Or(e2) {
  throw new RangeError(Rr[e2]);
}
function Pr(e2, r2) {
  for (var t2 = e2.length, n2 = []; t2--; )
    n2[t2] = r2(e2[t2]);
  return n2;
}
function jr(e2, r2) {
  var t2 = e2.split("@"), n2 = "";
  return t2.length > 1 && (n2 = t2[0] + "@", e2 = t2[1]), n2 + Pr((e2 = e2.replace(Mr, ".")).split("."), r2).join(".");
}
function Ur(e2) {
  for (var r2, t2, n2 = [], s2 = 0, o2 = e2.length; s2 < o2; )
    (r2 = e2.charCodeAt(s2++)) >= 55296 && r2 <= 56319 && s2 < o2 ? 56320 == (64512 & (t2 = e2.charCodeAt(s2++))) ? n2.push(((1023 & r2) << 10) + (1023 & t2) + 65536) : (n2.push(r2), s2--) : n2.push(r2);
  return n2;
}
function Vr(e2) {
  return Pr(e2, function(e3) {
    var r2 = "";
    return e3 > 65535 && (r2 += Nr((e3 -= 65536) >>> 10 & 1023 | 55296), e3 = 56320 | 1023 & e3), r2 += Nr(e3);
  }).join("");
}
function Zr(e2, r2) {
  return e2 + 22 + 75 * (e2 < 26) - ((0 != r2) << 5);
}
function $r(e2, r2, t2) {
  var n2 = 0;
  for (e2 = t2 ? Br(e2 / 700) : e2 >> 1, e2 += Br(e2 / r2); e2 > 455; n2 += 36)
    e2 = Br(e2 / 35);
  return Br(n2 + 36 * e2 / (e2 + 38));
}
function Gr(e2) {
  var r2, t2, n2, s2, o2, i2, a2, c2, l2, u2, p2, h2 = [], f2 = e2.length, d2 = 0, m2 = 128, g2 = 72;
  for ((t2 = e2.lastIndexOf("-")) < 0 && (t2 = 0), n2 = 0; n2 < t2; ++n2)
    e2.charCodeAt(n2) >= 128 && Or("not-basic"), h2.push(e2.charCodeAt(n2));
  for (s2 = t2 > 0 ? t2 + 1 : 0; s2 < f2; ) {
    for (o2 = d2, i2 = 1, a2 = 36; s2 >= f2 && Or("invalid-input"), ((c2 = (p2 = e2.charCodeAt(s2++)) - 48 < 10 ? p2 - 22 : p2 - 65 < 26 ? p2 - 65 : p2 - 97 < 26 ? p2 - 97 : 36) >= 36 || c2 > Br((zr - d2) / i2)) && Or("overflow"), d2 += c2 * i2, !(c2 < (l2 = a2 <= g2 ? 1 : a2 >= g2 + 26 ? 26 : a2 - g2)); a2 += 36)
      i2 > Br(zr / (u2 = 36 - l2)) && Or("overflow"), i2 *= u2;
    g2 = $r(d2 - o2, r2 = h2.length + 1, 0 == o2), Br(d2 / r2) > zr - m2 && Or("overflow"), m2 += Br(d2 / r2), d2 %= r2, h2.splice(d2++, 0, m2);
  }
  return Vr(h2);
}
function Hr(e2) {
  var r2, t2, n2, s2, o2, i2, a2, c2, l2, u2, p2, h2, f2, d2, m2, g2 = [];
  for (h2 = (e2 = Ur(e2)).length, r2 = 128, t2 = 0, o2 = 72, i2 = 0; i2 < h2; ++i2)
    (p2 = e2[i2]) < 128 && g2.push(Nr(p2));
  for (n2 = s2 = g2.length, s2 && g2.push("-"); n2 < h2; ) {
    for (a2 = zr, i2 = 0; i2 < h2; ++i2)
      (p2 = e2[i2]) >= r2 && p2 < a2 && (a2 = p2);
    for (a2 - r2 > Br((zr - t2) / (f2 = n2 + 1)) && Or("overflow"), t2 += (a2 - r2) * f2, r2 = a2, i2 = 0; i2 < h2; ++i2)
      if ((p2 = e2[i2]) < r2 && ++t2 > zr && Or("overflow"), p2 == r2) {
        for (c2 = t2, l2 = 36; !(c2 < (u2 = l2 <= o2 ? 1 : l2 >= o2 + 26 ? 26 : l2 - o2)); l2 += 36)
          m2 = c2 - u2, d2 = 36 - u2, g2.push(Nr(Zr(u2 + m2 % d2, 0))), c2 = Br(m2 / d2);
        g2.push(Nr(Zr(c2, 0))), o2 = $r(t2, f2, n2 == s2), t2 = 0, ++n2;
      }
    ++t2, ++r2;
  }
  return g2.join("");
}
function Jr(e2) {
  return jr(e2, function(e3) {
    return Tr.test(e3) ? Gr(e3.slice(4).toLowerCase()) : e3;
  });
}
function Wr(e2) {
  return jr(e2, function(e3) {
    return Ir.test(e3) ? "xn--" + Hr(e3) : e3;
  });
}
var Yr = { decode: Ur, encode: Vr }, Kr = { version: "1.4.1", ucs2: Yr, toASCII: Wr, toUnicode: Jr, encode: Hr, decode: Gr }, Qr = r, Xr = q, et = R, rt = pe, tt = Ne, nt = br, st = Lr, ot = s, it = e(Object.freeze({ __proto__: null, decode: Gr, encode: Hr, toUnicode: Jr, toASCII: Wr, version: "1.4.1", ucs2: Yr, default: Kr })), at = { default: { options: { html: false, xhtmlOut: false, breaks: false, langPrefix: "language-", linkify: false, typographer: false, quotes: "“”‘’", highlight: null, maxNesting: 100 }, components: { core: {}, block: {}, inline: {} } }, zero: { options: { html: false, xhtmlOut: false, breaks: false, langPrefix: "language-", linkify: false, typographer: false, quotes: "“”‘’", highlight: null, maxNesting: 20 }, components: { core: { rules: ["normalize", "block", "inline", "text_join"] }, block: { rules: ["paragraph"] }, inline: { rules: ["text"], rules2: ["balance_pairs", "fragments_join"] } } }, commonmark: { options: { html: true, xhtmlOut: true, breaks: false, langPrefix: "language-", linkify: false, typographer: false, quotes: "“”‘’", highlight: null, maxNesting: 20 }, components: { core: { rules: ["normalize", "block", "inline", "text_join"] }, block: { rules: ["blockquote", "code", "fence", "heading", "hr", "html_block", "lheading", "list", "reference", "paragraph"] }, inline: { rules: ["autolink", "backticks", "emphasis", "entity", "escape", "html_inline", "image", "link", "newline", "text"], rules2: ["balance_pairs", "emphasis", "fragments_join"] } } } }, ct = /^(vbscript|javascript|file|data):/, lt = /^data:image\/(gif|png|jpeg|webp);/;
function ut(e2) {
  var r2 = e2.trim().toLowerCase();
  return !ct.test(r2) || !!lt.test(r2);
}
var pt = ["http:", "https:", "mailto:"];
function ht(e2) {
  var r2 = ot.parse(e2, true);
  if (r2.hostname && (!r2.protocol || pt.indexOf(r2.protocol) >= 0))
    try {
      r2.hostname = it.toASCII(r2.hostname);
    } catch (e3) {
    }
  return ot.encode(ot.format(r2));
}
function ft(e2) {
  var r2 = ot.parse(e2, true);
  if (r2.hostname && (!r2.protocol || pt.indexOf(r2.protocol) >= 0))
    try {
      r2.hostname = it.toUnicode(r2.hostname);
    } catch (e3) {
    }
  return ot.decode(ot.format(r2), ot.decode.defaultChars + "%");
}
function dt(e2, r2) {
  if (!(this instanceof dt))
    return new dt(e2, r2);
  r2 || Qr.isString(e2) || (r2 = e2 || {}, e2 = "default"), this.inline = new nt(), this.block = new tt(), this.core = new rt(), this.renderer = new et(), this.linkify = new st(), this.validateLink = ut, this.normalizeLink = ht, this.normalizeLinkText = ft, this.utils = Qr, this.helpers = Qr.assign({}, Xr), this.options = {}, this.configure(e2), r2 && this.set(r2);
}
dt.prototype.set = function(e2) {
  return Qr.assign(this.options, e2), this;
}, dt.prototype.configure = function(e2) {
  var r2, t2 = this;
  if (Qr.isString(e2) && !(e2 = at[r2 = e2]))
    throw new Error('Wrong `markdown-it` preset "' + r2 + '", check name');
  if (!e2)
    throw new Error("Wrong `markdown-it` preset, can't be empty");
  return e2.options && t2.set(e2.options), e2.components && Object.keys(e2.components).forEach(function(r3) {
    e2.components[r3].rules && t2[r3].ruler.enableOnly(e2.components[r3].rules), e2.components[r3].rules2 && t2[r3].ruler2.enableOnly(e2.components[r3].rules2);
  }), this;
}, dt.prototype.enable = function(e2, r2) {
  var t2 = [];
  Array.isArray(e2) || (e2 = [e2]), ["core", "block", "inline"].forEach(function(r3) {
    t2 = t2.concat(this[r3].ruler.enable(e2, true));
  }, this), t2 = t2.concat(this.inline.ruler2.enable(e2, true));
  var n2 = e2.filter(function(e3) {
    return t2.indexOf(e3) < 0;
  });
  if (n2.length && !r2)
    throw new Error("MarkdownIt. Failed to enable unknown rule(s): " + n2);
  return this;
}, dt.prototype.disable = function(e2, r2) {
  var t2 = [];
  Array.isArray(e2) || (e2 = [e2]), ["core", "block", "inline"].forEach(function(r3) {
    t2 = t2.concat(this[r3].ruler.disable(e2, true));
  }, this), t2 = t2.concat(this.inline.ruler2.disable(e2, true));
  var n2 = e2.filter(function(e3) {
    return t2.indexOf(e3) < 0;
  });
  if (n2.length && !r2)
    throw new Error("MarkdownIt. Failed to disable unknown rule(s): " + n2);
  return this;
}, dt.prototype.use = function(e2) {
  var r2 = [this].concat(Array.prototype.slice.call(arguments, 1));
  return e2.apply(e2, r2), this;
}, dt.prototype.parse = function(e2, r2) {
  if ("string" != typeof e2)
    throw new Error("Input data should be a String");
  var t2 = new this.core.State(e2, this, r2);
  return this.core.process(t2), t2.tokens;
}, dt.prototype.render = function(e2, r2) {
  return r2 = r2 || {}, this.renderer.render(this.parse(e2, r2), this.options, r2);
}, dt.prototype.parseInline = function(e2, r2) {
  var t2 = new this.core.State(e2, this, r2);
  return t2.inlineMode = true, this.core.process(t2), t2.tokens;
}, dt.prototype.renderInline = function(e2, r2) {
  return r2 = r2 || {}, this.renderer.render(this.parseInline(e2, r2), this.options, r2);
};
var mt = dt;
exports.mt = mt;
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/components/ua-markdown/lib/markdown-it.min.js.map
