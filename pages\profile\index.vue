<template>
  <view class="profile-edit-page">
    <!-- 头像区域 -->
    <view class="avatar-section">
      <view class="avatar-container" @tap="chooseAvatar">
        <image :src="userForm.headImgUrl" class="avatar" mode="aspectFill" />
        <view class="avatar-overlay">
          <image src="@/static/msg/<EMAIL>" class="camera-icon" mode="aspectFit" />
        </view>
      </view>
    </view>

    <!-- 表单区域 -->
    <view class="form-section">
      <!-- 昵称 -->
      <view class="form-item">
        <text class="label">昵称:</text>
        <input v-model="userForm.nickname" class="input" placeholder="输入" placeholder-class="placeholder"
          maxlength="20" />
      </view>

      <!-- 邮箱 -->
      <view class="form-item">
        <text class="label">邮箱:</text>
        <input v-model="userForm.email" class="input" placeholder="输入" placeholder-class="placeholder" type="email" />
      </view>
    </view>

    <!-- 保存按钮 -->
    <view class="save-section">
      <view class="save-btn" @tap="handleSave" :class="{ 'disabled': saving }">
        <text class="save-text">{{ saving ? '保存中...' : '保存修改' }}</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useUserStore } from '@/stores/user.js'
import { updateUserInfoApi, getUserInfoApi } from '@/api/index.js'
import { updataFileFun } from '@/api/common.js'

const userStore = useUserStore()

// 表单数据
let userForm = reactive({
  headImgUrl: '',
  nickname: '',
  email: ''
})

const getUserInfo = async () => {
  let res = await getUserInfoApi()
  userForm = Object.assign(userForm, res.data)
}

// 加载状态
const saving = ref(false)
const uploading = ref(false)

// 初始化用户信息
onMounted(() => {
  getUserInfo()
})

// 选择头像
const chooseAvatar = () => {
  if (uploading.value) return

  uni.chooseImage({
    count: 1,
    sizeType: ['compressed'],
    sourceType: ['album', 'camera'],
    success: async (res) => {
      const tempFilePath = res.tempFilePaths[0]
      await uploadAvatar(tempFilePath)
    },
    fail: (err) => {
      console.error('选择图片失败:', err)
    }
  })
}

// 上传头像
const uploadAvatar = async (filePath) => {
  try {
    uploading.value = true
    uni.showLoading({
      title: '上传中...',
      mask: true
    })

    const uploadRes = await updataFileFun(filePath)
    const result = JSON.parse(uploadRes.data)

    if (result.code === 0) {
      userForm.headImgUrl = result.data
      uni.showToast({
        title: '头像上传成功',
        icon: 'success'
      })
    } else {
      throw new Error(result.msg || '上传失败')
    }
  } catch (error) {
    console.error('上传头像失败:', error)
    uni.showToast({
      title: '上传失败',
      icon: 'none'
    })
  } finally {
    uploading.value = false
    uni.hideLoading()
  }
}

// 保存修改
const handleSave = async () => {
  if (saving.value) return

  // 表单验证
  if (!userForm.nickname.trim()) {
    uni.showToast({
      title: '请输入昵称',
      icon: 'none'
    })
    return
  }

  if (userForm.email && !isValidEmail(userForm.email)) {
    uni.showToast({
      title: '请输入正确的邮箱格式',
      icon: 'none'
    })
    return
  }

  try {
    saving.value = true
    uni.showLoading({
      title: '保存中...',
      mask: true
    })

    const updateData = {
      headImgUrl: userForm.headImgUrl,
      nickname: userForm.nickname.trim(),
      email: userForm.email.trim()
    }

    const res = await updateUserInfoApi(updateData)

    if (res.code === 0) {
      // 更新本地用户信息
      const updatedUserInfo = { ...userStore.userInfo, ...updateData }
      userStore.set_user_info(updatedUserInfo)

      uni.showToast({
        title: '保存成功',
        icon: 'success'
      })

      // 延迟返回上一页
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    } else {
      throw new Error(res.msg || '保存失败')
    }
  } catch (error) {
    console.error('保存失败:', error)
    uni.showToast({
      title: error.message || '保存失败',
      icon: 'none'
    })
  } finally {
    saving.value = false
    uni.hideLoading()
  }
}

// 邮箱格式验证
const isValidEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}
</script>

<style lang="scss" scoped>
.profile-edit-page {
  background: #F5F5F5;
  min-height: 100vh;
}

.avatar-section {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 80rpx 0 60rpx;
  // background: #ffffff;

  .avatar-container {
    position: relative;
    width: 200rpx;
    height: 200rpx;

    .avatar {
      width: 100%;
      height: 100%;
      border-radius: 50%;
      background: #f0f0f0;
    }

    .avatar-overlay {
      position: absolute;
      bottom: 0;
      right: 0;
      width: 60rpx;
      height: 60rpx;
      background: #3478f6;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 4rpx solid #ffffff;

      .camera-icon {
        width: 32rpx;
        height: 32rpx;
      }
    }
  }
}

.form-section {
  margin-top: 20rpx;
  padding: 0 32rpx;

  .form-item {
    display: flex;
    align-items: center;
    padding: 40rpx 16rpx;
    background: #ffffff;
    border-radius: 20rpx;
    margin-bottom: 20px;

    &:last-child {
      border-bottom: none;
    }

    .label {
      font-size: 30rpx;
      color: #1a1a1a;
      font-weight: 500;
      width: 120rpx;
      flex-shrink: 0;
    }

    .input {
      flex: 1;
      font-size: 28rpx;
      color: #1a1a1a;
      margin-left: 24rpx;
      height: 44rpx;
      line-height: 44rpx;

      &.placeholder {
        color: #CCCCCC;
      }
    }
  }
}

.save-section {
  padding: 80rpx 32rpx;
  padding-bottom: calc(80rpx + env(safe-area-inset-bottom));

  .save-btn {
    width: 100%;
    height: 96rpx;
    background: #3478f6;
    border-radius: 48rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;

    &.disabled {
      background: #CCCCCC;
    }

    .save-text {
      font-size: 32rpx;
      color: #ffffff;
      font-weight: 600;
    }
  }
}

/* 占位符样式 */
.placeholder {
  color: #CCCCCC !important;
}
</style>