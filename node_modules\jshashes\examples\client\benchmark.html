<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>jsHashes - Benchmark example</title>
<script type="text/javascript" src="../../hashes.js"></script>
<script type="text/javascript">

	function randomString(string_length) {
		var chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXTZabcdefghiklmnopqrstuvwxyz";
		string_length = string_length || 50;
		var randomstring = '';
		for (var i=0; i<string_length; i++) {
			var rnum = Math.floor(Math.random() * chars.length);
			randomstring += chars.substring(rnum,rnum+1);
		}
		return randomstring;
	}

	// new MD5 instance
	var MD5 = new Hashes.MD5;
	// new SHA1 instance
	var SHA1 = new Hashes.SHA1;
	// new SHA256 instance
	var SHA256 =  new Hashes.SHA256;
	// new SHA512 instace
	var SHA512 = new Hashes.SHA512;
	// new RIPEMD160 instace
	var RMD160 = new Hashes.RMD160;

	var num = 10000;
	var str = randomString(100);

	// output into DOM
	document.write('<h2>jsHashes</h2>');
	document.write('<h3>Simple benchmark test generating ' + num + ' hashes for each algorithm.</h3>');
	document.write('String: "' + str + '"');

	document.write('<h2>MD5</h2>');
	var time = new Date().getTime();
	for (var i=0; i<num; i++) {
		MD5.hex(str);
	}
	document.write('<b>Done in: ' + Math.round( new Date().getTime() - time ) + ' miliseconds</b>');

	document.write('<h2>SHA1</h2>');
	time = new Date().getTime();
	for (var i=0; i<num; i++) {
		SHA1.hex(str);
	}
	document.write('<b>Done in: ' + Math.round( new Date().getTime() - time ) + ' miliseconds</b>');

	document.write('<h2>SHA256</h2>');
	time =  new Date().getTime();
	for (var i=0; i<num; i++) {
		SHA256.hex(str);
	}
	document.write('<b>Done in: ' + Math.round( new Date().getTime() - time ) + ' miliseconds</b>');

	document.write('<h2>SHA512</h2>');
	time = new Date().getTime();
	for (var i=0; i<num; i++) {
		SHA512.hex(str);
	}
	document.write('<b>Done in: ' + Math.round( new Date().getTime() - time ) + ' miliseconds</b>');

	document.write('<h2>RMD160</h2>');
	time =  new Date().getTime();
	for (var i=0; i<num; i++) {
		RMD160.hex(str);
	}
	document.write('<b>Done in: ' + Math.round( new Date().getTime() - time ) + ' miliseconds</b>');

</script>
</head>
<body>
</body>
</html>