{"version": 3, "file": "index.js", "sources": ["pages/profile/index.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvcHJvZmlsZS9pbmRleC52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"profile-edit-page\">\r\n    <!-- 头像区域 -->\r\n    <view class=\"avatar-section\">\r\n      <view class=\"avatar-container\" @tap=\"chooseAvatar\">\r\n        <image :src=\"userForm.headImgUrl\" class=\"avatar\" mode=\"aspectFill\" />\r\n        <view class=\"avatar-overlay\">\r\n          <image src=\"@/static/msg/<EMAIL>\" class=\"camera-icon\" mode=\"aspectFit\" />\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 表单区域 -->\r\n    <view class=\"form-section\">\r\n      <!-- 昵称 -->\r\n      <view class=\"form-item\">\r\n        <text class=\"label\">昵称:</text>\r\n        <input v-model=\"userForm.nickname\" class=\"input\" placeholder=\"输入\" placeholder-class=\"placeholder\"\r\n          maxlength=\"20\" />\r\n      </view>\r\n\r\n      <!-- 邮箱 -->\r\n      <view class=\"form-item\">\r\n        <text class=\"label\">邮箱:</text>\r\n        <input v-model=\"userForm.email\" class=\"input\" placeholder=\"输入\" placeholder-class=\"placeholder\" type=\"email\" />\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 保存按钮 -->\r\n    <view class=\"save-section\">\r\n      <view class=\"save-btn\" @tap=\"handleSave\" :class=\"{ 'disabled': saving }\">\r\n        <text class=\"save-text\">{{ saving ? '保存中...' : '保存修改' }}</text>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, reactive, onMounted } from 'vue'\r\nimport { useUserStore } from '@/stores/user.js'\r\nimport { updateUserInfoApi, getUserInfoApi } from '@/api/index.js'\r\nimport { updataFileFun } from '@/api/common.js'\r\n\r\nconst userStore = useUserStore()\r\n\r\n// 表单数据\r\nlet userForm = reactive({\r\n  headImgUrl: '',\r\n  nickname: '',\r\n  email: ''\r\n})\r\n\r\nconst getUserInfo = async () => {\r\n  let res = await getUserInfoApi()\r\n  userForm = Object.assign(userForm, res.data)\r\n}\r\n\r\n// 加载状态\r\nconst saving = ref(false)\r\nconst uploading = ref(false)\r\n\r\n// 初始化用户信息\r\nonMounted(() => {\r\n  getUserInfo()\r\n})\r\n\r\n// 选择头像\r\nconst chooseAvatar = () => {\r\n  if (uploading.value) return\r\n\r\n  uni.chooseImage({\r\n    count: 1,\r\n    sizeType: ['compressed'],\r\n    sourceType: ['album', 'camera'],\r\n    success: async (res) => {\r\n      const tempFilePath = res.tempFilePaths[0]\r\n      await uploadAvatar(tempFilePath)\r\n    },\r\n    fail: (err) => {\r\n      console.error('选择图片失败:', err)\r\n    }\r\n  })\r\n}\r\n\r\n// 上传头像\r\nconst uploadAvatar = async (filePath) => {\r\n  try {\r\n    uploading.value = true\r\n    uni.showLoading({\r\n      title: '上传中...',\r\n      mask: true\r\n    })\r\n\r\n    const uploadRes = await updataFileFun(filePath)\r\n    const result = JSON.parse(uploadRes.data)\r\n\r\n    if (result.code === 0) {\r\n      userForm.headImgUrl = result.data\r\n      uni.showToast({\r\n        title: '头像上传成功',\r\n        icon: 'success'\r\n      })\r\n    } else {\r\n      throw new Error(result.msg || '上传失败')\r\n    }\r\n  } catch (error) {\r\n    console.error('上传头像失败:', error)\r\n    uni.showToast({\r\n      title: '上传失败',\r\n      icon: 'none'\r\n    })\r\n  } finally {\r\n    uploading.value = false\r\n    uni.hideLoading()\r\n  }\r\n}\r\n\r\n// 保存修改\r\nconst handleSave = async () => {\r\n  if (saving.value) return\r\n\r\n  // 表单验证\r\n  if (!userForm.nickname.trim()) {\r\n    uni.showToast({\r\n      title: '请输入昵称',\r\n      icon: 'none'\r\n    })\r\n    return\r\n  }\r\n\r\n  if (userForm.email && !isValidEmail(userForm.email)) {\r\n    uni.showToast({\r\n      title: '请输入正确的邮箱格式',\r\n      icon: 'none'\r\n    })\r\n    return\r\n  }\r\n\r\n  try {\r\n    saving.value = true\r\n    uni.showLoading({\r\n      title: '保存中...',\r\n      mask: true\r\n    })\r\n\r\n    const updateData = {\r\n      headImgUrl: userForm.headImgUrl,\r\n      nickname: userForm.nickname.trim(),\r\n      email: userForm.email.trim()\r\n    }\r\n\r\n    const res = await updateUserInfoApi(updateData)\r\n\r\n    if (res.code === 0) {\r\n      // 更新本地用户信息\r\n      const updatedUserInfo = { ...userStore.userInfo, ...updateData }\r\n      userStore.set_user_info(updatedUserInfo)\r\n\r\n      uni.showToast({\r\n        title: '保存成功',\r\n        icon: 'success'\r\n      })\r\n\r\n      // 延迟返回上一页\r\n      setTimeout(() => {\r\n        uni.navigateBack()\r\n      }, 1500)\r\n    } else {\r\n      throw new Error(res.msg || '保存失败')\r\n    }\r\n  } catch (error) {\r\n    console.error('保存失败:', error)\r\n    uni.showToast({\r\n      title: error.message || '保存失败',\r\n      icon: 'none'\r\n    })\r\n  } finally {\r\n    saving.value = false\r\n    uni.hideLoading()\r\n  }\r\n}\r\n\r\n// 邮箱格式验证\r\nconst isValidEmail = (email) => {\r\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\r\n  return emailRegex.test(email)\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.profile-edit-page {\r\n  background: #F5F5F5;\r\n  min-height: 100vh;\r\n}\r\n\r\n.avatar-section {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  padding: 80rpx 0 60rpx;\r\n  // background: #ffffff;\r\n\r\n  .avatar-container {\r\n    position: relative;\r\n    width: 200rpx;\r\n    height: 200rpx;\r\n\r\n    .avatar {\r\n      width: 100%;\r\n      height: 100%;\r\n      border-radius: 50%;\r\n      background: #f0f0f0;\r\n    }\r\n\r\n    .avatar-overlay {\r\n      position: absolute;\r\n      bottom: 0;\r\n      right: 0;\r\n      width: 60rpx;\r\n      height: 60rpx;\r\n      background: #3478f6;\r\n      border-radius: 50%;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      border: 4rpx solid #ffffff;\r\n\r\n      .camera-icon {\r\n        width: 32rpx;\r\n        height: 32rpx;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.form-section {\r\n  margin-top: 20rpx;\r\n  padding: 0 32rpx;\r\n\r\n  .form-item {\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 40rpx 16rpx;\r\n    background: #ffffff;\r\n    border-radius: 20rpx;\r\n    margin-bottom: 20px;\r\n\r\n    &:last-child {\r\n      border-bottom: none;\r\n    }\r\n\r\n    .label {\r\n      font-size: 30rpx;\r\n      color: #1a1a1a;\r\n      font-weight: 500;\r\n      width: 120rpx;\r\n      flex-shrink: 0;\r\n    }\r\n\r\n    .input {\r\n      flex: 1;\r\n      font-size: 28rpx;\r\n      color: #1a1a1a;\r\n      margin-left: 24rpx;\r\n      height: 44rpx;\r\n      line-height: 44rpx;\r\n\r\n      &.placeholder {\r\n        color: #CCCCCC;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.save-section {\r\n  padding: 80rpx 32rpx;\r\n  padding-bottom: calc(80rpx + env(safe-area-inset-bottom));\r\n\r\n  .save-btn {\r\n    width: 100%;\r\n    height: 96rpx;\r\n    background: #3478f6;\r\n    border-radius: 48rpx;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    transition: all 0.3s ease;\r\n\r\n    &.disabled {\r\n      background: #CCCCCC;\r\n    }\r\n\r\n    .save-text {\r\n      font-size: 32rpx;\r\n      color: #ffffff;\r\n      font-weight: 600;\r\n    }\r\n  }\r\n}\r\n\r\n/* 占位符样式 */\r\n.placeholder {\r\n  color: #CCCCCC !important;\r\n}\r\n</style>", "import MiniProgramPage from 'E:/youngProject/agent-mini-ui/pages/profile/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useUserStore", "reactive", "getUserInfoApi", "ref", "onMounted", "uni", "updataFileFun", "updateUserInfoApi"], "mappings": ";;;;;;;;;AA2CA,UAAM,YAAYA,YAAAA,aAAc;AAGhC,QAAI,WAAWC,cAAAA,SAAS;AAAA,MACtB,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT,CAAC;AAED,UAAM,cAAc,YAAY;AAC9B,UAAI,MAAM,MAAMC,yBAAgB;AAChC,iBAAW,OAAO,OAAO,UAAU,IAAI,IAAI;AAAA,IAC7C;AAGA,UAAM,SAASC,cAAG,IAAC,KAAK;AACxB,UAAM,YAAYA,cAAG,IAAC,KAAK;AAG3BC,kBAAAA,UAAU,MAAM;AACd,kBAAa;AAAA,IACf,CAAC;AAGD,UAAM,eAAe,MAAM;AACzB,UAAI,UAAU;AAAO;AAErBC,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,QACP,UAAU,CAAC,YAAY;AAAA,QACvB,YAAY,CAAC,SAAS,QAAQ;AAAA,QAC9B,SAAS,OAAO,QAAQ;AACtB,gBAAM,eAAe,IAAI,cAAc,CAAC;AACxC,gBAAM,aAAa,YAAY;AAAA,QAChC;AAAA,QACD,MAAM,CAAC,QAAQ;AACbA,wBAAAA,MAAc,MAAA,SAAA,iCAAA,WAAW,GAAG;AAAA,QAC7B;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,eAAe,OAAO,aAAa;AACvC,UAAI;AACF,kBAAU,QAAQ;AAClBA,sBAAAA,MAAI,YAAY;AAAA,UACd,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAED,cAAM,YAAY,MAAMC,WAAa,cAAC,QAAQ;AAC9C,cAAM,SAAS,KAAK,MAAM,UAAU,IAAI;AAExC,YAAI,OAAO,SAAS,GAAG;AACrB,mBAAS,aAAa,OAAO;AAC7BD,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AAAA,QACP,OAAW;AACL,gBAAM,IAAI,MAAM,OAAO,OAAO,MAAM;AAAA,QACrC;AAAA,MACF,SAAQ,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,kCAAc,WAAW,KAAK;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACL,UAAY;AACR,kBAAU,QAAQ;AAClBA,sBAAAA,MAAI,YAAa;AAAA,MAClB;AAAA,IACH;AAGA,UAAM,aAAa,YAAY;AAC7B,UAAI,OAAO;AAAO;AAGlB,UAAI,CAAC,SAAS,SAAS,QAAQ;AAC7BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAED,UAAI,SAAS,SAAS,CAAC,aAAa,SAAS,KAAK,GAAG;AACnDA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAED,UAAI;AACF,eAAO,QAAQ;AACfA,sBAAAA,MAAI,YAAY;AAAA,UACd,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAED,cAAM,aAAa;AAAA,UACjB,YAAY,SAAS;AAAA,UACrB,UAAU,SAAS,SAAS,KAAM;AAAA,UAClC,OAAO,SAAS,MAAM,KAAM;AAAA,QAC7B;AAED,cAAM,MAAM,MAAME,UAAiB,kBAAC,UAAU;AAE9C,YAAI,IAAI,SAAS,GAAG;AAElB,gBAAM,kBAAkB,EAAE,GAAG,UAAU,UAAU,GAAG,WAAY;AAChE,oBAAU,cAAc,eAAe;AAEvCF,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AAGD,qBAAW,MAAM;AACfA,0BAAAA,MAAI,aAAc;AAAA,UACnB,GAAE,IAAI;AAAA,QACb,OAAW;AACL,gBAAM,IAAI,MAAM,IAAI,OAAO,MAAM;AAAA,QAClC;AAAA,MACF,SAAQ,OAAO;AACdA,sBAAAA,uDAAc,SAAS,KAAK;AAC5BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,MAAM,WAAW;AAAA,UACxB,MAAM;AAAA,QACZ,CAAK;AAAA,MACL,UAAY;AACR,eAAO,QAAQ;AACfA,sBAAAA,MAAI,YAAa;AAAA,MAClB;AAAA,IACH;AAGA,UAAM,eAAe,CAAC,UAAU;AAC9B,YAAM,aAAa;AACnB,aAAO,WAAW,KAAK,KAAK;AAAA,IAC9B;;;;;;;;;;;;;;;;;;ACzLA,GAAG,WAAW,eAAe;"}