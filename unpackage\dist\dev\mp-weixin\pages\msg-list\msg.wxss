/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.container.data-v-59e0655d {
  background-color: #ffffff;
  height: 100vh;
  display: flex;
  flex-direction: column;
}
.page-scroll.data-v-59e0655d {
  flex: 1;
  height: 100%;
}
.chat-list.data-v-59e0655d {
  min-height: calc(100vh - 140rpx);
  /* 减去底部按钮高度 */
}

/* 加载状态样式 */
.loading-container.data-v-59e0655d {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400rpx;
  padding: 60rpx 0;
}
.loading-container .loading-text.data-v-59e0655d {
  font-size: 28rpx;
  color: #999999;
}

/* 空状态样式 */
.empty-container.data-v-59e0655d {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400rpx;
  padding: 120rpx 0;
}
.empty-container .empty-text.data-v-59e0655d {
  font-size: 28rpx;
  color: #999999;
}

/* 加载更多样式 */
.load-more-container.data-v-59e0655d {
  padding: 0;
}
.load-more-container .load-more-item.data-v-59e0655d {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 32rpx 0;
  background-color: #ffffff;
}
.load-more-container .load-more-item .load-more-text.data-v-59e0655d {
  font-size: 26rpx;
  color: #999999;
}
.chat-item.data-v-59e0655d {
  display: flex;
  align-items: center;
  padding: 16px;
  background-color: #ffffff;
  border-bottom: 1px solid #f5f5f5;
}
.chat-item.selected.data-v-59e0655d {
  background-color: #f0f0f0;
}
.chat-item .avatar.data-v-59e0655d {
  width: 50px;
  height: 50px;
  margin-right: 12px;
  flex-shrink: 0;
}
.chat-item .avatar .avatar-img.data-v-59e0655d {
  width: 100%;
  height: 100%;
  border-radius: 25px;
}
.chat-item .content.data-v-59e0655d {
  flex: 1;
  width: 530rpx;
}
.chat-item .content .name.data-v-59e0655d {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}
.chat-item .content .message.data-v-59e0655d {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.chat-item .badge.data-v-59e0655d {
  width: 60rpx;
  height: 100%;
}
.create-btn-container.data-v-59e0655d {
  position: fixed;
  width: 100%;
  bottom: 30px;
  left: 0;
  display: flex;
  justify-content: center;
  margin-top: 20rpx;
}
.create-btn-container .create-btn.data-v-59e0655d {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 300rpx;
  height: 90rpx;
  background: #3478f6;
  border-radius: 48rpx;
  border: none;
}
.create-btn-container .create-btn .plus-icon.data-v-59e0655d {
  width: 32rpx;
  height: 32rpx;
  margin-right: 8px;
}
.create-btn-container .create-btn .create-text.data-v-59e0655d {
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
}

/* 编辑弹窗样式 */
.modal-overlay.data-v-59e0655d {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}
.edit-modal.data-v-59e0655d {
  background-color: #ffffff;
  border-radius: 12px;
  width: 320px;
  padding: 24px;
  box-sizing: border-box;
}
.edit-modal .modal-title.data-v-59e0655d {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  text-align: center;
  margin-bottom: 24px;
}
.edit-modal .input-container.data-v-59e0655d {
  margin-bottom: 32px;
}
.edit-modal .input-container .edit-input.data-v-59e0655d {
  width: 100%;
  height: 44px;
  background-color: #F8F9FA;
  border-radius: 8px;
  padding: 0 16px;
  font-size: 16px;
  color: #333;
  border: none;
  box-sizing: border-box;
}
.edit-modal .input-container .edit-input.data-v-59e0655d::-webkit-input-placeholder {
  color: #999;
}
.edit-modal .input-container .edit-input.data-v-59e0655d::placeholder {
  color: #999;
}
.edit-modal .modal-buttons.data-v-59e0655d {
  display: flex;
  gap: 12px;
}
.edit-modal .modal-buttons .cancel-btn.data-v-59e0655d,
.edit-modal .modal-buttons .confirm-btn.data-v-59e0655d {
  flex: 1;
  height: 44px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 500;
}
.edit-modal .modal-buttons .cancel-btn.data-v-59e0655d {
  background-color: #F8F9FA;
  color: #666;
}
.edit-modal .modal-buttons .confirm-btn.data-v-59e0655d {
  background-color: #5A7BF7;
  color: #fff;
}