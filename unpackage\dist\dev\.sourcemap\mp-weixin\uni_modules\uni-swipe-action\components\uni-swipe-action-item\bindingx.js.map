{"version": 3, "file": "bindingx.js", "sources": ["uni_modules/uni-swipe-action/components/uni-swipe-action-item/bindingx.js"], "sourcesContent": ["let bindIngXMixins = {}\r\n\r\n// #ifdef APP-NVUE\r\nconst BindingX = uni.requireNativePlugin('bindingx');\r\nconst dom = uni.requireNativePlugin('dom');\r\nconst animation = uni.requireNativePlugin('animation');\r\n\r\nbindIngXMixins = {\r\n\tdata() {\r\n\t\treturn {}\r\n\t},\r\n\r\n\twatch: {\r\n\t\tshow(newVal) {\r\n\t\t\tif (this.autoClose) return\r\n\t\t\tif (this.stop) return\r\n\t\t\tthis.stop = true\r\n\t\t\tif (newVal) {\r\n\t\t\t\tthis.open(newVal)\r\n\t\t\t} else {\r\n\t\t\t\tthis.close()\r\n\t\t\t}\r\n\t\t},\r\n\t\tleftOptions() {\r\n\t\t\tthis.getSelectorQuery()\r\n\t\t\tthis.init()\r\n\t\t},\r\n\t\trightOptions(newVal) {\r\n\t\t\tthis.init()\r\n\t\t}\r\n\t},\r\n\tcreated() {\r\n\t\tthis.swipeaction = this.getSwipeAction()\r\n\t\tif (this.swipeaction && Array.isArray(this.swipeaction.children)) {\r\n\t\t\tthis.swipeaction.children.push(this)\r\n\t\t}\r\n\t},\r\n\tmounted() {\r\n\t\tthis.box = this.getEl(this.$refs['selector-box--hock'])\r\n\t\tthis.selector = this.getEl(this.$refs['selector-content--hock']);\r\n\t\tthis.leftButton = this.getEl(this.$refs['selector-left-button--hock']);\r\n\t\tthis.rightButton = this.getEl(this.$refs['selector-right-button--hock']);\r\n\t\tthis.init()\r\n\t},\r\n\t// beforeDestroy() {\r\n\t// \tthis.swipeaction.children.forEach((item, index) => {\r\n\t// \t\tif (item === this) {\r\n\t// \t\t\tthis.swipeaction.children.splice(index, 1)\r\n\t// \t\t}\r\n\t// \t})\r\n\t// },\r\n\tmethods: {\r\n\t\tinit() {\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\tthis.x = 0\r\n\t\t\t\tthis.button = {\r\n\t\t\t\t\tshow: false\r\n\t\t\t\t}\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tthis.getSelectorQuery()\r\n\t\t\t\t}, 200)\r\n\t\t\t})\r\n\t\t},\r\n\t\tonClick(index, item, position) {\r\n\t\t\tthis.$emit('click', {\r\n\t\t\t\tcontent: item,\r\n\t\t\t\tindex,\r\n\t\t\t\tposition\r\n\t\t\t})\r\n\t\t},\r\n\t\ttouchstart(e) {\r\n\t\t\t// fix by mehaotian 禁止滑动\r\n\t\t\tif (this.disabled) return\r\n\t\t\t// 每次只触发一次，避免多次监听造成闪烁\r\n\t\t\tif (this.stop) return\r\n\t\t\tthis.stop = true\r\n\t\t\tif (this.autoClose && this.swipeaction) {\r\n\t\t\t\tthis.swipeaction.closeOther(this)\r\n\t\t\t}\r\n\r\n\t\t\tconst leftWidth = this.button.left.width\r\n\t\t\tconst rightWidth = this.button.right.width\r\n\t\t\tlet expression = this.range(this.x, -rightWidth, leftWidth)\r\n\t\t\tlet leftExpression = this.range(this.x - leftWidth, -leftWidth, 0)\r\n\t\t\tlet rightExpression = this.range(this.x + rightWidth, 0, rightWidth)\r\n\r\n\t\t\tthis.eventpan = BindingX.bind({\r\n\t\t\t\tanchor: this.box,\r\n\t\t\t\teventType: 'pan',\r\n\t\t\t\tprops: [{\r\n\t\t\t\t\telement: this.selector,\r\n\t\t\t\t\tproperty: 'transform.translateX',\r\n\t\t\t\t\texpression\r\n\t\t\t\t}, {\r\n\t\t\t\t\telement: this.leftButton,\r\n\t\t\t\t\tproperty: 'transform.translateX',\r\n\t\t\t\t\texpression: leftExpression\r\n\t\t\t\t}, {\r\n\t\t\t\t\telement: this.rightButton,\r\n\t\t\t\t\tproperty: 'transform.translateX',\r\n\t\t\t\t\texpression: rightExpression\r\n\t\t\t\t}, ]\r\n\t\t\t}, (e) => {\r\n\t\t\t\t// nope\r\n\t\t\t\tif (e.state === 'end') {\r\n\t\t\t\t\tthis.x = e.deltaX + this.x;\r\n\t\t\t\t\tthis.isclick = true\r\n\t\t\t\t\tthis.bindTiming(e.deltaX)\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\ttouchend(e) {\r\n\t\t\tif (this.isopen !== 'none' && !this.isclick) {\r\n\t\t\t\tthis.open('none')\r\n\t\t\t}\r\n\t\t},\r\n\t\tbindTiming(x) {\r\n\t\t\tconst left = this.x\r\n\t\t\tconst leftWidth = this.button.left.width\r\n\t\t\tconst rightWidth = this.button.right.width\r\n\t\t\tconst threshold = this.threshold\r\n\t\t\tif (!this.isopen || this.isopen === 'none') {\r\n\t\t\t\tif (left > threshold) {\r\n\t\t\t\t\tthis.open('left')\r\n\t\t\t\t} else if (left < -threshold) {\r\n\t\t\t\t\tthis.open('right')\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.open('none')\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\tif ((x > -leftWidth && x < 0) || x > rightWidth) {\r\n\t\t\t\t\tif ((x > -threshold && x < 0) || (x - rightWidth > threshold)) {\r\n\t\t\t\t\t\tthis.open('left')\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.open('none')\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tif ((x < threshold && x > 0) || (x + leftWidth < -threshold)) {\r\n\t\t\t\t\t\tthis.open('right')\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.open('none')\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t * 移动范围\r\n\t\t * @param {Object} num\r\n\t\t * @param {Object} mix\r\n\t\t * @param {Object} max\r\n\t\t */\r\n\t\trange(num, mix, max) {\r\n\t\t\treturn `min(max(x+${num}, ${mix}), ${max})`\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t * 开启swipe\r\n\t\t */\r\n\t\topen(type) {\r\n\t\t\tthis.animation(type)\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t * 关闭swipe\r\n\t\t */\r\n\t\tclose() {\r\n\t\t\tthis.animation('none')\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t * 开启关闭动画\r\n\t\t * @param {Object} type\r\n\t\t */\r\n\t\tanimation(type) {\r\n\t\t\tconst time = 300\r\n\t\t\tconst leftWidth = this.button.left.width\r\n\t\t\tconst rightWidth = this.button.right.width\r\n\t\t\tif (this.eventpan && this.eventpan.token) {\r\n\t\t\t\tBindingX.unbind({\r\n\t\t\t\t\ttoken: this.eventpan.token,\r\n\t\t\t\t\teventType: 'pan'\r\n\t\t\t\t})\r\n\t\t\t}\r\n\r\n\t\t\tswitch (type) {\r\n\t\t\t\tcase 'left':\r\n\t\t\t\t\tPromise.all([\r\n\t\t\t\t\t\tthis.move(this.selector, leftWidth),\r\n\t\t\t\t\t\tthis.move(this.leftButton, 0),\r\n\t\t\t\t\t\tthis.move(this.rightButton, rightWidth * 2)\r\n\t\t\t\t\t]).then(() => {\r\n\t\t\t\t\t\tthis.setEmit(leftWidth, type)\r\n\t\t\t\t\t})\r\n\t\t\t\t\tbreak\r\n\t\t\t\tcase 'right':\r\n\t\t\t\t\tPromise.all([\r\n\t\t\t\t\t\tthis.move(this.selector, -rightWidth),\r\n\t\t\t\t\t\tthis.move(this.leftButton, -leftWidth * 2),\r\n\t\t\t\t\t\tthis.move(this.rightButton, 0)\r\n\t\t\t\t\t]).then(() => {\r\n\t\t\t\t\t\tthis.setEmit(-rightWidth, type)\r\n\t\t\t\t\t})\r\n\t\t\t\t\tbreak\r\n\t\t\t\tdefault:\r\n\t\t\t\t\tPromise.all([\r\n\t\t\t\t\t\tthis.move(this.selector, 0),\r\n\t\t\t\t\t\tthis.move(this.leftButton, -leftWidth),\r\n\t\t\t\t\t\tthis.move(this.rightButton, rightWidth)\r\n\t\t\t\t\t]).then(() => {\r\n\t\t\t\t\t\tthis.setEmit(0, type)\r\n\t\t\t\t\t})\r\n\r\n\t\t\t}\r\n\t\t},\r\n\t\tsetEmit(x, type) {\r\n\t\t\tconst leftWidth = this.button.left.width\r\n\t\t\tconst rightWidth = this.button.right.width\r\n\t\t\tthis.isopen = this.isopen || 'none'\r\n\t\t\tthis.stop = false\r\n\t\t\tthis.isclick = false\r\n\t\t\t// 只有状态不一致才会返回结果\r\n\t\t\tif (this.isopen !== type && this.x !== x) {\r\n\t\t\t\tif (type === 'left' && leftWidth > 0) {\r\n\t\t\t\t\tthis.$emit('change', 'left')\r\n\t\t\t\t}\r\n\t\t\t\tif (type === 'right' && rightWidth > 0) {\r\n\t\t\t\t\tthis.$emit('change', 'right')\r\n\t\t\t\t}\r\n\t\t\t\tif (type === 'none') {\r\n\t\t\t\t\tthis.$emit('change', 'none')\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tthis.x = x\r\n\t\t\tthis.isopen = type\r\n\t\t},\r\n\t\tmove(ref, value) {\r\n\t\t\treturn new Promise((resolve, reject) => {\r\n\t\t\t\tanimation.transition(ref, {\r\n\t\t\t\t\tstyles: {\r\n\t\t\t\t\t\ttransform: `translateX(${value})`,\r\n\t\t\t\t\t},\r\n\t\t\t\t\tduration: 150, //ms\r\n\t\t\t\t\ttimingFunction: 'linear',\r\n\t\t\t\t\tneedLayout: false,\r\n\t\t\t\t\tdelay: 0 //ms\r\n\t\t\t\t}, function(res) {\r\n\t\t\t\t\tresolve(res)\r\n\t\t\t\t})\r\n\t\t\t})\r\n\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t * 获取ref\r\n\t\t * @param {Object} el\r\n\t\t */\r\n\t\tgetEl(el) {\r\n\t\t\treturn el.ref\r\n\t\t},\r\n\t\t/**\r\n\t\t * 获取节点信息\r\n\t\t */\r\n\t\tgetSelectorQuery() {\r\n\t\t\tPromise.all([\r\n\t\t\t\tthis.getDom('left'),\r\n\t\t\t\tthis.getDom('right'),\r\n\t\t\t]).then((data) => {\r\n\t\t\t\tlet show = 'none'\r\n\t\t\t\tif (this.autoClose) {\r\n\t\t\t\t\tshow = 'none'\r\n\t\t\t\t} else {\r\n\t\t\t\t\tshow = this.show\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (show === 'none') {\r\n\t\t\t\t\t// this.close()\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.open(show)\r\n\t\t\t\t}\r\n\r\n\t\t\t})\r\n\r\n\t\t},\r\n\t\tgetDom(str) {\r\n\t\t\treturn new Promise((resolve, reject) => {\r\n\t\t\t\tdom.getComponentRect(this.$refs[`selector-${str}-button--hock`], (data) => {\r\n\t\t\t\t\tif (data) {\r\n\t\t\t\t\t\tthis.button[str] = data.size\r\n\t\t\t\t\t\tresolve(data)\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\treject()\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t})\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// #endif\r\n\r\nexport default bindIngXMixins\r\n"], "names": [], "mappings": ";AAAG,IAAC,iBAAiB,CAAA;;"}