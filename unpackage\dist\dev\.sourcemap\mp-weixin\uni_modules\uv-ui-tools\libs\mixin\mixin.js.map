{"version": 3, "file": "mixin.js", "sources": ["uni_modules/uv-ui-tools/libs/mixin/mixin.js"], "sourcesContent": ["import * as index from '../function/index.js';\r\nimport * as test from '../function/test.js';\r\nimport route from '../util/route.js';\r\nimport debounce from '../function/debounce.js';\r\nimport throttle from '../function/throttle.js';\r\nexport default {\r\n\t// 定义每个组件都可能需要用到的外部样式以及类名\r\n\tprops: {\r\n\t\t// 每个组件都有的父组件传递的样式，可以为字符串或者对象形式\r\n\t\tcustomStyle: {\r\n\t\t\ttype: [Object, String],\r\n\t\t\tdefault: () => ({})\r\n\t\t},\r\n\t\tcustomClass: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: ''\r\n\t\t},\r\n\t\t// 跳转的页面路径\r\n\t\turl: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: ''\r\n\t\t},\r\n\t\t// 页面跳转的类型\r\n\t\tlinkType: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: 'navigateTo'\r\n\t\t}\r\n\t},\r\n\tdata() {\r\n\t\treturn {}\r\n\t},\r\n\tonLoad() {\r\n\t\t// getRect挂载到$uv上，因为这方法需要使用in(this)，所以无法把它独立成一个单独的文件导出\r\n\t\tthis.$uv.getRect = this.$uvGetRect\r\n\t},\r\n\tcreated() {\r\n\t\t// 组件当中，只有created声明周期，为了能在组件使用，故也在created中将方法挂载到$uv\r\n\t\tthis.$uv.getRect = this.$uvGetRect\r\n\t},\r\n\tcomputed: {\r\n\t\t$uv() {\r\n\t\t\treturn {\r\n\t\t\t\t...index,\r\n\t\t\t\ttest,\r\n\t\t\t\troute,\r\n\t\t\t\tdebounce,\r\n\t\t\t\tthrottle,\r\n\t\t\t\tunit: uni?.$uv?.config?.unit\r\n\t\t\t}\r\n\t\t},\r\n\t\t/**\r\n\t\t * 生成bem规则类名\r\n\t\t * 由于微信小程序，H5，nvue之间绑定class的差异，无法通过:class=\"[bem()]\"的形式进行同用\r\n\t\t * 故采用如下折中做法，最后返回的是数组（一般平台）或字符串（支付宝和字节跳动平台），类似['a', 'b', 'c']或'a b c'的形式\r\n\t\t * @param {String} name 组件名称\r\n\t\t * @param {Array} fixed 一直会存在的类名\r\n\t\t * @param {Array} change 会根据变量值为true或者false而出现或者隐藏的类名\r\n\t\t * @returns {Array|string}\r\n\t\t */\r\n\t\tbem() {\r\n\t\t\treturn function(name, fixed, change) {\r\n\t\t\t\t// 类名前缀\r\n\t\t\t\tconst prefix = `uv-${name}--`\r\n\t\t\t\tconst classes = {}\r\n\t\t\t\tif (fixed) {\r\n\t\t\t\t\tfixed.map((item) => {\r\n\t\t\t\t\t\t// 这里的类名，会一直存在\r\n\t\t\t\t\t\tclasses[prefix + this[item]] = true\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\tif (change) {\r\n\t\t\t\t\tchange.map((item) => {\r\n\t\t\t\t\t\t// 这里的类名，会根据this[item]的值为true或者false，而进行添加或者移除某一个类\r\n\t\t\t\t\t\tthis[item] ? (classes[prefix + item] = this[item]) : (delete classes[prefix + item])\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\treturn Object.keys(classes)\r\n\t\t\t\t\t// 支付宝，头条小程序无法动态绑定一个数组类名，否则解析出来的结果会带有\",\"，而导致失效\r\n\t\t\t\t\t// #ifdef MP-ALIPAY || MP-TOUTIAO || MP-LARK || MP-BAIDU\r\n\t\t\t\t\t.join(' ')\r\n\t\t\t\t// #endif\r\n\t\t\t}\r\n\t\t}\r\n\t},\r\n\tmethods: {\r\n\t\t// 跳转某一个页面\r\n\t\topenPage(urlKey = 'url') {\r\n\t\t\tconst url = this[urlKey]\r\n\t\t\tif (url) {\r\n\t\t\t\t// 执行类似uni.navigateTo的方法\r\n\t\t\t\tuni[this.linkType]({\r\n\t\t\t\t\turl\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 查询节点信息\r\n\t\t// 目前此方法在支付宝小程序中无法获取组件跟接点的尺寸，为支付宝的bug(2020-07-21)\r\n\t\t// 解决办法为在组件根部再套一个没有任何作用的view元素\r\n\t\t$uvGetRect(selector, all) {\r\n\t\t\treturn new Promise((resolve) => {\r\n\t\t\t\tuni.createSelectorQuery()\r\n\t\t\t\t\t.in(this)[all ? 'selectAll' : 'select'](selector)\r\n\t\t\t\t\t.boundingClientRect((rect) => {\r\n\t\t\t\t\t\tif (all && Array.isArray(rect) && rect.length) {\r\n\t\t\t\t\t\t\tresolve(rect)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (!all && rect) {\r\n\t\t\t\t\t\t\tresolve(rect)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.exec()\r\n\t\t\t})\r\n\t\t},\r\n\t\tgetParentData(parentName = '') {\r\n\t\t\t// 避免在created中去定义parent变量\r\n\t\t\tif (!this.parent) this.parent = {}\r\n\t\t\t// 这里的本质原理是，通过获取父组件实例(也即类似uv-radio的父组件uv-radio-group的this)\r\n\t\t\t// 将父组件this中对应的参数，赋值给本组件(uv-radio的this)的parentData对象中对应的属性\r\n\t\t\t// 之所以需要这么做，是因为所有端中，头条小程序不支持通过this.parent.xxx去监听父组件参数的变化\r\n\t\t\t// 此处并不会自动更新子组件的数据，而是依赖父组件uv-radio-group去监听data的变化，手动调用更新子组件的方法去重新获取\r\n\t\t\tthis.parent = this.$uv.$parent.call(this, parentName)\r\n\t\t\tif (this.parent.children) {\r\n\t\t\t\t// 如果父组件的children不存在本组件的实例，才将本实例添加到父组件的children中\r\n\t\t\t\tthis.parent.children.indexOf(this) === -1 && this.parent.children.push(this)\r\n\t\t\t}\r\n\t\t\tif (this.parent && this.parentData) {\r\n\t\t\t\t// 历遍parentData中的属性，将parent中的同名属性赋值给parentData\r\n\t\t\t\tObject.keys(this.parentData).map((key) => {\r\n\t\t\t\t\tthis.parentData[key] = this.parent[key]\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 阻止事件冒泡\r\n\t\tpreventEvent(e) {\r\n\t\t\te && typeof(e.stopPropagation) === 'function' && e.stopPropagation()\r\n\t\t},\r\n\t\t// 空操作\r\n\t\tnoop(e) {\r\n\t\t\tthis.preventEvent(e)\r\n\t\t}\r\n\t},\r\n\tonReachBottom() {\r\n\t\tuni.$emit('uvOnReachBottom')\r\n\t},\r\n\tbeforeDestroy() {\r\n\t\t// 判断当前页面是否存在parent和chldren，一般在checkbox和checkbox-group父子联动的场景会有此情况\r\n\t\t// 组件销毁时，移除子组件在父组件children数组中的实例，释放资源，避免数据混乱\r\n\t\tif (this.parent && test.array(this.parent.children)) {\r\n\t\t\t// 组件销毁时，移除父组件中的children数组中对应的实例\r\n\t\t\tconst childrenList = this.parent.children\r\n\t\t\tchildrenList.map((child, index) => {\r\n\t\t\t\t// 如果相等，则移除\r\n\t\t\t\tif (child === this) {\r\n\t\t\t\t\tchildrenList.splice(index, 1)\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t}\r\n\t},\r\n\t// 兼容vue3\r\n\tunmounted() {\r\n\t\tif (this.parent && test.array(this.parent.children)) {\r\n\t\t\t// 组件销毁时，移除父组件中的children数组中对应的实例\r\n\t\t\tconst childrenList = this.parent.children\r\n\t\t\tchildrenList.map((child, index) => {\r\n\t\t\t\t// 如果相等，则移除\r\n\t\t\t\tif (child === this) {\r\n\t\t\t\t\tchildrenList.splice(index, 1)\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t}\r\n\t}\r\n}"], "names": ["index", "test", "route", "debounce", "throttle", "uni", "test.array"], "mappings": ";;;;;;;AAKA,MAAe,QAAA;AAAA;AAAA,EAEd,OAAO;AAAA;AAAA,IAEN,aAAa;AAAA,MACZ,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,OAAO,CAAA;AAAA,IAChB;AAAA,IACD,aAAa;AAAA,MACZ,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA;AAAA,IAED,KAAK;AAAA,MACJ,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA;AAAA,IAED,UAAU;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA,EACD;AAAA,EACD,OAAO;AACN,WAAO,CAAE;AAAA,EACT;AAAA,EACD,SAAS;AAER,SAAK,IAAI,UAAU,KAAK;AAAA,EACxB;AAAA,EACD,UAAU;AAET,SAAK,IAAI,UAAU,KAAK;AAAA,EACxB;AAAA,EACD,UAAU;AAAA,IACT,MAAM;;AACL,aAAO;AAAA,QACN,GAAGA,0CAAK;AAAA,QACZ,MAAIC,yCAAI;AAAA,QACR,OAAIC,sCAAK;AAAA,QACT,UAAIC,6CAAQ;AAAA,QACZ,UAAIC,6CAAQ;AAAA,QACR,OAAMC,+BAAG,UAAHA,mBAAK,QAALA,mBAAU,WAAVA,mBAAkB;AAAA,MACxB;AAAA,IACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAUD,MAAM;AACL,aAAO,SAAS,MAAM,OAAO,QAAQ;AAEpC,cAAM,SAAS,MAAM,IAAI;AACzB,cAAM,UAAU,CAAE;AAClB,YAAI,OAAO;AACV,gBAAM,IAAI,CAAC,SAAS;AAEnB,oBAAQ,SAAS,KAAK,IAAI,CAAC,IAAI;AAAA,UACrC,CAAM;AAAA,QACD;AACD,YAAI,QAAQ;AACX,iBAAO,IAAI,CAAC,SAAS;AAEpB,iBAAK,IAAI,IAAK,QAAQ,SAAS,IAAI,IAAI,KAAK,IAAI,IAAM,OAAO,QAAQ,SAAS,IAAI;AAAA,UACxF,CAAM;AAAA,QACD;AACD,eAAO,OAAO,KAAK,OAAO;AAAA,MAK1B;AAAA,IACD;AAAA,EACD;AAAA,EACD,SAAS;AAAA;AAAA,IAER,SAAS,SAAS,OAAO;AACxB,YAAM,MAAM,KAAK,MAAM;AACvB,UAAI,KAAK;AAERA,4BAAI,KAAK,QAAQ,EAAE;AAAA,UAClB;AAAA,QACL,CAAK;AAAA,MACD;AAAA,IACD;AAAA;AAAA;AAAA;AAAA,IAID,WAAW,UAAU,KAAK;AACzB,aAAO,IAAI,QAAQ,CAAC,YAAY;AAC/BA,sBAAAA,MAAI,oBAAqB,EACvB,GAAG,IAAI,EAAE,MAAM,cAAc,QAAQ,EAAE,QAAQ,EAC/C,mBAAmB,CAAC,SAAS;AAC7B,cAAI,OAAO,MAAM,QAAQ,IAAI,KAAK,KAAK,QAAQ;AAC9C,oBAAQ,IAAI;AAAA,UACZ;AACD,cAAI,CAAC,OAAO,MAAM;AACjB,oBAAQ,IAAI;AAAA,UACZ;AAAA,QACP,CAAM,EACA,KAAM;AAAA,MACZ,CAAI;AAAA,IACD;AAAA,IACD,cAAc,aAAa,IAAI;AAE9B,UAAI,CAAC,KAAK;AAAQ,aAAK,SAAS,CAAE;AAKlC,WAAK,SAAS,KAAK,IAAI,QAAQ,KAAK,MAAM,UAAU;AACpD,UAAI,KAAK,OAAO,UAAU;AAEzB,aAAK,OAAO,SAAS,QAAQ,IAAI,MAAM,MAAM,KAAK,OAAO,SAAS,KAAK,IAAI;AAAA,MAC3E;AACD,UAAI,KAAK,UAAU,KAAK,YAAY;AAEnC,eAAO,KAAK,KAAK,UAAU,EAAE,IAAI,CAAC,QAAQ;AACzC,eAAK,WAAW,GAAG,IAAI,KAAK,OAAO,GAAG;AAAA,QAC3C,CAAK;AAAA,MACD;AAAA,IACD;AAAA;AAAA,IAED,aAAa,GAAG;AACf,WAAK,OAAO,EAAE,oBAAqB,cAAc,EAAE,gBAAiB;AAAA,IACpE;AAAA;AAAA,IAED,KAAK,GAAG;AACP,WAAK,aAAa,CAAC;AAAA,IACnB;AAAA,EACD;AAAA,EACD,gBAAgB;AACfA,kBAAG,MAAC,MAAM,iBAAiB;AAAA,EAC3B;AAAA,EACD,gBAAgB;AAGf,QAAI,KAAK,UAAUC,yCAAAA,MAAW,KAAK,OAAO,QAAQ,GAAG;AAEpD,YAAM,eAAe,KAAK,OAAO;AACjC,mBAAa,IAAI,CAAC,OAAO,UAAU;AAElC,YAAI,UAAU,MAAM;AACnB,uBAAa,OAAO,OAAO,CAAC;AAAA,QAC5B;AAAA,MACL,CAAI;AAAA,IACD;AAAA,EACD;AAAA;AAAA,EAED,YAAY;AACX,QAAI,KAAK,UAAUA,yCAAAA,MAAW,KAAK,OAAO,QAAQ,GAAG;AAEpD,YAAM,eAAe,KAAK,OAAO;AACjC,mBAAa,IAAI,CAAC,OAAO,UAAU;AAElC,YAAI,UAAU,MAAM;AACnB,uBAAa,OAAO,OAAO,CAAC;AAAA,QAC5B;AAAA,MACL,CAAI;AAAA,IACD;AAAA,EACD;AACF;;"}