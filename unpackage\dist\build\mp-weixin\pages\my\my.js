"use strict";const e=require("../../common/vendor.js"),a=require("../../common/assets.js"),t=require("../../api/index.js"),i=require("../../stores/user.js"),n=require("../../api/common.js"),o={__name:"my",setup(o){const s=i.useUserStore(),r=["我的智能体","共创收益","我的工作台"],l=e.ref(0),u=["我的收藏","我的数字人"],c=e.ref(1),d=e.ref(0),g=e.ref([]),v=e.computed((()=>{var e;return(null==(e=g.value[d.value])?void 0:e.price)||"0.00"})),m=async()=>{try{let e=await t.getChatGoodsApi();0===e.code&&e.data?g.value=e.data:g.value=[]}catch(e){console.error("获取聊天点数商品列表失败:",e),g.value=[]}},h=e.ref([{label:"内部",value:1},{label:"dify",value:2},{label:"coze",value:3},{label:"阿里云百炼",value:4}]),p={1:"待审核",2:"审核通过",3:"审核拒绝"},x=e.ref([]),f=async()=>{let e=await t.getMyAgentListApi({merchantGuid:s.merchantGuid});x.value=e.data.data},y=e.ref([{name:"推广规则",icon:a.rule1},{name:"创作规则",icon:a.rule2},{name:"平台规则",icon:a.rule3}]),T=e.ref({availableAmount:"0.00",availableAmountText:"¥0.00",frozenAmount:"0.00",frozenAmountText:"¥0.00",totalEarnings:"0.00",totalEarningsText:"¥0.00",totalWithdrawn:"0.00",totalWithdrawnText:"¥0.00"}),_=e.ref([]);let b=e.reactive({merchantGuid:s.merchantGuid,page:1,pageSize:10});const w=e.ref(!1),G=e.ref(""),A=e.ref(!1),M=async()=>{try{let e=await t.getMyCollectionListApi(b);_.value=e.data.list}catch(a){console.error("获取收藏列表失败:",a),e.index.showToast({title:"加载失败",icon:"none"})}},S=()=>{e.index.navigateTo({url:"/pages/profile/index"})},C=()=>{e.index.navigateTo({url:"/pages/my/vip-list"})},k=()=>{e.index.navigateTo({url:"/pages/create-agent/index"})},P=()=>{console.log("提现")},j=()=>{e.index.navigateTo({url:"/pages/raw-record/index"})},$=()=>{e.index.navigateTo({url:"/pages/finance/index"})},q=()=>{w.value=!1,G.value=""},z=async()=>{await m(),A.value=!0},E=()=>{A.value=!1},I=async()=>{const a=g.value[d.value];if(a)try{e.index.showLoading({title:"正在创建订单...",mask:!0});const i=await t.buyChatGoodsApi({chatGoodsGuid:a.guid,payEnv:"xcx"});e.index.hideLoading(),n.miniPay(i.data).then((async()=>{L(i.data.orderNo,0)}),(a=>{e.index.showToast({title:a.msg||"支付失败",icon:"none"})}))}catch(i){e.index.hideLoading(),e.index.showToast({title:i.message||"创建订单失败",icon:"none"})}else e.index.showToast({title:"请选择充值选项",icon:"none"})},L=async(a,i)=>{i++;try{(await t.queryPayChatStautsApi({orderNo:a})).data.isPay?(e.index.showToast({title:"充值成功",icon:"success",duration:3e3}),E(),s.userToken&&Q()):setTimeout((()=>{L(a,i)}),2e3)}catch(n){e.index.showToast({title:n.msg||"查询支付状态失败",icon:"none"})}},U=()=>{e.index.navigateTo({url:"/pages/my/works-list"})},D=()=>{e.index.navigateTo({url:"/pages/my/figure-list"})},N=()=>{e.index.navigateTo({url:"/pages/my/video-create"})},W=()=>{e.index.navigateTo({url:"/pages/my/agent-list"})},O=()=>{e.index.navigateTo({url:"/pages/my/favorite-list"})};let V=e.reactive({headImgUrl:"",email:"",nickname:""});const B=async()=>{let e=await t.getUserInfoApi();V=Object.assign(V,e.data)},F=async()=>{let e=await t.getMyEarningsApi({merchantGuid:s.merchantGuid});console.log(e.data,"---------res.data"),T.value=e.data};e.watch((()=>s.userToken),((e,a)=>{e&&""===a&&(B(),f(),J(),F(),m())}));let H=e.ref("");const J=async()=>{try{console.log("invite.value",H.value),await t.bindInvitationApi({merchantGuid:s.merchantGuid,invitationCode:H.value})}catch(e){console.error("绑定邀请码失败:",e)}};e.onLoad((e=>{e.invite&&(H.value=e.invite,s.userToken&&J())}));let K=e.reactive({hasMembership:!1,isExpired:!0,membership:null,remainingDays:0,creatorSubscriptionCount:0,categorySubscriptionCount:0,userPoints:{totalPointsText:""}});const Q=async()=>{try{let e=await t.getUserVipInfoApi({merchantGuid:s.merchantGuid});K=Object.assign(K,e.data),console.log(e,"会员信息")}catch(a){console.error("获取会员信息失败:",a),e.index.showToast({title:"加载失败",icon:"none"})}};return e.onShow((()=>{s.userToken&&(B(),f(),M(),Q(),F(),m())})),(i,n)=>e.e({a:e.unref(V).headImgUrl,b:e.t(e.unref(V).nickname||"未设置昵称"),c:e.t(e.unref(V).email||"未设置邮箱"),d:e.o(S),e:!e.unref(K).hasMembership},e.unref(K).hasMembership?{f:e.t(e.unref(K).remainingDays)}:{},{g:!e.unref(K).hasMembership},(e.unref(K).hasMembership,{}),{h:"https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/c4d5b3ada53740d5b862f274ce48ef0c.png",i:!e.unref(K).hasMembership},(e.unref(K).hasMembership,{}),{j:e.o(C),k:a._imports_0$3,l:e.t(e.unref(K).userPoints.totalPointsText),m:e.f(r,((a,t,i)=>({a:e.t(a),b:a,c:e.n({active:l.value===t}),d:e.o((e=>l.value=t),a)}))),n:0===l.value},0===l.value?e.e({o:a._imports_0$1,p:e.o(k),q:e.f(x.value,((a,i,n)=>e.e({a:a.agentAvatar,b:e.t(a.agentName),c:e.t(a.agentDesc),d:e.t(h.value.find((e=>e.value===a.agentType)).label),e:e.t(a.priceText),f:e.t(a.isPublicText),g:e.o((t=>(a=>{e.index.navigateTo({url:`/pages/msg/index?sessionGuid=${a.guid}`})})(a)),i),h:e.o((t=>(a=>{e.index.navigateTo({url:`/pages/create-agent/index?guid=${a.guid}`})})(a)),i),i:e.o((i=>(async a=>{e.index.showModal({title:"提示",content:"确定要删除该智能体吗？",success:async i=>{i.confirm&&(await t.deleteAgentApi({merchantGuid:s.merchantGuid,agentGuid:a.guid}),e.index.showToast({title:"删除成功",icon:"success"}),f())}})})(a)),i),j:2!=a.auditStatus},2!=a.auditStatus?{k:e.t(p[a.auditStatus])}:{},{l:2===a.auditStatus},2===a.auditStatus?{m:e.t(p[a.auditStatus])}:{},{n:i}))),r:x.value.length>0},x.value.length>0?{s:a._imports_2,t:e.o(W)}:{}):1===l.value?{w:e.f(y.value,((a,t,i)=>({a:a.icon,b:e.t(a.name),c:t,d:e.o((a=>(a=>{0===a&&e.index.navigateTo({url:"/pages/rule/tg-index"}),1===a&&e.index.navigateTo({url:"/pages/rule/cz-index"}),2===a&&e.index.navigateTo({url:"/pages/rule/pt-index"})})(t)),t)}))),x:e.t(T.value.availableAmountText),y:e.o(P),z:e.t(T.value.totalEarningsText),A:e.t(T.value.totalWithdrawnText),B:a._imports_3,C:e.o(j),D:a._imports_4,E:e.o($)}:e.e({F:e.f(u,((a,t,i)=>({a:e.t(a),b:a,c:e.n({active:c.value===t}),d:e.o((e=>c.value=t),a)}))),G:0===c.value},0===c.value?e.e({H:e.f(_.value,((a,i,n)=>({a:e.t(a.collectTime),b:e.t(a.contentPreview),c:e.o((t=>(a=>{e.index.setClipboardData({data:a.messageContent,success(){e.index.showToast({title:"复制成功",icon:"none"})}})})(a)),i),d:e.o((e=>(e=>{G.value=e.messageContent||"",w.value=!0})(a)),i),e:e.o((i=>(async a=>{await t.cancelCollectMessageApi({merchantGuid:s.merchantGuid,messageGuid:a.messageGuid}),e.index.showToast({title:"取消收藏成功",icon:"none"}),b.page=1,M()})(a)),i),f:i}))),I:a._imports_0$2,J:a._imports_1$1,K:a._imports_2$2,L:_.value.length>0},_.value.length>0?{M:a._imports_2,N:e.o(O)}:{},{O:0===_.value.length},(_.value.length,{})):{P:a._imports_8,Q:e.t(e.unref(V).chat_count),R:e.o(z),S:a._imports_9,T:e.o(D),U:a._imports_10,V:e.o(N),W:a._imports_11,X:e.o(U)}),{v:1===l.value,Y:A.value},A.value?e.e({Z:g.value.length>0},g.value.length>0?{aa:e.f(g.value,((a,t,i)=>({a:e.t(a.goodsName||`${a.count}次`),b:e.t(a.price),c:a.guid||t,d:d.value===t?1:"",e:e.o((e=>(e=>{d.value=e})(t)),a.guid||t)})))}:{},{ab:e.t(v.value||"50.00"),ac:e.o(I),ad:a._imports_2$1,ae:e.o(E),af:e.o((()=>{}))}):{},{ag:w.value},w.value?{ah:e.o(q),ai:G.value,aj:e.o((()=>{})),ak:e.o(q)}:{})}},s=e._export_sfc(o,[["__scopeId","data-v-2c1fca68"]]);wx.createPage(s);
