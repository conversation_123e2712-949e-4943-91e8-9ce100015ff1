"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const bg = "https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/b9ed244f8bd849d48375bd6c29b48129.png";
const _sfc_main = {
  __name: "subscribe-popup",
  props: {
    show: {
      type: Boolean,
      default: false
    },
    agentInfo: {
      type: Object,
      default: () => ({
        agentName: "",
        agentDesc: "",
        agentAvatar: "",
        price: 0
      })
    }
  },
  emits: ["close", "subscribe"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const isIos = common_vendor.ref(false);
    const emit = __emit;
    const showPopup = common_vendor.ref(props.show);
    common_vendor.watch(() => props.show, (newVal) => {
      showPopup.value = newVal;
    });
    const closePopup = () => {
      showPopup.value = false;
      emit("close");
    };
    const handleSubscribe = () => {
      emit("subscribe", props.agentInfo);
    };
    common_vendor.onMounted(() => {
      let systemInfomations = common_vendor.index.getSystemInfoSync();
      if (systemInfomations.osName === "ios") {
        isIos.value = true;
      }
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: showPopup.value
      }, showPopup.value ? common_vendor.e({
        b: bg,
        c: common_assets._imports_0$11,
        d: common_vendor.o(closePopup),
        e: __props.agentInfo.agentAvatar,
        f: common_vendor.t(__props.agentInfo.agentName),
        g: common_vendor.t(__props.agentInfo.agentDesc),
        h: common_vendor.t(__props.agentInfo.price),
        i: isIos.value
      }, isIos.value ? {} : {
        j: common_vendor.o(handleSubscribe)
      }, {
        k: common_vendor.o(() => {
        }),
        l: common_vendor.o(closePopup)
      }) : {});
    };
  }
};
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-58f08366"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/subscribe-popup/subscribe-popup.js.map
