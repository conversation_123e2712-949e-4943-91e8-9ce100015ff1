{"version": 3, "file": "html-parser.js", "sources": ["components/ua-markdown/lib/html-parser.js"], "sourcesContent": ["/*\n * HTML5 Parser By <PERSON>\n *\n * Designed for HTML5 documents\n *\n * Original code by <PERSON> (ejohn.org)\n * http://ejohn.org/blog/pure-javascript-html-parser/\n * Original code by <PERSON>, Mozilla Public License\n * http://erik.eae.net/simplehtmlparser/simplehtmlparser.js\n *\n * ----------------------------------------------------------------------------\n * License\n * ----------------------------------------------------------------------------\n *\n * This code is triple licensed using Apache Software License 2.0,\n * Mozilla Public License or GNU Public License\n *\n * ////////////////////////////////////////////////////////////////////////////\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\"); you may not\n * use this file except in compliance with the License.  You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * ////////////////////////////////////////////////////////////////////////////\n *\n * The contents of this file are subject to the Mozilla Public License\n * Version 1.1 (the \"License\"); you may not use this file except in\n * compliance with the License. You may obtain a copy of the License at\n * http://www.mozilla.org/MPL/\n *\n * Software distributed under the License is distributed on an \"AS IS\"\n * basis, WITHOUT WARRANTY OF ANY KIND, either express or implied. See the\n * License for the specific language governing rights and limitations\n * under the License.\n *\n * The Original Code is Simple HTML Parser.\n *\n * The Initial Developer of the Original Code is Erik Arvidsson.\n * Portions created by Erik Arvidssson are Copyright (C) 2004. All Rights\n * Reserved.\n *\n * ////////////////////////////////////////////////////////////////////////////\n *\n * This program is free software; you can redistribute it and/or\n * modify it under the terms of the GNU General Public License\n * as published by the Free Software Foundation; either version 2\n * of the License, or (at your option) any later version.\n *\n * This program is distributed in the hope that it will be useful,\n * but WITHOUT ANY WARRANTY; without even the implied warranty of\n * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n * GNU General Public License for more details.\n *\n * You should have received a copy of the GNU General Public License\n * along with this program; if not, write to the Free Software\n * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301, USA.\n *\n * ----------------------------------------------------------------------------\n * Usage\n * ----------------------------------------------------------------------------\n *\n * // Use like so:\n * HTMLParser(htmlString, {\n *     start: function(tag, attrs, unary) {},\n *     end: function(tag) {},\n *     chars: function(text) {},\n *     comment: function(text) {}\n * });\n *\n * // or to get an XML string:\n * HTMLtoXML(htmlString);\n *\n * // or to get an XML DOM Document\n * HTMLtoDOM(htmlString);\n *\n * // or to inject into an existing document/DOM node\n * HTMLtoDOM(htmlString, document);\n * HTMLtoDOM(htmlString, document.body);\n *\n */\n// Regular Expressions for parsing tags and attributes\nvar startTag = /^<([-A-Za-z0-9_]+)((?:\\s+[a-zA-Z_:][-a-zA-Z0-9_:.]*(?:\\s*=\\s*(?:(?:\"[^\"]*\")|(?:'[^']*')|[^>\\s]+))?)*)\\s*(\\/?)>/;\nvar endTag = /^<\\/([-A-Za-z0-9_]+)[^>]*>/;\nvar attr = /([a-zA-Z_:][-a-zA-Z0-9_:.]*)(?:\\s*=\\s*(?:(?:\"((?:\\\\.|[^\"])*)\")|(?:'((?:\\\\.|[^'])*)')|([^>\\s]+)))?/g; // Empty Elements - HTML 5\n\nvar empty = makeMap('area,base,basefont,br,col,frame,hr,img,input,link,meta,param,embed,command,keygen,source,track,wbr'); // Block Elements - HTML 5\n// fixed by xxx 将 ins 标签从块级名单中移除\n\nvar block = makeMap('a,address,article,applet,aside,audio,blockquote,button,canvas,center,dd,del,dir,div,dl,dt,fieldset,figcaption,figure,footer,form,frameset,h1,h2,h3,h4,h5,h6,header,hgroup,hr,iframe,isindex,li,map,menu,noframes,noscript,object,ol,output,p,pre,section,script,table,tbody,td,tfoot,th,thead,tr,ul,video'); // Inline Elements - HTML 5\n\nvar inline = makeMap('abbr,acronym,applet,b,basefont,bdo,big,br,button,cite,code,del,dfn,em,font,i,iframe,img,input,ins,kbd,label,map,object,q,s,samp,script,select,small,span,strike,strong,sub,sup,textarea,tt,u,var'); // Elements that you can, intentionally, leave open\n// (and which close themselves)\n\nvar closeSelf = makeMap('colgroup,dd,dt,li,options,p,td,tfoot,th,thead,tr'); // Attributes that have their values filled in disabled=\"disabled\"\n\nvar fillAttrs = makeMap('checked,compact,declare,defer,disabled,ismap,multiple,nohref,noresize,noshade,nowrap,readonly,selected'); // Special Elements (can contain anything)\n\nvar special = makeMap('script,style');\nfunction HTMLParser(html, handler) {\n  var index;\n  var chars;\n  var match;\n  var stack = [];\n  var last = html;\n\n  stack.last = function () {\n    return this[this.length - 1];\n  };\n\n  while (html) {\n    chars = true; // Make sure we're not in a script or style element\n\n    if (!stack.last() || !special[stack.last()]) {\n      // Comment\n      if (html.indexOf('<!--') == 0) {\n        index = html.indexOf('-->');\n\n        if (index >= 0) {\n          if (handler.comment) {\n            handler.comment(html.substring(4, index));\n          }\n\n          html = html.substring(index + 3);\n          chars = false;\n        } // end tag\n\n      } else if (html.indexOf('</') == 0) {\n        match = html.match(endTag);\n\n        if (match) {\n          html = html.substring(match[0].length);\n          match[0].replace(endTag, parseEndTag);\n          chars = false;\n        } // start tag\n\n      } else if (html.indexOf('<') == 0) {\n        match = html.match(startTag);\n\n        if (match) {\n          html = html.substring(match[0].length);\n          match[0].replace(startTag, parseStartTag);\n          chars = false;\n        }\n      }\n\n      if (chars) {\n        index = html.indexOf('<');\n        var text = index < 0 ? html : html.substring(0, index);\n        html = index < 0 ? '' : html.substring(index);\n\n        if (handler.chars) {\n          handler.chars(text);\n        }\n      }\n    } else {\n      html = html.replace(new RegExp('([\\\\s\\\\S]*?)<\\/' + stack.last() + '[^>]*>'), function (all, text) {\n        text = text.replace(/<!--([\\s\\S]*?)-->|<!\\[CDATA\\[([\\s\\S]*?)]]>/g, '$1$2');\n\n        if (handler.chars) {\n          handler.chars(text);\n        }\n\n        return '';\n      });\n      parseEndTag('', stack.last());\n    }\n\n    if (html == last) {\n      throw 'Parse Error: ' + html;\n    }\n\n    last = html;\n  } // Clean up any remaining tags\n\n\n  parseEndTag();\n\n  function parseStartTag(tag, tagName, rest, unary) {\n    tagName = tagName.toLowerCase();\n\n    if (block[tagName]) {\n      while (stack.last() && inline[stack.last()]) {\n        parseEndTag('', stack.last());\n      }\n    }\n\n    if (closeSelf[tagName] && stack.last() == tagName) {\n      parseEndTag('', tagName);\n    }\n\n    unary = empty[tagName] || !!unary;\n\n    if (!unary) {\n      stack.push(tagName);\n    }\n\n    if (handler.start) {\n      var attrs = [];\n      rest.replace(attr, function (match, name) {\n        var value = arguments[2] ? arguments[2] : arguments[3] ? arguments[3] : arguments[4] ? arguments[4] : fillAttrs[name] ? name : '';\n        attrs.push({\n          name: name,\n          value: value,\n          escaped: value.replace(/(^|[^\\\\])\"/g, '$1\\\\\\\"') // \"\n\n        });\n      });\n\n      if (handler.start) {\n        handler.start(tagName, attrs, unary);\n      }\n    }\n  }\n\n  function parseEndTag(tag, tagName) {\n    // If no tag name is provided, clean shop\n    if (!tagName) {\n      var pos = 0;\n    } // Find the closest opened tag of the same type\n    else {\n        for (var pos = stack.length - 1; pos >= 0; pos--) {\n          if (stack[pos] == tagName) {\n            break;\n          }\n        }\n      }\n\n    if (pos >= 0) {\n      // Close all the open elements, up the stack\n      for (var i = stack.length - 1; i >= pos; i--) {\n        if (handler.end) {\n          handler.end(stack[i]);\n        }\n      } // Remove the open elements from the stack\n\n\n      stack.length = pos;\n    }\n  }\n}\n\nfunction makeMap(str) {\n  var obj = {};\n  var items = str.split(',');\n\n  for (var i = 0; i < items.length; i++) {\n    obj[items[i]] = true;\n  }\n\n  return obj;\n}\n\nfunction removeDOCTYPE(html) {\n  return html.replace(/<\\?xml.*\\?>\\n/, '').replace(/<!doctype.*>\\n/, '').replace(/<!DOCTYPE.*>\\n/, '');\n}\n\nfunction parseAttrs(attrs) {\n  return attrs.reduce(function (pre, attr) {\n    var value = attr.value;\n    var name = attr.name;\n\n    if (pre[name]) {\n\t\t\tpre[name] = pre[name] + \" \" + value;\n    } else {\n\t\t\tpre[name] = value;\n    }\n\n    return pre;\n  }, {});\n}\n\nfunction parseHtml(html) {\n  html = removeDOCTYPE(html);\n  var stacks = [];\n  var results = {\n    node: 'root',\n    children: []\n  };\n  HTMLParser(html, {\n    start: function start(tag, attrs, unary) {\n      var node = {\n        name: tag\n      };\n\n      if (attrs.length !== 0) {\n        node.attrs = parseAttrs(attrs);\n      }\n\n      if (unary) {\n        var parent = stacks[0] || results;\n\n        if (!parent.children) {\n          parent.children = [];\n        }\n\n        parent.children.push(node);\n      } else {\n        stacks.unshift(node);\n      }\n    },\n    end: function end(tag) {\n      var node = stacks.shift();\n      if (node.name !== tag) console.error('invalid state: mismatch end tag');\n\n      if (stacks.length === 0) {\n        results.children.push(node);\n      } else {\n        var parent = stacks[0];\n\n        if (!parent.children) {\n          parent.children = [];\n        }\n\n        parent.children.push(node);\n      }\n    },\n    chars: function chars(text) {\n      var node = {\n        type: 'text',\n        text: text\n      };\n\n      if (stacks.length === 0) {\n        results.children.push(node);\n      } else {\n        var parent = stacks[0];\n\n        if (!parent.children) {\n          parent.children = [];\n        }\n\n        parent.children.push(node);\n      }\n    },\n    comment: function comment(text) {\n      var node = {\n        node: 'comment',\n        text: text\n      };\n      var parent = stacks[0];\n\n      if (!parent.children) {\n        parent.children = [];\n      }\n\n      parent.children.push(node);\n    }\n  });\n  return results.children;\n}\n\nexport default parseHtml;"], "names": [], "mappings": ";;AAqFY,QAAQ,oGAAoG;AAG5G,QAAQ,2SAA2S;AAElT,QAAQ,kMAAkM;AAGvM,QAAQ,kDAAkD;AAE1D,QAAQ,wGAAwG;AAElH,QAAQ,cAAc;AAgJpC,SAAS,QAAQ,KAAK;AACpB,MAAI,MAAM,CAAA;AACV,MAAI,QAAQ,IAAI,MAAM,GAAG;AAEzB,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,QAAI,MAAM,CAAC,CAAC,IAAI;AAAA,EACjB;AAED,SAAO;AACT;"}