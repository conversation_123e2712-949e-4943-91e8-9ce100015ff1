<view class="agent-detail-page data-v-3c4ace4c"><view wx:if="{{a}}" class="agent-info data-v-3c4ace4c"><view class="avatar-section data-v-3c4ace4c"><image class="agent-avatar data-v-3c4ace4c" src="{{b}}" mode="aspectFill"/><view class="nick-name data-v-3c4ace4c">@{{c}}</view></view><view class="info-section data-v-3c4ace4c"><text class="agent-name data-v-3c4ace4c">{{d}}</text><text class="agent-desc data-v-3c4ace4c">{{e}}</text></view><view class="action-buttons data-v-3c4ace4c"><view wx:if="{{f}}" class="subscribe-btn data-v-3c4ace4c" bindtap="{{g}}"><text class="btn-text data-v-3c4ace4c">立即招募</text></view><view wx:if="{{h}}" class="chat-btn data-v-3c4ace4c" bindtap="{{i}}"><text class="btn-text data-v-3c4ace4c">去聊天</text></view><view class="share-btn data-v-3c4ace4c" bindtap="{{j}}"><text class="btn-text data-v-3c4ace4c">生成分享海报</text></view></view></view><view wx:elif="{{k}}" class="loading-state data-v-3c4ace4c"><text class="loading-text data-v-3c4ace4c">加载中...</text></view><view wx:else class="empty-state data-v-3c4ace4c"><text class="empty-text data-v-3c4ace4c">智能体信息加载失败</text></view></view>