{"version": 3, "file": "z-paging-refresh.js", "sources": ["uni_modules/z-paging/components/z-paging/components/z-paging-refresh.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RToveW91bmdQcm9qZWN0L2FnZW50LW1pbmktdWkvdW5pX21vZHVsZXMvei1wYWdpbmcvY29tcG9uZW50cy96LXBhZ2luZy9jb21wb25lbnRzL3otcGFnaW5nLXJlZnJlc2gudnVl"], "sourcesContent": ["<!-- [z-paging]下拉刷新view -->\r\n<template>\r\n\t<view style=\"height: 100%;\">\r\n\t\t<view :class=\"showUpdateTime?'zp-r-container zp-r-container-padding':'zp-r-container'\">\r\n\t\t\t<view class=\"zp-r-left\">\r\n\t\t\t\t<!-- 非加载中(继续下拉刷新、松手立即刷新状态图片) -->\r\n\t\t\t\t<image v-if=\"status!==R.Loading\" :class=\"leftImageClass\" :style=\"[leftImageStyle,imgStyle]\" :src=\"leftImageSrc\" />\r\n\t\t\t\t<!-- 加载状态图片 -->\r\n\t\t\t\t<!-- #ifndef APP-NVUE -->\r\n\t\t\t\t<image v-else :class=\"{'zp-line-loading-image':refreshingAnimated,'zp-r-left-image':true,'zp-r-left-image-pre-size-rpx':unit==='rpx','zp-r-left-image-pre-size-px':unit==='px'}\" :style=\"[leftImageStyle,imgStyle]\" :src=\"leftImageSrc\" />\r\n\t\t\t\t<!-- #endif -->\r\n\t\t\t\t<!-- 在nvue中，加载状态loading使用系统loading -->\r\n\t\t\t\t<!-- #ifdef APP-NVUE -->\r\n\t\t\t\t<view v-else :style=\"[{'margin-right':showUpdateTime?addUnit(18,unit):addUnit(12, unit)}]\">\r\n\t\t\t\t\t<loading-indicator :class=\"isIos?{'zp-loading-image-ios-rpx':unit==='rpx','zp-loading-image-ios-px':unit==='px'}:{'zp-loading-image-android-rpx':unit==='rpx','zp-loading-image-android-px':unit==='px'}\" \r\n\t\t\t\t\t:style=\"[{color:zTheme.indicator[ts]},imgStyle]\" :animating=\"true\" />\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- #endif -->\r\n\t\t\t</view>\r\n\t\t\t<!-- 右侧文字内容 -->\r\n\t\t\t<view class=\"zp-r-right\">\r\n\t\t\t\t<!-- 右侧下拉刷新状态文字 -->\r\n\t\t\t\t<text class=\"zp-r-right-text\" :style=\"[rightTextStyle,titleStyle]\">{{currentTitle}}</text>\r\n\t\t\t\t<!-- 右侧下拉刷新时间文字 -->\r\n\t\t\t\t<text v-if=\"showUpdateTime&&refresherTimeText.length\" class=\"zp-r-right-text\" :class=\"{'zp-r-right-time-text-rpx':unit==='rpx','zp-r-right-time-text-px':unit==='px'}\" :style=\"[{color:zTheme.title[ts]},updateTimeStyle]\">\r\n\t\t\t\t\t{{refresherTimeText}}\r\n\t\t\t\t</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n<script>\r\n\timport zStatic from '../js/z-paging-static'\r\n\timport u from '../js/z-paging-utils'\r\n\timport Enum from '../js/z-paging-enum'\r\n\t\r\n\texport default {\r\n\t\tname: 'z-paging-refresh',\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tR: Enum.Refresher,\r\n\t\t\t\tisIos: uni.getSystemInfoSync().platform === 'ios',\r\n\t\t\t\trefresherTimeText: '',\r\n\t\t\t\tzTheme: {\r\n\t\t\t\t\ttitle: { white: '#efefef', black: '#555555' },\r\n\t\t\t\t\tarrow: { white: zStatic.base64ArrowWhite, black: zStatic.base64Arrow },\r\n\t\t\t\t\tflower: { white: zStatic.base64FlowerWhite, black: zStatic.base64Flower },\r\n\t\t\t\t\tsuccess: { white: zStatic.base64SuccessWhite, black: zStatic.base64Success },\r\n\t\t\t\t\tindicator: { white: '#eeeeee', black: '#777777' }\r\n\t\t\t\t}\r\n\t\t\t};\r\n\t\t},\r\n\t\tprops: ['status', 'defaultThemeStyle', 'defaultText', 'pullingText', 'refreshingText', 'completeText', 'goF2Text', 'defaultImg', 'pullingImg', \r\n\t\t\t'refreshingImg', 'completeImg', 'refreshingAnimated', 'showUpdateTime', 'updateTimeKey', 'imgStyle', 'titleStyle', 'updateTimeStyle', 'updateTimeTextMap', 'unit'\r\n\t\t],\r\n\t\tcomputed: {\r\n\t\t\tts() {\r\n\t\t\t\treturn this.defaultThemeStyle;\r\n\t\t\t},\r\n\t\t\t// 当前状态数组\r\n\t\t\tstatusTextArr() {\r\n\t\t\t\tthis.updateTime();\r\n\t\t\t\treturn [this.defaultText, this.pullingText, this.refreshingText, this.completeText, this.goF2Text];\r\n\t\t\t},\r\n\t\t\t// 当前状态文字\r\n\t\t\tcurrentTitle() {\r\n\t\t\t\treturn this.statusTextArr[this.status] || this.defaultText;\r\n\t\t\t},\r\n\t\t\t// 左侧图片class\r\n\t\t\tleftImageClass() {\r\n\t\t\t\tconst preSizeClass = `zp-r-left-image-pre-size-${this.unit}`;\r\n\t\t\t\tif (this.status === this.R.Complete) return preSizeClass;\r\n\t\t\t\treturn `zp-r-left-image ${preSizeClass} ${this.status === this.R.Default ? 'zp-r-arrow-down' : 'zp-r-arrow-top'}`;\r\n\t\t\t},\r\n\t\t\t// 左侧图片style\r\n\t\t\tleftImageStyle() {\r\n\t\t\t\tconst showUpdateTime = this.showUpdateTime;\r\n\t\t\t\tconst size = showUpdateTime ? u.addUnit(36, this.unit) : u.addUnit(34, this.unit);\r\n\t\t\t\treturn {width: size,height: size,'margin-right': showUpdateTime ? u.addUnit(20, this.unit) : u.addUnit(9, this.unit)};\r\n\t\t\t},\r\n\t\t\t// 左侧图片src\r\n\t\t\tleftImageSrc() {\r\n\t\t\t\tconst R = this.R;\r\n\t\t\t\tconst status = this.status;\r\n\t\t\t\tif (status === R.Default) {\r\n\t\t\t\t\tif (!!this.defaultImg) return this.defaultImg;\r\n\t\t\t\t\treturn this.zTheme.arrow[this.ts];\r\n\t\t\t\t} else if (status === R.ReleaseToRefresh) {\r\n\t\t\t\t\tif (!!this.pullingImg) return this.pullingImg;\r\n\t\t\t\t\tif (!!this.defaultImg) return this.defaultImg;\r\n\t\t\t\t\treturn this.zTheme.arrow[this.ts];\r\n\t\t\t\t} else if (status === R.Loading) {\r\n\t\t\t\t\tif (!!this.refreshingImg) return this.refreshingImg;\r\n\t\t\t\t\treturn this.zTheme.flower[this.ts];;\r\n\t\t\t\t} else if (status === R.Complete) {\r\n\t\t\t\t\tif (!!this.completeImg) return this.completeImg;\r\n\t\t\t\t\treturn this.zTheme.success[this.ts];;\r\n\t\t\t\t} else if (status === R.GoF2) {\r\n\t\t\t\t\treturn this.zTheme.arrow[this.ts];\r\n\t\t\t\t}\r\n\t\t\t\treturn '';\r\n\t\t\t},\r\n\t\t\t// 右侧文字style\r\n\t\t\trightTextStyle() {\r\n\t\t\t\tlet stl = {};\r\n\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\tconst textHeight = this.showUpdateTime ? u.addUnit(40, this.unit) : u.addUnit(80, this.unit);\r\n\t\t\t\tstl = {'height': textHeight, 'line-height': textHeight}\r\n\t\t\t\t// #endif\r\n\t\t\t\tstl['color'] = this.zTheme.title[this.ts];\r\n\t\t\t\tstl['font-size'] = u.addUnit(30, this.unit);\r\n\t\t\t\treturn stl;\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 添加单位\r\n\t\t\taddUnit(value, unit) {\r\n\t\t\t\treturn u.addUnit(value, unit);\r\n\t\t\t},\r\n\t\t\t// 更新下拉刷新时间\r\n\t\t\tupdateTime() {\r\n\t\t\t\tif (this.showUpdateTime) {\r\n\t\t\t\t\tthis.refresherTimeText = u.getRefesrherFormatTimeByKey(this.updateTimeKey, this.updateTimeTextMap);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style scoped>\r\n\t@import \"../css/z-paging-static.css\";\r\n\r\n\t.zp-r-container {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\theight: 100%;\r\n\t\t/* #endif */\r\n\t\tflex-direction: row;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.zp-r-container-padding {\r\n\t\t/* #ifdef APP-NVUE */\r\n\t\tpadding: 7px 0rpx;\r\n\t\t/* #endif */\r\n\t}\r\n\r\n\t.zp-r-left {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\t/* #endif */\r\n\t\tflex-direction: row;\r\n\t\talign-items: center;\r\n\t\toverflow: hidden;\r\n\t\t/* #ifdef MP-ALIPAY */\r\n\t\tmargin-top: -4rpx;\r\n\t\t/* #endif */\r\n\t}\r\n\r\n\t.zp-r-left-image {\r\n\t\ttransition-duration: .2s;\r\n\t\ttransition-property: transform;\r\n\t\tcolor: #666666;\r\n\t}\r\n\t\r\n\t.zp-r-left-image-pre-size-rpx {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\twidth: 34rpx;\r\n\t\theight: 34rpx;\r\n\t\toverflow: hidden;\r\n\t\t/* #endif */\r\n\t}\r\n\t\r\n\t.zp-r-left-image-pre-size-px {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\twidth: 17px;\r\n\t\theight: 17px;\r\n\t\toverflow: hidden;\r\n\t\t/* #endif */\r\n\t}\r\n\r\n\t.zp-r-arrow-top {\r\n\t\ttransform: rotate(0deg);\r\n\t}\r\n\r\n\t.zp-r-arrow-down {\r\n\t\ttransform: rotate(180deg);\r\n\t}\r\n\r\n\t.zp-r-right {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\t/* #endif */\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t}\r\n\r\n\t.zp-r-right-time-text-rpx {\r\n\t\tmargin-top: 10rpx;\r\n\t\tfont-size: 26rpx;\r\n\t}\r\n\t.zp-r-right-time-text-px {\r\n\t\tmargin-top: 5px;\r\n\t\tfont-size: 13px;\r\n\t}\r\n</style>\r\n", "import Component from 'E:/youngProject/agent-mini-ui/uni_modules/z-paging/components/z-paging/components/z-paging-refresh.vue'\nwx.createComponent(Component)"], "names": ["Enum", "uni", "zStatic", "u"], "mappings": ";;;;;AAoCC,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,OAAO;AACN,WAAO;AAAA,MACN,GAAGA,sDAAI,KAAC;AAAA,MACR,OAAOC,cAAG,MAAC,kBAAmB,EAAC,aAAa;AAAA,MAC5C,mBAAmB;AAAA,MACnB,QAAQ;AAAA,QACP,OAAO,EAAE,OAAO,WAAW,OAAO,UAAW;AAAA,QAC7C,OAAO,EAAE,OAAOC,wDAAO,QAAC,kBAAkB,OAAOA,wDAAO,QAAC,YAAa;AAAA,QACtE,QAAQ,EAAE,OAAOA,wDAAO,QAAC,mBAAmB,OAAOA,wDAAO,QAAC,aAAc;AAAA,QACzE,SAAS,EAAE,OAAOA,wDAAO,QAAC,oBAAoB,OAAOA,wDAAO,QAAC,cAAe;AAAA,QAC5E,WAAW,EAAE,OAAO,WAAW,OAAO,UAAU;AAAA,MACjD;AAAA;EAED;AAAA,EACD,OAAO;AAAA,IAAC;AAAA,IAAU;AAAA,IAAqB;AAAA,IAAe;AAAA,IAAe;AAAA,IAAkB;AAAA,IAAgB;AAAA,IAAY;AAAA,IAAc;AAAA,IAChI;AAAA,IAAiB;AAAA,IAAe;AAAA,IAAsB;AAAA,IAAkB;AAAA,IAAiB;AAAA,IAAY;AAAA,IAAc;AAAA,IAAmB;AAAA,IAAqB;AAAA,EAC3J;AAAA,EACD,UAAU;AAAA,IACT,KAAK;AACJ,aAAO,KAAK;AAAA,IACZ;AAAA;AAAA,IAED,gBAAgB;AACf,WAAK,WAAU;AACf,aAAO,CAAC,KAAK,aAAa,KAAK,aAAa,KAAK,gBAAgB,KAAK,cAAc,KAAK,QAAQ;AAAA,IACjG;AAAA;AAAA,IAED,eAAe;AACd,aAAO,KAAK,cAAc,KAAK,MAAM,KAAK,KAAK;AAAA,IAC/C;AAAA;AAAA,IAED,iBAAiB;AAChB,YAAM,eAAe,4BAA4B,KAAK,IAAI;AAC1D,UAAI,KAAK,WAAW,KAAK,EAAE;AAAU,eAAO;AAC5C,aAAO,mBAAmB,YAAY,IAAI,KAAK,WAAW,KAAK,EAAE,UAAU,oBAAoB,gBAAgB;AAAA,IAC/G;AAAA;AAAA,IAED,iBAAiB;AAChB,YAAM,iBAAiB,KAAK;AAC5B,YAAM,OAAO,iBAAiBC,uDAAC,EAAC,QAAQ,IAAI,KAAK,IAAI,IAAIA,uDAAC,EAAC,QAAQ,IAAI,KAAK,IAAI;AAChF,aAAO,EAAC,OAAO,MAAK,QAAQ,MAAK,gBAAgB,iBAAiBA,uDAAAA,EAAE,QAAQ,IAAI,KAAK,IAAI,IAAIA,yDAAE,QAAQ,GAAG,KAAK,IAAI,EAAC;AAAA,IACpH;AAAA;AAAA,IAED,eAAe;AACd,YAAM,IAAI,KAAK;AACf,YAAM,SAAS,KAAK;AACpB,UAAI,WAAW,EAAE,SAAS;AACzB,YAAI,CAAC,CAAC,KAAK;AAAY,iBAAO,KAAK;AACnC,eAAO,KAAK,OAAO,MAAM,KAAK,EAAE;AAAA,MACjC,WAAW,WAAW,EAAE,kBAAkB;AACzC,YAAI,CAAC,CAAC,KAAK;AAAY,iBAAO,KAAK;AACnC,YAAI,CAAC,CAAC,KAAK;AAAY,iBAAO,KAAK;AACnC,eAAO,KAAK,OAAO,MAAM,KAAK,EAAE;AAAA,iBACtB,WAAW,EAAE,SAAS;AAChC,YAAI,CAAC,CAAC,KAAK;AAAe,iBAAO,KAAK;AACtC,eAAO,KAAK,OAAO,OAAO,KAAK,EAAE;AAAA,iBACvB,WAAW,EAAE,UAAU;AACjC,YAAI,CAAC,CAAC,KAAK;AAAa,iBAAO,KAAK;AACpC,eAAO,KAAK,OAAO,QAAQ,KAAK,EAAE;AAAA,iBACxB,WAAW,EAAE,MAAM;AAC7B,eAAO,KAAK,OAAO,MAAM,KAAK,EAAE;AAAA,MACjC;AACA,aAAO;AAAA,IACP;AAAA;AAAA,IAED,iBAAiB;AAChB,UAAI,MAAM,CAAA;AAKV,UAAI,OAAO,IAAI,KAAK,OAAO,MAAM,KAAK,EAAE;AACxC,UAAI,WAAW,IAAIA,uDAAC,EAAC,QAAQ,IAAI,KAAK,IAAI;AAC1C,aAAO;AAAA,IACR;AAAA,EACA;AAAA,EACD,SAAS;AAAA;AAAA,IAER,QAAQ,OAAO,MAAM;AACpB,aAAOA,yDAAE,QAAQ,OAAO,IAAI;AAAA,IAC5B;AAAA;AAAA,IAED,aAAa;AACZ,UAAI,KAAK,gBAAgB;AACxB,aAAK,oBAAoBA,uDAAAA,EAAE,4BAA4B,KAAK,eAAe,KAAK,iBAAiB;AAAA,MAClG;AAAA,IACD;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7HD,GAAG,gBAAgB,SAAS;"}