{"version": 3, "file": "uni-highlight.min.js", "sources": ["components/ua-markdown/lib/highlight/uni-highlight.min.js"], "sourcesContent": ["/*\r\n  Highlight.js v11.7.0\r\n  (c) 2006-2022 undefined and other contributors\r\n  License: BSD-3-Clause\r\n */\r\nvar e = {\r\n\texports: {}\r\n};\r\nfunction n(e) {\r\n\treturn e instanceof Map ? e.clear = e.delete = e.set = () => {\r\n\t\tthrow Error(\"map is read-only\")\r\n\t} : e instanceof Set && (e.add = e.clear = e.delete = () => {\r\n\t\tthrow Error(\"set is read-only\")\r\n\t}), Object.freeze(e), Object.getOwnPropertyNames(e).forEach((t => {\r\n\t\tvar a = e[t];\r\n\t\t\"object\" != typeof a || Object.isFrozen(a) || n(a)\r\n\t})), e\r\n}\r\ne.exports = n, e.exports.default = n;\r\nclass t {\r\n\tconstructor(e) {\r\n\t\tvoid 0 === e.data && (e.data = {}), this.data = e.data, this.isMatchIgnored = !1\r\n\t}\r\n\tignoreMatch() {\r\n\t\tthis.isMatchIgnored = !0\r\n\t}\r\n}\r\n\r\nfunction a(e) {\r\n\treturn e.replace(/&/g, \"&amp;\").replace(/</g, \"&lt;\").replace(/>/g, \"&gt;\").replace(/\"/g, \"&quot;\").replace(/'/g,\r\n\t\t\"&#x27;\")\r\n}\r\n\r\nfunction i(e, ...n) {\r\n\tconst t = Object.create(null);\r\n\tfor (const n in e) t[n] = e[n];\r\n\treturn n.forEach((e => {\r\n\t\tfor (const n in e) t[n] = e[n]\r\n\t})), t\r\n}\r\nconst r = e => !!e.scope || e.sublanguage && e.language;\r\nclass s {\r\n\tconstructor(e, n) {\r\n\t\tthis.buffer = \"\", this.classPrefix = n.classPrefix, e.walk(this)\r\n\t}\r\n\taddText(e) {\r\n\t\tthis.buffer += a(e)\r\n\t}\r\n\topenNode(e) {\r\n\t\tif (!r(e)) return;\r\n\t\tlet n = \"\";\r\n\t\tn = e.sublanguage ? \"language-\" + e.language : ((e, {\r\n\t\t\tprefix: n\r\n\t\t}) => {\r\n\t\t\tif (e.includes(\".\")) {\r\n\t\t\t\tconst t = e.split(\".\");\r\n\t\t\t\treturn [`${n}${t.shift()}`, ...t.map(((e, n) => `${e}${\"_\".repeat(n+1)}`))].join(\" \")\r\n\t\t\t}\r\n\t\t\treturn `${n}${e}`\r\n\t\t})(e.scope, {\r\n\t\t\tprefix: this.classPrefix\r\n\t\t}), this.span(n)\r\n\t}\r\n\tcloseNode(e) {\r\n\t\tr(e) && (this.buffer += \"</span>\")\r\n\t}\r\n\tvalue() {\r\n\t\treturn this.buffer\r\n\t}\r\n\tspan(e) {\r\n\t\tthis.buffer += `<span class=\"${e}\">`\r\n\t}\r\n}\r\nconst o = (e = {}) => {\r\n\tconst n = {\r\n\t\tchildren: []\r\n\t};\r\n\treturn Object.assign(n, e), n\r\n};\r\nclass l {\r\n\tconstructor() {\r\n\t\tthis.rootNode = o(), this.stack = [this.rootNode]\r\n\t}\r\n\tget top() {\r\n\t\treturn this.stack[this.stack.length - 1]\r\n\t}\r\n\tget root() {\r\n\t\treturn this.rootNode\r\n\t}\r\n\tadd(e) {\r\n\t\tthis.top.children.push(e)\r\n\t}\r\n\topenNode(e) {\r\n\t\tconst n = o({\r\n\t\t\tscope: e\r\n\t\t});\r\n\t\tthis.add(n), this.stack.push(n)\r\n\t}\r\n\tcloseNode() {\r\n\t\tif (this.stack.length > 1) return this.stack.pop()\r\n\t}\r\n\tcloseAllNodes() {\r\n\t\tfor (; this.closeNode(););\r\n\t}\r\n\ttoJSON() {\r\n\t\treturn JSON.stringify(this.rootNode, null, 4)\r\n\t}\r\n\twalk(e) {\r\n\t\treturn this.constructor._walk(e, this.rootNode)\r\n\t}\r\n\tstatic _walk(e, n) {\r\n\t\treturn \"string\" == typeof n ? e.addText(n) : n.children && (e.openNode(n),\r\n\t\t\tn.children.forEach((n => this._walk(e, n))), e.closeNode(n)), e\r\n\t}\r\n\tstatic _collapse(e) {\r\n\t\t\"string\" != typeof e && e.children && (e.children.every((e => \"string\" == typeof e)) ? e.children = [e.children\r\n\t\t\t.join(\"\")\r\n\t\t] : e.children.forEach((e => {\r\n\t\t\tl._collapse(e)\r\n\t\t})))\r\n\t}\r\n}\r\nclass c extends l {\r\n\tconstructor(e) {\r\n\t\tsuper(), this.options = e\r\n\t}\r\n\taddKeyword(e, n) {\r\n\t\t\"\" !== e && (this.openNode(n), this.addText(e), this.closeNode())\r\n\t}\r\n\taddText(e) {\r\n\t\t\"\" !== e && this.add(e)\r\n\t}\r\n\taddSublanguage(e, n) {\r\n\t\tconst t = e.root;\r\n\t\tt.sublanguage = !0, t.language = n, this.add(t)\r\n\t}\r\n\ttoHTML() {\r\n\t\treturn new s(this, this.options).value()\r\n\t}\r\n\tfinalize() {\r\n\t\treturn !0\r\n\t}\r\n}\r\n\r\nfunction d(e) {\r\n\treturn e ? \"string\" == typeof e ? e : e.source : null\r\n}\r\n\r\nfunction g(e) {\r\n\treturn m(\"(?=\", e, \")\")\r\n}\r\n\r\nfunction u(e) {\r\n\treturn m(\"(?:\", e, \")*\")\r\n}\r\n\r\nfunction b(e) {\r\n\treturn m(\"(?:\", e, \")?\")\r\n}\r\n\r\nfunction m(...e) {\r\n\treturn e.map((e => d(e))).join(\"\")\r\n}\r\n\r\nfunction p(...e) {\r\n\tconst n = (e => {\r\n\t\tconst n = e[e.length - 1];\r\n\t\treturn \"object\" == typeof n && n.constructor === Object ? (e.splice(e.length - 1, 1), n) : {}\r\n\t})(e);\r\n\treturn \"(\" + (n.capture ? \"\" : \"?:\") + e.map((e => d(e))).join(\"|\") + \")\"\r\n}\r\n\r\nfunction _(e) {\r\n\treturn RegExp(e.toString() + \"|\").exec(\"\").length - 1\r\n}\r\nconst h = /\\[(?:[^\\\\\\]]|\\\\.)*\\]|\\(\\??|\\\\([1-9][0-9]*)|\\\\./;\r\n\r\nfunction f(e, {\r\n\tjoinWith: n\r\n}) {\r\n\tlet t = 0;\r\n\treturn e.map((e => {\r\n\t\tt += 1;\r\n\t\tconst n = t;\r\n\t\tlet a = d(e),\r\n\t\t\ti = \"\";\r\n\t\tfor (; a.length > 0;) {\r\n\t\t\tconst e = h.exec(a);\r\n\t\t\tif (!e) {\r\n\t\t\t\ti += a;\r\n\t\t\t\tbreak\r\n\t\t\t}\r\n\t\t\ti += a.substring(0, e.index),\r\n\t\t\t\ta = a.substring(e.index + e[0].length), \"\\\\\" === e[0][0] && e[1] ? i += \"\\\\\" + (Number(e[1]) + n) : (i +=\r\n\t\t\t\t\te[0],\r\n\t\t\t\t\t\"(\" === e[0] && t++)\r\n\t\t}\r\n\t\treturn i\r\n\t})).map((e => `(${e})`)).join(n)\r\n}\r\nconst E = \"(-?)(\\\\b0[xX][a-fA-F0-9]+|(\\\\b\\\\d+(\\\\.\\\\d*)?|\\\\.\\\\d+)([eE][-+]?\\\\d+)?)\",\r\n\ty = {\r\n\t\tbegin: \"\\\\\\\\[\\\\s\\\\S]\",\r\n\t\trelevance: 0\r\n\t},\r\n\tw = {\r\n\t\tscope: \"string\",\r\n\t\tbegin: \"'\",\r\n\t\tend: \"'\",\r\n\t\tillegal: \"\\\\n\",\r\n\t\tcontains: [y]\r\n\t},\r\n\tN = {\r\n\t\tscope: \"string\",\r\n\t\tbegin: '\"',\r\n\t\tend: '\"',\r\n\t\tillegal: \"\\\\n\",\r\n\t\tcontains: [y]\r\n\t},\r\n\tv = (e, n, t = {}) => {\r\n\t\tconst a = i({\r\n\t\t\tscope: \"comment\",\r\n\t\t\tbegin: e,\r\n\t\t\tend: n,\r\n\t\t\tcontains: []\r\n\t\t}, t);\r\n\t\ta.contains.push({\r\n\t\t\tscope: \"doctag\",\r\n\t\t\tbegin: \"[ ]*(?=(TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):)\",\r\n\t\t\tend: /(TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):/,\r\n\t\t\texcludeBegin: !0,\r\n\t\t\trelevance: 0\r\n\t\t});\r\n\t\tconst r = p(\"I\", \"a\", \"is\", \"so\", \"us\", \"to\", \"at\", \"if\", \"in\", \"it\", \"on\", /[A-Za-z]+['](d|ve|re|ll|t|s|n)/,\r\n\t\t\t/[A-Za-z]+[-][a-z]+/, /[A-Za-z][a-z]{2,}/);\r\n\t\treturn a.contains.push({\r\n\t\t\tbegin: m(/[ ]+/, \"(\", r, /[.]?[:]?([.][ ]|[ ])/, \"){3}\")\r\n\t\t}), a\r\n\t},\r\n\tO = v(\"//\", \"$\"),\r\n\tk = v(\"/\\\\*\", \"\\\\*/\"),\r\n\tx = v(\"#\", \"$\");\r\nvar M = Object.freeze({\r\n\t__proto__: null,\r\n\tMATCH_NOTHING_RE: /\\b\\B/,\r\n\tIDENT_RE: \"[a-zA-Z]\\\\w*\",\r\n\tUNDERSCORE_IDENT_RE: \"[a-zA-Z_]\\\\w*\",\r\n\tNUMBER_RE: \"\\\\b\\\\d+(\\\\.\\\\d+)?\",\r\n\tC_NUMBER_RE: E,\r\n\tBINARY_NUMBER_RE: \"\\\\b(0b[01]+)\",\r\n\tRE_STARTERS_RE: \"!|!=|!==|%|%=|&|&&|&=|\\\\*|\\\\*=|\\\\+|\\\\+=|,|-|-=|/=|/|:|;|<<|<<=|<=|<|===|==|=|>>>=|>>=|>=|>>>|>>|>|\\\\?|\\\\[|\\\\{|\\\\(|\\\\^|\\\\^=|\\\\||\\\\|=|\\\\|\\\\||~\",\r\n\tSHEBANG: (e = {}) => {\r\n\t\tconst n = /^#![ ]*\\//;\r\n\t\treturn e.binary && (e.begin = m(n, /.*\\b/, e.binary, /\\b.*/)), i({\r\n\t\t\tscope: \"meta\",\r\n\t\t\tbegin: n,\r\n\t\t\tend: /$/,\r\n\t\t\trelevance: 0,\r\n\t\t\t\"on:begin\": (e, n) => {\r\n\t\t\t\t0 !== e.index && n.ignoreMatch()\r\n\t\t\t}\r\n\t\t}, e)\r\n\t},\r\n\tBACKSLASH_ESCAPE: y,\r\n\tAPOS_STRING_MODE: w,\r\n\tQUOTE_STRING_MODE: N,\r\n\tPHRASAL_WORDS_MODE: {\r\n\t\tbegin: /\\b(a|an|the|are|I'm|isn't|don't|doesn't|won't|but|just|should|pretty|simply|enough|gonna|going|wtf|so|such|will|you|your|they|like|more)\\b/\r\n\t},\r\n\tCOMMENT: v,\r\n\tC_LINE_COMMENT_MODE: O,\r\n\tC_BLOCK_COMMENT_MODE: k,\r\n\tHASH_COMMENT_MODE: x,\r\n\tNUMBER_MODE: {\r\n\t\tscope: \"number\",\r\n\t\tbegin: \"\\\\b\\\\d+(\\\\.\\\\d+)?\",\r\n\t\trelevance: 0\r\n\t},\r\n\tC_NUMBER_MODE: {\r\n\t\tscope: \"number\",\r\n\t\tbegin: E,\r\n\t\trelevance: 0\r\n\t},\r\n\tBINARY_NUMBER_MODE: {\r\n\t\tscope: \"number\",\r\n\t\tbegin: \"\\\\b(0b[01]+)\",\r\n\t\trelevance: 0\r\n\t},\r\n\tREGEXP_MODE: {\r\n\t\tbegin: /(?=\\/[^/\\n]*\\/)/,\r\n\t\tcontains: [{\r\n\t\t\tscope: \"regexp\",\r\n\t\t\tbegin: /\\//,\r\n\t\t\tend: /\\/[gimuy]*/,\r\n\t\t\tillegal: /\\n/,\r\n\t\t\tcontains: [y, {\r\n\t\t\t\tbegin: /\\[/,\r\n\t\t\t\tend: /\\]/,\r\n\t\t\t\trelevance: 0,\r\n\t\t\t\tcontains: [y]\r\n\t\t\t}]\r\n\t\t}]\r\n\t},\r\n\tTITLE_MODE: {\r\n\t\tscope: \"title\",\r\n\t\tbegin: \"[a-zA-Z]\\\\w*\",\r\n\t\trelevance: 0\r\n\t},\r\n\tUNDERSCORE_TITLE_MODE: {\r\n\t\tscope: \"title\",\r\n\t\tbegin: \"[a-zA-Z_]\\\\w*\",\r\n\t\trelevance: 0\r\n\t},\r\n\tMETHOD_GUARD: {\r\n\t\tbegin: \"\\\\.\\\\s*[a-zA-Z_]\\\\w*\",\r\n\t\trelevance: 0\r\n\t},\r\n\tEND_SAME_AS_BEGIN: e => Object.assign(e, {\r\n\t\t\"on:begin\": (e, n) => {\r\n\t\t\tn.data._beginMatch = e[1]\r\n\t\t},\r\n\t\t\"on:end\": (e, n) => {\r\n\t\t\tn.data._beginMatch !== e[1] && n.ignoreMatch()\r\n\t\t}\r\n\t})\r\n});\r\n\r\nfunction S(e, n) {\r\n\t\".\" === e.input[e.index - 1] && n.ignoreMatch()\r\n}\r\n\r\nfunction A(e, n) {\r\n\tvoid 0 !== e.className && (e.scope = e.className, delete e.className)\r\n}\r\n\r\nfunction C(e, n) {\r\n\tn && e.beginKeywords && (e.begin = \"\\\\b(\" + e.beginKeywords.split(\" \").join(\"|\") + \")(?!\\\\.)(?=\\\\b|\\\\s)\",\r\n\t\te.__beforeBegin = S, e.keywords = e.keywords || e.beginKeywords, delete e.beginKeywords,\r\n\t\tvoid 0 === e.relevance && (e.relevance = 0))\r\n}\r\n\r\nfunction T(e, n) {\r\n\tArray.isArray(e.illegal) && (e.illegal = p(...e.illegal))\r\n}\r\n\r\nfunction R(e, n) {\r\n\tif (e.match) {\r\n\t\tif (e.begin || e.end) throw Error(\"begin & end are not supported with match\");\r\n\t\te.begin = e.match, delete e.match\r\n\t}\r\n}\r\n\r\nfunction D(e, n) {\r\n\tvoid 0 === e.relevance && (e.relevance = 1)\r\n}\r\nconst I = (e, n) => {\r\n\t\tif (!e.beforeMatch) return;\r\n\t\tif (e.starts) throw Error(\"beforeMatch cannot be used with starts\");\r\n\t\tconst t = Object.assign({}, e);\r\n\t\tObject.keys(e).forEach((n => {\r\n\t\t\tdelete e[n]\r\n\t\t})), e.keywords = t.keywords, e.begin = m(t.beforeMatch, g(t.begin)), e.starts = {\r\n\t\t\trelevance: 0,\r\n\t\t\tcontains: [Object.assign(t, {\r\n\t\t\t\tendsParent: !0\r\n\t\t\t})]\r\n\t\t}, e.relevance = 0, delete t.beforeMatch\r\n\t},\r\n\tL = [\"of\", \"and\", \"for\", \"in\", \"not\", \"or\", \"if\", \"then\", \"parent\", \"list\", \"value\"];\r\n\r\nfunction B(e, n, t = \"keyword\") {\r\n\tconst a = Object.create(null);\r\n\treturn \"string\" == typeof e ? i(t, e.split(\" \")) : Array.isArray(e) ? i(t, e) : Object.keys(e).forEach((t => {\r\n\t\tObject.assign(a, B(e[t], n, t))\r\n\t})), a;\r\n\r\n\tfunction i(e, t) {\r\n\t\tn && (t = t.map((e => e.toLowerCase()))), t.forEach((n => {\r\n\t\t\tconst t = n.split(\"|\");\r\n\t\t\ta[t[0]] = [e, $(t[0], t[1])]\r\n\t\t}))\r\n\t}\r\n}\r\n\r\nfunction $(e, n) {\r\n\treturn n ? Number(n) : (e => L.includes(e.toLowerCase()))(e) ? 0 : 1\r\n}\r\nconst z = {},\r\n\tF = e => {\r\n\t\tconsole.error(e)\r\n\t},\r\n\tU = (e, ...n) => {\r\n\t\tconsole.log(\"WARN: \" + e, ...n)\r\n\t},\r\n\tj = (e, n) => {\r\n\t\tz[`${e}/${n}`] || (console.log(`Deprecated as of ${e}. ${n}`), z[`${e}/${n}`] = !0)\r\n\t},\r\n\tP = Error();\r\n\r\nfunction K(e, n, {\r\n\tkey: t\r\n}) {\r\n\tlet a = 0;\r\n\tconst i = e[t],\r\n\t\tr = {},\r\n\t\ts = {};\r\n\tfor (let e = 1; e <= n.length; e++) s[e + a] = i[e], r[e + a] = !0, a += _(n[e - 1]);\r\n\te[t] = s, e[t]._emit = r, e[t]._multi = !0\r\n}\r\n\r\nfunction H(e) {\r\n\t(e => {\r\n\t\te.scope && \"object\" == typeof e.scope && null !== e.scope && (e.beginScope = e.scope,\r\n\t\t\tdelete e.scope)\r\n\t})(e), \"string\" == typeof e.beginScope && (e.beginScope = {\r\n\t\t_wrap: e.beginScope\r\n\t}), \"string\" == typeof e.endScope && (e.endScope = {\r\n\t\t_wrap: e.endScope\r\n\t}), (e => {\r\n\t\tif (Array.isArray(e.begin)) {\r\n\t\t\tif (e.skip || e.excludeBegin || e.returnBegin) throw F(\r\n\t\t\t\t\t\"skip, excludeBegin, returnBegin not compatible with beginScope: {}\"),\r\n\t\t\t\tP;\r\n\t\t\tif (\"object\" != typeof e.beginScope || null === e.beginScope) throw F(\"beginScope must be object\"),\r\n\t\t\t\tP;\r\n\t\t\tK(e, e.begin, {\r\n\t\t\t\tkey: \"beginScope\"\r\n\t\t\t}), e.begin = f(e.begin, {\r\n\t\t\t\tjoinWith: \"\"\r\n\t\t\t})\r\n\t\t}\r\n\t})(e), (e => {\r\n\t\tif (Array.isArray(e.end)) {\r\n\t\t\tif (e.skip || e.excludeEnd || e.returnEnd) throw F(\r\n\t\t\t\t\t\"skip, excludeEnd, returnEnd not compatible with endScope: {}\"),\r\n\t\t\t\tP;\r\n\t\t\tif (\"object\" != typeof e.endScope || null === e.endScope) throw F(\"endScope must be object\"),\r\n\t\t\t\tP;\r\n\t\t\tK(e, e.end, {\r\n\t\t\t\tkey: \"endScope\"\r\n\t\t\t}), e.end = f(e.end, {\r\n\t\t\t\tjoinWith: \"\"\r\n\t\t\t})\r\n\t\t}\r\n\t})(e)\r\n}\r\n\r\nfunction q(e) {\r\n\tfunction n(n, t) {\r\n\t\treturn RegExp(d(n), \"m\" + (e.case_insensitive ? \"i\" : \"\") + (e.unicodeRegex ? \"u\" : \"\") + (t ? \"g\" : \"\"))\r\n\t}\r\n\tclass t {\r\n\t\tconstructor() {\r\n\t\t\tthis.matchIndexes = {}, this.regexes = [], this.matchAt = 1, this.position = 0\r\n\t\t}\r\n\t\taddRule(e, n) {\r\n\t\t\tn.position = this.position++, this.matchIndexes[this.matchAt] = n, this.regexes.push([n, e]),\r\n\t\t\t\tthis.matchAt += _(e) + 1\r\n\t\t}\r\n\t\tcompile() {\r\n\t\t\t0 === this.regexes.length && (this.exec = () => null);\r\n\t\t\tconst e = this.regexes.map((e => e[1]));\r\n\t\t\tthis.matcherRe = n(f(e, {\r\n\t\t\t\tjoinWith: \"|\"\r\n\t\t\t}), !0), this.lastIndex = 0\r\n\t\t}\r\n\t\texec(e) {\r\n\t\t\tthis.matcherRe.lastIndex = this.lastIndex;\r\n\t\t\tconst n = this.matcherRe.exec(e);\r\n\t\t\tif (!n) return null;\r\n\t\t\tconst t = n.findIndex(((e, n) => n > 0 && void 0 !== e)),\r\n\t\t\t\ta = this.matchIndexes[t];\r\n\t\t\treturn n.splice(0, t), Object.assign(n, a)\r\n\t\t}\r\n\t}\r\n\tclass a {\r\n\t\tconstructor() {\r\n\t\t\tthis.rules = [], this.multiRegexes = [],\r\n\t\t\t\tthis.count = 0, this.lastIndex = 0, this.regexIndex = 0\r\n\t\t}\r\n\t\tgetMatcher(e) {\r\n\t\t\tif (this.multiRegexes[e]) return this.multiRegexes[e];\r\n\t\t\tconst n = new t;\r\n\t\t\treturn this.rules.slice(e).forEach((([e, t]) => n.addRule(e, t))),\r\n\t\t\t\tn.compile(), this.multiRegexes[e] = n, n\r\n\t\t}\r\n\t\tresumingScanAtSamePosition() {\r\n\t\t\treturn 0 !== this.regexIndex\r\n\t\t}\r\n\t\tconsiderAll() {\r\n\t\t\tthis.regexIndex = 0\r\n\t\t}\r\n\t\taddRule(e, n) {\r\n\t\t\tthis.rules.push([e, n]), \"begin\" === n.type && this.count++\r\n\t\t}\r\n\t\texec(e) {\r\n\t\t\tconst n = this.getMatcher(this.regexIndex);\r\n\t\t\tn.lastIndex = this.lastIndex;\r\n\t\t\tlet t = n.exec(e);\r\n\t\t\tif (this.resumingScanAtSamePosition())\r\n\t\t\t\tif (t && t.index === this.lastIndex);\r\n\t\t\t\telse {\r\n\t\t\t\t\tconst n = this.getMatcher(0);\r\n\t\t\t\t\tn.lastIndex = this.lastIndex + 1, t = n.exec(e)\r\n\t\t\t\t}\r\n\t\t\treturn t && (this.regexIndex += t.position + 1,\r\n\t\t\t\tthis.regexIndex === this.count && this.considerAll()), t\r\n\t\t}\r\n\t}\r\n\tif (e.compilerExtensions || (e.compilerExtensions = []),\r\n\t\te.contains && e.contains.includes(\"self\")) throw Error(\r\n\t\t\"ERR: contains `self` is not supported at the top-level of a language.  See documentation.\");\r\n\treturn e.classNameAliases = i(e.classNameAliases || {}),\r\n\t\tfunction t(r, s) {\r\n\t\t\tconst o = r;\r\n\t\t\tif (r.isCompiled) return o;\r\n\t\t\t[A, R, H, I].forEach((e => e(r, s))), e.compilerExtensions.forEach((e => e(r, s))),\r\n\t\t\t\tr.__beforeBegin = null, [C, T, D].forEach((e => e(r, s))), r.isCompiled = !0;\r\n\t\t\tlet l = null;\r\n\t\t\treturn \"object\" == typeof r.keywords && r.keywords.$pattern && (r.keywords = Object.assign({}, r.keywords),\r\n\t\t\t\t\tl = r.keywords.$pattern,\r\n\t\t\t\t\tdelete r.keywords.$pattern), l = l || /\\w+/, r.keywords && (r.keywords = B(r.keywords, e.case_insensitive)),\r\n\t\t\t\to.keywordPatternRe = n(l, !0),\r\n\t\t\t\ts && (r.begin || (r.begin = /\\B|\\b/), o.beginRe = n(o.begin), r.end || r.endsWithParent || (r.end = /\\B|\\b/),\r\n\t\t\t\t\tr.end && (o.endRe = n(o.end)),\r\n\t\t\t\t\to.terminatorEnd = d(o.end) || \"\", r.endsWithParent && s.terminatorEnd && (o.terminatorEnd += (r.end ? \"|\" :\r\n\t\t\t\t\t\t\"\") + s.terminatorEnd)),\r\n\t\t\t\tr.illegal && (o.illegalRe = n(r.illegal)),\r\n\t\t\t\tr.contains || (r.contains = []), r.contains = [].concat(...r.contains.map((e => (e => (e.variants && !e\r\n\t\t\t\t\t.cachedVariants && (e.cachedVariants = e.variants.map((n => i(e, {\r\n\t\t\t\t\t\tvariants: null\r\n\t\t\t\t\t}, n)))), e.cachedVariants ? e.cachedVariants : Z(e) ? i(e, {\r\n\t\t\t\t\t\tstarts: e.starts ? i(e.starts) : null\r\n\t\t\t\t\t}) : Object.isFrozen(e) ? i(e) : e))(\"self\" === e ? r : e)))), r.contains.forEach((e => {\r\n\t\t\t\t\tt(e, o)\r\n\t\t\t\t})), r.starts && t(r.starts, s), o.matcher = (e => {\r\n\t\t\t\t\tconst n = new a;\r\n\t\t\t\t\treturn e.contains.forEach((e => n.addRule(e.begin, {\r\n\t\t\t\t\t\trule: e,\r\n\t\t\t\t\t\ttype: \"begin\"\r\n\t\t\t\t\t}))), e.terminatorEnd && n.addRule(e.terminatorEnd, {\r\n\t\t\t\t\t\ttype: \"end\"\r\n\t\t\t\t\t}), e.illegal && n.addRule(e.illegal, {\r\n\t\t\t\t\t\ttype: \"illegal\"\r\n\t\t\t\t\t}), n\r\n\t\t\t\t})(o), o\r\n\t\t}(e)\r\n}\r\n\r\nfunction Z(e) {\r\n\treturn !!e && (e.endsWithParent || Z(e.starts))\r\n}\r\nclass G extends Error {\r\n\tconstructor(e, n) {\r\n\t\tsuper(e), this.name = \"HTMLInjectionError\", this.html = n\r\n\t}\r\n}\r\nconst W = a,\r\n\tQ = i,\r\n\tX = Symbol(\"nomatch\");\r\nvar V = (n => {\r\n\tconst a = Object.create(null),\r\n\t\ti = Object.create(null),\r\n\t\tr = [];\r\n\tlet s = !0;\r\n\tconst o = \"Could not find the language '{}', did you forget to load/include a language module?\",\r\n\t\tl = {\r\n\t\t\tdisableAutodetect: !0,\r\n\t\t\tname: \"Plain text\",\r\n\t\t\tcontains: []\r\n\t\t};\r\n\tlet d = {\r\n\t\tignoreUnescapedHTML: !1,\r\n\t\tthrowUnescapedHTML: !1,\r\n\t\tnoHighlightRe: /^(no-?highlight)$/i,\r\n\t\tlanguageDetectRe: /\\blang(?:uage)?-([\\w-]+)\\b/i,\r\n\t\tclassPrefix: \"hljs-\",\r\n\t\tcssSelector: \"pre code\",\r\n\t\tlanguages: null,\r\n\t\t__emitter: c\r\n\t};\r\n\r\n\tfunction _(e) {\r\n\t\treturn d.noHighlightRe.test(e)\r\n\t}\r\n\r\n\tfunction h(e, n, t) {\r\n\t\tlet a = \"\",\r\n\t\t\ti = \"\";\r\n\t\t\"object\" == typeof n ? (a = e,\r\n\t\t\tt = n.ignoreIllegals, i = n.language) : (j(\"10.7.0\", \"highlight(lang, code, ...args) has been deprecated.\"),\r\n\t\t\tj(\"10.7.0\",\r\n\t\t\t\t\"Please use highlight(code, options) instead.\\nhttps://github.com/highlightjs/highlight.js/issues/2277\"),\r\n\t\t\ti = e, a = n), void 0 === t && (t = !0);\r\n\t\tconst r = {\r\n\t\t\tcode: a,\r\n\t\t\tlanguage: i\r\n\t\t};\r\n\t\tx(\"before:highlight\", r);\r\n\t\tconst s = r.result ? r.result : f(r.language, r.code, t);\r\n\t\treturn s.code = r.code, x(\"after:highlight\", s), s\r\n\t}\r\n\r\n\tfunction f(e, n, i, r) {\r\n\t\tconst l = Object.create(null);\r\n\r\n\t\tfunction c() {\r\n\t\t\tif (!k.keywords) return void M.addText(S);\r\n\t\t\tlet e = 0;\r\n\t\t\tk.keywordPatternRe.lastIndex = 0;\r\n\t\t\tlet n = k.keywordPatternRe.exec(S),\r\n\t\t\t\tt = \"\";\r\n\t\t\tfor (; n;) {\r\n\t\t\t\tt += S.substring(e, n.index);\r\n\t\t\t\tconst i = w.case_insensitive ? n[0].toLowerCase() : n[0],\r\n\t\t\t\t\tr = (a = i, k.keywords[a]);\r\n\t\t\t\tif (r) {\r\n\t\t\t\t\tconst [e, a] = r\r\n\t\t\t\t\t;\r\n\t\t\t\t\tif (M.addText(t), t = \"\", l[i] = (l[i] || 0) + 1, l[i] <= 7 && (A += a), e.startsWith(\"_\")) t += n[0];\r\n\t\t\t\t\telse {\r\n\t\t\t\t\t\tconst t = w.classNameAliases[e] || e;\r\n\t\t\t\t\t\tM.addKeyword(n[0], t)\r\n\t\t\t\t\t}\r\n\t\t\t\t} else t += n[0];\r\n\t\t\t\te = k.keywordPatternRe.lastIndex, n = k.keywordPatternRe.exec(S)\r\n\t\t\t}\r\n\t\t\tvar a;\r\n\t\t\tt += S.substring(e), M.addText(t)\r\n\t\t}\r\n\r\n\t\tfunction g() {\r\n\t\t\tnull != k.subLanguage ? (() => {\r\n\t\t\t\tif (\"\" === S) return;\r\n\t\t\t\tlet e = null;\r\n\t\t\t\tif (\"string\" == typeof k.subLanguage) {\r\n\t\t\t\t\tif (!a[k.subLanguage]) return void M.addText(S);\r\n\t\t\t\t\te = f(k.subLanguage, S, !0, x[k.subLanguage]), x[k.subLanguage] = e._top\r\n\t\t\t\t} else e = E(S, k.subLanguage.length ? k.subLanguage : null);\r\n\t\t\t\tk.relevance > 0 && (A += e.relevance), M.addSublanguage(e._emitter, e.language)\r\n\t\t\t})() : c(), S = \"\"\r\n\t\t}\r\n\r\n\t\tfunction u(e, n) {\r\n\t\t\tlet t = 1;\r\n\t\t\tconst a = n.length - 1;\r\n\t\t\tfor (; t <= a;) {\r\n\t\t\t\tif (!e._emit[t]) {\r\n\t\t\t\t\tt++;\r\n\t\t\t\t\tcontinue\r\n\t\t\t\t}\r\n\t\t\t\tconst a = w.classNameAliases[e[t]] || e[t],\r\n\t\t\t\t\ti = n[t];\r\n\t\t\t\ta ? M.addKeyword(i, a) : (S = i, c(), S = \"\"), t++\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tfunction b(e, n) {\r\n\t\t\treturn e.scope && \"string\" == typeof e.scope && M.openNode(w.classNameAliases[e.scope] || e.scope),\r\n\t\t\t\te.beginScope && (e.beginScope._wrap ? (M.addKeyword(S, w.classNameAliases[e.beginScope._wrap] || e\r\n\t\t\t\t\t\t.beginScope._wrap),\r\n\t\t\t\t\tS = \"\") : e.beginScope._multi && (u(e.beginScope, n), S = \"\")), k = Object.create(e, {\r\n\t\t\t\t\tparent: {\r\n\t\t\t\t\t\tvalue: k\r\n\t\t\t\t\t}\r\n\t\t\t\t}), k\r\n\t\t}\r\n\r\n\t\tfunction m(e, n, a) {\r\n\t\t\tlet i = ((e, n) => {\r\n\t\t\t\tconst t = e && e.exec(n);\r\n\t\t\t\treturn t && 0 === t.index\r\n\t\t\t})(e.endRe, a);\r\n\t\t\tif (i) {\r\n\t\t\t\tif (e[\"on:end\"]) {\r\n\t\t\t\t\tconst a = new t(e);\r\n\t\t\t\t\te[\"on:end\"](n, a), a.isMatchIgnored && (i = !1)\r\n\t\t\t\t}\r\n\t\t\t\tif (i) {\r\n\t\t\t\t\tfor (; e.endsParent && e.parent;) e = e.parent;\r\n\t\t\t\t\treturn e\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tif (e.endsWithParent) return m(e.parent, n, a)\r\n\t\t}\r\n\r\n\t\tfunction p(e) {\r\n\t\t\treturn 0 === k.matcher.regexIndex ? (S += e[0], 1) : (R = !0, 0)\r\n\t\t}\r\n\r\n\t\tfunction _(e) {\r\n\t\t\tconst t = e[0],\r\n\t\t\t\ta = n.substring(e.index),\r\n\t\t\t\ti = m(k, e, a);\r\n\t\t\tif (!i) return X;\r\n\t\t\tconst r = k;\r\n\t\t\tk.endScope && k.endScope._wrap ? (g(),\r\n\t\t\t\tM.addKeyword(t, k.endScope._wrap)) : k.endScope && k.endScope._multi ? (g(),\r\n\t\t\t\tu(k.endScope, e)) : r.skip ? S += t : (r.returnEnd || r.excludeEnd || (S += t),\r\n\t\t\t\tg(), r.excludeEnd && (S = t));\r\n\t\t\tdo {\r\n\t\t\t\tk.scope && M.closeNode(), k.skip || k.subLanguage || (A += k.relevance), k = k.parent\r\n\t\t\t} while (k !== i.parent);\r\n\t\t\treturn i.starts && b(i.starts, e), r.returnEnd ? 0 : t.length\r\n\t\t}\r\n\t\tlet h = {};\r\n\r\n\t\tfunction y(a, r) {\r\n\t\t\tconst o = r && r[0];\r\n\t\t\tif (S += a, null == o) return g(), 0;\r\n\t\t\tif (\"begin\" === h.type && \"end\" === r.type && h.index === r.index && \"\" === o) {\r\n\t\t\t\tif (S += n.slice(r.index, r.index + 1), !s) {\r\n\t\t\t\t\tconst n = Error(`0 width match regex (${e})`);\r\n\t\t\t\t\tthrow n.languageName = e, n.badRule = h.rule, n\r\n\t\t\t\t}\r\n\t\t\t\treturn 1\r\n\t\t\t}\r\n\t\t\tif (h = r, \"begin\" === r.type) return (e => {\r\n\t\t\t\tconst n = e[0],\r\n\t\t\t\t\ta = e.rule,\r\n\t\t\t\t\ti = new t(a),\r\n\t\t\t\t\tr = [a.__beforeBegin, a[\"on:begin\"]];\r\n\t\t\t\tfor (const t of r)\r\n\t\t\t\t\tif (t && (t(e, i), i.isMatchIgnored)) return p(n);\r\n\t\t\t\treturn a.skip ? S += n : (a.excludeBegin && (S += n),\r\n\t\t\t\t\tg(), a.returnBegin || a.excludeBegin || (S = n)), b(a, e), a.returnBegin ? 0 : n.length\r\n\t\t\t})(r);\r\n\t\t\tif (\"illegal\" === r.type && !i) {\r\n\t\t\t\tconst e = Error('Illegal lexeme \"' + o + '\" for mode \"' + (k.scope || \"<unnamed>\") + '\"');\r\n\t\t\t\tthrow e.mode = k, e\r\n\t\t\t}\r\n\t\t\tif (\"end\" === r.type) {\r\n\t\t\t\tconst e = _(r);\r\n\t\t\t\tif (e !== X) return e\r\n\t\t\t}\r\n\t\t\tif (\"illegal\" === r.type && \"\" === o) return 1;\r\n\t\t\tif (T > 1e5 && T > 3 * r.index) throw Error(\"potential infinite loop, way more iterations than matches\");\r\n\t\t\treturn S += o, o.length\r\n\t\t}\r\n\t\tconst w = v(e);\r\n\t\tif (!w) throw F(o.replace(\"{}\", e)), Error('Unknown language: \"' + e + '\"');\r\n\t\tconst N = q(w);\r\n\t\tlet O = \"\",\r\n\t\t\tk = r || N;\r\n\t\tconst x = {},\r\n\t\t\tM = new d.__emitter(d);\r\n\t\t(() => {\r\n\t\t\tconst e = [];\r\n\t\t\tfor (let n = k; n !== w; n = n.parent) n.scope && e.unshift(n.scope);\r\n\t\t\te.forEach((e => M.openNode(e)))\r\n\t\t})();\r\n\t\tlet S = \"\",\r\n\t\t\tA = 0,\r\n\t\t\tC = 0,\r\n\t\t\tT = 0,\r\n\t\t\tR = !1;\r\n\t\ttry {\r\n\t\t\tfor (k.matcher.considerAll();;) {\r\n\t\t\t\tT++, R ? R = !1 : k.matcher.considerAll(), k.matcher.lastIndex = C;\r\n\t\t\t\tconst e = k.matcher.exec(n);\r\n\t\t\t\tif (!e) break;\r\n\t\t\t\tconst t = y(n.substring(C, e.index), e);\r\n\t\t\t\tC = e.index + t\r\n\t\t\t}\r\n\t\t\treturn y(n.substring(C)), M.closeAllNodes(), M.finalize(), O = M.toHTML(), {\r\n\t\t\t\tlanguage: e,\r\n\t\t\t\tvalue: O,\r\n\t\t\t\trelevance: A,\r\n\t\t\t\tillegal: !1,\r\n\t\t\t\t_emitter: M,\r\n\t\t\t\t_top: k\r\n\t\t\t}\r\n\t\t} catch (t) {\r\n\t\t\tif (t.message && t.message.includes(\"Illegal\")) return {\r\n\t\t\t\tlanguage: e,\r\n\t\t\t\tvalue: W(n),\r\n\t\t\t\tillegal: !0,\r\n\t\t\t\trelevance: 0,\r\n\t\t\t\t_illegalBy: {\r\n\t\t\t\t\tmessage: t.message,\r\n\t\t\t\t\tindex: C,\r\n\t\t\t\t\tcontext: n.slice(C - 100, C + 100),\r\n\t\t\t\t\tmode: t.mode,\r\n\t\t\t\t\tresultSoFar: O\r\n\t\t\t\t},\r\n\t\t\t\t_emitter: M\r\n\t\t\t};\r\n\t\t\tif (s) return {\r\n\t\t\t\tlanguage: e,\r\n\t\t\t\tvalue: W(n),\r\n\t\t\t\tillegal: !1,\r\n\t\t\t\trelevance: 0,\r\n\t\t\t\terrorRaised: t,\r\n\t\t\t\t_emitter: M,\r\n\t\t\t\t_top: k\r\n\t\t\t};\r\n\t\t\tthrow t\r\n\t\t}\r\n\t}\r\n\r\n\tfunction E(e, n) {\r\n\t\tn = n || d.languages || Object.keys(a);\r\n\t\tconst t = (e => {\r\n\t\t\t\tconst n = {\r\n\t\t\t\t\tvalue: W(e),\r\n\t\t\t\t\tillegal: !1,\r\n\t\t\t\t\trelevance: 0,\r\n\t\t\t\t\t_top: l,\r\n\t\t\t\t\t_emitter: new d.__emitter(d)\r\n\t\t\t\t};\r\n\t\t\t\treturn n._emitter.addText(e), n\r\n\t\t\t})(e),\r\n\t\t\ti = n.filter(v).filter(k).map((n => f(n, e, !1)));\r\n\t\ti.unshift(t);\r\n\t\tconst r = i.sort(((e, n) => {\r\n\t\t\t\tif (e.relevance !== n.relevance) return n.relevance - e.relevance;\r\n\t\t\t\tif (e.language && n.language) {\r\n\t\t\t\t\tif (v(e.language).supersetOf === n.language) return 1;\r\n\t\t\t\t\tif (v(n.language).supersetOf === e.language) return -1\r\n\t\t\t\t}\r\n\t\t\t\treturn 0\r\n\t\t\t})),\r\n\t\t\t[s, o] = r,\r\n\t\t\tc = s;\r\n\t\treturn c.secondBest = o, c\r\n\t}\r\n\r\n\tfunction y(e) {\r\n\t\tlet n = null;\r\n\t\tconst t = (e => {\r\n\t\t\tlet n = e.className + \" \";\r\n\t\t\tn += e.parentNode ? e.parentNode.className : \"\";\r\n\t\t\tconst t = d.languageDetectRe.exec(n);\r\n\t\t\tif (t) {\r\n\t\t\t\tconst n = v(t[1]);\r\n\t\t\t\treturn n || (U(o.replace(\"{}\", t[1])),\r\n\t\t\t\t\tU(\"Falling back to no-highlight mode for this block.\", e)), n ? t[1] : \"no-highlight\"\r\n\t\t\t}\r\n\t\t\treturn n.split(/\\s+/).find((e => _(e) || v(e)))\r\n\t\t})(e);\r\n\t\tif (_(t)) return;\r\n\t\tif (x(\"before:highlightElement\", {\r\n\t\t\t\tel: e,\r\n\t\t\t\tlanguage: t\r\n\t\t\t}), e.children.length > 0 && (d.ignoreUnescapedHTML || (console.warn(\r\n\t\t\t\t\t\"One of your code blocks includes unescaped HTML. This is a potentially serious security risk.\"),\r\n\t\t\t\tconsole.warn(\"https://github.com/highlightjs/highlight.js/wiki/security\"),\r\n\t\t\t\tconsole.warn(\"The element with unescaped HTML:\"),\r\n\t\t\t\tconsole.warn(e)), d.throwUnescapedHTML)) throw new G(\"One of your code blocks includes unescaped HTML.\", e\r\n\t\t\t.innerHTML);\r\n\t\tn = e;\r\n\t\tconst a = n.textContent,\r\n\t\t\tr = t ? h(a, {\r\n\t\t\t\tlanguage: t,\r\n\t\t\t\tignoreIllegals: !0\r\n\t\t\t}) : E(a);\r\n\t\te.innerHTML = r.value, ((e, n, t) => {\r\n\t\t\tconst a = n && i[n] || t;\r\n\t\t\te.classList.add(\"hljs\"), e.classList.add(\"language-\" + a)\r\n\t\t})(e, t, r.language), e.result = {\r\n\t\t\tlanguage: r.language,\r\n\t\t\tre: r.relevance,\r\n\t\t\trelevance: r.relevance\r\n\t\t}, r.secondBest && (e.secondBest = {\r\n\t\t\tlanguage: r.secondBest.language,\r\n\t\t\trelevance: r.secondBest.relevance\r\n\t\t}), x(\"after:highlightElement\", {\r\n\t\t\tel: e,\r\n\t\t\tresult: r,\r\n\t\t\ttext: a\r\n\t\t})\r\n\t}\r\n\tlet w = !1;\r\n\r\n\tfunction N() {\r\n\t\t\"loading\" !== document.readyState ? document.querySelectorAll(d.cssSelector).forEach(y) : w = !0\r\n\t}\r\n\r\n\tfunction v(e) {\r\n\t\treturn e = (e || \"\").toLowerCase(), a[e] || a[i[e]]\r\n\t}\r\n\r\n\tfunction O(e, {\r\n\t\tlanguageName: n\r\n\t}) {\r\n\t\t\"string\" == typeof e && (e = [e]), e.forEach((e => {\r\n\t\t\ti[e.toLowerCase()] = n\r\n\t\t}))\r\n\t}\r\n\r\n\tfunction k(e) {\r\n\t\tconst n = v(e);\r\n\t\treturn n && !n.disableAutodetect\r\n\t}\r\n\r\n\tfunction x(e, n) {\r\n\t\tconst t = e;\r\n\t\tr.forEach((e => {\r\n\t\t\te[t] && e[t](n)\r\n\t\t}))\r\n\t}\r\n\t\"undefined\" != typeof window && window.addEventListener && window.addEventListener(\"DOMContentLoaded\", (() => {\r\n\t\tw && N()\r\n\t}), !1), Object.assign(n, {\r\n\t\thighlight: h,\r\n\t\thighlightAuto: E,\r\n\t\thighlightAll: N,\r\n\t\thighlightElement: y,\r\n\t\thighlightBlock: e => (j(\"10.7.0\", \"highlightBlock will be removed entirely in v12.0\"),\r\n\t\t\tj(\"10.7.0\", \"Please use highlightElement now.\"), y(e)),\r\n\t\tconfigure: e => {\r\n\t\t\td = Q(d, e)\r\n\t\t},\r\n\t\tinitHighlighting: () => {\r\n\t\t\tN(), j(\"10.6.0\", \"initHighlighting() deprecated.  Use highlightAll() now.\")\r\n\t\t},\r\n\t\tinitHighlightingOnLoad: () => {\r\n\t\t\tN(), j(\"10.6.0\", \"initHighlightingOnLoad() deprecated.  Use highlightAll() now.\")\r\n\t\t},\r\n\t\tregisterLanguage: (e, t) => {\r\n\t\t\tlet i = null;\r\n\t\t\ttry {\r\n\t\t\t\ti = t(n)\r\n\t\t\t} catch (n) {\r\n\t\t\t\tif (F(\"Language definition for '{}' could not be registered.\".replace(\"{}\", e)),\r\n\t\t\t\t\t!s) throw n;\r\n\t\t\t\tF(n), i = l\r\n\t\t\t}\r\n\t\t\ti.name || (i.name = e), a[e] = i, i.rawDefinition = t.bind(null, n), i.aliases && O(i.aliases, {\r\n\t\t\t\tlanguageName: e\r\n\t\t\t})\r\n\t\t},\r\n\t\tunregisterLanguage: e => {\r\n\t\t\tdelete a[e];\r\n\t\t\tfor (const n of Object.keys(i)) i[n] === e && delete i[n]\r\n\t\t},\r\n\t\tlistLanguages: () => Object.keys(a),\r\n\t\tgetLanguage: v,\r\n\t\tregisterAliases: O,\r\n\t\tautoDetection: k,\r\n\t\tinherit: Q,\r\n\t\taddPlugin: e => {\r\n\t\t\t(e => {\r\n\t\t\t\te[\"before:highlightBlock\"] && !e[\"before:highlightElement\"] && (e[\"before:highlightElement\"] =\r\n\t\t\t\t\tn => {\r\n\t\t\t\t\t\te[\"before:highlightBlock\"](Object.assign({\r\n\t\t\t\t\t\t\tblock: n.el\r\n\t\t\t\t\t\t}, n))\r\n\t\t\t\t\t}), e[\"after:highlightBlock\"] && !e[\"after:highlightElement\"] && (e[\"after:highlightElement\"] =\r\n\t\t\t\t\tn => {\r\n\t\t\t\t\t\te[\"after:highlightBlock\"](Object.assign({\r\n\t\t\t\t\t\t\tblock: n.el\r\n\t\t\t\t\t\t}, n))\r\n\t\t\t\t\t})\r\n\t\t\t})(e), r.push(e)\r\n\t\t}\r\n\t}), n.debugMode = () => {\r\n\t\ts = !1\r\n\t}, n.safeMode = () => {\r\n\t\ts = !0\r\n\t}, n.versionString = \"11.7.0\", n.regex = {\r\n\t\tconcat: m,\r\n\t\tlookahead: g,\r\n\t\teither: p,\r\n\t\toptional: b,\r\n\t\tanyNumberOfTimes: u\r\n\t};\r\n\tfor (const n in M) \"object\" == typeof M[n] && e.exports(M[n]);\r\n\treturn Object.assign(n, M), n\r\n})({});\r\nconst J = e => ({\r\n\t\tIMPORTANT: {\r\n\t\t\tscope: \"meta\",\r\n\t\t\tbegin: \"!important\"\r\n\t\t},\r\n\t\tBLOCK_COMMENT: e.C_BLOCK_COMMENT_MODE,\r\n\t\tHEXCOLOR: {\r\n\t\t\tscope: \"number\",\r\n\t\t\tbegin: /#(([0-9a-fA-F]{3,4})|(([0-9a-fA-F]{2}){3,4}))\\b/\r\n\t\t},\r\n\t\tFUNCTION_DISPATCH: {\r\n\t\t\tclassName: \"built_in\",\r\n\t\t\tbegin: /[\\w-]+(?=\\()/\r\n\t\t},\r\n\t\tATTRIBUTE_SELECTOR_MODE: {\r\n\t\t\tscope: \"selector-attr\",\r\n\t\t\tbegin: /\\[/,\r\n\t\t\tend: /\\]/,\r\n\t\t\tillegal: \"$\",\r\n\t\t\tcontains: [e.APOS_STRING_MODE, e.QUOTE_STRING_MODE]\r\n\t\t},\r\n\t\tCSS_NUMBER_MODE: {\r\n\t\t\tscope: \"number\",\r\n\t\t\tbegin: e.NUMBER_RE +\r\n\t\t\t\t\"(%|em|ex|ch|rem|vw|vh|vmin|vmax|cm|mm|in|pt|pc|px|deg|grad|rad|turn|s|ms|Hz|kHz|dpi|dpcm|dppx)?\",\r\n\t\t\trelevance: 0\r\n\t\t},\r\n\t\tCSS_VARIABLE: {\r\n\t\t\tclassName: \"attr\",\r\n\t\t\tbegin: /--[A-Za-z][A-Za-z0-9_-]*/\r\n\t\t}\r\n\t}),\r\n\tY = [\"a\", \"abbr\", \"address\", \"article\", \"aside\", \"audio\", \"b\", \"blockquote\", \"body\", \"button\", \"canvas\", \"caption\",\r\n\t\t\"cite\", \"code\", \"dd\", \"del\", \"details\", \"dfn\", \"div\", \"dl\", \"dt\", \"em\", \"fieldset\", \"figcaption\", \"figure\",\r\n\t\t\"footer\", \"form\", \"h1\", \"h2\", \"h3\", \"h4\", \"h5\", \"h6\", \"header\", \"hgroup\", \"html\", \"i\", \"iframe\", \"img\", \"input\",\r\n\t\t\"ins\", \"kbd\", \"label\", \"legend\", \"li\", \"main\", \"mark\", \"menu\", \"nav\", \"object\", \"ol\", \"p\", \"q\", \"quote\", \"samp\",\r\n\t\t\"section\", \"span\", \"strong\", \"summary\", \"sup\", \"table\", \"tbody\", \"td\", \"textarea\", \"tfoot\", \"th\", \"thead\", \"time\",\r\n\t\t\"tr\", \"ul\", \"var\", \"video\"\r\n\t],\r\n\tee = [\"any-hover\", \"any-pointer\", \"aspect-ratio\", \"color\", \"color-gamut\", \"color-index\", \"device-aspect-ratio\",\r\n\t\t\"device-height\", \"device-width\", \"display-mode\", \"forced-colors\", \"grid\", \"height\", \"hover\", \"inverted-colors\",\r\n\t\t\"monochrome\", \"orientation\", \"overflow-block\", \"overflow-inline\", \"pointer\", \"prefers-color-scheme\",\r\n\t\t\"prefers-contrast\", \"prefers-reduced-motion\", \"prefers-reduced-transparency\", \"resolution\", \"scan\", \"scripting\",\r\n\t\t\"update\", \"width\", \"min-width\", \"max-width\", \"min-height\", \"max-height\"\r\n\t],\r\n\tne = [\"active\", \"any-link\", \"blank\", \"checked\", \"current\", \"default\", \"defined\", \"dir\", \"disabled\", \"drop\", \"empty\",\r\n\t\t\"enabled\", \"first\", \"first-child\", \"first-of-type\", \"fullscreen\", \"future\", \"focus\", \"focus-visible\",\r\n\t\t\"focus-within\", \"has\", \"host\", \"host-context\", \"hover\", \"indeterminate\", \"in-range\", \"invalid\", \"is\", \"lang\",\r\n\t\t\"last-child\", \"last-of-type\", \"left\", \"link\", \"local-link\", \"not\", \"nth-child\", \"nth-col\", \"nth-last-child\",\r\n\t\t\"nth-last-col\", \"nth-last-of-type\", \"nth-of-type\", \"only-child\", \"only-of-type\", \"optional\", \"out-of-range\", \"past\",\r\n\t\t\"placeholder-shown\", \"read-only\", \"read-write\", \"required\", \"right\", \"root\", \"scope\", \"target\", \"target-within\",\r\n\t\t\"user-invalid\", \"valid\", \"visited\", \"where\"\r\n\t],\r\n\tte = [\"after\", \"backdrop\", \"before\", \"cue\", \"cue-region\", \"first-letter\", \"first-line\", \"grammar-error\", \"marker\",\r\n\t\t\"part\", \"placeholder\", \"selection\", \"slotted\", \"spelling-error\"\r\n\t],\r\n\tae = [\"align-content\", \"align-items\", \"align-self\", \"all\", \"animation\", \"animation-delay\", \"animation-direction\",\r\n\t\t\"animation-duration\", \"animation-fill-mode\", \"animation-iteration-count\", \"animation-name\", \"animation-play-state\",\r\n\t\t\"animation-timing-function\", \"backface-visibility\", \"background\", \"background-attachment\", \"background-blend-mode\",\r\n\t\t\"background-clip\", \"background-color\", \"background-image\", \"background-origin\", \"background-position\",\r\n\t\t\"background-repeat\", \"background-size\", \"block-size\", \"border\", \"border-block\", \"border-block-color\",\r\n\t\t\"border-block-end\", \"border-block-end-color\", \"border-block-end-style\", \"border-block-end-width\",\r\n\t\t\"border-block-start\", \"border-block-start-color\", \"border-block-start-style\", \"border-block-start-width\",\r\n\t\t\"border-block-style\", \"border-block-width\", \"border-bottom\", \"border-bottom-color\", \"border-bottom-left-radius\",\r\n\t\t\"border-bottom-right-radius\", \"border-bottom-style\", \"border-bottom-width\", \"border-collapse\", \"border-color\",\r\n\t\t\"border-image\", \"border-image-outset\", \"border-image-repeat\", \"border-image-slice\", \"border-image-source\",\r\n\t\t\"border-image-width\", \"border-inline\", \"border-inline-color\", \"border-inline-end\", \"border-inline-end-color\",\r\n\t\t\"border-inline-end-style\", \"border-inline-end-width\", \"border-inline-start\", \"border-inline-start-color\",\r\n\t\t\"border-inline-start-style\", \"border-inline-start-width\", \"border-inline-style\", \"border-inline-width\",\r\n\t\t\"border-left\", \"border-left-color\", \"border-left-style\", \"border-left-width\", \"border-radius\", \"border-right\",\r\n\t\t\"border-right-color\", \"border-right-style\", \"border-right-width\", \"border-spacing\", \"border-style\", \"border-top\",\r\n\t\t\"border-top-color\", \"border-top-left-radius\", \"border-top-right-radius\", \"border-top-style\", \"border-top-width\",\r\n\t\t\"border-width\", \"bottom\", \"box-decoration-break\", \"box-shadow\", \"box-sizing\", \"break-after\", \"break-before\",\r\n\t\t\"break-inside\", \"caption-side\", \"caret-color\", \"clear\", \"clip\", \"clip-path\", \"clip-rule\", \"color\", \"column-count\",\r\n\t\t\"column-fill\", \"column-gap\", \"column-rule\", \"column-rule-color\", \"column-rule-style\", \"column-rule-width\",\r\n\t\t\"column-span\", \"column-width\", \"columns\", \"contain\", \"content\", \"content-visibility\", \"counter-increment\",\r\n\t\t\"counter-reset\", \"cue\", \"cue-after\", \"cue-before\", \"cursor\", \"direction\", \"display\", \"empty-cells\", \"filter\",\r\n\t\t\"flex\", \"flex-basis\", \"flex-direction\", \"flex-flow\", \"flex-grow\", \"flex-shrink\", \"flex-wrap\", \"float\", \"flow\",\r\n\t\t\"font\", \"font-display\", \"font-family\", \"font-feature-settings\", \"font-kerning\", \"font-language-override\",\r\n\t\t\"font-size\", \"font-size-adjust\", \"font-smoothing\", \"font-stretch\", \"font-style\", \"font-synthesis\", \"font-variant\",\r\n\t\t\"font-variant-caps\", \"font-variant-east-asian\", \"font-variant-ligatures\", \"font-variant-numeric\",\r\n\t\t\"font-variant-position\", \"font-variation-settings\", \"font-weight\", \"gap\", \"glyph-orientation-vertical\", \"grid\",\r\n\t\t\"grid-area\", \"grid-auto-columns\", \"grid-auto-flow\", \"grid-auto-rows\", \"grid-column\", \"grid-column-end\",\r\n\t\t\"grid-column-start\", \"grid-gap\", \"grid-row\", \"grid-row-end\", \"grid-row-start\", \"grid-template\",\r\n\t\t\"grid-template-areas\", \"grid-template-columns\", \"grid-template-rows\", \"hanging-punctuation\", \"height\", \"hyphens\",\r\n\t\t\"icon\", \"image-orientation\", \"image-rendering\", \"image-resolution\", \"ime-mode\", \"inline-size\", \"isolation\",\r\n\t\t\"justify-content\", \"left\", \"letter-spacing\", \"line-break\", \"line-height\", \"list-style\", \"list-style-image\",\r\n\t\t\"list-style-position\", \"list-style-type\", \"margin\", \"margin-block\", \"margin-block-end\", \"margin-block-start\",\r\n\t\t\"margin-bottom\", \"margin-inline\", \"margin-inline-end\", \"margin-inline-start\", \"margin-left\", \"margin-right\",\r\n\t\t\"margin-top\", \"marks\", \"mask\", \"mask-border\", \"mask-border-mode\", \"mask-border-outset\", \"mask-border-repeat\",\r\n\t\t\"mask-border-slice\", \"mask-border-source\", \"mask-border-width\", \"mask-clip\", \"mask-composite\", \"mask-image\",\r\n\t\t\"mask-mode\", \"mask-origin\", \"mask-position\", \"mask-repeat\", \"mask-size\", \"mask-type\", \"max-block-size\",\r\n\t\t\"max-height\", \"max-inline-size\", \"max-width\", \"min-block-size\", \"min-height\", \"min-inline-size\", \"min-width\",\r\n\t\t\"mix-blend-mode\", \"nav-down\", \"nav-index\", \"nav-left\", \"nav-right\", \"nav-up\", \"none\", \"normal\", \"object-fit\",\r\n\t\t\"object-position\", \"opacity\", \"order\", \"orphans\", \"outline\", \"outline-color\", \"outline-offset\", \"outline-style\",\r\n\t\t\"outline-width\", \"overflow\", \"overflow-wrap\", \"overflow-x\", \"overflow-y\", \"padding\", \"padding-block\",\r\n\t\t\"padding-block-end\", \"padding-block-start\", \"padding-bottom\", \"padding-inline\", \"padding-inline-end\",\r\n\t\t\"padding-inline-start\", \"padding-left\", \"padding-right\", \"padding-top\", \"page-break-after\", \"page-break-before\",\r\n\t\t\"page-break-inside\", \"pause\", \"pause-after\", \"pause-before\", \"perspective\", \"perspective-origin\", \"pointer-events\",\r\n\t\t\"position\", \"quotes\", \"resize\", \"rest\", \"rest-after\", \"rest-before\", \"right\", \"row-gap\", \"scroll-margin\",\r\n\t\t\"scroll-margin-block\", \"scroll-margin-block-end\", \"scroll-margin-block-start\", \"scroll-margin-bottom\",\r\n\t\t\"scroll-margin-inline\", \"scroll-margin-inline-end\", \"scroll-margin-inline-start\", \"scroll-margin-left\",\r\n\t\t\"scroll-margin-right\", \"scroll-margin-top\", \"scroll-padding\", \"scroll-padding-block\", \"scroll-padding-block-end\",\r\n\t\t\"scroll-padding-block-start\", \"scroll-padding-bottom\", \"scroll-padding-inline\", \"scroll-padding-inline-end\",\r\n\t\t\"scroll-padding-inline-start\", \"scroll-padding-left\", \"scroll-padding-right\", \"scroll-padding-top\",\r\n\t\t\"scroll-snap-align\", \"scroll-snap-stop\", \"scroll-snap-type\", \"scrollbar-color\", \"scrollbar-gutter\",\r\n\t\t\"scrollbar-width\", \"shape-image-threshold\", \"shape-margin\", \"shape-outside\", \"speak\", \"speak-as\", \"src\", \"tab-size\",\r\n\t\t\"table-layout\", \"text-align\", \"text-align-all\", \"text-align-last\", \"text-combine-upright\", \"text-decoration\",\r\n\t\t\"text-decoration-color\", \"text-decoration-line\", \"text-decoration-style\", \"text-emphasis\", \"text-emphasis-color\",\r\n\t\t\"text-emphasis-position\", \"text-emphasis-style\", \"text-indent\", \"text-justify\", \"text-orientation\", \"text-overflow\",\r\n\t\t\"text-rendering\", \"text-shadow\", \"text-transform\", \"text-underline-position\", \"top\", \"transform\", \"transform-box\",\r\n\t\t\"transform-origin\", \"transform-style\", \"transition\", \"transition-delay\", \"transition-duration\",\r\n\t\t\"transition-property\", \"transition-timing-function\", \"unicode-bidi\", \"vertical-align\", \"visibility\",\r\n\t\t\"voice-balance\", \"voice-duration\", \"voice-family\", \"voice-pitch\", \"voice-range\", \"voice-rate\", \"voice-stress\",\r\n\t\t\"voice-volume\", \"white-space\", \"widows\", \"width\", \"will-change\", \"word-break\", \"word-spacing\", \"word-wrap\",\r\n\t\t\"writing-mode\", \"z-index\"\r\n\t].reverse(),\r\n\tie = ne.concat(te);\r\nvar re = \"\\\\.([0-9](_*[0-9])*)\",\r\n\tse = \"[0-9a-fA-F](_*[0-9a-fA-F])*\",\r\n\toe = {\r\n\t\tclassName: \"number\",\r\n\t\tvariants: [{\r\n\t\t\tbegin: `(\\\\b([0-9](_*[0-9])*)((${re})|\\\\.)?|(${re}))[eE][+-]?([0-9](_*[0-9])*)[fFdD]?\\\\b`\r\n\t\t}, {\r\n\t\t\tbegin: `\\\\b([0-9](_*[0-9])*)((${re})[fFdD]?\\\\b|\\\\.([fFdD]\\\\b)?)`\r\n\t\t}, {\r\n\t\t\tbegin: `(${re})[fFdD]?\\\\b`\r\n\t\t}, {\r\n\t\t\tbegin: \"\\\\b([0-9](_*[0-9])*)[fFdD]\\\\b\"\r\n\t\t}, {\r\n\t\t\tbegin: `\\\\b0[xX]((${se})\\\\.?|(${se})?\\\\.(${se}))[pP][+-]?([0-9](_*[0-9])*)[fFdD]?\\\\b`\r\n\t\t}, {\r\n\t\t\tbegin: \"\\\\b(0|[1-9](_*[0-9])*)[lL]?\\\\b\"\r\n\t\t}, {\r\n\t\t\tbegin: `\\\\b0[xX](${se})[lL]?\\\\b`\r\n\t\t}, {\r\n\t\t\tbegin: \"\\\\b0(_*[0-7])*[lL]?\\\\b\"\r\n\t\t}, {\r\n\t\t\tbegin: \"\\\\b0[bB][01](_*[01])*[lL]?\\\\b\"\r\n\t\t}],\r\n\t\trelevance: 0\r\n\t};\r\n\r\nfunction le(e, n, t) {\r\n\treturn -1 === t ? \"\" : e.replace(n, (a => le(e, n, t - 1)))\r\n}\r\nconst ce = \"[A-Za-z$_][0-9A-Za-z$_]*\",\r\n\tde = [\"as\", \"in\", \"of\", \"if\", \"for\", \"while\", \"finally\", \"var\", \"new\", \"function\", \"do\", \"return\", \"void\", \"else\",\r\n\t\t\"break\", \"catch\", \"instanceof\", \"with\", \"throw\", \"case\", \"default\", \"try\", \"switch\", \"continue\", \"typeof\", \"delete\",\r\n\t\t\"let\", \"yield\", \"const\", \"class\", \"debugger\", \"async\", \"await\", \"static\", \"import\", \"from\", \"export\", \"extends\"\r\n\t],\r\n\tge = [\"true\", \"false\", \"null\", \"undefined\", \"NaN\", \"Infinity\"],\r\n\tue = [\"Object\", \"Function\", \"Boolean\", \"Symbol\", \"Math\", \"Date\", \"Number\", \"BigInt\", \"String\", \"RegExp\", \"Array\",\r\n\t\t\"Float32Array\", \"Float64Array\", \"Int8Array\", \"Uint8Array\", \"Uint8ClampedArray\", \"Int16Array\", \"Int32Array\",\r\n\t\t\"Uint16Array\", \"Uint32Array\", \"BigInt64Array\", \"BigUint64Array\", \"Set\", \"Map\", \"WeakSet\", \"WeakMap\", \"ArrayBuffer\",\r\n\t\t\"SharedArrayBuffer\", \"Atomics\", \"DataView\", \"JSON\", \"Promise\", \"Generator\", \"GeneratorFunction\", \"AsyncFunction\",\r\n\t\t\"Reflect\", \"Proxy\", \"Intl\", \"WebAssembly\"\r\n\t],\r\n\tbe = [\"Error\", \"EvalError\", \"InternalError\", \"RangeError\", \"ReferenceError\", \"SyntaxError\", \"TypeError\", \"URIError\"],\r\n\tme = [\"setInterval\", \"setTimeout\", \"clearInterval\", \"clearTimeout\", \"require\", \"exports\", \"eval\", \"isFinite\", \"isNaN\",\r\n\t\t\"parseFloat\", \"parseInt\", \"decodeURI\", \"decodeURIComponent\", \"encodeURI\", \"encodeURIComponent\", \"escape\", \"unescape\"\r\n\t],\r\n\tpe = [\"arguments\", \"this\", \"super\", \"console\", \"window\", \"document\", \"localStorage\", \"module\", \"global\"],\r\n\t_e = [].concat(me, ue, be);\r\n\r\nfunction he(e) {\r\n\tconst n = e.regex,\r\n\t\tt = ce,\r\n\t\ta = {\r\n\t\t\tbegin: /<[A-Za-z0-9\\\\._:-]+/,\r\n\t\t\tend: /\\/[A-Za-z0-9\\\\._:-]+>|\\/>/,\r\n\t\t\tisTrulyOpeningTag: (e, n) => {\r\n\t\t\t\tconst t = e[0].length + e.index,\r\n\t\t\t\t\ta = e.input[t];\r\n\t\t\t\tif (\"<\" === a || \",\" === a) return void n.ignoreMatch();\r\n\t\t\t\tlet i;\r\n\t\t\t\t\">\" === a && (((e, {\r\n\t\t\t\t\tafter: n\r\n\t\t\t\t}) => {\r\n\t\t\t\t\tconst t = \"</\" + e[0].slice(1);\r\n\t\t\t\t\treturn -1 !== e.input.indexOf(t, n)\r\n\t\t\t\t})(e, {\r\n\t\t\t\t\tafter: t\r\n\t\t\t\t}) || n.ignoreMatch());\r\n\t\t\t\tconst r = e.input.substring(t);\r\n\t\t\t\t((i = r.match(/^\\s*=/)) || (i = r.match(/^\\s+extends\\s+/)) && 0 === i.index) && n.ignoreMatch()\r\n\t\t\t}\r\n\t\t},\r\n\t\ti = {\r\n\t\t\t$pattern: ce,\r\n\t\t\tkeyword: de,\r\n\t\t\tliteral: ge,\r\n\t\t\tbuilt_in: _e,\r\n\t\t\t\"variable.language\": pe\r\n\t\t},\r\n\t\tr = \"\\\\.([0-9](_?[0-9])*)\",\r\n\t\ts = \"0|[1-9](_?[0-9])*|0[0-7]*[89][0-9]*\",\r\n\t\to = {\r\n\t\t\tclassName: \"number\",\r\n\t\t\tvariants: [{\r\n\t\t\t\tbegin: `(\\\\b(${s})((${r})|\\\\.)?|(${r}))[eE][+-]?([0-9](_?[0-9])*)\\\\b`\r\n\t\t\t}, {\r\n\t\t\t\tbegin: `\\\\b(${s})\\\\b((${r})\\\\b|\\\\.)?|(${r})\\\\b`\r\n\t\t\t}, {\r\n\t\t\t\tbegin: \"\\\\b(0|[1-9](_?[0-9])*)n\\\\b\"\r\n\t\t\t}, {\r\n\t\t\t\tbegin: \"\\\\b0[xX][0-9a-fA-F](_?[0-9a-fA-F])*n?\\\\b\"\r\n\t\t\t}, {\r\n\t\t\t\tbegin: \"\\\\b0[bB][0-1](_?[0-1])*n?\\\\b\"\r\n\t\t\t}, {\r\n\t\t\t\tbegin: \"\\\\b0[oO][0-7](_?[0-7])*n?\\\\b\"\r\n\t\t\t}, {\r\n\t\t\t\tbegin: \"\\\\b0[0-7]+n?\\\\b\"\r\n\t\t\t}],\r\n\t\t\trelevance: 0\r\n\t\t},\r\n\t\tl = {\r\n\t\t\tclassName: \"subst\",\r\n\t\t\tbegin: \"\\\\$\\\\{\",\r\n\t\t\tend: \"\\\\}\",\r\n\t\t\tkeywords: i,\r\n\t\t\tcontains: []\r\n\t\t},\r\n\t\tc = {\r\n\t\t\tbegin: \"html`\",\r\n\t\t\tend: \"\",\r\n\t\t\tstarts: {\r\n\t\t\t\tend: \"`\",\r\n\t\t\t\treturnEnd: !1,\r\n\t\t\t\tcontains: [e.BACKSLASH_ESCAPE, l],\r\n\t\t\t\tsubLanguage: \"xml\"\r\n\t\t\t}\r\n\t\t},\r\n\t\td = {\r\n\t\t\tbegin: \"css`\",\r\n\t\t\tend: \"\",\r\n\t\t\tstarts: {\r\n\t\t\t\tend: \"`\",\r\n\t\t\t\treturnEnd: !1,\r\n\t\t\t\tcontains: [e.BACKSLASH_ESCAPE, l],\r\n\t\t\t\tsubLanguage: \"css\"\r\n\t\t\t}\r\n\t\t},\r\n\t\tg = {\r\n\t\t\tclassName: \"string\",\r\n\t\t\tbegin: \"`\",\r\n\t\t\tend: \"`\",\r\n\t\t\tcontains: [e.BACKSLASH_ESCAPE, l]\r\n\t\t},\r\n\t\tu = {\r\n\t\t\tclassName: \"comment\",\r\n\t\t\tvariants: [e.COMMENT(/\\/\\*\\*(?!\\/)/, \"\\\\*/\", {\r\n\t\t\t\trelevance: 0,\r\n\t\t\t\tcontains: [{\r\n\t\t\t\t\tbegin: \"(?=@[A-Za-z]+)\",\r\n\t\t\t\t\trelevance: 0,\r\n\t\t\t\t\tcontains: [{\r\n\t\t\t\t\t\tclassName: \"doctag\",\r\n\t\t\t\t\t\tbegin: \"@[A-Za-z]+\"\r\n\t\t\t\t\t}, {\r\n\t\t\t\t\t\tclassName: \"type\",\r\n\t\t\t\t\t\tbegin: \"\\\\{\",\r\n\t\t\t\t\t\tend: \"\\\\}\",\r\n\t\t\t\t\t\texcludeEnd: !0,\r\n\t\t\t\t\t\texcludeBegin: !0,\r\n\t\t\t\t\t\trelevance: 0\r\n\t\t\t\t\t}, {\r\n\t\t\t\t\t\tclassName: \"variable\",\r\n\t\t\t\t\t\tbegin: t + \"(?=\\\\s*(-)|$)\",\r\n\t\t\t\t\t\tendsParent: !0,\r\n\t\t\t\t\t\trelevance: 0\r\n\t\t\t\t\t}, {\r\n\t\t\t\t\t\tbegin: /(?=[^\\n])\\s/,\r\n\t\t\t\t\t\trelevance: 0\r\n\t\t\t\t\t}]\r\n\t\t\t\t}]\r\n\t\t\t}), e.C_BLOCK_COMMENT_MODE, e.C_LINE_COMMENT_MODE]\r\n\t\t},\r\n\t\tb = [e.APOS_STRING_MODE, e.QUOTE_STRING_MODE, c, d, g, {\r\n\t\t\tmatch: /\\$\\d+/\r\n\t\t}, o];\r\n\tl.contains = b.concat({\r\n\t\tbegin: /\\{/,\r\n\t\tend: /\\}/,\r\n\t\tkeywords: i,\r\n\t\tcontains: [\"self\"].concat(b)\r\n\t});\r\n\tconst m = [].concat(u, l.contains),\r\n\t\tp = m.concat([{\r\n\t\t\tbegin: /\\(/,\r\n\t\t\tend: /\\)/,\r\n\t\t\tkeywords: i,\r\n\t\t\tcontains: [\"self\"].concat(m)\r\n\t\t}]),\r\n\t\t_ = {\r\n\t\t\tclassName: \"params\",\r\n\t\t\tbegin: /\\(/,\r\n\t\t\tend: /\\)/,\r\n\t\t\texcludeBegin: !0,\r\n\t\t\texcludeEnd: !0,\r\n\t\t\tkeywords: i,\r\n\t\t\tcontains: p\r\n\t\t},\r\n\t\th = {\r\n\t\t\tvariants: [{\r\n\t\t\t\tmatch: [/class/, /\\s+/, t, /\\s+/, /extends/, /\\s+/, n.concat(t, \"(\", n.concat(/\\./, t), \")*\")],\r\n\t\t\t\tscope: {\r\n\t\t\t\t\t1: \"keyword\",\r\n\t\t\t\t\t3: \"title.class\",\r\n\t\t\t\t\t5: \"keyword\",\r\n\t\t\t\t\t7: \"title.class.inherited\"\r\n\t\t\t\t}\r\n\t\t\t}, {\r\n\t\t\t\tmatch: [/class/, /\\s+/, t],\r\n\t\t\t\tscope: {\r\n\t\t\t\t\t1: \"keyword\",\r\n\t\t\t\t\t3: \"title.class\"\r\n\t\t\t\t}\r\n\t\t\t}]\r\n\t\t},\r\n\t\tf = {\r\n\t\t\trelevance: 0,\r\n\t\t\tmatch: n.either(/\\bJSON/, /\\b[A-Z][a-z]+([A-Z][a-z]*|\\d)*/, /\\b[A-Z]{2,}([A-Z][a-z]+|\\d)+([A-Z][a-z]*)*/,\r\n\t\t\t\t/\\b[A-Z]{2,}[a-z]+([A-Z][a-z]+|\\d)*([A-Z][a-z]*)*/),\r\n\t\t\tclassName: \"title.class\",\r\n\t\t\tkeywords: {\r\n\t\t\t\t_: [...ue, ...be]\r\n\t\t\t}\r\n\t\t},\r\n\t\tE = {\r\n\t\t\tvariants: [{\r\n\t\t\t\tmatch: [/function/, /\\s+/, t, /(?=\\s*\\()/]\r\n\t\t\t}, {\r\n\t\t\t\tmatch: [/function/, /\\s*(?=\\()/]\r\n\t\t\t}],\r\n\t\t\tclassName: {\r\n\t\t\t\t1: \"keyword\",\r\n\t\t\t\t3: \"title.function\"\r\n\t\t\t},\r\n\t\t\tlabel: \"func.def\",\r\n\t\t\tcontains: [_],\r\n\t\t\tillegal: /%/\r\n\t\t},\r\n\t\ty = {\r\n\t\t\tmatch: n.concat(/\\b/, (w = [...me, \"super\", \"import\"], n.concat(\"(?!\", w.join(\"|\"), \")\")), t, n.lookahead(/\\(/)),\r\n\t\t\tclassName: \"title.function\",\r\n\t\t\trelevance: 0\r\n\t\t};\r\n\tvar w;\r\n\tconst N = {\r\n\t\t\tbegin: n.concat(/\\./, n.lookahead(n.concat(t, /(?![0-9A-Za-z$_(])/))),\r\n\t\t\tend: t,\r\n\t\t\texcludeBegin: !0,\r\n\t\t\tkeywords: \"prototype\",\r\n\t\t\tclassName: \"property\",\r\n\t\t\trelevance: 0\r\n\t\t},\r\n\t\tv = {\r\n\t\t\tmatch: [/get|set/, /\\s+/, t, /(?=\\()/],\r\n\t\t\tclassName: {\r\n\t\t\t\t1: \"keyword\",\r\n\t\t\t\t3: \"title.function\"\r\n\t\t\t},\r\n\t\t\tcontains: [{\r\n\t\t\t\tbegin: /\\(\\)/\r\n\t\t\t}, _]\r\n\t\t},\r\n\t\tO = \"(\\\\([^()]*(\\\\([^()]*(\\\\([^()]*\\\\)[^()]*)*\\\\)[^()]*)*\\\\)|\" + e.UNDERSCORE_IDENT_RE + \")\\\\s*=>\",\r\n\t\tk = {\r\n\t\t\tmatch: [/const|var|let/, /\\s+/, t, /\\s*/, /=\\s*/, /(async\\s*)?/, n.lookahead(O)],\r\n\t\t\tkeywords: \"async\",\r\n\t\t\tclassName: {\r\n\t\t\t\t1: \"keyword\",\r\n\t\t\t\t3: \"title.function\"\r\n\t\t\t},\r\n\t\t\tcontains: [_]\r\n\t\t};\r\n\treturn {\r\n\t\tname: \"Javascript\",\r\n\t\taliases: [\"js\", \"jsx\", \"mjs\", \"cjs\"],\r\n\t\tkeywords: i,\r\n\t\texports: {\r\n\t\t\tPARAMS_CONTAINS: p,\r\n\t\t\tCLASS_REFERENCE: f\r\n\t\t},\r\n\t\tillegal: /#(?![$_A-z])/,\r\n\t\tcontains: [e.SHEBANG({\r\n\t\t\tlabel: \"shebang\",\r\n\t\t\tbinary: \"node\",\r\n\t\t\trelevance: 5\r\n\t\t}), {\r\n\t\t\tlabel: \"use_strict\",\r\n\t\t\tclassName: \"meta\",\r\n\t\t\trelevance: 10,\r\n\t\t\tbegin: /^\\s*['\"]use (strict|asm)['\"]/\r\n\t\t}, e.APOS_STRING_MODE, e.QUOTE_STRING_MODE, c, d, g, u, {\r\n\t\t\tmatch: /\\$\\d+/\r\n\t\t}, o, f, {\r\n\t\t\tclassName: \"attr\",\r\n\t\t\tbegin: t + n.lookahead(\":\"),\r\n\t\t\trelevance: 0\r\n\t\t}, k, {\r\n\t\t\tbegin: \"(\" + e.RE_STARTERS_RE + \"|\\\\b(case|return|throw)\\\\b)\\\\s*\",\r\n\t\t\tkeywords: \"return throw case\",\r\n\t\t\trelevance: 0,\r\n\t\t\tcontains: [u, e.REGEXP_MODE, {\r\n\t\t\t\tclassName: \"function\",\r\n\t\t\t\tbegin: O,\r\n\t\t\t\treturnBegin: !0,\r\n\t\t\t\tend: \"\\\\s*=>\",\r\n\t\t\t\tcontains: [{\r\n\t\t\t\t\tclassName: \"params\",\r\n\t\t\t\t\tvariants: [{\r\n\t\t\t\t\t\tbegin: e.UNDERSCORE_IDENT_RE,\r\n\t\t\t\t\t\trelevance: 0\r\n\t\t\t\t\t}, {\r\n\t\t\t\t\t\tclassName: null,\r\n\t\t\t\t\t\tbegin: /\\(\\s*\\)/,\r\n\t\t\t\t\t\tskip: !0\r\n\t\t\t\t\t}, {\r\n\t\t\t\t\t\tbegin: /\\(/,\r\n\t\t\t\t\t\tend: /\\)/,\r\n\t\t\t\t\t\texcludeBegin: !0,\r\n\t\t\t\t\t\texcludeEnd: !0,\r\n\t\t\t\t\t\tkeywords: i,\r\n\t\t\t\t\t\tcontains: p\r\n\t\t\t\t\t}]\r\n\t\t\t\t}]\r\n\t\t\t}, {\r\n\t\t\t\tbegin: /,/,\r\n\t\t\t\trelevance: 0\r\n\t\t\t}, {\r\n\t\t\t\tmatch: /\\s+/,\r\n\t\t\t\trelevance: 0\r\n\t\t\t}, {\r\n\t\t\t\tvariants: [{\r\n\t\t\t\t\tbegin: \"<>\",\r\n\t\t\t\t\tend: \"</>\"\r\n\t\t\t\t}, {\r\n\t\t\t\t\tmatch: /<[A-Za-z0-9\\\\._:-]+\\s*\\/>/\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: a.begin,\r\n\t\t\t\t\t\"on:begin\": a.isTrulyOpeningTag,\r\n\t\t\t\t\tend: a.end\r\n\t\t\t\t}],\r\n\t\t\t\tsubLanguage: \"xml\",\r\n\t\t\t\tcontains: [{\r\n\t\t\t\t\tbegin: a.begin,\r\n\t\t\t\t\tend: a.end,\r\n\t\t\t\t\tskip: !0,\r\n\t\t\t\t\tcontains: [\"self\"]\r\n\t\t\t\t}]\r\n\t\t\t}]\r\n\t\t}, E, {\r\n\t\t\tbeginKeywords: \"while if switch catch for\"\r\n\t\t}, {\r\n\t\t\tbegin: \"\\\\b(?!function)\" + e.UNDERSCORE_IDENT_RE +\r\n\t\t\t\t\"\\\\([^()]*(\\\\([^()]*(\\\\([^()]*\\\\)[^()]*)*\\\\)[^()]*)*\\\\)\\\\s*\\\\{\",\r\n\t\t\treturnBegin: !0,\r\n\t\t\tlabel: \"func.def\",\r\n\t\t\tcontains: [_, e.inherit(e.TITLE_MODE, {\r\n\t\t\t\tbegin: t,\r\n\t\t\t\tclassName: \"title.function\"\r\n\t\t\t})]\r\n\t\t}, {\r\n\t\t\tmatch: /\\.\\.\\./,\r\n\t\t\trelevance: 0\r\n\t\t}, N, {\r\n\t\t\tmatch: \"\\\\$\" + t,\r\n\t\t\trelevance: 0\r\n\t\t}, {\r\n\t\t\tmatch: [/\\bconstructor(?=\\s*\\()/],\r\n\t\t\tclassName: {\r\n\t\t\t\t1: \"title.function\"\r\n\t\t\t},\r\n\t\t\tcontains: [_]\r\n\t\t}, y, {\r\n\t\t\trelevance: 0,\r\n\t\t\tmatch: /\\b[A-Z][A-Z_0-9]+\\b/,\r\n\t\t\tclassName: \"variable.constant\"\r\n\t\t}, h, v, {\r\n\t\t\tmatch: /\\$[(.]/\r\n\t\t}]\r\n\t}\r\n}\r\nconst fe = e => m(/\\b/, e, /\\w$/.test(e) ? /\\b/ : /\\B/),\r\n\tEe = [\"Protocol\", \"Type\"].map(fe),\r\n\tye = [\"init\", \"self\"].map(fe),\r\n\twe = [\"Any\", \"Self\"],\r\n\tNe = [\"actor\", \"any\", \"associatedtype\", \"async\", \"await\", /as\\?/, /as!/, \"as\", \"break\", \"case\", \"catch\", \"class\",\r\n\t\t\"continue\", \"convenience\", \"default\", \"defer\", \"deinit\", \"didSet\", \"distributed\", \"do\", \"dynamic\", \"else\", \"enum\",\r\n\t\t\"extension\", \"fallthrough\", /fileprivate\\(set\\)/, \"fileprivate\", \"final\", \"for\", \"func\", \"get\", \"guard\", \"if\",\r\n\t\t\"import\", \"indirect\", \"infix\", /init\\?/, /init!/, \"inout\", /internal\\(set\\)/, \"internal\", \"in\", \"is\", \"isolated\",\r\n\t\t\"nonisolated\", \"lazy\", \"let\", \"mutating\", \"nonmutating\", /open\\(set\\)/, \"open\", \"operator\", \"optional\", \"override\",\r\n\t\t\"postfix\", \"precedencegroup\", \"prefix\", /private\\(set\\)/, \"private\", \"protocol\", /public\\(set\\)/, \"public\",\r\n\t\t\"repeat\", \"required\", \"rethrows\", \"return\", \"set\", \"some\", \"static\", \"struct\", \"subscript\", \"super\", \"switch\",\r\n\t\t\"throws\", \"throw\", /try\\?/, /try!/, \"try\", \"typealias\", /unowned\\(safe\\)/, /unowned\\(unsafe\\)/, \"unowned\", \"var\",\r\n\t\t\"weak\", \"where\", \"while\", \"willSet\"\r\n\t],\r\n\tve = [\"false\", \"nil\", \"true\"],\r\n\tOe = [\"assignment\", \"associativity\", \"higherThan\", \"left\", \"lowerThan\", \"none\", \"right\"],\r\n\tke = [\"#colorLiteral\", \"#column\", \"#dsohandle\", \"#else\", \"#elseif\", \"#endif\", \"#error\", \"#file\", \"#fileID\",\r\n\t\t\"#fileLiteral\", \"#filePath\", \"#function\", \"#if\", \"#imageLiteral\", \"#keyPath\", \"#line\", \"#selector\",\r\n\t\t\"#sourceLocation\", \"#warn_unqualified_access\", \"#warning\"\r\n\t],\r\n\txe = [\"abs\", \"all\", \"any\", \"assert\", \"assertionFailure\", \"debugPrint\", \"dump\", \"fatalError\", \"getVaList\",\r\n\t\t\"isKnownUniquelyReferenced\", \"max\", \"min\", \"numericCast\", \"pointwiseMax\", \"pointwiseMin\", \"precondition\",\r\n\t\t\"preconditionFailure\", \"print\", \"readLine\", \"repeatElement\", \"sequence\", \"stride\", \"swap\",\r\n\t\t\"swift_unboxFromSwiftValueWithType\", \"transcode\", \"type\", \"unsafeBitCast\", \"unsafeDowncast\", \"withExtendedLifetime\",\r\n\t\t\"withUnsafeMutablePointer\", \"withUnsafePointer\", \"withVaList\", \"withoutActuallyEscaping\", \"zip\"\r\n\t],\r\n\tMe = p(/[/=\\-+!*%<>&|^~?]/, /[\\u00A1-\\u00A7]/, /[\\u00A9\\u00AB]/, /[\\u00AC\\u00AE]/, /[\\u00B0\\u00B1]/,\r\n\t\t/[\\u00B6\\u00BB\\u00BF\\u00D7\\u00F7]/, /[\\u2016-\\u2017]/, /[\\u2020-\\u2027]/, /[\\u2030-\\u203E]/, /[\\u2041-\\u2053]/,\r\n\t\t/[\\u2055-\\u205E]/, /[\\u2190-\\u23FF]/, /[\\u2500-\\u2775]/, /[\\u2794-\\u2BFF]/, /[\\u2E00-\\u2E7F]/, /[\\u3001-\\u3003]/,\r\n\t\t/[\\u3008-\\u3020]/, /[\\u3030]/),\r\n\tSe = p(Me, /[\\u0300-\\u036F]/, /[\\u1DC0-\\u1DFF]/, /[\\u20D0-\\u20FF]/, /[\\uFE00-\\uFE0F]/, /[\\uFE20-\\uFE2F]/),\r\n\tAe = m(Me, Se, \"*\"),\r\n\tCe = p(/[a-zA-Z_]/, /[\\u00A8\\u00AA\\u00AD\\u00AF\\u00B2-\\u00B5\\u00B7-\\u00BA]/,\r\n\t\t/[\\u00BC-\\u00BE\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u00FF]/, /[\\u0100-\\u02FF\\u0370-\\u167F\\u1681-\\u180D\\u180F-\\u1DBF]/,\r\n\t\t/[\\u1E00-\\u1FFF]/, /[\\u200B-\\u200D\\u202A-\\u202E\\u203F-\\u2040\\u2054\\u2060-\\u206F]/,\r\n\t\t/[\\u2070-\\u20CF\\u2100-\\u218F\\u2460-\\u24FF\\u2776-\\u2793]/, /[\\u2C00-\\u2DFF\\u2E80-\\u2FFF]/,\r\n\t\t/[\\u3004-\\u3007\\u3021-\\u302F\\u3031-\\u303F\\u3040-\\uD7FF]/, /[\\uF900-\\uFD3D\\uFD40-\\uFDCF\\uFDF0-\\uFE1F\\uFE30-\\uFE44]/,\r\n\t\t/[\\uFE47-\\uFEFE\\uFF00-\\uFFFD]/),\r\n\tTe = p(Ce, /\\d/, /[\\u0300-\\u036F\\u1DC0-\\u1DFF\\u20D0-\\u20FF\\uFE20-\\uFE2F]/),\r\n\tRe = m(Ce, Te, \"*\"),\r\n\tDe = m(/[A-Z]/, Te, \"*\"),\r\n\tIe = [\"autoclosure\", m(/convention\\(/, p(\"swift\", \"block\", \"c\"), /\\)/), \"discardableResult\", \"dynamicCallable\",\r\n\t\t\"dynamicMemberLookup\", \"escaping\", \"frozen\", \"GKInspectable\", \"IBAction\", \"IBDesignable\", \"IBInspectable\",\r\n\t\t\"IBOutlet\", \"IBSegueAction\", \"inlinable\", \"main\", \"nonobjc\", \"NSApplicationMain\", \"NSCopying\", \"NSManaged\", m(\r\n\t\t\t/objc\\(/, Re, /\\)/), \"objc\", \"objcMembers\", \"propertyWrapper\", \"requires_stored_property_inits\", \"resultBuilder\",\r\n\t\t\"testable\", \"UIApplicationMain\", \"unknown\", \"usableFromInline\"\r\n\t],\r\n\tLe = [\"iOS\", \"iOSApplicationExtension\", \"macOS\", \"macOSApplicationExtension\", \"macCatalyst\",\r\n\t\t\"macCatalystApplicationExtension\", \"watchOS\", \"watchOSApplicationExtension\", \"tvOS\", \"tvOSApplicationExtension\",\r\n\t\t\"swift\"\r\n\t];\r\nvar Be = Object.freeze({\r\n\t__proto__: null,\r\n\tgrmr_bash: e => {\r\n\t\tconst n = e.regex,\r\n\t\t\tt = {},\r\n\t\t\ta = {\r\n\t\t\t\tbegin: /\\$\\{/,\r\n\t\t\t\tend: /\\}/,\r\n\t\t\t\tcontains: [\"self\", {\r\n\t\t\t\t\tbegin: /:-/,\r\n\t\t\t\t\tcontains: [t]\r\n\t\t\t\t}]\r\n\t\t\t};\r\n\t\tObject.assign(t, {\r\n\t\t\tclassName: \"variable\",\r\n\t\t\tvariants: [{\r\n\t\t\t\tbegin: n.concat(/\\$[\\w\\d#@][\\w\\d_]*/, \"(?![\\\\w\\\\d])(?![$])\")\r\n\t\t\t}, a]\r\n\t\t});\r\n\t\tconst i = {\r\n\t\t\t\tclassName: \"subst\",\r\n\t\t\t\tbegin: /\\$\\(/,\r\n\t\t\t\tend: /\\)/,\r\n\t\t\t\tcontains: [e.BACKSLASH_ESCAPE]\r\n\t\t\t},\r\n\t\t\tr = {\r\n\t\t\t\tbegin: /<<-?\\s*(?=\\w+)/,\r\n\t\t\t\tstarts: {\r\n\t\t\t\t\tcontains: [e.END_SAME_AS_BEGIN({\r\n\t\t\t\t\t\tbegin: /(\\w+)/,\r\n\t\t\t\t\t\tend: /(\\w+)/,\r\n\t\t\t\t\t\tclassName: \"string\"\r\n\t\t\t\t\t})]\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\ts = {\r\n\t\t\t\tclassName: \"string\",\r\n\t\t\t\tbegin: /\"/,\r\n\t\t\t\tend: /\"/,\r\n\t\t\t\tcontains: [e.BACKSLASH_ESCAPE, t, i]\r\n\t\t\t};\r\n\t\ti.contains.push(s);\r\n\t\tconst o = {\r\n\t\t\t\tbegin: /\\$?\\(\\(/,\r\n\t\t\t\tend: /\\)\\)/,\r\n\t\t\t\tcontains: [{\r\n\t\t\t\t\tbegin: /\\d+#[0-9a-f]+/,\r\n\t\t\t\t\tclassName: \"number\"\r\n\t\t\t\t}, e.NUMBER_MODE, t]\r\n\t\t\t},\r\n\t\t\tl = e.SHEBANG({\r\n\t\t\t\tbinary: \"(fish|bash|zsh|sh|csh|ksh|tcsh|dash|scsh)\",\r\n\t\t\t\trelevance: 10\r\n\t\t\t}),\r\n\t\t\tc = {\r\n\t\t\t\tclassName: \"function\",\r\n\t\t\t\tbegin: /\\w[\\w\\d_]*\\s*\\(\\s*\\)\\s*\\{/,\r\n\t\t\t\treturnBegin: !0,\r\n\t\t\t\tcontains: [e.inherit(e.TITLE_MODE, {\r\n\t\t\t\t\tbegin: /\\w[\\w\\d_]*/\r\n\t\t\t\t})],\r\n\t\t\t\trelevance: 0\r\n\t\t\t};\r\n\t\treturn {\r\n\t\t\tname: \"Bash\",\r\n\t\t\taliases: [\"sh\"],\r\n\t\t\tkeywords: {\r\n\t\t\t\t$pattern: /\\b[a-z][a-z0-9._-]+\\b/,\r\n\t\t\t\tkeyword: [\"if\", \"then\", \"else\", \"elif\", \"fi\", \"for\", \"while\", \"in\", \"do\", \"done\", \"case\", \"esac\",\r\n\t\t\t\t\t\"function\"\r\n\t\t\t\t],\r\n\t\t\t\tliteral: [\"true\", \"false\"],\r\n\t\t\t\tbuilt_in: [\"break\", \"cd\", \"continue\", \"eval\", \"exec\", \"exit\", \"export\", \"getopts\", \"hash\", \"pwd\",\r\n\t\t\t\t\t\"readonly\", \"return\", \"shift\", \"test\", \"times\", \"trap\", \"umask\", \"unset\", \"alias\", \"bind\", \"builtin\",\r\n\t\t\t\t\t\"caller\", \"command\", \"declare\", \"echo\", \"enable\", \"help\", \"let\", \"local\", \"logout\", \"mapfile\",\r\n\t\t\t\t\t\"printf\", \"read\", \"readarray\", \"source\", \"type\", \"typeset\", \"ulimit\", \"unalias\", \"set\", \"shopt\",\r\n\t\t\t\t\t\"autoload\", \"bg\", \"bindkey\", \"bye\", \"cap\", \"chdir\", \"clone\", \"comparguments\", \"compcall\", \"compctl\",\r\n\t\t\t\t\t\"compdescribe\", \"compfiles\", \"compgroups\", \"compquote\", \"comptags\", \"comptry\", \"compvalues\", \"dirs\",\r\n\t\t\t\t\t\"disable\", \"disown\", \"echotc\", \"echoti\", \"emulate\", \"fc\", \"fg\", \"float\", \"functions\", \"getcap\",\r\n\t\t\t\t\t\"getln\", \"history\", \"integer\", \"jobs\", \"kill\", \"limit\", \"log\", \"noglob\", \"popd\", \"print\", \"pushd\",\r\n\t\t\t\t\t\"pushln\", \"rehash\", \"sched\", \"setcap\", \"setopt\", \"stat\", \"suspend\", \"ttyctl\", \"unfunction\", \"unhash\",\r\n\t\t\t\t\t\"unlimit\", \"unsetopt\", \"vared\", \"wait\", \"whence\", \"where\", \"which\", \"zcompile\", \"zformat\", \"zftp\",\r\n\t\t\t\t\t\"zle\", \"zmodload\", \"zparseopts\", \"zprof\", \"zpty\", \"zregexparse\", \"zsocket\", \"zstyle\", \"ztcp\", \"chcon\",\r\n\t\t\t\t\t\"chgrp\", \"chown\", \"chmod\", \"cp\", \"dd\", \"df\", \"dir\", \"dircolors\", \"ln\", \"ls\", \"mkdir\", \"mkfifo\",\r\n\t\t\t\t\t\"mknod\", \"mktemp\", \"mv\", \"realpath\", \"rm\", \"rmdir\", \"shred\", \"sync\", \"touch\", \"truncate\", \"vdir\",\r\n\t\t\t\t\t\"b2sum\", \"base32\", \"base64\", \"cat\", \"cksum\", \"comm\", \"csplit\", \"cut\", \"expand\", \"fmt\", \"fold\", \"head\",\r\n\t\t\t\t\t\"join\", \"md5sum\", \"nl\", \"numfmt\", \"od\", \"paste\", \"ptx\", \"pr\", \"sha1sum\", \"sha224sum\", \"sha256sum\",\r\n\t\t\t\t\t\"sha384sum\", \"sha512sum\", \"shuf\", \"sort\", \"split\", \"sum\", \"tac\", \"tail\", \"tr\", \"tsort\", \"unexpand\",\r\n\t\t\t\t\t\"uniq\", \"wc\", \"arch\", \"basename\", \"chroot\", \"date\", \"dirname\", \"du\", \"echo\", \"env\", \"expr\", \"factor\",\r\n\t\t\t\t\t\"groups\", \"hostid\", \"id\", \"link\", \"logname\", \"nice\", \"nohup\", \"nproc\", \"pathchk\", \"pinky\", \"printenv\",\r\n\t\t\t\t\t\"printf\", \"pwd\", \"readlink\", \"runcon\", \"seq\", \"sleep\", \"stat\", \"stdbuf\", \"stty\", \"tee\", \"test\",\r\n\t\t\t\t\t\"timeout\", \"tty\", \"uname\", \"unlink\", \"uptime\", \"users\", \"who\", \"whoami\", \"yes\"\r\n\t\t\t\t]\r\n\t\t\t},\r\n\t\t\tcontains: [l, e.SHEBANG(), c, o, e.HASH_COMMENT_MODE, r, {\r\n\t\t\t\tmatch: /(\\/[a-z._-]+)+/\r\n\t\t\t}, s, {\r\n\t\t\t\tclassName: \"\",\r\n\t\t\t\tbegin: /\\\\\"/\r\n\t\t\t}, {\r\n\t\t\t\tclassName: \"string\",\r\n\t\t\t\tbegin: /'/,\r\n\t\t\t\tend: /'/\r\n\t\t\t}, t]\r\n\t\t}\r\n\t},\r\n\tgrmr_c: e => {\r\n\t\tconst n = e.regex,\r\n\t\t\tt = e.COMMENT(\"//\", \"$\", {\r\n\t\t\t\tcontains: [{\r\n\t\t\t\t\tbegin: /\\\\\\n/\r\n\t\t\t\t}]\r\n\t\t\t}),\r\n\t\t\ta = \"[a-zA-Z_]\\\\w*::\",\r\n\t\t\ti = \"(decltype\\\\(auto\\\\)|\" + n.optional(a) + \"[a-zA-Z_]\\\\w*\" + n.optional(\"<[^<>]+>\") + \")\",\r\n\t\t\tr = {\r\n\t\t\t\tclassName: \"type\",\r\n\t\t\t\tvariants: [{\r\n\t\t\t\t\tbegin: \"\\\\b[a-z\\\\d_]*_t\\\\b\"\r\n\t\t\t\t}, {\r\n\t\t\t\t\tmatch: /\\batomic_[a-z]{3,6}\\b/\r\n\t\t\t\t}]\r\n\t\t\t},\r\n\t\t\ts = {\r\n\t\t\t\tclassName: \"string\",\r\n\t\t\t\tvariants: [{\r\n\t\t\t\t\tbegin: '(u8?|U|L)?\"',\r\n\t\t\t\t\tend: '\"',\r\n\t\t\t\t\tillegal: \"\\\\n\",\r\n\t\t\t\t\tcontains: [e.BACKSLASH_ESCAPE]\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: \"(u8?|U|L)?'(\\\\\\\\(x[0-9A-Fa-f]{2}|u[0-9A-Fa-f]{4,8}|[0-7]{3}|\\\\S)|.)\",\r\n\t\t\t\t\tend: \"'\",\r\n\t\t\t\t\tillegal: \".\"\r\n\t\t\t\t}, e.END_SAME_AS_BEGIN({\r\n\t\t\t\t\tbegin: /(?:u8?|U|L)?R\"([^()\\\\ ]{0,16})\\(/,\r\n\t\t\t\t\tend: /\\)([^()\\\\ ]{0,16})\"/\r\n\t\t\t\t})]\r\n\t\t\t},\r\n\t\t\to = {\r\n\t\t\t\tclassName: \"number\",\r\n\t\t\t\tvariants: [{\r\n\t\t\t\t\tbegin: \"\\\\b(0b[01']+)\"\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: \"(-?)\\\\b([\\\\d']+(\\\\.[\\\\d']*)?|\\\\.[\\\\d']+)((ll|LL|l|L)(u|U)?|(u|U)(ll|LL|l|L)?|f|F|b|B)\"\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: \"(-?)(\\\\b0[xX][a-fA-F0-9']+|(\\\\b[\\\\d']+(\\\\.[\\\\d']*)?|\\\\.[\\\\d']+)([eE][-+]?[\\\\d']+)?)\"\r\n\t\t\t\t}],\r\n\t\t\t\trelevance: 0\r\n\t\t\t},\r\n\t\t\tl = {\r\n\t\t\t\tclassName: \"meta\",\r\n\t\t\t\tbegin: /#\\s*[a-z]+\\b/,\r\n\t\t\t\tend: /$/,\r\n\t\t\t\tkeywords: {\r\n\t\t\t\t\tkeyword: \"if else elif endif define undef warning error line pragma _Pragma ifdef ifndef include\"\r\n\t\t\t\t},\r\n\t\t\t\tcontains: [{\r\n\t\t\t\t\tbegin: /\\\\\\n/,\r\n\t\t\t\t\trelevance: 0\r\n\t\t\t\t}, e.inherit(s, {\r\n\t\t\t\t\tclassName: \"string\"\r\n\t\t\t\t}), {\r\n\t\t\t\t\tclassName: \"string\",\r\n\t\t\t\t\tbegin: /<.*?>/\r\n\t\t\t\t}, t, e.C_BLOCK_COMMENT_MODE]\r\n\t\t\t},\r\n\t\t\tc = {\r\n\t\t\t\tclassName: \"title\",\r\n\t\t\t\tbegin: n.optional(a) + e.IDENT_RE,\r\n\t\t\t\trelevance: 0\r\n\t\t\t},\r\n\t\t\td = n.optional(a) + e.IDENT_RE + \"\\\\s*\\\\(\",\r\n\t\t\tg = {\r\n\t\t\t\tkeyword: [\"asm\", \"auto\", \"break\", \"case\", \"continue\", \"default\", \"do\", \"else\", \"enum\", \"extern\", \"for\",\r\n\t\t\t\t\t\"fortran\", \"goto\", \"if\", \"inline\", \"register\", \"restrict\", \"return\", \"sizeof\", \"struct\", \"switch\",\r\n\t\t\t\t\t\"typedef\", \"union\", \"volatile\", \"while\", \"_Alignas\", \"_Alignof\", \"_Atomic\", \"_Generic\", \"_Noreturn\",\r\n\t\t\t\t\t\"_Static_assert\", \"_Thread_local\", \"alignas\", \"alignof\", \"noreturn\", \"static_assert\", \"thread_local\",\r\n\t\t\t\t\t\"_Pragma\"\r\n\t\t\t\t],\r\n\t\t\t\ttype: [\"float\", \"double\", \"signed\", \"unsigned\", \"int\", \"short\", \"long\", \"char\", \"void\", \"_Bool\",\r\n\t\t\t\t\t\"_Complex\", \"_Imaginary\", \"_Decimal32\", \"_Decimal64\", \"_Decimal128\", \"const\", \"static\", \"complex\",\r\n\t\t\t\t\t\"bool\", \"imaginary\"\r\n\t\t\t\t],\r\n\t\t\t\tliteral: \"true false NULL\",\r\n\t\t\t\tbuilt_in: \"std string wstring cin cout cerr clog stdin stdout stderr stringstream istringstream ostringstream auto_ptr deque list queue stack vector map set pair bitset multiset multimap unordered_set unordered_map unordered_multiset unordered_multimap priority_queue make_pair array shared_ptr abort terminate abs acos asin atan2 atan calloc ceil cosh cos exit exp fabs floor fmod fprintf fputs free frexp fscanf future isalnum isalpha iscntrl isdigit isgraph islower isprint ispunct isspace isupper isxdigit tolower toupper labs ldexp log10 log malloc realloc memchr memcmp memcpy memset modf pow printf putchar puts scanf sinh sin snprintf sprintf sqrt sscanf strcat strchr strcmp strcpy strcspn strlen strncat strncmp strncpy strpbrk strrchr strspn strstr tanh tan vfprintf vprintf vsprintf endl initializer_list unique_ptr\"\r\n\t\t\t},\r\n\t\t\tu = [l, r, t, e.C_BLOCK_COMMENT_MODE, o, s],\r\n\t\t\tb = {\r\n\t\t\t\tvariants: [{\r\n\t\t\t\t\tbegin: /=/,\r\n\t\t\t\t\tend: /;/\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: /\\(/,\r\n\t\t\t\t\tend: /\\)/\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbeginKeywords: \"new throw return else\",\r\n\t\t\t\t\tend: /;/\r\n\t\t\t\t}],\r\n\t\t\t\tkeywords: g,\r\n\t\t\t\tcontains: u.concat([{\r\n\t\t\t\t\tbegin: /\\(/,\r\n\t\t\t\t\tend: /\\)/,\r\n\t\t\t\t\tkeywords: g,\r\n\t\t\t\t\tcontains: u.concat([\"self\"]),\r\n\t\t\t\t\trelevance: 0\r\n\t\t\t\t}]),\r\n\t\t\t\trelevance: 0\r\n\t\t\t},\r\n\t\t\tm = {\r\n\t\t\t\tbegin: \"(\" + i + \"[\\\\*&\\\\s]+)+\" + d,\r\n\t\t\t\treturnBegin: !0,\r\n\t\t\t\tend: /[{;=]/,\r\n\t\t\t\texcludeEnd: !0,\r\n\t\t\t\tkeywords: g,\r\n\t\t\t\tillegal: /[^\\w\\s\\*&:<>.]/,\r\n\t\t\t\tcontains: [{\r\n\t\t\t\t\tbegin: \"decltype\\\\(auto\\\\)\",\r\n\t\t\t\t\tkeywords: g,\r\n\t\t\t\t\trelevance: 0\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: d,\r\n\t\t\t\t\treturnBegin: !0,\r\n\t\t\t\t\tcontains: [e.inherit(c, {\r\n\t\t\t\t\t\tclassName: \"title.function\"\r\n\t\t\t\t\t})],\r\n\t\t\t\t\trelevance: 0\r\n\t\t\t\t}, {\r\n\t\t\t\t\trelevance: 0,\r\n\t\t\t\t\tmatch: /,/\r\n\t\t\t\t}, {\r\n\t\t\t\t\tclassName: \"params\",\r\n\t\t\t\t\tbegin: /\\(/,\r\n\t\t\t\t\tend: /\\)/,\r\n\t\t\t\t\tkeywords: g,\r\n\t\t\t\t\trelevance: 0,\r\n\t\t\t\t\tcontains: [t, e.C_BLOCK_COMMENT_MODE, s, o, r, {\r\n\t\t\t\t\t\tbegin: /\\(/,\r\n\t\t\t\t\t\tend: /\\)/,\r\n\t\t\t\t\t\tkeywords: g,\r\n\t\t\t\t\t\trelevance: 0,\r\n\t\t\t\t\t\tcontains: [\"self\", t, e.C_BLOCK_COMMENT_MODE, s, o, r]\r\n\t\t\t\t\t}]\r\n\t\t\t\t}, r, t, e.C_BLOCK_COMMENT_MODE, l]\r\n\t\t\t};\r\n\t\treturn {\r\n\t\t\tname: \"C\",\r\n\t\t\taliases: [\"h\"],\r\n\t\t\tkeywords: g,\r\n\t\t\tdisableAutodetect: !0,\r\n\t\t\tillegal: \"</\",\r\n\t\t\tcontains: [].concat(b, m, u, [l, {\r\n\t\t\t\tbegin: e.IDENT_RE + \"::\",\r\n\t\t\t\tkeywords: g\r\n\t\t\t}, {\r\n\t\t\t\tclassName: \"class\",\r\n\t\t\t\tbeginKeywords: \"enum class struct union\",\r\n\t\t\t\tend: /[{;:<>=]/,\r\n\t\t\t\tcontains: [{\r\n\t\t\t\t\tbeginKeywords: \"final class struct\"\r\n\t\t\t\t}, e.TITLE_MODE]\r\n\t\t\t}]),\r\n\t\t\texports: {\r\n\t\t\t\tpreprocessor: l,\r\n\t\t\t\tstrings: s,\r\n\t\t\t\tkeywords: g\r\n\t\t\t}\r\n\t\t}\r\n\t},\r\n\tgrmr_cpp: e => {\r\n\t\tconst n = e.regex,\r\n\t\t\tt = e.COMMENT(\"//\", \"$\", {\r\n\t\t\t\tcontains: [{\r\n\t\t\t\t\tbegin: /\\\\\\n/\r\n\t\t\t\t}]\r\n\t\t\t}),\r\n\t\t\ta = \"[a-zA-Z_]\\\\w*::\",\r\n\t\t\ti = \"(?!struct)(decltype\\\\(auto\\\\)|\" + n.optional(a) + \"[a-zA-Z_]\\\\w*\" + n.optional(\"<[^<>]+>\") + \")\",\r\n\t\t\tr = {\r\n\t\t\t\tclassName: \"type\",\r\n\t\t\t\tbegin: \"\\\\b[a-z\\\\d_]*_t\\\\b\"\r\n\t\t\t},\r\n\t\t\ts = {\r\n\t\t\t\tclassName: \"string\",\r\n\t\t\t\tvariants: [{\r\n\t\t\t\t\tbegin: '(u8?|U|L)?\"',\r\n\t\t\t\t\tend: '\"',\r\n\t\t\t\t\tillegal: \"\\\\n\",\r\n\t\t\t\t\tcontains: [e.BACKSLASH_ESCAPE]\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: \"(u8?|U|L)?'(\\\\\\\\(x[0-9A-Fa-f]{2}|u[0-9A-Fa-f]{4,8}|[0-7]{3}|\\\\S)|.)\",\r\n\t\t\t\t\tend: \"'\",\r\n\t\t\t\t\tillegal: \".\"\r\n\t\t\t\t}, e.END_SAME_AS_BEGIN({\r\n\t\t\t\t\tbegin: /(?:u8?|U|L)?R\"([^()\\\\ ]{0,16})\\(/,\r\n\t\t\t\t\tend: /\\)([^()\\\\ ]{0,16})\"/\r\n\t\t\t\t})]\r\n\t\t\t},\r\n\t\t\to = {\r\n\t\t\t\tclassName: \"number\",\r\n\t\t\t\tvariants: [{\r\n\t\t\t\t\tbegin: \"\\\\b(0b[01']+)\"\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: \"(-?)\\\\b([\\\\d']+(\\\\.[\\\\d']*)?|\\\\.[\\\\d']+)((ll|LL|l|L)(u|U)?|(u|U)(ll|LL|l|L)?|f|F|b|B)\"\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: \"(-?)(\\\\b0[xX][a-fA-F0-9']+|(\\\\b[\\\\d']+(\\\\.[\\\\d']*)?|\\\\.[\\\\d']+)([eE][-+]?[\\\\d']+)?)\"\r\n\t\t\t\t}],\r\n\t\t\t\trelevance: 0\r\n\t\t\t},\r\n\t\t\tl = {\r\n\t\t\t\tclassName: \"meta\",\r\n\t\t\t\tbegin: /#\\s*[a-z]+\\b/,\r\n\t\t\t\tend: /$/,\r\n\t\t\t\tkeywords: {\r\n\t\t\t\t\tkeyword: \"if else elif endif define undef warning error line pragma _Pragma ifdef ifndef include\"\r\n\t\t\t\t},\r\n\t\t\t\tcontains: [{\r\n\t\t\t\t\tbegin: /\\\\\\n/,\r\n\t\t\t\t\trelevance: 0\r\n\t\t\t\t}, e.inherit(s, {\r\n\t\t\t\t\tclassName: \"string\"\r\n\t\t\t\t}), {\r\n\t\t\t\t\tclassName: \"string\",\r\n\t\t\t\t\tbegin: /<.*?>/\r\n\t\t\t\t}, t, e.C_BLOCK_COMMENT_MODE]\r\n\t\t\t},\r\n\t\t\tc = {\r\n\t\t\t\tclassName: \"title\",\r\n\t\t\t\tbegin: n.optional(a) + e.IDENT_RE,\r\n\t\t\t\trelevance: 0\r\n\t\t\t},\r\n\t\t\td = n.optional(a) + e.IDENT_RE + \"\\\\s*\\\\(\",\r\n\t\t\tg = {\r\n\t\t\t\ttype: [\"bool\", \"char\", \"char16_t\", \"char32_t\", \"char8_t\", \"double\", \"float\", \"int\", \"long\", \"short\",\r\n\t\t\t\t\t\"void\", \"wchar_t\", \"unsigned\", \"signed\", \"const\", \"static\"\r\n\t\t\t\t],\r\n\t\t\t\tkeyword: [\"alignas\", \"alignof\", \"and\", \"and_eq\", \"asm\", \"atomic_cancel\", \"atomic_commit\",\r\n\t\t\t\t\t\"atomic_noexcept\", \"auto\", \"bitand\", \"bitor\", \"break\", \"case\", \"catch\", \"class\", \"co_await\",\r\n\t\t\t\t\t\"co_return\", \"co_yield\", \"compl\", \"concept\", \"const_cast|10\", \"consteval\", \"constexpr\", \"constinit\",\r\n\t\t\t\t\t\"continue\", \"decltype\", \"default\", \"delete\", \"do\", \"dynamic_cast|10\", \"else\", \"enum\", \"explicit\",\r\n\t\t\t\t\t\"export\", \"extern\", \"false\", \"final\", \"for\", \"friend\", \"goto\", \"if\", \"import\", \"inline\", \"module\",\r\n\t\t\t\t\t\"mutable\", \"namespace\", \"new\", \"noexcept\", \"not\", \"not_eq\", \"nullptr\", \"operator\", \"or\", \"or_eq\",\r\n\t\t\t\t\t\"override\", \"private\", \"protected\", \"public\", \"reflexpr\", \"register\", \"reinterpret_cast|10\",\r\n\t\t\t\t\t\"requires\", \"return\", \"sizeof\", \"static_assert\", \"static_cast|10\", \"struct\", \"switch\", \"synchronized\",\r\n\t\t\t\t\t\"template\", \"this\", \"thread_local\", \"throw\", \"transaction_safe\", \"transaction_safe_dynamic\", \"true\",\r\n\t\t\t\t\t\"try\", \"typedef\", \"typeid\", \"typename\", \"union\", \"using\", \"virtual\", \"volatile\", \"while\", \"xor\",\r\n\t\t\t\t\t\"xor_eq\"\r\n\t\t\t\t],\r\n\t\t\t\tliteral: [\"NULL\", \"false\", \"nullopt\", \"nullptr\", \"true\"],\r\n\t\t\t\tbuilt_in: [\"_Pragma\"],\r\n\t\t\t\t_type_hints: [\"any\", \"auto_ptr\", \"barrier\", \"binary_semaphore\", \"bitset\", \"complex\", \"condition_variable\",\r\n\t\t\t\t\t\"condition_variable_any\", \"counting_semaphore\", \"deque\", \"false_type\", \"future\", \"imaginary\",\r\n\t\t\t\t\t\"initializer_list\", \"istringstream\", \"jthread\", \"latch\", \"lock_guard\", \"multimap\", \"multiset\",\r\n\t\t\t\t\t\"mutex\", \"optional\", \"ostringstream\", \"packaged_task\", \"pair\", \"promise\", \"priority_queue\", \"queue\",\r\n\t\t\t\t\t\"recursive_mutex\", \"recursive_timed_mutex\", \"scoped_lock\", \"set\", \"shared_future\", \"shared_lock\",\r\n\t\t\t\t\t\"shared_mutex\", \"shared_timed_mutex\", \"shared_ptr\", \"stack\", \"string_view\", \"stringstream\",\r\n\t\t\t\t\t\"timed_mutex\", \"thread\", \"true_type\", \"tuple\", \"unique_lock\", \"unique_ptr\", \"unordered_map\",\r\n\t\t\t\t\t\"unordered_multimap\", \"unordered_multiset\", \"unordered_set\", \"variant\", \"vector\", \"weak_ptr\",\r\n\t\t\t\t\t\"wstring\", \"wstring_view\"\r\n\t\t\t\t]\r\n\t\t\t},\r\n\t\t\tu = {\r\n\t\t\t\tclassName: \"function.dispatch\",\r\n\t\t\t\trelevance: 0,\r\n\t\t\t\tkeywords: {\r\n\t\t\t\t\t_hint: [\"abort\", \"abs\", \"acos\", \"apply\", \"as_const\", \"asin\", \"atan\", \"atan2\", \"calloc\", \"ceil\", \"cerr\",\r\n\t\t\t\t\t\t\"cin\", \"clog\", \"cos\", \"cosh\", \"cout\", \"declval\", \"endl\", \"exchange\", \"exit\", \"exp\", \"fabs\", \"floor\",\r\n\t\t\t\t\t\t\"fmod\", \"forward\", \"fprintf\", \"fputs\", \"free\", \"frexp\", \"fscanf\", \"future\", \"invoke\", \"isalnum\",\r\n\t\t\t\t\t\t\"isalpha\", \"iscntrl\", \"isdigit\", \"isgraph\", \"islower\", \"isprint\", \"ispunct\", \"isspace\", \"isupper\",\r\n\t\t\t\t\t\t\"isxdigit\", \"labs\", \"launder\", \"ldexp\", \"log\", \"log10\", \"make_pair\", \"make_shared\",\r\n\t\t\t\t\t\t\"make_shared_for_overwrite\", \"make_tuple\", \"make_unique\", \"malloc\", \"memchr\", \"memcmp\", \"memcpy\",\r\n\t\t\t\t\t\t\"memset\", \"modf\", \"move\", \"pow\", \"printf\", \"putchar\", \"puts\", \"realloc\", \"scanf\", \"sin\", \"sinh\",\r\n\t\t\t\t\t\t\"snprintf\", \"sprintf\", \"sqrt\", \"sscanf\", \"std\", \"stderr\", \"stdin\", \"stdout\", \"strcat\", \"strchr\",\r\n\t\t\t\t\t\t\"strcmp\", \"strcpy\", \"strcspn\", \"strlen\", \"strncat\", \"strncmp\", \"strncpy\", \"strpbrk\", \"strrchr\",\r\n\t\t\t\t\t\t\"strspn\", \"strstr\", \"swap\", \"tan\", \"tanh\", \"terminate\", \"to_underlying\", \"tolower\", \"toupper\",\r\n\t\t\t\t\t\t\"vfprintf\", \"visit\", \"vprintf\", \"vsprintf\"\r\n\t\t\t\t\t]\r\n\t\t\t\t},\r\n\t\t\t\tbegin: n.concat(/\\b/, /(?!decltype)/, /(?!if)/, /(?!for)/, /(?!switch)/, /(?!while)/, e.IDENT_RE, n\r\n\t\t\t\t\t.lookahead(/(<[^<>]+>|)\\s*\\(/))\r\n\t\t\t},\r\n\t\t\tb = [u, l, r, t, e.C_BLOCK_COMMENT_MODE, o, s],\r\n\t\t\tm = {\r\n\t\t\t\tvariants: [{\r\n\t\t\t\t\tbegin: /=/,\r\n\t\t\t\t\tend: /;/\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: /\\(/,\r\n\t\t\t\t\tend: /\\)/\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbeginKeywords: \"new throw return else\",\r\n\t\t\t\t\tend: /;/\r\n\t\t\t\t}],\r\n\t\t\t\tkeywords: g,\r\n\t\t\t\tcontains: b.concat([{\r\n\t\t\t\t\tbegin: /\\(/,\r\n\t\t\t\t\tend: /\\)/,\r\n\t\t\t\t\tkeywords: g,\r\n\t\t\t\t\tcontains: b.concat([\"self\"]),\r\n\t\t\t\t\trelevance: 0\r\n\t\t\t\t}]),\r\n\t\t\t\trelevance: 0\r\n\t\t\t},\r\n\t\t\tp = {\r\n\t\t\t\tclassName: \"function\",\r\n\t\t\t\tbegin: \"(\" + i + \"[\\\\*&\\\\s]+)+\" + d,\r\n\t\t\t\treturnBegin: !0,\r\n\t\t\t\tend: /[{;=]/,\r\n\t\t\t\texcludeEnd: !0,\r\n\t\t\t\tkeywords: g,\r\n\t\t\t\tillegal: /[^\\w\\s\\*&:<>.]/,\r\n\t\t\t\tcontains: [{\r\n\t\t\t\t\tbegin: \"decltype\\\\(auto\\\\)\",\r\n\t\t\t\t\tkeywords: g,\r\n\t\t\t\t\trelevance: 0\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: d,\r\n\t\t\t\t\treturnBegin: !0,\r\n\t\t\t\t\tcontains: [c],\r\n\t\t\t\t\trelevance: 0\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: /::/,\r\n\t\t\t\t\trelevance: 0\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: /:/,\r\n\t\t\t\t\tendsWithParent: !0,\r\n\t\t\t\t\tcontains: [s, o]\r\n\t\t\t\t}, {\r\n\t\t\t\t\trelevance: 0,\r\n\t\t\t\t\tmatch: /,/\r\n\t\t\t\t}, {\r\n\t\t\t\t\tclassName: \"params\",\r\n\t\t\t\t\tbegin: /\\(/,\r\n\t\t\t\t\tend: /\\)/,\r\n\t\t\t\t\tkeywords: g,\r\n\t\t\t\t\trelevance: 0,\r\n\t\t\t\t\tcontains: [t, e.C_BLOCK_COMMENT_MODE, s, o, r, {\r\n\t\t\t\t\t\tbegin: /\\(/,\r\n\t\t\t\t\t\tend: /\\)/,\r\n\t\t\t\t\t\tkeywords: g,\r\n\t\t\t\t\t\trelevance: 0,\r\n\t\t\t\t\t\tcontains: [\"self\", t, e.C_BLOCK_COMMENT_MODE, s, o, r]\r\n\t\t\t\t\t}]\r\n\t\t\t\t}, r, t, e.C_BLOCK_COMMENT_MODE, l]\r\n\t\t\t};\r\n\t\treturn {\r\n\t\t\tname: \"C++\",\r\n\t\t\taliases: [\"cc\", \"c++\", \"h++\", \"hpp\", \"hh\", \"hxx\", \"cxx\"],\r\n\t\t\tkeywords: g,\r\n\t\t\tillegal: \"</\",\r\n\t\t\tclassNameAliases: {\r\n\t\t\t\t\"function.dispatch\": \"built_in\"\r\n\t\t\t},\r\n\t\t\tcontains: [].concat(m, p, u, b, [l, {\r\n\t\t\t\tbegin: \"\\\\b(deque|list|queue|priority_queue|pair|stack|vector|map|set|bitset|multiset|multimap|unordered_map|unordered_set|unordered_multiset|unordered_multimap|array|tuple|optional|variant|function)\\\\s*<(?!<)\",\r\n\t\t\t\tend: \">\",\r\n\t\t\t\tkeywords: g,\r\n\t\t\t\tcontains: [\"self\", r]\r\n\t\t\t}, {\r\n\t\t\t\tbegin: e.IDENT_RE + \"::\",\r\n\t\t\t\tkeywords: g\r\n\t\t\t}, {\r\n\t\t\t\tmatch: [/\\b(?:enum(?:\\s+(?:class|struct))?|class|struct|union)/, /\\s+/, /\\w+/],\r\n\t\t\t\tclassName: {\r\n\t\t\t\t\t1: \"keyword\",\r\n\t\t\t\t\t3: \"title.class\"\r\n\t\t\t\t}\r\n\t\t\t}])\r\n\t\t}\r\n\t},\r\n\tgrmr_csharp: e => {\r\n\t\tconst n = {\r\n\t\t\t\tkeyword: [\"abstract\", \"as\", \"base\", \"break\", \"case\", \"catch\", \"class\", \"const\", \"continue\", \"do\", \"else\",\r\n\t\t\t\t\t\"event\", \"explicit\", \"extern\", \"finally\", \"fixed\", \"for\", \"foreach\", \"goto\", \"if\", \"implicit\", \"in\",\r\n\t\t\t\t\t\"interface\", \"internal\", \"is\", \"lock\", \"namespace\", \"new\", \"operator\", \"out\", \"override\", \"params\",\r\n\t\t\t\t\t\"private\", \"protected\", \"public\", \"readonly\", \"record\", \"ref\", \"return\", \"scoped\", \"sealed\", \"sizeof\",\r\n\t\t\t\t\t\"stackalloc\", \"static\", \"struct\", \"switch\", \"this\", \"throw\", \"try\", \"typeof\", \"unchecked\", \"unsafe\",\r\n\t\t\t\t\t\"using\", \"virtual\", \"void\", \"volatile\", \"while\"\r\n\t\t\t\t].concat([\"add\", \"alias\", \"and\", \"ascending\", \"async\", \"await\", \"by\", \"descending\", \"equals\", \"from\",\r\n\t\t\t\t\t\"get\", \"global\", \"group\", \"init\", \"into\", \"join\", \"let\", \"nameof\", \"not\", \"notnull\", \"on\", \"or\",\r\n\t\t\t\t\t\"orderby\", \"partial\", \"remove\", \"select\", \"set\", \"unmanaged\", \"value|0\", \"var\", \"when\", \"where\",\r\n\t\t\t\t\t\"with\", \"yield\"\r\n\t\t\t\t]),\r\n\t\t\t\tbuilt_in: [\"bool\", \"byte\", \"char\", \"decimal\", \"delegate\", \"double\", \"dynamic\", \"enum\", \"float\", \"int\",\r\n\t\t\t\t\t\"long\", \"nint\", \"nuint\", \"object\", \"sbyte\", \"short\", \"string\", \"ulong\", \"uint\", \"ushort\"\r\n\t\t\t\t],\r\n\t\t\t\tliteral: [\"default\", \"false\", \"null\", \"true\"]\r\n\t\t\t},\r\n\t\t\tt = e.inherit(e.TITLE_MODE, {\r\n\t\t\t\tbegin: \"[a-zA-Z](\\\\.?\\\\w)*\"\r\n\t\t\t}),\r\n\t\t\ta = {\r\n\t\t\t\tclassName: \"number\",\r\n\t\t\t\tvariants: [{\r\n\t\t\t\t\tbegin: \"\\\\b(0b[01']+)\"\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: \"(-?)\\\\b([\\\\d']+(\\\\.[\\\\d']*)?|\\\\.[\\\\d']+)(u|U|l|L|ul|UL|f|F|b|B)\"\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: \"(-?)(\\\\b0[xX][a-fA-F0-9']+|(\\\\b[\\\\d']+(\\\\.[\\\\d']*)?|\\\\.[\\\\d']+)([eE][-+]?[\\\\d']+)?)\"\r\n\t\t\t\t}],\r\n\t\t\t\trelevance: 0\r\n\t\t\t},\r\n\t\t\ti = {\r\n\t\t\t\tclassName: \"string\",\r\n\t\t\t\tbegin: '@\"',\r\n\t\t\t\tend: '\"',\r\n\t\t\t\tcontains: [{\r\n\t\t\t\t\tbegin: '\"\"'\r\n\t\t\t\t}]\r\n\t\t\t},\r\n\t\t\tr = e.inherit(i, {\r\n\t\t\t\tillegal: /\\n/\r\n\t\t\t}),\r\n\t\t\ts = {\r\n\t\t\t\tclassName: \"subst\",\r\n\t\t\t\tbegin: /\\{/,\r\n\t\t\t\tend: /\\}/,\r\n\t\t\t\tkeywords: n\r\n\t\t\t},\r\n\t\t\to = e.inherit(s, {\r\n\t\t\t\tillegal: /\\n/\r\n\t\t\t}),\r\n\t\t\tl = {\r\n\t\t\t\tclassName: \"string\",\r\n\t\t\t\tbegin: /\\$\"/,\r\n\t\t\t\tend: '\"',\r\n\t\t\t\tillegal: /\\n/,\r\n\t\t\t\tcontains: [{\r\n\t\t\t\t\tbegin: /\\{\\{/\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: /\\}\\}/\r\n\t\t\t\t}, e.BACKSLASH_ESCAPE, o]\r\n\t\t\t},\r\n\t\t\tc = {\r\n\t\t\t\tclassName: \"string\",\r\n\t\t\t\tbegin: /\\$@\"/,\r\n\t\t\t\tend: '\"',\r\n\t\t\t\tcontains: [{\r\n\t\t\t\t\tbegin: /\\{\\{/\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: /\\}\\}/\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: '\"\"'\r\n\t\t\t\t}, s]\r\n\t\t\t},\r\n\t\t\td = e.inherit(c, {\r\n\t\t\t\tillegal: /\\n/,\r\n\t\t\t\tcontains: [{\r\n\t\t\t\t\tbegin: /\\{\\{/\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: /\\}\\}/\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: '\"\"'\r\n\t\t\t\t}, o]\r\n\t\t\t});\r\n\t\ts.contains = [c, l, i, e.APOS_STRING_MODE, e.QUOTE_STRING_MODE, a, e.C_BLOCK_COMMENT_MODE],\r\n\t\t\to.contains = [d, l, r, e.APOS_STRING_MODE, e.QUOTE_STRING_MODE, a, e.inherit(e.C_BLOCK_COMMENT_MODE, {\r\n\t\t\t\tillegal: /\\n/\r\n\t\t\t})];\r\n\t\tconst g = {\r\n\t\t\t\tvariants: [c, l, i, e.APOS_STRING_MODE, e.QUOTE_STRING_MODE]\r\n\t\t\t},\r\n\t\t\tu = {\r\n\t\t\t\tbegin: \"<\",\r\n\t\t\t\tend: \">\",\r\n\t\t\t\tcontains: [{\r\n\t\t\t\t\tbeginKeywords: \"in out\"\r\n\t\t\t\t}, t]\r\n\t\t\t},\r\n\t\t\tb = e.IDENT_RE + \"(<\" + e.IDENT_RE + \"(\\\\s*,\\\\s*\" + e.IDENT_RE + \")*>)?(\\\\[\\\\])?\",\r\n\t\t\tm = {\r\n\t\t\t\tbegin: \"@\" + e.IDENT_RE,\r\n\t\t\t\trelevance: 0\r\n\t\t\t};\r\n\t\treturn {\r\n\t\t\tname: \"C#\",\r\n\t\t\taliases: [\"cs\", \"c#\"],\r\n\t\t\tkeywords: n,\r\n\t\t\tillegal: /::/,\r\n\t\t\tcontains: [e.COMMENT(\"///\", \"$\", {\r\n\t\t\t\treturnBegin: !0,\r\n\t\t\t\tcontains: [{\r\n\t\t\t\t\tclassName: \"doctag\",\r\n\t\t\t\t\tvariants: [{\r\n\t\t\t\t\t\tbegin: \"///\",\r\n\t\t\t\t\t\trelevance: 0\r\n\t\t\t\t\t}, {\r\n\t\t\t\t\t\tbegin: \"\\x3c!--|--\\x3e\"\r\n\t\t\t\t\t}, {\r\n\t\t\t\t\t\tbegin: \"</?\",\r\n\t\t\t\t\t\tend: \">\"\r\n\t\t\t\t\t}]\r\n\t\t\t\t}]\r\n\t\t\t}), e.C_LINE_COMMENT_MODE, e.C_BLOCK_COMMENT_MODE, {\r\n\t\t\t\tclassName: \"meta\",\r\n\t\t\t\tbegin: \"#\",\r\n\t\t\t\tend: \"$\",\r\n\t\t\t\tkeywords: {\r\n\t\t\t\t\tkeyword: \"if else elif endif define undef warning error line region endregion pragma checksum\"\r\n\t\t\t\t}\r\n\t\t\t}, g, a, {\r\n\t\t\t\tbeginKeywords: \"class interface\",\r\n\t\t\t\trelevance: 0,\r\n\t\t\t\tend: /[{;=]/,\r\n\t\t\t\tillegal: /[^\\s:,]/,\r\n\t\t\t\tcontains: [{\r\n\t\t\t\t\tbeginKeywords: \"where class\"\r\n\t\t\t\t}, t, u, e.C_LINE_COMMENT_MODE, e.C_BLOCK_COMMENT_MODE]\r\n\t\t\t}, {\r\n\t\t\t\tbeginKeywords: \"namespace\",\r\n\t\t\t\trelevance: 0,\r\n\t\t\t\tend: /[{;=]/,\r\n\t\t\t\tillegal: /[^\\s:]/,\r\n\t\t\t\tcontains: [t, e.C_LINE_COMMENT_MODE, e.C_BLOCK_COMMENT_MODE]\r\n\t\t\t}, {\r\n\t\t\t\tbeginKeywords: \"record\",\r\n\t\t\t\trelevance: 0,\r\n\t\t\t\tend: /[{;=]/,\r\n\t\t\t\tillegal: /[^\\s:]/,\r\n\t\t\t\tcontains: [t, u, e.C_LINE_COMMENT_MODE, e.C_BLOCK_COMMENT_MODE]\r\n\t\t\t}, {\r\n\t\t\t\tclassName: \"meta\",\r\n\t\t\t\tbegin: \"^\\\\s*\\\\[(?=[\\\\w])\",\r\n\t\t\t\texcludeBegin: !0,\r\n\t\t\t\tend: \"\\\\]\",\r\n\t\t\t\texcludeEnd: !0,\r\n\t\t\t\tcontains: [{\r\n\t\t\t\t\tclassName: \"string\",\r\n\t\t\t\t\tbegin: /\"/,\r\n\t\t\t\t\tend: /\"/\r\n\t\t\t\t}]\r\n\t\t\t}, {\r\n\t\t\t\tbeginKeywords: \"new return throw await else\",\r\n\t\t\t\trelevance: 0\r\n\t\t\t}, {\r\n\t\t\t\tclassName: \"function\",\r\n\t\t\t\tbegin: \"(\" + b + \"\\\\s+)+\" + e.IDENT_RE + \"\\\\s*(<[^=]+>\\\\s*)?\\\\(\",\r\n\t\t\t\treturnBegin: !0,\r\n\t\t\t\tend: /\\s*[{;=]/,\r\n\t\t\t\texcludeEnd: !0,\r\n\t\t\t\tkeywords: n,\r\n\t\t\t\tcontains: [{\r\n\t\t\t\t\tbeginKeywords: \"public private protected static internal protected abstract async extern override unsafe virtual new sealed partial\",\r\n\t\t\t\t\trelevance: 0\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: e.IDENT_RE + \"\\\\s*(<[^=]+>\\\\s*)?\\\\(\",\r\n\t\t\t\t\treturnBegin: !0,\r\n\t\t\t\t\tcontains: [e.TITLE_MODE, u],\r\n\t\t\t\t\trelevance: 0\r\n\t\t\t\t}, {\r\n\t\t\t\t\tmatch: /\\(\\)/\r\n\t\t\t\t}, {\r\n\t\t\t\t\tclassName: \"params\",\r\n\t\t\t\t\tbegin: /\\(/,\r\n\t\t\t\t\tend: /\\)/,\r\n\t\t\t\t\texcludeBegin: !0,\r\n\t\t\t\t\texcludeEnd: !0,\r\n\t\t\t\t\tkeywords: n,\r\n\t\t\t\t\trelevance: 0,\r\n\t\t\t\t\tcontains: [g, a, e.C_BLOCK_COMMENT_MODE]\r\n\t\t\t\t}, e.C_LINE_COMMENT_MODE, e.C_BLOCK_COMMENT_MODE]\r\n\t\t\t}, m]\r\n\t\t}\r\n\t},\r\n\tgrmr_css: e => {\r\n\t\tconst n = e.regex,\r\n\t\t\tt = J(e),\r\n\t\t\ta = [e.APOS_STRING_MODE, e.QUOTE_STRING_MODE];\r\n\t\treturn {\r\n\t\t\tname: \"CSS\",\r\n\t\t\tcase_insensitive: !0,\r\n\t\t\tillegal: /[=|'\\$]/,\r\n\t\t\tkeywords: {\r\n\t\t\t\tkeyframePosition: \"from to\"\r\n\t\t\t},\r\n\t\t\tclassNameAliases: {\r\n\t\t\t\tkeyframePosition: \"selector-tag\"\r\n\t\t\t},\r\n\t\t\tcontains: [t.BLOCK_COMMENT, {\r\n\t\t\t\tbegin: /-(webkit|moz|ms|o)-(?=[a-z])/\r\n\t\t\t}, t.CSS_NUMBER_MODE, {\r\n\t\t\t\tclassName: \"selector-id\",\r\n\t\t\t\tbegin: /#[A-Za-z0-9_-]+/,\r\n\t\t\t\trelevance: 0\r\n\t\t\t}, {\r\n\t\t\t\tclassName: \"selector-class\",\r\n\t\t\t\tbegin: \"\\\\.[a-zA-Z-][a-zA-Z0-9_-]*\",\r\n\t\t\t\trelevance: 0\r\n\t\t\t}, t.ATTRIBUTE_SELECTOR_MODE, {\r\n\t\t\t\tclassName: \"selector-pseudo\",\r\n\t\t\t\tvariants: [{\r\n\t\t\t\t\tbegin: \":(\" + ne.join(\"|\") + \")\"\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: \":(:)?(\" + te.join(\"|\") + \")\"\r\n\t\t\t\t}]\r\n\t\t\t}, t.CSS_VARIABLE, {\r\n\t\t\t\tclassName: \"attribute\",\r\n\t\t\t\tbegin: \"\\\\b(\" + ae.join(\"|\") + \")\\\\b\"\r\n\t\t\t}, {\r\n\t\t\t\tbegin: /:/,\r\n\t\t\t\tend: /[;}{]/,\r\n\t\t\t\tcontains: [t.BLOCK_COMMENT, t.HEXCOLOR, t.IMPORTANT, t.CSS_NUMBER_MODE, ...a, {\r\n\t\t\t\t\tbegin: /(url|data-uri)\\(/,\r\n\t\t\t\t\tend: /\\)/,\r\n\t\t\t\t\trelevance: 0,\r\n\t\t\t\t\tkeywords: {\r\n\t\t\t\t\t\tbuilt_in: \"url data-uri\"\r\n\t\t\t\t\t},\r\n\t\t\t\t\tcontains: [...a, {\r\n\t\t\t\t\t\tclassName: \"string\",\r\n\t\t\t\t\t\tbegin: /[^)]/,\r\n\t\t\t\t\t\tendsWithParent: !0,\r\n\t\t\t\t\t\texcludeEnd: !0\r\n\t\t\t\t\t}]\r\n\t\t\t\t}, t.FUNCTION_DISPATCH]\r\n\t\t\t}, {\r\n\t\t\t\tbegin: n.lookahead(/@/),\r\n\t\t\t\tend: \"[{;]\",\r\n\t\t\t\trelevance: 0,\r\n\t\t\t\tillegal: /:/,\r\n\t\t\t\tcontains: [{\r\n\t\t\t\t\tclassName: \"keyword\",\r\n\t\t\t\t\tbegin: /@-?\\w[\\w]*(-\\w+)*/\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: /\\s/,\r\n\t\t\t\t\tendsWithParent: !0,\r\n\t\t\t\t\texcludeEnd: !0,\r\n\t\t\t\t\trelevance: 0,\r\n\t\t\t\t\tkeywords: {\r\n\t\t\t\t\t\t$pattern: /[a-z-]+/,\r\n\t\t\t\t\t\tkeyword: \"and or not only\",\r\n\t\t\t\t\t\tattribute: ee.join(\" \")\r\n\t\t\t\t\t},\r\n\t\t\t\t\tcontains: [{\r\n\t\t\t\t\t\tbegin: /[a-z-]+(?=:)/,\r\n\t\t\t\t\t\tclassName: \"attribute\"\r\n\t\t\t\t\t}, ...a, t.CSS_NUMBER_MODE]\r\n\t\t\t\t}]\r\n\t\t\t}, {\r\n\t\t\t\tclassName: \"selector-tag\",\r\n\t\t\t\tbegin: \"\\\\b(\" + Y.join(\"|\") + \")\\\\b\"\r\n\t\t\t}]\r\n\t\t}\r\n\t},\r\n\tgrmr_diff: e => {\r\n\t\tconst n = e.regex;\r\n\t\treturn {\r\n\t\t\tname: \"Diff\",\r\n\t\t\taliases: [\"patch\"],\r\n\t\t\tcontains: [{\r\n\t\t\t\tclassName: \"meta\",\r\n\t\t\t\trelevance: 10,\r\n\t\t\t\tmatch: n.either(/^@@ +-\\d+,\\d+ +\\+\\d+,\\d+ +@@/, /^\\*\\*\\* +\\d+,\\d+ +\\*\\*\\*\\*$/, /^--- +\\d+,\\d+ +----$/)\r\n\t\t\t}, {\r\n\t\t\t\tclassName: \"comment\",\r\n\t\t\t\tvariants: [{\r\n\t\t\t\t\tbegin: n.either(/Index: /, /^index/, /={3,}/, /^-{3}/, /^\\*{3} /, /^\\+{3}/, /^diff --git/),\r\n\t\t\t\t\tend: /$/\r\n\t\t\t\t}, {\r\n\t\t\t\t\tmatch: /^\\*{15}$/\r\n\t\t\t\t}]\r\n\t\t\t}, {\r\n\t\t\t\tclassName: \"addition\",\r\n\t\t\t\tbegin: /^\\+/,\r\n\t\t\t\tend: /$/\r\n\t\t\t}, {\r\n\t\t\t\tclassName: \"deletion\",\r\n\t\t\t\tbegin: /^-/,\r\n\t\t\t\tend: /$/\r\n\t\t\t}, {\r\n\t\t\t\tclassName: \"addition\",\r\n\t\t\t\tbegin: /^!/,\r\n\t\t\t\tend: /$/\r\n\t\t\t}]\r\n\t\t}\r\n\t},\r\n\tgrmr_go: e => {\r\n\t\tconst n = {\r\n\t\t\tkeyword: [\"break\", \"case\", \"chan\", \"const\", \"continue\", \"default\", \"defer\", \"else\", \"fallthrough\", \"for\",\r\n\t\t\t\t\"func\", \"go\", \"goto\", \"if\", \"import\", \"interface\", \"map\", \"package\", \"range\", \"return\", \"select\",\r\n\t\t\t\t\"struct\", \"switch\", \"type\", \"var\"\r\n\t\t\t],\r\n\t\t\ttype: [\"bool\", \"byte\", \"complex64\", \"complex128\", \"error\", \"float32\", \"float64\", \"int8\", \"int16\", \"int32\",\r\n\t\t\t\t\"int64\", \"string\", \"uint8\", \"uint16\", \"uint32\", \"uint64\", \"int\", \"uint\", \"uintptr\", \"rune\"\r\n\t\t\t],\r\n\t\t\tliteral: [\"true\", \"false\", \"iota\", \"nil\"],\r\n\t\t\tbuilt_in: [\"append\", \"cap\", \"close\", \"complex\", \"copy\", \"imag\", \"len\", \"make\", \"new\", \"panic\", \"print\",\r\n\t\t\t\t\"println\", \"real\", \"recover\", \"delete\"\r\n\t\t\t]\r\n\t\t};\r\n\t\treturn {\r\n\t\t\tname: \"Go\",\r\n\t\t\taliases: [\"golang\"],\r\n\t\t\tkeywords: n,\r\n\t\t\tillegal: \"</\",\r\n\t\t\tcontains: [e.C_LINE_COMMENT_MODE, e.C_BLOCK_COMMENT_MODE, {\r\n\t\t\t\tclassName: \"string\",\r\n\t\t\t\tvariants: [e.QUOTE_STRING_MODE, e.APOS_STRING_MODE, {\r\n\t\t\t\t\tbegin: \"`\",\r\n\t\t\t\t\tend: \"`\"\r\n\t\t\t\t}]\r\n\t\t\t}, {\r\n\t\t\t\tclassName: \"number\",\r\n\t\t\t\tvariants: [{\r\n\t\t\t\t\tbegin: e.C_NUMBER_RE + \"[i]\",\r\n\t\t\t\t\trelevance: 1\r\n\t\t\t\t}, e.C_NUMBER_MODE]\r\n\t\t\t}, {\r\n\t\t\t\tbegin: /:=/\r\n\t\t\t}, {\r\n\t\t\t\tclassName: \"function\",\r\n\t\t\t\tbeginKeywords: \"func\",\r\n\t\t\t\tend: \"\\\\s*(\\\\{|$)\",\r\n\t\t\t\texcludeEnd: !0,\r\n\t\t\t\tcontains: [e.TITLE_MODE, {\r\n\t\t\t\t\tclassName: \"params\",\r\n\t\t\t\t\tbegin: /\\(/,\r\n\t\t\t\t\tend: /\\)/,\r\n\t\t\t\t\tendsParent: !0,\r\n\t\t\t\t\tkeywords: n,\r\n\t\t\t\t\tillegal: /[\"']/\r\n\t\t\t\t}]\r\n\t\t\t}]\r\n\t\t}\r\n\t},\r\n\tgrmr_graphql: e => {\r\n\t\tconst n = e.regex;\r\n\t\treturn {\r\n\t\t\tname: \"GraphQL\",\r\n\t\t\taliases: [\"gql\"],\r\n\t\t\tcase_insensitive: !0,\r\n\t\t\tdisableAutodetect: !1,\r\n\t\t\tkeywords: {\r\n\t\t\t\tkeyword: [\"query\", \"mutation\", \"subscription\", \"type\", \"input\", \"schema\", \"directive\", \"interface\",\r\n\t\t\t\t\t\"union\", \"scalar\", \"fragment\", \"enum\", \"on\"\r\n\t\t\t\t],\r\n\t\t\t\tliteral: [\"true\", \"false\", \"null\"]\r\n\t\t\t},\r\n\t\t\tcontains: [e.HASH_COMMENT_MODE, e.QUOTE_STRING_MODE, e.NUMBER_MODE, {\r\n\t\t\t\tscope: \"punctuation\",\r\n\t\t\t\tmatch: /[.]{3}/,\r\n\t\t\t\trelevance: 0\r\n\t\t\t}, {\r\n\t\t\t\tscope: \"punctuation\",\r\n\t\t\t\tbegin: /[\\!\\(\\)\\:\\=\\[\\]\\{\\|\\}]{1}/,\r\n\t\t\t\trelevance: 0\r\n\t\t\t}, {\r\n\t\t\t\tscope: \"variable\",\r\n\t\t\t\tbegin: /\\$/,\r\n\t\t\t\tend: /\\W/,\r\n\t\t\t\texcludeEnd: !0,\r\n\t\t\t\trelevance: 0\r\n\t\t\t}, {\r\n\t\t\t\tscope: \"meta\",\r\n\t\t\t\tmatch: /@\\w+/,\r\n\t\t\t\texcludeEnd: !0\r\n\t\t\t}, {\r\n\t\t\t\tscope: \"symbol\",\r\n\t\t\t\tbegin: n.concat(/[_A-Za-z][_0-9A-Za-z]*/, n.lookahead(/\\s*:/)),\r\n\t\t\t\trelevance: 0\r\n\t\t\t}],\r\n\t\t\tillegal: [/[;<']/, /BEGIN/]\r\n\t\t}\r\n\t},\r\n\tgrmr_ini: e => {\r\n\t\tconst n = e.regex,\r\n\t\t\tt = {\r\n\t\t\t\tclassName: \"number\",\r\n\t\t\t\trelevance: 0,\r\n\t\t\t\tvariants: [{\r\n\t\t\t\t\tbegin: /([+-]+)?[\\d]+_[\\d_]+/\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: e.NUMBER_RE\r\n\t\t\t\t}]\r\n\t\t\t},\r\n\t\t\ta = e.COMMENT();\r\n\t\ta.variants = [{\r\n\t\t\tbegin: /;/,\r\n\t\t\tend: /$/\r\n\t\t}, {\r\n\t\t\tbegin: /#/,\r\n\t\t\tend: /$/\r\n\t\t}];\r\n\t\tconst i = {\r\n\t\t\t\tclassName: \"variable\",\r\n\t\t\t\tvariants: [{\r\n\t\t\t\t\tbegin: /\\$[\\w\\d\"][\\w\\d_]*/\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: /\\$\\{(.*?)\\}/\r\n\t\t\t\t}]\r\n\t\t\t},\r\n\t\t\tr = {\r\n\t\t\t\tclassName: \"literal\",\r\n\t\t\t\tbegin: /\\bon|off|true|false|yes|no\\b/\r\n\t\t\t},\r\n\t\t\ts = {\r\n\t\t\t\tclassName: \"string\",\r\n\t\t\t\tcontains: [e.BACKSLASH_ESCAPE],\r\n\t\t\t\tvariants: [{\r\n\t\t\t\t\tbegin: \"'''\",\r\n\t\t\t\t\tend: \"'''\",\r\n\t\t\t\t\trelevance: 10\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: '\"\"\"',\r\n\t\t\t\t\tend: '\"\"\"',\r\n\t\t\t\t\trelevance: 10\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: '\"',\r\n\t\t\t\t\tend: '\"'\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: \"'\",\r\n\t\t\t\t\tend: \"'\"\r\n\t\t\t\t}]\r\n\t\t\t},\r\n\t\t\to = {\r\n\t\t\t\tbegin: /\\[/,\r\n\t\t\t\tend: /\\]/,\r\n\t\t\t\tcontains: [a, r, i, s, t, \"self\"],\r\n\t\t\t\trelevance: 0\r\n\t\t\t},\r\n\t\t\tl = n.either(/[A-Za-z0-9_-]+/, /\"(\\\\\"|[^\"])*\"/, /'[^']*'/);\r\n\t\treturn {\r\n\t\t\tname: \"TOML, also INI\",\r\n\t\t\taliases: [\"toml\"],\r\n\t\t\tcase_insensitive: !0,\r\n\t\t\tillegal: /\\S/,\r\n\t\t\tcontains: [a, {\r\n\t\t\t\tclassName: \"section\",\r\n\t\t\t\tbegin: /\\[+/,\r\n\t\t\t\tend: /\\]+/\r\n\t\t\t}, {\r\n\t\t\t\tbegin: n.concat(l, \"(\\\\s*\\\\.\\\\s*\", l, \")*\", n.lookahead(/\\s*=\\s*[^#\\s]/)),\r\n\t\t\t\tclassName: \"attr\",\r\n\t\t\t\tstarts: {\r\n\t\t\t\t\tend: /$/,\r\n\t\t\t\t\tcontains: [a, o, r, i, s, t]\r\n\t\t\t\t}\r\n\t\t\t}]\r\n\t\t}\r\n\t},\r\n\tgrmr_java: e => {\r\n\t\tconst n = e.regex,\r\n\t\t\tt = \"[\\xc0-\\u02b8a-zA-Z_$][\\xc0-\\u02b8a-zA-Z_$0-9]*\",\r\n\t\t\ta = t + le(\"(?:<\" + t + \"~~~(?:\\\\s*,\\\\s*\" + t + \"~~~)*>)?\", /~~~/g, 2),\r\n\t\t\ti = {\r\n\t\t\t\tkeyword: [\"synchronized\", \"abstract\", \"private\", \"var\", \"static\", \"if\", \"const \", \"for\", \"while\",\r\n\t\t\t\t\t\"strictfp\", \"finally\", \"protected\", \"import\", \"native\", \"final\", \"void\", \"enum\", \"else\", \"break\",\r\n\t\t\t\t\t\"transient\", \"catch\", \"instanceof\", \"volatile\", \"case\", \"assert\", \"package\", \"default\", \"public\",\r\n\t\t\t\t\t\"try\", \"switch\", \"continue\", \"throws\", \"protected\", \"public\", \"private\", \"module\", \"requires\",\r\n\t\t\t\t\t\"exports\", \"do\", \"sealed\", \"yield\", \"permits\"\r\n\t\t\t\t],\r\n\t\t\t\tliteral: [\"false\", \"true\", \"null\"],\r\n\t\t\t\ttype: [\"char\", \"boolean\", \"long\", \"float\", \"int\", \"byte\", \"short\", \"double\"],\r\n\t\t\t\tbuilt_in: [\"super\", \"this\"]\r\n\t\t\t},\r\n\t\t\tr = {\r\n\t\t\t\tclassName: \"meta\",\r\n\t\t\t\tbegin: \"@\" + t,\r\n\t\t\t\tcontains: [{\r\n\t\t\t\t\tbegin: /\\(/,\r\n\t\t\t\t\tend: /\\)/,\r\n\t\t\t\t\tcontains: [\"self\"]\r\n\t\t\t\t}]\r\n\t\t\t},\r\n\t\t\ts = {\r\n\t\t\t\tclassName: \"params\",\r\n\t\t\t\tbegin: /\\(/,\r\n\t\t\t\tend: /\\)/,\r\n\t\t\t\tkeywords: i,\r\n\t\t\t\trelevance: 0,\r\n\t\t\t\tcontains: [e.C_BLOCK_COMMENT_MODE],\r\n\t\t\t\tendsParent: !0\r\n\t\t\t};\r\n\t\treturn {\r\n\t\t\tname: \"Java\",\r\n\t\t\taliases: [\"jsp\"],\r\n\t\t\tkeywords: i,\r\n\t\t\tillegal: /<\\/|#/,\r\n\t\t\tcontains: [e.COMMENT(\"/\\\\*\\\\*\", \"\\\\*/\", {\r\n\t\t\t\trelevance: 0,\r\n\t\t\t\tcontains: [{\r\n\t\t\t\t\tbegin: /\\w+@/,\r\n\t\t\t\t\trelevance: 0\r\n\t\t\t\t}, {\r\n\t\t\t\t\tclassName: \"doctag\",\r\n\t\t\t\t\tbegin: \"@[A-Za-z]+\"\r\n\t\t\t\t}]\r\n\t\t\t}), {\r\n\t\t\t\tbegin: /import java\\.[a-z]+\\./,\r\n\t\t\t\tkeywords: \"import\",\r\n\t\t\t\trelevance: 2\r\n\t\t\t}, e.C_LINE_COMMENT_MODE, e.C_BLOCK_COMMENT_MODE, {\r\n\t\t\t\tbegin: /\"\"\"/,\r\n\t\t\t\tend: /\"\"\"/,\r\n\t\t\t\tclassName: \"string\",\r\n\t\t\t\tcontains: [e.BACKSLASH_ESCAPE]\r\n\t\t\t}, e.APOS_STRING_MODE, e.QUOTE_STRING_MODE, {\r\n\t\t\t\tmatch: [/\\b(?:class|interface|enum|extends|implements|new)/, /\\s+/, t],\r\n\t\t\t\tclassName: {\r\n\t\t\t\t\t1: \"keyword\",\r\n\t\t\t\t\t3: \"title.class\"\r\n\t\t\t\t}\r\n\t\t\t}, {\r\n\t\t\t\tmatch: /non-sealed/,\r\n\t\t\t\tscope: \"keyword\"\r\n\t\t\t}, {\r\n\t\t\t\tbegin: [n.concat(/(?!else)/, t), /\\s+/, t, /\\s+/, /=(?!=)/],\r\n\t\t\t\tclassName: {\r\n\t\t\t\t\t1: \"type\",\r\n\t\t\t\t\t3: \"variable\",\r\n\t\t\t\t\t5: \"operator\"\r\n\t\t\t\t}\r\n\t\t\t}, {\r\n\t\t\t\tbegin: [/record/, /\\s+/, t],\r\n\t\t\t\tclassName: {\r\n\t\t\t\t\t1: \"keyword\",\r\n\t\t\t\t\t3: \"title.class\"\r\n\t\t\t\t},\r\n\t\t\t\tcontains: [s, e.C_LINE_COMMENT_MODE, e.C_BLOCK_COMMENT_MODE]\r\n\t\t\t}, {\r\n\t\t\t\tbeginKeywords: \"new throw return else\",\r\n\t\t\t\trelevance: 0\r\n\t\t\t}, {\r\n\t\t\t\tbegin: [\"(?:\" + a + \"\\\\s+)\", e.UNDERSCORE_IDENT_RE, /\\s*(?=\\()/],\r\n\t\t\t\tclassName: {\r\n\t\t\t\t\t2: \"title.function\"\r\n\t\t\t\t},\r\n\t\t\t\tkeywords: i,\r\n\t\t\t\tcontains: [{\r\n\t\t\t\t\tclassName: \"params\",\r\n\t\t\t\t\tbegin: /\\(/,\r\n\t\t\t\t\tend: /\\)/,\r\n\t\t\t\t\tkeywords: i,\r\n\t\t\t\t\trelevance: 0,\r\n\t\t\t\t\tcontains: [r, e.APOS_STRING_MODE, e.QUOTE_STRING_MODE, oe, e.C_BLOCK_COMMENT_MODE]\r\n\t\t\t\t}, e.C_LINE_COMMENT_MODE, e.C_BLOCK_COMMENT_MODE]\r\n\t\t\t}, oe, r]\r\n\t\t}\r\n\t},\r\n\tgrmr_javascript: he,\r\n\tgrmr_json: e => {\r\n\t\tconst n = [\"true\", \"false\", \"null\"],\r\n\t\t\tt = {\r\n\t\t\t\tscope: \"literal\",\r\n\t\t\t\tbeginKeywords: n.join(\" \")\r\n\t\t\t};\r\n\t\treturn {\r\n\t\t\tname: \"JSON\",\r\n\t\t\tkeywords: {\r\n\t\t\t\tliteral: n\r\n\t\t\t},\r\n\t\t\tcontains: [{\r\n\t\t\t\tclassName: \"attr\",\r\n\t\t\t\tbegin: /\"(\\\\.|[^\\\\\"\\r\\n])*\"(?=\\s*:)/,\r\n\t\t\t\trelevance: 1.01\r\n\t\t\t}, {\r\n\t\t\t\tmatch: /[{}[\\],:]/,\r\n\t\t\t\tclassName: \"punctuation\",\r\n\t\t\t\trelevance: 0\r\n\t\t\t}, e.QUOTE_STRING_MODE, t, e.C_NUMBER_MODE, e.C_LINE_COMMENT_MODE, e.C_BLOCK_COMMENT_MODE],\r\n\t\t\tillegal: \"\\\\S\"\r\n\t\t}\r\n\t},\r\n\tgrmr_kotlin: e => {\r\n\t\tconst n = {\r\n\t\t\t\tkeyword: \"abstract as val var vararg get set class object open private protected public noinline crossinline dynamic final enum if else do while for when throw try catch finally import package is in fun override companion reified inline lateinit init interface annotation data sealed internal infix operator out by constructor super tailrec where const inner suspend typealias external expect actual\",\r\n\t\t\t\tbuilt_in: \"Byte Short Char Int Long Boolean Float Double Void Unit Nothing\",\r\n\t\t\t\tliteral: \"true false null\"\r\n\t\t\t},\r\n\t\t\tt = {\r\n\t\t\t\tclassName: \"symbol\",\r\n\t\t\t\tbegin: e.UNDERSCORE_IDENT_RE + \"@\"\r\n\t\t\t},\r\n\t\t\ta = {\r\n\t\t\t\tclassName: \"subst\",\r\n\t\t\t\tbegin: /\\$\\{/,\r\n\t\t\t\tend: /\\}/,\r\n\t\t\t\tcontains: [e.C_NUMBER_MODE]\r\n\t\t\t},\r\n\t\t\ti = {\r\n\t\t\t\tclassName: \"variable\",\r\n\t\t\t\tbegin: \"\\\\$\" + e.UNDERSCORE_IDENT_RE\r\n\t\t\t},\r\n\t\t\tr = {\r\n\t\t\t\tclassName: \"string\",\r\n\t\t\t\tvariants: [{\r\n\t\t\t\t\tbegin: '\"\"\"',\r\n\t\t\t\t\tend: '\"\"\"(?=[^\"])',\r\n\t\t\t\t\tcontains: [i, a]\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: \"'\",\r\n\t\t\t\t\tend: \"'\",\r\n\t\t\t\t\tillegal: /\\n/,\r\n\t\t\t\t\tcontains: [e.BACKSLASH_ESCAPE]\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: '\"',\r\n\t\t\t\t\tend: '\"',\r\n\t\t\t\t\tillegal: /\\n/,\r\n\t\t\t\t\tcontains: [e.BACKSLASH_ESCAPE, i, a]\r\n\t\t\t\t}]\r\n\t\t\t};\r\n\t\ta.contains.push(r);\r\n\t\tconst s = {\r\n\t\t\t\tclassName: \"meta\",\r\n\t\t\t\tbegin: \"@(?:file|property|field|get|set|receiver|param|setparam|delegate)\\\\s*:(?:\\\\s*\" + e\r\n\t\t\t\t\t.UNDERSCORE_IDENT_RE + \")?\"\r\n\t\t\t},\r\n\t\t\to = {\r\n\t\t\t\tclassName: \"meta\",\r\n\t\t\t\tbegin: \"@\" + e.UNDERSCORE_IDENT_RE,\r\n\t\t\t\tcontains: [{\r\n\t\t\t\t\tbegin: /\\(/,\r\n\t\t\t\t\tend: /\\)/,\r\n\t\t\t\t\tcontains: [e.inherit(r, {\r\n\t\t\t\t\t\tclassName: \"string\"\r\n\t\t\t\t\t}), \"self\"]\r\n\t\t\t\t}]\r\n\t\t\t},\r\n\t\t\tl = oe,\r\n\t\t\tc = e.COMMENT(\"/\\\\*\", \"\\\\*/\", {\r\n\t\t\t\tcontains: [e.C_BLOCK_COMMENT_MODE]\r\n\t\t\t}),\r\n\t\t\td = {\r\n\t\t\t\tvariants: [{\r\n\t\t\t\t\tclassName: \"type\",\r\n\t\t\t\t\tbegin: e.UNDERSCORE_IDENT_RE\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: /\\(/,\r\n\t\t\t\t\tend: /\\)/,\r\n\t\t\t\t\tcontains: []\r\n\t\t\t\t}]\r\n\t\t\t},\r\n\t\t\tg = d;\r\n\t\treturn g.variants[1].contains = [d], d.variants[1].contains = [g], {\r\n\t\t\tname: \"Kotlin\",\r\n\t\t\taliases: [\"kt\", \"kts\"],\r\n\t\t\tkeywords: n,\r\n\t\t\tcontains: [e.COMMENT(\"/\\\\*\\\\*\", \"\\\\*/\", {\r\n\t\t\t\trelevance: 0,\r\n\t\t\t\tcontains: [{\r\n\t\t\t\t\tclassName: \"doctag\",\r\n\t\t\t\t\tbegin: \"@[A-Za-z]+\"\r\n\t\t\t\t}]\r\n\t\t\t}), e.C_LINE_COMMENT_MODE, c, {\r\n\t\t\t\tclassName: \"keyword\",\r\n\t\t\t\tbegin: /\\b(break|continue|return|this)\\b/,\r\n\t\t\t\tstarts: {\r\n\t\t\t\t\tcontains: [{\r\n\t\t\t\t\t\tclassName: \"symbol\",\r\n\t\t\t\t\t\tbegin: /@\\w+/\r\n\t\t\t\t\t}]\r\n\t\t\t\t}\r\n\t\t\t}, t, s, o, {\r\n\t\t\t\tclassName: \"function\",\r\n\t\t\t\tbeginKeywords: \"fun\",\r\n\t\t\t\tend: \"[(]|$\",\r\n\t\t\t\treturnBegin: !0,\r\n\t\t\t\texcludeEnd: !0,\r\n\t\t\t\tkeywords: n,\r\n\t\t\t\trelevance: 5,\r\n\t\t\t\tcontains: [{\r\n\t\t\t\t\tbegin: e.UNDERSCORE_IDENT_RE + \"\\\\s*\\\\(\",\r\n\t\t\t\t\treturnBegin: !0,\r\n\t\t\t\t\trelevance: 0,\r\n\t\t\t\t\tcontains: [e.UNDERSCORE_TITLE_MODE]\r\n\t\t\t\t}, {\r\n\t\t\t\t\tclassName: \"type\",\r\n\t\t\t\t\tbegin: /</,\r\n\t\t\t\t\tend: />/,\r\n\t\t\t\t\tkeywords: \"reified\",\r\n\t\t\t\t\trelevance: 0\r\n\t\t\t\t}, {\r\n\t\t\t\t\tclassName: \"params\",\r\n\t\t\t\t\tbegin: /\\(/,\r\n\t\t\t\t\tend: /\\)/,\r\n\t\t\t\t\tendsParent: !0,\r\n\t\t\t\t\tkeywords: n,\r\n\t\t\t\t\trelevance: 0,\r\n\t\t\t\t\tcontains: [{\r\n\t\t\t\t\t\tbegin: /:/,\r\n\t\t\t\t\t\tend: /[=,\\/]/,\r\n\t\t\t\t\t\tendsWithParent: !0,\r\n\t\t\t\t\t\tcontains: [d, e.C_LINE_COMMENT_MODE, c],\r\n\t\t\t\t\t\trelevance: 0\r\n\t\t\t\t\t}, e.C_LINE_COMMENT_MODE, c, s, o, r, e.C_NUMBER_MODE]\r\n\t\t\t\t}, c]\r\n\t\t\t}, {\r\n\t\t\t\tbegin: [/class|interface|trait/, /\\s+/, e.UNDERSCORE_IDENT_RE],\r\n\t\t\t\tbeginScope: {\r\n\t\t\t\t\t3: \"title.class\"\r\n\t\t\t\t},\r\n\t\t\t\tkeywords: \"class interface trait\",\r\n\t\t\t\tend: /[:\\{(]|$/,\r\n\t\t\t\texcludeEnd: !0,\r\n\t\t\t\tillegal: \"extends implements\",\r\n\t\t\t\tcontains: [{\r\n\t\t\t\t\tbeginKeywords: \"public protected internal private constructor\"\r\n\t\t\t\t}, e.UNDERSCORE_TITLE_MODE, {\r\n\t\t\t\t\tclassName: \"type\",\r\n\t\t\t\t\tbegin: /</,\r\n\t\t\t\t\tend: />/,\r\n\t\t\t\t\texcludeBegin: !0,\r\n\t\t\t\t\texcludeEnd: !0,\r\n\t\t\t\t\trelevance: 0\r\n\t\t\t\t}, {\r\n\t\t\t\t\tclassName: \"type\",\r\n\t\t\t\t\tbegin: /[,:]\\s*/,\r\n\t\t\t\t\tend: /[<\\(,){\\s]|$/,\r\n\t\t\t\t\texcludeBegin: !0,\r\n\t\t\t\t\treturnEnd: !0\r\n\t\t\t\t}, s, o]\r\n\t\t\t}, r, {\r\n\t\t\t\tclassName: \"meta\",\r\n\t\t\t\tbegin: \"^#!/usr/bin/env\",\r\n\t\t\t\tend: \"$\",\r\n\t\t\t\tillegal: \"\\n\"\r\n\t\t\t}, l]\r\n\t\t}\r\n\t},\r\n\tgrmr_less: e => {\r\n\t\tconst n = J(e),\r\n\t\t\tt = ie,\r\n\t\t\ta = \"([\\\\w-]+|@\\\\{[\\\\w-]+\\\\})\",\r\n\t\t\ti = [],\r\n\t\t\tr = [],\r\n\t\t\ts = e => ({\r\n\t\t\t\tclassName: \"string\",\r\n\t\t\t\tbegin: \"~?\" + e + \".*?\" + e\r\n\t\t\t}),\r\n\t\t\to = (e, n, t) => ({\r\n\t\t\t\tclassName: e,\r\n\t\t\t\tbegin: n,\r\n\t\t\t\trelevance: t\r\n\t\t\t}),\r\n\t\t\tl = {\r\n\t\t\t\t$pattern: /[a-z-]+/,\r\n\t\t\t\tkeyword: \"and or not only\",\r\n\t\t\t\tattribute: ee.join(\" \")\r\n\t\t\t},\r\n\t\t\tc = {\r\n\t\t\t\tbegin: \"\\\\(\",\r\n\t\t\t\tend: \"\\\\)\",\r\n\t\t\t\tcontains: r,\r\n\t\t\t\tkeywords: l,\r\n\t\t\t\trelevance: 0\r\n\t\t\t};\r\n\t\tr.push(e.C_LINE_COMMENT_MODE, e.C_BLOCK_COMMENT_MODE, s(\"'\"), s('\"'), n.CSS_NUMBER_MODE, {\r\n\t\t\tbegin: \"(url|data-uri)\\\\(\",\r\n\t\t\tstarts: {\r\n\t\t\t\tclassName: \"string\",\r\n\t\t\t\tend: \"[\\\\)\\\\n]\",\r\n\t\t\t\texcludeEnd: !0\r\n\t\t\t}\r\n\t\t}, n.HEXCOLOR, c, o(\"variable\", \"@@?[\\\\w-]+\", 10), o(\"variable\", \"@\\\\{[\\\\w-]+\\\\}\"), o(\"built_in\",\r\n\t\t\t\"~?`[^`]*?`\"), {\r\n\t\t\tclassName: \"attribute\",\r\n\t\t\tbegin: \"[\\\\w-]+\\\\s*:\",\r\n\t\t\tend: \":\",\r\n\t\t\treturnBegin: !0,\r\n\t\t\texcludeEnd: !0\r\n\t\t}, n.IMPORTANT, {\r\n\t\t\tbeginKeywords: \"and not\"\r\n\t\t}, n.FUNCTION_DISPATCH);\r\n\t\tconst d = r.concat({\r\n\t\t\t\tbegin: /\\{/,\r\n\t\t\t\tend: /\\}/,\r\n\t\t\t\tcontains: i\r\n\t\t\t}),\r\n\t\t\tg = {\r\n\t\t\t\tbeginKeywords: \"when\",\r\n\t\t\t\tendsWithParent: !0,\r\n\t\t\t\tcontains: [{\r\n\t\t\t\t\tbeginKeywords: \"and not\"\r\n\t\t\t\t}].concat(r)\r\n\t\t\t},\r\n\t\t\tu = {\r\n\t\t\t\tbegin: a + \"\\\\s*:\",\r\n\t\t\t\treturnBegin: !0,\r\n\t\t\t\tend: /[;}]/,\r\n\t\t\t\trelevance: 0,\r\n\t\t\t\tcontains: [{\r\n\t\t\t\t\tbegin: /-(webkit|moz|ms|o)-/\r\n\t\t\t\t}, n.CSS_VARIABLE, {\r\n\t\t\t\t\tclassName: \"attribute\",\r\n\t\t\t\t\tbegin: \"\\\\b(\" + ae.join(\"|\") + \")\\\\b\",\r\n\t\t\t\t\tend: /(?=:)/,\r\n\t\t\t\t\tstarts: {\r\n\t\t\t\t\t\tendsWithParent: !0,\r\n\t\t\t\t\t\tillegal: \"[<=$]\",\r\n\t\t\t\t\t\trelevance: 0,\r\n\t\t\t\t\t\tcontains: r\r\n\t\t\t\t\t}\r\n\t\t\t\t}]\r\n\t\t\t},\r\n\t\t\tb = {\r\n\t\t\t\tclassName: \"keyword\",\r\n\t\t\t\tbegin: \"@(import|media|charset|font-face|(-[a-z]+-)?keyframes|supports|document|namespace|page|viewport|host)\\\\b\",\r\n\t\t\t\tstarts: {\r\n\t\t\t\t\tend: \"[;{}]\",\r\n\t\t\t\t\tkeywords: l,\r\n\t\t\t\t\treturnEnd: !0,\r\n\t\t\t\t\tcontains: r,\r\n\t\t\t\t\trelevance: 0\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tm = {\r\n\t\t\t\tclassName: \"variable\",\r\n\t\t\t\tvariants: [{\r\n\t\t\t\t\tbegin: \"@[\\\\w-]+\\\\s*:\",\r\n\t\t\t\t\trelevance: 15\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: \"@[\\\\w-]+\"\r\n\t\t\t\t}],\r\n\t\t\t\tstarts: {\r\n\t\t\t\t\tend: \"[;}]\",\r\n\t\t\t\t\treturnEnd: !0,\r\n\t\t\t\t\tcontains: d\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tp = {\r\n\t\t\t\tvariants: [{\r\n\t\t\t\t\tbegin: \"[\\\\.#:&\\\\[>]\",\r\n\t\t\t\t\tend: \"[;{}]\"\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: a,\r\n\t\t\t\t\tend: /\\{/\r\n\t\t\t\t}],\r\n\t\t\t\treturnBegin: !0,\r\n\t\t\t\treturnEnd: !0,\r\n\t\t\t\tillegal: \"[<='$\\\"]\",\r\n\t\t\t\trelevance: 0,\r\n\t\t\t\tcontains: [e.C_LINE_COMMENT_MODE, e.C_BLOCK_COMMENT_MODE, g, o(\"keyword\", \"all\\\\b\"), o(\"variable\",\r\n\t\t\t\t\t\"@\\\\{[\\\\w-]+\\\\}\"), {\r\n\t\t\t\t\tbegin: \"\\\\b(\" + Y.join(\"|\") + \")\\\\b\",\r\n\t\t\t\t\tclassName: \"selector-tag\"\r\n\t\t\t\t}, n.CSS_NUMBER_MODE, o(\"selector-tag\", a, 0), o(\"selector-id\", \"#\" + a), o(\"selector-class\", \"\\\\.\" +\r\n\t\t\t\t\ta, 0), o(\"selector-tag\", \"&\", 0), n.ATTRIBUTE_SELECTOR_MODE, {\r\n\t\t\t\t\tclassName: \"selector-pseudo\",\r\n\t\t\t\t\tbegin: \":(\" + ne.join(\"|\") + \")\"\r\n\t\t\t\t}, {\r\n\t\t\t\t\tclassName: \"selector-pseudo\",\r\n\t\t\t\t\tbegin: \":(:)?(\" + te.join(\"|\") + \")\"\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: /\\(/,\r\n\t\t\t\t\tend: /\\)/,\r\n\t\t\t\t\trelevance: 0,\r\n\t\t\t\t\tcontains: d\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: \"!important\"\r\n\t\t\t\t}, n.FUNCTION_DISPATCH]\r\n\t\t\t},\r\n\t\t\t_ = {\r\n\t\t\t\tbegin: `[\\\\w-]+:(:)?(${t.join(\"|\")})`,\r\n\t\t\t\treturnBegin: !0,\r\n\t\t\t\tcontains: [p]\r\n\t\t\t};\r\n\t\treturn i.push(e.C_LINE_COMMENT_MODE, e.C_BLOCK_COMMENT_MODE, b, m, _, u, p, g, n.FUNCTION_DISPATCH), {\r\n\t\t\tname: \"Less\",\r\n\t\t\tcase_insensitive: !0,\r\n\t\t\tillegal: \"[=>'/<($\\\"]\",\r\n\t\t\tcontains: i\r\n\t\t}\r\n\t},\r\n\tgrmr_lua: e => {\r\n\t\tconst n = \"\\\\[=*\\\\[\",\r\n\t\t\tt = \"\\\\]=*\\\\]\",\r\n\t\t\ta = {\r\n\t\t\t\tbegin: n,\r\n\t\t\t\tend: t,\r\n\t\t\t\tcontains: [\"self\"]\r\n\t\t\t},\r\n\t\t\ti = [e.COMMENT(\"--(?!\\\\[=*\\\\[)\", \"$\"), e.COMMENT(\"--\\\\[=*\\\\[\", t, {\r\n\t\t\t\tcontains: [a],\r\n\t\t\t\trelevance: 10\r\n\t\t\t})];\r\n\t\treturn {\r\n\t\t\tname: \"Lua\",\r\n\t\t\tkeywords: {\r\n\t\t\t\t$pattern: e.UNDERSCORE_IDENT_RE,\r\n\t\t\t\tliteral: \"true false nil\",\r\n\t\t\t\tkeyword: \"and break do else elseif end for goto if in local not or repeat return then until while\",\r\n\t\t\t\tbuilt_in: \"_G _ENV _VERSION __index __newindex __mode __call __metatable __tostring __len __gc __add __sub __mul __div __mod __pow __concat __unm __eq __lt __le assert collectgarbage dofile error getfenv getmetatable ipairs load loadfile loadstring module next pairs pcall print rawequal rawget rawset require select setfenv setmetatable tonumber tostring type unpack xpcall arg self coroutine resume yield status wrap create running debug getupvalue debug sethook getmetatable gethook setmetatable setlocal traceback setfenv getinfo setupvalue getlocal getregistry getfenv io lines write close flush open output type read stderr stdin input stdout popen tmpfile math log max acos huge ldexp pi cos tanh pow deg tan cosh sinh random randomseed frexp ceil floor rad abs sqrt modf asin min mod fmod log10 atan2 exp sin atan os exit setlocale date getenv difftime remove time clock tmpname rename execute package preload loadlib loaded loaders cpath config path seeall string sub upper len gfind rep find match char dump gmatch reverse byte format gsub lower table setn insert getn foreachi maxn foreach concat sort remove\"\r\n\t\t\t},\r\n\t\t\tcontains: i.concat([{\r\n\t\t\t\tclassName: \"function\",\r\n\t\t\t\tbeginKeywords: \"function\",\r\n\t\t\t\tend: \"\\\\)\",\r\n\t\t\t\tcontains: [e.inherit(e.TITLE_MODE, {\r\n\t\t\t\t\tbegin: \"([_a-zA-Z]\\\\w*\\\\.)*([_a-zA-Z]\\\\w*:)?[_a-zA-Z]\\\\w*\"\r\n\t\t\t\t}), {\r\n\t\t\t\t\tclassName: \"params\",\r\n\t\t\t\t\tbegin: \"\\\\(\",\r\n\t\t\t\t\tendsWithParent: !0,\r\n\t\t\t\t\tcontains: i\r\n\t\t\t\t}].concat(i)\r\n\t\t\t}, e.C_NUMBER_MODE, e.APOS_STRING_MODE, e.QUOTE_STRING_MODE, {\r\n\t\t\t\tclassName: \"string\",\r\n\t\t\t\tbegin: n,\r\n\t\t\t\tend: t,\r\n\t\t\t\tcontains: [a],\r\n\t\t\t\trelevance: 5\r\n\t\t\t}])\r\n\t\t}\r\n\t},\r\n\tgrmr_makefile: e => {\r\n\t\tconst n = {\r\n\t\t\t\tclassName: \"variable\",\r\n\t\t\t\tvariants: [{\r\n\t\t\t\t\tbegin: \"\\\\$\\\\(\" + e.UNDERSCORE_IDENT_RE + \"\\\\)\",\r\n\t\t\t\t\tcontains: [e.BACKSLASH_ESCAPE]\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: /\\$[@%<?\\^\\+\\*]/\r\n\t\t\t\t}]\r\n\t\t\t},\r\n\t\t\tt = {\r\n\t\t\t\tclassName: \"string\",\r\n\t\t\t\tbegin: /\"/,\r\n\t\t\t\tend: /\"/,\r\n\t\t\t\tcontains: [e.BACKSLASH_ESCAPE, n]\r\n\t\t\t},\r\n\t\t\ta = {\r\n\t\t\t\tclassName: \"variable\",\r\n\t\t\t\tbegin: /\\$\\([\\w-]+\\s/,\r\n\t\t\t\tend: /\\)/,\r\n\t\t\t\tkeywords: {\r\n\t\t\t\t\tbuilt_in: \"subst patsubst strip findstring filter filter-out sort word wordlist firstword lastword dir notdir suffix basename addsuffix addprefix join wildcard realpath abspath error warning shell origin flavor foreach if or and call eval file value\"\r\n\t\t\t\t},\r\n\t\t\t\tcontains: [n]\r\n\t\t\t},\r\n\t\t\ti = {\r\n\t\t\t\tbegin: \"^\" + e.UNDERSCORE_IDENT_RE + \"\\\\s*(?=[:+?]?=)\"\r\n\t\t\t},\r\n\t\t\tr = {\r\n\t\t\t\tclassName: \"section\",\r\n\t\t\t\tbegin: /^[^\\s]+:/,\r\n\t\t\t\tend: /$/,\r\n\t\t\t\tcontains: [n]\r\n\t\t\t};\r\n\t\treturn {\r\n\t\t\tname: \"Makefile\",\r\n\t\t\taliases: [\"mk\", \"mak\", \"make\"],\r\n\t\t\tkeywords: {\r\n\t\t\t\t$pattern: /[\\w-]+/,\r\n\t\t\t\tkeyword: \"define endef undefine ifdef ifndef ifeq ifneq else endif include -include sinclude override export unexport private vpath\"\r\n\t\t\t},\r\n\t\t\tcontains: [e.HASH_COMMENT_MODE, n, t, a, i, {\r\n\t\t\t\tclassName: \"meta\",\r\n\t\t\t\tbegin: /^\\.PHONY:/,\r\n\t\t\t\tend: /$/,\r\n\t\t\t\tkeywords: {\r\n\t\t\t\t\t$pattern: /[\\.\\w]+/,\r\n\t\t\t\t\tkeyword: \".PHONY\"\r\n\t\t\t\t}\r\n\t\t\t}, r]\r\n\t\t}\r\n\t},\r\n\tgrmr_xml: e => {\r\n\t\tconst n = e.regex,\r\n\t\t\tt = n.concat(\r\n\t\t\t\t/(?:[A-Z_a-z\\xAA\\xB5\\xBA\\xC0-\\xD6\\xD8-\\xF6\\xF8-\\u02C1\\u02C6-\\u02D1\\u02E0-\\u02E4\\u02EC\\u02EE\\u0370-\\u0374\\u0376\\u0377\\u037A-\\u037D\\u037F\\u0386\\u0388-\\u038A\\u038C\\u038E-\\u03A1\\u03A3-\\u03F5\\u03F7-\\u0481\\u048A-\\u052F\\u0531-\\u0556\\u0559\\u0560-\\u0588\\u05D0-\\u05EA\\u05EF-\\u05F2\\u0620-\\u064A\\u066E\\u066F\\u0671-\\u06D3\\u06D5\\u06E5\\u06E6\\u06EE\\u06EF\\u06FA-\\u06FC\\u06FF\\u0710\\u0712-\\u072F\\u074D-\\u07A5\\u07B1\\u07CA-\\u07EA\\u07F4\\u07F5\\u07FA\\u0800-\\u0815\\u081A\\u0824\\u0828\\u0840-\\u0858\\u0860-\\u086A\\u0870-\\u0887\\u0889-\\u088E\\u08A0-\\u08C9\\u0904-\\u0939\\u093D\\u0950\\u0958-\\u0961\\u0971-\\u0980\\u0985-\\u098C\\u098F\\u0990\\u0993-\\u09A8\\u09AA-\\u09B0\\u09B2\\u09B6-\\u09B9\\u09BD\\u09CE\\u09DC\\u09DD\\u09DF-\\u09E1\\u09F0\\u09F1\\u09FC\\u0A05-\\u0A0A\\u0A0F\\u0A10\\u0A13-\\u0A28\\u0A2A-\\u0A30\\u0A32\\u0A33\\u0A35\\u0A36\\u0A38\\u0A39\\u0A59-\\u0A5C\\u0A5E\\u0A72-\\u0A74\\u0A85-\\u0A8D\\u0A8F-\\u0A91\\u0A93-\\u0AA8\\u0AAA-\\u0AB0\\u0AB2\\u0AB3\\u0AB5-\\u0AB9\\u0ABD\\u0AD0\\u0AE0\\u0AE1\\u0AF9\\u0B05-\\u0B0C\\u0B0F\\u0B10\\u0B13-\\u0B28\\u0B2A-\\u0B30\\u0B32\\u0B33\\u0B35-\\u0B39\\u0B3D\\u0B5C\\u0B5D\\u0B5F-\\u0B61\\u0B71\\u0B83\\u0B85-\\u0B8A\\u0B8E-\\u0B90\\u0B92-\\u0B95\\u0B99\\u0B9A\\u0B9C\\u0B9E\\u0B9F\\u0BA3\\u0BA4\\u0BA8-\\u0BAA\\u0BAE-\\u0BB9\\u0BD0\\u0C05-\\u0C0C\\u0C0E-\\u0C10\\u0C12-\\u0C28\\u0C2A-\\u0C39\\u0C3D\\u0C58-\\u0C5A\\u0C5D\\u0C60\\u0C61\\u0C80\\u0C85-\\u0C8C\\u0C8E-\\u0C90\\u0C92-\\u0CA8\\u0CAA-\\u0CB3\\u0CB5-\\u0CB9\\u0CBD\\u0CDD\\u0CDE\\u0CE0\\u0CE1\\u0CF1\\u0CF2\\u0D04-\\u0D0C\\u0D0E-\\u0D10\\u0D12-\\u0D3A\\u0D3D\\u0D4E\\u0D54-\\u0D56\\u0D5F-\\u0D61\\u0D7A-\\u0D7F\\u0D85-\\u0D96\\u0D9A-\\u0DB1\\u0DB3-\\u0DBB\\u0DBD\\u0DC0-\\u0DC6\\u0E01-\\u0E30\\u0E32\\u0E33\\u0E40-\\u0E46\\u0E81\\u0E82\\u0E84\\u0E86-\\u0E8A\\u0E8C-\\u0EA3\\u0EA5\\u0EA7-\\u0EB0\\u0EB2\\u0EB3\\u0EBD\\u0EC0-\\u0EC4\\u0EC6\\u0EDC-\\u0EDF\\u0F00\\u0F40-\\u0F47\\u0F49-\\u0F6C\\u0F88-\\u0F8C\\u1000-\\u102A\\u103F\\u1050-\\u1055\\u105A-\\u105D\\u1061\\u1065\\u1066\\u106E-\\u1070\\u1075-\\u1081\\u108E\\u10A0-\\u10C5\\u10C7\\u10CD\\u10D0-\\u10FA\\u10FC-\\u1248\\u124A-\\u124D\\u1250-\\u1256\\u1258\\u125A-\\u125D\\u1260-\\u1288\\u128A-\\u128D\\u1290-\\u12B0\\u12B2-\\u12B5\\u12B8-\\u12BE\\u12C0\\u12C2-\\u12C5\\u12C8-\\u12D6\\u12D8-\\u1310\\u1312-\\u1315\\u1318-\\u135A\\u1380-\\u138F\\u13A0-\\u13F5\\u13F8-\\u13FD\\u1401-\\u166C\\u166F-\\u167F\\u1681-\\u169A\\u16A0-\\u16EA\\u16F1-\\u16F8\\u1700-\\u1711\\u171F-\\u1731\\u1740-\\u1751\\u1760-\\u176C\\u176E-\\u1770\\u1780-\\u17B3\\u17D7\\u17DC\\u1820-\\u1878\\u1880-\\u1884\\u1887-\\u18A8\\u18AA\\u18B0-\\u18F5\\u1900-\\u191E\\u1950-\\u196D\\u1970-\\u1974\\u1980-\\u19AB\\u19B0-\\u19C9\\u1A00-\\u1A16\\u1A20-\\u1A54\\u1AA7\\u1B05-\\u1B33\\u1B45-\\u1B4C\\u1B83-\\u1BA0\\u1BAE\\u1BAF\\u1BBA-\\u1BE5\\u1C00-\\u1C23\\u1C4D-\\u1C4F\\u1C5A-\\u1C7D\\u1C80-\\u1C88\\u1C90-\\u1CBA\\u1CBD-\\u1CBF\\u1CE9-\\u1CEC\\u1CEE-\\u1CF3\\u1CF5\\u1CF6\\u1CFA\\u1D00-\\u1DBF\\u1E00-\\u1F15\\u1F18-\\u1F1D\\u1F20-\\u1F45\\u1F48-\\u1F4D\\u1F50-\\u1F57\\u1F59\\u1F5B\\u1F5D\\u1F5F-\\u1F7D\\u1F80-\\u1FB4\\u1FB6-\\u1FBC\\u1FBE\\u1FC2-\\u1FC4\\u1FC6-\\u1FCC\\u1FD0-\\u1FD3\\u1FD6-\\u1FDB\\u1FE0-\\u1FEC\\u1FF2-\\u1FF4\\u1FF6-\\u1FFC\\u2071\\u207F\\u2090-\\u209C\\u2102\\u2107\\u210A-\\u2113\\u2115\\u2119-\\u211D\\u2124\\u2126\\u2128\\u212A-\\u212D\\u212F-\\u2139\\u213C-\\u213F\\u2145-\\u2149\\u214E\\u2183\\u2184\\u2C00-\\u2CE4\\u2CEB-\\u2CEE\\u2CF2\\u2CF3\\u2D00-\\u2D25\\u2D27\\u2D2D\\u2D30-\\u2D67\\u2D6F\\u2D80-\\u2D96\\u2DA0-\\u2DA6\\u2DA8-\\u2DAE\\u2DB0-\\u2DB6\\u2DB8-\\u2DBE\\u2DC0-\\u2DC6\\u2DC8-\\u2DCE\\u2DD0-\\u2DD6\\u2DD8-\\u2DDE\\u2E2F\\u3005\\u3006\\u3031-\\u3035\\u303B\\u303C\\u3041-\\u3096\\u309D-\\u309F\\u30A1-\\u30FA\\u30FC-\\u30FF\\u3105-\\u312F\\u3131-\\u318E\\u31A0-\\u31BF\\u31F0-\\u31FF\\u3400-\\u4DBF\\u4E00-\\uA48C\\uA4D0-\\uA4FD\\uA500-\\uA60C\\uA610-\\uA61F\\uA62A\\uA62B\\uA640-\\uA66E\\uA67F-\\uA69D\\uA6A0-\\uA6E5\\uA717-\\uA71F\\uA722-\\uA788\\uA78B-\\uA7CA\\uA7D0\\uA7D1\\uA7D3\\uA7D5-\\uA7D9\\uA7F2-\\uA801\\uA803-\\uA805\\uA807-\\uA80A\\uA80C-\\uA822\\uA840-\\uA873\\uA882-\\uA8B3\\uA8F2-\\uA8F7\\uA8FB\\uA8FD\\uA8FE\\uA90A-\\uA925\\uA930-\\uA946\\uA960-\\uA97C\\uA984-\\uA9B2\\uA9CF\\uA9E0-\\uA9E4\\uA9E6-\\uA9EF\\uA9FA-\\uA9FE\\uAA00-\\uAA28\\uAA40-\\uAA42\\uAA44-\\uAA4B\\uAA60-\\uAA76\\uAA7A\\uAA7E-\\uAAAF\\uAAB1\\uAAB5\\uAAB6\\uAAB9-\\uAABD\\uAAC0\\uAAC2\\uAADB-\\uAADD\\uAAE0-\\uAAEA\\uAAF2-\\uAAF4\\uAB01-\\uAB06\\uAB09-\\uAB0E\\uAB11-\\uAB16\\uAB20-\\uAB26\\uAB28-\\uAB2E\\uAB30-\\uAB5A\\uAB5C-\\uAB69\\uAB70-\\uABE2\\uAC00-\\uD7A3\\uD7B0-\\uD7C6\\uD7CB-\\uD7FB\\uF900-\\uFA6D\\uFA70-\\uFAD9\\uFB00-\\uFB06\\uFB13-\\uFB17\\uFB1D\\uFB1F-\\uFB28\\uFB2A-\\uFB36\\uFB38-\\uFB3C\\uFB3E\\uFB40\\uFB41\\uFB43\\uFB44\\uFB46-\\uFBB1\\uFBD3-\\uFD3D\\uFD50-\\uFD8F\\uFD92-\\uFDC7\\uFDF0-\\uFDFB\\uFE70-\\uFE74\\uFE76-\\uFEFC\\uFF21-\\uFF3A\\uFF41-\\uFF5A\\uFF66-\\uFFBE\\uFFC2-\\uFFC7\\uFFCA-\\uFFCF\\uFFD2-\\uFFD7\\uFFDA-\\uFFDC]|\\uD800[\\uDC00-\\uDC0B\\uDC0D-\\uDC26\\uDC28-\\uDC3A\\uDC3C\\uDC3D\\uDC3F-\\uDC4D\\uDC50-\\uDC5D\\uDC80-\\uDCFA\\uDE80-\\uDE9C\\uDEA0-\\uDED0\\uDF00-\\uDF1F\\uDF2D-\\uDF40\\uDF42-\\uDF49\\uDF50-\\uDF75\\uDF80-\\uDF9D\\uDFA0-\\uDFC3\\uDFC8-\\uDFCF]|\\uD801[\\uDC00-\\uDC9D\\uDCB0-\\uDCD3\\uDCD8-\\uDCFB\\uDD00-\\uDD27\\uDD30-\\uDD63\\uDD70-\\uDD7A\\uDD7C-\\uDD8A\\uDD8C-\\uDD92\\uDD94\\uDD95\\uDD97-\\uDDA1\\uDDA3-\\uDDB1\\uDDB3-\\uDDB9\\uDDBB\\uDDBC\\uDE00-\\uDF36\\uDF40-\\uDF55\\uDF60-\\uDF67\\uDF80-\\uDF85\\uDF87-\\uDFB0\\uDFB2-\\uDFBA]|\\uD802[\\uDC00-\\uDC05\\uDC08\\uDC0A-\\uDC35\\uDC37\\uDC38\\uDC3C\\uDC3F-\\uDC55\\uDC60-\\uDC76\\uDC80-\\uDC9E\\uDCE0-\\uDCF2\\uDCF4\\uDCF5\\uDD00-\\uDD15\\uDD20-\\uDD39\\uDD80-\\uDDB7\\uDDBE\\uDDBF\\uDE00\\uDE10-\\uDE13\\uDE15-\\uDE17\\uDE19-\\uDE35\\uDE60-\\uDE7C\\uDE80-\\uDE9C\\uDEC0-\\uDEC7\\uDEC9-\\uDEE4\\uDF00-\\uDF35\\uDF40-\\uDF55\\uDF60-\\uDF72\\uDF80-\\uDF91]|\\uD803[\\uDC00-\\uDC48\\uDC80-\\uDCB2\\uDCC0-\\uDCF2\\uDD00-\\uDD23\\uDE80-\\uDEA9\\uDEB0\\uDEB1\\uDF00-\\uDF1C\\uDF27\\uDF30-\\uDF45\\uDF70-\\uDF81\\uDFB0-\\uDFC4\\uDFE0-\\uDFF6]|\\uD804[\\uDC03-\\uDC37\\uDC71\\uDC72\\uDC75\\uDC83-\\uDCAF\\uDCD0-\\uDCE8\\uDD03-\\uDD26\\uDD44\\uDD47\\uDD50-\\uDD72\\uDD76\\uDD83-\\uDDB2\\uDDC1-\\uDDC4\\uDDDA\\uDDDC\\uDE00-\\uDE11\\uDE13-\\uDE2B\\uDE3F\\uDE40\\uDE80-\\uDE86\\uDE88\\uDE8A-\\uDE8D\\uDE8F-\\uDE9D\\uDE9F-\\uDEA8\\uDEB0-\\uDEDE\\uDF05-\\uDF0C\\uDF0F\\uDF10\\uDF13-\\uDF28\\uDF2A-\\uDF30\\uDF32\\uDF33\\uDF35-\\uDF39\\uDF3D\\uDF50\\uDF5D-\\uDF61]|\\uD805[\\uDC00-\\uDC34\\uDC47-\\uDC4A\\uDC5F-\\uDC61\\uDC80-\\uDCAF\\uDCC4\\uDCC5\\uDCC7\\uDD80-\\uDDAE\\uDDD8-\\uDDDB\\uDE00-\\uDE2F\\uDE44\\uDE80-\\uDEAA\\uDEB8\\uDF00-\\uDF1A\\uDF40-\\uDF46]|\\uD806[\\uDC00-\\uDC2B\\uDCA0-\\uDCDF\\uDCFF-\\uDD06\\uDD09\\uDD0C-\\uDD13\\uDD15\\uDD16\\uDD18-\\uDD2F\\uDD3F\\uDD41\\uDDA0-\\uDDA7\\uDDAA-\\uDDD0\\uDDE1\\uDDE3\\uDE00\\uDE0B-\\uDE32\\uDE3A\\uDE50\\uDE5C-\\uDE89\\uDE9D\\uDEB0-\\uDEF8]|\\uD807[\\uDC00-\\uDC08\\uDC0A-\\uDC2E\\uDC40\\uDC72-\\uDC8F\\uDD00-\\uDD06\\uDD08\\uDD09\\uDD0B-\\uDD30\\uDD46\\uDD60-\\uDD65\\uDD67\\uDD68\\uDD6A-\\uDD89\\uDD98\\uDEE0-\\uDEF2\\uDF02\\uDF04-\\uDF10\\uDF12-\\uDF33\\uDFB0]|\\uD808[\\uDC00-\\uDF99]|\\uD809[\\uDC80-\\uDD43]|\\uD80B[\\uDF90-\\uDFF0]|[\\uD80C\\uD81C-\\uD820\\uD822\\uD840-\\uD868\\uD86A-\\uD86C\\uD86F-\\uD872\\uD874-\\uD879\\uD880-\\uD883\\uD885-\\uD887][\\uDC00-\\uDFFF]|\\uD80D[\\uDC00-\\uDC2F\\uDC41-\\uDC46]|\\uD811[\\uDC00-\\uDE46]|\\uD81A[\\uDC00-\\uDE38\\uDE40-\\uDE5E\\uDE70-\\uDEBE\\uDED0-\\uDEED\\uDF00-\\uDF2F\\uDF40-\\uDF43\\uDF63-\\uDF77\\uDF7D-\\uDF8F]|\\uD81B[\\uDE40-\\uDE7F\\uDF00-\\uDF4A\\uDF50\\uDF93-\\uDF9F\\uDFE0\\uDFE1\\uDFE3]|\\uD821[\\uDC00-\\uDFF7]|\\uD823[\\uDC00-\\uDCD5\\uDD00-\\uDD08]|\\uD82B[\\uDFF0-\\uDFF3\\uDFF5-\\uDFFB\\uDFFD\\uDFFE]|\\uD82C[\\uDC00-\\uDD22\\uDD32\\uDD50-\\uDD52\\uDD55\\uDD64-\\uDD67\\uDD70-\\uDEFB]|\\uD82F[\\uDC00-\\uDC6A\\uDC70-\\uDC7C\\uDC80-\\uDC88\\uDC90-\\uDC99]|\\uD835[\\uDC00-\\uDC54\\uDC56-\\uDC9C\\uDC9E\\uDC9F\\uDCA2\\uDCA5\\uDCA6\\uDCA9-\\uDCAC\\uDCAE-\\uDCB9\\uDCBB\\uDCBD-\\uDCC3\\uDCC5-\\uDD05\\uDD07-\\uDD0A\\uDD0D-\\uDD14\\uDD16-\\uDD1C\\uDD1E-\\uDD39\\uDD3B-\\uDD3E\\uDD40-\\uDD44\\uDD46\\uDD4A-\\uDD50\\uDD52-\\uDEA5\\uDEA8-\\uDEC0\\uDEC2-\\uDEDA\\uDEDC-\\uDEFA\\uDEFC-\\uDF14\\uDF16-\\uDF34\\uDF36-\\uDF4E\\uDF50-\\uDF6E\\uDF70-\\uDF88\\uDF8A-\\uDFA8\\uDFAA-\\uDFC2\\uDFC4-\\uDFCB]|\\uD837[\\uDF00-\\uDF1E\\uDF25-\\uDF2A]|\\uD838[\\uDC30-\\uDC6D\\uDD00-\\uDD2C\\uDD37-\\uDD3D\\uDD4E\\uDE90-\\uDEAD\\uDEC0-\\uDEEB]|\\uD839[\\uDCD0-\\uDCEB\\uDFE0-\\uDFE6\\uDFE8-\\uDFEB\\uDFED\\uDFEE\\uDFF0-\\uDFFE]|\\uD83A[\\uDC00-\\uDCC4\\uDD00-\\uDD43\\uDD4B]|\\uD83B[\\uDE00-\\uDE03\\uDE05-\\uDE1F\\uDE21\\uDE22\\uDE24\\uDE27\\uDE29-\\uDE32\\uDE34-\\uDE37\\uDE39\\uDE3B\\uDE42\\uDE47\\uDE49\\uDE4B\\uDE4D-\\uDE4F\\uDE51\\uDE52\\uDE54\\uDE57\\uDE59\\uDE5B\\uDE5D\\uDE5F\\uDE61\\uDE62\\uDE64\\uDE67-\\uDE6A\\uDE6C-\\uDE72\\uDE74-\\uDE77\\uDE79-\\uDE7C\\uDE7E\\uDE80-\\uDE89\\uDE8B-\\uDE9B\\uDEA1-\\uDEA3\\uDEA5-\\uDEA9\\uDEAB-\\uDEBB]|\\uD869[\\uDC00-\\uDEDF\\uDF00-\\uDFFF]|\\uD86D[\\uDC00-\\uDF39\\uDF40-\\uDFFF]|\\uD86E[\\uDC00-\\uDC1D\\uDC20-\\uDFFF]|\\uD873[\\uDC00-\\uDEA1\\uDEB0-\\uDFFF]|\\uD87A[\\uDC00-\\uDFE0]|\\uD87E[\\uDC00-\\uDE1D]|\\uD884[\\uDC00-\\uDF4A\\uDF50-\\uDFFF]|\\uD888[\\uDC00-\\uDFAF])/,\r\n\t\t\t\tn.optional(\r\n\t\t\t\t\t/(?:[\\x2D\\.0-9A-Z_a-z\\xAA\\xB5\\xBA\\xC0-\\xD6\\xD8-\\xF6\\xF8-\\u02C1\\u02C6-\\u02D1\\u02E0-\\u02E4\\u02EC\\u02EE\\u0370-\\u0374\\u0376\\u0377\\u037A-\\u037D\\u037F\\u0386\\u0388-\\u038A\\u038C\\u038E-\\u03A1\\u03A3-\\u03F5\\u03F7-\\u0481\\u048A-\\u052F\\u0531-\\u0556\\u0559\\u0560-\\u0588\\u05D0-\\u05EA\\u05EF-\\u05F2\\u0620-\\u064A\\u066E\\u066F\\u0671-\\u06D3\\u06D5\\u06E5\\u06E6\\u06EE\\u06EF\\u06FA-\\u06FC\\u06FF\\u0710\\u0712-\\u072F\\u074D-\\u07A5\\u07B1\\u07CA-\\u07EA\\u07F4\\u07F5\\u07FA\\u0800-\\u0815\\u081A\\u0824\\u0828\\u0840-\\u0858\\u0860-\\u086A\\u0870-\\u0887\\u0889-\\u088E\\u08A0-\\u08C9\\u0904-\\u0939\\u093D\\u0950\\u0958-\\u0961\\u0971-\\u0980\\u0985-\\u098C\\u098F\\u0990\\u0993-\\u09A8\\u09AA-\\u09B0\\u09B2\\u09B6-\\u09B9\\u09BD\\u09CE\\u09DC\\u09DD\\u09DF-\\u09E1\\u09F0\\u09F1\\u09FC\\u0A05-\\u0A0A\\u0A0F\\u0A10\\u0A13-\\u0A28\\u0A2A-\\u0A30\\u0A32\\u0A33\\u0A35\\u0A36\\u0A38\\u0A39\\u0A59-\\u0A5C\\u0A5E\\u0A72-\\u0A74\\u0A85-\\u0A8D\\u0A8F-\\u0A91\\u0A93-\\u0AA8\\u0AAA-\\u0AB0\\u0AB2\\u0AB3\\u0AB5-\\u0AB9\\u0ABD\\u0AD0\\u0AE0\\u0AE1\\u0AF9\\u0B05-\\u0B0C\\u0B0F\\u0B10\\u0B13-\\u0B28\\u0B2A-\\u0B30\\u0B32\\u0B33\\u0B35-\\u0B39\\u0B3D\\u0B5C\\u0B5D\\u0B5F-\\u0B61\\u0B71\\u0B83\\u0B85-\\u0B8A\\u0B8E-\\u0B90\\u0B92-\\u0B95\\u0B99\\u0B9A\\u0B9C\\u0B9E\\u0B9F\\u0BA3\\u0BA4\\u0BA8-\\u0BAA\\u0BAE-\\u0BB9\\u0BD0\\u0C05-\\u0C0C\\u0C0E-\\u0C10\\u0C12-\\u0C28\\u0C2A-\\u0C39\\u0C3D\\u0C58-\\u0C5A\\u0C5D\\u0C60\\u0C61\\u0C80\\u0C85-\\u0C8C\\u0C8E-\\u0C90\\u0C92-\\u0CA8\\u0CAA-\\u0CB3\\u0CB5-\\u0CB9\\u0CBD\\u0CDD\\u0CDE\\u0CE0\\u0CE1\\u0CF1\\u0CF2\\u0D04-\\u0D0C\\u0D0E-\\u0D10\\u0D12-\\u0D3A\\u0D3D\\u0D4E\\u0D54-\\u0D56\\u0D5F-\\u0D61\\u0D7A-\\u0D7F\\u0D85-\\u0D96\\u0D9A-\\u0DB1\\u0DB3-\\u0DBB\\u0DBD\\u0DC0-\\u0DC6\\u0E01-\\u0E30\\u0E32\\u0E33\\u0E40-\\u0E46\\u0E81\\u0E82\\u0E84\\u0E86-\\u0E8A\\u0E8C-\\u0EA3\\u0EA5\\u0EA7-\\u0EB0\\u0EB2\\u0EB3\\u0EBD\\u0EC0-\\u0EC4\\u0EC6\\u0EDC-\\u0EDF\\u0F00\\u0F40-\\u0F47\\u0F49-\\u0F6C\\u0F88-\\u0F8C\\u1000-\\u102A\\u103F\\u1050-\\u1055\\u105A-\\u105D\\u1061\\u1065\\u1066\\u106E-\\u1070\\u1075-\\u1081\\u108E\\u10A0-\\u10C5\\u10C7\\u10CD\\u10D0-\\u10FA\\u10FC-\\u1248\\u124A-\\u124D\\u1250-\\u1256\\u1258\\u125A-\\u125D\\u1260-\\u1288\\u128A-\\u128D\\u1290-\\u12B0\\u12B2-\\u12B5\\u12B8-\\u12BE\\u12C0\\u12C2-\\u12C5\\u12C8-\\u12D6\\u12D8-\\u1310\\u1312-\\u1315\\u1318-\\u135A\\u1380-\\u138F\\u13A0-\\u13F5\\u13F8-\\u13FD\\u1401-\\u166C\\u166F-\\u167F\\u1681-\\u169A\\u16A0-\\u16EA\\u16F1-\\u16F8\\u1700-\\u1711\\u171F-\\u1731\\u1740-\\u1751\\u1760-\\u176C\\u176E-\\u1770\\u1780-\\u17B3\\u17D7\\u17DC\\u1820-\\u1878\\u1880-\\u1884\\u1887-\\u18A8\\u18AA\\u18B0-\\u18F5\\u1900-\\u191E\\u1950-\\u196D\\u1970-\\u1974\\u1980-\\u19AB\\u19B0-\\u19C9\\u1A00-\\u1A16\\u1A20-\\u1A54\\u1AA7\\u1B05-\\u1B33\\u1B45-\\u1B4C\\u1B83-\\u1BA0\\u1BAE\\u1BAF\\u1BBA-\\u1BE5\\u1C00-\\u1C23\\u1C4D-\\u1C4F\\u1C5A-\\u1C7D\\u1C80-\\u1C88\\u1C90-\\u1CBA\\u1CBD-\\u1CBF\\u1CE9-\\u1CEC\\u1CEE-\\u1CF3\\u1CF5\\u1CF6\\u1CFA\\u1D00-\\u1DBF\\u1E00-\\u1F15\\u1F18-\\u1F1D\\u1F20-\\u1F45\\u1F48-\\u1F4D\\u1F50-\\u1F57\\u1F59\\u1F5B\\u1F5D\\u1F5F-\\u1F7D\\u1F80-\\u1FB4\\u1FB6-\\u1FBC\\u1FBE\\u1FC2-\\u1FC4\\u1FC6-\\u1FCC\\u1FD0-\\u1FD3\\u1FD6-\\u1FDB\\u1FE0-\\u1FEC\\u1FF2-\\u1FF4\\u1FF6-\\u1FFC\\u2071\\u207F\\u2090-\\u209C\\u2102\\u2107\\u210A-\\u2113\\u2115\\u2119-\\u211D\\u2124\\u2126\\u2128\\u212A-\\u212D\\u212F-\\u2139\\u213C-\\u213F\\u2145-\\u2149\\u214E\\u2183\\u2184\\u2C00-\\u2CE4\\u2CEB-\\u2CEE\\u2CF2\\u2CF3\\u2D00-\\u2D25\\u2D27\\u2D2D\\u2D30-\\u2D67\\u2D6F\\u2D80-\\u2D96\\u2DA0-\\u2DA6\\u2DA8-\\u2DAE\\u2DB0-\\u2DB6\\u2DB8-\\u2DBE\\u2DC0-\\u2DC6\\u2DC8-\\u2DCE\\u2DD0-\\u2DD6\\u2DD8-\\u2DDE\\u2E2F\\u3005\\u3006\\u3031-\\u3035\\u303B\\u303C\\u3041-\\u3096\\u309D-\\u309F\\u30A1-\\u30FA\\u30FC-\\u30FF\\u3105-\\u312F\\u3131-\\u318E\\u31A0-\\u31BF\\u31F0-\\u31FF\\u3400-\\u4DBF\\u4E00-\\uA48C\\uA4D0-\\uA4FD\\uA500-\\uA60C\\uA610-\\uA61F\\uA62A\\uA62B\\uA640-\\uA66E\\uA67F-\\uA69D\\uA6A0-\\uA6E5\\uA717-\\uA71F\\uA722-\\uA788\\uA78B-\\uA7CA\\uA7D0\\uA7D1\\uA7D3\\uA7D5-\\uA7D9\\uA7F2-\\uA801\\uA803-\\uA805\\uA807-\\uA80A\\uA80C-\\uA822\\uA840-\\uA873\\uA882-\\uA8B3\\uA8F2-\\uA8F7\\uA8FB\\uA8FD\\uA8FE\\uA90A-\\uA925\\uA930-\\uA946\\uA960-\\uA97C\\uA984-\\uA9B2\\uA9CF\\uA9E0-\\uA9E4\\uA9E6-\\uA9EF\\uA9FA-\\uA9FE\\uAA00-\\uAA28\\uAA40-\\uAA42\\uAA44-\\uAA4B\\uAA60-\\uAA76\\uAA7A\\uAA7E-\\uAAAF\\uAAB1\\uAAB5\\uAAB6\\uAAB9-\\uAABD\\uAAC0\\uAAC2\\uAADB-\\uAADD\\uAAE0-\\uAAEA\\uAAF2-\\uAAF4\\uAB01-\\uAB06\\uAB09-\\uAB0E\\uAB11-\\uAB16\\uAB20-\\uAB26\\uAB28-\\uAB2E\\uAB30-\\uAB5A\\uAB5C-\\uAB69\\uAB70-\\uABE2\\uAC00-\\uD7A3\\uD7B0-\\uD7C6\\uD7CB-\\uD7FB\\uF900-\\uFA6D\\uFA70-\\uFAD9\\uFB00-\\uFB06\\uFB13-\\uFB17\\uFB1D\\uFB1F-\\uFB28\\uFB2A-\\uFB36\\uFB38-\\uFB3C\\uFB3E\\uFB40\\uFB41\\uFB43\\uFB44\\uFB46-\\uFBB1\\uFBD3-\\uFD3D\\uFD50-\\uFD8F\\uFD92-\\uFDC7\\uFDF0-\\uFDFB\\uFE70-\\uFE74\\uFE76-\\uFEFC\\uFF21-\\uFF3A\\uFF41-\\uFF5A\\uFF66-\\uFFBE\\uFFC2-\\uFFC7\\uFFCA-\\uFFCF\\uFFD2-\\uFFD7\\uFFDA-\\uFFDC]|\\uD800[\\uDC00-\\uDC0B\\uDC0D-\\uDC26\\uDC28-\\uDC3A\\uDC3C\\uDC3D\\uDC3F-\\uDC4D\\uDC50-\\uDC5D\\uDC80-\\uDCFA\\uDE80-\\uDE9C\\uDEA0-\\uDED0\\uDF00-\\uDF1F\\uDF2D-\\uDF40\\uDF42-\\uDF49\\uDF50-\\uDF75\\uDF80-\\uDF9D\\uDFA0-\\uDFC3\\uDFC8-\\uDFCF]|\\uD801[\\uDC00-\\uDC9D\\uDCB0-\\uDCD3\\uDCD8-\\uDCFB\\uDD00-\\uDD27\\uDD30-\\uDD63\\uDD70-\\uDD7A\\uDD7C-\\uDD8A\\uDD8C-\\uDD92\\uDD94\\uDD95\\uDD97-\\uDDA1\\uDDA3-\\uDDB1\\uDDB3-\\uDDB9\\uDDBB\\uDDBC\\uDE00-\\uDF36\\uDF40-\\uDF55\\uDF60-\\uDF67\\uDF80-\\uDF85\\uDF87-\\uDFB0\\uDFB2-\\uDFBA]|\\uD802[\\uDC00-\\uDC05\\uDC08\\uDC0A-\\uDC35\\uDC37\\uDC38\\uDC3C\\uDC3F-\\uDC55\\uDC60-\\uDC76\\uDC80-\\uDC9E\\uDCE0-\\uDCF2\\uDCF4\\uDCF5\\uDD00-\\uDD15\\uDD20-\\uDD39\\uDD80-\\uDDB7\\uDDBE\\uDDBF\\uDE00\\uDE10-\\uDE13\\uDE15-\\uDE17\\uDE19-\\uDE35\\uDE60-\\uDE7C\\uDE80-\\uDE9C\\uDEC0-\\uDEC7\\uDEC9-\\uDEE4\\uDF00-\\uDF35\\uDF40-\\uDF55\\uDF60-\\uDF72\\uDF80-\\uDF91]|\\uD803[\\uDC00-\\uDC48\\uDC80-\\uDCB2\\uDCC0-\\uDCF2\\uDD00-\\uDD23\\uDE80-\\uDEA9\\uDEB0\\uDEB1\\uDF00-\\uDF1C\\uDF27\\uDF30-\\uDF45\\uDF70-\\uDF81\\uDFB0-\\uDFC4\\uDFE0-\\uDFF6]|\\uD804[\\uDC03-\\uDC37\\uDC71\\uDC72\\uDC75\\uDC83-\\uDCAF\\uDCD0-\\uDCE8\\uDD03-\\uDD26\\uDD44\\uDD47\\uDD50-\\uDD72\\uDD76\\uDD83-\\uDDB2\\uDDC1-\\uDDC4\\uDDDA\\uDDDC\\uDE00-\\uDE11\\uDE13-\\uDE2B\\uDE3F\\uDE40\\uDE80-\\uDE86\\uDE88\\uDE8A-\\uDE8D\\uDE8F-\\uDE9D\\uDE9F-\\uDEA8\\uDEB0-\\uDEDE\\uDF05-\\uDF0C\\uDF0F\\uDF10\\uDF13-\\uDF28\\uDF2A-\\uDF30\\uDF32\\uDF33\\uDF35-\\uDF39\\uDF3D\\uDF50\\uDF5D-\\uDF61]|\\uD805[\\uDC00-\\uDC34\\uDC47-\\uDC4A\\uDC5F-\\uDC61\\uDC80-\\uDCAF\\uDCC4\\uDCC5\\uDCC7\\uDD80-\\uDDAE\\uDDD8-\\uDDDB\\uDE00-\\uDE2F\\uDE44\\uDE80-\\uDEAA\\uDEB8\\uDF00-\\uDF1A\\uDF40-\\uDF46]|\\uD806[\\uDC00-\\uDC2B\\uDCA0-\\uDCDF\\uDCFF-\\uDD06\\uDD09\\uDD0C-\\uDD13\\uDD15\\uDD16\\uDD18-\\uDD2F\\uDD3F\\uDD41\\uDDA0-\\uDDA7\\uDDAA-\\uDDD0\\uDDE1\\uDDE3\\uDE00\\uDE0B-\\uDE32\\uDE3A\\uDE50\\uDE5C-\\uDE89\\uDE9D\\uDEB0-\\uDEF8]|\\uD807[\\uDC00-\\uDC08\\uDC0A-\\uDC2E\\uDC40\\uDC72-\\uDC8F\\uDD00-\\uDD06\\uDD08\\uDD09\\uDD0B-\\uDD30\\uDD46\\uDD60-\\uDD65\\uDD67\\uDD68\\uDD6A-\\uDD89\\uDD98\\uDEE0-\\uDEF2\\uDF02\\uDF04-\\uDF10\\uDF12-\\uDF33\\uDFB0]|\\uD808[\\uDC00-\\uDF99]|\\uD809[\\uDC80-\\uDD43]|\\uD80B[\\uDF90-\\uDFF0]|[\\uD80C\\uD81C-\\uD820\\uD822\\uD840-\\uD868\\uD86A-\\uD86C\\uD86F-\\uD872\\uD874-\\uD879\\uD880-\\uD883\\uD885-\\uD887][\\uDC00-\\uDFFF]|\\uD80D[\\uDC00-\\uDC2F\\uDC41-\\uDC46]|\\uD811[\\uDC00-\\uDE46]|\\uD81A[\\uDC00-\\uDE38\\uDE40-\\uDE5E\\uDE70-\\uDEBE\\uDED0-\\uDEED\\uDF00-\\uDF2F\\uDF40-\\uDF43\\uDF63-\\uDF77\\uDF7D-\\uDF8F]|\\uD81B[\\uDE40-\\uDE7F\\uDF00-\\uDF4A\\uDF50\\uDF93-\\uDF9F\\uDFE0\\uDFE1\\uDFE3]|\\uD821[\\uDC00-\\uDFF7]|\\uD823[\\uDC00-\\uDCD5\\uDD00-\\uDD08]|\\uD82B[\\uDFF0-\\uDFF3\\uDFF5-\\uDFFB\\uDFFD\\uDFFE]|\\uD82C[\\uDC00-\\uDD22\\uDD32\\uDD50-\\uDD52\\uDD55\\uDD64-\\uDD67\\uDD70-\\uDEFB]|\\uD82F[\\uDC00-\\uDC6A\\uDC70-\\uDC7C\\uDC80-\\uDC88\\uDC90-\\uDC99]|\\uD835[\\uDC00-\\uDC54\\uDC56-\\uDC9C\\uDC9E\\uDC9F\\uDCA2\\uDCA5\\uDCA6\\uDCA9-\\uDCAC\\uDCAE-\\uDCB9\\uDCBB\\uDCBD-\\uDCC3\\uDCC5-\\uDD05\\uDD07-\\uDD0A\\uDD0D-\\uDD14\\uDD16-\\uDD1C\\uDD1E-\\uDD39\\uDD3B-\\uDD3E\\uDD40-\\uDD44\\uDD46\\uDD4A-\\uDD50\\uDD52-\\uDEA5\\uDEA8-\\uDEC0\\uDEC2-\\uDEDA\\uDEDC-\\uDEFA\\uDEFC-\\uDF14\\uDF16-\\uDF34\\uDF36-\\uDF4E\\uDF50-\\uDF6E\\uDF70-\\uDF88\\uDF8A-\\uDFA8\\uDFAA-\\uDFC2\\uDFC4-\\uDFCB]|\\uD837[\\uDF00-\\uDF1E\\uDF25-\\uDF2A]|\\uD838[\\uDC30-\\uDC6D\\uDD00-\\uDD2C\\uDD37-\\uDD3D\\uDD4E\\uDE90-\\uDEAD\\uDEC0-\\uDEEB]|\\uD839[\\uDCD0-\\uDCEB\\uDFE0-\\uDFE6\\uDFE8-\\uDFEB\\uDFED\\uDFEE\\uDFF0-\\uDFFE]|\\uD83A[\\uDC00-\\uDCC4\\uDD00-\\uDD43\\uDD4B]|\\uD83B[\\uDE00-\\uDE03\\uDE05-\\uDE1F\\uDE21\\uDE22\\uDE24\\uDE27\\uDE29-\\uDE32\\uDE34-\\uDE37\\uDE39\\uDE3B\\uDE42\\uDE47\\uDE49\\uDE4B\\uDE4D-\\uDE4F\\uDE51\\uDE52\\uDE54\\uDE57\\uDE59\\uDE5B\\uDE5D\\uDE5F\\uDE61\\uDE62\\uDE64\\uDE67-\\uDE6A\\uDE6C-\\uDE72\\uDE74-\\uDE77\\uDE79-\\uDE7C\\uDE7E\\uDE80-\\uDE89\\uDE8B-\\uDE9B\\uDEA1-\\uDEA3\\uDEA5-\\uDEA9\\uDEAB-\\uDEBB]|\\uD869[\\uDC00-\\uDEDF\\uDF00-\\uDFFF]|\\uD86D[\\uDC00-\\uDF39\\uDF40-\\uDFFF]|\\uD86E[\\uDC00-\\uDC1D\\uDC20-\\uDFFF]|\\uD873[\\uDC00-\\uDEA1\\uDEB0-\\uDFFF]|\\uD87A[\\uDC00-\\uDFE0]|\\uD87E[\\uDC00-\\uDE1D]|\\uD884[\\uDC00-\\uDF4A\\uDF50-\\uDFFF]|\\uD888[\\uDC00-\\uDFAF])*:/\r\n\t\t\t\t\t),\r\n\t\t\t\t/(?:[\\x2D\\.0-9A-Z_a-z\\xAA\\xB5\\xBA\\xC0-\\xD6\\xD8-\\xF6\\xF8-\\u02C1\\u02C6-\\u02D1\\u02E0-\\u02E4\\u02EC\\u02EE\\u0370-\\u0374\\u0376\\u0377\\u037A-\\u037D\\u037F\\u0386\\u0388-\\u038A\\u038C\\u038E-\\u03A1\\u03A3-\\u03F5\\u03F7-\\u0481\\u048A-\\u052F\\u0531-\\u0556\\u0559\\u0560-\\u0588\\u05D0-\\u05EA\\u05EF-\\u05F2\\u0620-\\u064A\\u066E\\u066F\\u0671-\\u06D3\\u06D5\\u06E5\\u06E6\\u06EE\\u06EF\\u06FA-\\u06FC\\u06FF\\u0710\\u0712-\\u072F\\u074D-\\u07A5\\u07B1\\u07CA-\\u07EA\\u07F4\\u07F5\\u07FA\\u0800-\\u0815\\u081A\\u0824\\u0828\\u0840-\\u0858\\u0860-\\u086A\\u0870-\\u0887\\u0889-\\u088E\\u08A0-\\u08C9\\u0904-\\u0939\\u093D\\u0950\\u0958-\\u0961\\u0971-\\u0980\\u0985-\\u098C\\u098F\\u0990\\u0993-\\u09A8\\u09AA-\\u09B0\\u09B2\\u09B6-\\u09B9\\u09BD\\u09CE\\u09DC\\u09DD\\u09DF-\\u09E1\\u09F0\\u09F1\\u09FC\\u0A05-\\u0A0A\\u0A0F\\u0A10\\u0A13-\\u0A28\\u0A2A-\\u0A30\\u0A32\\u0A33\\u0A35\\u0A36\\u0A38\\u0A39\\u0A59-\\u0A5C\\u0A5E\\u0A72-\\u0A74\\u0A85-\\u0A8D\\u0A8F-\\u0A91\\u0A93-\\u0AA8\\u0AAA-\\u0AB0\\u0AB2\\u0AB3\\u0AB5-\\u0AB9\\u0ABD\\u0AD0\\u0AE0\\u0AE1\\u0AF9\\u0B05-\\u0B0C\\u0B0F\\u0B10\\u0B13-\\u0B28\\u0B2A-\\u0B30\\u0B32\\u0B33\\u0B35-\\u0B39\\u0B3D\\u0B5C\\u0B5D\\u0B5F-\\u0B61\\u0B71\\u0B83\\u0B85-\\u0B8A\\u0B8E-\\u0B90\\u0B92-\\u0B95\\u0B99\\u0B9A\\u0B9C\\u0B9E\\u0B9F\\u0BA3\\u0BA4\\u0BA8-\\u0BAA\\u0BAE-\\u0BB9\\u0BD0\\u0C05-\\u0C0C\\u0C0E-\\u0C10\\u0C12-\\u0C28\\u0C2A-\\u0C39\\u0C3D\\u0C58-\\u0C5A\\u0C5D\\u0C60\\u0C61\\u0C80\\u0C85-\\u0C8C\\u0C8E-\\u0C90\\u0C92-\\u0CA8\\u0CAA-\\u0CB3\\u0CB5-\\u0CB9\\u0CBD\\u0CDD\\u0CDE\\u0CE0\\u0CE1\\u0CF1\\u0CF2\\u0D04-\\u0D0C\\u0D0E-\\u0D10\\u0D12-\\u0D3A\\u0D3D\\u0D4E\\u0D54-\\u0D56\\u0D5F-\\u0D61\\u0D7A-\\u0D7F\\u0D85-\\u0D96\\u0D9A-\\u0DB1\\u0DB3-\\u0DBB\\u0DBD\\u0DC0-\\u0DC6\\u0E01-\\u0E30\\u0E32\\u0E33\\u0E40-\\u0E46\\u0E81\\u0E82\\u0E84\\u0E86-\\u0E8A\\u0E8C-\\u0EA3\\u0EA5\\u0EA7-\\u0EB0\\u0EB2\\u0EB3\\u0EBD\\u0EC0-\\u0EC4\\u0EC6\\u0EDC-\\u0EDF\\u0F00\\u0F40-\\u0F47\\u0F49-\\u0F6C\\u0F88-\\u0F8C\\u1000-\\u102A\\u103F\\u1050-\\u1055\\u105A-\\u105D\\u1061\\u1065\\u1066\\u106E-\\u1070\\u1075-\\u1081\\u108E\\u10A0-\\u10C5\\u10C7\\u10CD\\u10D0-\\u10FA\\u10FC-\\u1248\\u124A-\\u124D\\u1250-\\u1256\\u1258\\u125A-\\u125D\\u1260-\\u1288\\u128A-\\u128D\\u1290-\\u12B0\\u12B2-\\u12B5\\u12B8-\\u12BE\\u12C0\\u12C2-\\u12C5\\u12C8-\\u12D6\\u12D8-\\u1310\\u1312-\\u1315\\u1318-\\u135A\\u1380-\\u138F\\u13A0-\\u13F5\\u13F8-\\u13FD\\u1401-\\u166C\\u166F-\\u167F\\u1681-\\u169A\\u16A0-\\u16EA\\u16F1-\\u16F8\\u1700-\\u1711\\u171F-\\u1731\\u1740-\\u1751\\u1760-\\u176C\\u176E-\\u1770\\u1780-\\u17B3\\u17D7\\u17DC\\u1820-\\u1878\\u1880-\\u1884\\u1887-\\u18A8\\u18AA\\u18B0-\\u18F5\\u1900-\\u191E\\u1950-\\u196D\\u1970-\\u1974\\u1980-\\u19AB\\u19B0-\\u19C9\\u1A00-\\u1A16\\u1A20-\\u1A54\\u1AA7\\u1B05-\\u1B33\\u1B45-\\u1B4C\\u1B83-\\u1BA0\\u1BAE\\u1BAF\\u1BBA-\\u1BE5\\u1C00-\\u1C23\\u1C4D-\\u1C4F\\u1C5A-\\u1C7D\\u1C80-\\u1C88\\u1C90-\\u1CBA\\u1CBD-\\u1CBF\\u1CE9-\\u1CEC\\u1CEE-\\u1CF3\\u1CF5\\u1CF6\\u1CFA\\u1D00-\\u1DBF\\u1E00-\\u1F15\\u1F18-\\u1F1D\\u1F20-\\u1F45\\u1F48-\\u1F4D\\u1F50-\\u1F57\\u1F59\\u1F5B\\u1F5D\\u1F5F-\\u1F7D\\u1F80-\\u1FB4\\u1FB6-\\u1FBC\\u1FBE\\u1FC2-\\u1FC4\\u1FC6-\\u1FCC\\u1FD0-\\u1FD3\\u1FD6-\\u1FDB\\u1FE0-\\u1FEC\\u1FF2-\\u1FF4\\u1FF6-\\u1FFC\\u2071\\u207F\\u2090-\\u209C\\u2102\\u2107\\u210A-\\u2113\\u2115\\u2119-\\u211D\\u2124\\u2126\\u2128\\u212A-\\u212D\\u212F-\\u2139\\u213C-\\u213F\\u2145-\\u2149\\u214E\\u2183\\u2184\\u2C00-\\u2CE4\\u2CEB-\\u2CEE\\u2CF2\\u2CF3\\u2D00-\\u2D25\\u2D27\\u2D2D\\u2D30-\\u2D67\\u2D6F\\u2D80-\\u2D96\\u2DA0-\\u2DA6\\u2DA8-\\u2DAE\\u2DB0-\\u2DB6\\u2DB8-\\u2DBE\\u2DC0-\\u2DC6\\u2DC8-\\u2DCE\\u2DD0-\\u2DD6\\u2DD8-\\u2DDE\\u2E2F\\u3005\\u3006\\u3031-\\u3035\\u303B\\u303C\\u3041-\\u3096\\u309D-\\u309F\\u30A1-\\u30FA\\u30FC-\\u30FF\\u3105-\\u312F\\u3131-\\u318E\\u31A0-\\u31BF\\u31F0-\\u31FF\\u3400-\\u4DBF\\u4E00-\\uA48C\\uA4D0-\\uA4FD\\uA500-\\uA60C\\uA610-\\uA61F\\uA62A\\uA62B\\uA640-\\uA66E\\uA67F-\\uA69D\\uA6A0-\\uA6E5\\uA717-\\uA71F\\uA722-\\uA788\\uA78B-\\uA7CA\\uA7D0\\uA7D1\\uA7D3\\uA7D5-\\uA7D9\\uA7F2-\\uA801\\uA803-\\uA805\\uA807-\\uA80A\\uA80C-\\uA822\\uA840-\\uA873\\uA882-\\uA8B3\\uA8F2-\\uA8F7\\uA8FB\\uA8FD\\uA8FE\\uA90A-\\uA925\\uA930-\\uA946\\uA960-\\uA97C\\uA984-\\uA9B2\\uA9CF\\uA9E0-\\uA9E4\\uA9E6-\\uA9EF\\uA9FA-\\uA9FE\\uAA00-\\uAA28\\uAA40-\\uAA42\\uAA44-\\uAA4B\\uAA60-\\uAA76\\uAA7A\\uAA7E-\\uAAAF\\uAAB1\\uAAB5\\uAAB6\\uAAB9-\\uAABD\\uAAC0\\uAAC2\\uAADB-\\uAADD\\uAAE0-\\uAAEA\\uAAF2-\\uAAF4\\uAB01-\\uAB06\\uAB09-\\uAB0E\\uAB11-\\uAB16\\uAB20-\\uAB26\\uAB28-\\uAB2E\\uAB30-\\uAB5A\\uAB5C-\\uAB69\\uAB70-\\uABE2\\uAC00-\\uD7A3\\uD7B0-\\uD7C6\\uD7CB-\\uD7FB\\uF900-\\uFA6D\\uFA70-\\uFAD9\\uFB00-\\uFB06\\uFB13-\\uFB17\\uFB1D\\uFB1F-\\uFB28\\uFB2A-\\uFB36\\uFB38-\\uFB3C\\uFB3E\\uFB40\\uFB41\\uFB43\\uFB44\\uFB46-\\uFBB1\\uFBD3-\\uFD3D\\uFD50-\\uFD8F\\uFD92-\\uFDC7\\uFDF0-\\uFDFB\\uFE70-\\uFE74\\uFE76-\\uFEFC\\uFF21-\\uFF3A\\uFF41-\\uFF5A\\uFF66-\\uFFBE\\uFFC2-\\uFFC7\\uFFCA-\\uFFCF\\uFFD2-\\uFFD7\\uFFDA-\\uFFDC]|\\uD800[\\uDC00-\\uDC0B\\uDC0D-\\uDC26\\uDC28-\\uDC3A\\uDC3C\\uDC3D\\uDC3F-\\uDC4D\\uDC50-\\uDC5D\\uDC80-\\uDCFA\\uDE80-\\uDE9C\\uDEA0-\\uDED0\\uDF00-\\uDF1F\\uDF2D-\\uDF40\\uDF42-\\uDF49\\uDF50-\\uDF75\\uDF80-\\uDF9D\\uDFA0-\\uDFC3\\uDFC8-\\uDFCF]|\\uD801[\\uDC00-\\uDC9D\\uDCB0-\\uDCD3\\uDCD8-\\uDCFB\\uDD00-\\uDD27\\uDD30-\\uDD63\\uDD70-\\uDD7A\\uDD7C-\\uDD8A\\uDD8C-\\uDD92\\uDD94\\uDD95\\uDD97-\\uDDA1\\uDDA3-\\uDDB1\\uDDB3-\\uDDB9\\uDDBB\\uDDBC\\uDE00-\\uDF36\\uDF40-\\uDF55\\uDF60-\\uDF67\\uDF80-\\uDF85\\uDF87-\\uDFB0\\uDFB2-\\uDFBA]|\\uD802[\\uDC00-\\uDC05\\uDC08\\uDC0A-\\uDC35\\uDC37\\uDC38\\uDC3C\\uDC3F-\\uDC55\\uDC60-\\uDC76\\uDC80-\\uDC9E\\uDCE0-\\uDCF2\\uDCF4\\uDCF5\\uDD00-\\uDD15\\uDD20-\\uDD39\\uDD80-\\uDDB7\\uDDBE\\uDDBF\\uDE00\\uDE10-\\uDE13\\uDE15-\\uDE17\\uDE19-\\uDE35\\uDE60-\\uDE7C\\uDE80-\\uDE9C\\uDEC0-\\uDEC7\\uDEC9-\\uDEE4\\uDF00-\\uDF35\\uDF40-\\uDF55\\uDF60-\\uDF72\\uDF80-\\uDF91]|\\uD803[\\uDC00-\\uDC48\\uDC80-\\uDCB2\\uDCC0-\\uDCF2\\uDD00-\\uDD23\\uDE80-\\uDEA9\\uDEB0\\uDEB1\\uDF00-\\uDF1C\\uDF27\\uDF30-\\uDF45\\uDF70-\\uDF81\\uDFB0-\\uDFC4\\uDFE0-\\uDFF6]|\\uD804[\\uDC03-\\uDC37\\uDC71\\uDC72\\uDC75\\uDC83-\\uDCAF\\uDCD0-\\uDCE8\\uDD03-\\uDD26\\uDD44\\uDD47\\uDD50-\\uDD72\\uDD76\\uDD83-\\uDDB2\\uDDC1-\\uDDC4\\uDDDA\\uDDDC\\uDE00-\\uDE11\\uDE13-\\uDE2B\\uDE3F\\uDE40\\uDE80-\\uDE86\\uDE88\\uDE8A-\\uDE8D\\uDE8F-\\uDE9D\\uDE9F-\\uDEA8\\uDEB0-\\uDEDE\\uDF05-\\uDF0C\\uDF0F\\uDF10\\uDF13-\\uDF28\\uDF2A-\\uDF30\\uDF32\\uDF33\\uDF35-\\uDF39\\uDF3D\\uDF50\\uDF5D-\\uDF61]|\\uD805[\\uDC00-\\uDC34\\uDC47-\\uDC4A\\uDC5F-\\uDC61\\uDC80-\\uDCAF\\uDCC4\\uDCC5\\uDCC7\\uDD80-\\uDDAE\\uDDD8-\\uDDDB\\uDE00-\\uDE2F\\uDE44\\uDE80-\\uDEAA\\uDEB8\\uDF00-\\uDF1A\\uDF40-\\uDF46]|\\uD806[\\uDC00-\\uDC2B\\uDCA0-\\uDCDF\\uDCFF-\\uDD06\\uDD09\\uDD0C-\\uDD13\\uDD15\\uDD16\\uDD18-\\uDD2F\\uDD3F\\uDD41\\uDDA0-\\uDDA7\\uDDAA-\\uDDD0\\uDDE1\\uDDE3\\uDE00\\uDE0B-\\uDE32\\uDE3A\\uDE50\\uDE5C-\\uDE89\\uDE9D\\uDEB0-\\uDEF8]|\\uD807[\\uDC00-\\uDC08\\uDC0A-\\uDC2E\\uDC40\\uDC72-\\uDC8F\\uDD00-\\uDD06\\uDD08\\uDD09\\uDD0B-\\uDD30\\uDD46\\uDD60-\\uDD65\\uDD67\\uDD68\\uDD6A-\\uDD89\\uDD98\\uDEE0-\\uDEF2\\uDF02\\uDF04-\\uDF10\\uDF12-\\uDF33\\uDFB0]|\\uD808[\\uDC00-\\uDF99]|\\uD809[\\uDC80-\\uDD43]|\\uD80B[\\uDF90-\\uDFF0]|[\\uD80C\\uD81C-\\uD820\\uD822\\uD840-\\uD868\\uD86A-\\uD86C\\uD86F-\\uD872\\uD874-\\uD879\\uD880-\\uD883\\uD885-\\uD887][\\uDC00-\\uDFFF]|\\uD80D[\\uDC00-\\uDC2F\\uDC41-\\uDC46]|\\uD811[\\uDC00-\\uDE46]|\\uD81A[\\uDC00-\\uDE38\\uDE40-\\uDE5E\\uDE70-\\uDEBE\\uDED0-\\uDEED\\uDF00-\\uDF2F\\uDF40-\\uDF43\\uDF63-\\uDF77\\uDF7D-\\uDF8F]|\\uD81B[\\uDE40-\\uDE7F\\uDF00-\\uDF4A\\uDF50\\uDF93-\\uDF9F\\uDFE0\\uDFE1\\uDFE3]|\\uD821[\\uDC00-\\uDFF7]|\\uD823[\\uDC00-\\uDCD5\\uDD00-\\uDD08]|\\uD82B[\\uDFF0-\\uDFF3\\uDFF5-\\uDFFB\\uDFFD\\uDFFE]|\\uD82C[\\uDC00-\\uDD22\\uDD32\\uDD50-\\uDD52\\uDD55\\uDD64-\\uDD67\\uDD70-\\uDEFB]|\\uD82F[\\uDC00-\\uDC6A\\uDC70-\\uDC7C\\uDC80-\\uDC88\\uDC90-\\uDC99]|\\uD835[\\uDC00-\\uDC54\\uDC56-\\uDC9C\\uDC9E\\uDC9F\\uDCA2\\uDCA5\\uDCA6\\uDCA9-\\uDCAC\\uDCAE-\\uDCB9\\uDCBB\\uDCBD-\\uDCC3\\uDCC5-\\uDD05\\uDD07-\\uDD0A\\uDD0D-\\uDD14\\uDD16-\\uDD1C\\uDD1E-\\uDD39\\uDD3B-\\uDD3E\\uDD40-\\uDD44\\uDD46\\uDD4A-\\uDD50\\uDD52-\\uDEA5\\uDEA8-\\uDEC0\\uDEC2-\\uDEDA\\uDEDC-\\uDEFA\\uDEFC-\\uDF14\\uDF16-\\uDF34\\uDF36-\\uDF4E\\uDF50-\\uDF6E\\uDF70-\\uDF88\\uDF8A-\\uDFA8\\uDFAA-\\uDFC2\\uDFC4-\\uDFCB]|\\uD837[\\uDF00-\\uDF1E\\uDF25-\\uDF2A]|\\uD838[\\uDC30-\\uDC6D\\uDD00-\\uDD2C\\uDD37-\\uDD3D\\uDD4E\\uDE90-\\uDEAD\\uDEC0-\\uDEEB]|\\uD839[\\uDCD0-\\uDCEB\\uDFE0-\\uDFE6\\uDFE8-\\uDFEB\\uDFED\\uDFEE\\uDFF0-\\uDFFE]|\\uD83A[\\uDC00-\\uDCC4\\uDD00-\\uDD43\\uDD4B]|\\uD83B[\\uDE00-\\uDE03\\uDE05-\\uDE1F\\uDE21\\uDE22\\uDE24\\uDE27\\uDE29-\\uDE32\\uDE34-\\uDE37\\uDE39\\uDE3B\\uDE42\\uDE47\\uDE49\\uDE4B\\uDE4D-\\uDE4F\\uDE51\\uDE52\\uDE54\\uDE57\\uDE59\\uDE5B\\uDE5D\\uDE5F\\uDE61\\uDE62\\uDE64\\uDE67-\\uDE6A\\uDE6C-\\uDE72\\uDE74-\\uDE77\\uDE79-\\uDE7C\\uDE7E\\uDE80-\\uDE89\\uDE8B-\\uDE9B\\uDEA1-\\uDEA3\\uDEA5-\\uDEA9\\uDEAB-\\uDEBB]|\\uD869[\\uDC00-\\uDEDF\\uDF00-\\uDFFF]|\\uD86D[\\uDC00-\\uDF39\\uDF40-\\uDFFF]|\\uD86E[\\uDC00-\\uDC1D\\uDC20-\\uDFFF]|\\uD873[\\uDC00-\\uDEA1\\uDEB0-\\uDFFF]|\\uD87A[\\uDC00-\\uDFE0]|\\uD87E[\\uDC00-\\uDE1D]|\\uD884[\\uDC00-\\uDF4A\\uDF50-\\uDFFF]|\\uD888[\\uDC00-\\uDFAF])*/\r\n\t\t\t\t),\r\n\t\t\ta = {\r\n\t\t\t\tclassName: \"symbol\",\r\n\t\t\t\tbegin: /&[a-z]+;|&#[0-9]+;|&#x[a-f0-9]+;/\r\n\t\t\t},\r\n\t\t\ti = {\r\n\t\t\t\tbegin: /\\s/,\r\n\t\t\t\tcontains: [{\r\n\t\t\t\t\tclassName: \"keyword\",\r\n\t\t\t\t\tbegin: /#?[a-z_][a-z1-9_-]+/,\r\n\t\t\t\t\tillegal: /\\n/\r\n\t\t\t\t}]\r\n\t\t\t},\r\n\t\t\tr = e.inherit(i, {\r\n\t\t\t\tbegin: /\\(/,\r\n\t\t\t\tend: /\\)/\r\n\t\t\t}),\r\n\t\t\ts = e.inherit(e.APOS_STRING_MODE, {\r\n\t\t\t\tclassName: \"string\"\r\n\t\t\t}),\r\n\t\t\to = e.inherit(e.QUOTE_STRING_MODE, {\r\n\t\t\t\tclassName: \"string\"\r\n\t\t\t}),\r\n\t\t\tl = {\r\n\t\t\t\tendsWithParent: !0,\r\n\t\t\t\tillegal: /</,\r\n\t\t\t\trelevance: 0,\r\n\t\t\t\tcontains: [{\r\n\t\t\t\t\tclassName: \"attr\",\r\n\t\t\t\t\tbegin: /(?:[\\x2D\\.0-:A-Z_a-z\\xAA\\xB5\\xBA\\xC0-\\xD6\\xD8-\\xF6\\xF8-\\u02C1\\u02C6-\\u02D1\\u02E0-\\u02E4\\u02EC\\u02EE\\u0370-\\u0374\\u0376\\u0377\\u037A-\\u037D\\u037F\\u0386\\u0388-\\u038A\\u038C\\u038E-\\u03A1\\u03A3-\\u03F5\\u03F7-\\u0481\\u048A-\\u052F\\u0531-\\u0556\\u0559\\u0560-\\u0588\\u05D0-\\u05EA\\u05EF-\\u05F2\\u0620-\\u064A\\u066E\\u066F\\u0671-\\u06D3\\u06D5\\u06E5\\u06E6\\u06EE\\u06EF\\u06FA-\\u06FC\\u06FF\\u0710\\u0712-\\u072F\\u074D-\\u07A5\\u07B1\\u07CA-\\u07EA\\u07F4\\u07F5\\u07FA\\u0800-\\u0815\\u081A\\u0824\\u0828\\u0840-\\u0858\\u0860-\\u086A\\u0870-\\u0887\\u0889-\\u088E\\u08A0-\\u08C9\\u0904-\\u0939\\u093D\\u0950\\u0958-\\u0961\\u0971-\\u0980\\u0985-\\u098C\\u098F\\u0990\\u0993-\\u09A8\\u09AA-\\u09B0\\u09B2\\u09B6-\\u09B9\\u09BD\\u09CE\\u09DC\\u09DD\\u09DF-\\u09E1\\u09F0\\u09F1\\u09FC\\u0A05-\\u0A0A\\u0A0F\\u0A10\\u0A13-\\u0A28\\u0A2A-\\u0A30\\u0A32\\u0A33\\u0A35\\u0A36\\u0A38\\u0A39\\u0A59-\\u0A5C\\u0A5E\\u0A72-\\u0A74\\u0A85-\\u0A8D\\u0A8F-\\u0A91\\u0A93-\\u0AA8\\u0AAA-\\u0AB0\\u0AB2\\u0AB3\\u0AB5-\\u0AB9\\u0ABD\\u0AD0\\u0AE0\\u0AE1\\u0AF9\\u0B05-\\u0B0C\\u0B0F\\u0B10\\u0B13-\\u0B28\\u0B2A-\\u0B30\\u0B32\\u0B33\\u0B35-\\u0B39\\u0B3D\\u0B5C\\u0B5D\\u0B5F-\\u0B61\\u0B71\\u0B83\\u0B85-\\u0B8A\\u0B8E-\\u0B90\\u0B92-\\u0B95\\u0B99\\u0B9A\\u0B9C\\u0B9E\\u0B9F\\u0BA3\\u0BA4\\u0BA8-\\u0BAA\\u0BAE-\\u0BB9\\u0BD0\\u0C05-\\u0C0C\\u0C0E-\\u0C10\\u0C12-\\u0C28\\u0C2A-\\u0C39\\u0C3D\\u0C58-\\u0C5A\\u0C5D\\u0C60\\u0C61\\u0C80\\u0C85-\\u0C8C\\u0C8E-\\u0C90\\u0C92-\\u0CA8\\u0CAA-\\u0CB3\\u0CB5-\\u0CB9\\u0CBD\\u0CDD\\u0CDE\\u0CE0\\u0CE1\\u0CF1\\u0CF2\\u0D04-\\u0D0C\\u0D0E-\\u0D10\\u0D12-\\u0D3A\\u0D3D\\u0D4E\\u0D54-\\u0D56\\u0D5F-\\u0D61\\u0D7A-\\u0D7F\\u0D85-\\u0D96\\u0D9A-\\u0DB1\\u0DB3-\\u0DBB\\u0DBD\\u0DC0-\\u0DC6\\u0E01-\\u0E30\\u0E32\\u0E33\\u0E40-\\u0E46\\u0E81\\u0E82\\u0E84\\u0E86-\\u0E8A\\u0E8C-\\u0EA3\\u0EA5\\u0EA7-\\u0EB0\\u0EB2\\u0EB3\\u0EBD\\u0EC0-\\u0EC4\\u0EC6\\u0EDC-\\u0EDF\\u0F00\\u0F40-\\u0F47\\u0F49-\\u0F6C\\u0F88-\\u0F8C\\u1000-\\u102A\\u103F\\u1050-\\u1055\\u105A-\\u105D\\u1061\\u1065\\u1066\\u106E-\\u1070\\u1075-\\u1081\\u108E\\u10A0-\\u10C5\\u10C7\\u10CD\\u10D0-\\u10FA\\u10FC-\\u1248\\u124A-\\u124D\\u1250-\\u1256\\u1258\\u125A-\\u125D\\u1260-\\u1288\\u128A-\\u128D\\u1290-\\u12B0\\u12B2-\\u12B5\\u12B8-\\u12BE\\u12C0\\u12C2-\\u12C5\\u12C8-\\u12D6\\u12D8-\\u1310\\u1312-\\u1315\\u1318-\\u135A\\u1380-\\u138F\\u13A0-\\u13F5\\u13F8-\\u13FD\\u1401-\\u166C\\u166F-\\u167F\\u1681-\\u169A\\u16A0-\\u16EA\\u16F1-\\u16F8\\u1700-\\u1711\\u171F-\\u1731\\u1740-\\u1751\\u1760-\\u176C\\u176E-\\u1770\\u1780-\\u17B3\\u17D7\\u17DC\\u1820-\\u1878\\u1880-\\u1884\\u1887-\\u18A8\\u18AA\\u18B0-\\u18F5\\u1900-\\u191E\\u1950-\\u196D\\u1970-\\u1974\\u1980-\\u19AB\\u19B0-\\u19C9\\u1A00-\\u1A16\\u1A20-\\u1A54\\u1AA7\\u1B05-\\u1B33\\u1B45-\\u1B4C\\u1B83-\\u1BA0\\u1BAE\\u1BAF\\u1BBA-\\u1BE5\\u1C00-\\u1C23\\u1C4D-\\u1C4F\\u1C5A-\\u1C7D\\u1C80-\\u1C88\\u1C90-\\u1CBA\\u1CBD-\\u1CBF\\u1CE9-\\u1CEC\\u1CEE-\\u1CF3\\u1CF5\\u1CF6\\u1CFA\\u1D00-\\u1DBF\\u1E00-\\u1F15\\u1F18-\\u1F1D\\u1F20-\\u1F45\\u1F48-\\u1F4D\\u1F50-\\u1F57\\u1F59\\u1F5B\\u1F5D\\u1F5F-\\u1F7D\\u1F80-\\u1FB4\\u1FB6-\\u1FBC\\u1FBE\\u1FC2-\\u1FC4\\u1FC6-\\u1FCC\\u1FD0-\\u1FD3\\u1FD6-\\u1FDB\\u1FE0-\\u1FEC\\u1FF2-\\u1FF4\\u1FF6-\\u1FFC\\u2071\\u207F\\u2090-\\u209C\\u2102\\u2107\\u210A-\\u2113\\u2115\\u2119-\\u211D\\u2124\\u2126\\u2128\\u212A-\\u212D\\u212F-\\u2139\\u213C-\\u213F\\u2145-\\u2149\\u214E\\u2183\\u2184\\u2C00-\\u2CE4\\u2CEB-\\u2CEE\\u2CF2\\u2CF3\\u2D00-\\u2D25\\u2D27\\u2D2D\\u2D30-\\u2D67\\u2D6F\\u2D80-\\u2D96\\u2DA0-\\u2DA6\\u2DA8-\\u2DAE\\u2DB0-\\u2DB6\\u2DB8-\\u2DBE\\u2DC0-\\u2DC6\\u2DC8-\\u2DCE\\u2DD0-\\u2DD6\\u2DD8-\\u2DDE\\u2E2F\\u3005\\u3006\\u3031-\\u3035\\u303B\\u303C\\u3041-\\u3096\\u309D-\\u309F\\u30A1-\\u30FA\\u30FC-\\u30FF\\u3105-\\u312F\\u3131-\\u318E\\u31A0-\\u31BF\\u31F0-\\u31FF\\u3400-\\u4DBF\\u4E00-\\uA48C\\uA4D0-\\uA4FD\\uA500-\\uA60C\\uA610-\\uA61F\\uA62A\\uA62B\\uA640-\\uA66E\\uA67F-\\uA69D\\uA6A0-\\uA6E5\\uA717-\\uA71F\\uA722-\\uA788\\uA78B-\\uA7CA\\uA7D0\\uA7D1\\uA7D3\\uA7D5-\\uA7D9\\uA7F2-\\uA801\\uA803-\\uA805\\uA807-\\uA80A\\uA80C-\\uA822\\uA840-\\uA873\\uA882-\\uA8B3\\uA8F2-\\uA8F7\\uA8FB\\uA8FD\\uA8FE\\uA90A-\\uA925\\uA930-\\uA946\\uA960-\\uA97C\\uA984-\\uA9B2\\uA9CF\\uA9E0-\\uA9E4\\uA9E6-\\uA9EF\\uA9FA-\\uA9FE\\uAA00-\\uAA28\\uAA40-\\uAA42\\uAA44-\\uAA4B\\uAA60-\\uAA76\\uAA7A\\uAA7E-\\uAAAF\\uAAB1\\uAAB5\\uAAB6\\uAAB9-\\uAABD\\uAAC0\\uAAC2\\uAADB-\\uAADD\\uAAE0-\\uAAEA\\uAAF2-\\uAAF4\\uAB01-\\uAB06\\uAB09-\\uAB0E\\uAB11-\\uAB16\\uAB20-\\uAB26\\uAB28-\\uAB2E\\uAB30-\\uAB5A\\uAB5C-\\uAB69\\uAB70-\\uABE2\\uAC00-\\uD7A3\\uD7B0-\\uD7C6\\uD7CB-\\uD7FB\\uF900-\\uFA6D\\uFA70-\\uFAD9\\uFB00-\\uFB06\\uFB13-\\uFB17\\uFB1D\\uFB1F-\\uFB28\\uFB2A-\\uFB36\\uFB38-\\uFB3C\\uFB3E\\uFB40\\uFB41\\uFB43\\uFB44\\uFB46-\\uFBB1\\uFBD3-\\uFD3D\\uFD50-\\uFD8F\\uFD92-\\uFDC7\\uFDF0-\\uFDFB\\uFE70-\\uFE74\\uFE76-\\uFEFC\\uFF21-\\uFF3A\\uFF41-\\uFF5A\\uFF66-\\uFFBE\\uFFC2-\\uFFC7\\uFFCA-\\uFFCF\\uFFD2-\\uFFD7\\uFFDA-\\uFFDC]|\\uD800[\\uDC00-\\uDC0B\\uDC0D-\\uDC26\\uDC28-\\uDC3A\\uDC3C\\uDC3D\\uDC3F-\\uDC4D\\uDC50-\\uDC5D\\uDC80-\\uDCFA\\uDE80-\\uDE9C\\uDEA0-\\uDED0\\uDF00-\\uDF1F\\uDF2D-\\uDF40\\uDF42-\\uDF49\\uDF50-\\uDF75\\uDF80-\\uDF9D\\uDFA0-\\uDFC3\\uDFC8-\\uDFCF]|\\uD801[\\uDC00-\\uDC9D\\uDCB0-\\uDCD3\\uDCD8-\\uDCFB\\uDD00-\\uDD27\\uDD30-\\uDD63\\uDD70-\\uDD7A\\uDD7C-\\uDD8A\\uDD8C-\\uDD92\\uDD94\\uDD95\\uDD97-\\uDDA1\\uDDA3-\\uDDB1\\uDDB3-\\uDDB9\\uDDBB\\uDDBC\\uDE00-\\uDF36\\uDF40-\\uDF55\\uDF60-\\uDF67\\uDF80-\\uDF85\\uDF87-\\uDFB0\\uDFB2-\\uDFBA]|\\uD802[\\uDC00-\\uDC05\\uDC08\\uDC0A-\\uDC35\\uDC37\\uDC38\\uDC3C\\uDC3F-\\uDC55\\uDC60-\\uDC76\\uDC80-\\uDC9E\\uDCE0-\\uDCF2\\uDCF4\\uDCF5\\uDD00-\\uDD15\\uDD20-\\uDD39\\uDD80-\\uDDB7\\uDDBE\\uDDBF\\uDE00\\uDE10-\\uDE13\\uDE15-\\uDE17\\uDE19-\\uDE35\\uDE60-\\uDE7C\\uDE80-\\uDE9C\\uDEC0-\\uDEC7\\uDEC9-\\uDEE4\\uDF00-\\uDF35\\uDF40-\\uDF55\\uDF60-\\uDF72\\uDF80-\\uDF91]|\\uD803[\\uDC00-\\uDC48\\uDC80-\\uDCB2\\uDCC0-\\uDCF2\\uDD00-\\uDD23\\uDE80-\\uDEA9\\uDEB0\\uDEB1\\uDF00-\\uDF1C\\uDF27\\uDF30-\\uDF45\\uDF70-\\uDF81\\uDFB0-\\uDFC4\\uDFE0-\\uDFF6]|\\uD804[\\uDC03-\\uDC37\\uDC71\\uDC72\\uDC75\\uDC83-\\uDCAF\\uDCD0-\\uDCE8\\uDD03-\\uDD26\\uDD44\\uDD47\\uDD50-\\uDD72\\uDD76\\uDD83-\\uDDB2\\uDDC1-\\uDDC4\\uDDDA\\uDDDC\\uDE00-\\uDE11\\uDE13-\\uDE2B\\uDE3F\\uDE40\\uDE80-\\uDE86\\uDE88\\uDE8A-\\uDE8D\\uDE8F-\\uDE9D\\uDE9F-\\uDEA8\\uDEB0-\\uDEDE\\uDF05-\\uDF0C\\uDF0F\\uDF10\\uDF13-\\uDF28\\uDF2A-\\uDF30\\uDF32\\uDF33\\uDF35-\\uDF39\\uDF3D\\uDF50\\uDF5D-\\uDF61]|\\uD805[\\uDC00-\\uDC34\\uDC47-\\uDC4A\\uDC5F-\\uDC61\\uDC80-\\uDCAF\\uDCC4\\uDCC5\\uDCC7\\uDD80-\\uDDAE\\uDDD8-\\uDDDB\\uDE00-\\uDE2F\\uDE44\\uDE80-\\uDEAA\\uDEB8\\uDF00-\\uDF1A\\uDF40-\\uDF46]|\\uD806[\\uDC00-\\uDC2B\\uDCA0-\\uDCDF\\uDCFF-\\uDD06\\uDD09\\uDD0C-\\uDD13\\uDD15\\uDD16\\uDD18-\\uDD2F\\uDD3F\\uDD41\\uDDA0-\\uDDA7\\uDDAA-\\uDDD0\\uDDE1\\uDDE3\\uDE00\\uDE0B-\\uDE32\\uDE3A\\uDE50\\uDE5C-\\uDE89\\uDE9D\\uDEB0-\\uDEF8]|\\uD807[\\uDC00-\\uDC08\\uDC0A-\\uDC2E\\uDC40\\uDC72-\\uDC8F\\uDD00-\\uDD06\\uDD08\\uDD09\\uDD0B-\\uDD30\\uDD46\\uDD60-\\uDD65\\uDD67\\uDD68\\uDD6A-\\uDD89\\uDD98\\uDEE0-\\uDEF2\\uDF02\\uDF04-\\uDF10\\uDF12-\\uDF33\\uDFB0]|\\uD808[\\uDC00-\\uDF99]|\\uD809[\\uDC80-\\uDD43]|\\uD80B[\\uDF90-\\uDFF0]|[\\uD80C\\uD81C-\\uD820\\uD822\\uD840-\\uD868\\uD86A-\\uD86C\\uD86F-\\uD872\\uD874-\\uD879\\uD880-\\uD883\\uD885-\\uD887][\\uDC00-\\uDFFF]|\\uD80D[\\uDC00-\\uDC2F\\uDC41-\\uDC46]|\\uD811[\\uDC00-\\uDE46]|\\uD81A[\\uDC00-\\uDE38\\uDE40-\\uDE5E\\uDE70-\\uDEBE\\uDED0-\\uDEED\\uDF00-\\uDF2F\\uDF40-\\uDF43\\uDF63-\\uDF77\\uDF7D-\\uDF8F]|\\uD81B[\\uDE40-\\uDE7F\\uDF00-\\uDF4A\\uDF50\\uDF93-\\uDF9F\\uDFE0\\uDFE1\\uDFE3]|\\uD821[\\uDC00-\\uDFF7]|\\uD823[\\uDC00-\\uDCD5\\uDD00-\\uDD08]|\\uD82B[\\uDFF0-\\uDFF3\\uDFF5-\\uDFFB\\uDFFD\\uDFFE]|\\uD82C[\\uDC00-\\uDD22\\uDD32\\uDD50-\\uDD52\\uDD55\\uDD64-\\uDD67\\uDD70-\\uDEFB]|\\uD82F[\\uDC00-\\uDC6A\\uDC70-\\uDC7C\\uDC80-\\uDC88\\uDC90-\\uDC99]|\\uD835[\\uDC00-\\uDC54\\uDC56-\\uDC9C\\uDC9E\\uDC9F\\uDCA2\\uDCA5\\uDCA6\\uDCA9-\\uDCAC\\uDCAE-\\uDCB9\\uDCBB\\uDCBD-\\uDCC3\\uDCC5-\\uDD05\\uDD07-\\uDD0A\\uDD0D-\\uDD14\\uDD16-\\uDD1C\\uDD1E-\\uDD39\\uDD3B-\\uDD3E\\uDD40-\\uDD44\\uDD46\\uDD4A-\\uDD50\\uDD52-\\uDEA5\\uDEA8-\\uDEC0\\uDEC2-\\uDEDA\\uDEDC-\\uDEFA\\uDEFC-\\uDF14\\uDF16-\\uDF34\\uDF36-\\uDF4E\\uDF50-\\uDF6E\\uDF70-\\uDF88\\uDF8A-\\uDFA8\\uDFAA-\\uDFC2\\uDFC4-\\uDFCB]|\\uD837[\\uDF00-\\uDF1E\\uDF25-\\uDF2A]|\\uD838[\\uDC30-\\uDC6D\\uDD00-\\uDD2C\\uDD37-\\uDD3D\\uDD4E\\uDE90-\\uDEAD\\uDEC0-\\uDEEB]|\\uD839[\\uDCD0-\\uDCEB\\uDFE0-\\uDFE6\\uDFE8-\\uDFEB\\uDFED\\uDFEE\\uDFF0-\\uDFFE]|\\uD83A[\\uDC00-\\uDCC4\\uDD00-\\uDD43\\uDD4B]|\\uD83B[\\uDE00-\\uDE03\\uDE05-\\uDE1F\\uDE21\\uDE22\\uDE24\\uDE27\\uDE29-\\uDE32\\uDE34-\\uDE37\\uDE39\\uDE3B\\uDE42\\uDE47\\uDE49\\uDE4B\\uDE4D-\\uDE4F\\uDE51\\uDE52\\uDE54\\uDE57\\uDE59\\uDE5B\\uDE5D\\uDE5F\\uDE61\\uDE62\\uDE64\\uDE67-\\uDE6A\\uDE6C-\\uDE72\\uDE74-\\uDE77\\uDE79-\\uDE7C\\uDE7E\\uDE80-\\uDE89\\uDE8B-\\uDE9B\\uDEA1-\\uDEA3\\uDEA5-\\uDEA9\\uDEAB-\\uDEBB]|\\uD869[\\uDC00-\\uDEDF\\uDF00-\\uDFFF]|\\uD86D[\\uDC00-\\uDF39\\uDF40-\\uDFFF]|\\uD86E[\\uDC00-\\uDC1D\\uDC20-\\uDFFF]|\\uD873[\\uDC00-\\uDEA1\\uDEB0-\\uDFFF]|\\uD87A[\\uDC00-\\uDFE0]|\\uD87E[\\uDC00-\\uDE1D]|\\uD884[\\uDC00-\\uDF4A\\uDF50-\\uDFFF]|\\uD888[\\uDC00-\\uDFAF])+/,\r\n\t\t\t\t\trelevance: 0\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: /=\\s*/,\r\n\t\t\t\t\trelevance: 0,\r\n\t\t\t\t\tcontains: [{\r\n\t\t\t\t\t\tclassName: \"string\",\r\n\t\t\t\t\t\tendsParent: !0,\r\n\t\t\t\t\t\tvariants: [{\r\n\t\t\t\t\t\t\tbegin: /\"/,\r\n\t\t\t\t\t\t\tend: /\"/,\r\n\t\t\t\t\t\t\tcontains: [a]\r\n\t\t\t\t\t\t}, {\r\n\t\t\t\t\t\t\tbegin: /'/,\r\n\t\t\t\t\t\t\tend: /'/,\r\n\t\t\t\t\t\t\tcontains: [a]\r\n\t\t\t\t\t\t}, {\r\n\t\t\t\t\t\t\tbegin: /[^\\s\"'=<>`]+/\r\n\t\t\t\t\t\t}]\r\n\t\t\t\t\t}]\r\n\t\t\t\t}]\r\n\t\t\t};\r\n\t\treturn {\r\n\t\t\tname: \"HTML, XML\",\r\n\t\t\taliases: [\"html\", \"xhtml\", \"rss\", \"atom\", \"xjb\", \"xsd\", \"xsl\", \"plist\", \"wsf\", \"svg\"],\r\n\t\t\tcase_insensitive: !0,\r\n\t\t\tunicodeRegex: !0,\r\n\t\t\tcontains: [{\r\n\t\t\t\tclassName: \"meta\",\r\n\t\t\t\tbegin: /<![a-z]/,\r\n\t\t\t\tend: />/,\r\n\t\t\t\trelevance: 10,\r\n\t\t\t\tcontains: [i, o, s, r, {\r\n\t\t\t\t\tbegin: /\\[/,\r\n\t\t\t\t\tend: /\\]/,\r\n\t\t\t\t\tcontains: [{\r\n\t\t\t\t\t\tclassName: \"meta\",\r\n\t\t\t\t\t\tbegin: /<![a-z]/,\r\n\t\t\t\t\t\tend: />/,\r\n\t\t\t\t\t\tcontains: [i, r, o, s]\r\n\t\t\t\t\t}]\r\n\t\t\t\t}]\r\n\t\t\t}, e.COMMENT(/<!--/, /-->/, {\r\n\t\t\t\trelevance: 10\r\n\t\t\t}), {\r\n\t\t\t\tbegin: /<!\\[CDATA\\[/,\r\n\t\t\t\tend: /\\]\\]>/,\r\n\t\t\t\trelevance: 10\r\n\t\t\t}, a, {\r\n\t\t\t\tclassName: \"meta\",\r\n\t\t\t\tend: /\\?>/,\r\n\t\t\t\tvariants: [{\r\n\t\t\t\t\tbegin: /<\\?xml/,\r\n\t\t\t\t\trelevance: 10,\r\n\t\t\t\t\tcontains: [o]\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: /<\\?[a-z][a-z0-9]+/\r\n\t\t\t\t}]\r\n\t\t\t}, {\r\n\t\t\t\tclassName: \"tag\",\r\n\t\t\t\tbegin: /<style(?=\\s|>)/,\r\n\t\t\t\tend: />/,\r\n\t\t\t\tkeywords: {\r\n\t\t\t\t\tname: \"style\"\r\n\t\t\t\t},\r\n\t\t\t\tcontains: [l],\r\n\t\t\t\tstarts: {\r\n\t\t\t\t\tend: /<\\/style>/,\r\n\t\t\t\t\treturnEnd: !0,\r\n\t\t\t\t\tsubLanguage: [\"css\", \"xml\"]\r\n\t\t\t\t}\r\n\t\t\t}, {\r\n\t\t\t\tclassName: \"tag\",\r\n\t\t\t\tbegin: /<script(?=\\s|>)/,\r\n\t\t\t\tend: />/,\r\n\t\t\t\tkeywords: {\r\n\t\t\t\t\tname: \"script\"\r\n\t\t\t\t},\r\n\t\t\t\tcontains: [l],\r\n\t\t\t\tstarts: {\r\n\t\t\t\t\tend: /<\\/script>/,\r\n\t\t\t\t\treturnEnd: !0,\r\n\t\t\t\t\tsubLanguage: [\"javascript\", \"handlebars\", \"xml\"]\r\n\t\t\t\t}\r\n\t\t\t}, {\r\n\t\t\t\tclassName: \"tag\",\r\n\t\t\t\tbegin: /<>|<\\/>/\r\n\t\t\t}, {\r\n\t\t\t\tclassName: \"tag\",\r\n\t\t\t\tbegin: n.concat(/</, n.lookahead(n.concat(t, n.either(/\\/>/, />/, /\\s/)))),\r\n\t\t\t\tend: /\\/?>/,\r\n\t\t\t\tcontains: [{\r\n\t\t\t\t\tclassName: \"name\",\r\n\t\t\t\t\tbegin: t,\r\n\t\t\t\t\trelevance: 0,\r\n\t\t\t\t\tstarts: l\r\n\t\t\t\t}]\r\n\t\t\t}, {\r\n\t\t\t\tclassName: \"tag\",\r\n\t\t\t\tbegin: n.concat(/<\\//, n.lookahead(n.concat(t, />/))),\r\n\t\t\t\tcontains: [{\r\n\t\t\t\t\tclassName: \"name\",\r\n\t\t\t\t\tbegin: t,\r\n\t\t\t\t\trelevance: 0\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: />/,\r\n\t\t\t\t\trelevance: 0,\r\n\t\t\t\t\tendsParent: !0\r\n\t\t\t\t}]\r\n\t\t\t}]\r\n\t\t}\r\n\t},\r\n\tgrmr_markdown: e => {\r\n\t\tconst n = {\r\n\t\t\t\tbegin: /<\\/?[A-Za-z_]/,\r\n\t\t\t\tend: \">\",\r\n\t\t\t\tsubLanguage: \"xml\",\r\n\t\t\t\trelevance: 0\r\n\t\t\t},\r\n\t\t\tt = {\r\n\t\t\t\tvariants: [{\r\n\t\t\t\t\tbegin: /\\[.+?\\]\\[.*?\\]/,\r\n\t\t\t\t\trelevance: 0\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: /\\[.+?\\]\\(((data|javascript|mailto):|(?:http|ftp)s?:\\/\\/).*?\\)/,\r\n\t\t\t\t\trelevance: 2\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: e.regex.concat(/\\[.+?\\]\\(/, /[A-Za-z][A-Za-z0-9+.-]*/, /:\\/\\/.*?\\)/),\r\n\t\t\t\t\trelevance: 2\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: /\\[.+?\\]\\([./?&#].*?\\)/,\r\n\t\t\t\t\trelevance: 1\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: /\\[.*?\\]\\(.*?\\)/,\r\n\t\t\t\t\trelevance: 0\r\n\t\t\t\t}],\r\n\t\t\t\treturnBegin: !0,\r\n\t\t\t\tcontains: [{\r\n\t\t\t\t\tmatch: /\\[(?=\\])/\r\n\t\t\t\t}, {\r\n\t\t\t\t\tclassName: \"string\",\r\n\t\t\t\t\trelevance: 0,\r\n\t\t\t\t\tbegin: \"\\\\[\",\r\n\t\t\t\t\tend: \"\\\\]\",\r\n\t\t\t\t\texcludeBegin: !0,\r\n\t\t\t\t\treturnEnd: !0\r\n\t\t\t\t}, {\r\n\t\t\t\t\tclassName: \"link\",\r\n\t\t\t\t\trelevance: 0,\r\n\t\t\t\t\tbegin: \"\\\\]\\\\(\",\r\n\t\t\t\t\tend: \"\\\\)\",\r\n\t\t\t\t\texcludeBegin: !0,\r\n\t\t\t\t\texcludeEnd: !0\r\n\t\t\t\t}, {\r\n\t\t\t\t\tclassName: \"symbol\",\r\n\t\t\t\t\trelevance: 0,\r\n\t\t\t\t\tbegin: \"\\\\]\\\\[\",\r\n\t\t\t\t\tend: \"\\\\]\",\r\n\t\t\t\t\texcludeBegin: !0,\r\n\t\t\t\t\texcludeEnd: !0\r\n\t\t\t\t}]\r\n\t\t\t},\r\n\t\t\ta = {\r\n\t\t\t\tclassName: \"strong\",\r\n\t\t\t\tcontains: [],\r\n\t\t\t\tvariants: [{\r\n\t\t\t\t\tbegin: /_{2}(?!\\s)/,\r\n\t\t\t\t\tend: /_{2}/\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: /\\*{2}(?!\\s)/,\r\n\t\t\t\t\tend: /\\*{2}/\r\n\t\t\t\t}]\r\n\t\t\t},\r\n\t\t\ti = {\r\n\t\t\t\tclassName: \"emphasis\",\r\n\t\t\t\tcontains: [],\r\n\t\t\t\tvariants: [{\r\n\t\t\t\t\tbegin: /\\*(?![*\\s])/,\r\n\t\t\t\t\tend: /\\*/\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: /_(?![_\\s])/,\r\n\t\t\t\t\tend: /_/,\r\n\t\t\t\t\trelevance: 0\r\n\t\t\t\t}]\r\n\t\t\t},\r\n\t\t\tr = e.inherit(a, {\r\n\t\t\t\tcontains: []\r\n\t\t\t}),\r\n\t\t\ts = e.inherit(i, {\r\n\t\t\t\tcontains: []\r\n\t\t\t});\r\n\t\ta.contains.push(s), i.contains.push(r);\r\n\t\tlet o = [n, t];\r\n\t\treturn [a, i, r, s].forEach((e => {\r\n\t\t\te.contains = e.contains.concat(o)\r\n\t\t})), o = o.concat(a, i), {\r\n\t\t\tname: \"Markdown\",\r\n\t\t\taliases: [\"md\", \"mkdown\", \"mkd\"],\r\n\t\t\tcontains: [{\r\n\t\t\t\tclassName: \"section\",\r\n\t\t\t\tvariants: [{\r\n\t\t\t\t\tbegin: \"^#{1,6}\",\r\n\t\t\t\t\tend: \"$\",\r\n\t\t\t\t\tcontains: o\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: \"(?=^.+?\\\\n[=-]{2,}$)\",\r\n\t\t\t\t\tcontains: [{\r\n\t\t\t\t\t\tbegin: \"^[=-]*$\"\r\n\t\t\t\t\t}, {\r\n\t\t\t\t\t\tbegin: \"^\",\r\n\t\t\t\t\t\tend: \"\\\\n\",\r\n\t\t\t\t\t\tcontains: o\r\n\t\t\t\t\t}]\r\n\t\t\t\t}]\r\n\t\t\t}, n, {\r\n\t\t\t\tclassName: \"bullet\",\r\n\t\t\t\tbegin: \"^[ \\t]*([*+-]|(\\\\d+\\\\.))(?=\\\\s+)\",\r\n\t\t\t\tend: \"\\\\s+\",\r\n\t\t\t\texcludeEnd: !0\r\n\t\t\t}, a, i, {\r\n\t\t\t\tclassName: \"quote\",\r\n\t\t\t\tbegin: \"^>\\\\s+\",\r\n\t\t\t\tcontains: o,\r\n\t\t\t\tend: \"$\"\r\n\t\t\t}, {\r\n\t\t\t\tclassName: \"code\",\r\n\t\t\t\tvariants: [{\r\n\t\t\t\t\tbegin: \"(`{3,})[^`](.|\\\\n)*?\\\\1`*[ ]*\"\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: \"(~{3,})[^~](.|\\\\n)*?\\\\1~*[ ]*\"\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: \"```\",\r\n\t\t\t\t\tend: \"```+[ ]*$\"\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: \"~~~\",\r\n\t\t\t\t\tend: \"~~~+[ ]*$\"\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: \"`.+?`\"\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: \"(?=^( {4}|\\\\t))\",\r\n\t\t\t\t\tcontains: [{\r\n\t\t\t\t\t\tbegin: \"^( {4}|\\\\t)\",\r\n\t\t\t\t\t\tend: \"(\\\\n)$\"\r\n\t\t\t\t\t}],\r\n\t\t\t\t\trelevance: 0\r\n\t\t\t\t}]\r\n\t\t\t}, {\r\n\t\t\t\tbegin: \"^[-\\\\*]{3,}\",\r\n\t\t\t\tend: \"$\"\r\n\t\t\t}, t, {\r\n\t\t\t\tbegin: /^\\[[^\\n]+\\]:/,\r\n\t\t\t\treturnBegin: !0,\r\n\t\t\t\tcontains: [{\r\n\t\t\t\t\tclassName: \"symbol\",\r\n\t\t\t\t\tbegin: /\\[/,\r\n\t\t\t\t\tend: /\\]/,\r\n\t\t\t\t\texcludeBegin: !0,\r\n\t\t\t\t\texcludeEnd: !0\r\n\t\t\t\t}, {\r\n\t\t\t\t\tclassName: \"link\",\r\n\t\t\t\t\tbegin: /:\\s*/,\r\n\t\t\t\t\tend: /$/,\r\n\t\t\t\t\texcludeBegin: !0\r\n\t\t\t\t}]\r\n\t\t\t}]\r\n\t\t}\r\n\t},\r\n\tgrmr_objectivec: e => {\r\n\t\tconst n = /[a-zA-Z@][a-zA-Z0-9_]*/,\r\n\t\t\tt = {\r\n\t\t\t\t$pattern: n,\r\n\t\t\t\tkeyword: [\"@interface\", \"@class\", \"@protocol\", \"@implementation\"]\r\n\t\t\t};\r\n\t\treturn {\r\n\t\t\tname: \"Objective-C\",\r\n\t\t\taliases: [\"mm\", \"objc\", \"obj-c\", \"obj-c++\", \"objective-c++\"],\r\n\t\t\tkeywords: {\r\n\t\t\t\t\"variable.language\": [\"this\", \"super\"],\r\n\t\t\t\t$pattern: n,\r\n\t\t\t\tkeyword: [\"while\", \"export\", \"sizeof\", \"typedef\", \"const\", \"struct\", \"for\", \"union\", \"volatile\", \"static\",\r\n\t\t\t\t\t\"mutable\", \"if\", \"do\", \"return\", \"goto\", \"enum\", \"else\", \"break\", \"extern\", \"asm\", \"case\", \"default\",\r\n\t\t\t\t\t\"register\", \"explicit\", \"typename\", \"switch\", \"continue\", \"inline\", \"readonly\", \"assign\", \"readwrite\",\r\n\t\t\t\t\t\"self\", \"@synchronized\", \"id\", \"typeof\", \"nonatomic\", \"IBOutlet\", \"IBAction\", \"strong\", \"weak\",\r\n\t\t\t\t\t\"copy\", \"in\", \"out\", \"inout\", \"bycopy\", \"byref\", \"oneway\", \"__strong\", \"__weak\", \"__block\",\r\n\t\t\t\t\t\"__autoreleasing\", \"@private\", \"@protected\", \"@public\", \"@try\", \"@property\", \"@end\", \"@throw\",\r\n\t\t\t\t\t\"@catch\", \"@finally\", \"@autoreleasepool\", \"@synthesize\", \"@dynamic\", \"@selector\", \"@optional\",\r\n\t\t\t\t\t\"@required\", \"@encode\", \"@package\", \"@import\", \"@defs\", \"@compatibility_alias\", \"__bridge\",\r\n\t\t\t\t\t\"__bridge_transfer\", \"__bridge_retained\", \"__bridge_retain\", \"__covariant\", \"__contravariant\",\r\n\t\t\t\t\t\"__kindof\", \"_Nonnull\", \"_Nullable\", \"_Null_unspecified\", \"__FUNCTION__\", \"__PRETTY_FUNCTION__\",\r\n\t\t\t\t\t\"__attribute__\", \"getter\", \"setter\", \"retain\", \"unsafe_unretained\", \"nonnull\", \"nullable\",\r\n\t\t\t\t\t\"null_unspecified\", \"null_resettable\", \"class\", \"instancetype\", \"NS_DESIGNATED_INITIALIZER\",\r\n\t\t\t\t\t\"NS_UNAVAILABLE\", \"NS_REQUIRES_SUPER\", \"NS_RETURNS_INNER_POINTER\", \"NS_INLINE\", \"NS_AVAILABLE\",\r\n\t\t\t\t\t\"NS_DEPRECATED\", \"NS_ENUM\", \"NS_OPTIONS\", \"NS_SWIFT_UNAVAILABLE\", \"NS_ASSUME_NONNULL_BEGIN\",\r\n\t\t\t\t\t\"NS_ASSUME_NONNULL_END\", \"NS_REFINED_FOR_SWIFT\", \"NS_SWIFT_NAME\", \"NS_SWIFT_NOTHROW\", \"NS_DURING\",\r\n\t\t\t\t\t\"NS_HANDLER\", \"NS_ENDHANDLER\", \"NS_VALUERETURN\", \"NS_VOIDRETURN\"\r\n\t\t\t\t],\r\n\t\t\t\tliteral: [\"false\", \"true\", \"FALSE\", \"TRUE\", \"nil\", \"YES\", \"NO\", \"NULL\"],\r\n\t\t\t\tbuilt_in: [\"dispatch_once_t\", \"dispatch_queue_t\", \"dispatch_sync\", \"dispatch_async\", \"dispatch_once\"],\r\n\t\t\t\ttype: [\"int\", \"float\", \"char\", \"unsigned\", \"signed\", \"short\", \"long\", \"double\", \"wchar_t\", \"unichar\",\r\n\t\t\t\t\t\"void\", \"bool\", \"BOOL\", \"id|0\", \"_Bool\"\r\n\t\t\t\t]\r\n\t\t\t},\r\n\t\t\tillegal: \"</\",\r\n\t\t\tcontains: [{\r\n\t\t\t\t\tclassName: \"built_in\",\r\n\t\t\t\t\tbegin: \"\\\\b(AV|CA|CF|CG|CI|CL|CM|CN|CT|MK|MP|MTK|MTL|NS|SCN|SK|UI|WK|XC)\\\\w+\"\r\n\t\t\t\t}, e.C_LINE_COMMENT_MODE, e.C_BLOCK_COMMENT_MODE, e.C_NUMBER_MODE, e.QUOTE_STRING_MODE, e\r\n\t\t\t\t.APOS_STRING_MODE, {\r\n\t\t\t\t\tclassName: \"string\",\r\n\t\t\t\t\tvariants: [{\r\n\t\t\t\t\t\tbegin: '@\"',\r\n\t\t\t\t\t\tend: '\"',\r\n\t\t\t\t\t\tillegal: \"\\\\n\",\r\n\t\t\t\t\t\tcontains: [e.BACKSLASH_ESCAPE]\r\n\t\t\t\t\t}]\r\n\t\t\t\t}, {\r\n\t\t\t\t\tclassName: \"meta\",\r\n\t\t\t\t\tbegin: /#\\s*[a-z]+\\b/,\r\n\t\t\t\t\tend: /$/,\r\n\t\t\t\t\tkeywords: {\r\n\t\t\t\t\t\tkeyword: \"if else elif endif define undef warning error line pragma ifdef ifndef include\"\r\n\t\t\t\t\t},\r\n\t\t\t\t\tcontains: [{\r\n\t\t\t\t\t\tbegin: /\\\\\\n/,\r\n\t\t\t\t\t\trelevance: 0\r\n\t\t\t\t\t}, e.inherit(e.QUOTE_STRING_MODE, {\r\n\t\t\t\t\t\tclassName: \"string\"\r\n\t\t\t\t\t}), {\r\n\t\t\t\t\t\tclassName: \"string\",\r\n\t\t\t\t\t\tbegin: /<.*?>/,\r\n\t\t\t\t\t\tend: /$/,\r\n\t\t\t\t\t\tillegal: \"\\\\n\"\r\n\t\t\t\t\t}, e.C_LINE_COMMENT_MODE, e.C_BLOCK_COMMENT_MODE]\r\n\t\t\t\t}, {\r\n\t\t\t\t\tclassName: \"class\",\r\n\t\t\t\t\tbegin: \"(\" + t.keyword.join(\"|\") + \")\\\\b\",\r\n\t\t\t\t\tend: /(\\{|$)/,\r\n\t\t\t\t\texcludeEnd: !0,\r\n\t\t\t\t\tkeywords: t,\r\n\t\t\t\t\tcontains: [e.UNDERSCORE_TITLE_MODE]\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: \"\\\\.\" + e.UNDERSCORE_IDENT_RE,\r\n\t\t\t\t\trelevance: 0\r\n\t\t\t\t}\r\n\t\t\t]\r\n\t\t}\r\n\t},\r\n\tgrmr_perl: e => {\r\n\t\tconst n = e.regex,\r\n\t\t\tt = /[dualxmsipngr]{0,12}/,\r\n\t\t\ta = {\r\n\t\t\t\t$pattern: /[\\w.]+/,\r\n\t\t\t\tkeyword: \"abs accept alarm and atan2 bind binmode bless break caller chdir chmod chomp chop chown chr chroot close closedir connect continue cos crypt dbmclose dbmopen defined delete die do dump each else elsif endgrent endhostent endnetent endprotoent endpwent endservent eof eval exec exists exit exp fcntl fileno flock for foreach fork format formline getc getgrent getgrgid getgrnam gethostbyaddr gethostbyname gethostent getlogin getnetbyaddr getnetbyname getnetent getpeername getpgrp getpriority getprotobyname getprotobynumber getprotoent getpwent getpwnam getpwuid getservbyname getservbyport getservent getsockname getsockopt given glob gmtime goto grep gt hex if index int ioctl join keys kill last lc lcfirst length link listen local localtime log lstat lt ma map mkdir msgctl msgget msgrcv msgsnd my ne next no not oct open opendir or ord our pack package pipe pop pos print printf prototype push q|0 qq quotemeta qw qx rand read readdir readline readlink readpipe recv redo ref rename require reset return reverse rewinddir rindex rmdir say scalar seek seekdir select semctl semget semop send setgrent sethostent setnetent setpgrp setpriority setprotoent setpwent setservent setsockopt shift shmctl shmget shmread shmwrite shutdown sin sleep socket socketpair sort splice split sprintf sqrt srand stat state study sub substr symlink syscall sysopen sysread sysseek system syswrite tell telldir tie tied time times tr truncate uc ucfirst umask undef unless unlink unpack unshift untie until use utime values vec wait waitpid wantarray warn when while write x|0 xor y|0\"\r\n\t\t\t},\r\n\t\t\ti = {\r\n\t\t\t\tclassName: \"subst\",\r\n\t\t\t\tbegin: \"[$@]\\\\{\",\r\n\t\t\t\tend: \"\\\\}\",\r\n\t\t\t\tkeywords: a\r\n\t\t\t},\r\n\t\t\tr = {\r\n\t\t\t\tbegin: /->\\{/,\r\n\t\t\t\tend: /\\}/\r\n\t\t\t},\r\n\t\t\ts = {\r\n\t\t\t\tvariants: [{\r\n\t\t\t\t\tbegin: /\\$\\d/\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: n.concat(/[$%@](\\^\\w\\b|#\\w+(::\\w+)*|\\{\\w+\\}|\\w+(::\\w*)*)/, \"(?![A-Za-z])(?![@$%])\")\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: /[$%@][^\\s\\w{]/,\r\n\t\t\t\t\trelevance: 0\r\n\t\t\t\t}]\r\n\t\t\t},\r\n\t\t\to = [e.BACKSLASH_ESCAPE, i, s],\r\n\t\t\tl = [/!/, /\\//, /\\|/, /\\?/, /'/, /\"/, /#/],\r\n\t\t\tc = (e, a, i = \"\\\\1\") => {\r\n\t\t\t\tconst r = \"\\\\1\" === i ? i : n.concat(i, a);\r\n\t\t\t\treturn n.concat(n.concat(\"(?:\", e, \")\"), a, /(?:\\\\.|[^\\\\\\/])*?/, r, /(?:\\\\.|[^\\\\\\/])*?/, i, t)\r\n\t\t\t},\r\n\t\t\td = (e, a, i) => n.concat(n.concat(\"(?:\", e, \")\"), a, /(?:\\\\.|[^\\\\\\/])*?/, i, t),\r\n\t\t\tg = [s, e.HASH_COMMENT_MODE, e.COMMENT(/^=\\w/, /=cut/, {\r\n\t\t\t\tendsWithParent: !0\r\n\t\t\t}), r, {\r\n\t\t\t\tclassName: \"string\",\r\n\t\t\t\tcontains: o,\r\n\t\t\t\tvariants: [{\r\n\t\t\t\t\tbegin: \"q[qwxr]?\\\\s*\\\\(\",\r\n\t\t\t\t\tend: \"\\\\)\",\r\n\t\t\t\t\trelevance: 5\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: \"q[qwxr]?\\\\s*\\\\[\",\r\n\t\t\t\t\tend: \"\\\\]\",\r\n\t\t\t\t\trelevance: 5\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: \"q[qwxr]?\\\\s*\\\\{\",\r\n\t\t\t\t\tend: \"\\\\}\",\r\n\t\t\t\t\trelevance: 5\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: \"q[qwxr]?\\\\s*\\\\|\",\r\n\t\t\t\t\tend: \"\\\\|\",\r\n\t\t\t\t\trelevance: 5\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: \"q[qwxr]?\\\\s*<\",\r\n\t\t\t\t\tend: \">\",\r\n\t\t\t\t\trelevance: 5\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: \"qw\\\\s+q\",\r\n\t\t\t\t\tend: \"q\",\r\n\t\t\t\t\trelevance: 5\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: \"'\",\r\n\t\t\t\t\tend: \"'\",\r\n\t\t\t\t\tcontains: [e.BACKSLASH_ESCAPE]\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: '\"',\r\n\t\t\t\t\tend: '\"'\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: \"`\",\r\n\t\t\t\t\tend: \"`\",\r\n\t\t\t\t\tcontains: [e.BACKSLASH_ESCAPE]\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: /\\{\\w+\\}/,\r\n\t\t\t\t\trelevance: 0\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: \"-?\\\\w+\\\\s*=>\",\r\n\t\t\t\t\trelevance: 0\r\n\t\t\t\t}]\r\n\t\t\t}, {\r\n\t\t\t\tclassName: \"number\",\r\n\t\t\t\tbegin: \"(\\\\b0[0-7_]+)|(\\\\b0x[0-9a-fA-F_]+)|(\\\\b[1-9][0-9_]*(\\\\.[0-9_]+)?)|[0_]\\\\b\",\r\n\t\t\t\trelevance: 0\r\n\t\t\t}, {\r\n\t\t\t\tbegin: \"(\\\\/\\\\/|\" + e.RE_STARTERS_RE + \"|\\\\b(split|return|print|reverse|grep)\\\\b)\\\\s*\",\r\n\t\t\t\tkeywords: \"split return print reverse grep\",\r\n\t\t\t\trelevance: 0,\r\n\t\t\t\tcontains: [e.HASH_COMMENT_MODE, {\r\n\t\t\t\t\tclassName: \"regexp\",\r\n\t\t\t\t\tvariants: [{\r\n\t\t\t\t\t\tbegin: c(\"s|tr|y\", n.either(...l, {\r\n\t\t\t\t\t\t\tcapture: !0\r\n\t\t\t\t\t\t}))\r\n\t\t\t\t\t}, {\r\n\t\t\t\t\t\tbegin: c(\"s|tr|y\", \"\\\\(\", \"\\\\)\")\r\n\t\t\t\t\t}, {\r\n\t\t\t\t\t\tbegin: c(\"s|tr|y\", \"\\\\[\", \"\\\\]\")\r\n\t\t\t\t\t}, {\r\n\t\t\t\t\t\tbegin: c(\"s|tr|y\", \"\\\\{\", \"\\\\}\")\r\n\t\t\t\t\t}],\r\n\t\t\t\t\trelevance: 2\r\n\t\t\t\t}, {\r\n\t\t\t\t\tclassName: \"regexp\",\r\n\t\t\t\t\tvariants: [{\r\n\t\t\t\t\t\tbegin: /(m|qr)\\/\\//,\r\n\t\t\t\t\t\trelevance: 0\r\n\t\t\t\t\t}, {\r\n\t\t\t\t\t\tbegin: d(\"(?:m|qr)?\", /\\//, /\\//)\r\n\t\t\t\t\t}, {\r\n\t\t\t\t\t\tbegin: d(\"m|qr\", n.either(...l, {\r\n\t\t\t\t\t\t\tcapture: !0\r\n\t\t\t\t\t\t}), /\\1/)\r\n\t\t\t\t\t}, {\r\n\t\t\t\t\t\tbegin: d(\"m|qr\", /\\(/, /\\)/)\r\n\t\t\t\t\t}, {\r\n\t\t\t\t\t\tbegin: d(\"m|qr\", /\\[/, /\\]/)\r\n\t\t\t\t\t}, {\r\n\t\t\t\t\t\tbegin: d(\"m|qr\", /\\{/, /\\}/)\r\n\t\t\t\t\t}]\r\n\t\t\t\t}]\r\n\t\t\t}, {\r\n\t\t\t\tclassName: \"function\",\r\n\t\t\t\tbeginKeywords: \"sub\",\r\n\t\t\t\tend: \"(\\\\s*\\\\(.*?\\\\))?[;{]\",\r\n\t\t\t\texcludeEnd: !0,\r\n\t\t\t\trelevance: 5,\r\n\t\t\t\tcontains: [e.TITLE_MODE]\r\n\t\t\t}, {\r\n\t\t\t\tbegin: \"-\\\\w\\\\b\",\r\n\t\t\t\trelevance: 0\r\n\t\t\t}, {\r\n\t\t\t\tbegin: \"^__DATA__$\",\r\n\t\t\t\tend: \"^__END__$\",\r\n\t\t\t\tsubLanguage: \"mojolicious\",\r\n\t\t\t\tcontains: [{\r\n\t\t\t\t\tbegin: \"^@@.*\",\r\n\t\t\t\t\tend: \"$\",\r\n\t\t\t\t\tclassName: \"comment\"\r\n\t\t\t\t}]\r\n\t\t\t}];\r\n\t\treturn i.contains = g, r.contains = g, {\r\n\t\t\tname: \"Perl\",\r\n\t\t\taliases: [\"pl\", \"pm\"],\r\n\t\t\tkeywords: a,\r\n\t\t\tcontains: g\r\n\t\t}\r\n\t},\r\n\tgrmr_php: e => {\r\n\t\tconst n = e.regex,\r\n\t\t\tt = /(?![A-Za-z0-9])(?![$])/,\r\n\t\t\ta = n.concat(/[a-zA-Z_\\x7f-\\xff][a-zA-Z0-9_\\x7f-\\xff]*/, t),\r\n\t\t\ti = n.concat(/(\\\\?[A-Z][a-z0-9_\\x7f-\\xff]+|\\\\?[A-Z]+(?=[A-Z][a-z0-9_\\x7f-\\xff])){1,}/, t),\r\n\t\t\tr = {\r\n\t\t\t\tscope: \"variable\",\r\n\t\t\t\tmatch: \"\\\\$+\" + a\r\n\t\t\t},\r\n\t\t\ts = {\r\n\t\t\t\tscope: \"subst\",\r\n\t\t\t\tvariants: [{\r\n\t\t\t\t\tbegin: /\\$\\w+/\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: /\\{\\$/,\r\n\t\t\t\t\tend: /\\}/\r\n\t\t\t\t}]\r\n\t\t\t},\r\n\t\t\to = e.inherit(e.APOS_STRING_MODE, {\r\n\t\t\t\tillegal: null\r\n\t\t\t}),\r\n\t\t\tl = \"[ \\t\\n]\",\r\n\t\t\tc = {\r\n\t\t\t\tscope: \"string\",\r\n\t\t\t\tvariants: [e.inherit(e.QUOTE_STRING_MODE, {\r\n\t\t\t\t\tillegal: null,\r\n\t\t\t\t\tcontains: e.QUOTE_STRING_MODE.contains.concat(s)\r\n\t\t\t\t}), o, e.END_SAME_AS_BEGIN({\r\n\t\t\t\t\tbegin: /<<<[ \\t]*(\\w+)\\n/,\r\n\t\t\t\t\tend: /[ \\t]*(\\w+)\\b/,\r\n\t\t\t\t\tcontains: e.QUOTE_STRING_MODE.contains.concat(s)\r\n\t\t\t\t})]\r\n\t\t\t},\r\n\t\t\td = {\r\n\t\t\t\tscope: \"number\",\r\n\t\t\t\tvariants: [{\r\n\t\t\t\t\tbegin: \"\\\\b0[bB][01]+(?:_[01]+)*\\\\b\"\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: \"\\\\b0[oO][0-7]+(?:_[0-7]+)*\\\\b\"\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: \"\\\\b0[xX][\\\\da-fA-F]+(?:_[\\\\da-fA-F]+)*\\\\b\"\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: \"(?:\\\\b\\\\d+(?:_\\\\d+)*(\\\\.(?:\\\\d+(?:_\\\\d+)*))?|\\\\B\\\\.\\\\d+)(?:[eE][+-]?\\\\d+)?\"\r\n\t\t\t\t}],\r\n\t\t\t\trelevance: 0\r\n\t\t\t},\r\n\t\t\tg = [\"false\", \"null\", \"true\"],\r\n\t\t\tu = [\"__CLASS__\", \"__DIR__\", \"__FILE__\", \"__FUNCTION__\", \"__COMPILER_HALT_OFFSET__\", \"__LINE__\",\r\n\t\t\t\t\"__METHOD__\", \"__NAMESPACE__\", \"__TRAIT__\", \"die\", \"echo\", \"exit\", \"include\", \"include_once\", \"print\",\r\n\t\t\t\t\"require\", \"require_once\", \"array\", \"abstract\", \"and\", \"as\", \"binary\", \"bool\", \"boolean\", \"break\",\r\n\t\t\t\t\"callable\", \"case\", \"catch\", \"class\", \"clone\", \"const\", \"continue\", \"declare\", \"default\", \"do\", \"double\",\r\n\t\t\t\t\"else\", \"elseif\", \"empty\", \"enddeclare\", \"endfor\", \"endforeach\", \"endif\", \"endswitch\", \"endwhile\", \"enum\",\r\n\t\t\t\t\"eval\", \"extends\", \"final\", \"finally\", \"float\", \"for\", \"foreach\", \"from\", \"global\", \"goto\", \"if\",\r\n\t\t\t\t\"implements\", \"instanceof\", \"insteadof\", \"int\", \"integer\", \"interface\", \"isset\", \"iterable\", \"list\",\r\n\t\t\t\t\"match|0\", \"mixed\", \"new\", \"never\", \"object\", \"or\", \"private\", \"protected\", \"public\", \"readonly\", \"real\",\r\n\t\t\t\t\"return\", \"string\", \"switch\", \"throw\", \"trait\", \"try\", \"unset\", \"use\", \"var\", \"void\", \"while\", \"xor\",\r\n\t\t\t\t\"yield\"\r\n\t\t\t],\r\n\t\t\tb = [\"Error|0\", \"AppendIterator\", \"ArgumentCountError\", \"ArithmeticError\", \"ArrayIterator\", \"ArrayObject\",\r\n\t\t\t\t\"AssertionError\", \"BadFunctionCallException\", \"BadMethodCallException\", \"CachingIterator\",\r\n\t\t\t\t\"CallbackFilterIterator\", \"CompileError\", \"Countable\", \"DirectoryIterator\", \"DivisionByZeroError\",\r\n\t\t\t\t\"DomainException\", \"EmptyIterator\", \"ErrorException\", \"Exception\", \"FilesystemIterator\", \"FilterIterator\",\r\n\t\t\t\t\"GlobIterator\", \"InfiniteIterator\", \"InvalidArgumentException\", \"IteratorIterator\", \"LengthException\",\r\n\t\t\t\t\"LimitIterator\", \"LogicException\", \"MultipleIterator\", \"NoRewindIterator\", \"OutOfBoundsException\",\r\n\t\t\t\t\"OutOfRangeException\", \"OuterIterator\", \"OverflowException\", \"ParentIterator\", \"ParseError\",\r\n\t\t\t\t\"RangeException\", \"RecursiveArrayIterator\", \"RecursiveCachingIterator\", \"RecursiveCallbackFilterIterator\",\r\n\t\t\t\t\"RecursiveDirectoryIterator\", \"RecursiveFilterIterator\", \"RecursiveIterator\", \"RecursiveIteratorIterator\",\r\n\t\t\t\t\"RecursiveRegexIterator\", \"RecursiveTreeIterator\", \"RegexIterator\", \"RuntimeException\",\r\n\t\t\t\t\"SeekableIterator\", \"SplDoublyLinkedList\", \"SplFileInfo\", \"SplFileObject\", \"SplFixedArray\", \"SplHeap\",\r\n\t\t\t\t\"SplMaxHeap\", \"SplMinHeap\", \"SplObjectStorage\", \"SplObserver\", \"SplPriorityQueue\", \"SplQueue\", \"SplStack\",\r\n\t\t\t\t\"SplSubject\", \"SplTempFileObject\", \"TypeError\", \"UnderflowException\", \"UnexpectedValueException\",\r\n\t\t\t\t\"UnhandledMatchError\", \"ArrayAccess\", \"BackedEnum\", \"Closure\", \"Fiber\", \"Generator\", \"Iterator\",\r\n\t\t\t\t\"IteratorAggregate\", \"Serializable\", \"Stringable\", \"Throwable\", \"Traversable\", \"UnitEnum\",\r\n\t\t\t\t\"WeakReference\", \"WeakMap\", \"Directory\", \"__PHP_Incomplete_Class\", \"parent\", \"php_user_filter\", \"self\",\r\n\t\t\t\t\"static\", \"stdClass\"\r\n\t\t\t],\r\n\t\t\tm = {\r\n\t\t\t\tkeyword: u,\r\n\t\t\t\tliteral: (e => {\r\n\t\t\t\t\tconst n = [];\r\n\t\t\t\t\treturn e.forEach((e => {\r\n\t\t\t\t\t\tn.push(e), e.toLowerCase() === e ? n.push(e.toUpperCase()) : n.push(e.toLowerCase())\r\n\t\t\t\t\t})), n\r\n\t\t\t\t})(g),\r\n\t\t\t\tbuilt_in: b\r\n\t\t\t},\r\n\t\t\tp = e => e.map((e => e.replace(/\\|\\d+$/, \"\"))),\r\n\t\t\t_ = {\r\n\t\t\t\tvariants: [{\r\n\t\t\t\t\tmatch: [/new/, n.concat(l, \"+\"), n.concat(\"(?!\", p(b).join(\"\\\\b|\"), \"\\\\b)\"), i],\r\n\t\t\t\t\tscope: {\r\n\t\t\t\t\t\t1: \"keyword\",\r\n\t\t\t\t\t\t4: \"title.class\"\r\n\t\t\t\t\t}\r\n\t\t\t\t}]\r\n\t\t\t},\r\n\t\t\th = n.concat(a, \"\\\\b(?!\\\\()\"),\r\n\t\t\tf = {\r\n\t\t\t\tvariants: [{\r\n\t\t\t\t\tmatch: [n.concat(/::/, n.lookahead(/(?!class\\b)/)), h],\r\n\t\t\t\t\tscope: {\r\n\t\t\t\t\t\t2: \"variable.constant\"\r\n\t\t\t\t\t}\r\n\t\t\t\t}, {\r\n\t\t\t\t\tmatch: [/::/, /class/],\r\n\t\t\t\t\tscope: {\r\n\t\t\t\t\t\t2: \"variable.language\"\r\n\t\t\t\t\t}\r\n\t\t\t\t}, {\r\n\t\t\t\t\tmatch: [i, n.concat(/::/, n.lookahead(/(?!class\\b)/)), h],\r\n\t\t\t\t\tscope: {\r\n\t\t\t\t\t\t1: \"title.class\",\r\n\t\t\t\t\t\t3: \"variable.constant\"\r\n\t\t\t\t\t}\r\n\t\t\t\t}, {\r\n\t\t\t\t\tmatch: [i, n.concat(\"::\", n.lookahead(/(?!class\\b)/))],\r\n\t\t\t\t\tscope: {\r\n\t\t\t\t\t\t1: \"title.class\"\r\n\t\t\t\t\t}\r\n\t\t\t\t}, {\r\n\t\t\t\t\tmatch: [i, /::/, /class/],\r\n\t\t\t\t\tscope: {\r\n\t\t\t\t\t\t1: \"title.class\",\r\n\t\t\t\t\t\t3: \"variable.language\"\r\n\t\t\t\t\t}\r\n\t\t\t\t}]\r\n\t\t\t},\r\n\t\t\tE = {\r\n\t\t\t\tscope: \"attr\",\r\n\t\t\t\tmatch: n.concat(a, n.lookahead(\":\"), n.lookahead(/(?!::)/))\r\n\t\t\t},\r\n\t\t\ty = {\r\n\t\t\t\trelevance: 0,\r\n\t\t\t\tbegin: /\\(/,\r\n\t\t\t\tend: /\\)/,\r\n\t\t\t\tkeywords: m,\r\n\t\t\t\tcontains: [E, r, f, e.C_BLOCK_COMMENT_MODE, c, d, _]\r\n\t\t\t},\r\n\t\t\tw = {\r\n\t\t\t\trelevance: 0,\r\n\t\t\t\tmatch: [/\\b/, n.concat(\"(?!fn\\\\b|function\\\\b|\", p(u).join(\"\\\\b|\"), \"|\", p(b).join(\"\\\\b|\"), \"\\\\b)\"), a, n\r\n\t\t\t\t\t.concat(l, \"*\"), n.lookahead(/(?=\\()/)\r\n\t\t\t\t],\r\n\t\t\t\tscope: {\r\n\t\t\t\t\t3: \"title.function.invoke\"\r\n\t\t\t\t},\r\n\t\t\t\tcontains: [y]\r\n\t\t\t};\r\n\t\ty.contains.push(w);\r\n\t\tconst N = [E, f, e.C_BLOCK_COMMENT_MODE, c, d, _];\r\n\t\treturn {\r\n\t\t\tcase_insensitive: !1,\r\n\t\t\tkeywords: m,\r\n\t\t\tcontains: [{\r\n\t\t\t\tbegin: n.concat(/#\\[\\s*/, i),\r\n\t\t\t\tbeginScope: \"meta\",\r\n\t\t\t\tend: /]/,\r\n\t\t\t\tendScope: \"meta\",\r\n\t\t\t\tkeywords: {\r\n\t\t\t\t\tliteral: g,\r\n\t\t\t\t\tkeyword: [\"new\", \"array\"]\r\n\t\t\t\t},\r\n\t\t\t\tcontains: [{\r\n\t\t\t\t\tbegin: /\\[/,\r\n\t\t\t\t\tend: /]/,\r\n\t\t\t\t\tkeywords: {\r\n\t\t\t\t\t\tliteral: g,\r\n\t\t\t\t\t\tkeyword: [\"new\", \"array\"]\r\n\t\t\t\t\t},\r\n\t\t\t\t\tcontains: [\"self\", ...N]\r\n\t\t\t\t}, ...N, {\r\n\t\t\t\t\tscope: \"meta\",\r\n\t\t\t\t\tmatch: i\r\n\t\t\t\t}]\r\n\t\t\t}, e.HASH_COMMENT_MODE, e.COMMENT(\"//\", \"$\"), e.COMMENT(\"/\\\\*\", \"\\\\*/\", {\r\n\t\t\t\tcontains: [{\r\n\t\t\t\t\tscope: \"doctag\",\r\n\t\t\t\t\tmatch: \"@[A-Za-z]+\"\r\n\t\t\t\t}]\r\n\t\t\t}), {\r\n\t\t\t\tmatch: /__halt_compiler\\(\\);/,\r\n\t\t\t\tkeywords: \"__halt_compiler\",\r\n\t\t\t\tstarts: {\r\n\t\t\t\t\tscope: \"comment\",\r\n\t\t\t\t\tend: e.MATCH_NOTHING_RE,\r\n\t\t\t\t\tcontains: [{\r\n\t\t\t\t\t\tmatch: /\\?>/,\r\n\t\t\t\t\t\tscope: \"meta\",\r\n\t\t\t\t\t\tendsParent: !0\r\n\t\t\t\t\t}]\r\n\t\t\t\t}\r\n\t\t\t}, {\r\n\t\t\t\tscope: \"meta\",\r\n\t\t\t\tvariants: [{\r\n\t\t\t\t\tbegin: /<\\?php/,\r\n\t\t\t\t\trelevance: 10\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: /<\\?=/\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: /<\\?/,\r\n\t\t\t\t\trelevance: .1\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: /\\?>/\r\n\t\t\t\t}]\r\n\t\t\t}, {\r\n\t\t\t\tscope: \"variable.language\",\r\n\t\t\t\tmatch: /\\$this\\b/\r\n\t\t\t}, r, w, f, {\r\n\t\t\t\tmatch: [/const/, /\\s/, a],\r\n\t\t\t\tscope: {\r\n\t\t\t\t\t1: \"keyword\",\r\n\t\t\t\t\t3: \"variable.constant\"\r\n\t\t\t\t}\r\n\t\t\t}, _, {\r\n\t\t\t\tscope: \"function\",\r\n\t\t\t\trelevance: 0,\r\n\t\t\t\tbeginKeywords: \"fn function\",\r\n\t\t\t\tend: /[;{]/,\r\n\t\t\t\texcludeEnd: !0,\r\n\t\t\t\tillegal: \"[$%\\\\[]\",\r\n\t\t\t\tcontains: [{\r\n\t\t\t\t\tbeginKeywords: \"use\"\r\n\t\t\t\t}, e.UNDERSCORE_TITLE_MODE, {\r\n\t\t\t\t\tbegin: \"=>\",\r\n\t\t\t\t\tendsParent: !0\r\n\t\t\t\t}, {\r\n\t\t\t\t\tscope: \"params\",\r\n\t\t\t\t\tbegin: \"\\\\(\",\r\n\t\t\t\t\tend: \"\\\\)\",\r\n\t\t\t\t\texcludeBegin: !0,\r\n\t\t\t\t\texcludeEnd: !0,\r\n\t\t\t\t\tkeywords: m,\r\n\t\t\t\t\tcontains: [\"self\", r, f, e.C_BLOCK_COMMENT_MODE, c, d]\r\n\t\t\t\t}]\r\n\t\t\t}, {\r\n\t\t\t\tscope: \"class\",\r\n\t\t\t\tvariants: [{\r\n\t\t\t\t\tbeginKeywords: \"enum\",\r\n\t\t\t\t\tillegal: /[($\"]/\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbeginKeywords: \"class interface trait\",\r\n\t\t\t\t\tillegal: /[:($\"]/\r\n\t\t\t\t}],\r\n\t\t\t\trelevance: 0,\r\n\t\t\t\tend: /\\{/,\r\n\t\t\t\texcludeEnd: !0,\r\n\t\t\t\tcontains: [{\r\n\t\t\t\t\tbeginKeywords: \"extends implements\"\r\n\t\t\t\t}, e.UNDERSCORE_TITLE_MODE]\r\n\t\t\t}, {\r\n\t\t\t\tbeginKeywords: \"namespace\",\r\n\t\t\t\trelevance: 0,\r\n\t\t\t\tend: \";\",\r\n\t\t\t\tillegal: /[.']/,\r\n\t\t\t\tcontains: [e.inherit(e.UNDERSCORE_TITLE_MODE, {\r\n\t\t\t\t\tscope: \"title.class\"\r\n\t\t\t\t})]\r\n\t\t\t}, {\r\n\t\t\t\tbeginKeywords: \"use\",\r\n\t\t\t\trelevance: 0,\r\n\t\t\t\tend: \";\",\r\n\t\t\t\tcontains: [{\r\n\t\t\t\t\tmatch: /\\b(as|const|function)\\b/,\r\n\t\t\t\t\tscope: \"keyword\"\r\n\t\t\t\t}, e.UNDERSCORE_TITLE_MODE]\r\n\t\t\t}, c, d]\r\n\t\t}\r\n\t},\r\n\tgrmr_php_template: e => ({\r\n\t\tname: \"PHP template\",\r\n\t\tsubLanguage: \"xml\",\r\n\t\tcontains: [{\r\n\t\t\tbegin: /<\\?(php|=)?/,\r\n\t\t\tend: /\\?>/,\r\n\t\t\tsubLanguage: \"php\",\r\n\t\t\tcontains: [{\r\n\t\t\t\tbegin: \"/\\\\*\",\r\n\t\t\t\tend: \"\\\\*/\",\r\n\t\t\t\tskip: !0\r\n\t\t\t}, {\r\n\t\t\t\tbegin: 'b\"',\r\n\t\t\t\tend: '\"',\r\n\t\t\t\tskip: !0\r\n\t\t\t}, {\r\n\t\t\t\tbegin: \"b'\",\r\n\t\t\t\tend: \"'\",\r\n\t\t\t\tskip: !0\r\n\t\t\t}, e.inherit(e.APOS_STRING_MODE, {\r\n\t\t\t\tillegal: null,\r\n\t\t\t\tclassName: null,\r\n\t\t\t\tcontains: null,\r\n\t\t\t\tskip: !0\r\n\t\t\t}), e.inherit(e.QUOTE_STRING_MODE, {\r\n\t\t\t\tillegal: null,\r\n\t\t\t\tclassName: null,\r\n\t\t\t\tcontains: null,\r\n\t\t\t\tskip: !0\r\n\t\t\t})]\r\n\t\t}]\r\n\t}),\r\n\tgrmr_plaintext: e => ({\r\n\t\tname: \"Plain text\",\r\n\t\taliases: [\"text\", \"txt\"],\r\n\t\tdisableAutodetect: !0\r\n\t}),\r\n\tgrmr_python: e => {\r\n\t\tconst n = e.regex,\r\n\t\t\tt = /(?:[A-Z_a-z\\xAA\\xB5\\xBA\\xC0-\\xD6\\xD8-\\xF6\\xF8-\\u02C1\\u02C6-\\u02D1\\u02E0-\\u02E4\\u02EC\\u02EE\\u0370-\\u0374\\u0376\\u0377\\u037B-\\u037D\\u037F\\u0386\\u0388-\\u038A\\u038C\\u038E-\\u03A1\\u03A3-\\u03F5\\u03F7-\\u0481\\u048A-\\u052F\\u0531-\\u0556\\u0559\\u0560-\\u0588\\u05D0-\\u05EA\\u05EF-\\u05F2\\u0620-\\u064A\\u066E\\u066F\\u0671-\\u06D3\\u06D5\\u06E5\\u06E6\\u06EE\\u06EF\\u06FA-\\u06FC\\u06FF\\u0710\\u0712-\\u072F\\u074D-\\u07A5\\u07B1\\u07CA-\\u07EA\\u07F4\\u07F5\\u07FA\\u0800-\\u0815\\u081A\\u0824\\u0828\\u0840-\\u0858\\u0860-\\u086A\\u0870-\\u0887\\u0889-\\u088E\\u08A0-\\u08C9\\u0904-\\u0939\\u093D\\u0950\\u0958-\\u0961\\u0971-\\u0980\\u0985-\\u098C\\u098F\\u0990\\u0993-\\u09A8\\u09AA-\\u09B0\\u09B2\\u09B6-\\u09B9\\u09BD\\u09CE\\u09DC\\u09DD\\u09DF-\\u09E1\\u09F0\\u09F1\\u09FC\\u0A05-\\u0A0A\\u0A0F\\u0A10\\u0A13-\\u0A28\\u0A2A-\\u0A30\\u0A32\\u0A33\\u0A35\\u0A36\\u0A38\\u0A39\\u0A59-\\u0A5C\\u0A5E\\u0A72-\\u0A74\\u0A85-\\u0A8D\\u0A8F-\\u0A91\\u0A93-\\u0AA8\\u0AAA-\\u0AB0\\u0AB2\\u0AB3\\u0AB5-\\u0AB9\\u0ABD\\u0AD0\\u0AE0\\u0AE1\\u0AF9\\u0B05-\\u0B0C\\u0B0F\\u0B10\\u0B13-\\u0B28\\u0B2A-\\u0B30\\u0B32\\u0B33\\u0B35-\\u0B39\\u0B3D\\u0B5C\\u0B5D\\u0B5F-\\u0B61\\u0B71\\u0B83\\u0B85-\\u0B8A\\u0B8E-\\u0B90\\u0B92-\\u0B95\\u0B99\\u0B9A\\u0B9C\\u0B9E\\u0B9F\\u0BA3\\u0BA4\\u0BA8-\\u0BAA\\u0BAE-\\u0BB9\\u0BD0\\u0C05-\\u0C0C\\u0C0E-\\u0C10\\u0C12-\\u0C28\\u0C2A-\\u0C39\\u0C3D\\u0C58-\\u0C5A\\u0C5D\\u0C60\\u0C61\\u0C80\\u0C85-\\u0C8C\\u0C8E-\\u0C90\\u0C92-\\u0CA8\\u0CAA-\\u0CB3\\u0CB5-\\u0CB9\\u0CBD\\u0CDD\\u0CDE\\u0CE0\\u0CE1\\u0CF1\\u0CF2\\u0D04-\\u0D0C\\u0D0E-\\u0D10\\u0D12-\\u0D3A\\u0D3D\\u0D4E\\u0D54-\\u0D56\\u0D5F-\\u0D61\\u0D7A-\\u0D7F\\u0D85-\\u0D96\\u0D9A-\\u0DB1\\u0DB3-\\u0DBB\\u0DBD\\u0DC0-\\u0DC6\\u0E01-\\u0E30\\u0E32\\u0E40-\\u0E46\\u0E81\\u0E82\\u0E84\\u0E86-\\u0E8A\\u0E8C-\\u0EA3\\u0EA5\\u0EA7-\\u0EB0\\u0EB2\\u0EBD\\u0EC0-\\u0EC4\\u0EC6\\u0EDC-\\u0EDF\\u0F00\\u0F40-\\u0F47\\u0F49-\\u0F6C\\u0F88-\\u0F8C\\u1000-\\u102A\\u103F\\u1050-\\u1055\\u105A-\\u105D\\u1061\\u1065\\u1066\\u106E-\\u1070\\u1075-\\u1081\\u108E\\u10A0-\\u10C5\\u10C7\\u10CD\\u10D0-\\u10FA\\u10FC-\\u1248\\u124A-\\u124D\\u1250-\\u1256\\u1258\\u125A-\\u125D\\u1260-\\u1288\\u128A-\\u128D\\u1290-\\u12B0\\u12B2-\\u12B5\\u12B8-\\u12BE\\u12C0\\u12C2-\\u12C5\\u12C8-\\u12D6\\u12D8-\\u1310\\u1312-\\u1315\\u1318-\\u135A\\u1380-\\u138F\\u13A0-\\u13F5\\u13F8-\\u13FD\\u1401-\\u166C\\u166F-\\u167F\\u1681-\\u169A\\u16A0-\\u16EA\\u16EE-\\u16F8\\u1700-\\u1711\\u171F-\\u1731\\u1740-\\u1751\\u1760-\\u176C\\u176E-\\u1770\\u1780-\\u17B3\\u17D7\\u17DC\\u1820-\\u1878\\u1880-\\u18A8\\u18AA\\u18B0-\\u18F5\\u1900-\\u191E\\u1950-\\u196D\\u1970-\\u1974\\u1980-\\u19AB\\u19B0-\\u19C9\\u1A00-\\u1A16\\u1A20-\\u1A54\\u1AA7\\u1B05-\\u1B33\\u1B45-\\u1B4C\\u1B83-\\u1BA0\\u1BAE\\u1BAF\\u1BBA-\\u1BE5\\u1C00-\\u1C23\\u1C4D-\\u1C4F\\u1C5A-\\u1C7D\\u1C80-\\u1C88\\u1C90-\\u1CBA\\u1CBD-\\u1CBF\\u1CE9-\\u1CEC\\u1CEE-\\u1CF3\\u1CF5\\u1CF6\\u1CFA\\u1D00-\\u1DBF\\u1E00-\\u1F15\\u1F18-\\u1F1D\\u1F20-\\u1F45\\u1F48-\\u1F4D\\u1F50-\\u1F57\\u1F59\\u1F5B\\u1F5D\\u1F5F-\\u1F7D\\u1F80-\\u1FB4\\u1FB6-\\u1FBC\\u1FBE\\u1FC2-\\u1FC4\\u1FC6-\\u1FCC\\u1FD0-\\u1FD3\\u1FD6-\\u1FDB\\u1FE0-\\u1FEC\\u1FF2-\\u1FF4\\u1FF6-\\u1FFC\\u2071\\u207F\\u2090-\\u209C\\u2102\\u2107\\u210A-\\u2113\\u2115\\u2118-\\u211D\\u2124\\u2126\\u2128\\u212A-\\u2139\\u213C-\\u213F\\u2145-\\u2149\\u214E\\u2160-\\u2188\\u2C00-\\u2CE4\\u2CEB-\\u2CEE\\u2CF2\\u2CF3\\u2D00-\\u2D25\\u2D27\\u2D2D\\u2D30-\\u2D67\\u2D6F\\u2D80-\\u2D96\\u2DA0-\\u2DA6\\u2DA8-\\u2DAE\\u2DB0-\\u2DB6\\u2DB8-\\u2DBE\\u2DC0-\\u2DC6\\u2DC8-\\u2DCE\\u2DD0-\\u2DD6\\u2DD8-\\u2DDE\\u3005-\\u3007\\u3021-\\u3029\\u3031-\\u3035\\u3038-\\u303C\\u3041-\\u3096\\u309D-\\u309F\\u30A1-\\u30FA\\u30FC-\\u30FF\\u3105-\\u312F\\u3131-\\u318E\\u31A0-\\u31BF\\u31F0-\\u31FF\\u3400-\\u4DBF\\u4E00-\\uA48C\\uA4D0-\\uA4FD\\uA500-\\uA60C\\uA610-\\uA61F\\uA62A\\uA62B\\uA640-\\uA66E\\uA67F-\\uA69D\\uA6A0-\\uA6EF\\uA717-\\uA71F\\uA722-\\uA788\\uA78B-\\uA7CA\\uA7D0\\uA7D1\\uA7D3\\uA7D5-\\uA7D9\\uA7F2-\\uA801\\uA803-\\uA805\\uA807-\\uA80A\\uA80C-\\uA822\\uA840-\\uA873\\uA882-\\uA8B3\\uA8F2-\\uA8F7\\uA8FB\\uA8FD\\uA8FE\\uA90A-\\uA925\\uA930-\\uA946\\uA960-\\uA97C\\uA984-\\uA9B2\\uA9CF\\uA9E0-\\uA9E4\\uA9E6-\\uA9EF\\uA9FA-\\uA9FE\\uAA00-\\uAA28\\uAA40-\\uAA42\\uAA44-\\uAA4B\\uAA60-\\uAA76\\uAA7A\\uAA7E-\\uAAAF\\uAAB1\\uAAB5\\uAAB6\\uAAB9-\\uAABD\\uAAC0\\uAAC2\\uAADB-\\uAADD\\uAAE0-\\uAAEA\\uAAF2-\\uAAF4\\uAB01-\\uAB06\\uAB09-\\uAB0E\\uAB11-\\uAB16\\uAB20-\\uAB26\\uAB28-\\uAB2E\\uAB30-\\uAB5A\\uAB5C-\\uAB69\\uAB70-\\uABE2\\uAC00-\\uD7A3\\uD7B0-\\uD7C6\\uD7CB-\\uD7FB\\uF900-\\uFA6D\\uFA70-\\uFAD9\\uFB00-\\uFB06\\uFB13-\\uFB17\\uFB1D\\uFB1F-\\uFB28\\uFB2A-\\uFB36\\uFB38-\\uFB3C\\uFB3E\\uFB40\\uFB41\\uFB43\\uFB44\\uFB46-\\uFBB1\\uFBD3-\\uFC5D\\uFC64-\\uFD3D\\uFD50-\\uFD8F\\uFD92-\\uFDC7\\uFDF0-\\uFDF9\\uFE71\\uFE73\\uFE77\\uFE79\\uFE7B\\uFE7D\\uFE7F-\\uFEFC\\uFF21-\\uFF3A\\uFF41-\\uFF5A\\uFF66-\\uFF9D\\uFFA0-\\uFFBE\\uFFC2-\\uFFC7\\uFFCA-\\uFFCF\\uFFD2-\\uFFD7\\uFFDA-\\uFFDC]|\\uD800[\\uDC00-\\uDC0B\\uDC0D-\\uDC26\\uDC28-\\uDC3A\\uDC3C\\uDC3D\\uDC3F-\\uDC4D\\uDC50-\\uDC5D\\uDC80-\\uDCFA\\uDD40-\\uDD74\\uDE80-\\uDE9C\\uDEA0-\\uDED0\\uDF00-\\uDF1F\\uDF2D-\\uDF4A\\uDF50-\\uDF75\\uDF80-\\uDF9D\\uDFA0-\\uDFC3\\uDFC8-\\uDFCF\\uDFD1-\\uDFD5]|\\uD801[\\uDC00-\\uDC9D\\uDCB0-\\uDCD3\\uDCD8-\\uDCFB\\uDD00-\\uDD27\\uDD30-\\uDD63\\uDD70-\\uDD7A\\uDD7C-\\uDD8A\\uDD8C-\\uDD92\\uDD94\\uDD95\\uDD97-\\uDDA1\\uDDA3-\\uDDB1\\uDDB3-\\uDDB9\\uDDBB\\uDDBC\\uDE00-\\uDF36\\uDF40-\\uDF55\\uDF60-\\uDF67\\uDF80-\\uDF85\\uDF87-\\uDFB0\\uDFB2-\\uDFBA]|\\uD802[\\uDC00-\\uDC05\\uDC08\\uDC0A-\\uDC35\\uDC37\\uDC38\\uDC3C\\uDC3F-\\uDC55\\uDC60-\\uDC76\\uDC80-\\uDC9E\\uDCE0-\\uDCF2\\uDCF4\\uDCF5\\uDD00-\\uDD15\\uDD20-\\uDD39\\uDD80-\\uDDB7\\uDDBE\\uDDBF\\uDE00\\uDE10-\\uDE13\\uDE15-\\uDE17\\uDE19-\\uDE35\\uDE60-\\uDE7C\\uDE80-\\uDE9C\\uDEC0-\\uDEC7\\uDEC9-\\uDEE4\\uDF00-\\uDF35\\uDF40-\\uDF55\\uDF60-\\uDF72\\uDF80-\\uDF91]|\\uD803[\\uDC00-\\uDC48\\uDC80-\\uDCB2\\uDCC0-\\uDCF2\\uDD00-\\uDD23\\uDE80-\\uDEA9\\uDEB0\\uDEB1\\uDF00-\\uDF1C\\uDF27\\uDF30-\\uDF45\\uDF70-\\uDF81\\uDFB0-\\uDFC4\\uDFE0-\\uDFF6]|\\uD804[\\uDC03-\\uDC37\\uDC71\\uDC72\\uDC75\\uDC83-\\uDCAF\\uDCD0-\\uDCE8\\uDD03-\\uDD26\\uDD44\\uDD47\\uDD50-\\uDD72\\uDD76\\uDD83-\\uDDB2\\uDDC1-\\uDDC4\\uDDDA\\uDDDC\\uDE00-\\uDE11\\uDE13-\\uDE2B\\uDE3F\\uDE40\\uDE80-\\uDE86\\uDE88\\uDE8A-\\uDE8D\\uDE8F-\\uDE9D\\uDE9F-\\uDEA8\\uDEB0-\\uDEDE\\uDF05-\\uDF0C\\uDF0F\\uDF10\\uDF13-\\uDF28\\uDF2A-\\uDF30\\uDF32\\uDF33\\uDF35-\\uDF39\\uDF3D\\uDF50\\uDF5D-\\uDF61]|\\uD805[\\uDC00-\\uDC34\\uDC47-\\uDC4A\\uDC5F-\\uDC61\\uDC80-\\uDCAF\\uDCC4\\uDCC5\\uDCC7\\uDD80-\\uDDAE\\uDDD8-\\uDDDB\\uDE00-\\uDE2F\\uDE44\\uDE80-\\uDEAA\\uDEB8\\uDF00-\\uDF1A\\uDF40-\\uDF46]|\\uD806[\\uDC00-\\uDC2B\\uDCA0-\\uDCDF\\uDCFF-\\uDD06\\uDD09\\uDD0C-\\uDD13\\uDD15\\uDD16\\uDD18-\\uDD2F\\uDD3F\\uDD41\\uDDA0-\\uDDA7\\uDDAA-\\uDDD0\\uDDE1\\uDDE3\\uDE00\\uDE0B-\\uDE32\\uDE3A\\uDE50\\uDE5C-\\uDE89\\uDE9D\\uDEB0-\\uDEF8]|\\uD807[\\uDC00-\\uDC08\\uDC0A-\\uDC2E\\uDC40\\uDC72-\\uDC8F\\uDD00-\\uDD06\\uDD08\\uDD09\\uDD0B-\\uDD30\\uDD46\\uDD60-\\uDD65\\uDD67\\uDD68\\uDD6A-\\uDD89\\uDD98\\uDEE0-\\uDEF2\\uDF02\\uDF04-\\uDF10\\uDF12-\\uDF33\\uDFB0]|\\uD808[\\uDC00-\\uDF99]|\\uD809[\\uDC00-\\uDC6E\\uDC80-\\uDD43]|\\uD80B[\\uDF90-\\uDFF0]|[\\uD80C\\uD81C-\\uD820\\uD822\\uD840-\\uD868\\uD86A-\\uD86C\\uD86F-\\uD872\\uD874-\\uD879\\uD880-\\uD883\\uD885-\\uD887][\\uDC00-\\uDFFF]|\\uD80D[\\uDC00-\\uDC2F\\uDC41-\\uDC46]|\\uD811[\\uDC00-\\uDE46]|\\uD81A[\\uDC00-\\uDE38\\uDE40-\\uDE5E\\uDE70-\\uDEBE\\uDED0-\\uDEED\\uDF00-\\uDF2F\\uDF40-\\uDF43\\uDF63-\\uDF77\\uDF7D-\\uDF8F]|\\uD81B[\\uDE40-\\uDE7F\\uDF00-\\uDF4A\\uDF50\\uDF93-\\uDF9F\\uDFE0\\uDFE1\\uDFE3]|\\uD821[\\uDC00-\\uDFF7]|\\uD823[\\uDC00-\\uDCD5\\uDD00-\\uDD08]|\\uD82B[\\uDFF0-\\uDFF3\\uDFF5-\\uDFFB\\uDFFD\\uDFFE]|\\uD82C[\\uDC00-\\uDD22\\uDD32\\uDD50-\\uDD52\\uDD55\\uDD64-\\uDD67\\uDD70-\\uDEFB]|\\uD82F[\\uDC00-\\uDC6A\\uDC70-\\uDC7C\\uDC80-\\uDC88\\uDC90-\\uDC99]|\\uD835[\\uDC00-\\uDC54\\uDC56-\\uDC9C\\uDC9E\\uDC9F\\uDCA2\\uDCA5\\uDCA6\\uDCA9-\\uDCAC\\uDCAE-\\uDCB9\\uDCBB\\uDCBD-\\uDCC3\\uDCC5-\\uDD05\\uDD07-\\uDD0A\\uDD0D-\\uDD14\\uDD16-\\uDD1C\\uDD1E-\\uDD39\\uDD3B-\\uDD3E\\uDD40-\\uDD44\\uDD46\\uDD4A-\\uDD50\\uDD52-\\uDEA5\\uDEA8-\\uDEC0\\uDEC2-\\uDEDA\\uDEDC-\\uDEFA\\uDEFC-\\uDF14\\uDF16-\\uDF34\\uDF36-\\uDF4E\\uDF50-\\uDF6E\\uDF70-\\uDF88\\uDF8A-\\uDFA8\\uDFAA-\\uDFC2\\uDFC4-\\uDFCB]|\\uD837[\\uDF00-\\uDF1E\\uDF25-\\uDF2A]|\\uD838[\\uDC30-\\uDC6D\\uDD00-\\uDD2C\\uDD37-\\uDD3D\\uDD4E\\uDE90-\\uDEAD\\uDEC0-\\uDEEB]|\\uD839[\\uDCD0-\\uDCEB\\uDFE0-\\uDFE6\\uDFE8-\\uDFEB\\uDFED\\uDFEE\\uDFF0-\\uDFFE]|\\uD83A[\\uDC00-\\uDCC4\\uDD00-\\uDD43\\uDD4B]|\\uD83B[\\uDE00-\\uDE03\\uDE05-\\uDE1F\\uDE21\\uDE22\\uDE24\\uDE27\\uDE29-\\uDE32\\uDE34-\\uDE37\\uDE39\\uDE3B\\uDE42\\uDE47\\uDE49\\uDE4B\\uDE4D-\\uDE4F\\uDE51\\uDE52\\uDE54\\uDE57\\uDE59\\uDE5B\\uDE5D\\uDE5F\\uDE61\\uDE62\\uDE64\\uDE67-\\uDE6A\\uDE6C-\\uDE72\\uDE74-\\uDE77\\uDE79-\\uDE7C\\uDE7E\\uDE80-\\uDE89\\uDE8B-\\uDE9B\\uDEA1-\\uDEA3\\uDEA5-\\uDEA9\\uDEAB-\\uDEBB]|\\uD869[\\uDC00-\\uDEDF\\uDF00-\\uDFFF]|\\uD86D[\\uDC00-\\uDF39\\uDF40-\\uDFFF]|\\uD86E[\\uDC00-\\uDC1D\\uDC20-\\uDFFF]|\\uD873[\\uDC00-\\uDEA1\\uDEB0-\\uDFFF]|\\uD87A[\\uDC00-\\uDFE0]|\\uD87E[\\uDC00-\\uDE1D]|\\uD884[\\uDC00-\\uDF4A\\uDF50-\\uDFFF]|\\uD888[\\uDC00-\\uDFAF])(?:[0-9A-Z_a-z\\xAA\\xB5\\xB7\\xBA\\xC0-\\xD6\\xD8-\\xF6\\xF8-\\u02C1\\u02C6-\\u02D1\\u02E0-\\u02E4\\u02EC\\u02EE\\u0300-\\u0374\\u0376\\u0377\\u037B-\\u037D\\u037F\\u0386-\\u038A\\u038C\\u038E-\\u03A1\\u03A3-\\u03F5\\u03F7-\\u0481\\u0483-\\u0487\\u048A-\\u052F\\u0531-\\u0556\\u0559\\u0560-\\u0588\\u0591-\\u05BD\\u05BF\\u05C1\\u05C2\\u05C4\\u05C5\\u05C7\\u05D0-\\u05EA\\u05EF-\\u05F2\\u0610-\\u061A\\u0620-\\u0669\\u066E-\\u06D3\\u06D5-\\u06DC\\u06DF-\\u06E8\\u06EA-\\u06FC\\u06FF\\u0710-\\u074A\\u074D-\\u07B1\\u07C0-\\u07F5\\u07FA\\u07FD\\u0800-\\u082D\\u0840-\\u085B\\u0860-\\u086A\\u0870-\\u0887\\u0889-\\u088E\\u0898-\\u08E1\\u08E3-\\u0963\\u0966-\\u096F\\u0971-\\u0983\\u0985-\\u098C\\u098F\\u0990\\u0993-\\u09A8\\u09AA-\\u09B0\\u09B2\\u09B6-\\u09B9\\u09BC-\\u09C4\\u09C7\\u09C8\\u09CB-\\u09CE\\u09D7\\u09DC\\u09DD\\u09DF-\\u09E3\\u09E6-\\u09F1\\u09FC\\u09FE\\u0A01-\\u0A03\\u0A05-\\u0A0A\\u0A0F\\u0A10\\u0A13-\\u0A28\\u0A2A-\\u0A30\\u0A32\\u0A33\\u0A35\\u0A36\\u0A38\\u0A39\\u0A3C\\u0A3E-\\u0A42\\u0A47\\u0A48\\u0A4B-\\u0A4D\\u0A51\\u0A59-\\u0A5C\\u0A5E\\u0A66-\\u0A75\\u0A81-\\u0A83\\u0A85-\\u0A8D\\u0A8F-\\u0A91\\u0A93-\\u0AA8\\u0AAA-\\u0AB0\\u0AB2\\u0AB3\\u0AB5-\\u0AB9\\u0ABC-\\u0AC5\\u0AC7-\\u0AC9\\u0ACB-\\u0ACD\\u0AD0\\u0AE0-\\u0AE3\\u0AE6-\\u0AEF\\u0AF9-\\u0AFF\\u0B01-\\u0B03\\u0B05-\\u0B0C\\u0B0F\\u0B10\\u0B13-\\u0B28\\u0B2A-\\u0B30\\u0B32\\u0B33\\u0B35-\\u0B39\\u0B3C-\\u0B44\\u0B47\\u0B48\\u0B4B-\\u0B4D\\u0B55-\\u0B57\\u0B5C\\u0B5D\\u0B5F-\\u0B63\\u0B66-\\u0B6F\\u0B71\\u0B82\\u0B83\\u0B85-\\u0B8A\\u0B8E-\\u0B90\\u0B92-\\u0B95\\u0B99\\u0B9A\\u0B9C\\u0B9E\\u0B9F\\u0BA3\\u0BA4\\u0BA8-\\u0BAA\\u0BAE-\\u0BB9\\u0BBE-\\u0BC2\\u0BC6-\\u0BC8\\u0BCA-\\u0BCD\\u0BD0\\u0BD7\\u0BE6-\\u0BEF\\u0C00-\\u0C0C\\u0C0E-\\u0C10\\u0C12-\\u0C28\\u0C2A-\\u0C39\\u0C3C-\\u0C44\\u0C46-\\u0C48\\u0C4A-\\u0C4D\\u0C55\\u0C56\\u0C58-\\u0C5A\\u0C5D\\u0C60-\\u0C63\\u0C66-\\u0C6F\\u0C80-\\u0C83\\u0C85-\\u0C8C\\u0C8E-\\u0C90\\u0C92-\\u0CA8\\u0CAA-\\u0CB3\\u0CB5-\\u0CB9\\u0CBC-\\u0CC4\\u0CC6-\\u0CC8\\u0CCA-\\u0CCD\\u0CD5\\u0CD6\\u0CDD\\u0CDE\\u0CE0-\\u0CE3\\u0CE6-\\u0CEF\\u0CF1-\\u0CF3\\u0D00-\\u0D0C\\u0D0E-\\u0D10\\u0D12-\\u0D44\\u0D46-\\u0D48\\u0D4A-\\u0D4E\\u0D54-\\u0D57\\u0D5F-\\u0D63\\u0D66-\\u0D6F\\u0D7A-\\u0D7F\\u0D81-\\u0D83\\u0D85-\\u0D96\\u0D9A-\\u0DB1\\u0DB3-\\u0DBB\\u0DBD\\u0DC0-\\u0DC6\\u0DCA\\u0DCF-\\u0DD4\\u0DD6\\u0DD8-\\u0DDF\\u0DE6-\\u0DEF\\u0DF2\\u0DF3\\u0E01-\\u0E3A\\u0E40-\\u0E4E\\u0E50-\\u0E59\\u0E81\\u0E82\\u0E84\\u0E86-\\u0E8A\\u0E8C-\\u0EA3\\u0EA5\\u0EA7-\\u0EBD\\u0EC0-\\u0EC4\\u0EC6\\u0EC8-\\u0ECE\\u0ED0-\\u0ED9\\u0EDC-\\u0EDF\\u0F00\\u0F18\\u0F19\\u0F20-\\u0F29\\u0F35\\u0F37\\u0F39\\u0F3E-\\u0F47\\u0F49-\\u0F6C\\u0F71-\\u0F84\\u0F86-\\u0F97\\u0F99-\\u0FBC\\u0FC6\\u1000-\\u1049\\u1050-\\u109D\\u10A0-\\u10C5\\u10C7\\u10CD\\u10D0-\\u10FA\\u10FC-\\u1248\\u124A-\\u124D\\u1250-\\u1256\\u1258\\u125A-\\u125D\\u1260-\\u1288\\u128A-\\u128D\\u1290-\\u12B0\\u12B2-\\u12B5\\u12B8-\\u12BE\\u12C0\\u12C2-\\u12C5\\u12C8-\\u12D6\\u12D8-\\u1310\\u1312-\\u1315\\u1318-\\u135A\\u135D-\\u135F\\u1369-\\u1371\\u1380-\\u138F\\u13A0-\\u13F5\\u13F8-\\u13FD\\u1401-\\u166C\\u166F-\\u167F\\u1681-\\u169A\\u16A0-\\u16EA\\u16EE-\\u16F8\\u1700-\\u1715\\u171F-\\u1734\\u1740-\\u1753\\u1760-\\u176C\\u176E-\\u1770\\u1772\\u1773\\u1780-\\u17D3\\u17D7\\u17DC\\u17DD\\u17E0-\\u17E9\\u180B-\\u180D\\u180F-\\u1819\\u1820-\\u1878\\u1880-\\u18AA\\u18B0-\\u18F5\\u1900-\\u191E\\u1920-\\u192B\\u1930-\\u193B\\u1946-\\u196D\\u1970-\\u1974\\u1980-\\u19AB\\u19B0-\\u19C9\\u19D0-\\u19DA\\u1A00-\\u1A1B\\u1A20-\\u1A5E\\u1A60-\\u1A7C\\u1A7F-\\u1A89\\u1A90-\\u1A99\\u1AA7\\u1AB0-\\u1ABD\\u1ABF-\\u1ACE\\u1B00-\\u1B4C\\u1B50-\\u1B59\\u1B6B-\\u1B73\\u1B80-\\u1BF3\\u1C00-\\u1C37\\u1C40-\\u1C49\\u1C4D-\\u1C7D\\u1C80-\\u1C88\\u1C90-\\u1CBA\\u1CBD-\\u1CBF\\u1CD0-\\u1CD2\\u1CD4-\\u1CFA\\u1D00-\\u1F15\\u1F18-\\u1F1D\\u1F20-\\u1F45\\u1F48-\\u1F4D\\u1F50-\\u1F57\\u1F59\\u1F5B\\u1F5D\\u1F5F-\\u1F7D\\u1F80-\\u1FB4\\u1FB6-\\u1FBC\\u1FBE\\u1FC2-\\u1FC4\\u1FC6-\\u1FCC\\u1FD0-\\u1FD3\\u1FD6-\\u1FDB\\u1FE0-\\u1FEC\\u1FF2-\\u1FF4\\u1FF6-\\u1FFC\\u203F\\u2040\\u2054\\u2071\\u207F\\u2090-\\u209C\\u20D0-\\u20DC\\u20E1\\u20E5-\\u20F0\\u2102\\u2107\\u210A-\\u2113\\u2115\\u2118-\\u211D\\u2124\\u2126\\u2128\\u212A-\\u2139\\u213C-\\u213F\\u2145-\\u2149\\u214E\\u2160-\\u2188\\u2C00-\\u2CE4\\u2CEB-\\u2CF3\\u2D00-\\u2D25\\u2D27\\u2D2D\\u2D30-\\u2D67\\u2D6F\\u2D7F-\\u2D96\\u2DA0-\\u2DA6\\u2DA8-\\u2DAE\\u2DB0-\\u2DB6\\u2DB8-\\u2DBE\\u2DC0-\\u2DC6\\u2DC8-\\u2DCE\\u2DD0-\\u2DD6\\u2DD8-\\u2DDE\\u2DE0-\\u2DFF\\u3005-\\u3007\\u3021-\\u302F\\u3031-\\u3035\\u3038-\\u303C\\u3041-\\u3096\\u3099\\u309A\\u309D-\\u309F\\u30A1-\\u30FA\\u30FC-\\u30FF\\u3105-\\u312F\\u3131-\\u318E\\u31A0-\\u31BF\\u31F0-\\u31FF\\u3400-\\u4DBF\\u4E00-\\uA48C\\uA4D0-\\uA4FD\\uA500-\\uA60C\\uA610-\\uA62B\\uA640-\\uA66F\\uA674-\\uA67D\\uA67F-\\uA6F1\\uA717-\\uA71F\\uA722-\\uA788\\uA78B-\\uA7CA\\uA7D0\\uA7D1\\uA7D3\\uA7D5-\\uA7D9\\uA7F2-\\uA827\\uA82C\\uA840-\\uA873\\uA880-\\uA8C5\\uA8D0-\\uA8D9\\uA8E0-\\uA8F7\\uA8FB\\uA8FD-\\uA92D\\uA930-\\uA953\\uA960-\\uA97C\\uA980-\\uA9C0\\uA9CF-\\uA9D9\\uA9E0-\\uA9FE\\uAA00-\\uAA36\\uAA40-\\uAA4D\\uAA50-\\uAA59\\uAA60-\\uAA76\\uAA7A-\\uAAC2\\uAADB-\\uAADD\\uAAE0-\\uAAEF\\uAAF2-\\uAAF6\\uAB01-\\uAB06\\uAB09-\\uAB0E\\uAB11-\\uAB16\\uAB20-\\uAB26\\uAB28-\\uAB2E\\uAB30-\\uAB5A\\uAB5C-\\uAB69\\uAB70-\\uABEA\\uABEC\\uABED\\uABF0-\\uABF9\\uAC00-\\uD7A3\\uD7B0-\\uD7C6\\uD7CB-\\uD7FB\\uF900-\\uFA6D\\uFA70-\\uFAD9\\uFB00-\\uFB06\\uFB13-\\uFB17\\uFB1D-\\uFB28\\uFB2A-\\uFB36\\uFB38-\\uFB3C\\uFB3E\\uFB40\\uFB41\\uFB43\\uFB44\\uFB46-\\uFBB1\\uFBD3-\\uFC5D\\uFC64-\\uFD3D\\uFD50-\\uFD8F\\uFD92-\\uFDC7\\uFDF0-\\uFDF9\\uFE00-\\uFE0F\\uFE20-\\uFE2F\\uFE33\\uFE34\\uFE4D-\\uFE4F\\uFE71\\uFE73\\uFE77\\uFE79\\uFE7B\\uFE7D\\uFE7F-\\uFEFC\\uFF10-\\uFF19\\uFF21-\\uFF3A\\uFF3F\\uFF41-\\uFF5A\\uFF66-\\uFFBE\\uFFC2-\\uFFC7\\uFFCA-\\uFFCF\\uFFD2-\\uFFD7\\uFFDA-\\uFFDC]|\\uD800[\\uDC00-\\uDC0B\\uDC0D-\\uDC26\\uDC28-\\uDC3A\\uDC3C\\uDC3D\\uDC3F-\\uDC4D\\uDC50-\\uDC5D\\uDC80-\\uDCFA\\uDD40-\\uDD74\\uDDFD\\uDE80-\\uDE9C\\uDEA0-\\uDED0\\uDEE0\\uDF00-\\uDF1F\\uDF2D-\\uDF4A\\uDF50-\\uDF7A\\uDF80-\\uDF9D\\uDFA0-\\uDFC3\\uDFC8-\\uDFCF\\uDFD1-\\uDFD5]|\\uD801[\\uDC00-\\uDC9D\\uDCA0-\\uDCA9\\uDCB0-\\uDCD3\\uDCD8-\\uDCFB\\uDD00-\\uDD27\\uDD30-\\uDD63\\uDD70-\\uDD7A\\uDD7C-\\uDD8A\\uDD8C-\\uDD92\\uDD94\\uDD95\\uDD97-\\uDDA1\\uDDA3-\\uDDB1\\uDDB3-\\uDDB9\\uDDBB\\uDDBC\\uDE00-\\uDF36\\uDF40-\\uDF55\\uDF60-\\uDF67\\uDF80-\\uDF85\\uDF87-\\uDFB0\\uDFB2-\\uDFBA]|\\uD802[\\uDC00-\\uDC05\\uDC08\\uDC0A-\\uDC35\\uDC37\\uDC38\\uDC3C\\uDC3F-\\uDC55\\uDC60-\\uDC76\\uDC80-\\uDC9E\\uDCE0-\\uDCF2\\uDCF4\\uDCF5\\uDD00-\\uDD15\\uDD20-\\uDD39\\uDD80-\\uDDB7\\uDDBE\\uDDBF\\uDE00-\\uDE03\\uDE05\\uDE06\\uDE0C-\\uDE13\\uDE15-\\uDE17\\uDE19-\\uDE35\\uDE38-\\uDE3A\\uDE3F\\uDE60-\\uDE7C\\uDE80-\\uDE9C\\uDEC0-\\uDEC7\\uDEC9-\\uDEE6\\uDF00-\\uDF35\\uDF40-\\uDF55\\uDF60-\\uDF72\\uDF80-\\uDF91]|\\uD803[\\uDC00-\\uDC48\\uDC80-\\uDCB2\\uDCC0-\\uDCF2\\uDD00-\\uDD27\\uDD30-\\uDD39\\uDE80-\\uDEA9\\uDEAB\\uDEAC\\uDEB0\\uDEB1\\uDEFD-\\uDF1C\\uDF27\\uDF30-\\uDF50\\uDF70-\\uDF85\\uDFB0-\\uDFC4\\uDFE0-\\uDFF6]|\\uD804[\\uDC00-\\uDC46\\uDC66-\\uDC75\\uDC7F-\\uDCBA\\uDCC2\\uDCD0-\\uDCE8\\uDCF0-\\uDCF9\\uDD00-\\uDD34\\uDD36-\\uDD3F\\uDD44-\\uDD47\\uDD50-\\uDD73\\uDD76\\uDD80-\\uDDC4\\uDDC9-\\uDDCC\\uDDCE-\\uDDDA\\uDDDC\\uDE00-\\uDE11\\uDE13-\\uDE37\\uDE3E-\\uDE41\\uDE80-\\uDE86\\uDE88\\uDE8A-\\uDE8D\\uDE8F-\\uDE9D\\uDE9F-\\uDEA8\\uDEB0-\\uDEEA\\uDEF0-\\uDEF9\\uDF00-\\uDF03\\uDF05-\\uDF0C\\uDF0F\\uDF10\\uDF13-\\uDF28\\uDF2A-\\uDF30\\uDF32\\uDF33\\uDF35-\\uDF39\\uDF3B-\\uDF44\\uDF47\\uDF48\\uDF4B-\\uDF4D\\uDF50\\uDF57\\uDF5D-\\uDF63\\uDF66-\\uDF6C\\uDF70-\\uDF74]|\\uD805[\\uDC00-\\uDC4A\\uDC50-\\uDC59\\uDC5E-\\uDC61\\uDC80-\\uDCC5\\uDCC7\\uDCD0-\\uDCD9\\uDD80-\\uDDB5\\uDDB8-\\uDDC0\\uDDD8-\\uDDDD\\uDE00-\\uDE40\\uDE44\\uDE50-\\uDE59\\uDE80-\\uDEB8\\uDEC0-\\uDEC9\\uDF00-\\uDF1A\\uDF1D-\\uDF2B\\uDF30-\\uDF39\\uDF40-\\uDF46]|\\uD806[\\uDC00-\\uDC3A\\uDCA0-\\uDCE9\\uDCFF-\\uDD06\\uDD09\\uDD0C-\\uDD13\\uDD15\\uDD16\\uDD18-\\uDD35\\uDD37\\uDD38\\uDD3B-\\uDD43\\uDD50-\\uDD59\\uDDA0-\\uDDA7\\uDDAA-\\uDDD7\\uDDDA-\\uDDE1\\uDDE3\\uDDE4\\uDE00-\\uDE3E\\uDE47\\uDE50-\\uDE99\\uDE9D\\uDEB0-\\uDEF8]|\\uD807[\\uDC00-\\uDC08\\uDC0A-\\uDC36\\uDC38-\\uDC40\\uDC50-\\uDC59\\uDC72-\\uDC8F\\uDC92-\\uDCA7\\uDCA9-\\uDCB6\\uDD00-\\uDD06\\uDD08\\uDD09\\uDD0B-\\uDD36\\uDD3A\\uDD3C\\uDD3D\\uDD3F-\\uDD47\\uDD50-\\uDD59\\uDD60-\\uDD65\\uDD67\\uDD68\\uDD6A-\\uDD8E\\uDD90\\uDD91\\uDD93-\\uDD98\\uDDA0-\\uDDA9\\uDEE0-\\uDEF6\\uDF00-\\uDF10\\uDF12-\\uDF3A\\uDF3E-\\uDF42\\uDF50-\\uDF59\\uDFB0]|\\uD808[\\uDC00-\\uDF99]|\\uD809[\\uDC00-\\uDC6E\\uDC80-\\uDD43]|\\uD80B[\\uDF90-\\uDFF0]|[\\uD80C\\uD81C-\\uD820\\uD822\\uD840-\\uD868\\uD86A-\\uD86C\\uD86F-\\uD872\\uD874-\\uD879\\uD880-\\uD883\\uD885-\\uD887][\\uDC00-\\uDFFF]|\\uD80D[\\uDC00-\\uDC2F\\uDC40-\\uDC55]|\\uD811[\\uDC00-\\uDE46]|\\uD81A[\\uDC00-\\uDE38\\uDE40-\\uDE5E\\uDE60-\\uDE69\\uDE70-\\uDEBE\\uDEC0-\\uDEC9\\uDED0-\\uDEED\\uDEF0-\\uDEF4\\uDF00-\\uDF36\\uDF40-\\uDF43\\uDF50-\\uDF59\\uDF63-\\uDF77\\uDF7D-\\uDF8F]|\\uD81B[\\uDE40-\\uDE7F\\uDF00-\\uDF4A\\uDF4F-\\uDF87\\uDF8F-\\uDF9F\\uDFE0\\uDFE1\\uDFE3\\uDFE4\\uDFF0\\uDFF1]|\\uD821[\\uDC00-\\uDFF7]|\\uD823[\\uDC00-\\uDCD5\\uDD00-\\uDD08]|\\uD82B[\\uDFF0-\\uDFF3\\uDFF5-\\uDFFB\\uDFFD\\uDFFE]|\\uD82C[\\uDC00-\\uDD22\\uDD32\\uDD50-\\uDD52\\uDD55\\uDD64-\\uDD67\\uDD70-\\uDEFB]|\\uD82F[\\uDC00-\\uDC6A\\uDC70-\\uDC7C\\uDC80-\\uDC88\\uDC90-\\uDC99\\uDC9D\\uDC9E]|\\uD833[\\uDF00-\\uDF2D\\uDF30-\\uDF46]|\\uD834[\\uDD65-\\uDD69\\uDD6D-\\uDD72\\uDD7B-\\uDD82\\uDD85-\\uDD8B\\uDDAA-\\uDDAD\\uDE42-\\uDE44]|\\uD835[\\uDC00-\\uDC54\\uDC56-\\uDC9C\\uDC9E\\uDC9F\\uDCA2\\uDCA5\\uDCA6\\uDCA9-\\uDCAC\\uDCAE-\\uDCB9\\uDCBB\\uDCBD-\\uDCC3\\uDCC5-\\uDD05\\uDD07-\\uDD0A\\uDD0D-\\uDD14\\uDD16-\\uDD1C\\uDD1E-\\uDD39\\uDD3B-\\uDD3E\\uDD40-\\uDD44\\uDD46\\uDD4A-\\uDD50\\uDD52-\\uDEA5\\uDEA8-\\uDEC0\\uDEC2-\\uDEDA\\uDEDC-\\uDEFA\\uDEFC-\\uDF14\\uDF16-\\uDF34\\uDF36-\\uDF4E\\uDF50-\\uDF6E\\uDF70-\\uDF88\\uDF8A-\\uDFA8\\uDFAA-\\uDFC2\\uDFC4-\\uDFCB\\uDFCE-\\uDFFF]|\\uD836[\\uDE00-\\uDE36\\uDE3B-\\uDE6C\\uDE75\\uDE84\\uDE9B-\\uDE9F\\uDEA1-\\uDEAF]|\\uD837[\\uDF00-\\uDF1E\\uDF25-\\uDF2A]|\\uD838[\\uDC00-\\uDC06\\uDC08-\\uDC18\\uDC1B-\\uDC21\\uDC23\\uDC24\\uDC26-\\uDC2A\\uDC30-\\uDC6D\\uDC8F\\uDD00-\\uDD2C\\uDD30-\\uDD3D\\uDD40-\\uDD49\\uDD4E\\uDE90-\\uDEAE\\uDEC0-\\uDEF9]|\\uD839[\\uDCD0-\\uDCF9\\uDFE0-\\uDFE6\\uDFE8-\\uDFEB\\uDFED\\uDFEE\\uDFF0-\\uDFFE]|\\uD83A[\\uDC00-\\uDCC4\\uDCD0-\\uDCD6\\uDD00-\\uDD4B\\uDD50-\\uDD59]|\\uD83B[\\uDE00-\\uDE03\\uDE05-\\uDE1F\\uDE21\\uDE22\\uDE24\\uDE27\\uDE29-\\uDE32\\uDE34-\\uDE37\\uDE39\\uDE3B\\uDE42\\uDE47\\uDE49\\uDE4B\\uDE4D-\\uDE4F\\uDE51\\uDE52\\uDE54\\uDE57\\uDE59\\uDE5B\\uDE5D\\uDE5F\\uDE61\\uDE62\\uDE64\\uDE67-\\uDE6A\\uDE6C-\\uDE72\\uDE74-\\uDE77\\uDE79-\\uDE7C\\uDE7E\\uDE80-\\uDE89\\uDE8B-\\uDE9B\\uDEA1-\\uDEA3\\uDEA5-\\uDEA9\\uDEAB-\\uDEBB]|\\uD83E[\\uDFF0-\\uDFF9]|\\uD869[\\uDC00-\\uDEDF\\uDF00-\\uDFFF]|\\uD86D[\\uDC00-\\uDF39\\uDF40-\\uDFFF]|\\uD86E[\\uDC00-\\uDC1D\\uDC20-\\uDFFF]|\\uD873[\\uDC00-\\uDEA1\\uDEB0-\\uDFFF]|\\uD87A[\\uDC00-\\uDFE0]|\\uD87E[\\uDC00-\\uDE1D]|\\uD884[\\uDC00-\\uDF4A\\uDF50-\\uDFFF]|\\uD888[\\uDC00-\\uDFAF]|\\uDB40[\\uDD00-\\uDDEF])*/,\r\n\t\t\ta = [\"and\", \"as\", \"assert\", \"async\", \"await\", \"break\", \"case\", \"class\", \"continue\", \"def\", \"del\", \"elif\",\r\n\t\t\t\t\"else\", \"except\", \"finally\", \"for\", \"from\", \"global\", \"if\", \"import\", \"in\", \"is\", \"lambda\", \"match\",\r\n\t\t\t\t\"nonlocal|10\", \"not\", \"or\", \"pass\", \"raise\", \"return\", \"try\", \"while\", \"with\", \"yield\"\r\n\t\t\t],\r\n\t\t\ti = {\r\n\t\t\t\t$pattern: /[A-Za-z]\\w+|__\\w+__/,\r\n\t\t\t\tkeyword: a,\r\n\t\t\t\tbuilt_in: [\"__import__\", \"abs\", \"all\", \"any\", \"ascii\", \"bin\", \"bool\", \"breakpoint\", \"bytearray\", \"bytes\",\r\n\t\t\t\t\t\"callable\", \"chr\", \"classmethod\", \"compile\", \"complex\", \"delattr\", \"dict\", \"dir\", \"divmod\",\r\n\t\t\t\t\t\"enumerate\", \"eval\", \"exec\", \"filter\", \"float\", \"format\", \"frozenset\", \"getattr\", \"globals\",\r\n\t\t\t\t\t\"hasattr\", \"hash\", \"help\", \"hex\", \"id\", \"input\", \"int\", \"isinstance\", \"issubclass\", \"iter\", \"len\",\r\n\t\t\t\t\t\"list\", \"locals\", \"map\", \"max\", \"memoryview\", \"min\", \"next\", \"object\", \"oct\", \"open\", \"ord\", \"pow\",\r\n\t\t\t\t\t\"print\", \"property\", \"range\", \"repr\", \"reversed\", \"round\", \"set\", \"setattr\", \"slice\", \"sorted\",\r\n\t\t\t\t\t\"staticmethod\", \"str\", \"sum\", \"super\", \"tuple\", \"type\", \"vars\", \"zip\"\r\n\t\t\t\t],\r\n\t\t\t\tliteral: [\"__debug__\", \"Ellipsis\", \"False\", \"None\", \"NotImplemented\", \"True\"],\r\n\t\t\t\ttype: [\"Any\", \"Callable\", \"Coroutine\", \"Dict\", \"List\", \"Literal\", \"Generic\", \"Optional\", \"Sequence\",\r\n\t\t\t\t\t\"Set\", \"Tuple\", \"Type\", \"Union\"\r\n\t\t\t\t]\r\n\t\t\t},\r\n\t\t\tr = {\r\n\t\t\t\tclassName: \"meta\",\r\n\t\t\t\tbegin: /^(>>>|\\.\\.\\.) /\r\n\t\t\t},\r\n\t\t\ts = {\r\n\t\t\t\tclassName: \"subst\",\r\n\t\t\t\tbegin: /\\{/,\r\n\t\t\t\tend: /\\}/,\r\n\t\t\t\tkeywords: i,\r\n\t\t\t\tillegal: /#/\r\n\t\t\t},\r\n\t\t\to = {\r\n\t\t\t\tbegin: /\\{\\{/,\r\n\t\t\t\trelevance: 0\r\n\t\t\t},\r\n\t\t\tl = {\r\n\t\t\t\tclassName: \"string\",\r\n\t\t\t\tcontains: [e.BACKSLASH_ESCAPE],\r\n\t\t\t\tvariants: [{\r\n\t\t\t\t\tbegin: /([uU]|[bB]|[rR]|[bB][rR]|[rR][bB])?'''/,\r\n\t\t\t\t\tend: /'''/,\r\n\t\t\t\t\tcontains: [e.BACKSLASH_ESCAPE, r],\r\n\t\t\t\t\trelevance: 10\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: /([uU]|[bB]|[rR]|[bB][rR]|[rR][bB])?\"\"\"/,\r\n\t\t\t\t\tend: /\"\"\"/,\r\n\t\t\t\t\tcontains: [e.BACKSLASH_ESCAPE, r],\r\n\t\t\t\t\trelevance: 10\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: /([fF][rR]|[rR][fF]|[fF])'''/,\r\n\t\t\t\t\tend: /'''/,\r\n\t\t\t\t\tcontains: [e.BACKSLASH_ESCAPE, r, o, s]\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: /([fF][rR]|[rR][fF]|[fF])\"\"\"/,\r\n\t\t\t\t\tend: /\"\"\"/,\r\n\t\t\t\t\tcontains: [e.BACKSLASH_ESCAPE, r, o, s]\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: /([uU]|[rR])'/,\r\n\t\t\t\t\tend: /'/,\r\n\t\t\t\t\trelevance: 10\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: /([uU]|[rR])\"/,\r\n\t\t\t\t\tend: /\"/,\r\n\t\t\t\t\trelevance: 10\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: /([bB]|[bB][rR]|[rR][bB])'/,\r\n\t\t\t\t\tend: /'/\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: /([bB]|[bB][rR]|[rR][bB])\"/,\r\n\t\t\t\t\tend: /\"/\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: /([fF][rR]|[rR][fF]|[fF])'/,\r\n\t\t\t\t\tend: /'/,\r\n\t\t\t\t\tcontains: [e.BACKSLASH_ESCAPE, o, s]\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: /([fF][rR]|[rR][fF]|[fF])\"/,\r\n\t\t\t\t\tend: /\"/,\r\n\t\t\t\t\tcontains: [e.BACKSLASH_ESCAPE, o, s]\r\n\t\t\t\t}, e.APOS_STRING_MODE, e.QUOTE_STRING_MODE]\r\n\t\t\t},\r\n\t\t\tc = \"[0-9](_?[0-9])*\",\r\n\t\t\td = `(\\\\b(${c}))?\\\\.(${c})|\\\\b(${c})\\\\.`,\r\n\t\t\tg = \"\\\\b|\" + a.join(\"|\"),\r\n\t\t\tu = {\r\n\t\t\t\tclassName: \"number\",\r\n\t\t\t\trelevance: 0,\r\n\t\t\t\tvariants: [{\r\n\t\t\t\t\tbegin: `(\\\\b(${c})|(${d}))[eE][+-]?(${c})[jJ]?(?=${g})`\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: `(${d})[jJ]?`\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: `\\\\b([1-9](_?[0-9])*|0+(_?0)*)[lLjJ]?(?=${g})`\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: `\\\\b0[bB](_?[01])+[lL]?(?=${g})`\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: `\\\\b0[oO](_?[0-7])+[lL]?(?=${g})`\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: `\\\\b0[xX](_?[0-9a-fA-F])+[lL]?(?=${g})`\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: `\\\\b(${c})[jJ](?=${g})`\r\n\t\t\t\t}]\r\n\t\t\t},\r\n\t\t\tb = {\r\n\t\t\t\tclassName: \"comment\",\r\n\t\t\t\tbegin: n.lookahead(/# type:/),\r\n\t\t\t\tend: /$/,\r\n\t\t\t\tkeywords: i,\r\n\t\t\t\tcontains: [{\r\n\t\t\t\t\tbegin: /# type:/\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: /#/,\r\n\t\t\t\t\tend: /\\b\\B/,\r\n\t\t\t\t\tendsWithParent: !0\r\n\t\t\t\t}]\r\n\t\t\t},\r\n\t\t\tm = {\r\n\t\t\t\tclassName: \"params\",\r\n\t\t\t\tvariants: [{\r\n\t\t\t\t\tclassName: \"\",\r\n\t\t\t\t\tbegin: /\\(\\s*\\)/,\r\n\t\t\t\t\tskip: !0\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: /\\(/,\r\n\t\t\t\t\tend: /\\)/,\r\n\t\t\t\t\texcludeBegin: !0,\r\n\t\t\t\t\texcludeEnd: !0,\r\n\t\t\t\t\tkeywords: i,\r\n\t\t\t\t\tcontains: [\"self\", r, u, l, e.HASH_COMMENT_MODE]\r\n\t\t\t\t}]\r\n\t\t\t};\r\n\t\treturn s.contains = [l, u, r], {\r\n\t\t\tname: \"Python\",\r\n\t\t\taliases: [\"py\", \"gyp\", \"ipython\"],\r\n\t\t\tunicodeRegex: !0,\r\n\t\t\tkeywords: i,\r\n\t\t\tillegal: /(<\\/|->|\\?)|=>/,\r\n\t\t\tcontains: [r, u, {\r\n\t\t\t\tbegin: /\\bself\\b/\r\n\t\t\t}, {\r\n\t\t\t\tbeginKeywords: \"if\",\r\n\t\t\t\trelevance: 0\r\n\t\t\t}, l, b, e.HASH_COMMENT_MODE, {\r\n\t\t\t\tmatch: [/\\bdef/, /\\s+/, t],\r\n\t\t\t\tscope: {\r\n\t\t\t\t\t1: \"keyword\",\r\n\t\t\t\t\t3: \"title.function\"\r\n\t\t\t\t},\r\n\t\t\t\tcontains: [m]\r\n\t\t\t}, {\r\n\t\t\t\tvariants: [{\r\n\t\t\t\t\tmatch: [/\\bclass/, /\\s+/, t, /\\s*/, /\\(\\s*/, t, /\\s*\\)/]\r\n\t\t\t\t}, {\r\n\t\t\t\t\tmatch: [/\\bclass/, /\\s+/, t]\r\n\t\t\t\t}],\r\n\t\t\t\tscope: {\r\n\t\t\t\t\t1: \"keyword\",\r\n\t\t\t\t\t3: \"title.class\",\r\n\t\t\t\t\t6: \"title.class.inherited\"\r\n\t\t\t\t}\r\n\t\t\t}, {\r\n\t\t\t\tclassName: \"meta\",\r\n\t\t\t\tbegin: /^[\\t ]*@/,\r\n\t\t\t\tend: /(?=#)|$/,\r\n\t\t\t\tcontains: [u, m, l]\r\n\t\t\t}]\r\n\t\t}\r\n\t},\r\n\tgrmr_python_repl: e => ({\r\n\t\taliases: [\"pycon\"],\r\n\t\tcontains: [{\r\n\t\t\tclassName: \"meta.prompt\",\r\n\t\t\tstarts: {\r\n\t\t\t\tend: / |$/,\r\n\t\t\t\tstarts: {\r\n\t\t\t\t\tend: \"$\",\r\n\t\t\t\t\tsubLanguage: \"python\"\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tvariants: [{\r\n\t\t\t\tbegin: /^>>>(?=[ ]|$)/\r\n\t\t\t}, {\r\n\t\t\t\tbegin: /^\\.\\.\\.(?=[ ]|$)/\r\n\t\t\t}]\r\n\t\t}]\r\n\t}),\r\n\tgrmr_r: e => {\r\n\t\tconst n = e.regex,\r\n\t\t\tt = /(?:(?:[a-zA-Z]|\\.[._a-zA-Z])[._a-zA-Z0-9]*)|\\.(?!\\d)/,\r\n\t\t\ta = n.either(/0[xX][0-9a-fA-F]+\\.[0-9a-fA-F]*[pP][+-]?\\d+i?/, /0[xX][0-9a-fA-F]+(?:[pP][+-]?\\d+)?[Li]?/,\r\n\t\t\t\t/(?:\\d+(?:\\.\\d*)?|\\.\\d+)(?:[eE][+-]?\\d+)?[Li]?/),\r\n\t\t\ti = /[=!<>:]=|\\|\\||&&|:::?|<-|<<-|->>|->|\\|>|[-+*\\/?!$&|:<=>@^~]|\\*\\*/,\r\n\t\t\tr = n.either(/[()]/, /[{}]/, /\\[\\[/, /[[\\]]/, /\\\\/, /,/);\r\n\t\treturn {\r\n\t\t\tname: \"R\",\r\n\t\t\tkeywords: {\r\n\t\t\t\t$pattern: t,\r\n\t\t\t\tkeyword: \"function if in break next repeat else for while\",\r\n\t\t\t\tliteral: \"NULL NA TRUE FALSE Inf NaN NA_integer_|10 NA_real_|10 NA_character_|10 NA_complex_|10\",\r\n\t\t\t\tbuilt_in: \"LETTERS letters month.abb month.name pi T F abs acos acosh all any anyNA Arg as.call as.character as.complex as.double as.environment as.integer as.logical as.null.default as.numeric as.raw asin asinh atan atanh attr attributes baseenv browser c call ceiling class Conj cos cosh cospi cummax cummin cumprod cumsum digamma dim dimnames emptyenv exp expression floor forceAndCall gamma gc.time globalenv Im interactive invisible is.array is.atomic is.call is.character is.complex is.double is.environment is.expression is.finite is.function is.infinite is.integer is.language is.list is.logical is.matrix is.na is.name is.nan is.null is.numeric is.object is.pairlist is.raw is.recursive is.single is.symbol lazyLoadDBfetch length lgamma list log max min missing Mod names nargs nzchar oldClass on.exit pos.to.env proc.time prod quote range Re rep retracemem return round seq_along seq_len seq.int sign signif sin sinh sinpi sqrt standardGeneric substitute sum switch tan tanh tanpi tracemem trigamma trunc unclass untracemem UseMethod xtfrm\"\r\n\t\t\t},\r\n\t\t\tcontains: [e.COMMENT(/#'/, /$/, {\r\n\t\t\t\tcontains: [{\r\n\t\t\t\t\tscope: \"doctag\",\r\n\t\t\t\t\tmatch: /@examples/,\r\n\t\t\t\t\tstarts: {\r\n\t\t\t\t\t\tend: n.lookahead(n.either(/\\n^#'\\s*(?=@[a-zA-Z]+)/, /\\n^(?!#')/)),\r\n\t\t\t\t\t\tendsParent: !0\r\n\t\t\t\t\t}\r\n\t\t\t\t}, {\r\n\t\t\t\t\tscope: \"doctag\",\r\n\t\t\t\t\tbegin: \"@param\",\r\n\t\t\t\t\tend: /$/,\r\n\t\t\t\t\tcontains: [{\r\n\t\t\t\t\t\tscope: \"variable\",\r\n\t\t\t\t\t\tvariants: [{\r\n\t\t\t\t\t\t\tmatch: t\r\n\t\t\t\t\t\t}, {\r\n\t\t\t\t\t\t\tmatch: /`(?:\\\\.|[^`\\\\])+`/\r\n\t\t\t\t\t\t}],\r\n\t\t\t\t\t\tendsParent: !0\r\n\t\t\t\t\t}]\r\n\t\t\t\t}, {\r\n\t\t\t\t\tscope: \"doctag\",\r\n\t\t\t\t\tmatch: /@[a-zA-Z]+/\r\n\t\t\t\t}, {\r\n\t\t\t\t\tscope: \"keyword\",\r\n\t\t\t\t\tmatch: /\\\\[a-zA-Z]+/\r\n\t\t\t\t}]\r\n\t\t\t}), e.HASH_COMMENT_MODE, {\r\n\t\t\t\tscope: \"string\",\r\n\t\t\t\tcontains: [e.BACKSLASH_ESCAPE],\r\n\t\t\t\tvariants: [e.END_SAME_AS_BEGIN({\r\n\t\t\t\t\tbegin: /[rR]\"(-*)\\(/,\r\n\t\t\t\t\tend: /\\)(-*)\"/\r\n\t\t\t\t}), e.END_SAME_AS_BEGIN({\r\n\t\t\t\t\tbegin: /[rR]\"(-*)\\{/,\r\n\t\t\t\t\tend: /\\}(-*)\"/\r\n\t\t\t\t}), e.END_SAME_AS_BEGIN({\r\n\t\t\t\t\tbegin: /[rR]\"(-*)\\[/,\r\n\t\t\t\t\tend: /\\](-*)\"/\r\n\t\t\t\t}), e.END_SAME_AS_BEGIN({\r\n\t\t\t\t\tbegin: /[rR]'(-*)\\(/,\r\n\t\t\t\t\tend: /\\)(-*)'/\r\n\t\t\t\t}), e.END_SAME_AS_BEGIN({\r\n\t\t\t\t\tbegin: /[rR]'(-*)\\{/,\r\n\t\t\t\t\tend: /\\}(-*)'/\r\n\t\t\t\t}), e.END_SAME_AS_BEGIN({\r\n\t\t\t\t\tbegin: /[rR]'(-*)\\[/,\r\n\t\t\t\t\tend: /\\](-*)'/\r\n\t\t\t\t}), {\r\n\t\t\t\t\tbegin: '\"',\r\n\t\t\t\t\tend: '\"',\r\n\t\t\t\t\trelevance: 0\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: \"'\",\r\n\t\t\t\t\tend: \"'\",\r\n\t\t\t\t\trelevance: 0\r\n\t\t\t\t}]\r\n\t\t\t}, {\r\n\t\t\t\trelevance: 0,\r\n\t\t\t\tvariants: [{\r\n\t\t\t\t\tscope: {\r\n\t\t\t\t\t\t1: \"operator\",\r\n\t\t\t\t\t\t2: \"number\"\r\n\t\t\t\t\t},\r\n\t\t\t\t\tmatch: [i, a]\r\n\t\t\t\t}, {\r\n\t\t\t\t\tscope: {\r\n\t\t\t\t\t\t1: \"operator\",\r\n\t\t\t\t\t\t2: \"number\"\r\n\t\t\t\t\t},\r\n\t\t\t\t\tmatch: [/%[^%]*%/, a]\r\n\t\t\t\t}, {\r\n\t\t\t\t\tscope: {\r\n\t\t\t\t\t\t1: \"punctuation\",\r\n\t\t\t\t\t\t2: \"number\"\r\n\t\t\t\t\t},\r\n\t\t\t\t\tmatch: [r, a]\r\n\t\t\t\t}, {\r\n\t\t\t\t\tscope: {\r\n\t\t\t\t\t\t2: \"number\"\r\n\t\t\t\t\t},\r\n\t\t\t\t\tmatch: [/[^a-zA-Z0-9._]|^/, a]\r\n\t\t\t\t}]\r\n\t\t\t}, {\r\n\t\t\t\tscope: {\r\n\t\t\t\t\t3: \"operator\"\r\n\t\t\t\t},\r\n\t\t\t\tmatch: [t, /\\s+/, /<-/, /\\s+/]\r\n\t\t\t}, {\r\n\t\t\t\tscope: \"operator\",\r\n\t\t\t\trelevance: 0,\r\n\t\t\t\tvariants: [{\r\n\t\t\t\t\tmatch: i\r\n\t\t\t\t}, {\r\n\t\t\t\t\tmatch: /%[^%]*%/\r\n\t\t\t\t}]\r\n\t\t\t}, {\r\n\t\t\t\tscope: \"punctuation\",\r\n\t\t\t\trelevance: 0,\r\n\t\t\t\tmatch: r\r\n\t\t\t}, {\r\n\t\t\t\tbegin: \"`\",\r\n\t\t\t\tend: \"`\",\r\n\t\t\t\tcontains: [{\r\n\t\t\t\t\tbegin: /\\\\./\r\n\t\t\t\t}]\r\n\t\t\t}]\r\n\t\t}\r\n\t},\r\n\tgrmr_ruby: e => {\r\n\t\tconst n = e.regex,\r\n\t\t\tt = \"([a-zA-Z_]\\\\w*[!?=]?|[-+~]@|<<|>>|=~|===?|<=>|[<>]=?|\\\\*\\\\*|[-/+%^&*~`|]|\\\\[\\\\]=?)\",\r\n\t\t\ta = n.either(/\\b([A-Z]+[a-z0-9]+)+/, /\\b([A-Z]+[a-z0-9]+)+[A-Z]+/),\r\n\t\t\ti = n.concat(a, /(::\\w+)*/),\r\n\t\t\tr = {\r\n\t\t\t\t\"variable.constant\": [\"__FILE__\", \"__LINE__\", \"__ENCODING__\"],\r\n\t\t\t\t\"variable.language\": [\"self\", \"super\"],\r\n\t\t\t\tkeyword: [\"alias\", \"and\", \"begin\", \"BEGIN\", \"break\", \"case\", \"class\", \"defined\", \"do\", \"else\", \"elsif\",\r\n\t\t\t\t\t\"end\", \"END\", \"ensure\", \"for\", \"if\", \"in\", \"module\", \"next\", \"not\", \"or\", \"redo\", \"require\", \"rescue\",\r\n\t\t\t\t\t\"retry\", \"return\", \"then\", \"undef\", \"unless\", \"until\", \"when\", \"while\", \"yield\", \"include\", \"extend\",\r\n\t\t\t\t\t\"prepend\", \"public\", \"private\", \"protected\", \"raise\", \"throw\"\r\n\t\t\t\t],\r\n\t\t\t\tbuilt_in: [\"proc\", \"lambda\", \"attr_accessor\", \"attr_reader\", \"attr_writer\", \"define_method\",\r\n\t\t\t\t\t\"private_constant\", \"module_function\"\r\n\t\t\t\t],\r\n\t\t\t\tliteral: [\"true\", \"false\", \"nil\"]\r\n\t\t\t},\r\n\t\t\ts = {\r\n\t\t\t\tclassName: \"doctag\",\r\n\t\t\t\tbegin: \"@[A-Za-z]+\"\r\n\t\t\t},\r\n\t\t\to = {\r\n\t\t\t\tbegin: \"#<\",\r\n\t\t\t\tend: \">\"\r\n\t\t\t},\r\n\t\t\tl = [e.COMMENT(\"#\", \"$\", {\r\n\t\t\t\tcontains: [s]\r\n\t\t\t}), e.COMMENT(\"^=begin\", \"^=end\", {\r\n\t\t\t\tcontains: [s],\r\n\t\t\t\trelevance: 10\r\n\t\t\t}), e.COMMENT(\"^__END__\", e.MATCH_NOTHING_RE)],\r\n\t\t\tc = {\r\n\t\t\t\tclassName: \"subst\",\r\n\t\t\t\tbegin: /#\\{/,\r\n\t\t\t\tend: /\\}/,\r\n\t\t\t\tkeywords: r\r\n\t\t\t},\r\n\t\t\td = {\r\n\t\t\t\tclassName: \"string\",\r\n\t\t\t\tcontains: [e.BACKSLASH_ESCAPE, c],\r\n\t\t\t\tvariants: [{\r\n\t\t\t\t\tbegin: /'/,\r\n\t\t\t\t\tend: /'/\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: /\"/,\r\n\t\t\t\t\tend: /\"/\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: /`/,\r\n\t\t\t\t\tend: /`/\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: /%[qQwWx]?\\(/,\r\n\t\t\t\t\tend: /\\)/\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: /%[qQwWx]?\\[/,\r\n\t\t\t\t\tend: /\\]/\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: /%[qQwWx]?\\{/,\r\n\t\t\t\t\tend: /\\}/\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: /%[qQwWx]?</,\r\n\t\t\t\t\tend: />/\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: /%[qQwWx]?\\//,\r\n\t\t\t\t\tend: /\\//\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: /%[qQwWx]?%/,\r\n\t\t\t\t\tend: /%/\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: /%[qQwWx]?-/,\r\n\t\t\t\t\tend: /-/\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: /%[qQwWx]?\\|/,\r\n\t\t\t\t\tend: /\\|/\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: /\\B\\?(\\\\\\d{1,3})/\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: /\\B\\?(\\\\x[A-Fa-f0-9]{1,2})/\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: /\\B\\?(\\\\u\\{?[A-Fa-f0-9]{1,6}\\}?)/\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: /\\B\\?(\\\\M-\\\\C-|\\\\M-\\\\c|\\\\c\\\\M-|\\\\M-|\\\\C-\\\\M-)[\\x20-\\x7e]/\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: /\\B\\?\\\\(c|C-)[\\x20-\\x7e]/\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: /\\B\\?\\\\?\\S/\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: n.concat(/<<[-~]?'?/, n.lookahead(/(\\w+)(?=\\W)[^\\n]*\\n(?:[^\\n]*\\n)*?\\s*\\1\\b/)),\r\n\t\t\t\t\tcontains: [e.END_SAME_AS_BEGIN({\r\n\t\t\t\t\t\tbegin: /(\\w+)/,\r\n\t\t\t\t\t\tend: /(\\w+)/,\r\n\t\t\t\t\t\tcontains: [e.BACKSLASH_ESCAPE, c]\r\n\t\t\t\t\t})]\r\n\t\t\t\t}]\r\n\t\t\t},\r\n\t\t\tg = \"[0-9](_?[0-9])*\",\r\n\t\t\tu = {\r\n\t\t\t\tclassName: \"number\",\r\n\t\t\t\trelevance: 0,\r\n\t\t\t\tvariants: [{\r\n\t\t\t\t\tbegin: `\\\\b([1-9](_?[0-9])*|0)(\\\\.(${g}))?([eE][+-]?(${g})|r)?i?\\\\b`\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: \"\\\\b0[dD][0-9](_?[0-9])*r?i?\\\\b\"\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: \"\\\\b0[bB][0-1](_?[0-1])*r?i?\\\\b\"\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: \"\\\\b0[oO][0-7](_?[0-7])*r?i?\\\\b\"\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: \"\\\\b0[xX][0-9a-fA-F](_?[0-9a-fA-F])*r?i?\\\\b\"\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: \"\\\\b0(_?[0-7])+r?i?\\\\b\"\r\n\t\t\t\t}]\r\n\t\t\t},\r\n\t\t\tb = {\r\n\t\t\t\tvariants: [{\r\n\t\t\t\t\tmatch: /\\(\\)/\r\n\t\t\t\t}, {\r\n\t\t\t\t\tclassName: \"params\",\r\n\t\t\t\t\tbegin: /\\(/,\r\n\t\t\t\t\tend: /(?=\\))/,\r\n\t\t\t\t\texcludeBegin: !0,\r\n\t\t\t\t\tendsParent: !0,\r\n\t\t\t\t\tkeywords: r\r\n\t\t\t\t}]\r\n\t\t\t},\r\n\t\t\tm = [d, {\r\n\t\t\t\tvariants: [{\r\n\t\t\t\t\tmatch: [/class\\s+/, i, /\\s+<\\s+/, i]\r\n\t\t\t\t}, {\r\n\t\t\t\t\tmatch: [/\\b(class|module)\\s+/, i]\r\n\t\t\t\t}],\r\n\t\t\t\tscope: {\r\n\t\t\t\t\t2: \"title.class\",\r\n\t\t\t\t\t4: \"title.class.inherited\"\r\n\t\t\t\t},\r\n\t\t\t\tkeywords: r\r\n\t\t\t}, {\r\n\t\t\t\tmatch: [/(include|extend)\\s+/, i],\r\n\t\t\t\tscope: {\r\n\t\t\t\t\t2: \"title.class\"\r\n\t\t\t\t},\r\n\t\t\t\tkeywords: r\r\n\t\t\t}, {\r\n\t\t\t\trelevance: 0,\r\n\t\t\t\tmatch: [i, /\\.new[. (]/],\r\n\t\t\t\tscope: {\r\n\t\t\t\t\t1: \"title.class\"\r\n\t\t\t\t}\r\n\t\t\t}, {\r\n\t\t\t\trelevance: 0,\r\n\t\t\t\tmatch: /\\b[A-Z][A-Z_0-9]+\\b/,\r\n\t\t\t\tclassName: \"variable.constant\"\r\n\t\t\t}, {\r\n\t\t\t\trelevance: 0,\r\n\t\t\t\tmatch: a,\r\n\t\t\t\tscope: \"title.class\"\r\n\t\t\t}, {\r\n\t\t\t\tmatch: [/def/, /\\s+/, t],\r\n\t\t\t\tscope: {\r\n\t\t\t\t\t1: \"keyword\",\r\n\t\t\t\t\t3: \"title.function\"\r\n\t\t\t\t},\r\n\t\t\t\tcontains: [b]\r\n\t\t\t}, {\r\n\t\t\t\tbegin: e.IDENT_RE + \"::\"\r\n\t\t\t}, {\r\n\t\t\t\tclassName: \"symbol\",\r\n\t\t\t\tbegin: e.UNDERSCORE_IDENT_RE + \"(!|\\\\?)?:\",\r\n\t\t\t\trelevance: 0\r\n\t\t\t}, {\r\n\t\t\t\tclassName: \"symbol\",\r\n\t\t\t\tbegin: \":(?!\\\\s)\",\r\n\t\t\t\tcontains: [d, {\r\n\t\t\t\t\tbegin: t\r\n\t\t\t\t}],\r\n\t\t\t\trelevance: 0\r\n\t\t\t}, u, {\r\n\t\t\t\tclassName: \"variable\",\r\n\t\t\t\tbegin: \"(\\\\$\\\\W)|((\\\\$|@@?)(\\\\w+))(?=[^@$?])(?![A-Za-z])(?![@$?'])\"\r\n\t\t\t}, {\r\n\t\t\t\tclassName: \"params\",\r\n\t\t\t\tbegin: /\\|/,\r\n\t\t\t\tend: /\\|/,\r\n\t\t\t\texcludeBegin: !0,\r\n\t\t\t\texcludeEnd: !0,\r\n\t\t\t\trelevance: 0,\r\n\t\t\t\tkeywords: r\r\n\t\t\t}, {\r\n\t\t\t\tbegin: \"(\" + e.RE_STARTERS_RE + \"|unless)\\\\s*\",\r\n\t\t\t\tkeywords: \"unless\",\r\n\t\t\t\tcontains: [{\r\n\t\t\t\t\tclassName: \"regexp\",\r\n\t\t\t\t\tcontains: [e.BACKSLASH_ESCAPE, c],\r\n\t\t\t\t\tillegal: /\\n/,\r\n\t\t\t\t\tvariants: [{\r\n\t\t\t\t\t\tbegin: \"/\",\r\n\t\t\t\t\t\tend: \"/[a-z]*\"\r\n\t\t\t\t\t}, {\r\n\t\t\t\t\t\tbegin: /%r\\{/,\r\n\t\t\t\t\t\tend: /\\}[a-z]*/\r\n\t\t\t\t\t}, {\r\n\t\t\t\t\t\tbegin: \"%r\\\\(\",\r\n\t\t\t\t\t\tend: \"\\\\)[a-z]*\"\r\n\t\t\t\t\t}, {\r\n\t\t\t\t\t\tbegin: \"%r!\",\r\n\t\t\t\t\t\tend: \"![a-z]*\"\r\n\t\t\t\t\t}, {\r\n\t\t\t\t\t\tbegin: \"%r\\\\[\",\r\n\t\t\t\t\t\tend: \"\\\\][a-z]*\"\r\n\t\t\t\t\t}]\r\n\t\t\t\t}].concat(o, l),\r\n\t\t\t\trelevance: 0\r\n\t\t\t}].concat(o, l);\r\n\t\tc.contains = m, b.contains = m;\r\n\t\tconst p = [{\r\n\t\t\tbegin: /^\\s*=>/,\r\n\t\t\tstarts: {\r\n\t\t\t\tend: \"$\",\r\n\t\t\t\tcontains: m\r\n\t\t\t}\r\n\t\t}, {\r\n\t\t\tclassName: \"meta.prompt\",\r\n\t\t\tbegin: \"^([>?]>|[\\\\w#]+\\\\(\\\\w+\\\\):\\\\d+:\\\\d+[>*]|(\\\\w+-)?\\\\d+\\\\.\\\\d+\\\\.\\\\d+(p\\\\d+)?[^\\\\d][^>]+>)(?=[ ])\",\r\n\t\t\tstarts: {\r\n\t\t\t\tend: \"$\",\r\n\t\t\t\tkeywords: r,\r\n\t\t\t\tcontains: m\r\n\t\t\t}\r\n\t\t}];\r\n\t\treturn l.unshift(o), {\r\n\t\t\tname: \"Ruby\",\r\n\t\t\taliases: [\"rb\", \"gemspec\", \"podspec\", \"thor\", \"irb\"],\r\n\t\t\tkeywords: r,\r\n\t\t\tillegal: /\\/\\*/,\r\n\t\t\tcontains: [e.SHEBANG({\r\n\t\t\t\tbinary: \"ruby\"\r\n\t\t\t})].concat(p).concat(l).concat(m)\r\n\t\t}\r\n\t},\r\n\tgrmr_rust: e => {\r\n\t\tconst n = e.regex,\r\n\t\t\tt = {\r\n\t\t\t\tclassName: \"title.function.invoke\",\r\n\t\t\t\trelevance: 0,\r\n\t\t\t\tbegin: n.concat(/\\b/, /(?!let\\b)/, e.IDENT_RE, n.lookahead(/\\s*\\(/))\r\n\t\t\t},\r\n\t\t\ta = \"([ui](8|16|32|64|128|size)|f(32|64))?\",\r\n\t\t\ti = [\"drop \", \"Copy\", \"Send\", \"Sized\", \"Sync\", \"Drop\", \"Fn\", \"FnMut\", \"FnOnce\", \"ToOwned\", \"Clone\", \"Debug\",\r\n\t\t\t\t\"PartialEq\", \"PartialOrd\", \"Eq\", \"Ord\", \"AsRef\", \"AsMut\", \"Into\", \"From\", \"Default\", \"Iterator\", \"Extend\",\r\n\t\t\t\t\"IntoIterator\", \"DoubleEndedIterator\", \"ExactSizeIterator\", \"SliceConcatExt\", \"ToString\", \"assert!\",\r\n\t\t\t\t\"assert_eq!\", \"bitflags!\", \"bytes!\", \"cfg!\", \"col!\", \"concat!\", \"concat_idents!\", \"debug_assert!\",\r\n\t\t\t\t\"debug_assert_eq!\", \"env!\", \"panic!\", \"file!\", \"format!\", \"format_args!\", \"include_bytes!\",\r\n\t\t\t\t\"include_str!\", \"line!\", \"local_data_key!\", \"module_path!\", \"option_env!\", \"print!\", \"println!\",\r\n\t\t\t\t\"select!\", \"stringify!\", \"try!\", \"unimplemented!\", \"unreachable!\", \"vec!\", \"write!\", \"writeln!\",\r\n\t\t\t\t\"macro_rules!\", \"assert_ne!\", \"debug_assert_ne!\"\r\n\t\t\t],\r\n\t\t\tr = [\"i8\", \"i16\", \"i32\", \"i64\", \"i128\", \"isize\", \"u8\", \"u16\", \"u32\", \"u64\", \"u128\", \"usize\", \"f32\", \"f64\",\r\n\t\t\t\t\"str\", \"char\", \"bool\", \"Box\", \"Option\", \"Result\", \"String\", \"Vec\"\r\n\t\t\t];\r\n\t\treturn {\r\n\t\t\tname: \"Rust\",\r\n\t\t\taliases: [\"rs\"],\r\n\t\t\tkeywords: {\r\n\t\t\t\t$pattern: e.IDENT_RE + \"!?\",\r\n\t\t\t\ttype: r,\r\n\t\t\t\tkeyword: [\"abstract\", \"as\", \"async\", \"await\", \"become\", \"box\", \"break\", \"const\", \"continue\", \"crate\",\r\n\t\t\t\t\t\"do\", \"dyn\", \"else\", \"enum\", \"extern\", \"false\", \"final\", \"fn\", \"for\", \"if\", \"impl\", \"in\", \"let\",\r\n\t\t\t\t\t\"loop\", \"macro\", \"match\", \"mod\", \"move\", \"mut\", \"override\", \"priv\", \"pub\", \"ref\", \"return\", \"self\",\r\n\t\t\t\t\t\"Self\", \"static\", \"struct\", \"super\", \"trait\", \"true\", \"try\", \"type\", \"typeof\", \"unsafe\", \"unsized\",\r\n\t\t\t\t\t\"use\", \"virtual\", \"where\", \"while\", \"yield\"\r\n\t\t\t\t],\r\n\t\t\t\tliteral: [\"true\", \"false\", \"Some\", \"None\", \"Ok\", \"Err\"],\r\n\t\t\t\tbuilt_in: i\r\n\t\t\t},\r\n\t\t\tillegal: \"</\",\r\n\t\t\tcontains: [e.C_LINE_COMMENT_MODE, e.COMMENT(\"/\\\\*\", \"\\\\*/\", {\r\n\t\t\t\tcontains: [\"self\"]\r\n\t\t\t}), e.inherit(e.QUOTE_STRING_MODE, {\r\n\t\t\t\tbegin: /b?\"/,\r\n\t\t\t\tillegal: null\r\n\t\t\t}), {\r\n\t\t\t\tclassName: \"string\",\r\n\t\t\t\tvariants: [{\r\n\t\t\t\t\tbegin: /b?r(#*)\"(.|\\n)*?\"\\1(?!#)/\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: /b?'\\\\?(x\\w{2}|u\\w{4}|U\\w{8}|.)'/\r\n\t\t\t\t}]\r\n\t\t\t}, {\r\n\t\t\t\tclassName: \"symbol\",\r\n\t\t\t\tbegin: /'[a-zA-Z_][a-zA-Z0-9_]*/\r\n\t\t\t}, {\r\n\t\t\t\tclassName: \"number\",\r\n\t\t\t\tvariants: [{\r\n\t\t\t\t\tbegin: \"\\\\b0b([01_]+)\" + a\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: \"\\\\b0o([0-7_]+)\" + a\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: \"\\\\b0x([A-Fa-f0-9_]+)\" + a\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: \"\\\\b(\\\\d[\\\\d_]*(\\\\.[0-9_]+)?([eE][+-]?[0-9_]+)?)\" + a\r\n\t\t\t\t}],\r\n\t\t\t\trelevance: 0\r\n\t\t\t}, {\r\n\t\t\t\tbegin: [/fn/, /\\s+/, e.UNDERSCORE_IDENT_RE],\r\n\t\t\t\tclassName: {\r\n\t\t\t\t\t1: \"keyword\",\r\n\t\t\t\t\t3: \"title.function\"\r\n\t\t\t\t}\r\n\t\t\t}, {\r\n\t\t\t\tclassName: \"meta\",\r\n\t\t\t\tbegin: \"#!?\\\\[\",\r\n\t\t\t\tend: \"\\\\]\",\r\n\t\t\t\tcontains: [{\r\n\t\t\t\t\tclassName: \"string\",\r\n\t\t\t\t\tbegin: /\"/,\r\n\t\t\t\t\tend: /\"/\r\n\t\t\t\t}]\r\n\t\t\t}, {\r\n\t\t\t\tbegin: [/let/, /\\s+/, /(?:mut\\s+)?/, e.UNDERSCORE_IDENT_RE],\r\n\t\t\t\tclassName: {\r\n\t\t\t\t\t1: \"keyword\",\r\n\t\t\t\t\t3: \"keyword\",\r\n\t\t\t\t\t4: \"variable\"\r\n\t\t\t\t}\r\n\t\t\t}, {\r\n\t\t\t\tbegin: [/for/, /\\s+/, e.UNDERSCORE_IDENT_RE, /\\s+/, /in/],\r\n\t\t\t\tclassName: {\r\n\t\t\t\t\t1: \"keyword\",\r\n\t\t\t\t\t3: \"variable\",\r\n\t\t\t\t\t5: \"keyword\"\r\n\t\t\t\t}\r\n\t\t\t}, {\r\n\t\t\t\tbegin: [/type/, /\\s+/, e.UNDERSCORE_IDENT_RE],\r\n\t\t\t\tclassName: {\r\n\t\t\t\t\t1: \"keyword\",\r\n\t\t\t\t\t3: \"title.class\"\r\n\t\t\t\t}\r\n\t\t\t}, {\r\n\t\t\t\tbegin: [/(?:trait|enum|struct|union|impl|for)/, /\\s+/, e.UNDERSCORE_IDENT_RE],\r\n\t\t\t\tclassName: {\r\n\t\t\t\t\t1: \"keyword\",\r\n\t\t\t\t\t3: \"title.class\"\r\n\t\t\t\t}\r\n\t\t\t}, {\r\n\t\t\t\tbegin: e.IDENT_RE + \"::\",\r\n\t\t\t\tkeywords: {\r\n\t\t\t\t\tkeyword: \"Self\",\r\n\t\t\t\t\tbuilt_in: i,\r\n\t\t\t\t\ttype: r\r\n\t\t\t\t}\r\n\t\t\t}, {\r\n\t\t\t\tclassName: \"punctuation\",\r\n\t\t\t\tbegin: \"->\"\r\n\t\t\t}, t]\r\n\t\t}\r\n\t},\r\n\tgrmr_scss: e => {\r\n\t\tconst n = J(e),\r\n\t\t\tt = te,\r\n\t\t\ta = ne,\r\n\t\t\ti = \"@[a-z-]+\",\r\n\t\t\tr = {\r\n\t\t\t\tclassName: \"variable\",\r\n\t\t\t\tbegin: \"(\\\\$[a-zA-Z-][a-zA-Z0-9_-]*)\\\\b\",\r\n\t\t\t\trelevance: 0\r\n\t\t\t};\r\n\t\treturn {\r\n\t\t\tname: \"SCSS\",\r\n\t\t\tcase_insensitive: !0,\r\n\t\t\tillegal: \"[=/|']\",\r\n\t\t\tcontains: [e.C_LINE_COMMENT_MODE, e.C_BLOCK_COMMENT_MODE, n.CSS_NUMBER_MODE, {\r\n\t\t\t\tclassName: \"selector-id\",\r\n\t\t\t\tbegin: \"#[A-Za-z0-9_-]+\",\r\n\t\t\t\trelevance: 0\r\n\t\t\t}, {\r\n\t\t\t\tclassName: \"selector-class\",\r\n\t\t\t\tbegin: \"\\\\.[A-Za-z0-9_-]+\",\r\n\t\t\t\trelevance: 0\r\n\t\t\t}, n.ATTRIBUTE_SELECTOR_MODE, {\r\n\t\t\t\tclassName: \"selector-tag\",\r\n\t\t\t\tbegin: \"\\\\b(\" + Y.join(\"|\") + \")\\\\b\",\r\n\t\t\t\trelevance: 0\r\n\t\t\t}, {\r\n\t\t\t\tclassName: \"selector-pseudo\",\r\n\t\t\t\tbegin: \":(\" + a.join(\"|\") + \")\"\r\n\t\t\t}, {\r\n\t\t\t\tclassName: \"selector-pseudo\",\r\n\t\t\t\tbegin: \":(:)?(\" + t.join(\"|\") + \")\"\r\n\t\t\t}, r, {\r\n\t\t\t\tbegin: /\\(/,\r\n\t\t\t\tend: /\\)/,\r\n\t\t\t\tcontains: [n.CSS_NUMBER_MODE]\r\n\t\t\t}, n.CSS_VARIABLE, {\r\n\t\t\t\tclassName: \"attribute\",\r\n\t\t\t\tbegin: \"\\\\b(\" + ae.join(\"|\") + \")\\\\b\"\r\n\t\t\t}, {\r\n\t\t\t\tbegin: \"\\\\b(whitespace|wait|w-resize|visible|vertical-text|vertical-ideographic|uppercase|upper-roman|upper-alpha|underline|transparent|top|thin|thick|text|text-top|text-bottom|tb-rl|table-header-group|table-footer-group|sw-resize|super|strict|static|square|solid|small-caps|separate|se-resize|scroll|s-resize|rtl|row-resize|ridge|right|repeat|repeat-y|repeat-x|relative|progress|pointer|overline|outside|outset|oblique|nowrap|not-allowed|normal|none|nw-resize|no-repeat|no-drop|newspaper|ne-resize|n-resize|move|middle|medium|ltr|lr-tb|lowercase|lower-roman|lower-alpha|loose|list-item|line|line-through|line-edge|lighter|left|keep-all|justify|italic|inter-word|inter-ideograph|inside|inset|inline|inline-block|inherit|inactive|ideograph-space|ideograph-parenthesis|ideograph-numeric|ideograph-alpha|horizontal|hidden|help|hand|groove|fixed|ellipsis|e-resize|double|dotted|distribute|distribute-space|distribute-letter|distribute-all-lines|disc|disabled|default|decimal|dashed|crosshair|collapse|col-resize|circle|char|center|capitalize|break-word|break-all|bottom|both|bolder|bold|block|bidi-override|below|baseline|auto|always|all-scroll|absolute|table|table-cell)\\\\b\"\r\n\t\t\t}, {\r\n\t\t\t\tbegin: /:/,\r\n\t\t\t\tend: /[;}{]/,\r\n\t\t\t\trelevance: 0,\r\n\t\t\t\tcontains: [n.BLOCK_COMMENT, r, n.HEXCOLOR, n.CSS_NUMBER_MODE, e.QUOTE_STRING_MODE, e.APOS_STRING_MODE,\r\n\t\t\t\t\tn.IMPORTANT, n.FUNCTION_DISPATCH\r\n\t\t\t\t]\r\n\t\t\t}, {\r\n\t\t\t\tbegin: \"@(page|font-face)\",\r\n\t\t\t\tkeywords: {\r\n\t\t\t\t\t$pattern: i,\r\n\t\t\t\t\tkeyword: \"@page @font-face\"\r\n\t\t\t\t}\r\n\t\t\t}, {\r\n\t\t\t\tbegin: \"@\",\r\n\t\t\t\tend: \"[{;]\",\r\n\t\t\t\treturnBegin: !0,\r\n\t\t\t\tkeywords: {\r\n\t\t\t\t\t$pattern: /[a-z-]+/,\r\n\t\t\t\t\tkeyword: \"and or not only\",\r\n\t\t\t\t\tattribute: ee.join(\" \")\r\n\t\t\t\t},\r\n\t\t\t\tcontains: [{\r\n\t\t\t\t\tbegin: i,\r\n\t\t\t\t\tclassName: \"keyword\"\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: /[a-z-]+(?=:)/,\r\n\t\t\t\t\tclassName: \"attribute\"\r\n\t\t\t\t}, r, e.QUOTE_STRING_MODE, e.APOS_STRING_MODE, n.HEXCOLOR, n.CSS_NUMBER_MODE]\r\n\t\t\t}, n.FUNCTION_DISPATCH]\r\n\t\t}\r\n\t},\r\n\tgrmr_shell: e => ({\r\n\t\tname: \"Shell Session\",\r\n\t\taliases: [\"console\", \"shellsession\"],\r\n\t\tcontains: [{\r\n\t\t\tclassName: \"meta.prompt\",\r\n\t\t\tbegin: /^\\s{0,3}[/~\\w\\d[\\]()@-]*[>%$#][ ]?/,\r\n\t\t\tstarts: {\r\n\t\t\t\tend: /[^\\\\](?=\\s*$)/,\r\n\t\t\t\tsubLanguage: \"bash\"\r\n\t\t\t}\r\n\t\t}]\r\n\t}),\r\n\tgrmr_sql: e => {\r\n\t\tconst n = e.regex,\r\n\t\t\tt = e.COMMENT(\"--\", \"$\"),\r\n\t\t\ta = [\"true\", \"false\", \"unknown\"],\r\n\t\t\ti = [\"bigint\", \"binary\", \"blob\", \"boolean\", \"char\", \"character\", \"clob\", \"date\", \"dec\", \"decfloat\",\r\n\t\t\t\t\"decimal\", \"float\", \"int\", \"integer\", \"interval\", \"nchar\", \"nclob\", \"national\", \"numeric\", \"real\", \"row\",\r\n\t\t\t\t\"smallint\", \"time\", \"timestamp\", \"varchar\", \"varying\", \"varbinary\"\r\n\t\t\t],\r\n\t\t\tr = [\"abs\", \"acos\", \"array_agg\", \"asin\", \"atan\", \"avg\", \"cast\", \"ceil\", \"ceiling\", \"coalesce\", \"corr\",\r\n\t\t\t\t\"cos\", \"cosh\", \"count\", \"covar_pop\", \"covar_samp\", \"cume_dist\", \"dense_rank\", \"deref\", \"element\", \"exp\",\r\n\t\t\t\t\"extract\", \"first_value\", \"floor\", \"json_array\", \"json_arrayagg\", \"json_exists\", \"json_object\",\r\n\t\t\t\t\"json_objectagg\", \"json_query\", \"json_table\", \"json_table_primitive\", \"json_value\", \"lag\", \"last_value\",\r\n\t\t\t\t\"lead\", \"listagg\", \"ln\", \"log\", \"log10\", \"lower\", \"max\", \"min\", \"mod\", \"nth_value\", \"ntile\", \"nullif\",\r\n\t\t\t\t\"percent_rank\", \"percentile_cont\", \"percentile_disc\", \"position\", \"position_regex\", \"power\", \"rank\",\r\n\t\t\t\t\"regr_avgx\", \"regr_avgy\", \"regr_count\", \"regr_intercept\", \"regr_r2\", \"regr_slope\", \"regr_sxx\", \"regr_sxy\",\r\n\t\t\t\t\"regr_syy\", \"row_number\", \"sin\", \"sinh\", \"sqrt\", \"stddev_pop\", \"stddev_samp\", \"substring\",\r\n\t\t\t\t\"substring_regex\", \"sum\", \"tan\", \"tanh\", \"translate\", \"translate_regex\", \"treat\", \"trim\", \"trim_array\",\r\n\t\t\t\t\"unnest\", \"upper\", \"value_of\", \"var_pop\", \"var_samp\", \"width_bucket\"\r\n\t\t\t],\r\n\t\t\ts = [\"create table\", \"insert into\", \"primary key\", \"foreign key\", \"not null\", \"alter table\",\r\n\t\t\t\t\"add constraint\", \"grouping sets\", \"on overflow\", \"character set\", \"respect nulls\", \"ignore nulls\",\r\n\t\t\t\t\"nulls first\", \"nulls last\", \"depth first\", \"breadth first\"\r\n\t\t\t],\r\n\t\t\to = r,\r\n\t\t\tl = [\"abs\", \"acos\", \"all\", \"allocate\", \"alter\", \"and\", \"any\", \"are\", \"array\", \"array_agg\",\r\n\t\t\t\t\"array_max_cardinality\", \"as\", \"asensitive\", \"asin\", \"asymmetric\", \"at\", \"atan\", \"atomic\",\r\n\t\t\t\t\"authorization\", \"avg\", \"begin\", \"begin_frame\", \"begin_partition\", \"between\", \"bigint\", \"binary\", \"blob\",\r\n\t\t\t\t\"boolean\", \"both\", \"by\", \"call\", \"called\", \"cardinality\", \"cascaded\", \"case\", \"cast\", \"ceil\", \"ceiling\",\r\n\t\t\t\t\"char\", \"char_length\", \"character\", \"character_length\", \"check\", \"classifier\", \"clob\", \"close\",\r\n\t\t\t\t\"coalesce\", \"collate\", \"collect\", \"column\", \"commit\", \"condition\", \"connect\", \"constraint\", \"contains\",\r\n\t\t\t\t\"convert\", \"copy\", \"corr\", \"corresponding\", \"cos\", \"cosh\", \"count\", \"covar_pop\", \"covar_samp\", \"create\",\r\n\t\t\t\t\"cross\", \"cube\", \"cume_dist\", \"current\", \"current_catalog\", \"current_date\",\r\n\t\t\t\t\"current_default_transform_group\", \"current_path\", \"current_role\", \"current_row\", \"current_schema\",\r\n\t\t\t\t\"current_time\", \"current_timestamp\", \"current_path\", \"current_role\", \"current_transform_group_for_type\",\r\n\t\t\t\t\"current_user\", \"cursor\", \"cycle\", \"date\", \"day\", \"deallocate\", \"dec\", \"decimal\", \"decfloat\", \"declare\",\r\n\t\t\t\t\"default\", \"define\", \"delete\", \"dense_rank\", \"deref\", \"describe\", \"deterministic\", \"disconnect\",\r\n\t\t\t\t\"distinct\", \"double\", \"drop\", \"dynamic\", \"each\", \"element\", \"else\", \"empty\", \"end\", \"end_frame\",\r\n\t\t\t\t\"end_partition\", \"end-exec\", \"equals\", \"escape\", \"every\", \"except\", \"exec\", \"execute\", \"exists\", \"exp\",\r\n\t\t\t\t\"external\", \"extract\", \"false\", \"fetch\", \"filter\", \"first_value\", \"float\", \"floor\", \"for\", \"foreign\",\r\n\t\t\t\t\"frame_row\", \"free\", \"from\", \"full\", \"function\", \"fusion\", \"get\", \"global\", \"grant\", \"group\", \"grouping\",\r\n\t\t\t\t\"groups\", \"having\", \"hold\", \"hour\", \"identity\", \"in\", \"indicator\", \"initial\", \"inner\", \"inout\",\r\n\t\t\t\t\"insensitive\", \"insert\", \"int\", \"integer\", \"intersect\", \"intersection\", \"interval\", \"into\", \"is\", \"join\",\r\n\t\t\t\t\"json_array\", \"json_arrayagg\", \"json_exists\", \"json_object\", \"json_objectagg\", \"json_query\", \"json_table\",\r\n\t\t\t\t\"json_table_primitive\", \"json_value\", \"lag\", \"language\", \"large\", \"last_value\", \"lateral\", \"lead\",\r\n\t\t\t\t\"leading\", \"left\", \"like\", \"like_regex\", \"listagg\", \"ln\", \"local\", \"localtime\", \"localtimestamp\", \"log\",\r\n\t\t\t\t\"log10\", \"lower\", \"match\", \"match_number\", \"match_recognize\", \"matches\", \"max\", \"member\", \"merge\",\r\n\t\t\t\t\"method\", \"min\", \"minute\", \"mod\", \"modifies\", \"module\", \"month\", \"multiset\", \"national\", \"natural\",\r\n\t\t\t\t\"nchar\", \"nclob\", \"new\", \"no\", \"none\", \"normalize\", \"not\", \"nth_value\", \"ntile\", \"null\", \"nullif\",\r\n\t\t\t\t\"numeric\", \"octet_length\", \"occurrences_regex\", \"of\", \"offset\", \"old\", \"omit\", \"on\", \"one\", \"only\",\r\n\t\t\t\t\"open\", \"or\", \"order\", \"out\", \"outer\", \"over\", \"overlaps\", \"overlay\", \"parameter\", \"partition\", \"pattern\",\r\n\t\t\t\t\"per\", \"percent\", \"percent_rank\", \"percentile_cont\", \"percentile_disc\", \"period\", \"portion\", \"position\",\r\n\t\t\t\t\"position_regex\", \"power\", \"precedes\", \"precision\", \"prepare\", \"primary\", \"procedure\", \"ptf\", \"range\",\r\n\t\t\t\t\"rank\", \"reads\", \"real\", \"recursive\", \"ref\", \"references\", \"referencing\", \"regr_avgx\", \"regr_avgy\",\r\n\t\t\t\t\"regr_count\", \"regr_intercept\", \"regr_r2\", \"regr_slope\", \"regr_sxx\", \"regr_sxy\", \"regr_syy\", \"release\",\r\n\t\t\t\t\"result\", \"return\", \"returns\", \"revoke\", \"right\", \"rollback\", \"rollup\", \"row\", \"row_number\", \"rows\",\r\n\t\t\t\t\"running\", \"savepoint\", \"scope\", \"scroll\", \"search\", \"second\", \"seek\", \"select\", \"sensitive\",\r\n\t\t\t\t\"session_user\", \"set\", \"show\", \"similar\", \"sin\", \"sinh\", \"skip\", \"smallint\", \"some\", \"specific\",\r\n\t\t\t\t\"specifictype\", \"sql\", \"sqlexception\", \"sqlstate\", \"sqlwarning\", \"sqrt\", \"start\", \"static\", \"stddev_pop\",\r\n\t\t\t\t\"stddev_samp\", \"submultiset\", \"subset\", \"substring\", \"substring_regex\", \"succeeds\", \"sum\", \"symmetric\",\r\n\t\t\t\t\"system\", \"system_time\", \"system_user\", \"table\", \"tablesample\", \"tan\", \"tanh\", \"then\", \"time\",\r\n\t\t\t\t\"timestamp\", \"timezone_hour\", \"timezone_minute\", \"to\", \"trailing\", \"translate\", \"translate_regex\",\r\n\t\t\t\t\"translation\", \"treat\", \"trigger\", \"trim\", \"trim_array\", \"true\", \"truncate\", \"uescape\", \"union\", \"unique\",\r\n\t\t\t\t\"unknown\", \"unnest\", \"update\", \"upper\", \"user\", \"using\", \"value\", \"values\", \"value_of\", \"var_pop\",\r\n\t\t\t\t\"var_samp\", \"varbinary\", \"varchar\", \"varying\", \"versioning\", \"when\", \"whenever\", \"where\", \"width_bucket\",\r\n\t\t\t\t\"window\", \"with\", \"within\", \"without\", \"year\", \"add\", \"asc\", \"collation\", \"desc\", \"final\", \"first\",\r\n\t\t\t\t\"last\", \"view\"\r\n\t\t\t].filter((e => !r.includes(e))),\r\n\t\t\tc = {\r\n\t\t\t\tbegin: n.concat(/\\b/, n.either(...o), /\\s*\\(/),\r\n\t\t\t\trelevance: 0,\r\n\t\t\t\tkeywords: {\r\n\t\t\t\t\tbuilt_in: o\r\n\t\t\t\t}\r\n\t\t\t};\r\n\t\treturn {\r\n\t\t\tname: \"SQL\",\r\n\t\t\tcase_insensitive: !0,\r\n\t\t\tillegal: /[{}]|<\\//,\r\n\t\t\tkeywords: {\r\n\t\t\t\t$pattern: /\\b[\\w\\.]+/,\r\n\t\t\t\tkeyword: ((e, {\r\n\t\t\t\t\texceptions: n,\r\n\t\t\t\t\twhen: t\r\n\t\t\t\t} = {}) => {\r\n\t\t\t\t\tconst a = t;\r\n\t\t\t\t\treturn n = n || [], e.map((e => e.match(/\\|\\d+$/) || n.includes(e) ? e : a(e) ? e + \"|0\" : e))\r\n\t\t\t\t})(l, {\r\n\t\t\t\t\twhen: e => e.length < 3\r\n\t\t\t\t}),\r\n\t\t\t\tliteral: a,\r\n\t\t\t\ttype: i,\r\n\t\t\t\tbuilt_in: [\"current_catalog\", \"current_date\", \"current_default_transform_group\", \"current_path\",\r\n\t\t\t\t\t\"current_role\", \"current_schema\", \"current_transform_group_for_type\", \"current_user\", \"session_user\",\r\n\t\t\t\t\t\"system_time\", \"system_user\", \"current_time\", \"localtime\", \"current_timestamp\", \"localtimestamp\"\r\n\t\t\t\t]\r\n\t\t\t},\r\n\t\t\tcontains: [{\r\n\t\t\t\tbegin: n.either(...s),\r\n\t\t\t\trelevance: 0,\r\n\t\t\t\tkeywords: {\r\n\t\t\t\t\t$pattern: /[\\w\\.]+/,\r\n\t\t\t\t\tkeyword: l.concat(s),\r\n\t\t\t\t\tliteral: a,\r\n\t\t\t\t\ttype: i\r\n\t\t\t\t}\r\n\t\t\t}, {\r\n\t\t\t\tclassName: \"type\",\r\n\t\t\t\tbegin: n.either(\"double precision\", \"large object\", \"with timezone\", \"without timezone\")\r\n\t\t\t}, c, {\r\n\t\t\t\tclassName: \"variable\",\r\n\t\t\t\tbegin: /@[a-z0-9]+/\r\n\t\t\t}, {\r\n\t\t\t\tclassName: \"string\",\r\n\t\t\t\tvariants: [{\r\n\t\t\t\t\tbegin: /'/,\r\n\t\t\t\t\tend: /'/,\r\n\t\t\t\t\tcontains: [{\r\n\t\t\t\t\t\tbegin: /''/\r\n\t\t\t\t\t}]\r\n\t\t\t\t}]\r\n\t\t\t}, {\r\n\t\t\t\tbegin: /\"/,\r\n\t\t\t\tend: /\"/,\r\n\t\t\t\tcontains: [{\r\n\t\t\t\t\tbegin: /\"\"/\r\n\t\t\t\t}]\r\n\t\t\t}, e.C_NUMBER_MODE, e.C_BLOCK_COMMENT_MODE, t, {\r\n\t\t\t\tclassName: \"operator\",\r\n\t\t\t\tbegin: /[-+*/=%^~]|&&?|\\|\\|?|!=?|<(?:=>?|<|>)?|>[>=]?/,\r\n\t\t\t\trelevance: 0\r\n\t\t\t}]\r\n\t\t}\r\n\t},\r\n\tgrmr_swift: e => {\r\n\t\tconst n = {\r\n\t\t\t\tmatch: /\\s+/,\r\n\t\t\t\trelevance: 0\r\n\t\t\t},\r\n\t\t\tt = e.COMMENT(\"/\\\\*\", \"\\\\*/\", {\r\n\t\t\t\tcontains: [\"self\"]\r\n\t\t\t}),\r\n\t\t\ta = [e.C_LINE_COMMENT_MODE, t],\r\n\t\t\ti = {\r\n\t\t\t\tmatch: [/\\./, p(...Ee, ...ye)],\r\n\t\t\t\tclassName: {\r\n\t\t\t\t\t2: \"keyword\"\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tr = {\r\n\t\t\t\tmatch: m(/\\./, p(...Ne)),\r\n\t\t\t\trelevance: 0\r\n\t\t\t},\r\n\t\t\ts = Ne.filter((e => \"string\" == typeof e)).concat([\"_|0\"]),\r\n\t\t\to = {\r\n\t\t\t\tvariants: [{\r\n\t\t\t\t\tclassName: \"keyword\",\r\n\t\t\t\t\tmatch: p(...Ne.filter((e => \"string\" != typeof e)).concat(we).map(fe), ...ye)\r\n\t\t\t\t}]\r\n\t\t\t},\r\n\t\t\tl = {\r\n\t\t\t\t$pattern: p(/\\b\\w+/, /#\\w+/),\r\n\t\t\t\tkeyword: s.concat(ke),\r\n\t\t\t\tliteral: ve\r\n\t\t\t},\r\n\t\t\tc = [i, r, o],\r\n\t\t\td = [{\r\n\t\t\t\tmatch: m(/\\./, p(...xe)),\r\n\t\t\t\trelevance: 0\r\n\t\t\t}, {\r\n\t\t\t\tclassName: \"built_in\",\r\n\t\t\t\tmatch: m(/\\b/, p(...xe), /(?=\\()/)\r\n\t\t\t}],\r\n\t\t\tu = {\r\n\t\t\t\tmatch: /->/,\r\n\t\t\t\trelevance: 0\r\n\t\t\t},\r\n\t\t\tb = [u, {\r\n\t\t\t\tclassName: \"operator\",\r\n\t\t\t\trelevance: 0,\r\n\t\t\t\tvariants: [{\r\n\t\t\t\t\tmatch: Ae\r\n\t\t\t\t}, {\r\n\t\t\t\t\tmatch: `\\\\.(\\\\.|${Se})+`\r\n\t\t\t\t}]\r\n\t\t\t}],\r\n\t\t\t_ = \"([0-9a-fA-F]_*)+\",\r\n\t\t\th = {\r\n\t\t\t\tclassName: \"number\",\r\n\t\t\t\trelevance: 0,\r\n\t\t\t\tvariants: [{\r\n\t\t\t\t\tmatch: \"\\\\b(([0-9]_*)+)(\\\\.(([0-9]_*)+))?([eE][+-]?(([0-9]_*)+))?\\\\b\"\r\n\t\t\t\t}, {\r\n\t\t\t\t\tmatch: `\\\\b0x(${_})(\\\\.(${_}))?([pP][+-]?(([0-9]_*)+))?\\\\b`\r\n\t\t\t\t}, {\r\n\t\t\t\t\tmatch: /\\b0o([0-7]_*)+\\b/\r\n\t\t\t\t}, {\r\n\t\t\t\t\tmatch: /\\b0b([01]_*)+\\b/\r\n\t\t\t\t}]\r\n\t\t\t},\r\n\t\t\tf = (e = \"\") => ({\r\n\t\t\t\tclassName: \"subst\",\r\n\t\t\t\tvariants: [{\r\n\t\t\t\t\tmatch: m(/\\\\/, e, /[0\\\\tnr\"']/)\r\n\t\t\t\t}, {\r\n\t\t\t\t\tmatch: m(/\\\\/, e, /u\\{[0-9a-fA-F]{1,8}\\}/)\r\n\t\t\t\t}]\r\n\t\t\t}),\r\n\t\t\tE = (e = \"\") => ({\r\n\t\t\t\tclassName: \"subst\",\r\n\t\t\t\tmatch: m(/\\\\/, e, /[\\t ]*(?:[\\r\\n]|\\r\\n)/)\r\n\t\t\t}),\r\n\t\t\ty = (e = \"\") => ({\r\n\t\t\t\tclassName: \"subst\",\r\n\t\t\t\tlabel: \"interpol\",\r\n\t\t\t\tbegin: m(/\\\\/, e, /\\(/),\r\n\t\t\t\tend: /\\)/\r\n\t\t\t}),\r\n\t\t\tw = (e = \"\") => ({\r\n\t\t\t\tbegin: m(e, /\"\"\"/),\r\n\t\t\t\tend: m(/\"\"\"/, e),\r\n\t\t\t\tcontains: [f(e), E(e), y(e)]\r\n\t\t\t}),\r\n\t\t\tN = (e = \"\") => ({\r\n\t\t\t\tbegin: m(e, /\"/),\r\n\t\t\t\tend: m(/\"/, e),\r\n\t\t\t\tcontains: [f(e), y(e)]\r\n\t\t\t}),\r\n\t\t\tv = {\r\n\t\t\t\tclassName: \"string\",\r\n\t\t\t\tvariants: [w(), w(\"#\"), w(\"##\"), w(\"###\"), N(), N(\"#\"), N(\"##\"), N(\"###\")]\r\n\t\t\t},\r\n\t\t\tO = {\r\n\t\t\t\tmatch: m(/`/, Re, /`/)\r\n\t\t\t},\r\n\t\t\tk = [O, {\r\n\t\t\t\tclassName: \"variable\",\r\n\t\t\t\tmatch: /\\$\\d+/\r\n\t\t\t}, {\r\n\t\t\t\tclassName: \"variable\",\r\n\t\t\t\tmatch: `\\\\$${Te}+`\r\n\t\t\t}],\r\n\t\t\tx = [{\r\n\t\t\t\tmatch: /(@|#(un)?)available/,\r\n\t\t\t\tclassName: \"keyword\",\r\n\t\t\t\tstarts: {\r\n\t\t\t\t\tcontains: [{\r\n\t\t\t\t\t\tbegin: /\\(/,\r\n\t\t\t\t\t\tend: /\\)/,\r\n\t\t\t\t\t\tkeywords: Le,\r\n\t\t\t\t\t\tcontains: [...b, h, v]\r\n\t\t\t\t\t}]\r\n\t\t\t\t}\r\n\t\t\t}, {\r\n\t\t\t\tclassName: \"keyword\",\r\n\t\t\t\tmatch: m(/@/, p(...Ie))\r\n\t\t\t}, {\r\n\t\t\t\tclassName: \"meta\",\r\n\t\t\t\tmatch: m(/@/, Re)\r\n\t\t\t}],\r\n\t\t\tM = {\r\n\t\t\t\tmatch: g(/\\b[A-Z]/),\r\n\t\t\t\trelevance: 0,\r\n\t\t\t\tcontains: [{\r\n\t\t\t\t\tclassName: \"type\",\r\n\t\t\t\t\tmatch: m(/(AV|CA|CF|CG|CI|CL|CM|CN|CT|MK|MP|MTK|MTL|NS|SCN|SK|UI|WK|XC)/, Te, \"+\")\r\n\t\t\t\t}, {\r\n\t\t\t\t\tclassName: \"type\",\r\n\t\t\t\t\tmatch: De,\r\n\t\t\t\t\trelevance: 0\r\n\t\t\t\t}, {\r\n\t\t\t\t\tmatch: /[?!]+/,\r\n\t\t\t\t\trelevance: 0\r\n\t\t\t\t}, {\r\n\t\t\t\t\tmatch: /\\.\\.\\./,\r\n\t\t\t\t\trelevance: 0\r\n\t\t\t\t}, {\r\n\t\t\t\t\tmatch: m(/\\s+&\\s+/, g(De)),\r\n\t\t\t\t\trelevance: 0\r\n\t\t\t\t}]\r\n\t\t\t},\r\n\t\t\tS = {\r\n\t\t\t\tbegin: /</,\r\n\t\t\t\tend: />/,\r\n\t\t\t\tkeywords: l,\r\n\t\t\t\tcontains: [...a, ...c, ...x, u, M]\r\n\t\t\t};\r\n\t\tM.contains.push(S);\r\n\t\tconst A = {\r\n\t\t\t\tbegin: /\\(/,\r\n\t\t\t\tend: /\\)/,\r\n\t\t\t\trelevance: 0,\r\n\t\t\t\tkeywords: l,\r\n\t\t\t\tcontains: [\"self\", {\r\n\t\t\t\t\tmatch: m(Re, /\\s*:/),\r\n\t\t\t\t\tkeywords: \"_|0\",\r\n\t\t\t\t\trelevance: 0\r\n\t\t\t\t}, ...a, ...c, ...d, ...b, h, v, ...k, ...x, M]\r\n\t\t\t},\r\n\t\t\tC = {\r\n\t\t\t\tbegin: /</,\r\n\t\t\t\tend: />/,\r\n\t\t\t\tcontains: [...a, M]\r\n\t\t\t},\r\n\t\t\tT = {\r\n\t\t\t\tbegin: /\\(/,\r\n\t\t\t\tend: /\\)/,\r\n\t\t\t\tkeywords: l,\r\n\t\t\t\tcontains: [{\r\n\t\t\t\t\tbegin: p(g(m(Re, /\\s*:/)), g(m(Re, /\\s+/, Re, /\\s*:/))),\r\n\t\t\t\t\tend: /:/,\r\n\t\t\t\t\trelevance: 0,\r\n\t\t\t\t\tcontains: [{\r\n\t\t\t\t\t\tclassName: \"keyword\",\r\n\t\t\t\t\t\tmatch: /\\b_\\b/\r\n\t\t\t\t\t}, {\r\n\t\t\t\t\t\tclassName: \"params\",\r\n\t\t\t\t\t\tmatch: Re\r\n\t\t\t\t\t}]\r\n\t\t\t\t}, ...a, ...c, ...b, h, v, ...x, M, A],\r\n\t\t\t\tendsParent: !0,\r\n\t\t\t\tillegal: /[\"']/\r\n\t\t\t},\r\n\t\t\tR = {\r\n\t\t\t\tmatch: [/func/, /\\s+/, p(O.match, Re, Ae)],\r\n\t\t\t\tclassName: {\r\n\t\t\t\t\t1: \"keyword\",\r\n\t\t\t\t\t3: \"title.function\"\r\n\t\t\t\t},\r\n\t\t\t\tcontains: [C, T, n],\r\n\t\t\t\tillegal: [/\\[/, /%/]\r\n\t\t\t},\r\n\t\t\tD = {\r\n\t\t\t\tmatch: [/\\b(?:subscript|init[?!]?)/, /\\s*(?=[<(])/],\r\n\t\t\t\tclassName: {\r\n\t\t\t\t\t1: \"keyword\"\r\n\t\t\t\t},\r\n\t\t\t\tcontains: [C, T, n],\r\n\t\t\t\tillegal: /\\[|%/\r\n\t\t\t},\r\n\t\t\tI = {\r\n\t\t\t\tmatch: [/operator/, /\\s+/, Ae],\r\n\t\t\t\tclassName: {\r\n\t\t\t\t\t1: \"keyword\",\r\n\t\t\t\t\t3: \"title\"\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tL = {\r\n\t\t\t\tbegin: [/precedencegroup/, /\\s+/, De],\r\n\t\t\t\tclassName: {\r\n\t\t\t\t\t1: \"keyword\",\r\n\t\t\t\t\t3: \"title\"\r\n\t\t\t\t},\r\n\t\t\t\tcontains: [M],\r\n\t\t\t\tkeywords: [...Oe, ...ve],\r\n\t\t\t\tend: /}/\r\n\t\t\t};\r\n\t\tfor (const e of v.variants) {\r\n\t\t\tconst n = e.contains.find((e => \"interpol\" === e.label));\r\n\t\t\tn.keywords = l;\r\n\t\t\tconst t = [...c, ...d, ...b, h, v, ...k];\r\n\t\t\tn.contains = [...t, {\r\n\t\t\t\tbegin: /\\(/,\r\n\t\t\t\tend: /\\)/,\r\n\t\t\t\tcontains: [\"self\", ...t]\r\n\t\t\t}]\r\n\t\t}\r\n\t\treturn {\r\n\t\t\tname: \"Swift\",\r\n\t\t\tkeywords: l,\r\n\t\t\tcontains: [...a, R, D, {\r\n\t\t\t\tbeginKeywords: \"struct protocol class extension enum actor\",\r\n\t\t\t\tend: \"\\\\{\",\r\n\t\t\t\texcludeEnd: !0,\r\n\t\t\t\tkeywords: l,\r\n\t\t\t\tcontains: [e.inherit(e.TITLE_MODE, {\r\n\t\t\t\t\tclassName: \"title.class\",\r\n\t\t\t\t\tbegin: /[A-Za-z$_][\\u00C0-\\u02B80-9A-Za-z$_]*/\r\n\t\t\t\t}), ...c]\r\n\t\t\t}, I, L, {\r\n\t\t\t\tbeginKeywords: \"import\",\r\n\t\t\t\tend: /$/,\r\n\t\t\t\tcontains: [...a],\r\n\t\t\t\trelevance: 0\r\n\t\t\t}, ...c, ...d, ...b, h, v, ...k, ...x, M, A]\r\n\t\t}\r\n\t},\r\n\tgrmr_typescript: e => {\r\n\t\tconst n = he(e),\r\n\t\t\tt = [\"any\", \"void\", \"number\", \"boolean\", \"string\", \"object\", \"never\", \"symbol\", \"bigint\", \"unknown\"],\r\n\t\t\ta = {\r\n\t\t\t\tbeginKeywords: \"namespace\",\r\n\t\t\t\tend: /\\{/,\r\n\t\t\t\texcludeEnd: !0,\r\n\t\t\t\tcontains: [n.exports.CLASS_REFERENCE]\r\n\t\t\t},\r\n\t\t\ti = {\r\n\t\t\t\tbeginKeywords: \"interface\",\r\n\t\t\t\tend: /\\{/,\r\n\t\t\t\texcludeEnd: !0,\r\n\t\t\t\tkeywords: {\r\n\t\t\t\t\tkeyword: \"interface extends\",\r\n\t\t\t\t\tbuilt_in: t\r\n\t\t\t\t},\r\n\t\t\t\tcontains: [n.exports.CLASS_REFERENCE]\r\n\t\t\t},\r\n\t\t\tr = {\r\n\t\t\t\t$pattern: ce,\r\n\t\t\t\tkeyword: de.concat([\"type\", \"namespace\", \"interface\", \"public\", \"private\", \"protected\", \"implements\",\r\n\t\t\t\t\t\"declare\", \"abstract\", \"readonly\", \"enum\", \"override\"\r\n\t\t\t\t]),\r\n\t\t\t\tliteral: ge,\r\n\t\t\t\tbuilt_in: _e.concat(t),\r\n\t\t\t\t\"variable.language\": pe\r\n\t\t\t},\r\n\t\t\ts = {\r\n\t\t\t\tclassName: \"meta\",\r\n\t\t\t\tbegin: \"@[A-Za-z$_][0-9A-Za-z$_]*\"\r\n\t\t\t},\r\n\t\t\to = (e, n, t) => {\r\n\t\t\t\tconst a = e.contains.findIndex((e => e.label === n));\r\n\t\t\t\tif (-1 === a) throw Error(\"can not find mode to replace\");\r\n\t\t\t\te.contains.splice(a, 1, t)\r\n\t\t\t};\r\n\t\treturn Object.assign(n.keywords, r),\r\n\t\t\tn.exports.PARAMS_CONTAINS.push(s), n.contains = n.contains.concat([s, a, i]),\r\n\t\t\to(n, \"shebang\", e.SHEBANG()), o(n, \"use_strict\", {\r\n\t\t\t\tclassName: \"meta\",\r\n\t\t\t\trelevance: 10,\r\n\t\t\t\tbegin: /^\\s*['\"]use strict['\"]/\r\n\t\t\t}), n.contains.find((e => \"func.def\" === e.label)).relevance = 0, Object.assign(n, {\r\n\t\t\t\tname: \"TypeScript\",\r\n\t\t\t\taliases: [\"ts\", \"tsx\"]\r\n\t\t\t}), n\r\n\t},\r\n\tgrmr_vbnet: e => {\r\n\t\tconst n = e.regex,\r\n\t\t\tt = /\\d{1,2}\\/\\d{1,2}\\/\\d{4}/,\r\n\t\t\ta = /\\d{4}-\\d{1,2}-\\d{1,2}/,\r\n\t\t\ti = /(\\d|1[012])(:\\d+){0,2} *(AM|PM)/,\r\n\t\t\tr = /\\d{1,2}(:\\d{1,2}){1,2}/,\r\n\t\t\ts = {\r\n\t\t\t\tclassName: \"literal\",\r\n\t\t\t\tvariants: [{\r\n\t\t\t\t\tbegin: n.concat(/# */, n.either(a, t), / *#/)\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: n.concat(/# */, r, / *#/)\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: n.concat(/# */, i, / *#/)\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: n.concat(/# */, n.either(a, t), / +/, n.either(i, r), / *#/)\r\n\t\t\t\t}]\r\n\t\t\t},\r\n\t\t\to = e.COMMENT(/'''/, /$/, {\r\n\t\t\t\tcontains: [{\r\n\t\t\t\t\tclassName: \"doctag\",\r\n\t\t\t\t\tbegin: /<\\/?/,\r\n\t\t\t\t\tend: />/\r\n\t\t\t\t}]\r\n\t\t\t}),\r\n\t\t\tl = e.COMMENT(null, /$/, {\r\n\t\t\t\tvariants: [{\r\n\t\t\t\t\tbegin: /'/\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: /([\\t ]|^)REM(?=\\s)/\r\n\t\t\t\t}]\r\n\t\t\t});\r\n\t\treturn {\r\n\t\t\tname: \"Visual Basic .NET\",\r\n\t\t\taliases: [\"vb\"],\r\n\t\t\tcase_insensitive: !0,\r\n\t\t\tclassNameAliases: {\r\n\t\t\t\tlabel: \"symbol\"\r\n\t\t\t},\r\n\t\t\tkeywords: {\r\n\t\t\t\tkeyword: \"addhandler alias aggregate ansi as async assembly auto binary by byref byval call case catch class compare const continue custom declare default delegate dim distinct do each equals else elseif end enum erase error event exit explicit finally for friend from function get global goto group handles if implements imports in inherits interface into iterator join key let lib loop me mid module mustinherit mustoverride mybase myclass namespace narrowing new next notinheritable notoverridable of off on operator option optional order overloads overridable overrides paramarray partial preserve private property protected public raiseevent readonly redim removehandler resume return select set shadows shared skip static step stop structure strict sub synclock take text then throw to try unicode until using when where while widening with withevents writeonly yield\",\r\n\t\t\t\tbuilt_in: \"addressof and andalso await directcast gettype getxmlnamespace is isfalse isnot istrue like mod nameof new not or orelse trycast typeof xor cbool cbyte cchar cdate cdbl cdec cint clng cobj csbyte cshort csng cstr cuint culng cushort\",\r\n\t\t\t\ttype: \"boolean byte char date decimal double integer long object sbyte short single string uinteger ulong ushort\",\r\n\t\t\t\tliteral: \"true false nothing\"\r\n\t\t\t},\r\n\t\t\tillegal: \"//|\\\\{|\\\\}|endif|gosub|variant|wend|^\\\\$ \",\r\n\t\t\tcontains: [{\r\n\t\t\t\tclassName: \"string\",\r\n\t\t\t\tbegin: /\"(\"\"|[^/n])\"C\\b/\r\n\t\t\t}, {\r\n\t\t\t\tclassName: \"string\",\r\n\t\t\t\tbegin: /\"/,\r\n\t\t\t\tend: /\"/,\r\n\t\t\t\tillegal: /\\n/,\r\n\t\t\t\tcontains: [{\r\n\t\t\t\t\tbegin: /\"\"/\r\n\t\t\t\t}]\r\n\t\t\t}, s, {\r\n\t\t\t\tclassName: \"number\",\r\n\t\t\t\trelevance: 0,\r\n\t\t\t\tvariants: [{\r\n\t\t\t\t\tbegin: /\\b\\d[\\d_]*((\\.[\\d_]+(E[+-]?[\\d_]+)?)|(E[+-]?[\\d_]+))[RFD@!#]?/\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: /\\b\\d[\\d_]*((U?[SIL])|[%&])?/\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: /&H[\\dA-F_]+((U?[SIL])|[%&])?/\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: /&O[0-7_]+((U?[SIL])|[%&])?/\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: /&B[01_]+((U?[SIL])|[%&])?/\r\n\t\t\t\t}]\r\n\t\t\t}, {\r\n\t\t\t\tclassName: \"label\",\r\n\t\t\t\tbegin: /^\\w+:/\r\n\t\t\t}, o, l, {\r\n\t\t\t\tclassName: \"meta\",\r\n\t\t\t\tbegin: /[\\t ]*#(const|disable|else|elseif|enable|end|externalsource|if|region)\\b/,\r\n\t\t\t\tend: /$/,\r\n\t\t\t\tkeywords: {\r\n\t\t\t\t\tkeyword: \"const disable else elseif enable end externalsource if region then\"\r\n\t\t\t\t},\r\n\t\t\t\tcontains: [l]\r\n\t\t\t}]\r\n\t\t}\r\n\t},\r\n\tgrmr_wasm: e => {\r\n\t\te.regex;\r\n\t\tconst n = e.COMMENT(/\\(;/, /;\\)/);\r\n\t\treturn n.contains.push(\"self\"), {\r\n\t\t\tname: \"WebAssembly\",\r\n\t\t\tkeywords: {\r\n\t\t\t\t$pattern: /[\\w.]+/,\r\n\t\t\t\tkeyword: [\"anyfunc\", \"block\", \"br\", \"br_if\", \"br_table\", \"call\", \"call_indirect\", \"data\", \"drop\",\r\n\t\t\t\t\t\"elem\", \"else\", \"end\", \"export\", \"func\", \"global.get\", \"global.set\", \"local.get\", \"local.set\",\r\n\t\t\t\t\t\"local.tee\", \"get_global\", \"get_local\", \"global\", \"if\", \"import\", \"local\", \"loop\", \"memory\",\r\n\t\t\t\t\t\"memory.grow\", \"memory.size\", \"module\", \"mut\", \"nop\", \"offset\", \"param\", \"result\", \"return\",\r\n\t\t\t\t\t\"select\", \"set_global\", \"set_local\", \"start\", \"table\", \"tee_local\", \"then\", \"type\", \"unreachable\"\r\n\t\t\t\t]\r\n\t\t\t},\r\n\t\t\tcontains: [e.COMMENT(/;;/, /$/), n, {\r\n\t\t\t\tmatch: [/(?:offset|align)/, /\\s*/, /=/],\r\n\t\t\t\tclassName: {\r\n\t\t\t\t\t1: \"keyword\",\r\n\t\t\t\t\t3: \"operator\"\r\n\t\t\t\t}\r\n\t\t\t}, {\r\n\t\t\t\tclassName: \"variable\",\r\n\t\t\t\tbegin: /\\$[\\w_]+/\r\n\t\t\t}, {\r\n\t\t\t\tmatch: /(\\((?!;)|\\))+/,\r\n\t\t\t\tclassName: \"punctuation\",\r\n\t\t\t\trelevance: 0\r\n\t\t\t}, {\r\n\t\t\t\tbegin: [/(?:func|call|call_indirect)/, /\\s+/, /\\$[^\\s)]+/],\r\n\t\t\t\tclassName: {\r\n\t\t\t\t\t1: \"keyword\",\r\n\t\t\t\t\t3: \"title.function\"\r\n\t\t\t\t}\r\n\t\t\t}, e.QUOTE_STRING_MODE, {\r\n\t\t\t\tmatch: /(i32|i64|f32|f64)(?!\\.)/,\r\n\t\t\t\tclassName: \"type\"\r\n\t\t\t}, {\r\n\t\t\t\tclassName: \"keyword\",\r\n\t\t\t\tmatch: /\\b(f32|f64|i32|i64)(?:\\.(?:abs|add|and|ceil|clz|const|convert_[su]\\/i(?:32|64)|copysign|ctz|demote\\/f64|div(?:_[su])?|eqz?|extend_[su]\\/i32|floor|ge(?:_[su])?|gt(?:_[su])?|le(?:_[su])?|load(?:(?:8|16|32)_[su])?|lt(?:_[su])?|max|min|mul|nearest|neg?|or|popcnt|promote\\/f32|reinterpret\\/[fi](?:32|64)|rem_[su]|rot[lr]|shl|shr_[su]|store(?:8|16|32)?|sqrt|sub|trunc(?:_[su]\\/f(?:32|64))?|wrap\\/i64|xor))\\b/\r\n\t\t\t}, {\r\n\t\t\t\tclassName: \"number\",\r\n\t\t\t\trelevance: 0,\r\n\t\t\t\tmatch: /[+-]?\\b(?:\\d(?:_?\\d)*(?:\\.\\d(?:_?\\d)*)?(?:[eE][+-]?\\d(?:_?\\d)*)?|0x[\\da-fA-F](?:_?[\\da-fA-F])*(?:\\.[\\da-fA-F](?:_?[\\da-fA-D])*)?(?:[pP][+-]?\\d(?:_?\\d)*)?)\\b|\\binf\\b|\\bnan(?::0x[\\da-fA-F](?:_?[\\da-fA-D])*)?\\b/\r\n\t\t\t}]\r\n\t\t}\r\n\t},\r\n\tgrmr_yaml: e => {\r\n\t\tconst n = \"true false yes no null\",\r\n\t\t\tt = \"[\\\\w#;/?:@&=+$,.~*'()[\\\\]]+\",\r\n\t\t\ta = {\r\n\t\t\t\tclassName: \"string\",\r\n\t\t\t\trelevance: 0,\r\n\t\t\t\tvariants: [{\r\n\t\t\t\t\tbegin: /'/,\r\n\t\t\t\t\tend: /'/\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: /\"/,\r\n\t\t\t\t\tend: /\"/\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: /\\S+/\r\n\t\t\t\t}],\r\n\t\t\t\tcontains: [e.BACKSLASH_ESCAPE, {\r\n\t\t\t\t\tclassName: \"template-variable\",\r\n\t\t\t\t\tvariants: [{\r\n\t\t\t\t\t\tbegin: /\\{\\{/,\r\n\t\t\t\t\t\tend: /\\}\\}/\r\n\t\t\t\t\t}, {\r\n\t\t\t\t\t\tbegin: /%\\{/,\r\n\t\t\t\t\t\tend: /\\}/\r\n\t\t\t\t\t}]\r\n\t\t\t\t}]\r\n\t\t\t},\r\n\t\t\ti = e.inherit(a, {\r\n\t\t\t\tvariants: [{\r\n\t\t\t\t\tbegin: /'/,\r\n\t\t\t\t\tend: /'/\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: /\"/,\r\n\t\t\t\t\tend: /\"/\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: /[^\\s,{}[\\]]+/\r\n\t\t\t\t}]\r\n\t\t\t}),\r\n\t\t\tr = {\r\n\t\t\t\tend: \",\",\r\n\t\t\t\tendsWithParent: !0,\r\n\t\t\t\texcludeEnd: !0,\r\n\t\t\t\tkeywords: n,\r\n\t\t\t\trelevance: 0\r\n\t\t\t},\r\n\t\t\ts = {\r\n\t\t\t\tbegin: /\\{/,\r\n\t\t\t\tend: /\\}/,\r\n\t\t\t\tcontains: [r],\r\n\t\t\t\tillegal: \"\\\\n\",\r\n\t\t\t\trelevance: 0\r\n\t\t\t},\r\n\t\t\to = {\r\n\t\t\t\tbegin: \"\\\\[\",\r\n\t\t\t\tend: \"\\\\]\",\r\n\t\t\t\tcontains: [r],\r\n\t\t\t\tillegal: \"\\\\n\",\r\n\t\t\t\trelevance: 0\r\n\t\t\t},\r\n\t\t\tl = [{\r\n\t\t\t\tclassName: \"attr\",\r\n\t\t\t\tvariants: [{\r\n\t\t\t\t\tbegin: \"\\\\w[\\\\w :\\\\/.-]*:(?=[ \\t]|$)\"\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: '\"\\\\w[\\\\w :\\\\/.-]*\":(?=[ \\t]|$)'\r\n\t\t\t\t}, {\r\n\t\t\t\t\tbegin: \"'\\\\w[\\\\w :\\\\/.-]*':(?=[ \\t]|$)\"\r\n\t\t\t\t}]\r\n\t\t\t}, {\r\n\t\t\t\tclassName: \"meta\",\r\n\t\t\t\tbegin: \"^---\\\\s*$\",\r\n\t\t\t\trelevance: 10\r\n\t\t\t}, {\r\n\t\t\t\tclassName: \"string\",\r\n\t\t\t\tbegin: \"[\\\\|>]([1-9]?[+-])?[ ]*\\\\n( +)[^ ][^\\\\n]*\\\\n(\\\\2[^\\\\n]+\\\\n?)*\"\r\n\t\t\t}, {\r\n\t\t\t\tbegin: \"<%[%=-]?\",\r\n\t\t\t\tend: \"[%-]?%>\",\r\n\t\t\t\tsubLanguage: \"ruby\",\r\n\t\t\t\texcludeBegin: !0,\r\n\t\t\t\texcludeEnd: !0,\r\n\t\t\t\trelevance: 0\r\n\t\t\t}, {\r\n\t\t\t\tclassName: \"type\",\r\n\t\t\t\tbegin: \"!\\\\w+!\" + t\r\n\t\t\t}, {\r\n\t\t\t\tclassName: \"type\",\r\n\t\t\t\tbegin: \"!<\" + t + \">\"\r\n\t\t\t}, {\r\n\t\t\t\tclassName: \"type\",\r\n\t\t\t\tbegin: \"!\" + t\r\n\t\t\t}, {\r\n\t\t\t\tclassName: \"type\",\r\n\t\t\t\tbegin: \"!!\" + t\r\n\t\t\t}, {\r\n\t\t\t\tclassName: \"meta\",\r\n\t\t\t\tbegin: \"&\" + e.UNDERSCORE_IDENT_RE + \"$\"\r\n\t\t\t}, {\r\n\t\t\t\tclassName: \"meta\",\r\n\t\t\t\tbegin: \"\\\\*\" + e.UNDERSCORE_IDENT_RE + \"$\"\r\n\t\t\t}, {\r\n\t\t\t\tclassName: \"bullet\",\r\n\t\t\t\tbegin: \"-(?=[ ]|$)\",\r\n\t\t\t\trelevance: 0\r\n\t\t\t}, e.HASH_COMMENT_MODE, {\r\n\t\t\t\tbeginKeywords: n,\r\n\t\t\t\tkeywords: {\r\n\t\t\t\t\tliteral: n\r\n\t\t\t\t}\r\n\t\t\t}, {\r\n\t\t\t\tclassName: \"number\",\r\n\t\t\t\tbegin: \"\\\\b[0-9]{4}(-[0-9][0-9]){0,2}([Tt \\\\t][0-9][0-9]?(:[0-9][0-9]){2})?(\\\\.[0-9]*)?([ \\\\t])*(Z|[-+][0-9][0-9]?(:[0-9][0-9])?)?\\\\b\"\r\n\t\t\t}, {\r\n\t\t\t\tclassName: \"number\",\r\n\t\t\t\tbegin: e.C_NUMBER_RE + \"\\\\b\",\r\n\t\t\t\trelevance: 0\r\n\t\t\t}, s, o, a],\r\n\t\t\tc = [...l];\r\n\t\treturn c.pop(), c.push(i), r.contains = c, {\r\n\t\t\tname: \"YAML\",\r\n\t\t\tcase_insensitive: !0,\r\n\t\t\taliases: [\"yml\"],\r\n\t\t\tcontains: l\r\n\t\t}\r\n\t}\r\n});\r\nconst $e = V;\r\nfor (const e of Object.keys(Be)) {\r\n\tconst n = e.replace(\"grmr_\", \"\").replace(\"_\", \"-\");\r\n\t$e.registerLanguage(n, Be[e])\r\n}\r\nexport {\r\n\t$e as\r\n\tdefault\r\n};"], "names": ["e", "t", "a", "n", "i", "r", "uni", "s", "o", "l", "d", "_", "h", "x", "f", "c", "k", "M", "S", "w", "A", "g", "E", "u", "b", "m", "p", "R", "y", "T", "v", "N", "O", "C", "D", "I", "L"], "mappings": ";;AAKA,IAAI,IAAI;AAAA,EACP,SAAS,CAAE;AACZ;AACA,SAAS,EAAEA,IAAG;AACb,SAAOA,cAAa,MAAMA,GAAE,QAAQA,GAAE,SAASA,GAAE,MAAM,MAAM;AAC5D,UAAM,MAAM,kBAAkB;AAAA,EAChC,IAAKA,cAAa,QAAQA,GAAE,MAAMA,GAAE,QAAQA,GAAE,SAAS,MAAM;AAC3D,UAAM,MAAM,kBAAkB;AAAA,EAChC,IAAK,OAAO,OAAOA,EAAC,GAAG,OAAO,oBAAoBA,EAAC,EAAE,QAAS,CAAAC,OAAK;AACjE,QAAIC,KAAIF,GAAEC,EAAC;AACX,gBAAY,OAAOC,MAAK,OAAO,SAASA,EAAC,KAAK,EAAEA,EAAC;AAAA,EACjD,CAAA,GAAIF;AACN;AACA,EAAE,UAAU,GAAG,EAAE,QAAQ,UAAU;AACnC,MAAM,EAAE;AAAA,EACP,YAAYA,IAAG;AACd,eAAWA,GAAE,SAASA,GAAE,OAAO,CAAE,IAAG,KAAK,OAAOA,GAAE,MAAM,KAAK,iBAAiB;AAAA,EAC9E;AAAA,EACD,cAAc;AACb,SAAK,iBAAiB;AAAA,EACtB;AACF;AAEA,SAAS,EAAEA,IAAG;AACb,SAAOA,GAAE,QAAQ,MAAM,OAAO,EAAE,QAAQ,MAAM,MAAM,EAAE,QAAQ,MAAM,MAAM,EAAE,QAAQ,MAAM,QAAQ,EAAE;AAAA,IAAQ;AAAA,IAC3G;AAAA,EAAQ;AACV;AAEA,SAAS,EAAEA,OAAMG,IAAG;AACnB,QAAMF,KAAI,uBAAO,OAAO,IAAI;AAC5B,aAAWE,MAAKH;AAAG,IAAAC,GAAEE,EAAC,IAAIH,GAAEG,EAAC;AAC7B,SAAOA,GAAE,QAAS,CAAAH,OAAK;AACtB,eAAWG,MAAKH;AAAG,MAAAC,GAAEE,EAAC,IAAIH,GAAEG,EAAC;AAAA,EAC7B,CAAA,GAAIF;AACN;AACA,MAAM,IAAI,CAAAD,OAAK,CAAC,CAACA,GAAE,SAASA,GAAE,eAAeA,GAAE;AAC/C,MAAM,EAAE;AAAA,EACP,YAAYA,IAAGG,IAAG;AACjB,SAAK,SAAS,IAAI,KAAK,cAAcA,GAAE,aAAaH,GAAE,KAAK,IAAI;AAAA,EAC/D;AAAA,EACD,QAAQA,IAAG;AACV,SAAK,UAAU,EAAEA,EAAC;AAAA,EAClB;AAAA,EACD,SAASA,IAAG;AACX,QAAI,CAAC,EAAEA,EAAC;AAAG;AACX,QAAIG,KAAI;AACR,IAAAA,KAAIH,GAAE,cAAc,cAAcA,GAAE,YAAY,CAACA,IAAG;AAAA,MACnD,QAAQG;AAAA,IACX,MAAQ;AACL,UAAIH,GAAE,SAAS,GAAG,GAAG;AACpB,cAAMC,KAAID,GAAE,MAAM,GAAG;AACrB,eAAO,CAAC,GAAGG,EAAC,GAAGF,GAAE,OAAO,IAAI,GAAGA,GAAE,IAAK,CAACD,IAAGG,OAAM,GAAGH,EAAC,GAAG,IAAI,OAAOG,KAAE,CAAC,CAAC,EAAE,CAAE,EAAE,KAAK,GAAG;AAAA,MACpF;AACD,aAAO,GAAGA,EAAC,GAAGH,EAAC;AAAA,IAClB,GAAKA,GAAE,OAAO;AAAA,MACX,QAAQ,KAAK;AAAA,IAChB,CAAG,GAAG,KAAK,KAAKG,EAAC;AAAA,EACf;AAAA,EACD,UAAUH,IAAG;AACZ,MAAEA,EAAC,MAAM,KAAK,UAAU;AAAA,EACxB;AAAA,EACD,QAAQ;AACP,WAAO,KAAK;AAAA,EACZ;AAAA,EACD,KAAKA,IAAG;AACP,SAAK,UAAU,gBAAgBA,EAAC;AAAA,EAChC;AACF;AACA,MAAM,IAAI,CAACA,KAAI,OAAO;AACrB,QAAMG,KAAI;AAAA,IACT,UAAU,CAAE;AAAA,EACd;AACC,SAAO,OAAO,OAAOA,IAAGH,EAAC,GAAGG;AAC7B;AACA,MAAM,EAAE;AAAA,EACP,cAAc;AACb,SAAK,WAAW,EAAG,GAAE,KAAK,QAAQ,CAAC,KAAK,QAAQ;AAAA,EAChD;AAAA,EACD,IAAI,MAAM;AACT,WAAO,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAAA,EACvC;AAAA,EACD,IAAI,OAAO;AACV,WAAO,KAAK;AAAA,EACZ;AAAA,EACD,IAAIH,IAAG;AACN,SAAK,IAAI,SAAS,KAAKA,EAAC;AAAA,EACxB;AAAA,EACD,SAASA,IAAG;AACX,UAAMG,KAAI,EAAE;AAAA,MACX,OAAOH;AAAA,IACV,CAAG;AACD,SAAK,IAAIG,EAAC,GAAG,KAAK,MAAM,KAAKA,EAAC;AAAA,EAC9B;AAAA,EACD,YAAY;AACX,QAAI,KAAK,MAAM,SAAS;AAAG,aAAO,KAAK,MAAM,IAAK;AAAA,EAClD;AAAA,EACD,gBAAgB;AACf,WAAO,KAAK,UAAS;AAAI;AAAA,EACzB;AAAA,EACD,SAAS;AACR,WAAO,KAAK,UAAU,KAAK,UAAU,MAAM,CAAC;AAAA,EAC5C;AAAA,EACD,KAAKH,IAAG;AACP,WAAO,KAAK,YAAY,MAAMA,IAAG,KAAK,QAAQ;AAAA,EAC9C;AAAA,EACD,OAAO,MAAMA,IAAGG,IAAG;AAClB,WAAO,YAAY,OAAOA,KAAIH,GAAE,QAAQG,EAAC,IAAIA,GAAE,aAAaH,GAAE,SAASG,EAAC,GACvEA,GAAE,SAAS,QAAS,CAAAA,OAAK,KAAK,MAAMH,IAAGG,EAAC,CAAG,GAAEH,GAAE,UAAUG,EAAC,IAAIH;AAAA,EAC/D;AAAA,EACD,OAAO,UAAUA,IAAG;AACnB,gBAAY,OAAOA,MAAKA,GAAE,aAAaA,GAAE,SAAS,MAAO,CAAAA,OAAK,YAAY,OAAOA,EAAG,IAAGA,GAAE,WAAW;AAAA,MAACA,GAAE,SACrG,KAAK,EAAE;AAAA,IACR,IAAGA,GAAE,SAAS,QAAS,CAAAA,OAAK;AAC5B,QAAE,UAAUA,EAAC;AAAA,IAChB;EACE;AACF;AACA,MAAM,UAAU,EAAE;AAAA,EACjB,YAAYA,IAAG;AACd,UAAO,GAAE,KAAK,UAAUA;AAAA,EACxB;AAAA,EACD,WAAWA,IAAGG,IAAG;AAChB,WAAOH,OAAM,KAAK,SAASG,EAAC,GAAG,KAAK,QAAQH,EAAC,GAAG,KAAK,UAAS;AAAA,EAC9D;AAAA,EACD,QAAQA,IAAG;AACV,WAAOA,MAAK,KAAK,IAAIA,EAAC;AAAA,EACtB;AAAA,EACD,eAAeA,IAAGG,IAAG;AACpB,UAAMF,KAAID,GAAE;AACZ,IAAAC,GAAE,cAAc,MAAIA,GAAE,WAAWE,IAAG,KAAK,IAAIF,EAAC;AAAA,EAC9C;AAAA,EACD,SAAS;AACR,WAAO,IAAI,EAAE,MAAM,KAAK,OAAO,EAAE,MAAO;AAAA,EACxC;AAAA,EACD,WAAW;AACV,WAAO;AAAA,EACP;AACF;AAEA,SAAS,EAAED,IAAG;AACb,SAAOA,KAAI,YAAY,OAAOA,KAAIA,KAAIA,GAAE,SAAS;AAClD;AAEA,SAAS,EAAEA,IAAG;AACb,SAAO,EAAE,OAAOA,IAAG,GAAG;AACvB;AAEA,SAAS,EAAEA,IAAG;AACb,SAAO,EAAE,OAAOA,IAAG,IAAI;AACxB;AAEA,SAAS,EAAEA,IAAG;AACb,SAAO,EAAE,OAAOA,IAAG,IAAI;AACxB;AAEA,SAAS,KAAKA,IAAG;AAChB,SAAOA,GAAE,IAAK,CAAAA,OAAK,EAAEA,EAAC,CAAG,EAAC,KAAK,EAAE;AAClC;AAEA,SAAS,KAAKA,IAAG;AAChB,QAAMG,MAAK,CAAAH,OAAK;AACf,UAAMG,KAAIH,GAAEA,GAAE,SAAS,CAAC;AACxB,WAAO,YAAY,OAAOG,MAAKA,GAAE,gBAAgB,UAAUH,GAAE,OAAOA,GAAE,SAAS,GAAG,CAAC,GAAGG,MAAK,CAAE;AAAA,EAC7F,GAAEH,EAAC;AACJ,SAAO,OAAOG,GAAE,UAAU,KAAK,QAAQH,GAAE,IAAK,CAAAA,OAAK,EAAEA,EAAC,CAAG,EAAC,KAAK,GAAG,IAAI;AACvE;AAEA,SAAS,EAAEA,IAAG;AACb,SAAO,OAAOA,GAAE,aAAa,GAAG,EAAE,KAAK,EAAE,EAAE,SAAS;AACrD;AACA,MAAM,IAAI;AAEV,SAAS,EAAEA,IAAG;AAAA,EACb,UAAUG;AACX,GAAG;AACF,MAAIF,KAAI;AACR,SAAOD,GAAE,IAAK,CAAAA,OAAK;AAClB,IAAAC,MAAK;AACL,UAAME,KAAIF;AACV,QAAIC,KAAI,EAAEF,EAAC,GACVI,KAAI;AACL,WAAOF,GAAE,SAAS,KAAI;AACrB,YAAMF,KAAI,EAAE,KAAKE,EAAC;AAClB,UAAI,CAACF,IAAG;AACP,QAAAI,MAAKF;AACL;AAAA,MACA;AACD,MAAAE,MAAKF,GAAE,UAAU,GAAGF,GAAE,KAAK,GAC1BE,KAAIA,GAAE,UAAUF,GAAE,QAAQA,GAAE,CAAC,EAAE,MAAM,GAAG,SAASA,GAAE,CAAC,EAAE,CAAC,KAAKA,GAAE,CAAC,IAAII,MAAK,QAAQ,OAAOJ,GAAE,CAAC,CAAC,IAAIG,OAAMC,MACpGJ,GAAE,CAAC,GACH,QAAQA,GAAE,CAAC,KAAKC;AAAA,IAClB;AACD,WAAOG;AAAA,EACT,GAAK,IAAK,CAAAJ,OAAK,IAAIA,EAAC,GAAG,EAAG,KAAKG,EAAC;AAChC;AACA,MAAM,IAAI,0EACT,IAAI;AAAA,EACH,OAAO;AAAA,EACP,WAAW;AACX,GACD,IAAI;AAAA,EACH,OAAO;AAAA,EACP,OAAO;AAAA,EACP,KAAK;AAAA,EACL,SAAS;AAAA,EACT,UAAU,CAAC,CAAC;AACZ,GACD,IAAI;AAAA,EACH,OAAO;AAAA,EACP,OAAO;AAAA,EACP,KAAK;AAAA,EACL,SAAS;AAAA,EACT,UAAU,CAAC,CAAC;AACZ,GACD,IAAI,CAACH,IAAGG,IAAGF,KAAI,CAAA,MAAO;AACrB,QAAMC,KAAI,EAAE;AAAA,IACX,OAAO;AAAA,IACP,OAAOF;AAAA,IACP,KAAKG;AAAA,IACL,UAAU,CAAE;AAAA,EACZ,GAAEF,EAAC;AACJ,EAAAC,GAAE,SAAS,KAAK;AAAA,IACf,OAAO;AAAA,IACP,OAAO;AAAA,IACP,KAAK;AAAA,IACL,cAAc;AAAA,IACd,WAAW;AAAA,EACd,CAAG;AACD,QAAMG,KAAI;AAAA,IAAE;AAAA,IAAK;AAAA,IAAK;AAAA,IAAM;AAAA,IAAM;AAAA,IAAM;AAAA,IAAM;AAAA,IAAM;AAAA,IAAM;AAAA,IAAM;AAAA,IAAM;AAAA,IAAM;AAAA,IAC3E;AAAA,IAAsB;AAAA,EAAmB;AAC1C,SAAOH,GAAE,SAAS,KAAK;AAAA,IACtB,OAAO,EAAE,QAAQ,KAAKG,IAAG,wBAAwB,MAAM;AAAA,EACvD,CAAA,GAAGH;AACJ,GACD,IAAI,EAAE,MAAM,GAAG,GACf,IAAI,EAAE,QAAQ,MAAM,GACpB,IAAI,EAAE,KAAK,GAAG;AACf,IAAI,IAAI,OAAO,OAAO;AAAA,EACrB,WAAW;AAAA,EACX,kBAAkB;AAAA,EAClB,UAAU;AAAA,EACV,qBAAqB;AAAA,EACrB,WAAW;AAAA,EACX,aAAa;AAAA,EACb,kBAAkB;AAAA,EAClB,gBAAgB;AAAA,EAChB,SAAS,CAACF,KAAI,OAAO;AACpB,UAAMG,KAAI;AACV,WAAOH,GAAE,WAAWA,GAAE,QAAQ,EAAEG,IAAG,QAAQH,GAAE,QAAQ,MAAM,IAAI,EAAE;AAAA,MAChE,OAAO;AAAA,MACP,OAAOG;AAAA,MACP,KAAK;AAAA,MACL,WAAW;AAAA,MACX,YAAY,CAACH,IAAGG,OAAM;AACrB,cAAMH,GAAE,SAASG,GAAE,YAAa;AAAA,MAChC;AAAA,IACD,GAAEH,EAAC;AAAA,EACJ;AAAA,EACD,kBAAkB;AAAA,EAClB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,IACnB,OAAO;AAAA,EACP;AAAA,EACD,SAAS;AAAA,EACT,qBAAqB;AAAA,EACrB,sBAAsB;AAAA,EACtB,mBAAmB;AAAA,EACnB,aAAa;AAAA,IACZ,OAAO;AAAA,IACP,OAAO;AAAA,IACP,WAAW;AAAA,EACX;AAAA,EACD,eAAe;AAAA,IACd,OAAO;AAAA,IACP,OAAO;AAAA,IACP,WAAW;AAAA,EACX;AAAA,EACD,oBAAoB;AAAA,IACnB,OAAO;AAAA,IACP,OAAO;AAAA,IACP,WAAW;AAAA,EACX;AAAA,EACD,aAAa;AAAA,IACZ,OAAO;AAAA,IACP,UAAU,CAAC;AAAA,MACV,OAAO;AAAA,MACP,OAAO;AAAA,MACP,KAAK;AAAA,MACL,SAAS;AAAA,MACT,UAAU,CAAC,GAAG;AAAA,QACb,OAAO;AAAA,QACP,KAAK;AAAA,QACL,WAAW;AAAA,QACX,UAAU,CAAC,CAAC;AAAA,MAChB,CAAI;AAAA,IACJ,CAAG;AAAA,EACD;AAAA,EACD,YAAY;AAAA,IACX,OAAO;AAAA,IACP,OAAO;AAAA,IACP,WAAW;AAAA,EACX;AAAA,EACD,uBAAuB;AAAA,IACtB,OAAO;AAAA,IACP,OAAO;AAAA,IACP,WAAW;AAAA,EACX;AAAA,EACD,cAAc;AAAA,IACb,OAAO;AAAA,IACP,WAAW;AAAA,EACX;AAAA,EACD,mBAAmB,CAAAA,OAAK,OAAO,OAAOA,IAAG;AAAA,IACxC,YAAY,CAACA,IAAGG,OAAM;AACrB,MAAAA,GAAE,KAAK,cAAcH,GAAE,CAAC;AAAA,IACxB;AAAA,IACD,UAAU,CAACA,IAAGG,OAAM;AACnB,MAAAA,GAAE,KAAK,gBAAgBH,GAAE,CAAC,KAAKG,GAAE,YAAa;AAAA,IAC9C;AAAA,EACH,CAAE;AACF,CAAC;AAED,SAAS,EAAEH,IAAGG,IAAG;AAChB,UAAQH,GAAE,MAAMA,GAAE,QAAQ,CAAC,KAAKG,GAAE,YAAa;AAChD;AAEA,SAAS,EAAEH,IAAGG,IAAG;AAChB,aAAWH,GAAE,cAAcA,GAAE,QAAQA,GAAE,WAAW,OAAOA,GAAE;AAC5D;AAEA,SAAS,EAAEA,IAAGG,IAAG;AAChB,EAAAA,MAAKH,GAAE,kBAAkBA,GAAE,QAAQ,SAASA,GAAE,cAAc,MAAM,GAAG,EAAE,KAAK,GAAG,IAAI,uBAClFA,GAAE,gBAAgB,GAAGA,GAAE,WAAWA,GAAE,YAAYA,GAAE,eAAe,OAAOA,GAAE,eAC1E,WAAWA,GAAE,cAAcA,GAAE,YAAY;AAC3C;AAEA,SAAS,EAAEA,IAAGG,IAAG;AAChB,QAAM,QAAQH,GAAE,OAAO,MAAMA,GAAE,UAAU,EAAE,GAAGA,GAAE,OAAO;AACxD;AAEA,SAAS,EAAEA,IAAGG,IAAG;AAChB,MAAIH,GAAE,OAAO;AACZ,QAAIA,GAAE,SAASA,GAAE;AAAK,YAAM,MAAM,0CAA0C;AAC5E,IAAAA,GAAE,QAAQA,GAAE,OAAO,OAAOA,GAAE;AAAA,EAC5B;AACF;AAEA,SAAS,EAAEA,IAAGG,IAAG;AAChB,aAAWH,GAAE,cAAcA,GAAE,YAAY;AAC1C;AACA,MAAM,IAAI,CAACA,IAAGG,OAAM;AAClB,MAAI,CAACH,GAAE;AAAa;AACpB,MAAIA,GAAE;AAAQ,UAAM,MAAM,wCAAwC;AAClE,QAAMC,KAAI,OAAO,OAAO,CAAE,GAAED,EAAC;AAC7B,SAAO,KAAKA,EAAC,EAAE,QAAS,CAAAG,OAAK;AAC5B,WAAOH,GAAEG,EAAC;AAAA,EACb,CAAK,GAAEH,GAAE,WAAWC,GAAE,UAAUD,GAAE,QAAQ,EAAEC,GAAE,aAAa,EAAEA,GAAE,KAAK,CAAC,GAAGD,GAAE,SAAS;AAAA,IAChF,WAAW;AAAA,IACX,UAAU,CAAC,OAAO,OAAOC,IAAG;AAAA,MAC3B,YAAY;AAAA,IAChB,CAAI,CAAC;AAAA,EACF,GAAED,GAAE,YAAY,GAAG,OAAOC,GAAE;AAC7B,GACD,IAAI,CAAC,MAAM,OAAO,OAAO,MAAM,OAAO,MAAM,MAAM,QAAQ,UAAU,QAAQ,OAAO;AAEpF,SAAS,EAAED,IAAGG,IAAGF,KAAI,WAAW;AAC/B,QAAMC,KAAI,uBAAO,OAAO,IAAI;AAC5B,SAAO,YAAY,OAAOF,KAAII,GAAEH,IAAGD,GAAE,MAAM,GAAG,CAAC,IAAI,MAAM,QAAQA,EAAC,IAAII,GAAEH,IAAGD,EAAC,IAAI,OAAO,KAAKA,EAAC,EAAE,QAAS,CAAAC,OAAK;AAC5G,WAAO,OAAOC,IAAG,EAAEF,GAAEC,EAAC,GAAGE,IAAGF,EAAC,CAAC;AAAA,EAC9B,CAAA,GAAIC;AAEL,WAASE,GAAEJ,IAAGC,IAAG;AAChB,IAAAE,OAAMF,KAAIA,GAAE,IAAK,CAAAD,OAAKA,GAAE,YAAW,KAAOC,GAAE,QAAS,CAAAE,OAAK;AACzD,YAAMF,KAAIE,GAAE,MAAM,GAAG;AACrB,MAAAD,GAAED,GAAE,CAAC,CAAC,IAAI,CAACD,IAAG,EAAEC,GAAE,CAAC,GAAGA,GAAE,CAAC,CAAC,CAAC;AAAA,IAC9B,CAAK;AAAA,EACH;AACF;AAEA,SAAS,EAAED,IAAGG,IAAG;AAChB,SAAOA,KAAI,OAAOA,EAAC,KAAK,CAAAH,OAAK,EAAE,SAASA,GAAE,YAAa,CAAA,GAAGA,EAAC,IAAI,IAAI;AACpE;AACA,MAAM,IAAI,CAAE,GACX,IAAI,CAAAA,OAAK;AACRM,gBAAAA,MAAA,MAAA,SAAA,oEAAcN,EAAC;AACf,GACD,IAAI,CAACA,OAAMG,OAAM;AAChBG,uGAAY,WAAWN,IAAG,GAAGG,EAAC;AAC9B,GACD,IAAI,CAACH,IAAGG,OAAM;AACb,IAAE,GAAGH,EAAC,IAAIG,EAAC,EAAE,MAAMG,cAAAA,MAAA,MAAA,OAAA,oEAAY,oBAAoBN,EAAC,KAAKG,EAAC,EAAE,GAAG,EAAE,GAAGH,EAAC,IAAIG,EAAC,EAAE,IAAI;AAChF,GACD,IAAI,MAAK;AAEV,SAAS,EAAEH,IAAGG,IAAG;AAAA,EAChB,KAAKF;AACN,GAAG;AACF,MAAIC,KAAI;AACR,QAAME,KAAIJ,GAAEC,EAAC,GACZI,KAAI,CAAE,GACNE,KAAI,CAAA;AACL,WAASP,KAAI,GAAGA,MAAKG,GAAE,QAAQH;AAAK,IAAAO,GAAEP,KAAIE,EAAC,IAAIE,GAAEJ,EAAC,GAAGK,GAAEL,KAAIE,EAAC,IAAI,MAAIA,MAAK,EAAEC,GAAEH,KAAI,CAAC,CAAC;AACnF,EAAAA,GAAEC,EAAC,IAAIM,IAAGP,GAAEC,EAAC,EAAE,QAAQI,IAAGL,GAAEC,EAAC,EAAE,SAAS;AACzC;AAEA,SAAS,EAAED,IAAG;AACb,GAAC,CAAAA,OAAK;AACL,IAAAA,GAAE,SAAS,YAAY,OAAOA,GAAE,SAAS,SAASA,GAAE,UAAUA,GAAE,aAAaA,GAAE,OAC9E,OAAOA,GAAE;AAAA,EACZ,GAAIA,EAAC,GAAG,YAAY,OAAOA,GAAE,eAAeA,GAAE,aAAa;AAAA,IACzD,OAAOA,GAAE;AAAA,EACX,IAAK,YAAY,OAAOA,GAAE,aAAaA,GAAE,WAAW;AAAA,IAClD,OAAOA,GAAE;AAAA,EACX,KAAM,CAAAA,OAAK;AACT,QAAI,MAAM,QAAQA,GAAE,KAAK,GAAG;AAC3B,UAAIA,GAAE,QAAQA,GAAE,gBAAgBA,GAAE;AAAa,cAAM;AAAA,UACnD;AAAA,QAAoE,GACrE;AACD,UAAI,YAAY,OAAOA,GAAE,cAAc,SAASA,GAAE;AAAY,cAAM,EAAE,2BAA2B,GAChG;AACD,QAAEA,IAAGA,GAAE,OAAO;AAAA,QACb,KAAK;AAAA,MACL,CAAA,GAAGA,GAAE,QAAQ,EAAEA,GAAE,OAAO;AAAA,QACxB,UAAU;AAAA,MACd,CAAI;AAAA,IACD;AAAA,EACH,GAAIA,EAAC,IAAI,CAAAA,OAAK;AACZ,QAAI,MAAM,QAAQA,GAAE,GAAG,GAAG;AACzB,UAAIA,GAAE,QAAQA,GAAE,cAAcA,GAAE;AAAW,cAAM;AAAA,UAC/C;AAAA,QAA8D,GAC/D;AACD,UAAI,YAAY,OAAOA,GAAE,YAAY,SAASA,GAAE;AAAU,cAAM,EAAE,yBAAyB,GAC1F;AACD,QAAEA,IAAGA,GAAE,KAAK;AAAA,QACX,KAAK;AAAA,MACL,CAAA,GAAGA,GAAE,MAAM,EAAEA,GAAE,KAAK;AAAA,QACpB,UAAU;AAAA,MACd,CAAI;AAAA,IACD;AAAA,EACD,GAAEA,EAAC;AACL;AAEA,SAAS,EAAEA,IAAG;AACb,WAASG,GAAEA,IAAGF,IAAG;AAChB,WAAO,OAAO,EAAEE,EAAC,GAAG,OAAOH,GAAE,mBAAmB,MAAM,OAAOA,GAAE,eAAe,MAAM,OAAOC,KAAI,MAAM,GAAG;AAAA,EACxG;AAAA,EACD,MAAMA,GAAE;AAAA,IACP,cAAc;AACb,WAAK,eAAe,IAAI,KAAK,UAAU,CAAA,GAAI,KAAK,UAAU,GAAG,KAAK,WAAW;AAAA,IAC7E;AAAA,IACD,QAAQD,IAAGG,IAAG;AACb,MAAAA,GAAE,WAAW,KAAK,YAAY,KAAK,aAAa,KAAK,OAAO,IAAIA,IAAG,KAAK,QAAQ,KAAK,CAACA,IAAGH,EAAC,CAAC,GAC1F,KAAK,WAAW,EAAEA,EAAC,IAAI;AAAA,IACxB;AAAA,IACD,UAAU;AACT,YAAM,KAAK,QAAQ,WAAW,KAAK,OAAO,MAAM;AAChD,YAAMA,KAAI,KAAK,QAAQ,IAAK,CAAAA,OAAKA,GAAE,CAAC;AACpC,WAAK,YAAYG,GAAE,EAAEH,IAAG;AAAA,QACvB,UAAU;AAAA,MACV,CAAA,GAAG,IAAE,GAAG,KAAK,YAAY;AAAA,IAC1B;AAAA,IACD,KAAKA,IAAG;AACP,WAAK,UAAU,YAAY,KAAK;AAChC,YAAMG,KAAI,KAAK,UAAU,KAAKH,EAAC;AAC/B,UAAI,CAACG;AAAG,eAAO;AACf,YAAMF,KAAIE,GAAE,UAAW,CAACH,IAAGG,OAAMA,KAAI,KAAK,WAAWH,EAAG,GACvDE,KAAI,KAAK,aAAaD,EAAC;AACxB,aAAOE,GAAE,OAAO,GAAGF,EAAC,GAAG,OAAO,OAAOE,IAAGD,EAAC;AAAA,IACzC;AAAA,EACD;AAAA,EACD,MAAMA,GAAE;AAAA,IACP,cAAc;AACb,WAAK,QAAQ,CAAA,GAAI,KAAK,eAAe,CAAE,GACtC,KAAK,QAAQ,GAAG,KAAK,YAAY,GAAG,KAAK,aAAa;AAAA,IACvD;AAAA,IACD,WAAWF,IAAG;AACb,UAAI,KAAK,aAAaA,EAAC;AAAG,eAAO,KAAK,aAAaA,EAAC;AACpD,YAAMG,KAAI,IAAIF;AACd,aAAO,KAAK,MAAM,MAAMD,EAAC,EAAE,QAAS,CAAC,CAACA,IAAGC,EAAC,MAAME,GAAE,QAAQH,IAAGC,EAAC,CAAG,GAChEE,GAAE,QAAO,GAAI,KAAK,aAAaH,EAAC,IAAIG,IAAGA;AAAA,IACxC;AAAA,IACD,6BAA6B;AAC5B,aAAO,MAAM,KAAK;AAAA,IAClB;AAAA,IACD,cAAc;AACb,WAAK,aAAa;AAAA,IAClB;AAAA,IACD,QAAQH,IAAGG,IAAG;AACb,WAAK,MAAM,KAAK,CAACH,IAAGG,EAAC,CAAC,GAAG,YAAYA,GAAE,QAAQ,KAAK;AAAA,IACpD;AAAA,IACD,KAAKH,IAAG;AACP,YAAMG,KAAI,KAAK,WAAW,KAAK,UAAU;AACzC,MAAAA,GAAE,YAAY,KAAK;AACnB,UAAIF,KAAIE,GAAE,KAAKH,EAAC;AAChB,UAAI,KAAK,2BAA4B;AACpC,YAAIC,MAAKA,GAAE,UAAU,KAAK;AAAU;AAAA,aAC/B;AACJ,gBAAME,KAAI,KAAK,WAAW,CAAC;AAC3B,UAAAA,GAAE,YAAY,KAAK,YAAY,GAAGF,KAAIE,GAAE,KAAKH,EAAC;AAAA,QAC9C;AACF,aAAOC,OAAM,KAAK,cAAcA,GAAE,WAAW,GAC5C,KAAK,eAAe,KAAK,SAAS,KAAK,YAAa,IAAGA;AAAA,IACxD;AAAA,EACD;AACD,MAAID,GAAE,uBAAuBA,GAAE,qBAAqB,CAAA,IACnDA,GAAE,YAAYA,GAAE,SAAS,SAAS,MAAM;AAAG,UAAM;AAAA,MACjD;AAAA,IAA2F;AAC5F,SAAOA,GAAE,mBAAmB,EAAEA,GAAE,oBAAoB,CAAA,CAAE,GACrD,SAASC,GAAEI,IAAGE,IAAG;AAChB,UAAMC,KAAIH;AACV,QAAIA,GAAE;AAAY,aAAOG;AACzB,KAAC,GAAG,GAAG,GAAG,CAAC,EAAE,QAAS,CAAAR,OAAKA,GAAEK,IAAGE,EAAC,IAAKP,GAAE,mBAAmB,QAAS,CAAAA,OAAKA,GAAEK,IAAGE,EAAC,CAAG,GACjFF,GAAE,gBAAgB,MAAM,CAAC,GAAG,GAAG,CAAC,EAAE,QAAS,CAAAL,OAAKA,GAAEK,IAAGE,EAAC,CAAC,GAAIF,GAAE,aAAa;AAC3E,QAAII,KAAI;AACR,WAAO,YAAY,OAAOJ,GAAE,YAAYA,GAAE,SAAS,aAAaA,GAAE,WAAW,OAAO,OAAO,CAAA,GAAIA,GAAE,QAAQ,GACvGI,KAAIJ,GAAE,SAAS,UACf,OAAOA,GAAE,SAAS,WAAWI,KAAIA,MAAK,OAAOJ,GAAE,aAAaA,GAAE,WAAW,EAAEA,GAAE,UAAUL,GAAE,gBAAgB,IAC1GQ,GAAE,mBAAmBL,GAAEM,IAAG,IAAE,GAC5BF,OAAMF,GAAE,UAAUA,GAAE,QAAQ,UAAUG,GAAE,UAAUL,GAAEK,GAAE,KAAK,GAAGH,GAAE,OAAOA,GAAE,mBAAmBA,GAAE,MAAM,UACnGA,GAAE,QAAQG,GAAE,QAAQL,GAAEK,GAAE,GAAG,IAC3BA,GAAE,gBAAgB,EAAEA,GAAE,GAAG,KAAK,IAAIH,GAAE,kBAAkBE,GAAE,kBAAkBC,GAAE,kBAAkBH,GAAE,MAAM,MACrG,MAAME,GAAE,iBACVF,GAAE,YAAYG,GAAE,YAAYL,GAAEE,GAAE,OAAO,IACvCA,GAAE,aAAaA,GAAE,WAAW,CAAE,IAAGA,GAAE,WAAW,GAAG,OAAO,GAAGA,GAAE,SAAS,IAAK,CAAAL,QAAM,CAAAA,QAAMA,GAAE,YAAY,CAACA,GACpG,mBAAmBA,GAAE,iBAAiBA,GAAE,SAAS,IAAK,CAAAG,OAAK,EAAEH,IAAG;AAAA,MAChE,UAAU;AAAA,IACV,GAAEG,EAAC,CAAC,IAAKH,GAAE,iBAAiBA,GAAE,iBAAiB,EAAEA,EAAC,IAAI,EAAEA,IAAG;AAAA,MAC3D,QAAQA,GAAE,SAAS,EAAEA,GAAE,MAAM,IAAI;AAAA,IACvC,CAAM,IAAI,OAAO,SAASA,EAAC,IAAI,EAAEA,EAAC,IAAIA,KAAI,WAAWA,KAAIK,KAAIL,EAAC,CAAG,CAAA,GAAGK,GAAE,SAAS,QAAS,CAAAL,OAAK;AACxF,MAAAC,GAAED,IAAGQ,EAAC;AAAA,IACN,CAAA,GAAIH,GAAE,UAAUJ,GAAEI,GAAE,QAAQE,EAAC,GAAGC,GAAE,WAAW,CAAAR,OAAK;AAClD,YAAMG,KAAI,IAAID;AACd,aAAOF,GAAE,SAAS,QAAS,CAAAA,OAAKG,GAAE,QAAQH,GAAE,OAAO;AAAA,QAClD,MAAMA;AAAA,QACN,MAAM;AAAA,MACZ,CAAM,CAAC,GAAIA,GAAE,iBAAiBG,GAAE,QAAQH,GAAE,eAAe;AAAA,QACnD,MAAM;AAAA,MACZ,CAAM,GAAGA,GAAE,WAAWG,GAAE,QAAQH,GAAE,SAAS;AAAA,QACrC,MAAM;AAAA,MACN,CAAA,GAAGG;AAAA,IACT,GAAOK,EAAC,GAAGA;AAAA,EACR,EAACR,EAAC;AACL;AAEA,SAAS,EAAEA,IAAG;AACb,SAAO,CAAC,CAACA,OAAMA,GAAE,kBAAkB,EAAEA,GAAE,MAAM;AAC9C;AACA,MAAM,UAAU,MAAM;AAAA,EACrB,YAAYA,IAAGG,IAAG;AACjB,UAAMH,EAAC,GAAG,KAAK,OAAO,sBAAsB,KAAK,OAAOG;AAAA,EACxD;AACF;AACA,MAAM,IAAI,GACT,IAAI,GACJ,IAAI,OAAO,SAAS;AACrB,IAAI,KAAK,CAAAA,OAAK;AACb,QAAMD,KAAI,uBAAO,OAAO,IAAI,GAC3BE,KAAI,uBAAO,OAAO,IAAI,GACtBC,KAAI,CAAA;AACL,MAAIE,KAAI;AACR,QAAMC,KAAI,uFACTC,KAAI;AAAA,IACH,mBAAmB;AAAA,IACnB,MAAM;AAAA,IACN,UAAU,CAAE;AAAA,EACf;AACC,MAAIC,KAAI;AAAA,IACP,qBAAqB;AAAA,IACrB,oBAAoB;AAAA,IACpB,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,aAAa;AAAA,IACb,aAAa;AAAA,IACb,WAAW;AAAA,IACX,WAAW;AAAA,EACb;AAEC,WAASC,GAAEX,IAAG;AACb,WAAOU,GAAE,cAAc,KAAKV,EAAC;AAAA,EAC7B;AAED,WAASY,GAAEZ,IAAGG,IAAGF,IAAG;AACnB,QAAIC,KAAI,IACPE,KAAI;AACL,gBAAY,OAAOD,MAAKD,KAAIF,IAC3BC,KAAIE,GAAE,gBAAgBC,KAAID,GAAE,aAAa,EAAE,UAAU,qDAAqD,GAC1G;AAAA,MAAE;AAAA,MACD;AAAA,IAAuG,GACxGC,KAAIJ,IAAGE,KAAIC,KAAI,WAAWF,OAAMA,KAAI;AACrC,UAAMI,KAAI;AAAA,MACT,MAAMH;AAAA,MACN,UAAUE;AAAA,IACb;AACE,IAAAS,GAAE,oBAAoBR,EAAC;AACvB,UAAME,KAAIF,GAAE,SAASA,GAAE,SAASS,GAAET,GAAE,UAAUA,GAAE,MAAMJ,EAAC;AACvD,WAAOM,GAAE,OAAOF,GAAE,MAAMQ,GAAE,mBAAmBN,EAAC,GAAGA;AAAA,EACjD;AAED,WAASO,GAAEd,IAAGG,IAAGC,IAAGC,IAAG;AACtB,UAAMI,KAAI,uBAAO,OAAO,IAAI;AAE5B,aAASM,KAAI;AACZ,UAAI,CAACC,GAAE;AAAU,eAAO,KAAKC,GAAE,QAAQC,EAAC;AACxC,UAAIlB,KAAI;AACR,MAAAgB,GAAE,iBAAiB,YAAY;AAC/B,UAAIb,KAAIa,GAAE,iBAAiB,KAAKE,EAAC,GAChCjB,KAAI;AACL,aAAOE,MAAI;AACV,QAAAF,MAAKiB,GAAE,UAAUlB,IAAGG,GAAE,KAAK;AAC3B,cAAMC,KAAIe,GAAE,mBAAmBhB,GAAE,CAAC,EAAE,YAAW,IAAKA,GAAE,CAAC,GACtDE,MAAKH,KAAIE,IAAGY,GAAE,SAASd,EAAC;AACzB,YAAIG,IAAG;AACN,gBAAM,CAACL,IAAGE,EAAC,IAAIG;AAEf,cAAIY,GAAE,QAAQhB,EAAC,GAAGA,KAAI,IAAIQ,GAAEL,EAAC,KAAKK,GAAEL,EAAC,KAAK,KAAK,GAAGK,GAAEL,EAAC,KAAK,MAAMgB,MAAKlB,KAAIF,GAAE,WAAW,GAAG;AAAG,YAAAC,MAAKE,GAAE,CAAC;AAAA,eAC/F;AACJ,kBAAMF,KAAIkB,GAAE,iBAAiBnB,EAAC,KAAKA;AACnC,YAAAiB,GAAE,WAAWd,GAAE,CAAC,GAAGF,EAAC;AAAA,UACpB;AAAA,QACN;AAAW,UAAAA,MAAKE,GAAE,CAAC;AACf,QAAAH,KAAIgB,GAAE,iBAAiB,WAAWb,KAAIa,GAAE,iBAAiB,KAAKE,EAAC;AAAA,MAC/D;AACD,UAAIhB;AACJ,MAAAD,MAAKiB,GAAE,UAAUlB,EAAC,GAAGiB,GAAE,QAAQhB,EAAC;AAAA,IAChC;AAED,aAASoB,KAAI;AACZ,cAAQL,GAAE,eAAe,MAAM;AAC9B,YAAI,OAAOE;AAAG;AACd,YAAIlB,KAAI;AACR,YAAI,YAAY,OAAOgB,GAAE,aAAa;AACrC,cAAI,CAACd,GAAEc,GAAE,WAAW;AAAG,mBAAO,KAAKC,GAAE,QAAQC,EAAC;AAC9C,UAAAlB,KAAIc,GAAEE,GAAE,aAAaE,IAAG,MAAIL,GAAEG,GAAE,WAAW,CAAC,GAAGH,GAAEG,GAAE,WAAW,IAAIhB,GAAE;AAAA,QACzE;AAAW,UAAAA,KAAIsB,GAAEJ,IAAGF,GAAE,YAAY,SAASA,GAAE,cAAc,IAAI;AAC3D,QAAAA,GAAE,YAAY,MAAMI,MAAKpB,GAAE,YAAYiB,GAAE,eAAejB,GAAE,UAAUA,GAAE,QAAQ;AAAA,MAClF,OAAUe,MAAKG,KAAI;AAAA,IAChB;AAED,aAASK,GAAEvB,IAAGG,IAAG;AAChB,UAAIF,KAAI;AACR,YAAMC,KAAIC,GAAE,SAAS;AACrB,aAAOF,MAAKC,MAAI;AACf,YAAI,CAACF,GAAE,MAAMC,EAAC,GAAG;AAChB,UAAAA;AACA;AAAA,QACA;AACD,cAAMC,KAAIiB,GAAE,iBAAiBnB,GAAEC,EAAC,CAAC,KAAKD,GAAEC,EAAC,GACxCG,KAAID,GAAEF,EAAC;AACR,QAAAC,KAAIe,GAAE,WAAWb,IAAGF,EAAC,KAAKgB,KAAId,IAAGW,GAAC,GAAIG,KAAI,KAAKjB;AAAA,MAC/C;AAAA,IACD;AAED,aAASuB,GAAExB,IAAGG,IAAG;AAChB,aAAOH,GAAE,SAAS,YAAY,OAAOA,GAAE,SAASiB,GAAE,SAASE,GAAE,iBAAiBnB,GAAE,KAAK,KAAKA,GAAE,KAAK,GAChGA,GAAE,eAAeA,GAAE,WAAW,SAASiB,GAAE,WAAWC,IAAGC,GAAE,iBAAiBnB,GAAE,WAAW,KAAK,KAAKA,GAC9F,WAAW,KAAK,GAClBkB,KAAI,MAAMlB,GAAE,WAAW,WAAWuB,GAAEvB,GAAE,YAAYG,EAAC,GAAGe,KAAI,MAAMF,KAAI,OAAO,OAAOhB,IAAG;AAAA,QACrF,QAAQ;AAAA,UACP,OAAOgB;AAAA,QACP;AAAA,MACD,CAAA,GAAGA;AAAA,IACL;AAED,aAASS,GAAEzB,IAAGG,IAAGD,IAAG;AACnB,UAAIE,MAAK,CAACJ,IAAGG,OAAM;AAClB,cAAMF,KAAID,MAAKA,GAAE,KAAKG,EAAC;AACvB,eAAOF,MAAK,MAAMA,GAAE;AAAA,MACxB,GAAMD,GAAE,OAAOE,EAAC;AACb,UAAIE,IAAG;AACN,YAAIJ,GAAE,QAAQ,GAAG;AAChB,gBAAME,KAAI,IAAI,EAAEF,EAAC;AACjB,UAAAA,GAAE,QAAQ,EAAEG,IAAGD,EAAC,GAAGA,GAAE,mBAAmBE,KAAI;AAAA,QAC5C;AACD,YAAIA,IAAG;AACN,iBAAOJ,GAAE,cAAcA,GAAE;AAAS,YAAAA,KAAIA,GAAE;AACxC,iBAAOA;AAAA,QACP;AAAA,MACD;AACD,UAAIA,GAAE;AAAgB,eAAOyB,GAAEzB,GAAE,QAAQG,IAAGD,EAAC;AAAA,IAC7C;AAED,aAASwB,GAAE1B,IAAG;AACb,aAAO,MAAMgB,GAAE,QAAQ,cAAcE,MAAKlB,GAAE,CAAC,GAAG,MAAM2B,KAAI,MAAI;AAAA,IAC9D;AAED,aAAShB,GAAEX,IAAG;AACb,YAAMC,KAAID,GAAE,CAAC,GACZE,KAAIC,GAAE,UAAUH,GAAE,KAAK,GACvBI,KAAIqB,GAAET,IAAGhB,IAAGE,EAAC;AACd,UAAI,CAACE;AAAG,eAAO;AACf,YAAMC,KAAIW;AACV,MAAAA,GAAE,YAAYA,GAAE,SAAS,SAASK,GAAG,GACpCJ,GAAE,WAAWhB,IAAGe,GAAE,SAAS,KAAK,KAAKA,GAAE,YAAYA,GAAE,SAAS,UAAUK,GAAG,GAC3EE,GAAEP,GAAE,UAAUhB,EAAC,KAAKK,GAAE,OAAOa,MAAKjB,MAAKI,GAAE,aAAaA,GAAE,eAAea,MAAKjB,KAC5EoB,GAAC,GAAIhB,GAAE,eAAea,KAAIjB;AAC3B,SAAG;AACF,QAAAe,GAAE,SAASC,GAAE,UAAS,GAAID,GAAE,QAAQA,GAAE,gBAAgBI,MAAKJ,GAAE,YAAYA,KAAIA,GAAE;AAAA,MACnF,SAAYA,OAAMZ,GAAE;AACjB,aAAOA,GAAE,UAAUoB,GAAEpB,GAAE,QAAQJ,EAAC,GAAGK,GAAE,YAAY,IAAIJ,GAAE;AAAA,IACvD;AACD,QAAIW,KAAI,CAAA;AAER,aAASgB,GAAE1B,IAAGG,IAAG;AAChB,YAAMG,KAAIH,MAAKA,GAAE,CAAC;AAClB,UAAIa,MAAKhB,IAAG,QAAQM;AAAG,eAAOa,GAAG,GAAE;AACnC,UAAI,YAAYT,GAAE,QAAQ,UAAUP,GAAE,QAAQO,GAAE,UAAUP,GAAE,SAAS,OAAOG,IAAG;AAC9E,YAAIU,MAAKf,GAAE,MAAME,GAAE,OAAOA,GAAE,QAAQ,CAAC,GAAG,CAACE,IAAG;AAC3C,gBAAMJ,KAAI,MAAM,wBAAwBH,EAAC,GAAG;AAC5C,gBAAMG,GAAE,eAAeH,IAAGG,GAAE,UAAUS,GAAE,MAAMT;AAAA,QAC9C;AACD,eAAO;AAAA,MACP;AACD,UAAIS,KAAIP,IAAG,YAAYA,GAAE;AAAM,gBAAQ,CAAAL,OAAK;AAC3C,gBAAMG,KAAIH,GAAE,CAAC,GACZE,KAAIF,GAAE,MACNI,KAAI,IAAI,EAAEF,EAAC,GACXG,KAAI,CAACH,GAAE,eAAeA,GAAE,UAAU,CAAC;AACpC,qBAAWD,MAAKI;AACf,gBAAIJ,OAAMA,GAAED,IAAGI,EAAC,GAAGA,GAAE;AAAiB,qBAAOsB,GAAEvB,EAAC;AACjD,iBAAOD,GAAE,OAAOgB,MAAKf,MAAKD,GAAE,iBAAiBgB,MAAKf,KACjDkB,GAAC,GAAInB,GAAE,eAAeA,GAAE,iBAAiBgB,KAAIf,MAAKqB,GAAEtB,IAAGF,EAAC,GAAGE,GAAE,cAAc,IAAIC,GAAE;AAAA,QAClF,GAAEE,EAAC;AACJ,UAAI,cAAcA,GAAE,QAAQ,CAACD,IAAG;AAC/B,cAAMJ,KAAI,MAAM,qBAAqBQ,KAAI,kBAAkBQ,GAAE,SAAS,eAAe,GAAG;AACxF,cAAMhB,GAAE,OAAOgB,IAAGhB;AAAA,MAClB;AACD,UAAI,UAAUK,GAAE,MAAM;AACrB,cAAML,KAAIW,GAAEN,EAAC;AACb,YAAIL,OAAM;AAAG,iBAAOA;AAAA,MACpB;AACD,UAAI,cAAcK,GAAE,QAAQ,OAAOG;AAAG,eAAO;AAC7C,UAAIqB,KAAI,OAAOA,KAAI,IAAIxB,GAAE;AAAO,cAAM,MAAM,2DAA2D;AACvG,aAAOa,MAAKV,IAAGA,GAAE;AAAA,IACjB;AACD,UAAMW,KAAIW,GAAE9B,EAAC;AACb,QAAI,CAACmB;AAAG,YAAM,EAAEX,GAAE,QAAQ,MAAMR,EAAC,CAAC,GAAG,MAAM,wBAAwBA,KAAI,GAAG;AAC1E,UAAM+B,KAAI,EAAEZ,EAAC;AACb,QAAIa,KAAI,IACPhB,KAAIX,MAAK0B;AACV,UAAMlB,KAAI,CAAE,GACXI,KAAI,IAAIP,GAAE,UAAUA,EAAC;AACtB,KAAC,MAAM;AACN,YAAMV,KAAI,CAAA;AACV,eAASG,KAAIa,IAAGb,OAAMgB,IAAGhB,KAAIA,GAAE;AAAQ,QAAAA,GAAE,SAASH,GAAE,QAAQG,GAAE,KAAK;AACnE,MAAAH,GAAE,QAAS,CAAAA,OAAKiB,GAAE,SAASjB,EAAC,CAAG;AAAA,IAClC;AACE,QAAIkB,KAAI,IACPE,KAAI,GACJa,KAAI,GACJJ,KAAI,GACJF,KAAI;AACL,QAAI;AACH,WAAKX,GAAE,QAAQ,mBAAiB;AAC/B,QAAAa,MAAKF,KAAIA,KAAI,QAAKX,GAAE,QAAQ,YAAa,GAAEA,GAAE,QAAQ,YAAYiB;AACjE,cAAMjC,KAAIgB,GAAE,QAAQ,KAAKb,EAAC;AAC1B,YAAI,CAACH;AAAG;AACR,cAAMC,KAAI2B,GAAEzB,GAAE,UAAU8B,IAAGjC,GAAE,KAAK,GAAGA,EAAC;AACtC,QAAAiC,KAAIjC,GAAE,QAAQC;AAAA,MACd;AACD,aAAO2B,GAAEzB,GAAE,UAAU8B,EAAC,CAAC,GAAGhB,GAAE,cAAa,GAAIA,GAAE,SAAU,GAAEe,KAAIf,GAAE,OAAM,GAAI;AAAA,QAC1E,UAAUjB;AAAA,QACV,OAAOgC;AAAA,QACP,WAAWZ;AAAA,QACX,SAAS;AAAA,QACT,UAAUH;AAAA,QACV,MAAMD;AAAA,MACN;AAAA,IACD,SAAQf,IAAG;AACX,UAAIA,GAAE,WAAWA,GAAE,QAAQ,SAAS,SAAS;AAAG,eAAO;AAAA,UACtD,UAAUD;AAAA,UACV,OAAO,EAAEG,EAAC;AAAA,UACV,SAAS;AAAA,UACT,WAAW;AAAA,UACX,YAAY;AAAA,YACX,SAASF,GAAE;AAAA,YACX,OAAOgC;AAAA,YACP,SAAS9B,GAAE,MAAM8B,KAAI,KAAKA,KAAI,GAAG;AAAA,YACjC,MAAMhC,GAAE;AAAA,YACR,aAAa+B;AAAA,UACb;AAAA,UACD,UAAUf;AAAA,QACd;AACG,UAAIV;AAAG,eAAO;AAAA,UACb,UAAUP;AAAA,UACV,OAAO,EAAEG,EAAC;AAAA,UACV,SAAS;AAAA,UACT,WAAW;AAAA,UACX,aAAaF;AAAA,UACb,UAAUgB;AAAA,UACV,MAAMD;AAAA,QACV;AACG,YAAMf;AAAA,IACN;AAAA,EACD;AAED,WAASqB,GAAEtB,IAAGG,IAAG;AAChB,IAAAA,KAAIA,MAAKO,GAAE,aAAa,OAAO,KAAKR,EAAC;AACrC,UAAMD,MAAK,CAAAD,OAAK;AACd,YAAMG,KAAI;AAAA,QACT,OAAO,EAAEH,EAAC;AAAA,QACV,SAAS;AAAA,QACT,WAAW;AAAA,QACX,MAAMS;AAAA,QACN,UAAU,IAAIC,GAAE,UAAUA,EAAC;AAAA,MAChC;AACI,aAAOP,GAAE,SAAS,QAAQH,EAAC,GAAGG;AAAA,IAC9B,GAAEH,EAAC,GACJI,KAAID,GAAE,OAAO2B,EAAC,EAAE,OAAOd,EAAC,EAAE,IAAK,CAAAb,OAAKW,GAAEX,IAAGH,IAAG,KAAE,CAAC;AAChD,IAAAI,GAAE,QAAQH,EAAC;AACX,UAAMI,KAAID,GAAE,KAAM,CAACJ,IAAGG,OAAM;AAC1B,UAAIH,GAAE,cAAcG,GAAE;AAAW,eAAOA,GAAE,YAAYH,GAAE;AACxD,UAAIA,GAAE,YAAYG,GAAE,UAAU;AAC7B,YAAI2B,GAAE9B,GAAE,QAAQ,EAAE,eAAeG,GAAE;AAAU,iBAAO;AACpD,YAAI2B,GAAE3B,GAAE,QAAQ,EAAE,eAAeH,GAAE;AAAU,iBAAO;AAAA,MACpD;AACD,aAAO;AAAA,IACX,CAAM,GACH,CAACO,IAAGC,EAAC,IAAIH,IACTU,KAAIR;AACL,WAAOQ,GAAE,aAAaP,IAAGO;AAAA,EACzB;AAED,WAASa,GAAE5B,IAAG;AACb,QAAIG,KAAI;AACR,UAAMF,MAAK,CAAAD,OAAK;AACf,UAAIG,KAAIH,GAAE,YAAY;AACtB,MAAAG,MAAKH,GAAE,aAAaA,GAAE,WAAW,YAAY;AAC7C,YAAMC,KAAIS,GAAE,iBAAiB,KAAKP,EAAC;AACnC,UAAIF,IAAG;AACN,cAAME,KAAI2B,GAAE7B,GAAE,CAAC,CAAC;AAChB,eAAOE,OAAM,EAAEK,GAAE,QAAQ,MAAMP,GAAE,CAAC,CAAC,CAAC,GACnC,EAAE,qDAAqDD,EAAC,IAAIG,KAAIF,GAAE,CAAC,IAAI;AAAA,MACxE;AACD,aAAOE,GAAE,MAAM,KAAK,EAAE,KAAM,CAAAH,OAAKW,GAAEX,EAAC,KAAK8B,GAAE9B,EAAC,CAAG;AAAA,IAC/C,GAAEA,EAAC;AACJ,QAAIW,GAAEV,EAAC;AAAG;AACV,QAAIY,GAAE,2BAA2B;AAAA,MAC/B,IAAIb;AAAA,MACJ,UAAUC;AAAA,IACV,CAAA,GAAGD,GAAE,SAAS,SAAS,MAAMU,GAAE,wBAAwBJ,cAAA,MAAA;AAAA,MAAA;AAAA,MAAA;AAAA,MACtD;AAAA,IAA+F,GAChGA,cAAAA,MAAA,MAAA,QAAA,oEAAa,2DAA2D,GACxEA,cAAAA,wFAAa,kCAAkC,GAC/CA,cAAAA,MAAA,MAAA,QAAA,oEAAaN,EAAC,IAAIU,GAAE;AAAqB,YAAM,IAAI,EAAE,oDAAoDV,GACzG,SAAS;AACX,IAAAG,KAAIH;AACJ,UAAME,KAAIC,GAAE,aACXE,KAAIJ,KAAIW,GAAEV,IAAG;AAAA,MACZ,UAAUD;AAAA,MACV,gBAAgB;AAAA,IACpB,CAAI,IAAIqB,GAAEpB,EAAC;AACT,IAAAF,GAAE,YAAYK,GAAE,QAAQ,CAACL,IAAGG,IAAGF,OAAM;AACpC,YAAMC,KAAIC,MAAKC,GAAED,EAAC,KAAKF;AACvB,MAAAD,GAAE,UAAU,IAAI,MAAM,GAAGA,GAAE,UAAU,IAAI,cAAcE,EAAC;AAAA,IAC3D,GAAKF,IAAGC,IAAGI,GAAE,QAAQ,GAAGL,GAAE,SAAS;AAAA,MAChC,UAAUK,GAAE;AAAA,MACZ,IAAIA,GAAE;AAAA,MACN,WAAWA,GAAE;AAAA,IACb,GAAEA,GAAE,eAAeL,GAAE,aAAa;AAAA,MAClC,UAAUK,GAAE,WAAW;AAAA,MACvB,WAAWA,GAAE,WAAW;AAAA,IAC3B,IAAMQ,GAAE,0BAA0B;AAAA,MAC/B,IAAIb;AAAA,MACJ,QAAQK;AAAA,MACR,MAAMH;AAAA,IACT,CAAG;AAAA,EACD;AACD,MAAIiB,KAAI;AAER,WAASY,KAAI;AACZ,kBAAc,SAAS,aAAa,SAAS,iBAAiBrB,GAAE,WAAW,EAAE,QAAQkB,EAAC,IAAIT,KAAI;AAAA,EAC9F;AAED,WAASW,GAAE9B,IAAG;AACb,WAAOA,MAAKA,MAAK,IAAI,YAAa,GAAEE,GAAEF,EAAC,KAAKE,GAAEE,GAAEJ,EAAC,CAAC;AAAA,EAClD;AAED,WAASgC,GAAEhC,IAAG;AAAA,IACb,cAAcG;AAAA,EAChB,GAAI;AACF,gBAAY,OAAOH,OAAMA,KAAI,CAACA,EAAC,IAAIA,GAAE,QAAS,CAAAA,OAAK;AAClD,MAAAI,GAAEJ,GAAE,YAAa,CAAA,IAAIG;AAAA,IACxB,CAAK;AAAA,EACH;AAED,WAASa,GAAEhB,IAAG;AACb,UAAMG,KAAI2B,GAAE9B,EAAC;AACb,WAAOG,MAAK,CAACA,GAAE;AAAA,EACf;AAED,WAASU,GAAEb,IAAGG,IAAG;AAChB,UAAMF,KAAID;AACV,IAAAK,GAAE,QAAS,CAAAL,OAAK;AACf,MAAAA,GAAEC,EAAC,KAAKD,GAAEC,EAAC,EAAEE,EAAC;AAAA,IACjB,CAAK;AAAA,EACH;AACD,iBAAe,OAAO,UAAU,OAAO,oBAAoB,OAAO,iBAAiB,oBAAqB,MAAM;AAC7G,IAAAgB,MAAKY,GAAG;AAAA,EACR,GAAG,KAAE,GAAG,OAAO,OAAO5B,IAAG;AAAA,IACzB,WAAWS;AAAA,IACX,eAAeU;AAAA,IACf,cAAcS;AAAA,IACd,kBAAkBH;AAAA,IAClB,gBAAgB,CAAA5B,QAAM,EAAE,UAAU,kDAAkD,GACnF,EAAE,UAAU,kCAAkC,GAAG4B,GAAE5B,EAAC;AAAA,IACrD,WAAW,CAAAA,OAAK;AACf,MAAAU,KAAI,EAAEA,IAAGV,EAAC;AAAA,IACV;AAAA,IACD,kBAAkB,MAAM;AACvB,MAAA+B,MAAK,EAAE,UAAU,yDAAyD;AAAA,IAC1E;AAAA,IACD,wBAAwB,MAAM;AAC7B,MAAAA,MAAK,EAAE,UAAU,+DAA+D;AAAA,IAChF;AAAA,IACD,kBAAkB,CAAC/B,IAAGC,OAAM;AAC3B,UAAIG,KAAI;AACR,UAAI;AACH,QAAAA,KAAIH,GAAEE,EAAC;AAAA,MACP,SAAQA,IAAG;AACX,YAAI,EAAE,wDAAwD,QAAQ,MAAMH,EAAC,CAAC,GAC7E,CAACO;AAAG,gBAAMJ;AACX,UAAEA,EAAC,GAAGC,KAAIK;AAAA,MACV;AACD,MAAAL,GAAE,SAASA,GAAE,OAAOJ,KAAIE,GAAEF,EAAC,IAAII,IAAGA,GAAE,gBAAgBH,GAAE,KAAK,MAAME,EAAC,GAAGC,GAAE,WAAW4B,GAAE5B,GAAE,SAAS;AAAA,QAC9F,cAAcJ;AAAA,MAClB,CAAI;AAAA,IACD;AAAA,IACD,oBAAoB,CAAAA,OAAK;AACxB,aAAOE,GAAEF,EAAC;AACV,iBAAWG,MAAK,OAAO,KAAKC,EAAC;AAAG,QAAAA,GAAED,EAAC,MAAMH,MAAK,OAAOI,GAAED,EAAC;AAAA,IACxD;AAAA,IACD,eAAe,MAAM,OAAO,KAAKD,EAAC;AAAA,IAClC,aAAa4B;AAAA,IACb,iBAAiBE;AAAA,IACjB,eAAehB;AAAA,IACf,SAAS;AAAA,IACT,WAAW,CAAAhB,OAAK;AACf,OAAC,CAAAA,OAAK;AACL,QAAAA,GAAE,uBAAuB,KAAK,CAACA,GAAE,yBAAyB,MAAMA,GAAE,yBAAyB,IAC1F,CAAAG,OAAK;AACJ,UAAAH,GAAE,uBAAuB,EAAE,OAAO,OAAO;AAAA,YACxC,OAAOG,GAAE;AAAA,UACT,GAAEA,EAAC,CAAC;AAAA,QACX,IAASH,GAAE,sBAAsB,KAAK,CAACA,GAAE,wBAAwB,MAAMA,GAAE,wBAAwB,IAC5F,CAAAG,OAAK;AACJ,UAAAH,GAAE,sBAAsB,EAAE,OAAO,OAAO;AAAA,YACvC,OAAOG,GAAE;AAAA,UACT,GAAEA,EAAC,CAAC;AAAA,QACX;AAAA,MACI,GAAEH,EAAC,GAAGK,GAAE,KAAKL,EAAC;AAAA,IACf;AAAA,EACH,CAAE,GAAGG,GAAE,YAAY,MAAM;AACvB,IAAAI,KAAI;AAAA,EACN,GAAIJ,GAAE,WAAW,MAAM;AACrB,IAAAI,KAAI;AAAA,EACJ,GAAEJ,GAAE,gBAAgB,UAAUA,GAAE,QAAQ;AAAA,IACxC,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,kBAAkB;AAAA,EACpB;AACC,aAAWA,MAAK;AAAG,gBAAY,OAAO,EAAEA,EAAC,KAAK,EAAE,QAAQ,EAAEA,EAAC,CAAC;AAC5D,SAAO,OAAO,OAAOA,IAAG,CAAC,GAAGA;AAC7B,GAAG,CAAE,CAAA;AACL,MAAM,IAAI,CAAAH,QAAM;AAAA,EACd,WAAW;AAAA,IACV,OAAO;AAAA,IACP,OAAO;AAAA,EACP;AAAA,EACD,eAAeA,GAAE;AAAA,EACjB,UAAU;AAAA,IACT,OAAO;AAAA,IACP,OAAO;AAAA,EACP;AAAA,EACD,mBAAmB;AAAA,IAClB,WAAW;AAAA,IACX,OAAO;AAAA,EACP;AAAA,EACD,yBAAyB;AAAA,IACxB,OAAO;AAAA,IACP,OAAO;AAAA,IACP,KAAK;AAAA,IACL,SAAS;AAAA,IACT,UAAU,CAACA,GAAE,kBAAkBA,GAAE,iBAAiB;AAAA,EAClD;AAAA,EACD,iBAAiB;AAAA,IAChB,OAAO;AAAA,IACP,OAAOA,GAAE,YACR;AAAA,IACD,WAAW;AAAA,EACX;AAAA,EACD,cAAc;AAAA,IACb,WAAW;AAAA,IACX,OAAO;AAAA,EACP;AACH,IACC,IAAI;AAAA,EAAC;AAAA,EAAK;AAAA,EAAQ;AAAA,EAAW;AAAA,EAAW;AAAA,EAAS;AAAA,EAAS;AAAA,EAAK;AAAA,EAAc;AAAA,EAAQ;AAAA,EAAU;AAAA,EAAU;AAAA,EACxG;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAM;AAAA,EAAO;AAAA,EAAW;AAAA,EAAO;AAAA,EAAO;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAY;AAAA,EAAc;AAAA,EAClG;AAAA,EAAU;AAAA,EAAQ;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAU;AAAA,EAAU;AAAA,EAAQ;AAAA,EAAK;AAAA,EAAU;AAAA,EAAO;AAAA,EACxG;AAAA,EAAO;AAAA,EAAO;AAAA,EAAS;AAAA,EAAU;AAAA,EAAM;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAO;AAAA,EAAU;AAAA,EAAM;AAAA,EAAK;AAAA,EAAK;AAAA,EAAS;AAAA,EACzG;AAAA,EAAW;AAAA,EAAQ;AAAA,EAAU;AAAA,EAAW;AAAA,EAAO;AAAA,EAAS;AAAA,EAAS;AAAA,EAAM;AAAA,EAAY;AAAA,EAAS;AAAA,EAAM;AAAA,EAAS;AAAA,EAC3G;AAAA,EAAM;AAAA,EAAM;AAAA,EAAO;AACnB,GACD,KAAK;AAAA,EAAC;AAAA,EAAa;AAAA,EAAe;AAAA,EAAgB;AAAA,EAAS;AAAA,EAAe;AAAA,EAAe;AAAA,EACxF;AAAA,EAAiB;AAAA,EAAgB;AAAA,EAAgB;AAAA,EAAiB;AAAA,EAAQ;AAAA,EAAU;AAAA,EAAS;AAAA,EAC7F;AAAA,EAAc;AAAA,EAAe;AAAA,EAAkB;AAAA,EAAmB;AAAA,EAAW;AAAA,EAC7E;AAAA,EAAoB;AAAA,EAA0B;AAAA,EAAgC;AAAA,EAAc;AAAA,EAAQ;AAAA,EACpG;AAAA,EAAU;AAAA,EAAS;AAAA,EAAa;AAAA,EAAa;AAAA,EAAc;AAC3D,GACD,KAAK;AAAA,EAAC;AAAA,EAAU;AAAA,EAAY;AAAA,EAAS;AAAA,EAAW;AAAA,EAAW;AAAA,EAAW;AAAA,EAAW;AAAA,EAAO;AAAA,EAAY;AAAA,EAAQ;AAAA,EAC3G;AAAA,EAAW;AAAA,EAAS;AAAA,EAAe;AAAA,EAAiB;AAAA,EAAc;AAAA,EAAU;AAAA,EAAS;AAAA,EACrF;AAAA,EAAgB;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAgB;AAAA,EAAS;AAAA,EAAiB;AAAA,EAAY;AAAA,EAAW;AAAA,EAAM;AAAA,EACtG;AAAA,EAAc;AAAA,EAAgB;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAc;AAAA,EAAO;AAAA,EAAa;AAAA,EAAW;AAAA,EAC3F;AAAA,EAAgB;AAAA,EAAoB;AAAA,EAAe;AAAA,EAAc;AAAA,EAAgB;AAAA,EAAY;AAAA,EAAgB;AAAA,EAC7G;AAAA,EAAqB;AAAA,EAAa;AAAA,EAAc;AAAA,EAAY;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAU;AAAA,EAChG;AAAA,EAAgB;AAAA,EAAS;AAAA,EAAW;AACpC,GACD,KAAK;AAAA,EAAC;AAAA,EAAS;AAAA,EAAY;AAAA,EAAU;AAAA,EAAO;AAAA,EAAc;AAAA,EAAgB;AAAA,EAAc;AAAA,EAAiB;AAAA,EACxG;AAAA,EAAQ;AAAA,EAAe;AAAA,EAAa;AAAA,EAAW;AAC/C,GACD,KAAK;AAAA,EAAC;AAAA,EAAiB;AAAA,EAAe;AAAA,EAAc;AAAA,EAAO;AAAA,EAAa;AAAA,EAAmB;AAAA,EAC1F;AAAA,EAAsB;AAAA,EAAuB;AAAA,EAA6B;AAAA,EAAkB;AAAA,EAC5F;AAAA,EAA6B;AAAA,EAAuB;AAAA,EAAc;AAAA,EAAyB;AAAA,EAC3F;AAAA,EAAmB;AAAA,EAAoB;AAAA,EAAoB;AAAA,EAAqB;AAAA,EAChF;AAAA,EAAqB;AAAA,EAAmB;AAAA,EAAc;AAAA,EAAU;AAAA,EAAgB;AAAA,EAChF;AAAA,EAAoB;AAAA,EAA0B;AAAA,EAA0B;AAAA,EACxE;AAAA,EAAsB;AAAA,EAA4B;AAAA,EAA4B;AAAA,EAC9E;AAAA,EAAsB;AAAA,EAAsB;AAAA,EAAiB;AAAA,EAAuB;AAAA,EACpF;AAAA,EAA8B;AAAA,EAAuB;AAAA,EAAuB;AAAA,EAAmB;AAAA,EAC/F;AAAA,EAAgB;AAAA,EAAuB;AAAA,EAAuB;AAAA,EAAsB;AAAA,EACpF;AAAA,EAAsB;AAAA,EAAiB;AAAA,EAAuB;AAAA,EAAqB;AAAA,EACnF;AAAA,EAA2B;AAAA,EAA2B;AAAA,EAAuB;AAAA,EAC7E;AAAA,EAA6B;AAAA,EAA6B;AAAA,EAAuB;AAAA,EACjF;AAAA,EAAe;AAAA,EAAqB;AAAA,EAAqB;AAAA,EAAqB;AAAA,EAAiB;AAAA,EAC/F;AAAA,EAAsB;AAAA,EAAsB;AAAA,EAAsB;AAAA,EAAkB;AAAA,EAAgB;AAAA,EACpG;AAAA,EAAoB;AAAA,EAA0B;AAAA,EAA2B;AAAA,EAAoB;AAAA,EAC7F;AAAA,EAAgB;AAAA,EAAU;AAAA,EAAwB;AAAA,EAAc;AAAA,EAAc;AAAA,EAAe;AAAA,EAC7F;AAAA,EAAgB;AAAA,EAAgB;AAAA,EAAe;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAa;AAAA,EAAa;AAAA,EAAS;AAAA,EACnG;AAAA,EAAe;AAAA,EAAc;AAAA,EAAe;AAAA,EAAqB;AAAA,EAAqB;AAAA,EACtF;AAAA,EAAe;AAAA,EAAgB;AAAA,EAAW;AAAA,EAAW;AAAA,EAAW;AAAA,EAAsB;AAAA,EACtF;AAAA,EAAiB;AAAA,EAAO;AAAA,EAAa;AAAA,EAAc;AAAA,EAAU;AAAA,EAAa;AAAA,EAAW;AAAA,EAAe;AAAA,EACpG;AAAA,EAAQ;AAAA,EAAc;AAAA,EAAkB;AAAA,EAAa;AAAA,EAAa;AAAA,EAAe;AAAA,EAAa;AAAA,EAAS;AAAA,EACvG;AAAA,EAAQ;AAAA,EAAgB;AAAA,EAAe;AAAA,EAAyB;AAAA,EAAgB;AAAA,EAChF;AAAA,EAAa;AAAA,EAAoB;AAAA,EAAkB;AAAA,EAAgB;AAAA,EAAc;AAAA,EAAkB;AAAA,EACnG;AAAA,EAAqB;AAAA,EAA2B;AAAA,EAA0B;AAAA,EAC1E;AAAA,EAAyB;AAAA,EAA2B;AAAA,EAAe;AAAA,EAAO;AAAA,EAA8B;AAAA,EACxG;AAAA,EAAa;AAAA,EAAqB;AAAA,EAAkB;AAAA,EAAkB;AAAA,EAAe;AAAA,EACrF;AAAA,EAAqB;AAAA,EAAY;AAAA,EAAY;AAAA,EAAgB;AAAA,EAAkB;AAAA,EAC/E;AAAA,EAAuB;AAAA,EAAyB;AAAA,EAAsB;AAAA,EAAuB;AAAA,EAAU;AAAA,EACvG;AAAA,EAAQ;AAAA,EAAqB;AAAA,EAAmB;AAAA,EAAoB;AAAA,EAAY;AAAA,EAAe;AAAA,EAC/F;AAAA,EAAmB;AAAA,EAAQ;AAAA,EAAkB;AAAA,EAAc;AAAA,EAAe;AAAA,EAAc;AAAA,EACxF;AAAA,EAAuB;AAAA,EAAmB;AAAA,EAAU;AAAA,EAAgB;AAAA,EAAoB;AAAA,EACxF;AAAA,EAAiB;AAAA,EAAiB;AAAA,EAAqB;AAAA,EAAuB;AAAA,EAAe;AAAA,EAC7F;AAAA,EAAc;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAe;AAAA,EAAoB;AAAA,EAAsB;AAAA,EACxF;AAAA,EAAqB;AAAA,EAAsB;AAAA,EAAqB;AAAA,EAAa;AAAA,EAAkB;AAAA,EAC/F;AAAA,EAAa;AAAA,EAAe;AAAA,EAAiB;AAAA,EAAe;AAAA,EAAa;AAAA,EAAa;AAAA,EACtF;AAAA,EAAc;AAAA,EAAmB;AAAA,EAAa;AAAA,EAAkB;AAAA,EAAc;AAAA,EAAmB;AAAA,EACjG;AAAA,EAAkB;AAAA,EAAY;AAAA,EAAa;AAAA,EAAY;AAAA,EAAa;AAAA,EAAU;AAAA,EAAQ;AAAA,EAAU;AAAA,EAChG;AAAA,EAAmB;AAAA,EAAW;AAAA,EAAS;AAAA,EAAW;AAAA,EAAW;AAAA,EAAiB;AAAA,EAAkB;AAAA,EAChG;AAAA,EAAiB;AAAA,EAAY;AAAA,EAAiB;AAAA,EAAc;AAAA,EAAc;AAAA,EAAW;AAAA,EACrF;AAAA,EAAqB;AAAA,EAAuB;AAAA,EAAkB;AAAA,EAAkB;AAAA,EAChF;AAAA,EAAwB;AAAA,EAAgB;AAAA,EAAiB;AAAA,EAAe;AAAA,EAAoB;AAAA,EAC5F;AAAA,EAAqB;AAAA,EAAS;AAAA,EAAe;AAAA,EAAgB;AAAA,EAAe;AAAA,EAAsB;AAAA,EAClG;AAAA,EAAY;AAAA,EAAU;AAAA,EAAU;AAAA,EAAQ;AAAA,EAAc;AAAA,EAAe;AAAA,EAAS;AAAA,EAAW;AAAA,EACzF;AAAA,EAAuB;AAAA,EAA2B;AAAA,EAA6B;AAAA,EAC/E;AAAA,EAAwB;AAAA,EAA4B;AAAA,EAA8B;AAAA,EAClF;AAAA,EAAuB;AAAA,EAAqB;AAAA,EAAkB;AAAA,EAAwB;AAAA,EACtF;AAAA,EAA8B;AAAA,EAAyB;AAAA,EAAyB;AAAA,EAChF;AAAA,EAA+B;AAAA,EAAuB;AAAA,EAAwB;AAAA,EAC9E;AAAA,EAAqB;AAAA,EAAoB;AAAA,EAAoB;AAAA,EAAmB;AAAA,EAChF;AAAA,EAAmB;AAAA,EAAyB;AAAA,EAAgB;AAAA,EAAiB;AAAA,EAAS;AAAA,EAAY;AAAA,EAAO;AAAA,EACzG;AAAA,EAAgB;AAAA,EAAc;AAAA,EAAkB;AAAA,EAAmB;AAAA,EAAwB;AAAA,EAC3F;AAAA,EAAyB;AAAA,EAAwB;AAAA,EAAyB;AAAA,EAAiB;AAAA,EAC3F;AAAA,EAA0B;AAAA,EAAuB;AAAA,EAAe;AAAA,EAAgB;AAAA,EAAoB;AAAA,EACpG;AAAA,EAAkB;AAAA,EAAe;AAAA,EAAkB;AAAA,EAA2B;AAAA,EAAO;AAAA,EAAa;AAAA,EAClG;AAAA,EAAoB;AAAA,EAAmB;AAAA,EAAc;AAAA,EAAoB;AAAA,EACzE;AAAA,EAAuB;AAAA,EAA8B;AAAA,EAAgB;AAAA,EAAkB;AAAA,EACvF;AAAA,EAAiB;AAAA,EAAkB;AAAA,EAAgB;AAAA,EAAe;AAAA,EAAe;AAAA,EAAc;AAAA,EAC/F;AAAA,EAAgB;AAAA,EAAe;AAAA,EAAU;AAAA,EAAS;AAAA,EAAe;AAAA,EAAc;AAAA,EAAgB;AAAA,EAC/F;AAAA,EAAgB;AAChB,EAAC,QAAS,GACX,KAAK,GAAG,OAAO,EAAE;AAClB,IAAI,KAAK,wBACR,KAAK,+BACL,KAAK;AAAA,EACJ,WAAW;AAAA,EACX,UAAU,CAAC;AAAA,IACV,OAAO,0BAA0B,EAAE,YAAY,EAAE;AAAA,EACpD,GAAK;AAAA,IACF,OAAO,yBAAyB,EAAE;AAAA,EACrC,GAAK;AAAA,IACF,OAAO,IAAI,EAAE;AAAA,EAChB,GAAK;AAAA,IACF,OAAO;AAAA,EACV,GAAK;AAAA,IACF,OAAO,aAAa,EAAE,UAAU,EAAE,SAAS,EAAE;AAAA,EAChD,GAAK;AAAA,IACF,OAAO;AAAA,EACV,GAAK;AAAA,IACF,OAAO,YAAY,EAAE;AAAA,EACxB,GAAK;AAAA,IACF,OAAO;AAAA,EACV,GAAK;AAAA,IACF,OAAO;AAAA,EACV,CAAG;AAAA,EACD,WAAW;AACb;AAEA,SAAS,GAAGA,IAAGG,IAAGF,IAAG;AACpB,SAAO,OAAOA,KAAI,KAAKD,GAAE,QAAQG,IAAI,CAAAD,OAAK,GAAGF,IAAGG,IAAGF,KAAI,CAAC,CAAG;AAC5D;AACA,MAAM,KAAK,4BACV,KAAK;AAAA,EAAC;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAO;AAAA,EAAS;AAAA,EAAW;AAAA,EAAO;AAAA,EAAO;AAAA,EAAY;AAAA,EAAM;AAAA,EAAU;AAAA,EAAQ;AAAA,EAC1G;AAAA,EAAS;AAAA,EAAS;AAAA,EAAc;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAW;AAAA,EAAO;AAAA,EAAU;AAAA,EAAY;AAAA,EAAU;AAAA,EAC3G;AAAA,EAAO;AAAA,EAAS;AAAA,EAAS;AAAA,EAAS;AAAA,EAAY;AAAA,EAAS;AAAA,EAAS;AAAA,EAAU;AAAA,EAAU;AAAA,EAAQ;AAAA,EAAU;AACtG,GACD,KAAK,CAAC,QAAQ,SAAS,QAAQ,aAAa,OAAO,UAAU,GAC7D,KAAK;AAAA,EAAC;AAAA,EAAU;AAAA,EAAY;AAAA,EAAW;AAAA,EAAU;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAU;AAAA,EAAU;AAAA,EAAU;AAAA,EAAU;AAAA,EACxG;AAAA,EAAgB;AAAA,EAAgB;AAAA,EAAa;AAAA,EAAc;AAAA,EAAqB;AAAA,EAAc;AAAA,EAC9F;AAAA,EAAe;AAAA,EAAe;AAAA,EAAiB;AAAA,EAAkB;AAAA,EAAO;AAAA,EAAO;AAAA,EAAW;AAAA,EAAW;AAAA,EACrG;AAAA,EAAqB;AAAA,EAAW;AAAA,EAAY;AAAA,EAAQ;AAAA,EAAW;AAAA,EAAa;AAAA,EAAqB;AAAA,EACjG;AAAA,EAAW;AAAA,EAAS;AAAA,EAAQ;AAC5B,GACD,KAAK,CAAC,SAAS,aAAa,iBAAiB,cAAc,kBAAkB,eAAe,aAAa,UAAU,GACnH,KAAK;AAAA,EAAC;AAAA,EAAe;AAAA,EAAc;AAAA,EAAiB;AAAA,EAAgB;AAAA,EAAW;AAAA,EAAW;AAAA,EAAQ;AAAA,EAAY;AAAA,EAC7G;AAAA,EAAc;AAAA,EAAY;AAAA,EAAa;AAAA,EAAsB;AAAA,EAAa;AAAA,EAAsB;AAAA,EAAU;AAC1G,GACD,KAAK,CAAC,aAAa,QAAQ,SAAS,WAAW,UAAU,YAAY,gBAAgB,UAAU,QAAQ,GACvG,KAAK,CAAE,EAAC,OAAO,IAAI,IAAI,EAAE;AAE1B,SAAS,GAAGD,IAAG;AACd,QAAMG,KAAIH,GAAE,OACXC,KAAI,IACJC,KAAI;AAAA,IACH,OAAO;AAAA,IACP,KAAK;AAAA,IACL,mBAAmB,CAACF,IAAGG,OAAM;AAC5B,YAAMF,KAAID,GAAE,CAAC,EAAE,SAASA,GAAE,OACzBE,KAAIF,GAAE,MAAMC,EAAC;AACd,UAAI,QAAQC,MAAK,QAAQA;AAAG,eAAO,KAAKC,GAAE;AAC1C,UAAIC;AACJ,cAAQF,QAAO,CAACF,IAAG;AAAA,QAClB,OAAOG;AAAA,MACZ,MAAU;AACL,cAAMF,KAAI,OAAOD,GAAE,CAAC,EAAE,MAAM,CAAC;AAC7B,eAAO,OAAOA,GAAE,MAAM,QAAQC,IAAGE,EAAC;AAAA,MAClC,GAAEH,IAAG;AAAA,QACL,OAAOC;AAAA,MACZ,CAAK,KAAKE,GAAE,YAAW;AACnB,YAAME,KAAIL,GAAE,MAAM,UAAUC,EAAC;AAC7B,QAAEG,KAAIC,GAAE,MAAM,OAAO,OAAOD,KAAIC,GAAE,MAAM,gBAAgB,MAAM,MAAMD,GAAE,UAAUD,GAAE,YAAa;AAAA,IAC/F;AAAA,EACD,GACDC,KAAI;AAAA,IACH,UAAU;AAAA,IACV,SAAS;AAAA,IACT,SAAS;AAAA,IACT,UAAU;AAAA,IACV,qBAAqB;AAAA,EACrB,GACDC,KAAI,wBACJE,KAAI,uCACJC,KAAI;AAAA,IACH,WAAW;AAAA,IACX,UAAU,CAAC;AAAA,MACV,OAAO,QAAQD,EAAC,MAAMF,EAAC,YAAYA,EAAC;AAAA,IACxC,GAAM;AAAA,MACF,OAAO,OAAOE,EAAC,SAASF,EAAC,eAAeA,EAAC;AAAA,IAC7C,GAAM;AAAA,MACF,OAAO;AAAA,IACX,GAAM;AAAA,MACF,OAAO;AAAA,IACX,GAAM;AAAA,MACF,OAAO;AAAA,IACX,GAAM;AAAA,MACF,OAAO;AAAA,IACX,GAAM;AAAA,MACF,OAAO;AAAA,IACX,CAAI;AAAA,IACD,WAAW;AAAA,EACX,GACDI,KAAI;AAAA,IACH,WAAW;AAAA,IACX,OAAO;AAAA,IACP,KAAK;AAAA,IACL,UAAUL;AAAA,IACV,UAAU,CAAE;AAAA,EACZ,GACDW,KAAI;AAAA,IACH,OAAO;AAAA,IACP,KAAK;AAAA,IACL,QAAQ;AAAA,MACP,KAAK;AAAA,MACL,WAAW;AAAA,MACX,UAAU,CAACf,GAAE,kBAAkBS,EAAC;AAAA,MAChC,aAAa;AAAA,IACb;AAAA,EACD,GACDC,KAAI;AAAA,IACH,OAAO;AAAA,IACP,KAAK;AAAA,IACL,QAAQ;AAAA,MACP,KAAK;AAAA,MACL,WAAW;AAAA,MACX,UAAU,CAACV,GAAE,kBAAkBS,EAAC;AAAA,MAChC,aAAa;AAAA,IACb;AAAA,EACD,GACDY,KAAI;AAAA,IACH,WAAW;AAAA,IACX,OAAO;AAAA,IACP,KAAK;AAAA,IACL,UAAU,CAACrB,GAAE,kBAAkBS,EAAC;AAAA,EAChC,GACDc,KAAI;AAAA,IACH,WAAW;AAAA,IACX,UAAU,CAACvB,GAAE,QAAQ,gBAAgB,QAAQ;AAAA,MAC5C,WAAW;AAAA,MACX,UAAU,CAAC;AAAA,QACV,OAAO;AAAA,QACP,WAAW;AAAA,QACX,UAAU,CAAC;AAAA,UACV,WAAW;AAAA,UACX,OAAO;AAAA,QACb,GAAQ;AAAA,UACF,WAAW;AAAA,UACX,OAAO;AAAA,UACP,KAAK;AAAA,UACL,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,WAAW;AAAA,QACjB,GAAQ;AAAA,UACF,WAAW;AAAA,UACX,OAAOC,KAAI;AAAA,UACX,YAAY;AAAA,UACZ,WAAW;AAAA,QACjB,GAAQ;AAAA,UACF,OAAO;AAAA,UACP,WAAW;AAAA,QACjB,CAAM;AAAA,MACN,CAAK;AAAA,IACD,CAAA,GAAGD,GAAE,sBAAsBA,GAAE,mBAAmB;AAAA,EACjD,GACDwB,KAAI,CAACxB,GAAE,kBAAkBA,GAAE,mBAAmBe,IAAGL,IAAGW,IAAG;AAAA,IACtD,OAAO;AAAA,EACP,GAAEb,EAAC;AACL,EAAAC,GAAE,WAAWe,GAAE,OAAO;AAAA,IACrB,OAAO;AAAA,IACP,KAAK;AAAA,IACL,UAAUpB;AAAA,IACV,UAAU,CAAC,MAAM,EAAE,OAAOoB,EAAC;AAAA,EAC7B,CAAE;AACD,QAAMC,KAAI,CAAA,EAAG,OAAOF,IAAGd,GAAE,QAAQ,GAChCiB,KAAID,GAAE,OAAO,CAAC;AAAA,IACb,OAAO;AAAA,IACP,KAAK;AAAA,IACL,UAAUrB;AAAA,IACV,UAAU,CAAC,MAAM,EAAE,OAAOqB,EAAC;AAAA,EAC9B,CAAG,CAAC,GACFd,KAAI;AAAA,IACH,WAAW;AAAA,IACX,OAAO;AAAA,IACP,KAAK;AAAA,IACL,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,UAAUP;AAAA,IACV,UAAUsB;AAAA,EACV,GACDd,KAAI;AAAA,IACH,UAAU,CAAC;AAAA,MACV,OAAO,CAAC,SAAS,OAAOX,IAAG,OAAO,WAAW,OAAOE,GAAE,OAAOF,IAAG,KAAKE,GAAE,OAAO,MAAMF,EAAC,GAAG,IAAI,CAAC;AAAA,MAC7F,OAAO;AAAA,QACN,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,MACH;AAAA,IACL,GAAM;AAAA,MACF,OAAO,CAAC,SAAS,OAAOA,EAAC;AAAA,MACzB,OAAO;AAAA,QACN,GAAG;AAAA,QACH,GAAG;AAAA,MACH;AAAA,IACL,CAAI;AAAA,EACD,GACDa,KAAI;AAAA,IACH,WAAW;AAAA,IACX,OAAOX,GAAE;AAAA,MAAO;AAAA,MAAU;AAAA,MAAkC;AAAA,MAC3D;AAAA,IAAkD;AAAA,IACnD,WAAW;AAAA,IACX,UAAU;AAAA,MACT,GAAG,CAAC,GAAG,IAAI,GAAG,EAAE;AAAA,IAChB;AAAA,EACD,GACDmB,KAAI;AAAA,IACH,UAAU,CAAC;AAAA,MACV,OAAO,CAAC,YAAY,OAAOrB,IAAG,WAAW;AAAA,IAC7C,GAAM;AAAA,MACF,OAAO,CAAC,YAAY,WAAW;AAAA,IACnC,CAAI;AAAA,IACD,WAAW;AAAA,MACV,GAAG;AAAA,MACH,GAAG;AAAA,IACH;AAAA,IACD,OAAO;AAAA,IACP,UAAU,CAACU,EAAC;AAAA,IACZ,SAAS;AAAA,EACT,GACDiB,KAAI;AAAA,IACH,OAAOzB,GAAE,OAAO,OAAOgB,KAAI,CAAC,GAAG,IAAI,SAAS,QAAQ,GAAGhB,GAAE,OAAO,OAAOgB,GAAE,KAAK,GAAG,GAAG,GAAG,IAAIlB,IAAGE,GAAE,UAAU,IAAI,CAAC;AAAA,IAC/G,WAAW;AAAA,IACX,WAAW;AAAA,EACd;AACC,MAAIgB;AACJ,QAAMY,KAAI;AAAA,IACR,OAAO5B,GAAE,OAAO,MAAMA,GAAE,UAAUA,GAAE,OAAOF,IAAG,oBAAoB,CAAC,CAAC;AAAA,IACpE,KAAKA;AAAA,IACL,cAAc;AAAA,IACd,UAAU;AAAA,IACV,WAAW;AAAA,IACX,WAAW;AAAA,EACX,GACD6B,KAAI;AAAA,IACH,OAAO,CAAC,WAAW,OAAO7B,IAAG,QAAQ;AAAA,IACrC,WAAW;AAAA,MACV,GAAG;AAAA,MACH,GAAG;AAAA,IACH;AAAA,IACD,UAAU,CAAC;AAAA,MACV,OAAO;AAAA,IACP,GAAEU,EAAC;AAAA,EACJ,GACDqB,KAAI,6DAA6DhC,GAAE,sBAAsB,WACzFgB,KAAI;AAAA,IACH,OAAO,CAAC,iBAAiB,OAAOf,IAAG,OAAO,QAAQ,eAAeE,GAAE,UAAU6B,EAAC,CAAC;AAAA,IAC/E,UAAU;AAAA,IACV,WAAW;AAAA,MACV,GAAG;AAAA,MACH,GAAG;AAAA,IACH;AAAA,IACD,UAAU,CAACrB,EAAC;AAAA,EACf;AACC,SAAO;AAAA,IACN,MAAM;AAAA,IACN,SAAS,CAAC,MAAM,OAAO,OAAO,KAAK;AAAA,IACnC,UAAUP;AAAA,IACV,SAAS;AAAA,MACR,iBAAiBsB;AAAA,MACjB,iBAAiBZ;AAAA,IACjB;AAAA,IACD,SAAS;AAAA,IACT,UAAU,CAACd,GAAE,QAAQ;AAAA,MACpB,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,WAAW;AAAA,IACd,CAAG,GAAG;AAAA,MACH,OAAO;AAAA,MACP,WAAW;AAAA,MACX,WAAW;AAAA,MACX,OAAO;AAAA,IACV,GAAKA,GAAE,kBAAkBA,GAAE,mBAAmBe,IAAGL,IAAGW,IAAGE,IAAG;AAAA,MACvD,OAAO;AAAA,IACV,GAAKf,IAAGM,IAAG;AAAA,MACR,WAAW;AAAA,MACX,OAAOb,KAAIE,GAAE,UAAU,GAAG;AAAA,MAC1B,WAAW;AAAA,IACX,GAAEa,IAAG;AAAA,MACL,OAAO,MAAMhB,GAAE,iBAAiB;AAAA,MAChC,UAAU;AAAA,MACV,WAAW;AAAA,MACX,UAAU,CAACuB,IAAGvB,GAAE,aAAa;AAAA,QAC5B,WAAW;AAAA,QACX,OAAOgC;AAAA,QACP,aAAa;AAAA,QACb,KAAK;AAAA,QACL,UAAU,CAAC;AAAA,UACV,WAAW;AAAA,UACX,UAAU,CAAC;AAAA,YACV,OAAOhC,GAAE;AAAA,YACT,WAAW;AAAA,UACjB,GAAQ;AAAA,YACF,WAAW;AAAA,YACX,OAAO;AAAA,YACP,MAAM;AAAA,UACZ,GAAQ;AAAA,YACF,OAAO;AAAA,YACP,KAAK;AAAA,YACL,cAAc;AAAA,YACd,YAAY;AAAA,YACZ,UAAUI;AAAA,YACV,UAAUsB;AAAA,UAChB,CAAM;AAAA,QACN,CAAK;AAAA,MACL,GAAM;AAAA,QACF,OAAO;AAAA,QACP,WAAW;AAAA,MACf,GAAM;AAAA,QACF,OAAO;AAAA,QACP,WAAW;AAAA,MACf,GAAM;AAAA,QACF,UAAU,CAAC;AAAA,UACV,OAAO;AAAA,UACP,KAAK;AAAA,QACV,GAAO;AAAA,UACF,OAAO;AAAA,QACZ,GAAO;AAAA,UACF,OAAOxB,GAAE;AAAA,UACT,YAAYA,GAAE;AAAA,UACd,KAAKA,GAAE;AAAA,QACZ,CAAK;AAAA,QACD,aAAa;AAAA,QACb,UAAU,CAAC;AAAA,UACV,OAAOA,GAAE;AAAA,UACT,KAAKA,GAAE;AAAA,UACP,MAAM;AAAA,UACN,UAAU,CAAC,MAAM;AAAA,QACtB,CAAK;AAAA,MACL,CAAI;AAAA,IACD,GAAEoB,IAAG;AAAA,MACL,eAAe;AAAA,IAClB,GAAK;AAAA,MACF,OAAO,oBAAoBtB,GAAE,sBAC5B;AAAA,MACD,aAAa;AAAA,MACb,OAAO;AAAA,MACP,UAAU,CAACW,IAAGX,GAAE,QAAQA,GAAE,YAAY;AAAA,QACrC,OAAOC;AAAA,QACP,WAAW;AAAA,MACf,CAAI,CAAC;AAAA,IACL,GAAK;AAAA,MACF,OAAO;AAAA,MACP,WAAW;AAAA,IACX,GAAE8B,IAAG;AAAA,MACL,OAAO,QAAQ9B;AAAA,MACf,WAAW;AAAA,IACd,GAAK;AAAA,MACF,OAAO,CAAC,wBAAwB;AAAA,MAChC,WAAW;AAAA,QACV,GAAG;AAAA,MACH;AAAA,MACD,UAAU,CAACU,EAAC;AAAA,IACZ,GAAEiB,IAAG;AAAA,MACL,WAAW;AAAA,MACX,OAAO;AAAA,MACP,WAAW;AAAA,IACd,GAAKhB,IAAGkB,IAAG;AAAA,MACR,OAAO;AAAA,IACV,CAAG;AAAA,EACD;AACF;AACA,MAAM,KAAK,CAAA9B,OAAK,EAAE,MAAMA,IAAG,MAAM,KAAKA,EAAC,IAAI,OAAO,IAAI,GACrD,KAAK,CAAC,YAAY,MAAM,EAAE,IAAI,EAAE,GAChC,KAAK,CAAC,QAAQ,MAAM,EAAE,IAAI,EAAE,GAC5B,KAAK,CAAC,OAAO,MAAM,GACnB,KAAK;AAAA,EAAC;AAAA,EAAS;AAAA,EAAO;AAAA,EAAkB;AAAA,EAAS;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAO;AAAA,EAAM;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAS;AAAA,EACxG;AAAA,EAAY;AAAA,EAAe;AAAA,EAAW;AAAA,EAAS;AAAA,EAAU;AAAA,EAAU;AAAA,EAAe;AAAA,EAAM;AAAA,EAAW;AAAA,EAAQ;AAAA,EAC3G;AAAA,EAAa;AAAA,EAAe;AAAA,EAAsB;AAAA,EAAe;AAAA,EAAS;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAO;AAAA,EAAS;AAAA,EACzG;AAAA,EAAU;AAAA,EAAY;AAAA,EAAS;AAAA,EAAU;AAAA,EAAS;AAAA,EAAS;AAAA,EAAmB;AAAA,EAAY;AAAA,EAAM;AAAA,EAAM;AAAA,EACtG;AAAA,EAAe;AAAA,EAAQ;AAAA,EAAO;AAAA,EAAY;AAAA,EAAe;AAAA,EAAe;AAAA,EAAQ;AAAA,EAAY;AAAA,EAAY;AAAA,EACxG;AAAA,EAAW;AAAA,EAAmB;AAAA,EAAU;AAAA,EAAkB;AAAA,EAAW;AAAA,EAAY;AAAA,EAAiB;AAAA,EAClG;AAAA,EAAU;AAAA,EAAY;AAAA,EAAY;AAAA,EAAU;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAU;AAAA,EAAU;AAAA,EAAa;AAAA,EAAS;AAAA,EACrG;AAAA,EAAU;AAAA,EAAS;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAO;AAAA,EAAa;AAAA,EAAmB;AAAA,EAAqB;AAAA,EAAW;AAAA,EAC3G;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAS;AAC1B,GACD,KAAK,CAAC,SAAS,OAAO,MAAM,GAC5B,KAAK,CAAC,cAAc,iBAAiB,cAAc,QAAQ,aAAa,QAAQ,OAAO,GACvF,KAAK;AAAA,EAAC;AAAA,EAAiB;AAAA,EAAW;AAAA,EAAc;AAAA,EAAS;AAAA,EAAW;AAAA,EAAU;AAAA,EAAU;AAAA,EAAS;AAAA,EAChG;AAAA,EAAgB;AAAA,EAAa;AAAA,EAAa;AAAA,EAAO;AAAA,EAAiB;AAAA,EAAY;AAAA,EAAS;AAAA,EACvF;AAAA,EAAmB;AAAA,EAA4B;AAC/C,GACD,KAAK;AAAA,EAAC;AAAA,EAAO;AAAA,EAAO;AAAA,EAAO;AAAA,EAAU;AAAA,EAAoB;AAAA,EAAc;AAAA,EAAQ;AAAA,EAAc;AAAA,EAC5F;AAAA,EAA6B;AAAA,EAAO;AAAA,EAAO;AAAA,EAAe;AAAA,EAAgB;AAAA,EAAgB;AAAA,EAC1F;AAAA,EAAuB;AAAA,EAAS;AAAA,EAAY;AAAA,EAAiB;AAAA,EAAY;AAAA,EAAU;AAAA,EACnF;AAAA,EAAqC;AAAA,EAAa;AAAA,EAAQ;AAAA,EAAiB;AAAA,EAAkB;AAAA,EAC7F;AAAA,EAA4B;AAAA,EAAqB;AAAA,EAAc;AAAA,EAA2B;AAC1F,GACD,KAAK;AAAA,EAAE;AAAA,EAAqB;AAAA,EAAmB;AAAA,EAAkB;AAAA,EAAkB;AAAA,EAClF;AAAA,EAAoC;AAAA,EAAmB;AAAA,EAAmB;AAAA,EAAmB;AAAA,EAC7F;AAAA,EAAmB;AAAA,EAAmB;AAAA,EAAmB;AAAA,EAAmB;AAAA,EAAmB;AAAA,EAC/F;AAAA,EAAmB;AAAU,GAC9B,KAAK,EAAE,IAAI,mBAAmB,mBAAmB,mBAAmB,mBAAmB,iBAAiB,GACxG,KAAK,EAAE,IAAI,IAAI,GAAG,GAClB,KAAK;AAAA,EAAE;AAAA,EAAa;AAAA,EACnB;AAAA,EAA0D;AAAA,EAC1D;AAAA,EAAmB;AAAA,EACnB;AAAA,EAA0D;AAAA,EAC1D;AAAA,EAA0D;AAAA,EAC1D;AAA8B,GAC/B,KAAK,EAAE,IAAI,MAAM,wDAAwD,GACzE,KAAK,EAAE,IAAI,IAAI,GAAG,GAClB,KAAK,EAAE,SAAS,IAAI,GAAG,GACvB,KAAK;AAAA,EAAC;AAAA,EAAe,EAAE,gBAAgB,EAAE,SAAS,SAAS,GAAG,GAAG,IAAI;AAAA,EAAG;AAAA,EAAqB;AAAA,EAC5F;AAAA,EAAuB;AAAA,EAAY;AAAA,EAAU;AAAA,EAAiB;AAAA,EAAY;AAAA,EAAgB;AAAA,EAC1F;AAAA,EAAY;AAAA,EAAiB;AAAA,EAAa;AAAA,EAAQ;AAAA,EAAW;AAAA,EAAqB;AAAA,EAAa;AAAA,EAAa;AAAA,IAC3G;AAAA,IAAU;AAAA,IAAI;AAAA,EAAI;AAAA,EAAG;AAAA,EAAQ;AAAA,EAAe;AAAA,EAAmB;AAAA,EAAkC;AAAA,EAClG;AAAA,EAAY;AAAA,EAAqB;AAAA,EAAW;AAC5C,GACD,KAAK;AAAA,EAAC;AAAA,EAAO;AAAA,EAA2B;AAAA,EAAS;AAAA,EAA6B;AAAA,EAC7E;AAAA,EAAmC;AAAA,EAAW;AAAA,EAA+B;AAAA,EAAQ;AAAA,EACrF;AACF;AACA,IAAI,KAAK,OAAO,OAAO;AAAA,EACtB,WAAW;AAAA,EACX,WAAW,CAAAA,OAAK;AACf,UAAMG,KAAIH,GAAE,OACXC,KAAI,CAAE,GACNC,KAAI;AAAA,MACH,OAAO;AAAA,MACP,KAAK;AAAA,MACL,UAAU,CAAC,QAAQ;AAAA,QAClB,OAAO;AAAA,QACP,UAAU,CAACD,EAAC;AAAA,MACjB,CAAK;AAAA,IACL;AACE,WAAO,OAAOA,IAAG;AAAA,MAChB,WAAW;AAAA,MACX,UAAU,CAAC;AAAA,QACV,OAAOE,GAAE,OAAO,sBAAsB,qBAAqB;AAAA,MAC3D,GAAED,EAAC;AAAA,IACP,CAAG;AACD,UAAME,KAAI;AAAA,MACR,WAAW;AAAA,MACX,OAAO;AAAA,MACP,KAAK;AAAA,MACL,UAAU,CAACJ,GAAE,gBAAgB;AAAA,IAC7B,GACDK,KAAI;AAAA,MACH,OAAO;AAAA,MACP,QAAQ;AAAA,QACP,UAAU,CAACL,GAAE,kBAAkB;AAAA,UAC9B,OAAO;AAAA,UACP,KAAK;AAAA,UACL,WAAW;AAAA,QACjB,CAAM,CAAC;AAAA,MACF;AAAA,IACD,GACDO,KAAI;AAAA,MACH,WAAW;AAAA,MACX,OAAO;AAAA,MACP,KAAK;AAAA,MACL,UAAU,CAACP,GAAE,kBAAkBC,IAAGG,EAAC;AAAA,IACvC;AACE,IAAAA,GAAE,SAAS,KAAKG,EAAC;AACjB,UAAMC,KAAI;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,UAAU,CAAC;AAAA,QACV,OAAO;AAAA,QACP,WAAW;AAAA,MAChB,GAAOR,GAAE,aAAaC,EAAC;AAAA,IACnB,GACDQ,KAAIT,GAAE,QAAQ;AAAA,MACb,QAAQ;AAAA,MACR,WAAW;AAAA,IACf,CAAI,GACDe,KAAI;AAAA,MACH,WAAW;AAAA,MACX,OAAO;AAAA,MACP,aAAa;AAAA,MACb,UAAU,CAACf,GAAE,QAAQA,GAAE,YAAY;AAAA,QAClC,OAAO;AAAA,MACZ,CAAK,CAAC;AAAA,MACF,WAAW;AAAA,IACf;AACE,WAAO;AAAA,MACN,MAAM;AAAA,MACN,SAAS,CAAC,IAAI;AAAA,MACd,UAAU;AAAA,QACT,UAAU;AAAA,QACV,SAAS;AAAA,UAAC;AAAA,UAAM;AAAA,UAAQ;AAAA,UAAQ;AAAA,UAAQ;AAAA,UAAM;AAAA,UAAO;AAAA,UAAS;AAAA,UAAM;AAAA,UAAM;AAAA,UAAQ;AAAA,UAAQ;AAAA,UACzF;AAAA,QACA;AAAA,QACD,SAAS,CAAC,QAAQ,OAAO;AAAA,QACzB,UAAU;AAAA,UAAC;AAAA,UAAS;AAAA,UAAM;AAAA,UAAY;AAAA,UAAQ;AAAA,UAAQ;AAAA,UAAQ;AAAA,UAAU;AAAA,UAAW;AAAA,UAAQ;AAAA,UAC1F;AAAA,UAAY;AAAA,UAAU;AAAA,UAAS;AAAA,UAAQ;AAAA,UAAS;AAAA,UAAQ;AAAA,UAAS;AAAA,UAAS;AAAA,UAAS;AAAA,UAAQ;AAAA,UAC3F;AAAA,UAAU;AAAA,UAAW;AAAA,UAAW;AAAA,UAAQ;AAAA,UAAU;AAAA,UAAQ;AAAA,UAAO;AAAA,UAAS;AAAA,UAAU;AAAA,UACpF;AAAA,UAAU;AAAA,UAAQ;AAAA,UAAa;AAAA,UAAU;AAAA,UAAQ;AAAA,UAAW;AAAA,UAAU;AAAA,UAAW;AAAA,UAAO;AAAA,UACxF;AAAA,UAAY;AAAA,UAAM;AAAA,UAAW;AAAA,UAAO;AAAA,UAAO;AAAA,UAAS;AAAA,UAAS;AAAA,UAAiB;AAAA,UAAY;AAAA,UAC1F;AAAA,UAAgB;AAAA,UAAa;AAAA,UAAc;AAAA,UAAa;AAAA,UAAY;AAAA,UAAW;AAAA,UAAc;AAAA,UAC7F;AAAA,UAAW;AAAA,UAAU;AAAA,UAAU;AAAA,UAAU;AAAA,UAAW;AAAA,UAAM;AAAA,UAAM;AAAA,UAAS;AAAA,UAAa;AAAA,UACtF;AAAA,UAAS;AAAA,UAAW;AAAA,UAAW;AAAA,UAAQ;AAAA,UAAQ;AAAA,UAAS;AAAA,UAAO;AAAA,UAAU;AAAA,UAAQ;AAAA,UAAS;AAAA,UAC1F;AAAA,UAAU;AAAA,UAAU;AAAA,UAAS;AAAA,UAAU;AAAA,UAAU;AAAA,UAAQ;AAAA,UAAW;AAAA,UAAU;AAAA,UAAc;AAAA,UAC5F;AAAA,UAAW;AAAA,UAAY;AAAA,UAAS;AAAA,UAAQ;AAAA,UAAU;AAAA,UAAS;AAAA,UAAS;AAAA,UAAY;AAAA,UAAW;AAAA,UAC3F;AAAA,UAAO;AAAA,UAAY;AAAA,UAAc;AAAA,UAAS;AAAA,UAAQ;AAAA,UAAe;AAAA,UAAW;AAAA,UAAU;AAAA,UAAQ;AAAA,UAC9F;AAAA,UAAS;AAAA,UAAS;AAAA,UAAS;AAAA,UAAM;AAAA,UAAM;AAAA,UAAM;AAAA,UAAO;AAAA,UAAa;AAAA,UAAM;AAAA,UAAM;AAAA,UAAS;AAAA,UACtF;AAAA,UAAS;AAAA,UAAU;AAAA,UAAM;AAAA,UAAY;AAAA,UAAM;AAAA,UAAS;AAAA,UAAS;AAAA,UAAQ;AAAA,UAAS;AAAA,UAAY;AAAA,UAC1F;AAAA,UAAS;AAAA,UAAU;AAAA,UAAU;AAAA,UAAO;AAAA,UAAS;AAAA,UAAQ;AAAA,UAAU;AAAA,UAAO;AAAA,UAAU;AAAA,UAAO;AAAA,UAAQ;AAAA,UAC/F;AAAA,UAAQ;AAAA,UAAU;AAAA,UAAM;AAAA,UAAU;AAAA,UAAM;AAAA,UAAS;AAAA,UAAO;AAAA,UAAM;AAAA,UAAW;AAAA,UAAa;AAAA,UACtF;AAAA,UAAa;AAAA,UAAa;AAAA,UAAQ;AAAA,UAAQ;AAAA,UAAS;AAAA,UAAO;AAAA,UAAO;AAAA,UAAQ;AAAA,UAAM;AAAA,UAAS;AAAA,UACxF;AAAA,UAAQ;AAAA,UAAM;AAAA,UAAQ;AAAA,UAAY;AAAA,UAAU;AAAA,UAAQ;AAAA,UAAW;AAAA,UAAM;AAAA,UAAQ;AAAA,UAAO;AAAA,UAAQ;AAAA,UAC5F;AAAA,UAAU;AAAA,UAAU;AAAA,UAAM;AAAA,UAAQ;AAAA,UAAW;AAAA,UAAQ;AAAA,UAAS;AAAA,UAAS;AAAA,UAAW;AAAA,UAAS;AAAA,UAC3F;AAAA,UAAU;AAAA,UAAO;AAAA,UAAY;AAAA,UAAU;AAAA,UAAO;AAAA,UAAS;AAAA,UAAQ;AAAA,UAAU;AAAA,UAAQ;AAAA,UAAO;AAAA,UACxF;AAAA,UAAW;AAAA,UAAO;AAAA,UAAS;AAAA,UAAU;AAAA,UAAU;AAAA,UAAS;AAAA,UAAO;AAAA,UAAU;AAAA,QACzE;AAAA,MACD;AAAA,MACD,UAAU,CAACS,IAAGT,GAAE,QAAO,GAAIe,IAAGP,IAAGR,GAAE,mBAAmBK,IAAG;AAAA,QACxD,OAAO;AAAA,MACP,GAAEE,IAAG;AAAA,QACL,WAAW;AAAA,QACX,OAAO;AAAA,MACX,GAAM;AAAA,QACF,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,MACL,GAAEN,EAAC;AAAA,IACJ;AAAA,EACD;AAAA,EACD,QAAQ,CAAAD,OAAK;AACZ,UAAMG,KAAIH,GAAE,OACXC,KAAID,GAAE,QAAQ,MAAM,KAAK;AAAA,MACxB,UAAU,CAAC;AAAA,QACV,OAAO;AAAA,MACZ,CAAK;AAAA,IACL,CAAI,GACDE,KAAI,mBACJE,KAAI,yBAAyBD,GAAE,SAASD,EAAC,IAAI,kBAAkBC,GAAE,SAAS,UAAU,IAAI,KACxFE,KAAI;AAAA,MACH,WAAW;AAAA,MACX,UAAU,CAAC;AAAA,QACV,OAAO;AAAA,MACZ,GAAO;AAAA,QACF,OAAO;AAAA,MACZ,CAAK;AAAA,IACD,GACDE,KAAI;AAAA,MACH,WAAW;AAAA,MACX,UAAU,CAAC;AAAA,QACV,OAAO;AAAA,QACP,KAAK;AAAA,QACL,SAAS;AAAA,QACT,UAAU,CAACP,GAAE,gBAAgB;AAAA,MAClC,GAAO;AAAA,QACF,OAAO;AAAA,QACP,KAAK;AAAA,QACL,SAAS;AAAA,MACd,GAAOA,GAAE,kBAAkB;AAAA,QACtB,OAAO;AAAA,QACP,KAAK;AAAA,MACV,CAAK,CAAC;AAAA,IACF,GACDQ,KAAI;AAAA,MACH,WAAW;AAAA,MACX,UAAU,CAAC;AAAA,QACV,OAAO;AAAA,MACZ,GAAO;AAAA,QACF,OAAO;AAAA,MACZ,GAAO;AAAA,QACF,OAAO;AAAA,MACZ,CAAK;AAAA,MACD,WAAW;AAAA,IACX,GACDC,KAAI;AAAA,MACH,WAAW;AAAA,MACX,OAAO;AAAA,MACP,KAAK;AAAA,MACL,UAAU;AAAA,QACT,SAAS;AAAA,MACT;AAAA,MACD,UAAU,CAAC;AAAA,QACV,OAAO;AAAA,QACP,WAAW;AAAA,MAChB,GAAOT,GAAE,QAAQO,IAAG;AAAA,QACf,WAAW;AAAA,MAChB,CAAK,GAAG;AAAA,QACH,WAAW;AAAA,QACX,OAAO;AAAA,MACZ,GAAON,IAAGD,GAAE,oBAAoB;AAAA,IAC5B,GACDe,KAAI;AAAA,MACH,WAAW;AAAA,MACX,OAAOZ,GAAE,SAASD,EAAC,IAAIF,GAAE;AAAA,MACzB,WAAW;AAAA,IACX,GACDU,KAAIP,GAAE,SAASD,EAAC,IAAIF,GAAE,WAAW,WACjCqB,KAAI;AAAA,MACH,SAAS;AAAA,QAAC;AAAA,QAAO;AAAA,QAAQ;AAAA,QAAS;AAAA,QAAQ;AAAA,QAAY;AAAA,QAAW;AAAA,QAAM;AAAA,QAAQ;AAAA,QAAQ;AAAA,QAAU;AAAA,QAChG;AAAA,QAAW;AAAA,QAAQ;AAAA,QAAM;AAAA,QAAU;AAAA,QAAY;AAAA,QAAY;AAAA,QAAU;AAAA,QAAU;AAAA,QAAU;AAAA,QACzF;AAAA,QAAW;AAAA,QAAS;AAAA,QAAY;AAAA,QAAS;AAAA,QAAY;AAAA,QAAY;AAAA,QAAW;AAAA,QAAY;AAAA,QACxF;AAAA,QAAkB;AAAA,QAAiB;AAAA,QAAW;AAAA,QAAW;AAAA,QAAY;AAAA,QAAiB;AAAA,QACtF;AAAA,MACA;AAAA,MACD,MAAM;AAAA,QAAC;AAAA,QAAS;AAAA,QAAU;AAAA,QAAU;AAAA,QAAY;AAAA,QAAO;AAAA,QAAS;AAAA,QAAQ;AAAA,QAAQ;AAAA,QAAQ;AAAA,QACvF;AAAA,QAAY;AAAA,QAAc;AAAA,QAAc;AAAA,QAAc;AAAA,QAAe;AAAA,QAAS;AAAA,QAAU;AAAA,QACxF;AAAA,QAAQ;AAAA,MACR;AAAA,MACD,SAAS;AAAA,MACT,UAAU;AAAA,IACV,GACDE,KAAI,CAACd,IAAGJ,IAAGJ,IAAGD,GAAE,sBAAsBQ,IAAGD,EAAC,GAC1CiB,KAAI;AAAA,MACH,UAAU,CAAC;AAAA,QACV,OAAO;AAAA,QACP,KAAK;AAAA,MACV,GAAO;AAAA,QACF,OAAO;AAAA,QACP,KAAK;AAAA,MACV,GAAO;AAAA,QACF,eAAe;AAAA,QACf,KAAK;AAAA,MACV,CAAK;AAAA,MACD,UAAUH;AAAA,MACV,UAAUE,GAAE,OAAO,CAAC;AAAA,QACnB,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAUF;AAAA,QACV,UAAUE,GAAE,OAAO,CAAC,MAAM,CAAC;AAAA,QAC3B,WAAW;AAAA,MAChB,CAAK,CAAC;AAAA,MACF,WAAW;AAAA,IACX,GACDE,KAAI;AAAA,MACH,OAAO,MAAMrB,KAAI,iBAAiBM;AAAA,MAClC,aAAa;AAAA,MACb,KAAK;AAAA,MACL,YAAY;AAAA,MACZ,UAAUW;AAAA,MACV,SAAS;AAAA,MACT,UAAU,CAAC;AAAA,QACV,OAAO;AAAA,QACP,UAAUA;AAAA,QACV,WAAW;AAAA,MAChB,GAAO;AAAA,QACF,OAAOX;AAAA,QACP,aAAa;AAAA,QACb,UAAU,CAACV,GAAE,QAAQe,IAAG;AAAA,UACvB,WAAW;AAAA,QACjB,CAAM,CAAC;AAAA,QACF,WAAW;AAAA,MAChB,GAAO;AAAA,QACF,WAAW;AAAA,QACX,OAAO;AAAA,MACZ,GAAO;AAAA,QACF,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAUM;AAAA,QACV,WAAW;AAAA,QACX,UAAU,CAACpB,IAAGD,GAAE,sBAAsBO,IAAGC,IAAGH,IAAG;AAAA,UAC9C,OAAO;AAAA,UACP,KAAK;AAAA,UACL,UAAUgB;AAAA,UACV,WAAW;AAAA,UACX,UAAU,CAAC,QAAQpB,IAAGD,GAAE,sBAAsBO,IAAGC,IAAGH,EAAC;AAAA,QAC3D,CAAM;AAAA,MACD,GAAEA,IAAGJ,IAAGD,GAAE,sBAAsBS,EAAC;AAAA,IACtC;AACE,WAAO;AAAA,MACN,MAAM;AAAA,MACN,SAAS,CAAC,GAAG;AAAA,MACb,UAAUY;AAAA,MACV,mBAAmB;AAAA,MACnB,SAAS;AAAA,MACT,UAAU,CAAE,EAAC,OAAOG,IAAGC,IAAGF,IAAG,CAACd,IAAG;AAAA,QAChC,OAAOT,GAAE,WAAW;AAAA,QACpB,UAAUqB;AAAA,MACd,GAAM;AAAA,QACF,WAAW;AAAA,QACX,eAAe;AAAA,QACf,KAAK;AAAA,QACL,UAAU,CAAC;AAAA,UACV,eAAe;AAAA,QACpB,GAAOrB,GAAE,UAAU;AAAA,MACnB,CAAI,CAAC;AAAA,MACF,SAAS;AAAA,QACR,cAAcS;AAAA,QACd,SAASF;AAAA,QACT,UAAUc;AAAA,MACV;AAAA,IACD;AAAA,EACD;AAAA,EACD,UAAU,CAAArB,OAAK;AACd,UAAMG,KAAIH,GAAE,OACXC,KAAID,GAAE,QAAQ,MAAM,KAAK;AAAA,MACxB,UAAU,CAAC;AAAA,QACV,OAAO;AAAA,MACZ,CAAK;AAAA,IACL,CAAI,GACDE,KAAI,mBACJE,KAAI,mCAAmCD,GAAE,SAASD,EAAC,IAAI,kBAAkBC,GAAE,SAAS,UAAU,IAAI,KAClGE,KAAI;AAAA,MACH,WAAW;AAAA,MACX,OAAO;AAAA,IACP,GACDE,KAAI;AAAA,MACH,WAAW;AAAA,MACX,UAAU,CAAC;AAAA,QACV,OAAO;AAAA,QACP,KAAK;AAAA,QACL,SAAS;AAAA,QACT,UAAU,CAACP,GAAE,gBAAgB;AAAA,MAClC,GAAO;AAAA,QACF,OAAO;AAAA,QACP,KAAK;AAAA,QACL,SAAS;AAAA,MACd,GAAOA,GAAE,kBAAkB;AAAA,QACtB,OAAO;AAAA,QACP,KAAK;AAAA,MACV,CAAK,CAAC;AAAA,IACF,GACDQ,KAAI;AAAA,MACH,WAAW;AAAA,MACX,UAAU,CAAC;AAAA,QACV,OAAO;AAAA,MACZ,GAAO;AAAA,QACF,OAAO;AAAA,MACZ,GAAO;AAAA,QACF,OAAO;AAAA,MACZ,CAAK;AAAA,MACD,WAAW;AAAA,IACX,GACDC,KAAI;AAAA,MACH,WAAW;AAAA,MACX,OAAO;AAAA,MACP,KAAK;AAAA,MACL,UAAU;AAAA,QACT,SAAS;AAAA,MACT;AAAA,MACD,UAAU,CAAC;AAAA,QACV,OAAO;AAAA,QACP,WAAW;AAAA,MAChB,GAAOT,GAAE,QAAQO,IAAG;AAAA,QACf,WAAW;AAAA,MAChB,CAAK,GAAG;AAAA,QACH,WAAW;AAAA,QACX,OAAO;AAAA,MACZ,GAAON,IAAGD,GAAE,oBAAoB;AAAA,IAC5B,GACDe,KAAI;AAAA,MACH,WAAW;AAAA,MACX,OAAOZ,GAAE,SAASD,EAAC,IAAIF,GAAE;AAAA,MACzB,WAAW;AAAA,IACX,GACDU,KAAIP,GAAE,SAASD,EAAC,IAAIF,GAAE,WAAW,WACjCqB,KAAI;AAAA,MACH,MAAM;AAAA,QAAC;AAAA,QAAQ;AAAA,QAAQ;AAAA,QAAY;AAAA,QAAY;AAAA,QAAW;AAAA,QAAU;AAAA,QAAS;AAAA,QAAO;AAAA,QAAQ;AAAA,QAC3F;AAAA,QAAQ;AAAA,QAAW;AAAA,QAAY;AAAA,QAAU;AAAA,QAAS;AAAA,MAClD;AAAA,MACD,SAAS;AAAA,QAAC;AAAA,QAAW;AAAA,QAAW;AAAA,QAAO;AAAA,QAAU;AAAA,QAAO;AAAA,QAAiB;AAAA,QACxE;AAAA,QAAmB;AAAA,QAAQ;AAAA,QAAU;AAAA,QAAS;AAAA,QAAS;AAAA,QAAQ;AAAA,QAAS;AAAA,QAAS;AAAA,QACjF;AAAA,QAAa;AAAA,QAAY;AAAA,QAAS;AAAA,QAAW;AAAA,QAAiB;AAAA,QAAa;AAAA,QAAa;AAAA,QACxF;AAAA,QAAY;AAAA,QAAY;AAAA,QAAW;AAAA,QAAU;AAAA,QAAM;AAAA,QAAmB;AAAA,QAAQ;AAAA,QAAQ;AAAA,QACtF;AAAA,QAAU;AAAA,QAAU;AAAA,QAAS;AAAA,QAAS;AAAA,QAAO;AAAA,QAAU;AAAA,QAAQ;AAAA,QAAM;AAAA,QAAU;AAAA,QAAU;AAAA,QACzF;AAAA,QAAW;AAAA,QAAa;AAAA,QAAO;AAAA,QAAY;AAAA,QAAO;AAAA,QAAU;AAAA,QAAW;AAAA,QAAY;AAAA,QAAM;AAAA,QACzF;AAAA,QAAY;AAAA,QAAW;AAAA,QAAa;AAAA,QAAU;AAAA,QAAY;AAAA,QAAY;AAAA,QACtE;AAAA,QAAY;AAAA,QAAU;AAAA,QAAU;AAAA,QAAiB;AAAA,QAAkB;AAAA,QAAU;AAAA,QAAU;AAAA,QACvF;AAAA,QAAY;AAAA,QAAQ;AAAA,QAAgB;AAAA,QAAS;AAAA,QAAoB;AAAA,QAA4B;AAAA,QAC7F;AAAA,QAAO;AAAA,QAAW;AAAA,QAAU;AAAA,QAAY;AAAA,QAAS;AAAA,QAAS;AAAA,QAAW;AAAA,QAAY;AAAA,QAAS;AAAA,QAC1F;AAAA,MACA;AAAA,MACD,SAAS,CAAC,QAAQ,SAAS,WAAW,WAAW,MAAM;AAAA,MACvD,UAAU,CAAC,SAAS;AAAA,MACpB,aAAa;AAAA,QAAC;AAAA,QAAO;AAAA,QAAY;AAAA,QAAW;AAAA,QAAoB;AAAA,QAAU;AAAA,QAAW;AAAA,QACpF;AAAA,QAA0B;AAAA,QAAsB;AAAA,QAAS;AAAA,QAAc;AAAA,QAAU;AAAA,QACjF;AAAA,QAAoB;AAAA,QAAiB;AAAA,QAAW;AAAA,QAAS;AAAA,QAAc;AAAA,QAAY;AAAA,QACnF;AAAA,QAAS;AAAA,QAAY;AAAA,QAAiB;AAAA,QAAiB;AAAA,QAAQ;AAAA,QAAW;AAAA,QAAkB;AAAA,QAC5F;AAAA,QAAmB;AAAA,QAAyB;AAAA,QAAe;AAAA,QAAO;AAAA,QAAiB;AAAA,QACnF;AAAA,QAAgB;AAAA,QAAsB;AAAA,QAAc;AAAA,QAAS;AAAA,QAAe;AAAA,QAC5E;AAAA,QAAe;AAAA,QAAU;AAAA,QAAa;AAAA,QAAS;AAAA,QAAe;AAAA,QAAc;AAAA,QAC5E;AAAA,QAAsB;AAAA,QAAsB;AAAA,QAAiB;AAAA,QAAW;AAAA,QAAU;AAAA,QAClF;AAAA,QAAW;AAAA,MACX;AAAA,IACD,GACDE,KAAI;AAAA,MACH,WAAW;AAAA,MACX,WAAW;AAAA,MACX,UAAU;AAAA,QACT,OAAO;AAAA,UAAC;AAAA,UAAS;AAAA,UAAO;AAAA,UAAQ;AAAA,UAAS;AAAA,UAAY;AAAA,UAAQ;AAAA,UAAQ;AAAA,UAAS;AAAA,UAAU;AAAA,UAAQ;AAAA,UAC/F;AAAA,UAAO;AAAA,UAAQ;AAAA,UAAO;AAAA,UAAQ;AAAA,UAAQ;AAAA,UAAW;AAAA,UAAQ;AAAA,UAAY;AAAA,UAAQ;AAAA,UAAO;AAAA,UAAQ;AAAA,UAC5F;AAAA,UAAQ;AAAA,UAAW;AAAA,UAAW;AAAA,UAAS;AAAA,UAAQ;AAAA,UAAS;AAAA,UAAU;AAAA,UAAU;AAAA,UAAU;AAAA,UACtF;AAAA,UAAW;AAAA,UAAW;AAAA,UAAW;AAAA,UAAW;AAAA,UAAW;AAAA,UAAW;AAAA,UAAW;AAAA,UAAW;AAAA,UACxF;AAAA,UAAY;AAAA,UAAQ;AAAA,UAAW;AAAA,UAAS;AAAA,UAAO;AAAA,UAAS;AAAA,UAAa;AAAA,UACrE;AAAA,UAA6B;AAAA,UAAc;AAAA,UAAe;AAAA,UAAU;AAAA,UAAU;AAAA,UAAU;AAAA,UACxF;AAAA,UAAU;AAAA,UAAQ;AAAA,UAAQ;AAAA,UAAO;AAAA,UAAU;AAAA,UAAW;AAAA,UAAQ;AAAA,UAAW;AAAA,UAAS;AAAA,UAAO;AAAA,UACzF;AAAA,UAAY;AAAA,UAAW;AAAA,UAAQ;AAAA,UAAU;AAAA,UAAO;AAAA,UAAU;AAAA,UAAS;AAAA,UAAU;AAAA,UAAU;AAAA,UACvF;AAAA,UAAU;AAAA,UAAU;AAAA,UAAW;AAAA,UAAU;AAAA,UAAW;AAAA,UAAW;AAAA,UAAW;AAAA,UAAW;AAAA,UACrF;AAAA,UAAU;AAAA,UAAU;AAAA,UAAQ;AAAA,UAAO;AAAA,UAAQ;AAAA,UAAa;AAAA,UAAiB;AAAA,UAAW;AAAA,UACpF;AAAA,UAAY;AAAA,UAAS;AAAA,UAAW;AAAA,QAChC;AAAA,MACD;AAAA,MACD,OAAOpB,GAAE,OAAO,MAAM,gBAAgB,UAAU,WAAW,cAAc,aAAaH,GAAE,UAAUG,GAChG,UAAU,kBAAkB,CAAC;AAAA,IAC/B,GACDqB,KAAI,CAACD,IAAGd,IAAGJ,IAAGJ,IAAGD,GAAE,sBAAsBQ,IAAGD,EAAC,GAC7CkB,KAAI;AAAA,MACH,UAAU,CAAC;AAAA,QACV,OAAO;AAAA,QACP,KAAK;AAAA,MACV,GAAO;AAAA,QACF,OAAO;AAAA,QACP,KAAK;AAAA,MACV,GAAO;AAAA,QACF,eAAe;AAAA,QACf,KAAK;AAAA,MACV,CAAK;AAAA,MACD,UAAUJ;AAAA,MACV,UAAUG,GAAE,OAAO,CAAC;AAAA,QACnB,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAUH;AAAA,QACV,UAAUG,GAAE,OAAO,CAAC,MAAM,CAAC;AAAA,QAC3B,WAAW;AAAA,MAChB,CAAK,CAAC;AAAA,MACF,WAAW;AAAA,IACX,GACDE,KAAI;AAAA,MACH,WAAW;AAAA,MACX,OAAO,MAAMtB,KAAI,iBAAiBM;AAAA,MAClC,aAAa;AAAA,MACb,KAAK;AAAA,MACL,YAAY;AAAA,MACZ,UAAUW;AAAA,MACV,SAAS;AAAA,MACT,UAAU,CAAC;AAAA,QACV,OAAO;AAAA,QACP,UAAUA;AAAA,QACV,WAAW;AAAA,MAChB,GAAO;AAAA,QACF,OAAOX;AAAA,QACP,aAAa;AAAA,QACb,UAAU,CAACK,EAAC;AAAA,QACZ,WAAW;AAAA,MAChB,GAAO;AAAA,QACF,OAAO;AAAA,QACP,WAAW;AAAA,MAChB,GAAO;AAAA,QACF,OAAO;AAAA,QACP,gBAAgB;AAAA,QAChB,UAAU,CAACR,IAAGC,EAAC;AAAA,MACpB,GAAO;AAAA,QACF,WAAW;AAAA,QACX,OAAO;AAAA,MACZ,GAAO;AAAA,QACF,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAUa;AAAA,QACV,WAAW;AAAA,QACX,UAAU,CAACpB,IAAGD,GAAE,sBAAsBO,IAAGC,IAAGH,IAAG;AAAA,UAC9C,OAAO;AAAA,UACP,KAAK;AAAA,UACL,UAAUgB;AAAA,UACV,WAAW;AAAA,UACX,UAAU,CAAC,QAAQpB,IAAGD,GAAE,sBAAsBO,IAAGC,IAAGH,EAAC;AAAA,QAC3D,CAAM;AAAA,MACD,GAAEA,IAAGJ,IAAGD,GAAE,sBAAsBS,EAAC;AAAA,IACtC;AACE,WAAO;AAAA,MACN,MAAM;AAAA,MACN,SAAS,CAAC,MAAM,OAAO,OAAO,OAAO,MAAM,OAAO,KAAK;AAAA,MACvD,UAAUY;AAAA,MACV,SAAS;AAAA,MACT,kBAAkB;AAAA,QACjB,qBAAqB;AAAA,MACrB;AAAA,MACD,UAAU,CAAA,EAAG,OAAOI,IAAGC,IAAGH,IAAGC,IAAG,CAACf,IAAG;AAAA,QACnC,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAUY;AAAA,QACV,UAAU,CAAC,QAAQhB,EAAC;AAAA,MACxB,GAAM;AAAA,QACF,OAAOL,GAAE,WAAW;AAAA,QACpB,UAAUqB;AAAA,MACd,GAAM;AAAA,QACF,OAAO,CAAC,yDAAyD,OAAO,KAAK;AAAA,QAC7E,WAAW;AAAA,UACV,GAAG;AAAA,UACH,GAAG;AAAA,QACH;AAAA,MACL,CAAI,CAAC;AAAA,IACF;AAAA,EACD;AAAA,EACD,aAAa,CAAArB,OAAK;AACjB,UAAMG,KAAI;AAAA,MACR,SAAS;AAAA,QAAC;AAAA,QAAY;AAAA,QAAM;AAAA,QAAQ;AAAA,QAAS;AAAA,QAAQ;AAAA,QAAS;AAAA,QAAS;AAAA,QAAS;AAAA,QAAY;AAAA,QAAM;AAAA,QACjG;AAAA,QAAS;AAAA,QAAY;AAAA,QAAU;AAAA,QAAW;AAAA,QAAS;AAAA,QAAO;AAAA,QAAW;AAAA,QAAQ;AAAA,QAAM;AAAA,QAAY;AAAA,QAC/F;AAAA,QAAa;AAAA,QAAY;AAAA,QAAM;AAAA,QAAQ;AAAA,QAAa;AAAA,QAAO;AAAA,QAAY;AAAA,QAAO;AAAA,QAAY;AAAA,QAC1F;AAAA,QAAW;AAAA,QAAa;AAAA,QAAU;AAAA,QAAY;AAAA,QAAU;AAAA,QAAO;AAAA,QAAU;AAAA,QAAU;AAAA,QAAU;AAAA,QAC7F;AAAA,QAAc;AAAA,QAAU;AAAA,QAAU;AAAA,QAAU;AAAA,QAAQ;AAAA,QAAS;AAAA,QAAO;AAAA,QAAU;AAAA,QAAa;AAAA,QAC3F;AAAA,QAAS;AAAA,QAAW;AAAA,QAAQ;AAAA,QAAY;AAAA,MACxC,EAAC,OAAO;AAAA,QAAC;AAAA,QAAO;AAAA,QAAS;AAAA,QAAO;AAAA,QAAa;AAAA,QAAS;AAAA,QAAS;AAAA,QAAM;AAAA,QAAc;AAAA,QAAU;AAAA,QAC7F;AAAA,QAAO;AAAA,QAAU;AAAA,QAAS;AAAA,QAAQ;AAAA,QAAQ;AAAA,QAAQ;AAAA,QAAO;AAAA,QAAU;AAAA,QAAO;AAAA,QAAW;AAAA,QAAM;AAAA,QAC3F;AAAA,QAAW;AAAA,QAAW;AAAA,QAAU;AAAA,QAAU;AAAA,QAAO;AAAA,QAAa;AAAA,QAAW;AAAA,QAAO;AAAA,QAAQ;AAAA,QACxF;AAAA,QAAQ;AAAA,MACb,CAAK;AAAA,MACD,UAAU;AAAA,QAAC;AAAA,QAAQ;AAAA,QAAQ;AAAA,QAAQ;AAAA,QAAW;AAAA,QAAY;AAAA,QAAU;AAAA,QAAW;AAAA,QAAQ;AAAA,QAAS;AAAA,QAC/F;AAAA,QAAQ;AAAA,QAAQ;AAAA,QAAS;AAAA,QAAU;AAAA,QAAS;AAAA,QAAS;AAAA,QAAU;AAAA,QAAS;AAAA,QAAQ;AAAA,MAChF;AAAA,MACD,SAAS,CAAC,WAAW,SAAS,QAAQ,MAAM;AAAA,IAC5C,GACDF,KAAID,GAAE,QAAQA,GAAE,YAAY;AAAA,MAC3B,OAAO;AAAA,IACX,CAAI,GACDE,KAAI;AAAA,MACH,WAAW;AAAA,MACX,UAAU,CAAC;AAAA,QACV,OAAO;AAAA,MACZ,GAAO;AAAA,QACF,OAAO;AAAA,MACZ,GAAO;AAAA,QACF,OAAO;AAAA,MACZ,CAAK;AAAA,MACD,WAAW;AAAA,IACX,GACDE,KAAI;AAAA,MACH,WAAW;AAAA,MACX,OAAO;AAAA,MACP,KAAK;AAAA,MACL,UAAU,CAAC;AAAA,QACV,OAAO;AAAA,MACZ,CAAK;AAAA,IACD,GACDC,KAAIL,GAAE,QAAQI,IAAG;AAAA,MAChB,SAAS;AAAA,IACb,CAAI,GACDG,KAAI;AAAA,MACH,WAAW;AAAA,MACX,OAAO;AAAA,MACP,KAAK;AAAA,MACL,UAAUJ;AAAA,IACV,GACDK,KAAIR,GAAE,QAAQO,IAAG;AAAA,MAChB,SAAS;AAAA,IACb,CAAI,GACDE,KAAI;AAAA,MACH,WAAW;AAAA,MACX,OAAO;AAAA,MACP,KAAK;AAAA,MACL,SAAS;AAAA,MACT,UAAU,CAAC;AAAA,QACV,OAAO;AAAA,MACZ,GAAO;AAAA,QACF,OAAO;AAAA,MACZ,GAAOT,GAAE,kBAAkBQ,EAAC;AAAA,IACxB,GACDO,KAAI;AAAA,MACH,WAAW;AAAA,MACX,OAAO;AAAA,MACP,KAAK;AAAA,MACL,UAAU,CAAC;AAAA,QACV,OAAO;AAAA,MACZ,GAAO;AAAA,QACF,OAAO;AAAA,MACZ,GAAO;AAAA,QACF,OAAO;AAAA,MACP,GAAER,EAAC;AAAA,IACJ,GACDG,KAAIV,GAAE,QAAQe,IAAG;AAAA,MAChB,SAAS;AAAA,MACT,UAAU,CAAC;AAAA,QACV,OAAO;AAAA,MACZ,GAAO;AAAA,QACF,OAAO;AAAA,MACZ,GAAO;AAAA,QACF,OAAO;AAAA,MACP,GAAEP,EAAC;AAAA,IACR,CAAI;AACF,IAAAD,GAAE,WAAW,CAACQ,IAAGN,IAAGL,IAAGJ,GAAE,kBAAkBA,GAAE,mBAAmBE,IAAGF,GAAE,oBAAoB,GACxFQ,GAAE,WAAW,CAACE,IAAGD,IAAGJ,IAAGL,GAAE,kBAAkBA,GAAE,mBAAmBE,IAAGF,GAAE,QAAQA,GAAE,sBAAsB;AAAA,MACpG,SAAS;AAAA,IACT,CAAA,CAAC;AACH,UAAMqB,KAAI;AAAA,MACR,UAAU,CAACN,IAAGN,IAAGL,IAAGJ,GAAE,kBAAkBA,GAAE,iBAAiB;AAAA,IAC3D,GACDuB,KAAI;AAAA,MACH,OAAO;AAAA,MACP,KAAK;AAAA,MACL,UAAU,CAAC;AAAA,QACV,eAAe;AAAA,MACf,GAAEtB,EAAC;AAAA,IACJ,GACDuB,KAAIxB,GAAE,WAAW,OAAOA,GAAE,WAAW,eAAeA,GAAE,WAAW,kBACjEyB,KAAI;AAAA,MACH,OAAO,MAAMzB,GAAE;AAAA,MACf,WAAW;AAAA,IACf;AACE,WAAO;AAAA,MACN,MAAM;AAAA,MACN,SAAS,CAAC,MAAM,IAAI;AAAA,MACpB,UAAUG;AAAA,MACV,SAAS;AAAA,MACT,UAAU,CAACH,GAAE,QAAQ,OAAO,KAAK;AAAA,QAChC,aAAa;AAAA,QACb,UAAU,CAAC;AAAA,UACV,WAAW;AAAA,UACX,UAAU,CAAC;AAAA,YACV,OAAO;AAAA,YACP,WAAW;AAAA,UACjB,GAAQ;AAAA,YACF,OAAO;AAAA,UACb,GAAQ;AAAA,YACF,OAAO;AAAA,YACP,KAAK;AAAA,UACX,CAAM;AAAA,QACN,CAAK;AAAA,MACD,CAAA,GAAGA,GAAE,qBAAqBA,GAAE,sBAAsB;AAAA,QAClD,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU;AAAA,UACT,SAAS;AAAA,QACT;AAAA,MACL,GAAMqB,IAAGnB,IAAG;AAAA,QACR,eAAe;AAAA,QACf,WAAW;AAAA,QACX,KAAK;AAAA,QACL,SAAS;AAAA,QACT,UAAU,CAAC;AAAA,UACV,eAAe;AAAA,QACpB,GAAOD,IAAGsB,IAAGvB,GAAE,qBAAqBA,GAAE,oBAAoB;AAAA,MAC1D,GAAM;AAAA,QACF,eAAe;AAAA,QACf,WAAW;AAAA,QACX,KAAK;AAAA,QACL,SAAS;AAAA,QACT,UAAU,CAACC,IAAGD,GAAE,qBAAqBA,GAAE,oBAAoB;AAAA,MAC/D,GAAM;AAAA,QACF,eAAe;AAAA,QACf,WAAW;AAAA,QACX,KAAK;AAAA,QACL,SAAS;AAAA,QACT,UAAU,CAACC,IAAGsB,IAAGvB,GAAE,qBAAqBA,GAAE,oBAAoB;AAAA,MAClE,GAAM;AAAA,QACF,WAAW;AAAA,QACX,OAAO;AAAA,QACP,cAAc;AAAA,QACd,KAAK;AAAA,QACL,YAAY;AAAA,QACZ,UAAU,CAAC;AAAA,UACV,WAAW;AAAA,UACX,OAAO;AAAA,UACP,KAAK;AAAA,QACV,CAAK;AAAA,MACL,GAAM;AAAA,QACF,eAAe;AAAA,QACf,WAAW;AAAA,MACf,GAAM;AAAA,QACF,WAAW;AAAA,QACX,OAAO,MAAMwB,KAAI,WAAWxB,GAAE,WAAW;AAAA,QACzC,aAAa;AAAA,QACb,KAAK;AAAA,QACL,YAAY;AAAA,QACZ,UAAUG;AAAA,QACV,UAAU,CAAC;AAAA,UACV,eAAe;AAAA,UACf,WAAW;AAAA,QAChB,GAAO;AAAA,UACF,OAAOH,GAAE,WAAW;AAAA,UACpB,aAAa;AAAA,UACb,UAAU,CAACA,GAAE,YAAYuB,EAAC;AAAA,UAC1B,WAAW;AAAA,QAChB,GAAO;AAAA,UACF,OAAO;AAAA,QACZ,GAAO;AAAA,UACF,WAAW;AAAA,UACX,OAAO;AAAA,UACP,KAAK;AAAA,UACL,cAAc;AAAA,UACd,YAAY;AAAA,UACZ,UAAUpB;AAAA,UACV,WAAW;AAAA,UACX,UAAU,CAACkB,IAAGnB,IAAGF,GAAE,oBAAoB;AAAA,QACvC,GAAEA,GAAE,qBAAqBA,GAAE,oBAAoB;AAAA,MAChD,GAAEyB,EAAC;AAAA,IACJ;AAAA,EACD;AAAA,EACD,UAAU,CAAAzB,OAAK;AACd,UAAMG,KAAIH,GAAE,OACXC,KAAI,EAAED,EAAC,GACPE,KAAI,CAACF,GAAE,kBAAkBA,GAAE,iBAAiB;AAC7C,WAAO;AAAA,MACN,MAAM;AAAA,MACN,kBAAkB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,QACT,kBAAkB;AAAA,MAClB;AAAA,MACD,kBAAkB;AAAA,QACjB,kBAAkB;AAAA,MAClB;AAAA,MACD,UAAU,CAACC,GAAE,eAAe;AAAA,QAC3B,OAAO;AAAA,MACX,GAAMA,GAAE,iBAAiB;AAAA,QACrB,WAAW;AAAA,QACX,OAAO;AAAA,QACP,WAAW;AAAA,MACf,GAAM;AAAA,QACF,WAAW;AAAA,QACX,OAAO;AAAA,QACP,WAAW;AAAA,MACf,GAAMA,GAAE,yBAAyB;AAAA,QAC7B,WAAW;AAAA,QACX,UAAU,CAAC;AAAA,UACV,OAAO,OAAO,GAAG,KAAK,GAAG,IAAI;AAAA,QAClC,GAAO;AAAA,UACF,OAAO,WAAW,GAAG,KAAK,GAAG,IAAI;AAAA,QACtC,CAAK;AAAA,MACL,GAAMA,GAAE,cAAc;AAAA,QAClB,WAAW;AAAA,QACX,OAAO,SAAS,GAAG,KAAK,GAAG,IAAI;AAAA,MACnC,GAAM;AAAA,QACF,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU,CAACA,GAAE,eAAeA,GAAE,UAAUA,GAAE,WAAWA,GAAE,iBAAiB,GAAGC,IAAG;AAAA,UAC7E,OAAO;AAAA,UACP,KAAK;AAAA,UACL,WAAW;AAAA,UACX,UAAU;AAAA,YACT,UAAU;AAAA,UACV;AAAA,UACD,UAAU,CAAC,GAAGA,IAAG;AAAA,YAChB,WAAW;AAAA,YACX,OAAO;AAAA,YACP,gBAAgB;AAAA,YAChB,YAAY;AAAA,UAClB,CAAM;AAAA,QACN,GAAOD,GAAE,iBAAiB;AAAA,MAC1B,GAAM;AAAA,QACF,OAAOE,GAAE,UAAU,GAAG;AAAA,QACtB,KAAK;AAAA,QACL,WAAW;AAAA,QACX,SAAS;AAAA,QACT,UAAU,CAAC;AAAA,UACV,WAAW;AAAA,UACX,OAAO;AAAA,QACZ,GAAO;AAAA,UACF,OAAO;AAAA,UACP,gBAAgB;AAAA,UAChB,YAAY;AAAA,UACZ,WAAW;AAAA,UACX,UAAU;AAAA,YACT,UAAU;AAAA,YACV,SAAS;AAAA,YACT,WAAW,GAAG,KAAK,GAAG;AAAA,UACtB;AAAA,UACD,UAAU,CAAC;AAAA,YACV,OAAO;AAAA,YACP,WAAW;AAAA,UACjB,GAAQ,GAAGD,IAAGD,GAAE,eAAe;AAAA,QAC/B,CAAK;AAAA,MACL,GAAM;AAAA,QACF,WAAW;AAAA,QACX,OAAO,SAAS,EAAE,KAAK,GAAG,IAAI;AAAA,MAClC,CAAI;AAAA,IACD;AAAA,EACD;AAAA,EACD,WAAW,CAAAD,OAAK;AACf,UAAMG,KAAIH,GAAE;AACZ,WAAO;AAAA,MACN,MAAM;AAAA,MACN,SAAS,CAAC,OAAO;AAAA,MACjB,UAAU,CAAC;AAAA,QACV,WAAW;AAAA,QACX,WAAW;AAAA,QACX,OAAOG,GAAE,OAAO,gCAAgC,+BAA+B,sBAAsB;AAAA,MACzG,GAAM;AAAA,QACF,WAAW;AAAA,QACX,UAAU,CAAC;AAAA,UACV,OAAOA,GAAE,OAAO,WAAW,UAAU,SAAS,SAAS,WAAW,UAAU,aAAa;AAAA,UACzF,KAAK;AAAA,QACV,GAAO;AAAA,UACF,OAAO;AAAA,QACZ,CAAK;AAAA,MACL,GAAM;AAAA,QACF,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,MACT,GAAM;AAAA,QACF,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,MACT,GAAM;AAAA,QACF,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,MACT,CAAI;AAAA,IACD;AAAA,EACD;AAAA,EACD,SAAS,CAAAH,OAAK;AACb,UAAMG,KAAI;AAAA,MACT,SAAS;AAAA,QAAC;AAAA,QAAS;AAAA,QAAQ;AAAA,QAAQ;AAAA,QAAS;AAAA,QAAY;AAAA,QAAW;AAAA,QAAS;AAAA,QAAQ;AAAA,QAAe;AAAA,QAClG;AAAA,QAAQ;AAAA,QAAM;AAAA,QAAQ;AAAA,QAAM;AAAA,QAAU;AAAA,QAAa;AAAA,QAAO;AAAA,QAAW;AAAA,QAAS;AAAA,QAAU;AAAA,QACxF;AAAA,QAAU;AAAA,QAAU;AAAA,QAAQ;AAAA,MAC5B;AAAA,MACD,MAAM;AAAA,QAAC;AAAA,QAAQ;AAAA,QAAQ;AAAA,QAAa;AAAA,QAAc;AAAA,QAAS;AAAA,QAAW;AAAA,QAAW;AAAA,QAAQ;AAAA,QAAS;AAAA,QACjG;AAAA,QAAS;AAAA,QAAU;AAAA,QAAS;AAAA,QAAU;AAAA,QAAU;AAAA,QAAU;AAAA,QAAO;AAAA,QAAQ;AAAA,QAAW;AAAA,MACpF;AAAA,MACD,SAAS,CAAC,QAAQ,SAAS,QAAQ,KAAK;AAAA,MACxC,UAAU;AAAA,QAAC;AAAA,QAAU;AAAA,QAAO;AAAA,QAAS;AAAA,QAAW;AAAA,QAAQ;AAAA,QAAQ;AAAA,QAAO;AAAA,QAAQ;AAAA,QAAO;AAAA,QAAS;AAAA,QAC9F;AAAA,QAAW;AAAA,QAAQ;AAAA,QAAW;AAAA,MAC9B;AAAA,IACJ;AACE,WAAO;AAAA,MACN,MAAM;AAAA,MACN,SAAS,CAAC,QAAQ;AAAA,MAClB,UAAUA;AAAA,MACV,SAAS;AAAA,MACT,UAAU,CAACH,GAAE,qBAAqBA,GAAE,sBAAsB;AAAA,QACzD,WAAW;AAAA,QACX,UAAU,CAACA,GAAE,mBAAmBA,GAAE,kBAAkB;AAAA,UACnD,OAAO;AAAA,UACP,KAAK;AAAA,QACV,CAAK;AAAA,MACL,GAAM;AAAA,QACF,WAAW;AAAA,QACX,UAAU,CAAC;AAAA,UACV,OAAOA,GAAE,cAAc;AAAA,UACvB,WAAW;AAAA,QAChB,GAAOA,GAAE,aAAa;AAAA,MACtB,GAAM;AAAA,QACF,OAAO;AAAA,MACX,GAAM;AAAA,QACF,WAAW;AAAA,QACX,eAAe;AAAA,QACf,KAAK;AAAA,QACL,YAAY;AAAA,QACZ,UAAU,CAACA,GAAE,YAAY;AAAA,UACxB,WAAW;AAAA,UACX,OAAO;AAAA,UACP,KAAK;AAAA,UACL,YAAY;AAAA,UACZ,UAAUG;AAAA,UACV,SAAS;AAAA,QACd,CAAK;AAAA,MACL,CAAI;AAAA,IACD;AAAA,EACD;AAAA,EACD,cAAc,CAAAH,OAAK;AAClB,UAAMG,KAAIH,GAAE;AACZ,WAAO;AAAA,MACN,MAAM;AAAA,MACN,SAAS,CAAC,KAAK;AAAA,MACf,kBAAkB;AAAA,MAClB,mBAAmB;AAAA,MACnB,UAAU;AAAA,QACT,SAAS;AAAA,UAAC;AAAA,UAAS;AAAA,UAAY;AAAA,UAAgB;AAAA,UAAQ;AAAA,UAAS;AAAA,UAAU;AAAA,UAAa;AAAA,UACtF;AAAA,UAAS;AAAA,UAAU;AAAA,UAAY;AAAA,UAAQ;AAAA,QACvC;AAAA,QACD,SAAS,CAAC,QAAQ,SAAS,MAAM;AAAA,MACjC;AAAA,MACD,UAAU,CAACA,GAAE,mBAAmBA,GAAE,mBAAmBA,GAAE,aAAa;AAAA,QACnE,OAAO;AAAA,QACP,OAAO;AAAA,QACP,WAAW;AAAA,MACf,GAAM;AAAA,QACF,OAAO;AAAA,QACP,OAAO;AAAA,QACP,WAAW;AAAA,MACf,GAAM;AAAA,QACF,OAAO;AAAA,QACP,OAAO;AAAA,QACP,KAAK;AAAA,QACL,YAAY;AAAA,QACZ,WAAW;AAAA,MACf,GAAM;AAAA,QACF,OAAO;AAAA,QACP,OAAO;AAAA,QACP,YAAY;AAAA,MAChB,GAAM;AAAA,QACF,OAAO;AAAA,QACP,OAAOG,GAAE,OAAO,0BAA0BA,GAAE,UAAU,MAAM,CAAC;AAAA,QAC7D,WAAW;AAAA,MACf,CAAI;AAAA,MACD,SAAS,CAAC,SAAS,OAAO;AAAA,IAC1B;AAAA,EACD;AAAA,EACD,UAAU,CAAAH,OAAK;AACd,UAAMG,KAAIH,GAAE,OACXC,KAAI;AAAA,MACH,WAAW;AAAA,MACX,WAAW;AAAA,MACX,UAAU,CAAC;AAAA,QACV,OAAO;AAAA,MACZ,GAAO;AAAA,QACF,OAAOD,GAAE;AAAA,MACd,CAAK;AAAA,IACD,GACDE,KAAIF,GAAE;AACP,IAAAE,GAAE,WAAW,CAAC;AAAA,MACb,OAAO;AAAA,MACP,KAAK;AAAA,IACR,GAAK;AAAA,MACF,OAAO;AAAA,MACP,KAAK;AAAA,IACR,CAAG;AACD,UAAME,KAAI;AAAA,MACR,WAAW;AAAA,MACX,UAAU,CAAC;AAAA,QACV,OAAO;AAAA,MACZ,GAAO;AAAA,QACF,OAAO;AAAA,MACZ,CAAK;AAAA,IACD,GACDC,KAAI;AAAA,MACH,WAAW;AAAA,MACX,OAAO;AAAA,IACP,GACDE,KAAI;AAAA,MACH,WAAW;AAAA,MACX,UAAU,CAACP,GAAE,gBAAgB;AAAA,MAC7B,UAAU,CAAC;AAAA,QACV,OAAO;AAAA,QACP,KAAK;AAAA,QACL,WAAW;AAAA,MAChB,GAAO;AAAA,QACF,OAAO;AAAA,QACP,KAAK;AAAA,QACL,WAAW;AAAA,MAChB,GAAO;AAAA,QACF,OAAO;AAAA,QACP,KAAK;AAAA,MACV,GAAO;AAAA,QACF,OAAO;AAAA,QACP,KAAK;AAAA,MACV,CAAK;AAAA,IACD,GACDQ,KAAI;AAAA,MACH,OAAO;AAAA,MACP,KAAK;AAAA,MACL,UAAU,CAACN,IAAGG,IAAGD,IAAGG,IAAGN,IAAG,MAAM;AAAA,MAChC,WAAW;AAAA,IACX,GACDQ,KAAIN,GAAE,OAAO,kBAAkB,iBAAiB,SAAS;AAC1D,WAAO;AAAA,MACN,MAAM;AAAA,MACN,SAAS,CAAC,MAAM;AAAA,MAChB,kBAAkB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU,CAACD,IAAG;AAAA,QACb,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,MACT,GAAM;AAAA,QACF,OAAOC,GAAE,OAAOM,IAAG,gBAAgBA,IAAG,MAAMN,GAAE,UAAU,eAAe,CAAC;AAAA,QACxE,WAAW;AAAA,QACX,QAAQ;AAAA,UACP,KAAK;AAAA,UACL,UAAU,CAACD,IAAGM,IAAGH,IAAGD,IAAGG,IAAGN,EAAC;AAAA,QAC3B;AAAA,MACL,CAAI;AAAA,IACD;AAAA,EACD;AAAA,EACD,WAAW,CAAAD,OAAK;AACf,UAAMG,KAAIH,GAAE,OACXC,KAAI,kCACJC,KAAID,KAAI,GAAG,SAASA,KAAI,oBAAoBA,KAAI,YAAY,QAAQ,CAAC,GACrEG,KAAI;AAAA,MACH,SAAS;AAAA,QAAC;AAAA,QAAgB;AAAA,QAAY;AAAA,QAAW;AAAA,QAAO;AAAA,QAAU;AAAA,QAAM;AAAA,QAAU;AAAA,QAAO;AAAA,QACxF;AAAA,QAAY;AAAA,QAAW;AAAA,QAAa;AAAA,QAAU;AAAA,QAAU;AAAA,QAAS;AAAA,QAAQ;AAAA,QAAQ;AAAA,QAAQ;AAAA,QACzF;AAAA,QAAa;AAAA,QAAS;AAAA,QAAc;AAAA,QAAY;AAAA,QAAQ;AAAA,QAAU;AAAA,QAAW;AAAA,QAAW;AAAA,QACxF;AAAA,QAAO;AAAA,QAAU;AAAA,QAAY;AAAA,QAAU;AAAA,QAAa;AAAA,QAAU;AAAA,QAAW;AAAA,QAAU;AAAA,QACnF;AAAA,QAAW;AAAA,QAAM;AAAA,QAAU;AAAA,QAAS;AAAA,MACpC;AAAA,MACD,SAAS,CAAC,SAAS,QAAQ,MAAM;AAAA,MACjC,MAAM,CAAC,QAAQ,WAAW,QAAQ,SAAS,OAAO,QAAQ,SAAS,QAAQ;AAAA,MAC3E,UAAU,CAAC,SAAS,MAAM;AAAA,IAC1B,GACDC,KAAI;AAAA,MACH,WAAW;AAAA,MACX,OAAO,MAAMJ;AAAA,MACb,UAAU,CAAC;AAAA,QACV,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,MACtB,CAAK;AAAA,IACD,GACDM,KAAI;AAAA,MACH,WAAW;AAAA,MACX,OAAO;AAAA,MACP,KAAK;AAAA,MACL,UAAUH;AAAA,MACV,WAAW;AAAA,MACX,UAAU,CAACJ,GAAE,oBAAoB;AAAA,MACjC,YAAY;AAAA,IAChB;AACE,WAAO;AAAA,MACN,MAAM;AAAA,MACN,SAAS,CAAC,KAAK;AAAA,MACf,UAAUI;AAAA,MACV,SAAS;AAAA,MACT,UAAU,CAACJ,GAAE,QAAQ,WAAW,QAAQ;AAAA,QACvC,WAAW;AAAA,QACX,UAAU,CAAC;AAAA,UACV,OAAO;AAAA,UACP,WAAW;AAAA,QAChB,GAAO;AAAA,UACF,WAAW;AAAA,UACX,OAAO;AAAA,QACZ,CAAK;AAAA,MACL,CAAI,GAAG;AAAA,QACH,OAAO;AAAA,QACP,UAAU;AAAA,QACV,WAAW;AAAA,MACX,GAAEA,GAAE,qBAAqBA,GAAE,sBAAsB;AAAA,QACjD,OAAO;AAAA,QACP,KAAK;AAAA,QACL,WAAW;AAAA,QACX,UAAU,CAACA,GAAE,gBAAgB;AAAA,MAC7B,GAAEA,GAAE,kBAAkBA,GAAE,mBAAmB;AAAA,QAC3C,OAAO,CAAC,qDAAqD,OAAOC,EAAC;AAAA,QACrE,WAAW;AAAA,UACV,GAAG;AAAA,UACH,GAAG;AAAA,QACH;AAAA,MACL,GAAM;AAAA,QACF,OAAO;AAAA,QACP,OAAO;AAAA,MACX,GAAM;AAAA,QACF,OAAO,CAACE,GAAE,OAAO,YAAYF,EAAC,GAAG,OAAOA,IAAG,OAAO,QAAQ;AAAA,QAC1D,WAAW;AAAA,UACV,GAAG;AAAA,UACH,GAAG;AAAA,UACH,GAAG;AAAA,QACH;AAAA,MACL,GAAM;AAAA,QACF,OAAO,CAAC,UAAU,OAAOA,EAAC;AAAA,QAC1B,WAAW;AAAA,UACV,GAAG;AAAA,UACH,GAAG;AAAA,QACH;AAAA,QACD,UAAU,CAACM,IAAGP,GAAE,qBAAqBA,GAAE,oBAAoB;AAAA,MAC/D,GAAM;AAAA,QACF,eAAe;AAAA,QACf,WAAW;AAAA,MACf,GAAM;AAAA,QACF,OAAO,CAAC,QAAQE,KAAI,SAASF,GAAE,qBAAqB,WAAW;AAAA,QAC/D,WAAW;AAAA,UACV,GAAG;AAAA,QACH;AAAA,QACD,UAAUI;AAAA,QACV,UAAU,CAAC;AAAA,UACV,WAAW;AAAA,UACX,OAAO;AAAA,UACP,KAAK;AAAA,UACL,UAAUA;AAAA,UACV,WAAW;AAAA,UACX,UAAU,CAACC,IAAGL,GAAE,kBAAkBA,GAAE,mBAAmB,IAAIA,GAAE,oBAAoB;AAAA,QACjF,GAAEA,GAAE,qBAAqBA,GAAE,oBAAoB;AAAA,MACpD,GAAM,IAAIK,EAAC;AAAA,IACR;AAAA,EACD;AAAA,EACD,iBAAiB;AAAA,EACjB,WAAW,CAAAL,OAAK;AACf,UAAMG,KAAI,CAAC,QAAQ,SAAS,MAAM,GACjCF,KAAI;AAAA,MACH,OAAO;AAAA,MACP,eAAeE,GAAE,KAAK,GAAG;AAAA,IAC7B;AACE,WAAO;AAAA,MACN,MAAM;AAAA,MACN,UAAU;AAAA,QACT,SAASA;AAAA,MACT;AAAA,MACD,UAAU,CAAC;AAAA,QACV,WAAW;AAAA,QACX,OAAO;AAAA,QACP,WAAW;AAAA,MACf,GAAM;AAAA,QACF,OAAO;AAAA,QACP,WAAW;AAAA,QACX,WAAW;AAAA,MACf,GAAMH,GAAE,mBAAmBC,IAAGD,GAAE,eAAeA,GAAE,qBAAqBA,GAAE,oBAAoB;AAAA,MACzF,SAAS;AAAA,IACT;AAAA,EACD;AAAA,EACD,aAAa,CAAAA,OAAK;AACjB,UAAMG,KAAI;AAAA,MACR,SAAS;AAAA,MACT,UAAU;AAAA,MACV,SAAS;AAAA,IACT,GACDF,KAAI;AAAA,MACH,WAAW;AAAA,MACX,OAAOD,GAAE,sBAAsB;AAAA,IAC/B,GACDE,KAAI;AAAA,MACH,WAAW;AAAA,MACX,OAAO;AAAA,MACP,KAAK;AAAA,MACL,UAAU,CAACF,GAAE,aAAa;AAAA,IAC1B,GACDI,KAAI;AAAA,MACH,WAAW;AAAA,MACX,OAAO,QAAQJ,GAAE;AAAA,IACjB,GACDK,KAAI;AAAA,MACH,WAAW;AAAA,MACX,UAAU,CAAC;AAAA,QACV,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU,CAACD,IAAGF,EAAC;AAAA,MACpB,GAAO;AAAA,QACF,OAAO;AAAA,QACP,KAAK;AAAA,QACL,SAAS;AAAA,QACT,UAAU,CAACF,GAAE,gBAAgB;AAAA,MAClC,GAAO;AAAA,QACF,OAAO;AAAA,QACP,KAAK;AAAA,QACL,SAAS;AAAA,QACT,UAAU,CAACA,GAAE,kBAAkBI,IAAGF,EAAC;AAAA,MACxC,CAAK;AAAA,IACL;AACE,IAAAA,GAAE,SAAS,KAAKG,EAAC;AACjB,UAAME,KAAI;AAAA,MACR,WAAW;AAAA,MACX,OAAO,kFAAkFP,GACvF,sBAAsB;AAAA,IACxB,GACDQ,KAAI;AAAA,MACH,WAAW;AAAA,MACX,OAAO,MAAMR,GAAE;AAAA,MACf,UAAU,CAAC;AAAA,QACV,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU,CAACA,GAAE,QAAQK,IAAG;AAAA,UACvB,WAAW;AAAA,QACX,CAAA,GAAG,MAAM;AAAA,MACf,CAAK;AAAA,IACD,GACDI,KAAI,IACJM,KAAIf,GAAE,QAAQ,QAAQ,QAAQ;AAAA,MAC7B,UAAU,CAACA,GAAE,oBAAoB;AAAA,IACrC,CAAI,GACDU,KAAI;AAAA,MACH,UAAU,CAAC;AAAA,QACV,WAAW;AAAA,QACX,OAAOV,GAAE;AAAA,MACd,GAAO;AAAA,QACF,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU,CAAE;AAAA,MACjB,CAAK;AAAA,IACD,GACDqB,KAAIX;AACL,WAAOW,GAAE,SAAS,CAAC,EAAE,WAAW,CAACX,EAAC,GAAGA,GAAE,SAAS,CAAC,EAAE,WAAW,CAACW,EAAC,GAAG;AAAA,MAClE,MAAM;AAAA,MACN,SAAS,CAAC,MAAM,KAAK;AAAA,MACrB,UAAUlB;AAAA,MACV,UAAU,CAACH,GAAE,QAAQ,WAAW,QAAQ;AAAA,QACvC,WAAW;AAAA,QACX,UAAU,CAAC;AAAA,UACV,WAAW;AAAA,UACX,OAAO;AAAA,QACZ,CAAK;AAAA,MACL,CAAI,GAAGA,GAAE,qBAAqBe,IAAG;AAAA,QAC7B,WAAW;AAAA,QACX,OAAO;AAAA,QACP,QAAQ;AAAA,UACP,UAAU,CAAC;AAAA,YACV,WAAW;AAAA,YACX,OAAO;AAAA,UACb,CAAM;AAAA,QACD;AAAA,MACL,GAAMd,IAAGM,IAAGC,IAAG;AAAA,QACX,WAAW;AAAA,QACX,eAAe;AAAA,QACf,KAAK;AAAA,QACL,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,UAAUL;AAAA,QACV,WAAW;AAAA,QACX,UAAU,CAAC;AAAA,UACV,OAAOH,GAAE,sBAAsB;AAAA,UAC/B,aAAa;AAAA,UACb,WAAW;AAAA,UACX,UAAU,CAACA,GAAE,qBAAqB;AAAA,QACvC,GAAO;AAAA,UACF,WAAW;AAAA,UACX,OAAO;AAAA,UACP,KAAK;AAAA,UACL,UAAU;AAAA,UACV,WAAW;AAAA,QAChB,GAAO;AAAA,UACF,WAAW;AAAA,UACX,OAAO;AAAA,UACP,KAAK;AAAA,UACL,YAAY;AAAA,UACZ,UAAUG;AAAA,UACV,WAAW;AAAA,UACX,UAAU,CAAC;AAAA,YACV,OAAO;AAAA,YACP,KAAK;AAAA,YACL,gBAAgB;AAAA,YAChB,UAAU,CAACO,IAAGV,GAAE,qBAAqBe,EAAC;AAAA,YACtC,WAAW;AAAA,UACjB,GAAQf,GAAE,qBAAqBe,IAAGR,IAAGC,IAAGH,IAAGL,GAAE,aAAa;AAAA,QACrD,GAAEe,EAAC;AAAA,MACR,GAAM;AAAA,QACF,OAAO,CAAC,yBAAyB,OAAOf,GAAE,mBAAmB;AAAA,QAC7D,YAAY;AAAA,UACX,GAAG;AAAA,QACH;AAAA,QACD,UAAU;AAAA,QACV,KAAK;AAAA,QACL,YAAY;AAAA,QACZ,SAAS;AAAA,QACT,UAAU,CAAC;AAAA,UACV,eAAe;AAAA,QACpB,GAAOA,GAAE,uBAAuB;AAAA,UAC3B,WAAW;AAAA,UACX,OAAO;AAAA,UACP,KAAK;AAAA,UACL,cAAc;AAAA,UACd,YAAY;AAAA,UACZ,WAAW;AAAA,QAChB,GAAO;AAAA,UACF,WAAW;AAAA,UACX,OAAO;AAAA,UACP,KAAK;AAAA,UACL,cAAc;AAAA,UACd,WAAW;AAAA,QAChB,GAAOO,IAAGC,EAAC;AAAA,MACP,GAAEH,IAAG;AAAA,QACL,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,SAAS;AAAA,MACT,GAAEI,EAAC;AAAA,IACJ;AAAA,EACD;AAAA,EACD,WAAW,CAAAT,OAAK;AACf,UAAMG,KAAI,EAAEH,EAAC,GACZC,KAAI,IACJC,KAAI,4BACJE,KAAI,CAAE,GACNC,KAAI,CAAE,GACNE,KAAI,CAAAP,QAAM;AAAA,MACT,WAAW;AAAA,MACX,OAAO,OAAOA,KAAI,QAAQA;AAAA,IAC9B,IACGQ,KAAI,CAACR,IAAGG,IAAGF,QAAO;AAAA,MACjB,WAAWD;AAAA,MACX,OAAOG;AAAA,MACP,WAAWF;AAAA,IACf,IACGQ,KAAI;AAAA,MACH,UAAU;AAAA,MACV,SAAS;AAAA,MACT,WAAW,GAAG,KAAK,GAAG;AAAA,IACtB,GACDM,KAAI;AAAA,MACH,OAAO;AAAA,MACP,KAAK;AAAA,MACL,UAAUV;AAAA,MACV,UAAUI;AAAA,MACV,WAAW;AAAA,IACf;AACE,IAAAJ,GAAE,KAAKL,GAAE,qBAAqBA,GAAE,sBAAsBO,GAAE,GAAG,GAAGA,GAAE,GAAG,GAAGJ,GAAE,iBAAiB;AAAA,MACxF,OAAO;AAAA,MACP,QAAQ;AAAA,QACP,WAAW;AAAA,QACX,KAAK;AAAA,QACL,YAAY;AAAA,MACZ;AAAA,IACD,GAAEA,GAAE,UAAUY,IAAGP,GAAE,YAAY,cAAc,EAAE,GAAGA,GAAE,YAAY,gBAAgB,GAAGA;AAAA,MAAE;AAAA,MACrF;AAAA,IAAY,GAAG;AAAA,MACf,WAAW;AAAA,MACX,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,YAAY;AAAA,IACf,GAAKL,GAAE,WAAW;AAAA,MACf,eAAe;AAAA,IAClB,GAAKA,GAAE,iBAAiB;AACtB,UAAMO,KAAIL,GAAE,OAAO;AAAA,MACjB,OAAO;AAAA,MACP,KAAK;AAAA,MACL,UAAUD;AAAA,IACd,CAAI,GACDiB,KAAI;AAAA,MACH,eAAe;AAAA,MACf,gBAAgB;AAAA,MAChB,UAAU,CAAC;AAAA,QACV,eAAe;AAAA,MACpB,CAAK,EAAE,OAAOhB,EAAC;AAAA,IACX,GACDkB,KAAI;AAAA,MACH,OAAOrB,KAAI;AAAA,MACX,aAAa;AAAA,MACb,KAAK;AAAA,MACL,WAAW;AAAA,MACX,UAAU,CAAC;AAAA,QACV,OAAO;AAAA,MACZ,GAAOC,GAAE,cAAc;AAAA,QAClB,WAAW;AAAA,QACX,OAAO,SAAS,GAAG,KAAK,GAAG,IAAI;AAAA,QAC/B,KAAK;AAAA,QACL,QAAQ;AAAA,UACP,gBAAgB;AAAA,UAChB,SAAS;AAAA,UACT,WAAW;AAAA,UACX,UAAUE;AAAA,QACV;AAAA,MACN,CAAK;AAAA,IACD,GACDmB,KAAI;AAAA,MACH,WAAW;AAAA,MACX,OAAO;AAAA,MACP,QAAQ;AAAA,QACP,KAAK;AAAA,QACL,UAAUf;AAAA,QACV,WAAW;AAAA,QACX,UAAUJ;AAAA,QACV,WAAW;AAAA,MACX;AAAA,IACD,GACDoB,KAAI;AAAA,MACH,WAAW;AAAA,MACX,UAAU,CAAC;AAAA,QACV,OAAO;AAAA,QACP,WAAW;AAAA,MAChB,GAAO;AAAA,QACF,OAAO;AAAA,MACZ,CAAK;AAAA,MACD,QAAQ;AAAA,QACP,KAAK;AAAA,QACL,WAAW;AAAA,QACX,UAAUf;AAAA,MACV;AAAA,IACD,GACDgB,KAAI;AAAA,MACH,UAAU,CAAC;AAAA,QACV,OAAO;AAAA,QACP,KAAK;AAAA,MACV,GAAO;AAAA,QACF,OAAOxB;AAAA,QACP,KAAK;AAAA,MACV,CAAK;AAAA,MACD,aAAa;AAAA,MACb,WAAW;AAAA,MACX,SAAS;AAAA,MACT,WAAW;AAAA,MACX,UAAU,CAACF,GAAE,qBAAqBA,GAAE,sBAAsBqB,IAAGb,GAAE,WAAW,QAAQ,GAAGA;AAAA,QAAE;AAAA,QACtF;AAAA,MAAgB,GAAG;AAAA,QACnB,OAAO,SAAS,EAAE,KAAK,GAAG,IAAI;AAAA,QAC9B,WAAW;AAAA,MACX,GAAEL,GAAE,iBAAiBK,GAAE,gBAAgBN,IAAG,CAAC,GAAGM,GAAE,eAAe,MAAMN,EAAC,GAAGM,GAAE,kBAAkB,QAC7FN,IAAG,CAAC,GAAGM,GAAE,gBAAgB,KAAK,CAAC,GAAGL,GAAE,yBAAyB;AAAA,QAC7D,WAAW;AAAA,QACX,OAAO,OAAO,GAAG,KAAK,GAAG,IAAI;AAAA,MAClC,GAAO;AAAA,QACF,WAAW;AAAA,QACX,OAAO,WAAW,GAAG,KAAK,GAAG,IAAI;AAAA,MACtC,GAAO;AAAA,QACF,OAAO;AAAA,QACP,KAAK;AAAA,QACL,WAAW;AAAA,QACX,UAAUO;AAAA,MACf,GAAO;AAAA,QACF,OAAO;AAAA,MACZ,GAAOP,GAAE,iBAAiB;AAAA,IACtB,GACDQ,KAAI;AAAA,MACH,OAAO,gBAAgBV,GAAE,KAAK,GAAG,CAAC;AAAA,MAClC,aAAa;AAAA,MACb,UAAU,CAACyB,EAAC;AAAA,IAChB;AACE,WAAOtB,GAAE,KAAKJ,GAAE,qBAAqBA,GAAE,sBAAsBwB,IAAGC,IAAGd,IAAGY,IAAGG,IAAGL,IAAGlB,GAAE,iBAAiB,GAAG;AAAA,MACpG,MAAM;AAAA,MACN,kBAAkB;AAAA,MAClB,SAAS;AAAA,MACT,UAAUC;AAAA,IACV;AAAA,EACD;AAAA,EACD,UAAU,CAAAJ,OAAK;AACd,UAAMG,KAAI,YACTF,KAAI,YACJC,KAAI;AAAA,MACH,OAAOC;AAAA,MACP,KAAKF;AAAA,MACL,UAAU,CAAC,MAAM;AAAA,IACjB,GACDG,KAAI,CAACJ,GAAE,QAAQ,kBAAkB,GAAG,GAAGA,GAAE,QAAQ,cAAcC,IAAG;AAAA,MACjE,UAAU,CAACC,EAAC;AAAA,MACZ,WAAW;AAAA,IACX,CAAA,CAAC;AACH,WAAO;AAAA,MACN,MAAM;AAAA,MACN,UAAU;AAAA,QACT,UAAUF,GAAE;AAAA,QACZ,SAAS;AAAA,QACT,SAAS;AAAA,QACT,UAAU;AAAA,MACV;AAAA,MACD,UAAUI,GAAE,OAAO,CAAC;AAAA,QACnB,WAAW;AAAA,QACX,eAAe;AAAA,QACf,KAAK;AAAA,QACL,UAAU,CAACJ,GAAE,QAAQA,GAAE,YAAY;AAAA,UAClC,OAAO;AAAA,QACZ,CAAK,GAAG;AAAA,UACH,WAAW;AAAA,UACX,OAAO;AAAA,UACP,gBAAgB;AAAA,UAChB,UAAUI;AAAA,QACf,CAAK,EAAE,OAAOA,EAAC;AAAA,MACf,GAAMJ,GAAE,eAAeA,GAAE,kBAAkBA,GAAE,mBAAmB;AAAA,QAC5D,WAAW;AAAA,QACX,OAAOG;AAAA,QACP,KAAKF;AAAA,QACL,UAAU,CAACC,EAAC;AAAA,QACZ,WAAW;AAAA,MACf,CAAI,CAAC;AAAA,IACF;AAAA,EACD;AAAA,EACD,eAAe,CAAAF,OAAK;AACnB,UAAMG,KAAI;AAAA,MACR,WAAW;AAAA,MACX,UAAU,CAAC;AAAA,QACV,OAAO,WAAWH,GAAE,sBAAsB;AAAA,QAC1C,UAAU,CAACA,GAAE,gBAAgB;AAAA,MAClC,GAAO;AAAA,QACF,OAAO;AAAA,MACZ,CAAK;AAAA,IACD,GACDC,KAAI;AAAA,MACH,WAAW;AAAA,MACX,OAAO;AAAA,MACP,KAAK;AAAA,MACL,UAAU,CAACD,GAAE,kBAAkBG,EAAC;AAAA,IAChC,GACDD,KAAI;AAAA,MACH,WAAW;AAAA,MACX,OAAO;AAAA,MACP,KAAK;AAAA,MACL,UAAU;AAAA,QACT,UAAU;AAAA,MACV;AAAA,MACD,UAAU,CAACC,EAAC;AAAA,IACZ,GACDC,KAAI;AAAA,MACH,OAAO,MAAMJ,GAAE,sBAAsB;AAAA,IACrC,GACDK,KAAI;AAAA,MACH,WAAW;AAAA,MACX,OAAO;AAAA,MACP,KAAK;AAAA,MACL,UAAU,CAACF,EAAC;AAAA,IAChB;AACE,WAAO;AAAA,MACN,MAAM;AAAA,MACN,SAAS,CAAC,MAAM,OAAO,MAAM;AAAA,MAC7B,UAAU;AAAA,QACT,UAAU;AAAA,QACV,SAAS;AAAA,MACT;AAAA,MACD,UAAU,CAACH,GAAE,mBAAmBG,IAAGF,IAAGC,IAAGE,IAAG;AAAA,QAC3C,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU;AAAA,UACT,UAAU;AAAA,UACV,SAAS;AAAA,QACT;AAAA,MACD,GAAEC,EAAC;AAAA,IACJ;AAAA,EACD;AAAA,EACD,UAAU,CAAAL,OAAK;AACd,UAAMG,KAAIH,GAAE,OACXC,KAAIE,GAAE;AAAA,MACL;AAAA,MACAA,GAAE;AAAA,QACD;AAAA,MACC;AAAA,MACF;AAAA,IACC,GACFD,KAAI;AAAA,MACH,WAAW;AAAA,MACX,OAAO;AAAA,IACP,GACDE,KAAI;AAAA,MACH,OAAO;AAAA,MACP,UAAU,CAAC;AAAA,QACV,WAAW;AAAA,QACX,OAAO;AAAA,QACP,SAAS;AAAA,MACd,CAAK;AAAA,IACD,GACDC,KAAIL,GAAE,QAAQI,IAAG;AAAA,MAChB,OAAO;AAAA,MACP,KAAK;AAAA,IACT,CAAI,GACDG,KAAIP,GAAE,QAAQA,GAAE,kBAAkB;AAAA,MACjC,WAAW;AAAA,IACf,CAAI,GACDQ,KAAIR,GAAE,QAAQA,GAAE,mBAAmB;AAAA,MAClC,WAAW;AAAA,IACf,CAAI,GACDS,KAAI;AAAA,MACH,gBAAgB;AAAA,MAChB,SAAS;AAAA,MACT,WAAW;AAAA,MACX,UAAU,CAAC;AAAA,QACV,WAAW;AAAA,QACX,OAAO;AAAA,QACP,WAAW;AAAA,MAChB,GAAO;AAAA,QACF,OAAO;AAAA,QACP,WAAW;AAAA,QACX,UAAU,CAAC;AAAA,UACV,WAAW;AAAA,UACX,YAAY;AAAA,UACZ,UAAU,CAAC;AAAA,YACV,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU,CAACP,EAAC;AAAA,UACnB,GAAS;AAAA,YACF,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU,CAACA,EAAC;AAAA,UACnB,GAAS;AAAA,YACF,OAAO;AAAA,UACd,CAAO;AAAA,QACP,CAAM;AAAA,MACN,CAAK;AAAA,IACL;AACE,WAAO;AAAA,MACN,MAAM;AAAA,MACN,SAAS,CAAC,QAAQ,SAAS,OAAO,QAAQ,OAAO,OAAO,OAAO,SAAS,OAAO,KAAK;AAAA,MACpF,kBAAkB;AAAA,MAClB,cAAc;AAAA,MACd,UAAU,CAAC;AAAA,QACV,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,WAAW;AAAA,QACX,UAAU,CAACE,IAAGI,IAAGD,IAAGF,IAAG;AAAA,UACtB,OAAO;AAAA,UACP,KAAK;AAAA,UACL,UAAU,CAAC;AAAA,YACV,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU,CAACD,IAAGC,IAAGG,IAAGD,EAAC;AAAA,UAC3B,CAAM;AAAA,QACN,CAAK;AAAA,MACD,GAAEP,GAAE,QAAQ,QAAQ,OAAO;AAAA,QAC3B,WAAW;AAAA,MACf,CAAI,GAAG;AAAA,QACH,OAAO;AAAA,QACP,KAAK;AAAA,QACL,WAAW;AAAA,MACX,GAAEE,IAAG;AAAA,QACL,WAAW;AAAA,QACX,KAAK;AAAA,QACL,UAAU,CAAC;AAAA,UACV,OAAO;AAAA,UACP,WAAW;AAAA,UACX,UAAU,CAACM,EAAC;AAAA,QACjB,GAAO;AAAA,UACF,OAAO;AAAA,QACZ,CAAK;AAAA,MACL,GAAM;AAAA,QACF,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU;AAAA,UACT,MAAM;AAAA,QACN;AAAA,QACD,UAAU,CAACC,EAAC;AAAA,QACZ,QAAQ;AAAA,UACP,KAAK;AAAA,UACL,WAAW;AAAA,UACX,aAAa,CAAC,OAAO,KAAK;AAAA,QAC1B;AAAA,MACL,GAAM;AAAA,QACF,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU;AAAA,UACT,MAAM;AAAA,QACN;AAAA,QACD,UAAU,CAACA,EAAC;AAAA,QACZ,QAAQ;AAAA,UACP,KAAK;AAAA,UACL,WAAW;AAAA,UACX,aAAa,CAAC,cAAc,cAAc,KAAK;AAAA,QAC/C;AAAA,MACL,GAAM;AAAA,QACF,WAAW;AAAA,QACX,OAAO;AAAA,MACX,GAAM;AAAA,QACF,WAAW;AAAA,QACX,OAAON,GAAE,OAAO,KAAKA,GAAE,UAAUA,GAAE,OAAOF,IAAGE,GAAE,OAAO,OAAO,KAAK,IAAI,CAAC,CAAC,CAAC;AAAA,QACzE,KAAK;AAAA,QACL,UAAU,CAAC;AAAA,UACV,WAAW;AAAA,UACX,OAAOF;AAAA,UACP,WAAW;AAAA,UACX,QAAQQ;AAAA,QACb,CAAK;AAAA,MACL,GAAM;AAAA,QACF,WAAW;AAAA,QACX,OAAON,GAAE,OAAO,OAAOA,GAAE,UAAUA,GAAE,OAAOF,IAAG,GAAG,CAAC,CAAC;AAAA,QACpD,UAAU,CAAC;AAAA,UACV,WAAW;AAAA,UACX,OAAOA;AAAA,UACP,WAAW;AAAA,QAChB,GAAO;AAAA,UACF,OAAO;AAAA,UACP,WAAW;AAAA,UACX,YAAY;AAAA,QACjB,CAAK;AAAA,MACL,CAAI;AAAA,IACD;AAAA,EACD;AAAA,EACD,eAAe,CAAAD,OAAK;AACnB,UAAMG,KAAI;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,WAAW;AAAA,IACX,GACDF,KAAI;AAAA,MACH,UAAU,CAAC;AAAA,QACV,OAAO;AAAA,QACP,WAAW;AAAA,MAChB,GAAO;AAAA,QACF,OAAO;AAAA,QACP,WAAW;AAAA,MAChB,GAAO;AAAA,QACF,OAAOD,GAAE,MAAM,OAAO,aAAa,2BAA2B,YAAY;AAAA,QAC1E,WAAW;AAAA,MAChB,GAAO;AAAA,QACF,OAAO;AAAA,QACP,WAAW;AAAA,MAChB,GAAO;AAAA,QACF,OAAO;AAAA,QACP,WAAW;AAAA,MAChB,CAAK;AAAA,MACD,aAAa;AAAA,MACb,UAAU,CAAC;AAAA,QACV,OAAO;AAAA,MACZ,GAAO;AAAA,QACF,WAAW;AAAA,QACX,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,cAAc;AAAA,QACd,WAAW;AAAA,MAChB,GAAO;AAAA,QACF,WAAW;AAAA,QACX,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,cAAc;AAAA,QACd,YAAY;AAAA,MACjB,GAAO;AAAA,QACF,WAAW;AAAA,QACX,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,cAAc;AAAA,QACd,YAAY;AAAA,MACjB,CAAK;AAAA,IACD,GACDE,KAAI;AAAA,MACH,WAAW;AAAA,MACX,UAAU,CAAE;AAAA,MACZ,UAAU,CAAC;AAAA,QACV,OAAO;AAAA,QACP,KAAK;AAAA,MACV,GAAO;AAAA,QACF,OAAO;AAAA,QACP,KAAK;AAAA,MACV,CAAK;AAAA,IACD,GACDE,KAAI;AAAA,MACH,WAAW;AAAA,MACX,UAAU,CAAE;AAAA,MACZ,UAAU,CAAC;AAAA,QACV,OAAO;AAAA,QACP,KAAK;AAAA,MACV,GAAO;AAAA,QACF,OAAO;AAAA,QACP,KAAK;AAAA,QACL,WAAW;AAAA,MAChB,CAAK;AAAA,IACD,GACDC,KAAIL,GAAE,QAAQE,IAAG;AAAA,MAChB,UAAU,CAAE;AAAA,IAChB,CAAI,GACDK,KAAIP,GAAE,QAAQI,IAAG;AAAA,MAChB,UAAU,CAAE;AAAA,IAChB,CAAI;AACF,IAAAF,GAAE,SAAS,KAAKK,EAAC,GAAGH,GAAE,SAAS,KAAKC,EAAC;AACrC,QAAIG,KAAI,CAACL,IAAGF,EAAC;AACb,WAAO,CAACC,IAAGE,IAAGC,IAAGE,EAAC,EAAE,QAAS,CAAAP,OAAK;AACjC,MAAAA,GAAE,WAAWA,GAAE,SAAS,OAAOQ,EAAC;AAAA,IACnC,CAAK,GAAEA,KAAIA,GAAE,OAAON,IAAGE,EAAC,GAAG;AAAA,MACxB,MAAM;AAAA,MACN,SAAS,CAAC,MAAM,UAAU,KAAK;AAAA,MAC/B,UAAU,CAAC;AAAA,QACV,WAAW;AAAA,QACX,UAAU,CAAC;AAAA,UACV,OAAO;AAAA,UACP,KAAK;AAAA,UACL,UAAUI;AAAA,QACf,GAAO;AAAA,UACF,OAAO;AAAA,UACP,UAAU,CAAC;AAAA,YACV,OAAO;AAAA,UACb,GAAQ;AAAA,YACF,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAUA;AAAA,UAChB,CAAM;AAAA,QACN,CAAK;AAAA,MACD,GAAEL,IAAG;AAAA,QACL,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,YAAY;AAAA,MAChB,GAAMD,IAAGE,IAAG;AAAA,QACR,WAAW;AAAA,QACX,OAAO;AAAA,QACP,UAAUI;AAAA,QACV,KAAK;AAAA,MACT,GAAM;AAAA,QACF,WAAW;AAAA,QACX,UAAU,CAAC;AAAA,UACV,OAAO;AAAA,QACZ,GAAO;AAAA,UACF,OAAO;AAAA,QACZ,GAAO;AAAA,UACF,OAAO;AAAA,UACP,KAAK;AAAA,QACV,GAAO;AAAA,UACF,OAAO;AAAA,UACP,KAAK;AAAA,QACV,GAAO;AAAA,UACF,OAAO;AAAA,QACZ,GAAO;AAAA,UACF,OAAO;AAAA,UACP,UAAU,CAAC;AAAA,YACV,OAAO;AAAA,YACP,KAAK;AAAA,UACX,CAAM;AAAA,UACD,WAAW;AAAA,QAChB,CAAK;AAAA,MACL,GAAM;AAAA,QACF,OAAO;AAAA,QACP,KAAK;AAAA,MACL,GAAEP,IAAG;AAAA,QACL,OAAO;AAAA,QACP,aAAa;AAAA,QACb,UAAU,CAAC;AAAA,UACV,WAAW;AAAA,UACX,OAAO;AAAA,UACP,KAAK;AAAA,UACL,cAAc;AAAA,UACd,YAAY;AAAA,QACjB,GAAO;AAAA,UACF,WAAW;AAAA,UACX,OAAO;AAAA,UACP,KAAK;AAAA,UACL,cAAc;AAAA,QACnB,CAAK;AAAA,MACL,CAAI;AAAA,IACD;AAAA,EACD;AAAA,EACD,iBAAiB,CAAAD,OAAK;AACrB,UAAMG,KAAI,0BACTF,KAAI;AAAA,MACH,UAAUE;AAAA,MACV,SAAS,CAAC,cAAc,UAAU,aAAa,iBAAiB;AAAA,IACpE;AACE,WAAO;AAAA,MACN,MAAM;AAAA,MACN,SAAS,CAAC,MAAM,QAAQ,SAAS,WAAW,eAAe;AAAA,MAC3D,UAAU;AAAA,QACT,qBAAqB,CAAC,QAAQ,OAAO;AAAA,QACrC,UAAUA;AAAA,QACV,SAAS;AAAA,UAAC;AAAA,UAAS;AAAA,UAAU;AAAA,UAAU;AAAA,UAAW;AAAA,UAAS;AAAA,UAAU;AAAA,UAAO;AAAA,UAAS;AAAA,UAAY;AAAA,UAChG;AAAA,UAAW;AAAA,UAAM;AAAA,UAAM;AAAA,UAAU;AAAA,UAAQ;AAAA,UAAQ;AAAA,UAAQ;AAAA,UAAS;AAAA,UAAU;AAAA,UAAO;AAAA,UAAQ;AAAA,UAC3F;AAAA,UAAY;AAAA,UAAY;AAAA,UAAY;AAAA,UAAU;AAAA,UAAY;AAAA,UAAU;AAAA,UAAY;AAAA,UAAU;AAAA,UAC1F;AAAA,UAAQ;AAAA,UAAiB;AAAA,UAAM;AAAA,UAAU;AAAA,UAAa;AAAA,UAAY;AAAA,UAAY;AAAA,UAAU;AAAA,UACxF;AAAA,UAAQ;AAAA,UAAM;AAAA,UAAO;AAAA,UAAS;AAAA,UAAU;AAAA,UAAS;AAAA,UAAU;AAAA,UAAY;AAAA,UAAU;AAAA,UACjF;AAAA,UAAmB;AAAA,UAAY;AAAA,UAAc;AAAA,UAAW;AAAA,UAAQ;AAAA,UAAa;AAAA,UAAQ;AAAA,UACrF;AAAA,UAAU;AAAA,UAAY;AAAA,UAAoB;AAAA,UAAe;AAAA,UAAY;AAAA,UAAa;AAAA,UAClF;AAAA,UAAa;AAAA,UAAW;AAAA,UAAY;AAAA,UAAW;AAAA,UAAS;AAAA,UAAwB;AAAA,UAChF;AAAA,UAAqB;AAAA,UAAqB;AAAA,UAAmB;AAAA,UAAe;AAAA,UAC5E;AAAA,UAAY;AAAA,UAAY;AAAA,UAAa;AAAA,UAAqB;AAAA,UAAgB;AAAA,UAC1E;AAAA,UAAiB;AAAA,UAAU;AAAA,UAAU;AAAA,UAAU;AAAA,UAAqB;AAAA,UAAW;AAAA,UAC/E;AAAA,UAAoB;AAAA,UAAmB;AAAA,UAAS;AAAA,UAAgB;AAAA,UAChE;AAAA,UAAkB;AAAA,UAAqB;AAAA,UAA4B;AAAA,UAAa;AAAA,UAChF;AAAA,UAAiB;AAAA,UAAW;AAAA,UAAc;AAAA,UAAwB;AAAA,UAClE;AAAA,UAAyB;AAAA,UAAwB;AAAA,UAAiB;AAAA,UAAoB;AAAA,UACtF;AAAA,UAAc;AAAA,UAAiB;AAAA,UAAkB;AAAA,QACjD;AAAA,QACD,SAAS,CAAC,SAAS,QAAQ,SAAS,QAAQ,OAAO,OAAO,MAAM,MAAM;AAAA,QACtE,UAAU,CAAC,mBAAmB,oBAAoB,iBAAiB,kBAAkB,eAAe;AAAA,QACpG,MAAM;AAAA,UAAC;AAAA,UAAO;AAAA,UAAS;AAAA,UAAQ;AAAA,UAAY;AAAA,UAAU;AAAA,UAAS;AAAA,UAAQ;AAAA,UAAU;AAAA,UAAW;AAAA,UAC1F;AAAA,UAAQ;AAAA,UAAQ;AAAA,UAAQ;AAAA,UAAQ;AAAA,QAChC;AAAA,MACD;AAAA,MACD,SAAS;AAAA,MACT,UAAU;AAAA,QAAC;AAAA,UACT,WAAW;AAAA,UACX,OAAO;AAAA,QACZ;AAAA,QAAOH,GAAE;AAAA,QAAqBA,GAAE;AAAA,QAAsBA,GAAE;AAAA,QAAeA,GAAE;AAAA,QAAmBA,GACvF;AAAA,QAAkB;AAAA,UAClB,WAAW;AAAA,UACX,UAAU,CAAC;AAAA,YACV,OAAO;AAAA,YACP,KAAK;AAAA,YACL,SAAS;AAAA,YACT,UAAU,CAACA,GAAE,gBAAgB;AAAA,UACnC,CAAM;AAAA,QACN;AAAA,QAAO;AAAA,UACF,WAAW;AAAA,UACX,OAAO;AAAA,UACP,KAAK;AAAA,UACL,UAAU;AAAA,YACT,SAAS;AAAA,UACT;AAAA,UACD,UAAU,CAAC;AAAA,YACV,OAAO;AAAA,YACP,WAAW;AAAA,UACX,GAAEA,GAAE,QAAQA,GAAE,mBAAmB;AAAA,YACjC,WAAW;AAAA,UACjB,CAAM,GAAG;AAAA,YACH,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,SAAS;AAAA,UACT,GAAEA,GAAE,qBAAqBA,GAAE,oBAAoB;AAAA,QACrD;AAAA,QAAO;AAAA,UACF,WAAW;AAAA,UACX,OAAO,MAAMC,GAAE,QAAQ,KAAK,GAAG,IAAI;AAAA,UACnC,KAAK;AAAA,UACL,YAAY;AAAA,UACZ,UAAUA;AAAA,UACV,UAAU,CAACD,GAAE,qBAAqB;AAAA,QACvC;AAAA,QAAO;AAAA,UACF,OAAO,QAAQA,GAAE;AAAA,UACjB,WAAW;AAAA,QACX;AAAA,MACD;AAAA,IACD;AAAA,EACD;AAAA,EACD,WAAW,CAAAA,OAAK;AACf,UAAMG,KAAIH,GAAE,OACXC,KAAI,wBACJC,KAAI;AAAA,MACH,UAAU;AAAA,MACV,SAAS;AAAA,IACT,GACDE,KAAI;AAAA,MACH,WAAW;AAAA,MACX,OAAO;AAAA,MACP,KAAK;AAAA,MACL,UAAUF;AAAA,IACV,GACDG,KAAI;AAAA,MACH,OAAO;AAAA,MACP,KAAK;AAAA,IACL,GACDE,KAAI;AAAA,MACH,UAAU,CAAC;AAAA,QACV,OAAO;AAAA,MACZ,GAAO;AAAA,QACF,OAAOJ,GAAE,OAAO,kDAAkD,uBAAuB;AAAA,MAC9F,GAAO;AAAA,QACF,OAAO;AAAA,QACP,WAAW;AAAA,MAChB,CAAK;AAAA,IACD,GACDK,KAAI,CAACR,GAAE,kBAAkBI,IAAGG,EAAC,GAC7BE,KAAI,CAAC,KAAK,MAAM,MAAM,MAAM,KAAK,KAAK,GAAG,GACzCM,KAAI,CAACf,IAAGE,IAAGE,KAAI,UAAU;AACxB,YAAMC,KAAI,UAAUD,KAAIA,KAAID,GAAE,OAAOC,IAAGF,EAAC;AACzC,aAAOC,GAAE,OAAOA,GAAE,OAAO,OAAOH,IAAG,GAAG,GAAGE,IAAG,qBAAqBG,IAAG,qBAAqBD,IAAGH,EAAC;AAAA,IAC7F,GACDS,KAAI,CAACV,IAAGE,IAAGE,OAAMD,GAAE,OAAOA,GAAE,OAAO,OAAOH,IAAG,GAAG,GAAGE,IAAG,qBAAqBE,IAAGH,EAAC,GAC/EoB,KAAI,CAACd,IAAGP,GAAE,mBAAmBA,GAAE,QAAQ,QAAQ,QAAQ;AAAA,MACtD,gBAAgB;AAAA,IAChB,CAAA,GAAGK,IAAG;AAAA,MACN,WAAW;AAAA,MACX,UAAUG;AAAA,MACV,UAAU,CAAC;AAAA,QACV,OAAO;AAAA,QACP,KAAK;AAAA,QACL,WAAW;AAAA,MAChB,GAAO;AAAA,QACF,OAAO;AAAA,QACP,KAAK;AAAA,QACL,WAAW;AAAA,MAChB,GAAO;AAAA,QACF,OAAO;AAAA,QACP,KAAK;AAAA,QACL,WAAW;AAAA,MAChB,GAAO;AAAA,QACF,OAAO;AAAA,QACP,KAAK;AAAA,QACL,WAAW;AAAA,MAChB,GAAO;AAAA,QACF,OAAO;AAAA,QACP,KAAK;AAAA,QACL,WAAW;AAAA,MAChB,GAAO;AAAA,QACF,OAAO;AAAA,QACP,KAAK;AAAA,QACL,WAAW;AAAA,MAChB,GAAO;AAAA,QACF,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU,CAACR,GAAE,gBAAgB;AAAA,MAClC,GAAO;AAAA,QACF,OAAO;AAAA,QACP,KAAK;AAAA,MACV,GAAO;AAAA,QACF,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU,CAACA,GAAE,gBAAgB;AAAA,MAClC,GAAO;AAAA,QACF,OAAO;AAAA,QACP,WAAW;AAAA,MAChB,GAAO;AAAA,QACF,OAAO;AAAA,QACP,WAAW;AAAA,MAChB,CAAK;AAAA,IACL,GAAM;AAAA,MACF,WAAW;AAAA,MACX,OAAO;AAAA,MACP,WAAW;AAAA,IACf,GAAM;AAAA,MACF,OAAO,aAAaA,GAAE,iBAAiB;AAAA,MACvC,UAAU;AAAA,MACV,WAAW;AAAA,MACX,UAAU,CAACA,GAAE,mBAAmB;AAAA,QAC/B,WAAW;AAAA,QACX,UAAU,CAAC;AAAA,UACV,OAAOe,GAAE,UAAUZ,GAAE,OAAO,GAAGM,IAAG;AAAA,YACjC,SAAS;AAAA,UAChB,CAAO,CAAC;AAAA,QACR,GAAQ;AAAA,UACF,OAAOM,GAAE,UAAU,OAAO,KAAK;AAAA,QACrC,GAAQ;AAAA,UACF,OAAOA,GAAE,UAAU,OAAO,KAAK;AAAA,QACrC,GAAQ;AAAA,UACF,OAAOA,GAAE,UAAU,OAAO,KAAK;AAAA,QACrC,CAAM;AAAA,QACD,WAAW;AAAA,MAChB,GAAO;AAAA,QACF,WAAW;AAAA,QACX,UAAU,CAAC;AAAA,UACV,OAAO;AAAA,UACP,WAAW;AAAA,QACjB,GAAQ;AAAA,UACF,OAAOL,GAAE,aAAa,MAAM,IAAI;AAAA,QACtC,GAAQ;AAAA,UACF,OAAOA,GAAE,QAAQP,GAAE,OAAO,GAAGM,IAAG;AAAA,YAC/B,SAAS;AAAA,UACT,CAAA,GAAG,IAAI;AAAA,QACd,GAAQ;AAAA,UACF,OAAOC,GAAE,QAAQ,MAAM,IAAI;AAAA,QACjC,GAAQ;AAAA,UACF,OAAOA,GAAE,QAAQ,MAAM,IAAI;AAAA,QACjC,GAAQ;AAAA,UACF,OAAOA,GAAE,QAAQ,MAAM,IAAI;AAAA,QACjC,CAAM;AAAA,MACN,CAAK;AAAA,IACL,GAAM;AAAA,MACF,WAAW;AAAA,MACX,eAAe;AAAA,MACf,KAAK;AAAA,MACL,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,UAAU,CAACV,GAAE,UAAU;AAAA,IAC3B,GAAM;AAAA,MACF,OAAO;AAAA,MACP,WAAW;AAAA,IACf,GAAM;AAAA,MACF,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,UAAU,CAAC;AAAA,QACV,OAAO;AAAA,QACP,KAAK;AAAA,QACL,WAAW;AAAA,MAChB,CAAK;AAAA,IACL,CAAI;AACF,WAAOI,GAAE,WAAWiB,IAAGhB,GAAE,WAAWgB,IAAG;AAAA,MACtC,MAAM;AAAA,MACN,SAAS,CAAC,MAAM,IAAI;AAAA,MACpB,UAAUnB;AAAA,MACV,UAAUmB;AAAA,IACV;AAAA,EACD;AAAA,EACD,UAAU,CAAArB,OAAK;AACd,UAAMG,KAAIH,GAAE,OACXC,KAAI,0BACJC,KAAIC,GAAE,OAAO,4CAA4CF,EAAC,GAC1DG,KAAID,GAAE,OAAO,0EAA0EF,EAAC,GACxFI,KAAI;AAAA,MACH,OAAO;AAAA,MACP,OAAO,SAASH;AAAA,IAChB,GACDK,KAAI;AAAA,MACH,OAAO;AAAA,MACP,UAAU,CAAC;AAAA,QACV,OAAO;AAAA,MACZ,GAAO;AAAA,QACF,OAAO;AAAA,QACP,KAAK;AAAA,MACV,CAAK;AAAA,IACD,GACDC,KAAIR,GAAE,QAAQA,GAAE,kBAAkB;AAAA,MACjC,SAAS;AAAA,IACb,CAAI,GACDS,KAAI,UACJM,KAAI;AAAA,MACH,OAAO;AAAA,MACP,UAAU,CAACf,GAAE,QAAQA,GAAE,mBAAmB;AAAA,QACzC,SAAS;AAAA,QACT,UAAUA,GAAE,kBAAkB,SAAS,OAAOO,EAAC;AAAA,MACpD,CAAK,GAAGC,IAAGR,GAAE,kBAAkB;AAAA,QAC1B,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAUA,GAAE,kBAAkB,SAAS,OAAOO,EAAC;AAAA,MACpD,CAAK,CAAC;AAAA,IACF,GACDG,KAAI;AAAA,MACH,OAAO;AAAA,MACP,UAAU,CAAC;AAAA,QACV,OAAO;AAAA,MACZ,GAAO;AAAA,QACF,OAAO;AAAA,MACZ,GAAO;AAAA,QACF,OAAO;AAAA,MACZ,GAAO;AAAA,QACF,OAAO;AAAA,MACZ,CAAK;AAAA,MACD,WAAW;AAAA,IACX,GACDW,KAAI,CAAC,SAAS,QAAQ,MAAM,GAC5BE,KAAI;AAAA,MAAC;AAAA,MAAa;AAAA,MAAW;AAAA,MAAY;AAAA,MAAgB;AAAA,MAA4B;AAAA,MACpF;AAAA,MAAc;AAAA,MAAiB;AAAA,MAAa;AAAA,MAAO;AAAA,MAAQ;AAAA,MAAQ;AAAA,MAAW;AAAA,MAAgB;AAAA,MAC9F;AAAA,MAAW;AAAA,MAAgB;AAAA,MAAS;AAAA,MAAY;AAAA,MAAO;AAAA,MAAM;AAAA,MAAU;AAAA,MAAQ;AAAA,MAAW;AAAA,MAC1F;AAAA,MAAY;AAAA,MAAQ;AAAA,MAAS;AAAA,MAAS;AAAA,MAAS;AAAA,MAAS;AAAA,MAAY;AAAA,MAAW;AAAA,MAAW;AAAA,MAAM;AAAA,MAChG;AAAA,MAAQ;AAAA,MAAU;AAAA,MAAS;AAAA,MAAc;AAAA,MAAU;AAAA,MAAc;AAAA,MAAS;AAAA,MAAa;AAAA,MAAY;AAAA,MACnG;AAAA,MAAQ;AAAA,MAAW;AAAA,MAAS;AAAA,MAAW;AAAA,MAAS;AAAA,MAAO;AAAA,MAAW;AAAA,MAAQ;AAAA,MAAU;AAAA,MAAQ;AAAA,MAC5F;AAAA,MAAc;AAAA,MAAc;AAAA,MAAa;AAAA,MAAO;AAAA,MAAW;AAAA,MAAa;AAAA,MAAS;AAAA,MAAY;AAAA,MAC7F;AAAA,MAAW;AAAA,MAAS;AAAA,MAAO;AAAA,MAAS;AAAA,MAAU;AAAA,MAAM;AAAA,MAAW;AAAA,MAAa;AAAA,MAAU;AAAA,MAAY;AAAA,MAClG;AAAA,MAAU;AAAA,MAAU;AAAA,MAAU;AAAA,MAAS;AAAA,MAAS;AAAA,MAAO;AAAA,MAAS;AAAA,MAAO;AAAA,MAAO;AAAA,MAAQ;AAAA,MAAS;AAAA,MAC/F;AAAA,IACA,GACDC,KAAI;AAAA,MAAC;AAAA,MAAW;AAAA,MAAkB;AAAA,MAAsB;AAAA,MAAmB;AAAA,MAAiB;AAAA,MAC3F;AAAA,MAAkB;AAAA,MAA4B;AAAA,MAA0B;AAAA,MACxE;AAAA,MAA0B;AAAA,MAAgB;AAAA,MAAa;AAAA,MAAqB;AAAA,MAC5E;AAAA,MAAmB;AAAA,MAAiB;AAAA,MAAkB;AAAA,MAAa;AAAA,MAAsB;AAAA,MACzF;AAAA,MAAgB;AAAA,MAAoB;AAAA,MAA4B;AAAA,MAAoB;AAAA,MACpF;AAAA,MAAiB;AAAA,MAAkB;AAAA,MAAoB;AAAA,MAAoB;AAAA,MAC3E;AAAA,MAAuB;AAAA,MAAiB;AAAA,MAAqB;AAAA,MAAkB;AAAA,MAC/E;AAAA,MAAkB;AAAA,MAA0B;AAAA,MAA4B;AAAA,MACxE;AAAA,MAA8B;AAAA,MAA2B;AAAA,MAAqB;AAAA,MAC9E;AAAA,MAA0B;AAAA,MAAyB;AAAA,MAAiB;AAAA,MACpE;AAAA,MAAoB;AAAA,MAAuB;AAAA,MAAe;AAAA,MAAiB;AAAA,MAAiB;AAAA,MAC5F;AAAA,MAAc;AAAA,MAAc;AAAA,MAAoB;AAAA,MAAe;AAAA,MAAoB;AAAA,MAAY;AAAA,MAC/F;AAAA,MAAc;AAAA,MAAqB;AAAA,MAAa;AAAA,MAAsB;AAAA,MACtE;AAAA,MAAuB;AAAA,MAAe;AAAA,MAAc;AAAA,MAAW;AAAA,MAAS;AAAA,MAAa;AAAA,MACrF;AAAA,MAAqB;AAAA,MAAgB;AAAA,MAAc;AAAA,MAAa;AAAA,MAAe;AAAA,MAC/E;AAAA,MAAiB;AAAA,MAAW;AAAA,MAAa;AAAA,MAA0B;AAAA,MAAU;AAAA,MAAmB;AAAA,MAChG;AAAA,MAAU;AAAA,IACV,GACDC,KAAI;AAAA,MACH,SAASF;AAAA,MACT,UAAU,CAAAvB,OAAK;AACd,cAAMG,KAAI,CAAA;AACV,eAAOH,GAAE,QAAS,CAAAA,OAAK;AACtB,UAAAG,GAAE,KAAKH,EAAC,GAAGA,GAAE,YAAW,MAAOA,KAAIG,GAAE,KAAKH,GAAE,YAAW,CAAE,IAAIG,GAAE,KAAKH,GAAE,aAAa;AAAA,QACnF,CAAA,GAAIG;AAAA,MACL,GAAEkB,EAAC;AAAA,MACJ,UAAUG;AAAA,IACV,GACDE,KAAI,CAAA1B,OAAKA,GAAE,IAAK,CAAAA,OAAKA,GAAE,QAAQ,UAAU,EAAE,CAAG,GAC9CW,KAAI;AAAA,MACH,UAAU,CAAC;AAAA,QACV,OAAO,CAAC,OAAOR,GAAE,OAAOM,IAAG,GAAG,GAAGN,GAAE,OAAO,OAAOuB,GAAEF,EAAC,EAAE,KAAK,MAAM,GAAG,MAAM,GAAGpB,EAAC;AAAA,QAC9E,OAAO;AAAA,UACN,GAAG;AAAA,UACH,GAAG;AAAA,QACH;AAAA,MACN,CAAK;AAAA,IACD,GACDQ,KAAIT,GAAE,OAAOD,IAAG,YAAY,GAC5BY,KAAI;AAAA,MACH,UAAU,CAAC;AAAA,QACV,OAAO,CAACX,GAAE,OAAO,MAAMA,GAAE,UAAU,aAAa,CAAC,GAAGS,EAAC;AAAA,QACrD,OAAO;AAAA,UACN,GAAG;AAAA,QACH;AAAA,MACN,GAAO;AAAA,QACF,OAAO,CAAC,MAAM,OAAO;AAAA,QACrB,OAAO;AAAA,UACN,GAAG;AAAA,QACH;AAAA,MACN,GAAO;AAAA,QACF,OAAO,CAACR,IAAGD,GAAE,OAAO,MAAMA,GAAE,UAAU,aAAa,CAAC,GAAGS,EAAC;AAAA,QACxD,OAAO;AAAA,UACN,GAAG;AAAA,UACH,GAAG;AAAA,QACH;AAAA,MACN,GAAO;AAAA,QACF,OAAO,CAACR,IAAGD,GAAE,OAAO,MAAMA,GAAE,UAAU,aAAa,CAAC,CAAC;AAAA,QACrD,OAAO;AAAA,UACN,GAAG;AAAA,QACH;AAAA,MACN,GAAO;AAAA,QACF,OAAO,CAACC,IAAG,MAAM,OAAO;AAAA,QACxB,OAAO;AAAA,UACN,GAAG;AAAA,UACH,GAAG;AAAA,QACH;AAAA,MACN,CAAK;AAAA,IACD,GACDkB,KAAI;AAAA,MACH,OAAO;AAAA,MACP,OAAOnB,GAAE,OAAOD,IAAGC,GAAE,UAAU,GAAG,GAAGA,GAAE,UAAU,QAAQ,CAAC;AAAA,IAC1D,GACDyB,KAAI;AAAA,MACH,WAAW;AAAA,MACX,OAAO;AAAA,MACP,KAAK;AAAA,MACL,UAAUH;AAAA,MACV,UAAU,CAACH,IAAGjB,IAAGS,IAAGd,GAAE,sBAAsBe,IAAGL,IAAGC,EAAC;AAAA,IACnD,GACDQ,KAAI;AAAA,MACH,WAAW;AAAA,MACX,OAAO;AAAA,QAAC;AAAA,QAAMhB,GAAE,OAAO,yBAAyBuB,GAAEH,EAAC,EAAE,KAAK,MAAM,GAAG,KAAKG,GAAEF,EAAC,EAAE,KAAK,MAAM,GAAG,MAAM;AAAA,QAAGtB;AAAA,QAAGC,GACrG,OAAOM,IAAG,GAAG;AAAA,QAAGN,GAAE,UAAU,QAAQ;AAAA,MACrC;AAAA,MACD,OAAO;AAAA,QACN,GAAG;AAAA,MACH;AAAA,MACD,UAAU,CAACyB,EAAC;AAAA,IAChB;AACE,IAAAA,GAAE,SAAS,KAAKT,EAAC;AACjB,UAAMY,KAAI,CAACT,IAAGR,IAAGd,GAAE,sBAAsBe,IAAGL,IAAGC,EAAC;AAChD,WAAO;AAAA,MACN,kBAAkB;AAAA,MAClB,UAAUc;AAAA,MACV,UAAU,CAAC;AAAA,QACV,OAAOtB,GAAE,OAAO,UAAUC,EAAC;AAAA,QAC3B,YAAY;AAAA,QACZ,KAAK;AAAA,QACL,UAAU;AAAA,QACV,UAAU;AAAA,UACT,SAASiB;AAAA,UACT,SAAS,CAAC,OAAO,OAAO;AAAA,QACxB;AAAA,QACD,UAAU,CAAC;AAAA,UACV,OAAO;AAAA,UACP,KAAK;AAAA,UACL,UAAU;AAAA,YACT,SAASA;AAAA,YACT,SAAS,CAAC,OAAO,OAAO;AAAA,UACxB;AAAA,UACD,UAAU,CAAC,QAAQ,GAAGU,EAAC;AAAA,QACvB,GAAE,GAAGA,IAAG;AAAA,UACR,OAAO;AAAA,UACP,OAAO3B;AAAA,QACZ,CAAK;AAAA,MACD,GAAEJ,GAAE,mBAAmBA,GAAE,QAAQ,MAAM,GAAG,GAAGA,GAAE,QAAQ,QAAQ,QAAQ;AAAA,QACvE,UAAU,CAAC;AAAA,UACV,OAAO;AAAA,UACP,OAAO;AAAA,QACZ,CAAK;AAAA,MACL,CAAI,GAAG;AAAA,QACH,OAAO;AAAA,QACP,UAAU;AAAA,QACV,QAAQ;AAAA,UACP,OAAO;AAAA,UACP,KAAKA,GAAE;AAAA,UACP,UAAU,CAAC;AAAA,YACV,OAAO;AAAA,YACP,OAAO;AAAA,YACP,YAAY;AAAA,UAClB,CAAM;AAAA,QACD;AAAA,MACL,GAAM;AAAA,QACF,OAAO;AAAA,QACP,UAAU,CAAC;AAAA,UACV,OAAO;AAAA,UACP,WAAW;AAAA,QAChB,GAAO;AAAA,UACF,OAAO;AAAA,QACZ,GAAO;AAAA,UACF,OAAO;AAAA,UACP,WAAW;AAAA,QAChB,GAAO;AAAA,UACF,OAAO;AAAA,QACZ,CAAK;AAAA,MACL,GAAM;AAAA,QACF,OAAO;AAAA,QACP,OAAO;AAAA,MACX,GAAMK,IAAGc,IAAGL,IAAG;AAAA,QACX,OAAO,CAAC,SAAS,MAAMZ,EAAC;AAAA,QACxB,OAAO;AAAA,UACN,GAAG;AAAA,UACH,GAAG;AAAA,QACH;AAAA,MACD,GAAES,IAAG;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,QACX,eAAe;AAAA,QACf,KAAK;AAAA,QACL,YAAY;AAAA,QACZ,SAAS;AAAA,QACT,UAAU,CAAC;AAAA,UACV,eAAe;AAAA,QACpB,GAAOX,GAAE,uBAAuB;AAAA,UAC3B,OAAO;AAAA,UACP,YAAY;AAAA,QACjB,GAAO;AAAA,UACF,OAAO;AAAA,UACP,OAAO;AAAA,UACP,KAAK;AAAA,UACL,cAAc;AAAA,UACd,YAAY;AAAA,UACZ,UAAUyB;AAAA,UACV,UAAU,CAAC,QAAQpB,IAAGS,IAAGd,GAAE,sBAAsBe,IAAGL,EAAC;AAAA,QAC1D,CAAK;AAAA,MACL,GAAM;AAAA,QACF,OAAO;AAAA,QACP,UAAU,CAAC;AAAA,UACV,eAAe;AAAA,UACf,SAAS;AAAA,QACd,GAAO;AAAA,UACF,eAAe;AAAA,UACf,SAAS;AAAA,QACd,CAAK;AAAA,QACD,WAAW;AAAA,QACX,KAAK;AAAA,QACL,YAAY;AAAA,QACZ,UAAU,CAAC;AAAA,UACV,eAAe;AAAA,QACpB,GAAOV,GAAE,qBAAqB;AAAA,MAC9B,GAAM;AAAA,QACF,eAAe;AAAA,QACf,WAAW;AAAA,QACX,KAAK;AAAA,QACL,SAAS;AAAA,QACT,UAAU,CAACA,GAAE,QAAQA,GAAE,uBAAuB;AAAA,UAC7C,OAAO;AAAA,QACZ,CAAK,CAAC;AAAA,MACN,GAAM;AAAA,QACF,eAAe;AAAA,QACf,WAAW;AAAA,QACX,KAAK;AAAA,QACL,UAAU,CAAC;AAAA,UACV,OAAO;AAAA,UACP,OAAO;AAAA,QACZ,GAAOA,GAAE,qBAAqB;AAAA,MAC9B,GAAMe,IAAGL,EAAC;AAAA,IACP;AAAA,EACD;AAAA,EACD,mBAAmB,CAAAV,QAAM;AAAA,IACxB,MAAM;AAAA,IACN,aAAa;AAAA,IACb,UAAU,CAAC;AAAA,MACV,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,UAAU,CAAC;AAAA,QACV,OAAO;AAAA,QACP,KAAK;AAAA,QACL,MAAM;AAAA,MACV,GAAM;AAAA,QACF,OAAO;AAAA,QACP,KAAK;AAAA,QACL,MAAM;AAAA,MACV,GAAM;AAAA,QACF,OAAO;AAAA,QACP,KAAK;AAAA,QACL,MAAM;AAAA,MACN,GAAEA,GAAE,QAAQA,GAAE,kBAAkB;AAAA,QAChC,SAAS;AAAA,QACT,WAAW;AAAA,QACX,UAAU;AAAA,QACV,MAAM;AAAA,MACN,CAAA,GAAGA,GAAE,QAAQA,GAAE,mBAAmB;AAAA,QAClC,SAAS;AAAA,QACT,WAAW;AAAA,QACX,UAAU;AAAA,QACV,MAAM;AAAA,MACV,CAAI,CAAC;AAAA,IACL,CAAG;AAAA,EACH;AAAA,EACC,gBAAgB,CAAAA,QAAM;AAAA,IACrB,MAAM;AAAA,IACN,SAAS,CAAC,QAAQ,KAAK;AAAA,IACvB,mBAAmB;AAAA,EACrB;AAAA,EACC,aAAa,CAAAA,OAAK;AACjB,UAAMG,KAAIH,GAAE,OACXC,KAAI,q0iBACJC,KAAI;AAAA,MAAC;AAAA,MAAO;AAAA,MAAM;AAAA,MAAU;AAAA,MAAS;AAAA,MAAS;AAAA,MAAS;AAAA,MAAQ;AAAA,MAAS;AAAA,MAAY;AAAA,MAAO;AAAA,MAAO;AAAA,MACjG;AAAA,MAAQ;AAAA,MAAU;AAAA,MAAW;AAAA,MAAO;AAAA,MAAQ;AAAA,MAAU;AAAA,MAAM;AAAA,MAAU;AAAA,MAAM;AAAA,MAAM;AAAA,MAAU;AAAA,MAC5F;AAAA,MAAe;AAAA,MAAO;AAAA,MAAM;AAAA,MAAQ;AAAA,MAAS;AAAA,MAAU;AAAA,MAAO;AAAA,MAAS;AAAA,MAAQ;AAAA,IAC/E,GACDE,KAAI;AAAA,MACH,UAAU;AAAA,MACV,SAASF;AAAA,MACT,UAAU;AAAA,QAAC;AAAA,QAAc;AAAA,QAAO;AAAA,QAAO;AAAA,QAAO;AAAA,QAAS;AAAA,QAAO;AAAA,QAAQ;AAAA,QAAc;AAAA,QAAa;AAAA,QAChG;AAAA,QAAY;AAAA,QAAO;AAAA,QAAe;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAQ;AAAA,QAAO;AAAA,QAClF;AAAA,QAAa;AAAA,QAAQ;AAAA,QAAQ;AAAA,QAAU;AAAA,QAAS;AAAA,QAAU;AAAA,QAAa;AAAA,QAAW;AAAA,QAClF;AAAA,QAAW;AAAA,QAAQ;AAAA,QAAQ;AAAA,QAAO;AAAA,QAAM;AAAA,QAAS;AAAA,QAAO;AAAA,QAAc;AAAA,QAAc;AAAA,QAAQ;AAAA,QAC5F;AAAA,QAAQ;AAAA,QAAU;AAAA,QAAO;AAAA,QAAO;AAAA,QAAc;AAAA,QAAO;AAAA,QAAQ;AAAA,QAAU;AAAA,QAAO;AAAA,QAAQ;AAAA,QAAO;AAAA,QAC7F;AAAA,QAAS;AAAA,QAAY;AAAA,QAAS;AAAA,QAAQ;AAAA,QAAY;AAAA,QAAS;AAAA,QAAO;AAAA,QAAW;AAAA,QAAS;AAAA,QACtF;AAAA,QAAgB;AAAA,QAAO;AAAA,QAAO;AAAA,QAAS;AAAA,QAAS;AAAA,QAAQ;AAAA,QAAQ;AAAA,MAChE;AAAA,MACD,SAAS,CAAC,aAAa,YAAY,SAAS,QAAQ,kBAAkB,MAAM;AAAA,MAC5E,MAAM;AAAA,QAAC;AAAA,QAAO;AAAA,QAAY;AAAA,QAAa;AAAA,QAAQ;AAAA,QAAQ;AAAA,QAAW;AAAA,QAAW;AAAA,QAAY;AAAA,QACxF;AAAA,QAAO;AAAA,QAAS;AAAA,QAAQ;AAAA,MACxB;AAAA,IACD,GACDG,KAAI;AAAA,MACH,WAAW;AAAA,MACX,OAAO;AAAA,IACP,GACDE,KAAI;AAAA,MACH,WAAW;AAAA,MACX,OAAO;AAAA,MACP,KAAK;AAAA,MACL,UAAUH;AAAA,MACV,SAAS;AAAA,IACT,GACDI,KAAI;AAAA,MACH,OAAO;AAAA,MACP,WAAW;AAAA,IACX,GACDC,KAAI;AAAA,MACH,WAAW;AAAA,MACX,UAAU,CAACT,GAAE,gBAAgB;AAAA,MAC7B,UAAU,CAAC;AAAA,QACV,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU,CAACA,GAAE,kBAAkBK,EAAC;AAAA,QAChC,WAAW;AAAA,MAChB,GAAO;AAAA,QACF,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU,CAACL,GAAE,kBAAkBK,EAAC;AAAA,QAChC,WAAW;AAAA,MAChB,GAAO;AAAA,QACF,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU,CAACL,GAAE,kBAAkBK,IAAGG,IAAGD,EAAC;AAAA,MAC3C,GAAO;AAAA,QACF,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU,CAACP,GAAE,kBAAkBK,IAAGG,IAAGD,EAAC;AAAA,MAC3C,GAAO;AAAA,QACF,OAAO;AAAA,QACP,KAAK;AAAA,QACL,WAAW;AAAA,MAChB,GAAO;AAAA,QACF,OAAO;AAAA,QACP,KAAK;AAAA,QACL,WAAW;AAAA,MAChB,GAAO;AAAA,QACF,OAAO;AAAA,QACP,KAAK;AAAA,MACV,GAAO;AAAA,QACF,OAAO;AAAA,QACP,KAAK;AAAA,MACV,GAAO;AAAA,QACF,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU,CAACP,GAAE,kBAAkBQ,IAAGD,EAAC;AAAA,MACxC,GAAO;AAAA,QACF,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU,CAACP,GAAE,kBAAkBQ,IAAGD,EAAC;AAAA,MACnC,GAAEP,GAAE,kBAAkBA,GAAE,iBAAiB;AAAA,IAC1C,GACDe,KAAI,mBACJL,KAAI,QAAQK,EAAC,UAAUA,EAAC,SAASA,EAAC,QAClCM,KAAI,SAASnB,GAAE,KAAK,GAAG,GACvBqB,KAAI;AAAA,MACH,WAAW;AAAA,MACX,WAAW;AAAA,MACX,UAAU,CAAC;AAAA,QACV,OAAO,QAAQR,EAAC,MAAML,EAAC,eAAeK,EAAC,YAAYM,EAAC;AAAA,MACzD,GAAO;AAAA,QACF,OAAO,IAAIX,EAAC;AAAA,MACjB,GAAO;AAAA,QACF,OAAO,0CAA0CW,EAAC;AAAA,MACvD,GAAO;AAAA,QACF,OAAO,4BAA4BA,EAAC;AAAA,MACzC,GAAO;AAAA,QACF,OAAO,6BAA6BA,EAAC;AAAA,MAC1C,GAAO;AAAA,QACF,OAAO,mCAAmCA,EAAC;AAAA,MAChD,GAAO;AAAA,QACF,OAAO,OAAON,EAAC,WAAWM,EAAC;AAAA,MAChC,CAAK;AAAA,IACD,GACDG,KAAI;AAAA,MACH,WAAW;AAAA,MACX,OAAOrB,GAAE,UAAU,SAAS;AAAA,MAC5B,KAAK;AAAA,MACL,UAAUC;AAAA,MACV,UAAU,CAAC;AAAA,QACV,OAAO;AAAA,MACZ,GAAO;AAAA,QACF,OAAO;AAAA,QACP,KAAK;AAAA,QACL,gBAAgB;AAAA,MACrB,CAAK;AAAA,IACD,GACDqB,KAAI;AAAA,MACH,WAAW;AAAA,MACX,UAAU,CAAC;AAAA,QACV,WAAW;AAAA,QACX,OAAO;AAAA,QACP,MAAM;AAAA,MACX,GAAO;AAAA,QACF,OAAO;AAAA,QACP,KAAK;AAAA,QACL,cAAc;AAAA,QACd,YAAY;AAAA,QACZ,UAAUrB;AAAA,QACV,UAAU,CAAC,QAAQC,IAAGkB,IAAGd,IAAGT,GAAE,iBAAiB;AAAA,MACpD,CAAK;AAAA,IACL;AACE,WAAOO,GAAE,WAAW,CAACE,IAAGc,IAAGlB,EAAC,GAAG;AAAA,MAC9B,MAAM;AAAA,MACN,SAAS,CAAC,MAAM,OAAO,SAAS;AAAA,MAChC,cAAc;AAAA,MACd,UAAUD;AAAA,MACV,SAAS;AAAA,MACT,UAAU,CAACC,IAAGkB,IAAG;AAAA,QAChB,OAAO;AAAA,MACX,GAAM;AAAA,QACF,eAAe;AAAA,QACf,WAAW;AAAA,MACX,GAAEd,IAAGe,IAAGxB,GAAE,mBAAmB;AAAA,QAC7B,OAAO,CAAC,SAAS,OAAOC,EAAC;AAAA,QACzB,OAAO;AAAA,UACN,GAAG;AAAA,UACH,GAAG;AAAA,QACH;AAAA,QACD,UAAU,CAACwB,EAAC;AAAA,MAChB,GAAM;AAAA,QACF,UAAU,CAAC;AAAA,UACV,OAAO,CAAC,WAAW,OAAOxB,IAAG,OAAO,SAASA,IAAG,OAAO;AAAA,QAC5D,GAAO;AAAA,UACF,OAAO,CAAC,WAAW,OAAOA,EAAC;AAAA,QAChC,CAAK;AAAA,QACD,OAAO;AAAA,UACN,GAAG;AAAA,UACH,GAAG;AAAA,UACH,GAAG;AAAA,QACH;AAAA,MACL,GAAM;AAAA,QACF,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU,CAACsB,IAAGE,IAAGhB,EAAC;AAAA,MACtB,CAAI;AAAA,IACD;AAAA,EACD;AAAA,EACD,kBAAkB,CAAAT,QAAM;AAAA,IACvB,SAAS,CAAC,OAAO;AAAA,IACjB,UAAU,CAAC;AAAA,MACV,WAAW;AAAA,MACX,QAAQ;AAAA,QACP,KAAK;AAAA,QACL,QAAQ;AAAA,UACP,KAAK;AAAA,UACL,aAAa;AAAA,QACb;AAAA,MACD;AAAA,MACD,UAAU,CAAC;AAAA,QACV,OAAO;AAAA,MACX,GAAM;AAAA,QACF,OAAO;AAAA,MACX,CAAI;AAAA,IACJ,CAAG;AAAA,EACH;AAAA,EACC,QAAQ,CAAAA,OAAK;AACZ,UAAMG,KAAIH,GAAE,OACXC,KAAI,wDACJC,KAAIC,GAAE;AAAA,MAAO;AAAA,MAAiD;AAAA,MAC7D;AAAA,IAA+C,GAChDC,KAAI,oEACJC,KAAIF,GAAE,OAAO,QAAQ,QAAQ,QAAQ,SAAS,MAAM,GAAG;AACxD,WAAO;AAAA,MACN,MAAM;AAAA,MACN,UAAU;AAAA,QACT,UAAUF;AAAA,QACV,SAAS;AAAA,QACT,SAAS;AAAA,QACT,UAAU;AAAA,MACV;AAAA,MACD,UAAU,CAACD,GAAE,QAAQ,MAAM,KAAK;AAAA,QAC/B,UAAU,CAAC;AAAA,UACV,OAAO;AAAA,UACP,OAAO;AAAA,UACP,QAAQ;AAAA,YACP,KAAKG,GAAE,UAAUA,GAAE,OAAO,0BAA0B,WAAW,CAAC;AAAA,YAChE,YAAY;AAAA,UACZ;AAAA,QACN,GAAO;AAAA,UACF,OAAO;AAAA,UACP,OAAO;AAAA,UACP,KAAK;AAAA,UACL,UAAU,CAAC;AAAA,YACV,OAAO;AAAA,YACP,UAAU,CAAC;AAAA,cACV,OAAOF;AAAA,YACd,GAAS;AAAA,cACF,OAAO;AAAA,YACd,CAAO;AAAA,YACD,YAAY;AAAA,UAClB,CAAM;AAAA,QACN,GAAO;AAAA,UACF,OAAO;AAAA,UACP,OAAO;AAAA,QACZ,GAAO;AAAA,UACF,OAAO;AAAA,UACP,OAAO;AAAA,QACZ,CAAK;AAAA,MACL,CAAI,GAAGD,GAAE,mBAAmB;AAAA,QACxB,OAAO;AAAA,QACP,UAAU,CAACA,GAAE,gBAAgB;AAAA,QAC7B,UAAU,CAACA,GAAE,kBAAkB;AAAA,UAC9B,OAAO;AAAA,UACP,KAAK;AAAA,QACV,CAAK,GAAGA,GAAE,kBAAkB;AAAA,UACvB,OAAO;AAAA,UACP,KAAK;AAAA,QACV,CAAK,GAAGA,GAAE,kBAAkB;AAAA,UACvB,OAAO;AAAA,UACP,KAAK;AAAA,QACV,CAAK,GAAGA,GAAE,kBAAkB;AAAA,UACvB,OAAO;AAAA,UACP,KAAK;AAAA,QACV,CAAK,GAAGA,GAAE,kBAAkB;AAAA,UACvB,OAAO;AAAA,UACP,KAAK;AAAA,QACV,CAAK,GAAGA,GAAE,kBAAkB;AAAA,UACvB,OAAO;AAAA,UACP,KAAK;AAAA,QACV,CAAK,GAAG;AAAA,UACH,OAAO;AAAA,UACP,KAAK;AAAA,UACL,WAAW;AAAA,QAChB,GAAO;AAAA,UACF,OAAO;AAAA,UACP,KAAK;AAAA,UACL,WAAW;AAAA,QAChB,CAAK;AAAA,MACL,GAAM;AAAA,QACF,WAAW;AAAA,QACX,UAAU,CAAC;AAAA,UACV,OAAO;AAAA,YACN,GAAG;AAAA,YACH,GAAG;AAAA,UACH;AAAA,UACD,OAAO,CAACI,IAAGF,EAAC;AAAA,QACjB,GAAO;AAAA,UACF,OAAO;AAAA,YACN,GAAG;AAAA,YACH,GAAG;AAAA,UACH;AAAA,UACD,OAAO,CAAC,WAAWA,EAAC;AAAA,QACzB,GAAO;AAAA,UACF,OAAO;AAAA,YACN,GAAG;AAAA,YACH,GAAG;AAAA,UACH;AAAA,UACD,OAAO,CAACG,IAAGH,EAAC;AAAA,QACjB,GAAO;AAAA,UACF,OAAO;AAAA,YACN,GAAG;AAAA,UACH;AAAA,UACD,OAAO,CAAC,oBAAoBA,EAAC;AAAA,QAClC,CAAK;AAAA,MACL,GAAM;AAAA,QACF,OAAO;AAAA,UACN,GAAG;AAAA,QACH;AAAA,QACD,OAAO,CAACD,IAAG,OAAO,MAAM,KAAK;AAAA,MACjC,GAAM;AAAA,QACF,OAAO;AAAA,QACP,WAAW;AAAA,QACX,UAAU,CAAC;AAAA,UACV,OAAOG;AAAA,QACZ,GAAO;AAAA,UACF,OAAO;AAAA,QACZ,CAAK;AAAA,MACL,GAAM;AAAA,QACF,OAAO;AAAA,QACP,WAAW;AAAA,QACX,OAAOC;AAAA,MACX,GAAM;AAAA,QACF,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU,CAAC;AAAA,UACV,OAAO;AAAA,QACZ,CAAK;AAAA,MACL,CAAI;AAAA,IACD;AAAA,EACD;AAAA,EACD,WAAW,CAAAL,OAAK;AACf,UAAMG,KAAIH,GAAE,OACXC,KAAI,sFACJC,KAAIC,GAAE,OAAO,wBAAwB,4BAA4B,GACjEC,KAAID,GAAE,OAAOD,IAAG,UAAU,GAC1BG,KAAI;AAAA,MACH,qBAAqB,CAAC,YAAY,YAAY,cAAc;AAAA,MAC5D,qBAAqB,CAAC,QAAQ,OAAO;AAAA,MACrC,SAAS;AAAA,QAAC;AAAA,QAAS;AAAA,QAAO;AAAA,QAAS;AAAA,QAAS;AAAA,QAAS;AAAA,QAAQ;AAAA,QAAS;AAAA,QAAW;AAAA,QAAM;AAAA,QAAQ;AAAA,QAC9F;AAAA,QAAO;AAAA,QAAO;AAAA,QAAU;AAAA,QAAO;AAAA,QAAM;AAAA,QAAM;AAAA,QAAU;AAAA,QAAQ;AAAA,QAAO;AAAA,QAAM;AAAA,QAAQ;AAAA,QAAW;AAAA,QAC7F;AAAA,QAAS;AAAA,QAAU;AAAA,QAAQ;AAAA,QAAS;AAAA,QAAU;AAAA,QAAS;AAAA,QAAQ;AAAA,QAAS;AAAA,QAAS;AAAA,QAAW;AAAA,QAC5F;AAAA,QAAW;AAAA,QAAU;AAAA,QAAW;AAAA,QAAa;AAAA,QAAS;AAAA,MACtD;AAAA,MACD,UAAU;AAAA,QAAC;AAAA,QAAQ;AAAA,QAAU;AAAA,QAAiB;AAAA,QAAe;AAAA,QAAe;AAAA,QAC3E;AAAA,QAAoB;AAAA,MACpB;AAAA,MACD,SAAS,CAAC,QAAQ,SAAS,KAAK;AAAA,IAChC,GACDE,KAAI;AAAA,MACH,WAAW;AAAA,MACX,OAAO;AAAA,IACP,GACDC,KAAI;AAAA,MACH,OAAO;AAAA,MACP,KAAK;AAAA,IACL,GACDC,KAAI,CAACT,GAAE,QAAQ,KAAK,KAAK;AAAA,MACxB,UAAU,CAACO,EAAC;AAAA,IACZ,CAAA,GAAGP,GAAE,QAAQ,WAAW,SAAS;AAAA,MACjC,UAAU,CAACO,EAAC;AAAA,MACZ,WAAW;AAAA,IACf,CAAI,GAAGP,GAAE,QAAQ,YAAYA,GAAE,gBAAgB,CAAC,GAC7Ce,KAAI;AAAA,MACH,WAAW;AAAA,MACX,OAAO;AAAA,MACP,KAAK;AAAA,MACL,UAAUV;AAAA,IACV,GACDK,KAAI;AAAA,MACH,WAAW;AAAA,MACX,UAAU,CAACV,GAAE,kBAAkBe,EAAC;AAAA,MAChC,UAAU,CAAC;AAAA,QACV,OAAO;AAAA,QACP,KAAK;AAAA,MACV,GAAO;AAAA,QACF,OAAO;AAAA,QACP,KAAK;AAAA,MACV,GAAO;AAAA,QACF,OAAO;AAAA,QACP,KAAK;AAAA,MACV,GAAO;AAAA,QACF,OAAO;AAAA,QACP,KAAK;AAAA,MACV,GAAO;AAAA,QACF,OAAO;AAAA,QACP,KAAK;AAAA,MACV,GAAO;AAAA,QACF,OAAO;AAAA,QACP,KAAK;AAAA,MACV,GAAO;AAAA,QACF,OAAO;AAAA,QACP,KAAK;AAAA,MACV,GAAO;AAAA,QACF,OAAO;AAAA,QACP,KAAK;AAAA,MACV,GAAO;AAAA,QACF,OAAO;AAAA,QACP,KAAK;AAAA,MACV,GAAO;AAAA,QACF,OAAO;AAAA,QACP,KAAK;AAAA,MACV,GAAO;AAAA,QACF,OAAO;AAAA,QACP,KAAK;AAAA,MACV,GAAO;AAAA,QACF,OAAO;AAAA,MACZ,GAAO;AAAA,QACF,OAAO;AAAA,MACZ,GAAO;AAAA,QACF,OAAO;AAAA,MACZ,GAAO;AAAA,QACF,OAAO;AAAA,MACZ,GAAO;AAAA,QACF,OAAO;AAAA,MACZ,GAAO;AAAA,QACF,OAAO;AAAA,MACZ,GAAO;AAAA,QACF,OAAOZ,GAAE,OAAO,aAAaA,GAAE,UAAU,0CAA0C,CAAC;AAAA,QACpF,UAAU,CAACH,GAAE,kBAAkB;AAAA,UAC9B,OAAO;AAAA,UACP,KAAK;AAAA,UACL,UAAU,CAACA,GAAE,kBAAkBe,EAAC;AAAA,QACtC,CAAM,CAAC;AAAA,MACP,CAAK;AAAA,IACD,GACDM,KAAI,mBACJE,KAAI;AAAA,MACH,WAAW;AAAA,MACX,WAAW;AAAA,MACX,UAAU,CAAC;AAAA,QACV,OAAO,8BAA8BF,EAAC,iBAAiBA,EAAC;AAAA,MAC7D,GAAO;AAAA,QACF,OAAO;AAAA,MACZ,GAAO;AAAA,QACF,OAAO;AAAA,MACZ,GAAO;AAAA,QACF,OAAO;AAAA,MACZ,GAAO;AAAA,QACF,OAAO;AAAA,MACZ,GAAO;AAAA,QACF,OAAO;AAAA,MACZ,CAAK;AAAA,IACD,GACDG,KAAI;AAAA,MACH,UAAU,CAAC;AAAA,QACV,OAAO;AAAA,MACZ,GAAO;AAAA,QACF,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,cAAc;AAAA,QACd,YAAY;AAAA,QACZ,UAAUnB;AAAA,MACf,CAAK;AAAA,IACD,GACDoB,KAAI,CAACf,IAAG;AAAA,MACP,UAAU,CAAC;AAAA,QACV,OAAO,CAAC,YAAYN,IAAG,WAAWA,EAAC;AAAA,MACxC,GAAO;AAAA,QACF,OAAO,CAAC,uBAAuBA,EAAC;AAAA,MACrC,CAAK;AAAA,MACD,OAAO;AAAA,QACN,GAAG;AAAA,QACH,GAAG;AAAA,MACH;AAAA,MACD,UAAUC;AAAA,IACd,GAAM;AAAA,MACF,OAAO,CAAC,uBAAuBD,EAAC;AAAA,MAChC,OAAO;AAAA,QACN,GAAG;AAAA,MACH;AAAA,MACD,UAAUC;AAAA,IACd,GAAM;AAAA,MACF,WAAW;AAAA,MACX,OAAO,CAACD,IAAG,YAAY;AAAA,MACvB,OAAO;AAAA,QACN,GAAG;AAAA,MACH;AAAA,IACL,GAAM;AAAA,MACF,WAAW;AAAA,MACX,OAAO;AAAA,MACP,WAAW;AAAA,IACf,GAAM;AAAA,MACF,WAAW;AAAA,MACX,OAAOF;AAAA,MACP,OAAO;AAAA,IACX,GAAM;AAAA,MACF,OAAO,CAAC,OAAO,OAAOD,EAAC;AAAA,MACvB,OAAO;AAAA,QACN,GAAG;AAAA,QACH,GAAG;AAAA,MACH;AAAA,MACD,UAAU,CAACuB,EAAC;AAAA,IAChB,GAAM;AAAA,MACF,OAAOxB,GAAE,WAAW;AAAA,IACxB,GAAM;AAAA,MACF,WAAW;AAAA,MACX,OAAOA,GAAE,sBAAsB;AAAA,MAC/B,WAAW;AAAA,IACf,GAAM;AAAA,MACF,WAAW;AAAA,MACX,OAAO;AAAA,MACP,UAAU,CAACU,IAAG;AAAA,QACb,OAAOT;AAAA,MACZ,CAAK;AAAA,MACD,WAAW;AAAA,IACX,GAAEsB,IAAG;AAAA,MACL,WAAW;AAAA,MACX,OAAO;AAAA,IACX,GAAM;AAAA,MACF,WAAW;AAAA,MACX,OAAO;AAAA,MACP,KAAK;AAAA,MACL,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,UAAUlB;AAAA,IACd,GAAM;AAAA,MACF,OAAO,MAAML,GAAE,iBAAiB;AAAA,MAChC,UAAU;AAAA,MACV,UAAU,CAAC;AAAA,QACV,WAAW;AAAA,QACX,UAAU,CAACA,GAAE,kBAAkBe,EAAC;AAAA,QAChC,SAAS;AAAA,QACT,UAAU,CAAC;AAAA,UACV,OAAO;AAAA,UACP,KAAK;AAAA,QACX,GAAQ;AAAA,UACF,OAAO;AAAA,UACP,KAAK;AAAA,QACX,GAAQ;AAAA,UACF,OAAO;AAAA,UACP,KAAK;AAAA,QACX,GAAQ;AAAA,UACF,OAAO;AAAA,UACP,KAAK;AAAA,QACX,GAAQ;AAAA,UACF,OAAO;AAAA,UACP,KAAK;AAAA,QACX,CAAM;AAAA,MACN,CAAK,EAAE,OAAOP,IAAGC,EAAC;AAAA,MACd,WAAW;AAAA,IACX,CAAA,EAAE,OAAOD,IAAGC,EAAC;AACf,IAAAM,GAAE,WAAWU,IAAGD,GAAE,WAAWC;AAC7B,UAAMC,KAAI,CAAC;AAAA,MACV,OAAO;AAAA,MACP,QAAQ;AAAA,QACP,KAAK;AAAA,QACL,UAAUD;AAAA,MACV;AAAA,IACJ,GAAK;AAAA,MACF,WAAW;AAAA,MACX,OAAO;AAAA,MACP,QAAQ;AAAA,QACP,KAAK;AAAA,QACL,UAAUpB;AAAA,QACV,UAAUoB;AAAA,MACV;AAAA,IACJ,CAAG;AACD,WAAOhB,GAAE,QAAQD,EAAC,GAAG;AAAA,MACpB,MAAM;AAAA,MACN,SAAS,CAAC,MAAM,WAAW,WAAW,QAAQ,KAAK;AAAA,MACnD,UAAUH;AAAA,MACV,SAAS;AAAA,MACT,UAAU,CAACL,GAAE,QAAQ;AAAA,QACpB,QAAQ;AAAA,MACZ,CAAI,CAAC,EAAE,OAAO0B,EAAC,EAAE,OAAOjB,EAAC,EAAE,OAAOgB,EAAC;AAAA,IAChC;AAAA,EACD;AAAA,EACD,WAAW,CAAAzB,OAAK;AACf,UAAMG,KAAIH,GAAE,OACXC,KAAI;AAAA,MACH,WAAW;AAAA,MACX,WAAW;AAAA,MACX,OAAOE,GAAE,OAAO,MAAM,aAAaH,GAAE,UAAUG,GAAE,UAAU,OAAO,CAAC;AAAA,IACnE,GACDD,KAAI,yCACJE,KAAI;AAAA,MAAC;AAAA,MAAS;AAAA,MAAQ;AAAA,MAAQ;AAAA,MAAS;AAAA,MAAQ;AAAA,MAAQ;AAAA,MAAM;AAAA,MAAS;AAAA,MAAU;AAAA,MAAW;AAAA,MAAS;AAAA,MACnG;AAAA,MAAa;AAAA,MAAc;AAAA,MAAM;AAAA,MAAO;AAAA,MAAS;AAAA,MAAS;AAAA,MAAQ;AAAA,MAAQ;AAAA,MAAW;AAAA,MAAY;AAAA,MACjG;AAAA,MAAgB;AAAA,MAAuB;AAAA,MAAqB;AAAA,MAAkB;AAAA,MAAY;AAAA,MAC1F;AAAA,MAAc;AAAA,MAAa;AAAA,MAAU;AAAA,MAAQ;AAAA,MAAQ;AAAA,MAAW;AAAA,MAAkB;AAAA,MAClF;AAAA,MAAoB;AAAA,MAAQ;AAAA,MAAU;AAAA,MAAS;AAAA,MAAW;AAAA,MAAgB;AAAA,MAC1E;AAAA,MAAgB;AAAA,MAAS;AAAA,MAAmB;AAAA,MAAgB;AAAA,MAAe;AAAA,MAAU;AAAA,MACrF;AAAA,MAAW;AAAA,MAAc;AAAA,MAAQ;AAAA,MAAkB;AAAA,MAAgB;AAAA,MAAQ;AAAA,MAAU;AAAA,MACrF;AAAA,MAAgB;AAAA,MAAc;AAAA,IAC9B,GACDC,KAAI;AAAA,MAAC;AAAA,MAAM;AAAA,MAAO;AAAA,MAAO;AAAA,MAAO;AAAA,MAAQ;AAAA,MAAS;AAAA,MAAM;AAAA,MAAO;AAAA,MAAO;AAAA,MAAO;AAAA,MAAQ;AAAA,MAAS;AAAA,MAAO;AAAA,MACnG;AAAA,MAAO;AAAA,MAAQ;AAAA,MAAQ;AAAA,MAAO;AAAA,MAAU;AAAA,MAAU;AAAA,MAAU;AAAA,IAChE;AACE,WAAO;AAAA,MACN,MAAM;AAAA,MACN,SAAS,CAAC,IAAI;AAAA,MACd,UAAU;AAAA,QACT,UAAUL,GAAE,WAAW;AAAA,QACvB,MAAMK;AAAA,QACN,SAAS;AAAA,UAAC;AAAA,UAAY;AAAA,UAAM;AAAA,UAAS;AAAA,UAAS;AAAA,UAAU;AAAA,UAAO;AAAA,UAAS;AAAA,UAAS;AAAA,UAAY;AAAA,UAC5F;AAAA,UAAM;AAAA,UAAO;AAAA,UAAQ;AAAA,UAAQ;AAAA,UAAU;AAAA,UAAS;AAAA,UAAS;AAAA,UAAM;AAAA,UAAO;AAAA,UAAM;AAAA,UAAQ;AAAA,UAAM;AAAA,UAC1F;AAAA,UAAQ;AAAA,UAAS;AAAA,UAAS;AAAA,UAAO;AAAA,UAAQ;AAAA,UAAO;AAAA,UAAY;AAAA,UAAQ;AAAA,UAAO;AAAA,UAAO;AAAA,UAAU;AAAA,UAC5F;AAAA,UAAQ;AAAA,UAAU;AAAA,UAAU;AAAA,UAAS;AAAA,UAAS;AAAA,UAAQ;AAAA,UAAO;AAAA,UAAQ;AAAA,UAAU;AAAA,UAAU;AAAA,UACzF;AAAA,UAAO;AAAA,UAAW;AAAA,UAAS;AAAA,UAAS;AAAA,QACpC;AAAA,QACD,SAAS,CAAC,QAAQ,SAAS,QAAQ,QAAQ,MAAM,KAAK;AAAA,QACtD,UAAUD;AAAA,MACV;AAAA,MACD,SAAS;AAAA,MACT,UAAU,CAACJ,GAAE,qBAAqBA,GAAE,QAAQ,QAAQ,QAAQ;AAAA,QAC3D,UAAU,CAAC,MAAM;AAAA,MACjB,CAAA,GAAGA,GAAE,QAAQA,GAAE,mBAAmB;AAAA,QAClC,OAAO;AAAA,QACP,SAAS;AAAA,MACb,CAAI,GAAG;AAAA,QACH,WAAW;AAAA,QACX,UAAU,CAAC;AAAA,UACV,OAAO;AAAA,QACZ,GAAO;AAAA,UACF,OAAO;AAAA,QACZ,CAAK;AAAA,MACL,GAAM;AAAA,QACF,WAAW;AAAA,QACX,OAAO;AAAA,MACX,GAAM;AAAA,QACF,WAAW;AAAA,QACX,UAAU,CAAC;AAAA,UACV,OAAO,kBAAkBE;AAAA,QAC9B,GAAO;AAAA,UACF,OAAO,mBAAmBA;AAAA,QAC/B,GAAO;AAAA,UACF,OAAO,yBAAyBA;AAAA,QACrC,GAAO;AAAA,UACF,OAAO,oDAAoDA;AAAA,QAChE,CAAK;AAAA,QACD,WAAW;AAAA,MACf,GAAM;AAAA,QACF,OAAO,CAAC,MAAM,OAAOF,GAAE,mBAAmB;AAAA,QAC1C,WAAW;AAAA,UACV,GAAG;AAAA,UACH,GAAG;AAAA,QACH;AAAA,MACL,GAAM;AAAA,QACF,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU,CAAC;AAAA,UACV,WAAW;AAAA,UACX,OAAO;AAAA,UACP,KAAK;AAAA,QACV,CAAK;AAAA,MACL,GAAM;AAAA,QACF,OAAO,CAAC,OAAO,OAAO,eAAeA,GAAE,mBAAmB;AAAA,QAC1D,WAAW;AAAA,UACV,GAAG;AAAA,UACH,GAAG;AAAA,UACH,GAAG;AAAA,QACH;AAAA,MACL,GAAM;AAAA,QACF,OAAO,CAAC,OAAO,OAAOA,GAAE,qBAAqB,OAAO,IAAI;AAAA,QACxD,WAAW;AAAA,UACV,GAAG;AAAA,UACH,GAAG;AAAA,UACH,GAAG;AAAA,QACH;AAAA,MACL,GAAM;AAAA,QACF,OAAO,CAAC,QAAQ,OAAOA,GAAE,mBAAmB;AAAA,QAC5C,WAAW;AAAA,UACV,GAAG;AAAA,UACH,GAAG;AAAA,QACH;AAAA,MACL,GAAM;AAAA,QACF,OAAO,CAAC,wCAAwC,OAAOA,GAAE,mBAAmB;AAAA,QAC5E,WAAW;AAAA,UACV,GAAG;AAAA,UACH,GAAG;AAAA,QACH;AAAA,MACL,GAAM;AAAA,QACF,OAAOA,GAAE,WAAW;AAAA,QACpB,UAAU;AAAA,UACT,SAAS;AAAA,UACT,UAAUI;AAAA,UACV,MAAMC;AAAA,QACN;AAAA,MACL,GAAM;AAAA,QACF,WAAW;AAAA,QACX,OAAO;AAAA,MACP,GAAEJ,EAAC;AAAA,IACJ;AAAA,EACD;AAAA,EACD,WAAW,CAAAD,OAAK;AACf,UAAMG,KAAI,EAAEH,EAAC,GACZC,KAAI,IACJC,KAAI,IACJE,KAAI,YACJC,KAAI;AAAA,MACH,WAAW;AAAA,MACX,OAAO;AAAA,MACP,WAAW;AAAA,IACf;AACE,WAAO;AAAA,MACN,MAAM;AAAA,MACN,kBAAkB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU,CAACL,GAAE,qBAAqBA,GAAE,sBAAsBG,GAAE,iBAAiB;AAAA,QAC5E,WAAW;AAAA,QACX,OAAO;AAAA,QACP,WAAW;AAAA,MACf,GAAM;AAAA,QACF,WAAW;AAAA,QACX,OAAO;AAAA,QACP,WAAW;AAAA,MACf,GAAMA,GAAE,yBAAyB;AAAA,QAC7B,WAAW;AAAA,QACX,OAAO,SAAS,EAAE,KAAK,GAAG,IAAI;AAAA,QAC9B,WAAW;AAAA,MACf,GAAM;AAAA,QACF,WAAW;AAAA,QACX,OAAO,OAAOD,GAAE,KAAK,GAAG,IAAI;AAAA,MAChC,GAAM;AAAA,QACF,WAAW;AAAA,QACX,OAAO,WAAWD,GAAE,KAAK,GAAG,IAAI;AAAA,MAChC,GAAEI,IAAG;AAAA,QACL,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU,CAACF,GAAE,eAAe;AAAA,MAChC,GAAMA,GAAE,cAAc;AAAA,QAClB,WAAW;AAAA,QACX,OAAO,SAAS,GAAG,KAAK,GAAG,IAAI;AAAA,MACnC,GAAM;AAAA,QACF,OAAO;AAAA,MACX,GAAM;AAAA,QACF,OAAO;AAAA,QACP,KAAK;AAAA,QACL,WAAW;AAAA,QACX,UAAU;AAAA,UAACA,GAAE;AAAA,UAAeE;AAAA,UAAGF,GAAE;AAAA,UAAUA,GAAE;AAAA,UAAiBH,GAAE;AAAA,UAAmBA,GAAE;AAAA,UACpFG,GAAE;AAAA,UAAWA,GAAE;AAAA,QACf;AAAA,MACL,GAAM;AAAA,QACF,OAAO;AAAA,QACP,UAAU;AAAA,UACT,UAAUC;AAAA,UACV,SAAS;AAAA,QACT;AAAA,MACL,GAAM;AAAA,QACF,OAAO;AAAA,QACP,KAAK;AAAA,QACL,aAAa;AAAA,QACb,UAAU;AAAA,UACT,UAAU;AAAA,UACV,SAAS;AAAA,UACT,WAAW,GAAG,KAAK,GAAG;AAAA,QACtB;AAAA,QACD,UAAU,CAAC;AAAA,UACV,OAAOA;AAAA,UACP,WAAW;AAAA,QAChB,GAAO;AAAA,UACF,OAAO;AAAA,UACP,WAAW;AAAA,QAChB,GAAOC,IAAGL,GAAE,mBAAmBA,GAAE,kBAAkBG,GAAE,UAAUA,GAAE,eAAe;AAAA,MAChF,GAAMA,GAAE,iBAAiB;AAAA,IACtB;AAAA,EACD;AAAA,EACD,YAAY,CAAAH,QAAM;AAAA,IACjB,MAAM;AAAA,IACN,SAAS,CAAC,WAAW,cAAc;AAAA,IACnC,UAAU,CAAC;AAAA,MACV,WAAW;AAAA,MACX,OAAO;AAAA,MACP,QAAQ;AAAA,QACP,KAAK;AAAA,QACL,aAAa;AAAA,MACb;AAAA,IACJ,CAAG;AAAA,EACH;AAAA,EACC,UAAU,CAAAA,OAAK;AACd,UAAMG,KAAIH,GAAE,OACXC,KAAID,GAAE,QAAQ,MAAM,GAAG,GACvBE,KAAI,CAAC,QAAQ,SAAS,SAAS,GAC/BE,KAAI;AAAA,MAAC;AAAA,MAAU;AAAA,MAAU;AAAA,MAAQ;AAAA,MAAW;AAAA,MAAQ;AAAA,MAAa;AAAA,MAAQ;AAAA,MAAQ;AAAA,MAAO;AAAA,MACvF;AAAA,MAAW;AAAA,MAAS;AAAA,MAAO;AAAA,MAAW;AAAA,MAAY;AAAA,MAAS;AAAA,MAAS;AAAA,MAAY;AAAA,MAAW;AAAA,MAAQ;AAAA,MACnG;AAAA,MAAY;AAAA,MAAQ;AAAA,MAAa;AAAA,MAAW;AAAA,MAAW;AAAA,IACvD,GACDC,KAAI;AAAA,MAAC;AAAA,MAAO;AAAA,MAAQ;AAAA,MAAa;AAAA,MAAQ;AAAA,MAAQ;AAAA,MAAO;AAAA,MAAQ;AAAA,MAAQ;AAAA,MAAW;AAAA,MAAY;AAAA,MAC9F;AAAA,MAAO;AAAA,MAAQ;AAAA,MAAS;AAAA,MAAa;AAAA,MAAc;AAAA,MAAa;AAAA,MAAc;AAAA,MAAS;AAAA,MAAW;AAAA,MAClG;AAAA,MAAW;AAAA,MAAe;AAAA,MAAS;AAAA,MAAc;AAAA,MAAiB;AAAA,MAAe;AAAA,MACjF;AAAA,MAAkB;AAAA,MAAc;AAAA,MAAc;AAAA,MAAwB;AAAA,MAAc;AAAA,MAAO;AAAA,MAC3F;AAAA,MAAQ;AAAA,MAAW;AAAA,MAAM;AAAA,MAAO;AAAA,MAAS;AAAA,MAAS;AAAA,MAAO;AAAA,MAAO;AAAA,MAAO;AAAA,MAAa;AAAA,MAAS;AAAA,MAC7F;AAAA,MAAgB;AAAA,MAAmB;AAAA,MAAmB;AAAA,MAAY;AAAA,MAAkB;AAAA,MAAS;AAAA,MAC7F;AAAA,MAAa;AAAA,MAAa;AAAA,MAAc;AAAA,MAAkB;AAAA,MAAW;AAAA,MAAc;AAAA,MAAY;AAAA,MAC/F;AAAA,MAAY;AAAA,MAAc;AAAA,MAAO;AAAA,MAAQ;AAAA,MAAQ;AAAA,MAAc;AAAA,MAAe;AAAA,MAC9E;AAAA,MAAmB;AAAA,MAAO;AAAA,MAAO;AAAA,MAAQ;AAAA,MAAa;AAAA,MAAmB;AAAA,MAAS;AAAA,MAAQ;AAAA,MAC1F;AAAA,MAAU;AAAA,MAAS;AAAA,MAAY;AAAA,MAAW;AAAA,MAAY;AAAA,IACtD,GACDE,KAAI;AAAA,MAAC;AAAA,MAAgB;AAAA,MAAe;AAAA,MAAe;AAAA,MAAe;AAAA,MAAY;AAAA,MAC7E;AAAA,MAAkB;AAAA,MAAiB;AAAA,MAAe;AAAA,MAAiB;AAAA,MAAiB;AAAA,MACpF;AAAA,MAAe;AAAA,MAAc;AAAA,MAAe;AAAA,IAC5C,GACDC,KAAIH,IACJI,KAAI;AAAA,MAAC;AAAA,MAAO;AAAA,MAAQ;AAAA,MAAO;AAAA,MAAY;AAAA,MAAS;AAAA,MAAO;AAAA,MAAO;AAAA,MAAO;AAAA,MAAS;AAAA,MAC7E;AAAA,MAAyB;AAAA,MAAM;AAAA,MAAc;AAAA,MAAQ;AAAA,MAAc;AAAA,MAAM;AAAA,MAAQ;AAAA,MACjF;AAAA,MAAiB;AAAA,MAAO;AAAA,MAAS;AAAA,MAAe;AAAA,MAAmB;AAAA,MAAW;AAAA,MAAU;AAAA,MAAU;AAAA,MAClG;AAAA,MAAW;AAAA,MAAQ;AAAA,MAAM;AAAA,MAAQ;AAAA,MAAU;AAAA,MAAe;AAAA,MAAY;AAAA,MAAQ;AAAA,MAAQ;AAAA,MAAQ;AAAA,MAC9F;AAAA,MAAQ;AAAA,MAAe;AAAA,MAAa;AAAA,MAAoB;AAAA,MAAS;AAAA,MAAc;AAAA,MAAQ;AAAA,MACvF;AAAA,MAAY;AAAA,MAAW;AAAA,MAAW;AAAA,MAAU;AAAA,MAAU;AAAA,MAAa;AAAA,MAAW;AAAA,MAAc;AAAA,MAC5F;AAAA,MAAW;AAAA,MAAQ;AAAA,MAAQ;AAAA,MAAiB;AAAA,MAAO;AAAA,MAAQ;AAAA,MAAS;AAAA,MAAa;AAAA,MAAc;AAAA,MAC/F;AAAA,MAAS;AAAA,MAAQ;AAAA,MAAa;AAAA,MAAW;AAAA,MAAmB;AAAA,MAC5D;AAAA,MAAmC;AAAA,MAAgB;AAAA,MAAgB;AAAA,MAAe;AAAA,MAClF;AAAA,MAAgB;AAAA,MAAqB;AAAA,MAAgB;AAAA,MAAgB;AAAA,MACrE;AAAA,MAAgB;AAAA,MAAU;AAAA,MAAS;AAAA,MAAQ;AAAA,MAAO;AAAA,MAAc;AAAA,MAAO;AAAA,MAAW;AAAA,MAAY;AAAA,MAC9F;AAAA,MAAW;AAAA,MAAU;AAAA,MAAU;AAAA,MAAc;AAAA,MAAS;AAAA,MAAY;AAAA,MAAiB;AAAA,MACnF;AAAA,MAAY;AAAA,MAAU;AAAA,MAAQ;AAAA,MAAW;AAAA,MAAQ;AAAA,MAAW;AAAA,MAAQ;AAAA,MAAS;AAAA,MAAO;AAAA,MACpF;AAAA,MAAiB;AAAA,MAAY;AAAA,MAAU;AAAA,MAAU;AAAA,MAAS;AAAA,MAAU;AAAA,MAAQ;AAAA,MAAW;AAAA,MAAU;AAAA,MACjG;AAAA,MAAY;AAAA,MAAW;AAAA,MAAS;AAAA,MAAS;AAAA,MAAU;AAAA,MAAe;AAAA,MAAS;AAAA,MAAS;AAAA,MAAO;AAAA,MAC3F;AAAA,MAAa;AAAA,MAAQ;AAAA,MAAQ;AAAA,MAAQ;AAAA,MAAY;AAAA,MAAU;AAAA,MAAO;AAAA,MAAU;AAAA,MAAS;AAAA,MAAS;AAAA,MAC9F;AAAA,MAAU;AAAA,MAAU;AAAA,MAAQ;AAAA,MAAQ;AAAA,MAAY;AAAA,MAAM;AAAA,MAAa;AAAA,MAAW;AAAA,MAAS;AAAA,MACvF;AAAA,MAAe;AAAA,MAAU;AAAA,MAAO;AAAA,MAAW;AAAA,MAAa;AAAA,MAAgB;AAAA,MAAY;AAAA,MAAQ;AAAA,MAAM;AAAA,MAClG;AAAA,MAAc;AAAA,MAAiB;AAAA,MAAe;AAAA,MAAe;AAAA,MAAkB;AAAA,MAAc;AAAA,MAC7F;AAAA,MAAwB;AAAA,MAAc;AAAA,MAAO;AAAA,MAAY;AAAA,MAAS;AAAA,MAAc;AAAA,MAAW;AAAA,MAC3F;AAAA,MAAW;AAAA,MAAQ;AAAA,MAAQ;AAAA,MAAc;AAAA,MAAW;AAAA,MAAM;AAAA,MAAS;AAAA,MAAa;AAAA,MAAkB;AAAA,MAClG;AAAA,MAAS;AAAA,MAAS;AAAA,MAAS;AAAA,MAAgB;AAAA,MAAmB;AAAA,MAAW;AAAA,MAAO;AAAA,MAAU;AAAA,MAC1F;AAAA,MAAU;AAAA,MAAO;AAAA,MAAU;AAAA,MAAO;AAAA,MAAY;AAAA,MAAU;AAAA,MAAS;AAAA,MAAY;AAAA,MAAY;AAAA,MACzF;AAAA,MAAS;AAAA,MAAS;AAAA,MAAO;AAAA,MAAM;AAAA,MAAQ;AAAA,MAAa;AAAA,MAAO;AAAA,MAAa;AAAA,MAAS;AAAA,MAAQ;AAAA,MACzF;AAAA,MAAW;AAAA,MAAgB;AAAA,MAAqB;AAAA,MAAM;AAAA,MAAU;AAAA,MAAO;AAAA,MAAQ;AAAA,MAAM;AAAA,MAAO;AAAA,MAC5F;AAAA,MAAQ;AAAA,MAAM;AAAA,MAAS;AAAA,MAAO;AAAA,MAAS;AAAA,MAAQ;AAAA,MAAY;AAAA,MAAW;AAAA,MAAa;AAAA,MAAa;AAAA,MAChG;AAAA,MAAO;AAAA,MAAW;AAAA,MAAgB;AAAA,MAAmB;AAAA,MAAmB;AAAA,MAAU;AAAA,MAAW;AAAA,MAC7F;AAAA,MAAkB;AAAA,MAAS;AAAA,MAAY;AAAA,MAAa;AAAA,MAAW;AAAA,MAAW;AAAA,MAAa;AAAA,MAAO;AAAA,MAC9F;AAAA,MAAQ;AAAA,MAAS;AAAA,MAAQ;AAAA,MAAa;AAAA,MAAO;AAAA,MAAc;AAAA,MAAe;AAAA,MAAa;AAAA,MACvF;AAAA,MAAc;AAAA,MAAkB;AAAA,MAAW;AAAA,MAAc;AAAA,MAAY;AAAA,MAAY;AAAA,MAAY;AAAA,MAC7F;AAAA,MAAU;AAAA,MAAU;AAAA,MAAW;AAAA,MAAU;AAAA,MAAS;AAAA,MAAY;AAAA,MAAU;AAAA,MAAO;AAAA,MAAc;AAAA,MAC7F;AAAA,MAAW;AAAA,MAAa;AAAA,MAAS;AAAA,MAAU;AAAA,MAAU;AAAA,MAAU;AAAA,MAAQ;AAAA,MAAU;AAAA,MACjF;AAAA,MAAgB;AAAA,MAAO;AAAA,MAAQ;AAAA,MAAW;AAAA,MAAO;AAAA,MAAQ;AAAA,MAAQ;AAAA,MAAY;AAAA,MAAQ;AAAA,MACrF;AAAA,MAAgB;AAAA,MAAO;AAAA,MAAgB;AAAA,MAAY;AAAA,MAAc;AAAA,MAAQ;AAAA,MAAS;AAAA,MAAU;AAAA,MAC5F;AAAA,MAAe;AAAA,MAAe;AAAA,MAAU;AAAA,MAAa;AAAA,MAAmB;AAAA,MAAY;AAAA,MAAO;AAAA,MAC3F;AAAA,MAAU;AAAA,MAAe;AAAA,MAAe;AAAA,MAAS;AAAA,MAAe;AAAA,MAAO;AAAA,MAAQ;AAAA,MAAQ;AAAA,MACvF;AAAA,MAAa;AAAA,MAAiB;AAAA,MAAmB;AAAA,MAAM;AAAA,MAAY;AAAA,MAAa;AAAA,MAChF;AAAA,MAAe;AAAA,MAAS;AAAA,MAAW;AAAA,MAAQ;AAAA,MAAc;AAAA,MAAQ;AAAA,MAAY;AAAA,MAAW;AAAA,MAAS;AAAA,MACjG;AAAA,MAAW;AAAA,MAAU;AAAA,MAAU;AAAA,MAAS;AAAA,MAAQ;AAAA,MAAS;AAAA,MAAS;AAAA,MAAU;AAAA,MAAY;AAAA,MACxF;AAAA,MAAY;AAAA,MAAa;AAAA,MAAW;AAAA,MAAW;AAAA,MAAc;AAAA,MAAQ;AAAA,MAAY;AAAA,MAAS;AAAA,MAC1F;AAAA,MAAU;AAAA,MAAQ;AAAA,MAAU;AAAA,MAAW;AAAA,MAAQ;AAAA,MAAO;AAAA,MAAO;AAAA,MAAa;AAAA,MAAQ;AAAA,MAAS;AAAA,MAC3F;AAAA,MAAQ;AAAA,IACZ,EAAK,OAAQ,CAAAT,OAAK,CAACK,GAAE,SAASL,EAAC,CAAG,GAC/Be,KAAI;AAAA,MACH,OAAOZ,GAAE,OAAO,MAAMA,GAAE,OAAO,GAAGK,EAAC,GAAG,OAAO;AAAA,MAC7C,WAAW;AAAA,MACX,UAAU;AAAA,QACT,UAAUA;AAAA,MACV;AAAA,IACL;AACE,WAAO;AAAA,MACN,MAAM;AAAA,MACN,kBAAkB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,QACT,UAAU;AAAA,QACV,UAAU,CAACR,IAAG;AAAA,UACb,YAAYG;AAAA,UACZ,MAAMF;AAAA,QACN,IAAG,OAAO;AACV,gBAAMC,KAAID;AACV,iBAAOE,KAAIA,MAAK,IAAIH,GAAE,IAAK,CAAAA,OAAKA,GAAE,MAAM,QAAQ,KAAKG,GAAE,SAASH,EAAC,IAAIA,KAAIE,GAAEF,EAAC,IAAIA,KAAI,OAAOA,EAAG;AAAA,QAC9F,GAAES,IAAG;AAAA,UACL,MAAM,CAAAT,OAAKA,GAAE,SAAS;AAAA,QAC3B,CAAK;AAAA,QACD,SAASE;AAAA,QACT,MAAME;AAAA,QACN,UAAU;AAAA,UAAC;AAAA,UAAmB;AAAA,UAAgB;AAAA,UAAmC;AAAA,UAChF;AAAA,UAAgB;AAAA,UAAkB;AAAA,UAAoC;AAAA,UAAgB;AAAA,UACtF;AAAA,UAAe;AAAA,UAAe;AAAA,UAAgB;AAAA,UAAa;AAAA,UAAqB;AAAA,QAChF;AAAA,MACD;AAAA,MACD,UAAU,CAAC;AAAA,QACV,OAAOD,GAAE,OAAO,GAAGI,EAAC;AAAA,QACpB,WAAW;AAAA,QACX,UAAU;AAAA,UACT,UAAU;AAAA,UACV,SAASE,GAAE,OAAOF,EAAC;AAAA,UACnB,SAASL;AAAA,UACT,MAAME;AAAA,QACN;AAAA,MACL,GAAM;AAAA,QACF,WAAW;AAAA,QACX,OAAOD,GAAE,OAAO,oBAAoB,gBAAgB,iBAAiB,kBAAkB;AAAA,MACvF,GAAEY,IAAG;AAAA,QACL,WAAW;AAAA,QACX,OAAO;AAAA,MACX,GAAM;AAAA,QACF,WAAW;AAAA,QACX,UAAU,CAAC;AAAA,UACV,OAAO;AAAA,UACP,KAAK;AAAA,UACL,UAAU,CAAC;AAAA,YACV,OAAO;AAAA,UACb,CAAM;AAAA,QACN,CAAK;AAAA,MACL,GAAM;AAAA,QACF,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU,CAAC;AAAA,UACV,OAAO;AAAA,QACZ,CAAK;AAAA,MACD,GAAEf,GAAE,eAAeA,GAAE,sBAAsBC,IAAG;AAAA,QAC9C,WAAW;AAAA,QACX,OAAO;AAAA,QACP,WAAW;AAAA,MACf,CAAI;AAAA,IACD;AAAA,EACD;AAAA,EACD,YAAY,CAAAD,OAAK;AAChB,UAAMG,KAAI;AAAA,MACR,OAAO;AAAA,MACP,WAAW;AAAA,IACX,GACDF,KAAID,GAAE,QAAQ,QAAQ,QAAQ;AAAA,MAC7B,UAAU,CAAC,MAAM;AAAA,IACrB,CAAI,GACDE,KAAI,CAACF,GAAE,qBAAqBC,EAAC,GAC7BG,KAAI;AAAA,MACH,OAAO,CAAC,MAAM,EAAE,GAAG,IAAI,GAAG,EAAE,CAAC;AAAA,MAC7B,WAAW;AAAA,QACV,GAAG;AAAA,MACH;AAAA,IACD,GACDC,KAAI;AAAA,MACH,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAAA,MACvB,WAAW;AAAA,IACX,GACDE,KAAI,GAAG,OAAQ,CAAAP,OAAK,YAAY,OAAOA,IAAI,OAAO,CAAC,KAAK,CAAC,GACzDQ,KAAI;AAAA,MACH,UAAU,CAAC;AAAA,QACV,WAAW;AAAA,QACX,OAAO,EAAE,GAAG,GAAG,OAAQ,CAAAR,OAAK,YAAY,OAAOA,EAAC,EAAG,OAAO,EAAE,EAAE,IAAI,EAAE,GAAG,GAAG,EAAE;AAAA,MACjF,CAAK;AAAA,IACD,GACDS,KAAI;AAAA,MACH,UAAU,EAAE,SAAS,MAAM;AAAA,MAC3B,SAASF,GAAE,OAAO,EAAE;AAAA,MACpB,SAAS;AAAA,IACT,GACDQ,KAAI,CAACX,IAAGC,IAAGG,EAAC,GACZE,KAAI,CAAC;AAAA,MACJ,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAAA,MACvB,WAAW;AAAA,IACf,GAAM;AAAA,MACF,WAAW;AAAA,MACX,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,QAAQ;AAAA,IACrC,CAAI,GACDa,KAAI;AAAA,MACH,OAAO;AAAA,MACP,WAAW;AAAA,IACX,GACDC,KAAI,CAACD,IAAG;AAAA,MACP,WAAW;AAAA,MACX,WAAW;AAAA,MACX,UAAU,CAAC;AAAA,QACV,OAAO;AAAA,MACZ,GAAO;AAAA,QACF,OAAO,WAAW,EAAE;AAAA,MACzB,CAAK;AAAA,IACL,CAAI,GACDZ,KAAI,oBACJC,KAAI;AAAA,MACH,WAAW;AAAA,MACX,WAAW;AAAA,MACX,UAAU,CAAC;AAAA,QACV,OAAO;AAAA,MACZ,GAAO;AAAA,QACF,OAAO,SAASD,EAAC,SAASA,EAAC;AAAA,MAChC,GAAO;AAAA,QACF,OAAO;AAAA,MACZ,GAAO;AAAA,QACF,OAAO;AAAA,MACZ,CAAK;AAAA,IACD,GACDG,KAAI,CAACd,KAAI,QAAQ;AAAA,MAChB,WAAW;AAAA,MACX,UAAU,CAAC;AAAA,QACV,OAAO,EAAE,MAAMA,IAAG,YAAY;AAAA,MACnC,GAAO;AAAA,QACF,OAAO,EAAE,MAAMA,IAAG,uBAAuB;AAAA,MAC9C,CAAK;AAAA,IACL,IACGsB,KAAI,CAACtB,KAAI,QAAQ;AAAA,MAChB,WAAW;AAAA,MACX,OAAO,EAAE,MAAMA,IAAG,uBAAuB;AAAA,IAC7C,IACG4B,KAAI,CAAC5B,KAAI,QAAQ;AAAA,MAChB,WAAW;AAAA,MACX,OAAO;AAAA,MACP,OAAO,EAAE,MAAMA,IAAG,IAAI;AAAA,MACtB,KAAK;AAAA,IACT,IACGmB,KAAI,CAACnB,KAAI,QAAQ;AAAA,MAChB,OAAO,EAAEA,IAAG,KAAK;AAAA,MACjB,KAAK,EAAE,OAAOA,EAAC;AAAA,MACf,UAAU,CAACc,GAAEd,EAAC,GAAGsB,GAAEtB,EAAC,GAAG4B,GAAE5B,EAAC,CAAC;AAAA,IAC/B,IACG+B,KAAI,CAAC/B,KAAI,QAAQ;AAAA,MAChB,OAAO,EAAEA,IAAG,GAAG;AAAA,MACf,KAAK,EAAE,KAAKA,EAAC;AAAA,MACb,UAAU,CAACc,GAAEd,EAAC,GAAG4B,GAAE5B,EAAC,CAAC;AAAA,IACzB,IACG8B,KAAI;AAAA,MACH,WAAW;AAAA,MACX,UAAU,CAACX,GAAC,GAAIA,GAAE,GAAG,GAAGA,GAAE,IAAI,GAAGA,GAAE,KAAK,GAAGY,GAAG,GAAEA,GAAE,GAAG,GAAGA,GAAE,IAAI,GAAGA,GAAE,KAAK,CAAC;AAAA,IACzE,GACDC,KAAI;AAAA,MACH,OAAO,EAAE,KAAK,IAAI,GAAG;AAAA,IACrB,GACDhB,KAAI,CAACgB,IAAG;AAAA,MACP,WAAW;AAAA,MACX,OAAO;AAAA,IACX,GAAM;AAAA,MACF,WAAW;AAAA,MACX,OAAO,MAAM,EAAE;AAAA,IACnB,CAAI,GACDnB,KAAI,CAAC;AAAA,MACJ,OAAO;AAAA,MACP,WAAW;AAAA,MACX,QAAQ;AAAA,QACP,UAAU,CAAC;AAAA,UACV,OAAO;AAAA,UACP,KAAK;AAAA,UACL,UAAU;AAAA,UACV,UAAU,CAAC,GAAGW,IAAGZ,IAAGkB,EAAC;AAAA,QAC3B,CAAM;AAAA,MACD;AAAA,IACL,GAAM;AAAA,MACF,WAAW;AAAA,MACX,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC;AAAA,IAC1B,GAAM;AAAA,MACF,WAAW;AAAA,MACX,OAAO,EAAE,KAAK,EAAE;AAAA,IACpB,CAAI,GACDb,KAAI;AAAA,MACH,OAAO,EAAE,SAAS;AAAA,MAClB,WAAW;AAAA,MACX,UAAU,CAAC;AAAA,QACV,WAAW;AAAA,QACX,OAAO,EAAE,iEAAiE,IAAI,GAAG;AAAA,MACtF,GAAO;AAAA,QACF,WAAW;AAAA,QACX,OAAO;AAAA,QACP,WAAW;AAAA,MAChB,GAAO;AAAA,QACF,OAAO;AAAA,QACP,WAAW;AAAA,MAChB,GAAO;AAAA,QACF,OAAO;AAAA,QACP,WAAW;AAAA,MAChB,GAAO;AAAA,QACF,OAAO,EAAE,WAAW,EAAE,EAAE,CAAC;AAAA,QACzB,WAAW;AAAA,MAChB,CAAK;AAAA,IACD,GACDC,KAAI;AAAA,MACH,OAAO;AAAA,MACP,KAAK;AAAA,MACL,UAAUT;AAAA,MACV,UAAU,CAAC,GAAGP,IAAG,GAAGa,IAAG,GAAGF,IAAGU,IAAGN,EAAC;AAAA,IACrC;AACE,IAAAA,GAAE,SAAS,KAAKC,EAAC;AACjB,UAAME,KAAI;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,WAAW;AAAA,MACX,UAAUX;AAAA,MACV,UAAU,CAAC,QAAQ;AAAA,QAClB,OAAO,EAAE,IAAI,MAAM;AAAA,QACnB,UAAU;AAAA,QACV,WAAW;AAAA,MACX,GAAE,GAAGP,IAAG,GAAGa,IAAG,GAAGL,IAAG,GAAGc,IAAGZ,IAAGkB,IAAG,GAAGd,IAAG,GAAGH,IAAGI,EAAC;AAAA,IAC9C,GACDgB,KAAI;AAAA,MACH,OAAO;AAAA,MACP,KAAK;AAAA,MACL,UAAU,CAAC,GAAG/B,IAAGe,EAAC;AAAA,IAClB,GACDY,KAAI;AAAA,MACH,OAAO;AAAA,MACP,KAAK;AAAA,MACL,UAAUpB;AAAA,MACV,UAAU,CAAC;AAAA,QACV,OAAO,EAAE,EAAE,EAAE,IAAI,MAAM,CAAC,GAAG,EAAE,EAAE,IAAI,OAAO,IAAI,MAAM,CAAC,CAAC;AAAA,QACtD,KAAK;AAAA,QACL,WAAW;AAAA,QACX,UAAU,CAAC;AAAA,UACV,WAAW;AAAA,UACX,OAAO;AAAA,QACb,GAAQ;AAAA,UACF,WAAW;AAAA,UACX,OAAO;AAAA,QACb,CAAM;AAAA,MACD,GAAE,GAAGP,IAAG,GAAGa,IAAG,GAAGS,IAAGZ,IAAGkB,IAAG,GAAGjB,IAAGI,IAAGG,EAAC;AAAA,MACrC,YAAY;AAAA,MACZ,SAAS;AAAA,IACT,GACDO,KAAI;AAAA,MACH,OAAO,CAAC,QAAQ,OAAO,EAAEK,GAAE,OAAO,IAAI,EAAE,CAAC;AAAA,MACzC,WAAW;AAAA,QACV,GAAG;AAAA,QACH,GAAG;AAAA,MACH;AAAA,MACD,UAAU,CAACC,IAAGJ,IAAG1B,EAAC;AAAA,MAClB,SAAS,CAAC,MAAM,GAAG;AAAA,IACnB,GACD+B,KAAI;AAAA,MACH,OAAO,CAAC,6BAA6B,aAAa;AAAA,MAClD,WAAW;AAAA,QACV,GAAG;AAAA,MACH;AAAA,MACD,UAAU,CAACD,IAAGJ,IAAG1B,EAAC;AAAA,MAClB,SAAS;AAAA,IACT,GACDgC,KAAI;AAAA,MACH,OAAO,CAAC,YAAY,OAAO,EAAE;AAAA,MAC7B,WAAW;AAAA,QACV,GAAG;AAAA,QACH,GAAG;AAAA,MACH;AAAA,IACD,GACDC,KAAI;AAAA,MACH,OAAO,CAAC,mBAAmB,OAAO,EAAE;AAAA,MACpC,WAAW;AAAA,QACV,GAAG;AAAA,QACH,GAAG;AAAA,MACH;AAAA,MACD,UAAU,CAACnB,EAAC;AAAA,MACZ,UAAU,CAAC,GAAG,IAAI,GAAG,EAAE;AAAA,MACvB,KAAK;AAAA,IACT;AACE,eAAWjB,MAAK8B,GAAE,UAAU;AAC3B,YAAM3B,KAAIH,GAAE,SAAS,KAAM,CAAAA,OAAK,eAAeA,GAAE;AACjD,MAAAG,GAAE,WAAWM;AACb,YAAMR,KAAI,CAAC,GAAGc,IAAG,GAAGL,IAAG,GAAGc,IAAGZ,IAAGkB,IAAG,GAAGd,EAAC;AACvC,MAAAb,GAAE,WAAW,CAAC,GAAGF,IAAG;AAAA,QACnB,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU,CAAC,QAAQ,GAAGA,EAAC;AAAA,MAC3B,CAAI;AAAA,IACD;AACD,WAAO;AAAA,MACN,MAAM;AAAA,MACN,UAAUQ;AAAA,MACV,UAAU,CAAC,GAAGP,IAAGyB,IAAGO,IAAG;AAAA,QACtB,eAAe;AAAA,QACf,KAAK;AAAA,QACL,YAAY;AAAA,QACZ,UAAUzB;AAAA,QACV,UAAU,CAACT,GAAE,QAAQA,GAAE,YAAY;AAAA,UAClC,WAAW;AAAA,UACX,OAAO;AAAA,QACZ,CAAK,GAAG,GAAGe,EAAC;AAAA,MACZ,GAAMoB,IAAGC,IAAG;AAAA,QACR,eAAe;AAAA,QACf,KAAK;AAAA,QACL,UAAU,CAAC,GAAGlC,EAAC;AAAA,QACf,WAAW;AAAA,MACX,GAAE,GAAGa,IAAG,GAAGL,IAAG,GAAGc,IAAGZ,IAAGkB,IAAG,GAAGd,IAAG,GAAGH,IAAGI,IAAGG,EAAC;AAAA,IAC3C;AAAA,EACD;AAAA,EACD,iBAAiB,CAAApB,OAAK;AACrB,UAAMG,KAAI,GAAGH,EAAC,GACbC,KAAI,CAAC,OAAO,QAAQ,UAAU,WAAW,UAAU,UAAU,SAAS,UAAU,UAAU,SAAS,GACnGC,KAAI;AAAA,MACH,eAAe;AAAA,MACf,KAAK;AAAA,MACL,YAAY;AAAA,MACZ,UAAU,CAACC,GAAE,QAAQ,eAAe;AAAA,IACpC,GACDC,KAAI;AAAA,MACH,eAAe;AAAA,MACf,KAAK;AAAA,MACL,YAAY;AAAA,MACZ,UAAU;AAAA,QACT,SAAS;AAAA,QACT,UAAUH;AAAA,MACV;AAAA,MACD,UAAU,CAACE,GAAE,QAAQ,eAAe;AAAA,IACpC,GACDE,KAAI;AAAA,MACH,UAAU;AAAA,MACV,SAAS,GAAG,OAAO;AAAA,QAAC;AAAA,QAAQ;AAAA,QAAa;AAAA,QAAa;AAAA,QAAU;AAAA,QAAW;AAAA,QAAa;AAAA,QACvF;AAAA,QAAW;AAAA,QAAY;AAAA,QAAY;AAAA,QAAQ;AAAA,MAChD,CAAK;AAAA,MACD,SAAS;AAAA,MACT,UAAU,GAAG,OAAOJ,EAAC;AAAA,MACrB,qBAAqB;AAAA,IACrB,GACDM,KAAI;AAAA,MACH,WAAW;AAAA,MACX,OAAO;AAAA,IACP,GACDC,KAAI,CAACR,IAAGG,IAAGF,OAAM;AAChB,YAAMC,KAAIF,GAAE,SAAS,UAAW,CAAAA,OAAKA,GAAE,UAAUG;AACjD,UAAI,OAAOD;AAAG,cAAM,MAAM,8BAA8B;AACxD,MAAAF,GAAE,SAAS,OAAOE,IAAG,GAAGD,EAAC;AAAA,IAC7B;AACE,WAAO,OAAO,OAAOE,GAAE,UAAUE,EAAC,GACjCF,GAAE,QAAQ,gBAAgB,KAAKI,EAAC,GAAGJ,GAAE,WAAWA,GAAE,SAAS,OAAO,CAACI,IAAGL,IAAGE,EAAC,CAAC,GAC3EI,GAAEL,IAAG,WAAWH,GAAE,QAAO,CAAE,GAAGQ,GAAEL,IAAG,cAAc;AAAA,MAChD,WAAW;AAAA,MACX,WAAW;AAAA,MACX,OAAO;AAAA,IACP,CAAA,GAAGA,GAAE,SAAS,KAAM,CAAAH,OAAK,eAAeA,GAAE,KAAK,EAAG,YAAY,GAAG,OAAO,OAAOG,IAAG;AAAA,MAClF,MAAM;AAAA,MACN,SAAS,CAAC,MAAM,KAAK;AAAA,IACrB,CAAA,GAAGA;AAAA,EACL;AAAA,EACD,YAAY,CAAAH,OAAK;AAChB,UAAMG,KAAIH,GAAE,OACXC,KAAI,2BACJC,KAAI,yBACJE,KAAI,mCACJC,KAAI,0BACJE,KAAI;AAAA,MACH,WAAW;AAAA,MACX,UAAU,CAAC;AAAA,QACV,OAAOJ,GAAE,OAAO,OAAOA,GAAE,OAAOD,IAAGD,EAAC,GAAG,KAAK;AAAA,MACjD,GAAO;AAAA,QACF,OAAOE,GAAE,OAAO,OAAOE,IAAG,KAAK;AAAA,MACpC,GAAO;AAAA,QACF,OAAOF,GAAE,OAAO,OAAOC,IAAG,KAAK;AAAA,MACpC,GAAO;AAAA,QACF,OAAOD,GAAE,OAAO,OAAOA,GAAE,OAAOD,IAAGD,EAAC,GAAG,MAAME,GAAE,OAAOC,IAAGC,EAAC,GAAG,KAAK;AAAA,MACvE,CAAK;AAAA,IACD,GACDG,KAAIR,GAAE,QAAQ,OAAO,KAAK;AAAA,MACzB,UAAU,CAAC;AAAA,QACV,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,MACV,CAAK;AAAA,IACL,CAAI,GACDS,KAAIT,GAAE,QAAQ,MAAM,KAAK;AAAA,MACxB,UAAU,CAAC;AAAA,QACV,OAAO;AAAA,MACZ,GAAO;AAAA,QACF,OAAO;AAAA,MACZ,CAAK;AAAA,IACL,CAAI;AACF,WAAO;AAAA,MACN,MAAM;AAAA,MACN,SAAS,CAAC,IAAI;AAAA,MACd,kBAAkB;AAAA,MAClB,kBAAkB;AAAA,QACjB,OAAO;AAAA,MACP;AAAA,MACD,UAAU;AAAA,QACT,SAAS;AAAA,QACT,UAAU;AAAA,QACV,MAAM;AAAA,QACN,SAAS;AAAA,MACT;AAAA,MACD,SAAS;AAAA,MACT,UAAU,CAAC;AAAA,QACV,WAAW;AAAA,QACX,OAAO;AAAA,MACX,GAAM;AAAA,QACF,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,SAAS;AAAA,QACT,UAAU,CAAC;AAAA,UACV,OAAO;AAAA,QACZ,CAAK;AAAA,MACD,GAAEO,IAAG;AAAA,QACL,WAAW;AAAA,QACX,WAAW;AAAA,QACX,UAAU,CAAC;AAAA,UACV,OAAO;AAAA,QACZ,GAAO;AAAA,UACF,OAAO;AAAA,QACZ,GAAO;AAAA,UACF,OAAO;AAAA,QACZ,GAAO;AAAA,UACF,OAAO;AAAA,QACZ,GAAO;AAAA,UACF,OAAO;AAAA,QACZ,CAAK;AAAA,MACL,GAAM;AAAA,QACF,WAAW;AAAA,QACX,OAAO;AAAA,MACX,GAAMC,IAAGC,IAAG;AAAA,QACR,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU;AAAA,UACT,SAAS;AAAA,QACT;AAAA,QACD,UAAU,CAACA,EAAC;AAAA,MAChB,CAAI;AAAA,IACD;AAAA,EACD;AAAA,EACD,WAAW,CAAAT,OAAK;AACf,IAAAA,GAAE;AACF,UAAMG,KAAIH,GAAE,QAAQ,OAAO,KAAK;AAChC,WAAOG,GAAE,SAAS,KAAK,MAAM,GAAG;AAAA,MAC/B,MAAM;AAAA,MACN,UAAU;AAAA,QACT,UAAU;AAAA,QACV,SAAS;AAAA,UAAC;AAAA,UAAW;AAAA,UAAS;AAAA,UAAM;AAAA,UAAS;AAAA,UAAY;AAAA,UAAQ;AAAA,UAAiB;AAAA,UAAQ;AAAA,UACzF;AAAA,UAAQ;AAAA,UAAQ;AAAA,UAAO;AAAA,UAAU;AAAA,UAAQ;AAAA,UAAc;AAAA,UAAc;AAAA,UAAa;AAAA,UAClF;AAAA,UAAa;AAAA,UAAc;AAAA,UAAa;AAAA,UAAU;AAAA,UAAM;AAAA,UAAU;AAAA,UAAS;AAAA,UAAQ;AAAA,UACnF;AAAA,UAAe;AAAA,UAAe;AAAA,UAAU;AAAA,UAAO;AAAA,UAAO;AAAA,UAAU;AAAA,UAAS;AAAA,UAAU;AAAA,UACnF;AAAA,UAAU;AAAA,UAAc;AAAA,UAAa;AAAA,UAAS;AAAA,UAAS;AAAA,UAAa;AAAA,UAAQ;AAAA,UAAQ;AAAA,QACpF;AAAA,MACD;AAAA,MACD,UAAU,CAACH,GAAE,QAAQ,MAAM,GAAG,GAAGG,IAAG;AAAA,QACnC,OAAO,CAAC,oBAAoB,OAAO,GAAG;AAAA,QACtC,WAAW;AAAA,UACV,GAAG;AAAA,UACH,GAAG;AAAA,QACH;AAAA,MACL,GAAM;AAAA,QACF,WAAW;AAAA,QACX,OAAO;AAAA,MACX,GAAM;AAAA,QACF,OAAO;AAAA,QACP,WAAW;AAAA,QACX,WAAW;AAAA,MACf,GAAM;AAAA,QACF,OAAO,CAAC,+BAA+B,OAAO,WAAW;AAAA,QACzD,WAAW;AAAA,UACV,GAAG;AAAA,UACH,GAAG;AAAA,QACH;AAAA,MACL,GAAMH,GAAE,mBAAmB;AAAA,QACvB,OAAO;AAAA,QACP,WAAW;AAAA,MACf,GAAM;AAAA,QACF,WAAW;AAAA,QACX,OAAO;AAAA,MACX,GAAM;AAAA,QACF,WAAW;AAAA,QACX,WAAW;AAAA,QACX,OAAO;AAAA,MACX,CAAI;AAAA,IACD;AAAA,EACD;AAAA,EACD,WAAW,CAAAA,OAAK;AACf,UAAMG,KAAI,0BACTF,KAAI,+BACJC,KAAI;AAAA,MACH,WAAW;AAAA,MACX,WAAW;AAAA,MACX,UAAU,CAAC;AAAA,QACV,OAAO;AAAA,QACP,KAAK;AAAA,MACV,GAAO;AAAA,QACF,OAAO;AAAA,QACP,KAAK;AAAA,MACV,GAAO;AAAA,QACF,OAAO;AAAA,MACZ,CAAK;AAAA,MACD,UAAU,CAACF,GAAE,kBAAkB;AAAA,QAC9B,WAAW;AAAA,QACX,UAAU,CAAC;AAAA,UACV,OAAO;AAAA,UACP,KAAK;AAAA,QACX,GAAQ;AAAA,UACF,OAAO;AAAA,UACP,KAAK;AAAA,QACX,CAAM;AAAA,MACN,CAAK;AAAA,IACD,GACDI,KAAIJ,GAAE,QAAQE,IAAG;AAAA,MAChB,UAAU,CAAC;AAAA,QACV,OAAO;AAAA,QACP,KAAK;AAAA,MACV,GAAO;AAAA,QACF,OAAO;AAAA,QACP,KAAK;AAAA,MACV,GAAO;AAAA,QACF,OAAO;AAAA,MACZ,CAAK;AAAA,IACL,CAAI,GACDG,KAAI;AAAA,MACH,KAAK;AAAA,MACL,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,UAAUF;AAAA,MACV,WAAW;AAAA,IACX,GACDI,KAAI;AAAA,MACH,OAAO;AAAA,MACP,KAAK;AAAA,MACL,UAAU,CAACF,EAAC;AAAA,MACZ,SAAS;AAAA,MACT,WAAW;AAAA,IACX,GACDG,KAAI;AAAA,MACH,OAAO;AAAA,MACP,KAAK;AAAA,MACL,UAAU,CAACH,EAAC;AAAA,MACZ,SAAS;AAAA,MACT,WAAW;AAAA,IACX,GACDI,KAAI,CAAC;AAAA,MACJ,WAAW;AAAA,MACX,UAAU,CAAC;AAAA,QACV,OAAO;AAAA,MACZ,GAAO;AAAA,QACF,OAAO;AAAA,MACZ,GAAO;AAAA,QACF,OAAO;AAAA,MACZ,CAAK;AAAA,IACL,GAAM;AAAA,MACF,WAAW;AAAA,MACX,OAAO;AAAA,MACP,WAAW;AAAA,IACf,GAAM;AAAA,MACF,WAAW;AAAA,MACX,OAAO;AAAA,IACX,GAAM;AAAA,MACF,OAAO;AAAA,MACP,KAAK;AAAA,MACL,aAAa;AAAA,MACb,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,WAAW;AAAA,IACf,GAAM;AAAA,MACF,WAAW;AAAA,MACX,OAAO,WAAWR;AAAA,IACtB,GAAM;AAAA,MACF,WAAW;AAAA,MACX,OAAO,OAAOA,KAAI;AAAA,IACtB,GAAM;AAAA,MACF,WAAW;AAAA,MACX,OAAO,MAAMA;AAAA,IACjB,GAAM;AAAA,MACF,WAAW;AAAA,MACX,OAAO,OAAOA;AAAA,IAClB,GAAM;AAAA,MACF,WAAW;AAAA,MACX,OAAO,MAAMD,GAAE,sBAAsB;AAAA,IACzC,GAAM;AAAA,MACF,WAAW;AAAA,MACX,OAAO,QAAQA,GAAE,sBAAsB;AAAA,IAC3C,GAAM;AAAA,MACF,WAAW;AAAA,MACX,OAAO;AAAA,MACP,WAAW;AAAA,IACf,GAAMA,GAAE,mBAAmB;AAAA,MACvB,eAAeG;AAAA,MACf,UAAU;AAAA,QACT,SAASA;AAAA,MACT;AAAA,IACL,GAAM;AAAA,MACF,WAAW;AAAA,MACX,OAAO;AAAA,IACX,GAAM;AAAA,MACF,WAAW;AAAA,MACX,OAAOH,GAAE,cAAc;AAAA,MACvB,WAAW;AAAA,IACf,GAAMO,IAAGC,IAAGN,EAAC,GACVa,KAAI,CAAC,GAAGN,EAAC;AACV,WAAOM,GAAE,IAAK,GAAEA,GAAE,KAAKX,EAAC,GAAGC,GAAE,WAAWU,IAAG;AAAA,MAC1C,MAAM;AAAA,MACN,kBAAkB;AAAA,MAClB,SAAS,CAAC,KAAK;AAAA,MACf,UAAUN;AAAA,IACV;AAAA,EACD;AACF,CAAC;AACI,MAAC,KAAK;AACX,WAAWT,MAAK,OAAO,KAAK,EAAE,GAAG;AAChC,QAAMG,KAAIH,GAAE,QAAQ,SAAS,EAAE,EAAE,QAAQ,KAAK,GAAG;AACjD,KAAG,iBAAiBG,IAAG,GAAGH,EAAC,CAAC;AAC7B;;"}