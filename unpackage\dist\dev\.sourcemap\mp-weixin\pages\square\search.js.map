{"version": 3, "file": "search.js", "sources": ["pages/square/search.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvc3F1YXJlL3NlYXJjaC52dWU"], "sourcesContent": ["<template>\n  <view class=\"container\">\n    <!-- 搜索框 -->\n    <view class=\"search-container\">\n      <view class=\"search-box\">\n        <image src=\"@/static/square/<EMAIL>\" class=\"search-icon\" mode=\"aspectFit\"></image>\n        <input v-model=\"searchKeyword\" class=\"search-input\" placeholder=\"搜索智能体\" placeholder-class=\"placeholder\"\n          @input=\"handleSearchInput\" @confirm=\"handleSearch\" focus />\n        <view v-if=\"searchKeyword\" class=\"clear-btn\" @tap=\"clearSearch\">\n          <text class=\"clear-text\">×</text>\n        </view>\n      </view>\n      <view class=\"cancel-btn\" @tap=\"handleCancel\">\n        <text class=\"cancel-text\">取消</text>\n      </view>\n    </view>\n\n    <!-- 搜索结果 -->\n    <view class=\"content-container\">\n      <!-- 加载状态 -->\n      <view v-if=\"loading\" class=\"loading-container\">\n        <text class=\"loading-text\">搜索中...</text>\n      </view>\n\n      <!-- 空状态 -->\n      <view v-else-if=\"!loading && searchResults.length === 0 && hasSearched\" class=\"empty-container\">\n        <image src=\"@/static/square/<EMAIL>\" class=\"empty-icon\" mode=\"aspectFit\"></image>\n        <text class=\"empty-text\">未找到相关智能体</text>\n        <text class=\"empty-desc\">试试其他关键词吧</text>\n      </view>\n\n      <!-- 搜索结果列表 -->\n      <scroll-view v-else-if=\"searchResults.length > 0\" scroll-y=\"true\" class=\"scroll-view\" @scrolltolower=\"loadMore\">\n        <view class=\"agent-list\">\n          <view v-for=\"item in searchResults\" :key=\"item.guid\" class=\"agent-item\">\n            <!-- 头像 -->\n            <view class=\"avatar\" @click=\"handleAgentClick(item)\">\n              <image :src=\"item.agentAvatar\" class=\"avatar-img\" mode=\"aspectFill\"></image>\n            </view>\n\n            <!-- 内容区域 -->\n            <view class=\"content\" @click=\"handleAgentClick(item)\">\n              <view class=\"title\">{{ item.agentName }}</view>\n              <view class=\"description\">{{ item.agentDesc }}</view>\n              <view class=\"author\">@{{ item.creator.nickname }}</view>\n            </view>\n\n            <!-- 右侧按钮 -->\n            <view class=\"action-btn\" :class=\"{ subscribed: item.isSubscribed }\" @click.stop=\"onSub(item)\">\n              <text class=\"btn-text\">{{ item.isSubscribed ? '已订阅' : '订阅' }}</text>\n            </view>\n          </view>\n        </view>\n\n        <!-- 加载更多 -->\n        <view v-if=\"hasMore\" class=\"load-more\">\n          <text class=\"load-more-text\">{{ loadingMore ? '加载中...' : '上拉加载更多' }}</text>\n        </view>\n\n        <!-- 没有更多 -->\n        <view v-else-if=\"searchResults.length > 0\" class=\"no-more\">\n          <text class=\"no-more-text\">没有更多了</text>\n        </view>\n      </scroll-view>\n\n      <!-- 默认状态 -->\n      <view v-else class=\"default-container\">\n        <image src=\"@/static/square/<EMAIL>\" class=\"default-icon\" mode=\"aspectFit\"></image>\n        <text class=\"default-text\">输入关键词搜索智能体</text>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref } from 'vue'\nimport { onLoad } from '@dcloudio/uni-app'\nimport { useUserStore } from '@/stores/user.js'\nimport { getAgentListApi, subscribeAgentApi } from '@/api/index.js'\n\nconst userStore = useUserStore()\n\n// 搜索相关\nconst searchKeyword = ref('')\nconst searchResults = ref([])\nconst loading = ref(false)\nconst hasSearched = ref(false)\nconst agentName = ref('')\n\n// 分页相关\nconst currentPage = ref(1)\nconst pageSize = ref(10)\nconst hasMore = ref(true)\nconst loadingMore = ref(false)\n\n// 搜索输入处理\nconst handleSearchInput = () => {\n  // 可以在这里添加防抖逻辑\n}\n\n\n// 执行搜索\nconst handleSearch = async () => {\n  if (!searchKeyword.value.trim()) {\n    uni.showToast({\n      title: '请输入搜索关键词',\n      icon: 'none'\n    })\n    return\n  }\n\n  // 重置搜索状态\n  currentPage.value = 1\n  hasMore.value = true\n  searchResults.value = []\n\n  await performSearch()\n}\n\n// 执行搜索请求\nconst performSearch = async () => {\n  if (loading.value || loadingMore.value) return\n\n  try {\n    if (currentPage.value === 1) {\n      loading.value = true\n    } else {\n      loadingMore.value = true\n    }\n\n    const res = await getAgentListApi({\n      merchantGuid: userStore.merchantGuid,\n      agentName: searchKeyword.value.trim(),\n      pageSize: pageSize.value,\n      page: currentPage.value,\n    })\n\n    if (res.code === 0) {\n      const newData = res.data.data || []\n\n      if (currentPage.value === 1) {\n        searchResults.value = newData\n        hasSearched.value = true\n\n      } else {\n        searchResults.value = [...searchResults.value, ...newData]\n      }\n\n      // 判断是否还有更多数据\n      hasMore.value = currentPage.value < res.data.last_page\n    }\n  } catch (error) {\n    console.error('搜索失败:', error)\n    uni.showToast({\n      title: '搜索失败，请重试',\n      icon: 'none'\n    })\n  } finally {\n    loading.value = false\n    loadingMore.value = false\n  }\n}\n\n// 加载更多\nconst loadMore = async () => {\n  if (!hasMore.value || loadingMore.value) return\n\n  currentPage.value++\n  await performSearch()\n}\n\n// 清除搜索\nconst clearSearch = () => {\n  searchKeyword.value = ''\n  searchResults.value = []\n  hasSearched.value = false\n  currentPage.value = 1\n  hasMore.value = true\n}\n\n// 取消搜索\nconst handleCancel = () => {\n  uni.navigateBack()\n}\n\n// 点击智能体\nconst handleAgentClick = (item) => {\n  console.log('点击智能体:', item.agentName)\n  // 跳转到详情页\n  uni.navigateTo({\n    url: `/pages/square/detail?sysId=${item.sysId}`\n  })\n}\n\n// 订阅/取消订阅\nconst onSub = async (item) => {\n  const req = {\n    merchantGuid: userStore.merchantGuid,\n    agentGuid: item.guid\n  }\n\n  try {\n    await subscribeAgentApi(req)\n\n    // 更新本地数据\n    const index = searchResults.value.findIndex(agent => agent.guid === item.guid)\n    if (index !== -1) {\n      searchResults.value[index].isSubscribed = !searchResults.value[index].isSubscribed\n    }\n\n    uni.showToast({\n      title: item.isSubscribed ? '取消订阅成功' : '订阅成功',\n      icon: 'none'\n    })\n  } catch (error) {\n    uni.showToast({\n      title: '操作失败',\n      icon: 'none'\n    })\n  }\n}\n\n// 页面加载时处理URL参数\nonLoad((options) => {\n  if (options.agentName) {\n    // 接收到agentName参数，设置搜索关键词并自动搜索\n    agentName.value = options.agentName\n    searchKeyword.value = options.agentName;\n    performSearch()\n  }\n})\n</script>\n\n<style lang=\"scss\" scoped>\n.container {\n  background-color: #ffffff;\n  min-height: 100vh;\n  display: flex;\n  flex-direction: column;\n}\n\n/* 搜索容器 */\n.search-container {\n  display: flex;\n  align-items: center;\n  padding: 20rpx;\n  background-color: #ffffff;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.search-box {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  background-color: #f8f9fa;\n  border-radius: 40rpx;\n  padding: 0 24rpx;\n  height: 80rpx;\n  margin-right: 20rpx;\n}\n\n.search-icon {\n  width: 32rpx;\n  height: 32rpx;\n  margin-right: 16rpx;\n}\n\n.search-input {\n  flex: 1;\n  font-size: 28rpx;\n  color: #333;\n  height: 100%;\n}\n\n.placeholder {\n  color: #999;\n}\n\n.clear-btn {\n  width: 40rpx;\n  height: 40rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background-color: #ccc;\n  border-radius: 50%;\n}\n\n.clear-text {\n  font-size: 24rpx;\n  color: #fff;\n  line-height: 1;\n}\n\n.cancel-btn {\n  padding: 0 10rpx;\n}\n\n.cancel-text {\n  font-size: 28rpx;\n  color: #222222;\n}\n\n/* 内容容器 */\n.content-container {\n  flex: 1;\n  overflow: hidden;\n  background-color: #ffffff;\n}\n\n.scroll-view {\n  height: 100%;\n}\n\n/* 状态容器 */\n.loading-container,\n.empty-container,\n.default-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 120rpx 40rpx;\n}\n\n.loading-text {\n  font-size: 28rpx;\n  color: #999;\n}\n\n.empty-icon,\n.default-icon {\n  width: 120rpx;\n  height: 120rpx;\n  margin-bottom: 24rpx;\n}\n\n.empty-text,\n.default-text {\n  font-size: 32rpx;\n  color: #333;\n  margin-bottom: 12rpx;\n}\n\n.empty-desc {\n  font-size: 24rpx;\n  color: #999;\n}\n\n/* 智能体列表 */\n.agent-list {\n  padding: 0 20px;\n  background-color: #ffffff;\n}\n\n.agent-item {\n  display: flex;\n  align-items: center;\n  padding: 20px 0;\n\n  &:last-child {\n    border-bottom: none;\n  }\n}\n\n.avatar {\n  width: 48px;\n  height: 48px;\n  margin-right: 16px;\n  flex-shrink: 0;\n\n  .avatar-img {\n    width: 100%;\n    height: 100%;\n    border-radius: 24px;\n  }\n}\n\n.content {\n  flex: 1;\n  margin-right: 16px;\n\n  .title {\n    font-size: 32rpx;\n    font-weight: 600;\n    color: #222;\n    margin-bottom: 6px;\n    line-height: 1.3;\n  }\n\n  .description {\n    font-size: 24rpx;\n    color: #333;\n    line-height: 1.4;\n    margin-bottom: 6px;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    display: -webkit-box;\n    -webkit-line-clamp: 2;\n    line-clamp: 2;\n    -webkit-box-orient: vertical;\n  }\n\n  .author {\n    font-size: 24rpx;\n    color: #999999;\n  }\n}\n\n.action-btn {\n  flex-shrink: 0;\n  height: 32px;\n  border-radius: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  min-width: 116rpx;\n  background-color: #007AFF;\n\n  .btn-text {\n    font-size: 28rpx;\n    font-weight: 500;\n    color: #ffffff;\n  }\n\n  &.subscribed {\n    background-color: #F2F2F7;\n    border: none;\n\n    .btn-text {\n      color: #8E8E93;\n    }\n  }\n}\n\n/* 加载更多 */\n.load-more,\n.no-more {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 40rpx;\n}\n\n.load-more-text,\n.no-more-text {\n  font-size: 24rpx;\n  color: #999;\n}\n</style>\n", "import MiniProgramPage from 'E:/youngProject/agent-mini-ui/pages/square/search.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useUserStore", "ref", "uni", "getAgentListApi", "subscribeAgentApi", "onLoad"], "mappings": ";;;;;;;;AAgFA,UAAM,YAAYA,YAAAA,aAAc;AAGhC,UAAM,gBAAgBC,cAAG,IAAC,EAAE;AAC5B,UAAM,gBAAgBA,cAAG,IAAC,EAAE;AAC5B,UAAM,UAAUA,cAAG,IAAC,KAAK;AACzB,UAAM,cAAcA,cAAG,IAAC,KAAK;AAC7B,UAAM,YAAYA,cAAG,IAAC,EAAE;AAGxB,UAAM,cAAcA,cAAG,IAAC,CAAC;AACzB,UAAM,WAAWA,cAAG,IAAC,EAAE;AACvB,UAAM,UAAUA,cAAG,IAAC,IAAI;AACxB,UAAM,cAAcA,cAAG,IAAC,KAAK;AAG7B,UAAM,oBAAoB,MAAM;AAAA,IAEhC;AAIA,UAAM,eAAe,YAAY;AAC/B,UAAI,CAAC,cAAc,MAAM,QAAQ;AAC/BC,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAGD,kBAAY,QAAQ;AACpB,cAAQ,QAAQ;AAChB,oBAAc,QAAQ,CAAE;AAExB,YAAM,cAAe;AAAA,IACvB;AAGA,UAAM,gBAAgB,YAAY;AAChC,UAAI,QAAQ,SAAS,YAAY;AAAO;AAExC,UAAI;AACF,YAAI,YAAY,UAAU,GAAG;AAC3B,kBAAQ,QAAQ;AAAA,QACtB,OAAW;AACL,sBAAY,QAAQ;AAAA,QACrB;AAED,cAAM,MAAM,MAAMC,0BAAgB;AAAA,UAChC,cAAc,UAAU;AAAA,UACxB,WAAW,cAAc,MAAM,KAAM;AAAA,UACrC,UAAU,SAAS;AAAA,UACnB,MAAM,YAAY;AAAA,QACxB,CAAK;AAED,YAAI,IAAI,SAAS,GAAG;AAClB,gBAAM,UAAU,IAAI,KAAK,QAAQ,CAAE;AAEnC,cAAI,YAAY,UAAU,GAAG;AAC3B,0BAAc,QAAQ;AACtB,wBAAY,QAAQ;AAAA,UAE5B,OAAa;AACL,0BAAc,QAAQ,CAAC,GAAG,cAAc,OAAO,GAAG,OAAO;AAAA,UAC1D;AAGD,kBAAQ,QAAQ,YAAY,QAAQ,IAAI,KAAK;AAAA,QAC9C;AAAA,MACF,SAAQ,OAAO;AACdD,sBAAAA,uDAAc,SAAS,KAAK;AAC5BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACL,UAAY;AACR,gBAAQ,QAAQ;AAChB,oBAAY,QAAQ;AAAA,MACrB;AAAA,IACH;AAGA,UAAM,WAAW,YAAY;AAC3B,UAAI,CAAC,QAAQ,SAAS,YAAY;AAAO;AAEzC,kBAAY;AACZ,YAAM,cAAe;AAAA,IACvB;AAGA,UAAM,cAAc,MAAM;AACxB,oBAAc,QAAQ;AACtB,oBAAc,QAAQ,CAAE;AACxB,kBAAY,QAAQ;AACpB,kBAAY,QAAQ;AACpB,cAAQ,QAAQ;AAAA,IAClB;AAGA,UAAM,eAAe,MAAM;AACzBA,oBAAAA,MAAI,aAAc;AAAA,IACpB;AAGA,UAAM,mBAAmB,CAAC,SAAS;AACjCA,oBAAY,MAAA,MAAA,OAAA,kCAAA,UAAU,KAAK,SAAS;AAEpCA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,8BAA8B,KAAK,KAAK;AAAA,MACjD,CAAG;AAAA,IACH;AAGA,UAAM,QAAQ,OAAO,SAAS;AAC5B,YAAM,MAAM;AAAA,QACV,cAAc,UAAU;AAAA,QACxB,WAAW,KAAK;AAAA,MACjB;AAED,UAAI;AACF,cAAME,UAAAA,kBAAkB,GAAG;AAG3B,cAAM,QAAQ,cAAc,MAAM,UAAU,WAAS,MAAM,SAAS,KAAK,IAAI;AAC7E,YAAI,UAAU,IAAI;AAChB,wBAAc,MAAM,KAAK,EAAE,eAAe,CAAC,cAAc,MAAM,KAAK,EAAE;AAAA,QACvE;AAEDF,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,KAAK,eAAe,WAAW;AAAA,UACtC,MAAM;AAAA,QACZ,CAAK;AAAA,MACF,SAAQ,OAAO;AACdA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACF;AAAA,IACH;AAGAG,kBAAM,OAAC,CAAC,YAAY;AAClB,UAAI,QAAQ,WAAW;AAErB,kBAAU,QAAQ,QAAQ;AAC1B,sBAAc,QAAQ,QAAQ;AAC9B,sBAAe;AAAA,MAChB;AAAA,IACH,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrOD,GAAG,WAAW,eAAe;"}