"use strict";const e=require("../../common/vendor.js"),a=require("../../common/assets.js"),t=require("../../api/index.js"),o=require("../../stores/user.js"),i=require("../../api/common.js"),n={__name:"index",setup(n){const s=o.useUserStore(),l=e.reactive({merchantGuid:s.merchantGuid,agentName:"",agentType:1,categoryGuid:"",promptContent:"",agentDesc:"",agentAvatar:"",isPaid:0,price:"",trial_chat_count:0,isPublic:1,agentConfig:{coze_sign:"",secret_token:""},knowledgeBaseIds:[],welcomeMessage:"",commonQuestions:["","",""]}),u=e.ref(!1),r=e.ref(!1),c=e.ref(!1),d=e.ref(!1),g=e.ref(""),v=e.ref(null),m=e.ref([]),p=e.ref(!1),h=e.ref(null),w=e.ref(""),y=e.computed((()=>w.value?m.value.filter((e=>e.categoryName.toLowerCase().includes(w.value.toLowerCase()))):m.value)),f=e.ref([{label:"内部",value:1},{label:"dify",value:2},{label:"coze",value:3}]),T=e.ref([{label:"免费",value:0},{label:"付费",value:1}]),x=()=>{r.value||e.index.chooseImage({count:1,sizeType:["compressed"],sourceType:["album","camera"],success:async e=>{const a=e.tempFilePaths[0];await _(a)},fail:e=>{console.error("选择图片失败:",e)}})},_=async a=>{try{r.value=!0,e.index.showLoading({title:"上传中...",mask:!0});const t=await i.updataFileFun(a),o=JSON.parse(t.data);if(0!==o.code)throw new Error(o.msg||"上传失败");l.agentAvatar=o.data,e.index.showToast({title:"头像上传成功",icon:"success"})}catch(t){console.error("上传头像失败:",t),e.index.showToast({title:"上传失败",icon:"none"})}finally{r.value=!1,e.index.hideLoading()}},A=async()=>{if(l.agentName.trim())if(l.agentDesc.trim()){if(!c.value)try{c.value=!0,e.index.showLoading({title:"AI生成中...",mask:!0});const a={merchantGuid:s.merchantGuid,agentName:l.agentName.trim(),agentDesc:l.agentDesc.trim()},o=await t.generateAvatarApi(a);if(0!==o.code)throw new Error(o.msg||"生成失败");l.agentAvatar=o.data.data.imageUrl,e.index.showToast({title:"头像生成成功",icon:"success"})}catch(a){console.error("AI生成头像失败:",a),e.index.showToast({title:a.message||"生成失败",icon:"none"})}finally{c.value=!1,e.index.hideLoading()}}else e.index.showToast({title:"请先输入智能体描述",icon:"none"});else e.index.showToast({title:"请先输入智能体名称",icon:"none"})},C=async()=>{try{const e=await t.getCategoryListApi({merchantGuid:s.merchantGuid});0===e.code&&(m.value=e.data||[])}catch(e){console.error("获取分类列表失败:",e)}},P=()=>{0===m.value.length&&C(),p.value=!0},b=()=>{p.value=!1,w.value=""},k=()=>{},G=()=>{w.value=""},N=()=>{h.value?(v.value=h.value,l.categoryGuid=h.value.guid,b()):e.index.showToast({title:"请选择分类",icon:"none"})};e.onLoad((a=>{a.guid&&(d.value=!0,(async a=>{try{e.index.showLoading({title:"加载中...",mask:!0});const o=await t.getMyDetailApi({guid:a});if(0!==o.code)throw new Error(o.msg||"获取智能体详情失败");{const e=o.data;l.agentName=e.agentName||"",l.agentType=e.agentType||1,l.categoryGuid=e.categoryGuid||"",l.promptContent=e.promptContent||"",l.agentDesc=e.agentDesc||"",l.agentAvatar=e.agentAvatar||"",l.isPaid=e.isPaid||0,l.price=e.price||"",l.trial_chat_count=e.trialChatCount||0,l.isPublic=e.isPublic||1,l.agentConfig=e.agentConfig||{secret_token:""},l.knowledgeBaseIds=e.knowledgeBaseIds||[],l.welcomeMessage=e.welcomeMessage||"",l.commonQuestions=e.commonQuestions.length>0?e.commonQuestions:["","",""],e.category&&(v.value=e.category),g.value=a}}catch(o){console.error("获取智能体详情失败:",o),e.index.showToast({title:o.message||"获取智能体详情失败",icon:"none"}),setTimeout((()=>{e.index.navigateBack()}),1500)}finally{e.index.hideLoading()}})(a.guid))})),e.onMounted((()=>{C()}));const L=async()=>{if(!u.value)if(l.categoryGuid)if(l.agentName.trim())try{if(u.value=!0,e.index.showLoading({title:d.value?"更新中...":"创建中...",mask:!0}),1===l.isPaid&&!l.price)return e.index.showToast({title:"付费智能体请输入价格",icon:"none"}),u.value=!1,void e.index.hideLoading();const a=l.commonQuestions.filter((e=>e.trim())),o={agentName:l.agentName.trim(),agentType:l.agentType,categoryGuid:l.categoryGuid,promptContent:l.promptContent.trim(),agentDesc:l.agentDesc.trim(),agentAvatar:l.agentAvatar,isPaid:l.isPaid,price:1===l.isPaid?parseFloat(l.price):0,isPublic:1,trial_chat_count:l.trial_chat_count,agentConfig:l.agentConfig,knowledgeBaseIds:l.knowledgeBaseIds,welcomeMessage:l.welcomeMessage.trim(),commonQuestions:a};if(d.value){o.guid=g.value,console.log("更新智能体数据:",o);const a=await t.updateMyAgentApi(o);if(0!==a.code)throw new Error(a.msg||"更新失败");e.index.showToast({title:"更新成功",icon:"success"}),setTimeout((()=>{e.index.navigateBack()}),1500)}else{o.merchantGuid=l.merchantGuid,console.log("创建智能体数据:",o);const a=await t.createAgentApi(o);if(0!==a.code)throw new Error(a.msg||"创建失败");e.index.showToast({title:"创建成功",icon:"success"}),setTimeout((()=>{e.index.navigateBack()}),1500)}}catch(a){console.error(d.value?"更新失败:":"创建失败:",a),e.index.showToast({title:a.msg||(d.value?"更新失败":"创建失败"),icon:"none"})}finally{u.value=!1,e.index.hideLoading()}else e.index.showToast({title:"请输入智能体名称",icon:"none"});else e.index.showToast({title:"请选择分类",icon:"none"})};return(t,o)=>e.e({a:l.agentAvatar,b:!c.value},c.value?{}:{c:a._imports_0$1},{d:c.value},(c.value,{}),{e:e.o(x),f:e.t(c.value?"AI生成中...":"AI生成形象"),g:e.o(A),h:c.value?1:"",i:e.t(v.value?v.value.categoryName:"请选择分类"),j:v.value?"":1,k:e.o(P),l:l.agentName,m:e.o((e=>l.agentName=e.detail.value)),n:l.agentDesc,o:e.o((e=>l.agentDesc=e.detail.value)),p:e.f(f.value,((a,t,o)=>e.e({a:l.agentType===a.value},(l.agentType,a.value,{}),{b:l.agentType===a.value?1:"",c:e.t(a.label),d:a.value,e:e.o((e=>{return t=a.value,void(l.agentType=t);var t}),a.value)}))),q:1===l.agentType},1===l.agentType?{r:l.promptContent,s:e.o((e=>l.promptContent=e.detail.value))}:{},{t:3===l.agentType},3===l.agentType?{v:l.agentConfig.coze_sign,w:e.o((e=>l.agentConfig.coze_sign=e.detail.value))}:{},{x:1!==l.agentType},1!==l.agentType?{y:l.agentConfig.secret_token,z:e.o((e=>l.agentConfig.secret_token=e.detail.value))}:{},{A:l.welcomeMessage,B:e.o((e=>l.welcomeMessage=e.detail.value)),C:e.f(l.commonQuestions,((a,t,o)=>({a:`请输入引导问题${t+1}`,b:l.commonQuestions[t],c:e.o((e=>l.commonQuestions[t]=e.detail.value),t),d:t}))),D:e.f(T.value,((a,t,o)=>e.e({a:l.isPaid===a.value},(l.isPaid,a.value,{}),{b:l.isPaid===a.value?1:"",c:e.t(a.label),d:a.value,e:e.o((e=>{return t=a.value,void(l.isPaid=t);var t}),a.value)}))),E:1===l.isPaid},1===l.isPaid?{F:l.price,G:e.o((e=>l.price=e.detail.value))}:{},{H:1===l.isPaid},1===l.isPaid?{I:l.trial_chat_count,J:e.o((e=>l.trial_chat_count=e.detail.value))}:{},{K:e.t(u.value?d.value?"更新中...":"创建中...":d.value?"完成":"创建AI智能体"),L:e.o(L),M:u.value?1:"",N:p.value},p.value?e.e({O:a._imports_1$6,P:e.o([e=>w.value=e.detail.value,k]),Q:w.value,R:w.value},w.value?{S:a._imports_2$5,T:e.o(G)}:{},{U:e.f(y.value,((a,t,o)=>{var i,n,s;return e.e({a:(null==(i=h.value)?void 0:i.guid)===a.guid},(null==(n=h.value)||n.guid,a.guid,{}),{b:(null==(s=h.value)?void 0:s.guid)===a.guid?1:"",c:e.t(a.categoryName),d:a.guid,e:e.o((e=>(e=>{h.value=e})(a)),a.guid)})})),V:0===y.value.length},(y.value.length,{}),{W:e.o(b),X:e.o(N),Y:h.value?"":1,Z:e.o((()=>{})),aa:e.o(b)}):{})}},s=e._export_sfc(n,[["__scopeId","data-v-a2eb353b"]]);wx.createPage(s);
