<template>
  <view class="withdraw-record-page">
    <!-- 记录列表 -->
    <view class="record-list">
      <view class="record-item" v-for="(item, index) in recordList" :key="index">
        <view class="record-content">
          <view class="amount-section">
            <text class="amount">{{ item.amount }}</text>
            <text class="status" :class="getStatusClass(item.status)">{{ item.statusText }}</text>
          </view>
          <view class="time-section">
            <text class="time-label">提现时间：</text>
            <text class="time-value">{{ item.withdrawTime }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" v-if="recordList.length === 0">
      <image src="@/static/common/empty_icon.png" class="empty-icon" mode="aspectFit" />
      <text class="empty-text">暂无提现记录</text>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'
import { onLoad } from '@dcloudio/uni-app'

// 提现记录列表
const recordList = ref([
  // {
  //   amount: '-2000.00',
  //   status: 'success',
  //   statusText: '成功',
  //   withdrawTime: '2025-05-18 12:56:00'
  // },
  // {
  //   amount: '-2000.00',
  //   status: 'failed',
  //   statusText: '打款失败，已退回余额',
  //   withdrawTime: '2025-05-18 12:56:00'
  // },
  // {
  //   amount: '-2000.00',
  //   status: 'processing',
  //   statusText: '打款中',
  //   withdrawTime: '2025-05-18 12:56:00'
  // }
])

// 获取状态样式类
const getStatusClass = (status) => {
  switch (status) {
    case 'success':
      return 'status-success'
    case 'failed':
      return 'status-failed'
    case 'processing':
      return 'status-processing'
    default:
      return ''
  }
}



// 加载提现记录列表
const loadRecordList = async () => {
  try {
    // 这里调用API获取提现记录
    // const res = await getWithdrawRecordApi()
    // recordList.value = res.data
    console.log('加载提现记录')
  } catch (error) {
    console.error('加载提现记录失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    })
  }
}

onLoad(() => {
  loadRecordList()
})
</script>

<style lang="scss" scoped>
.withdraw-record-page {
  background: #f5f5f5;
  min-height: 100vh;

  .record-list {
    padding: 32rpx;

    .record-item {
      background: #ffffff;
      border-radius: 16rpx;
      margin-bottom: 16rpx;
      overflow: hidden;

      .record-content {
        padding: 40rpx 32rpx;

        .amount-section {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 20rpx;

          .amount {
            font-size: 40rpx;
            font-weight: 600;
            color: #3478f6;
          }

          .status {
            font-size: 28rpx;
            font-weight: 500;

            &.status-success {
              color: #52c41a;
            }

            &.status-failed {
              color: #ff4d4f;
            }

            &.status-processing {
              color: #52c41a;
            }
          }
        }

        .time-section {
          display: flex;
          align-items: center;

          .time-label {
            font-size: 28rpx;
            color: #999999;
          }

          .time-value {
            font-size: 28rpx;
            color: #999999;
            margin-left: 8rpx;
          }
        }
      }
    }
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 200rpx 0;

    .empty-icon {
      width: 200rpx;
      height: 200rpx;
      margin-bottom: 40rpx;
      opacity: 0.6;
    }

    .empty-text {
      font-size: 28rpx;
      color: #999999;
    }
  }
}
</style>