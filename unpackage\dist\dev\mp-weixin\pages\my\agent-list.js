"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const api_index = require("../../api/index.js");
const stores_user = require("../../stores/user.js");
if (!Array) {
  const _easycom_z_paging2 = common_vendor.resolveComponent("z-paging");
  _easycom_z_paging2();
}
const _easycom_z_paging = () => "../../uni_modules/z-paging/components/z-paging/z-paging.js";
if (!Math) {
  _easycom_z_paging();
}
const _sfc_main = {
  __name: "agent-list",
  setup(__props) {
    const userStore = stores_user.useUserStore();
    const agentList = common_vendor.ref([]);
    const paging = common_vendor.ref(null);
    const agentTypes = common_vendor.ref([
      { label: "内部", value: 1 },
      { label: "dify", value: 2 },
      { label: "coze", value: 3 },
      { label: "阿里云百炼", value: 4 }
    ]);
    const auditStatus = {
      1: "待审核",
      2: "审核通过",
      3: "审核拒绝"
    };
    const handleDeleteAgent = async (agent) => {
      common_vendor.index.showModal({
        title: "提示",
        content: "确定要删除该智能体吗？",
        success: async (res) => {
          if (res.confirm) {
            await api_index.deleteAgentApi({
              merchantGuid: userStore.merchantGuid,
              agentGuid: agent.guid
            });
            common_vendor.index.showToast({
              title: "删除成功",
              icon: "success"
            });
            getMyAgentList();
          }
        }
      });
    };
    const queryList = async (page, pageSize) => {
      try {
        let res = await api_index.getMyAgentListApi({
          merchantGuid: userStore.merchantGuid,
          page,
          pageSize
        });
        paging.value.complete(res.data.data || []);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/my/agent-list.vue:96", "获取智能体列表失败:", error);
        common_vendor.index.showToast({
          title: "加载失败",
          icon: "none"
        });
        paging.value.complete(false);
      }
    };
    const handleCreateAgent = () => {
      common_vendor.index.navigateTo({
        url: "/pages/create-agent/index"
      });
    };
    const handleEditAgent = (agent) => {
      common_vendor.index.navigateTo({
        url: `/pages/create-agent/index?guid=${agent.guid}`
      });
    };
    return (_ctx, _cache) => {
      return {
        a: common_vendor.f(agentList.value, (agent, index, i0) => {
          return common_vendor.e({
            a: agent.agentAvatar,
            b: common_vendor.t(agent.agentName),
            c: common_vendor.t(agent.agentDesc),
            d: common_vendor.t(agentTypes.value.find((item) => item.value === agent.agentType).label),
            e: common_vendor.t(agent.priceText),
            f: common_vendor.t(agent.isPublicText),
            g: common_vendor.o(($event) => handleEditAgent(agent), index),
            h: common_vendor.o(($event) => handleDeleteAgent(agent), index),
            i: agent.auditStatus != 2
          }, agent.auditStatus != 2 ? {
            j: common_vendor.t(auditStatus[agent.auditStatus])
          } : {}, {
            k: agent.auditStatus === 2
          }, agent.auditStatus === 2 ? {
            l: common_vendor.t(auditStatus[agent.auditStatus])
          } : {}, {
            m: index
          });
        }),
        b: common_vendor.sr(paging, "e7ab54ac-0", {
          "k": "paging"
        }),
        c: common_vendor.o(queryList),
        d: common_vendor.o(($event) => agentList.value = $event),
        e: common_vendor.p({
          auto: true,
          ["auto-clean-list-when-reload"]: false,
          modelValue: agentList.value
        }),
        f: common_assets._imports_0$1,
        g: common_vendor.o(handleCreateAgent)
      };
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-e7ab54ac"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/my/agent-list.js.map
