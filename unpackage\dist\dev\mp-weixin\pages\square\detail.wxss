/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.agent-detail-page.data-v-efb7de88 {
  background: #f5f5f5;
  min-height: 100vh;
  padding: 60rpx 32rpx;
}
.agent-detail-page .agent-info.data-v-efb7de88 {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 60rpx 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}
.agent-detail-page .agent-info .avatar-section.data-v-efb7de88 {
  position: relative;
  margin-bottom: 40rpx;
}
.agent-detail-page .agent-info .avatar-section .agent-avatar.data-v-efb7de88 {
  width: 200rpx;
  height: 200rpx;
  border-radius: 50%;
  background: #f0f0f0;
}
.agent-detail-page .agent-info .avatar-section .edit-icon.data-v-efb7de88 {
  position: absolute;
  right: 0;
  top: 0;
  width: 60rpx;
  height: 60rpx;
  background: #3478f6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.agent-detail-page .agent-info .avatar-section .edit-icon .edit-img.data-v-efb7de88 {
  width: 32rpx;
  height: 32rpx;
}
.agent-detail-page .agent-info .avatar-section .nick-name.data-v-efb7de88 {
  font-size: 26rpx;
  color: #666666;
  text-align: center;
  margin-top: 10px;
}
.agent-detail-page .agent-info .info-section.data-v-efb7de88 {
  margin-bottom: 60rpx;
}
.agent-detail-page .agent-info .info-section .agent-name.data-v-efb7de88 {
  font-size: 48rpx;
  font-weight: 600;
  color: #1a1a1a;
  display: block;
  margin-bottom: 24rpx;
}
.agent-detail-page .agent-info .info-section .agent-desc.data-v-efb7de88 {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.6;
  display: block;
}
.agent-detail-page .agent-info .action-buttons.data-v-efb7de88 {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.agent-detail-page .agent-info .action-buttons .subscribe-btn.data-v-efb7de88 {
  width: 400rpx;
  height: 90rpx;
  background: #3478f6;
  border-radius: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24rpx;
}
.agent-detail-page .agent-info .action-buttons .subscribe-btn .btn-text.data-v-efb7de88 {
  font-size: 30rpx;
  color: #ffffff;
  font-weight: 600;
}
.agent-detail-page .agent-info .action-buttons .subscribe-btn.subscribed.data-v-efb7de88 {
  background-color: #F2F2F7;
  border: none;
}
.agent-detail-page .agent-info .action-buttons .subscribe-btn.subscribed .btn-text.data-v-efb7de88 {
  color: #8E8E93;
}
.agent-detail-page .agent-info .action-buttons .chat-btn.data-v-efb7de88 {
  width: 400rpx;
  height: 90rpx;
  background: #40a266;
  border-radius: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24rpx;
}
.agent-detail-page .agent-info .action-buttons .chat-btn .btn-text.data-v-efb7de88 {
  font-size: 30rpx;
  color: #ffffff;
  font-weight: 600;
}
.agent-detail-page .agent-info .action-buttons .share-btn.data-v-efb7de88 {
  width: 400rpx;
  height: 90rpx;
  background: #3478f6;
  border-radius: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 32rpx;
}
.agent-detail-page .agent-info .action-buttons .share-btn .btn-text.data-v-efb7de88 {
  font-size: 30rpx;
  color: #ffffff;
  font-weight: 600;
}
.agent-detail-page .agent-info .action-buttons .promotion-link.data-v-efb7de88 {
  display: flex;
  justify-content: center;
}
.agent-detail-page .agent-info .action-buttons .promotion-link .link-text.data-v-efb7de88 {
  font-size: 28rpx;
  color: #3478f6;
  text-decoration: underline;
}
.agent-detail-page .loading-state.data-v-efb7de88 {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}
.agent-detail-page .loading-state .loading-text.data-v-efb7de88 {
  font-size: 28rpx;
  color: #999999;
}
.agent-detail-page .empty-state.data-v-efb7de88 {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}
.agent-detail-page .empty-state .empty-text.data-v-efb7de88 {
  font-size: 28rpx;
  color: #999999;
}