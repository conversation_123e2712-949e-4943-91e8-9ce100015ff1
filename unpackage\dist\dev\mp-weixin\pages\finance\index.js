"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  __name: "index",
  setup(__props) {
    const recordList = common_vendor.ref([
      // {
      //   amount: '+2000.00',
      //   type: 'income', // income: 收入, expense: 支出
      //   reason: '用户订阅购买了您的智能体"我的修仙女友"',
      //   timeLabel: '',
      //   time: '',
      //   afterBalance: '2000'
      // },
      // {
      //   amount: '-2000.00',
      //   type: 'expense',
      //   reason: '用户订阅购买了您的智能体"我的修仙女友"',
      //   timeLabel: '变动时间',
      //   time: '2025-05-18 12:56:00',
      //   afterBalance: '0'
      // }
    ]);
    const getAmountClass = (type) => {
      return type === "income" ? "amount-income" : "amount-expense";
    };
    const loadRecordList = async () => {
      try {
        common_vendor.index.__f__("log", "at pages/finance/index.vue:71", "加载财务流水");
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/finance/index.vue:73", "加载财务流水失败:", error);
        common_vendor.index.showToast({
          title: "加载失败",
          icon: "none"
        });
      }
    };
    common_vendor.onLoad(() => {
      loadRecordList();
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.f(recordList.value, (item, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(item.amount),
            b: common_vendor.n(getAmountClass(item.type)),
            c: common_vendor.t(item.reason),
            d: item.time
          }, item.time ? {
            e: common_vendor.t(item.timeLabel),
            f: common_vendor.t(item.time)
          } : {}, {
            g: common_vendor.t(item.afterBalance),
            h: index
          });
        }),
        b: recordList.value.length === 0
      }, recordList.value.length === 0 ? {
        c: common_assets._imports_0$10
      } : {});
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-b0c916dd"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/finance/index.js.map
