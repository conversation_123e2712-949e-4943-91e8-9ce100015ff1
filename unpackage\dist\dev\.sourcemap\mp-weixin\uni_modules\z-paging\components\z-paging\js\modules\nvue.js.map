{"version": 3, "file": "nvue.js", "sources": ["uni_modules/z-paging/components/z-paging/js/modules/nvue.js"], "sourcesContent": ["// [z-paging]nvue独有部分模块\r\nimport u from '.././z-paging-utils'\r\nimport c from '.././z-paging-constant'\r\nimport Enum from '.././z-paging-enum'\r\n\r\n// #ifdef APP-NVUE\r\nconst weexAnimation = weex.requireModule('animation');\r\n// #endif\r\nexport default {\r\n\tprops: {\r\n\t\t// #ifdef APP-NVUE\r\n\t\t// nvue中修改列表类型，可选值有list、waterfall和scroller，默认为list\r\n\t\tnvueListIs: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: u.gc('nvueListIs', 'list')\r\n\t\t},\r\n\t\t// nvue waterfall配置，仅在nvue中且nvueListIs=waterfall时有效，配置参数详情参见：https://uniapp.dcloud.io/component/waterfall\r\n\t\tnvueWaterfallConfig: {\r\n\t\t\ttype: Object,\r\n\t\t\tdefault: u.gc('nvueWaterfallConfig', {})\r\n\t\t},\r\n\t\t// nvue 控制是否回弹效果，iOS不支持动态修改\r\n\t\tnvueBounce: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('nvueBounce', true)\r\n\t\t},\r\n\t\t// nvue中通过代码滚动到顶部/底部时，是否加快动画效果(无滚动动画时无效)，默认为否\r\n\t\tnvueFastScroll: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('nvueFastScroll', false)\r\n\t\t},\r\n\t\t// nvue中list的id\r\n\t\tnvueListId: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: u.gc('nvueListId', '')\r\n\t\t},\r\n\t\t// nvue中refresh组件的样式\r\n\t\tnvueRefresherStyle: {\r\n\t\t\ttype: Object,\r\n\t\t\tdefault: u.gc('nvueRefresherStyle', {})\r\n\t\t},\r\n\t\t// nvue中是否按分页模式(类似竖向swiper)显示List，默认为false\r\n\t\tnvuePagingEnabled: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('nvuePagingEnabled', false)\r\n\t\t},\r\n\t\t// 是否隐藏nvue列表底部的tagView，此view用于标识滚动到底部位置，若隐藏则滚动到底部功能将失效，在nvue中实现吸顶+swiper功能时需将最外层z-paging的此属性设置为true。默认为否\r\n\t\thideNvueBottomTag: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('hideNvueBottomTag', false)\r\n\t\t},\r\n\t\t// nvue中控制onscroll事件触发的频率：表示两次onscroll事件之间列表至少滚动了10px。注意，将该值设置为较小的数值会提高滚动事件采样的精度，但同时也会降低页面的性能\r\n\t\toffsetAccuracy: {\r\n\t\t\ttype: Number,\r\n\t\t\tdefault: u.gc('offsetAccuracy', 10)\r\n\t\t},\r\n\t\t// #endif\r\n\t},\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tnRefresherLoading: false,\r\n\t\t\tnListIsDragging: false,\r\n\t\t\tnShowBottom: true,\r\n\t\t\tnFixFreezing: false,\r\n\t\t\tnShowRefresherReveal: false,\r\n\t\t\tnLoadingMoreFixedHeight: false,\r\n\t\t\tnShowRefresherRevealHeight: 0,\r\n\t\t\tnOldShowRefresherRevealHeight: -1,\r\n\t\t\tnRefresherWidth: uni.upx2px(750),\r\n\t\t\tnF2Opacity: 0\r\n\t\t}\r\n\t},\r\n\tcomputed: {\r\n\t\t// #ifdef APP-NVUE\r\n\t\tnScopedSlots() {\r\n\t\t\t// #ifdef VUE2\r\n\t\t\treturn this.$scopedSlots;\r\n\t\t\t// #endif\r\n\t\t\t// #ifdef VUE3\r\n\t\t\treturn null;\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\tnWaterfallColumnCount() {\r\n\t\t\tif (this.finalNvueListIs !== 'waterfall') return 0;\r\n\t\t\treturn this._nGetWaterfallConfig('column-count', 2);\r\n\t\t},\r\n\t\tnWaterfallColumnWidth() {\r\n\t\t\treturn this._nGetWaterfallConfig('column-width', 'auto');\r\n\t\t},\r\n\t\tnWaterfallColumnGap() {\r\n\t\t\treturn this._nGetWaterfallConfig('column-gap', 'normal');\r\n\t\t},\r\n\t\tnWaterfallLeftGap() {\r\n\t\t\treturn this._nGetWaterfallConfig('left-gap', 0);\r\n\t\t},\r\n\t\tnWaterfallRightGap() {\r\n\t\t\treturn this._nGetWaterfallConfig('right-gap', 0);\r\n\t\t},\r\n\t\tnViewIs() {\r\n\t\t\tconst is = this.finalNvueListIs;\r\n\t\t\treturn is === 'scroller' || is === 'view' ? 'view' : is === 'waterfall' ? 'header' : 'cell';\r\n\t\t},\r\n\t\tnSafeAreaBottomHeight() {\r\n\t\t\treturn this.safeAreaInsetBottom ? this.safeAreaBottom : 0;\r\n\t\t},\r\n\t\tfinalNvueListIs() {\r\n\t\t\tif (this.usePageScroll) return 'view';\r\n\t\t\tconst nvueListIsLowerCase = this.nvueListIs.toLowerCase();\r\n\t\t\tif (['list','waterfall','scroller'].indexOf(nvueListIsLowerCase) !== -1) return nvueListIsLowerCase;\r\n\t\t\treturn 'list';\r\n\t\t},\r\n\t\tfinalNvueSuperListIs() {\r\n\t\t\treturn this.usePageScroll ? 'view' : 'scroller';\r\n\t\t},\r\n\t\tfinalNvueRefresherEnabled() {\r\n\t\t\treturn this.finalNvueListIs !== 'view' && this.finalRefresherEnabled && !this.nShowRefresherReveal && !this.useChatRecordMode;\r\n\t\t},\r\n\t\t// #endif\r\n\t},\r\n\tmounted(){\r\n\t\t// #ifdef APP-NVUE\r\n\t\t//旋转屏幕时更新宽度\r\n\t\tuni.onWindowResize((res) => {\r\n\t\t\t// this._nUpdateRefresherWidth();\r\n\t\t})\r\n\t\t// #endif\r\n\t},\r\n\tmethods: {\r\n\t\t// #ifdef APP-NVUE\r\n\t\t// 列表滚动时触发\r\n\t\t_nOnScroll(e) {\r\n\t\t\tthis.$emit('scroll', e);\r\n\t\t\tconst contentOffsetY = -e.contentOffset.y;\r\n\t\t\tthis.oldScrollTop = contentOffsetY;\r\n\t\t\tthis.nListIsDragging = e.isDragging;\r\n\t\t\tthis._checkShouldShowBackToTop(contentOffsetY, contentOffsetY - 1);\r\n\t\t},\r\n\t\t// 列表滚动结束\r\n\t\t_nOnScrollend(e) {\r\n\t\t\tthis.$emit('scrollend', e);\r\n\t\t},\r\n\t\t// 下拉刷新刷新中\r\n\t\t_nOnRrefresh() {\r\n\t\t\tif (this.nShowRefresherReveal) return;\r\n\t\t\t// 进入刷新状态\r\n\t\t\tthis.nRefresherLoading = true;\r\n\t\t\tif (this.refresherStatus === Enum.Refresher.GoF2) {\r\n\t\t\t\tthis._handleGoF2();\r\n\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\tthis._nRefresherEnd();\r\n\t\t\t\t})\r\n\t\t\t} else {\r\n\t\t\t\tthis.refresherStatus = Enum.Refresher.Loading;\r\n\t\t\t\tthis._doRefresherLoad();\r\n\t\t\t}\r\n\t\t\t\r\n\t\t},\r\n\t\t// 下拉刷新下拉中\r\n\t\t_nOnPullingdown(e) {\r\n\t\t\tif (this.refresherStatus === Enum.Refresher.Loading || (this.isIos && !this.nListIsDragging)) return;\r\n\t\t\tthis._emitTouchmove(e);\r\n\t\t\tlet { viewHeight, pullingDistance } = e;\r\n\t\t\t// 更新下拉刷新状态\r\n\t\t\t// 下拉刷新距离超过阈值\r\n\t\t\tif (pullingDistance >= viewHeight) {\r\n\t\t\t\t// 如果开启了下拉进入二楼并且下拉刷新距离超过进入二楼阈值，则当前下拉刷新状态为松手进入二楼，否则为松手立即刷新\r\n\t\t\t\t// (pullingDistance - viewHeight) + this.finalRefresherThreshold 不等同于pullingDistance，此处是为了兼容不同平台下拉相同距离pullingDistance不一致的问题，pullingDistance仅与viewHeight互相关联\r\n\t\t\t\tthis.refresherStatus = this.refresherF2Enabled && (pullingDistance - viewHeight) + this.finalRefresherThreshold >= this.finalRefresherF2Threshold ? Enum.Refresher.GoF2 : Enum.Refresher.ReleaseToRefresh;\r\n\t\t\t} else {\r\n\t\t\t\t// 下拉刷新距离未超过阈值，显示默认状态\r\n\t\t\t\tthis.refresherStatus = Enum.Refresher.Default;\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 下拉刷新结束\r\n\t\t_nRefresherEnd(doEnd = true) {\r\n\t\t\tif (doEnd) {\r\n\t\t\t   this._nDoRefresherEndAnimation(0, -this.nShowRefresherRevealHeight); \r\n\t\t\t   !this.usePageScroll && this.$refs['zp-n-list'].resetLoadmore();\r\n\t\t\t   this.nRefresherLoading = false;\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 执行主动触发下拉刷新动画\r\n\t\t_nDoRefresherEndAnimation(height, translateY, animate = true, checkStack = true) {\r\n\t\t\t// 清除下拉刷新相关timeout\r\n\t\t\tthis._cleanRefresherCompleteTimeout();\r\n\t\t\tthis._cleanRefresherEndTimeout();\r\n\t\t\t\r\n\t\t\tif (!this.finalShowRefresherWhenReload) {\r\n\t\t\t\t// 如果reload不需要自动展示下拉刷新view，则在complete duration结束后再把下拉刷新状态设置回默认\r\n\t\t\t\tthis.refresherEndTimeout = u.delay(() => {\r\n\t\t\t\t\tthis.refresherStatus = Enum.Refresher.Default;\r\n\t\t\t\t}, this.refresherCompleteDuration);\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t// 用户处理用户在短时间内多次调用reload的情况，此时下拉刷新view不需要重复显示，只需要保证最后一次reload对应的请求结束后收回下拉刷新view即可\r\n\t\t\tconst stackCount = this.refresherRevealStackCount;\r\n\t\t\tif (height === 0 && checkStack) {\r\n\t\t\t\tthis.refresherRevealStackCount --;\r\n\t\t\t\tif (stackCount > 1) return;\r\n\t\t\t\tthis.refresherEndTimeout = u.delay(() => {\r\n\t\t\t\t\tthis.refresherStatus = Enum.Refresher.Default;\r\n\t\t\t\t}, this.refresherCompleteDuration);\r\n\t\t\t}\r\n\t\t\tif (stackCount > 1) {\r\n\t\t\t\tthis.refresherStatus = Enum.Refresher.Loading;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tconst duration = animate ? 200 : 0;\r\n\t\t\tif (this.nOldShowRefresherRevealHeight !== height) {\r\n\t\t\t\tif (height > 0) {\r\n\t\t\t\t\tthis.nShowRefresherReveal = true;\r\n\t\t\t\t}\r\n\t\t\t\t// 展示下拉刷新view\r\n\t\t\t\tweexAnimation.transition(this.$refs['zp-n-list-refresher-reveal'], {\r\n\t\t\t\t\tstyles: {\r\n\t\t\t\t\t\theight: `${height}px`,\r\n\t\t\t\t\t\ttransform: `translateY(${translateY}px)`,\r\n\t\t\t\t\t},\r\n\t\t\t\t\tduration,\r\n\t\t\t\t\ttimingFunction: 'linear',\r\n\t\t\t\t\tneedLayout: true,\r\n\t\t\t\t\tdelay: 0\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t\tu.delay(() => {\r\n\t\t\t\tif (animate) {\r\n\t\t\t\t\tthis.nShowRefresherReveal = height > 0;\r\n\t\t\t\t}\r\n\t\t\t}, duration > 0 ? duration - 60 : 0);\r\n\t\t\tthis.nOldShowRefresherRevealHeight = height;\r\n\t\t},\r\n\t\t// 滚动到底部加载更多\r\n\t\t_nOnLoadmore() {\r\n\t\t\tif (this.nShowRefresherReveal || !this.totalData.length) return;\r\n\t\t\tthis.useChatRecordMode ? this.doChatRecordLoadMore() : this._onLoadingMore('toBottom');\r\n\t\t},\r\n\t\t// 获取nvue waterfall单项配置\r\n\t\t_nGetWaterfallConfig(key, defaultValue) {\r\n\t\t\treturn this.nvueWaterfallConfig[key] || defaultValue;\r\n\t\t},\r\n\t\t// 更新nvue 下拉刷新view容器的宽度\r\n\t\t_nUpdateRefresherWidth() {\r\n\t\t\tu.delay(() => {\r\n\t\t\t\tthis.$nextTick(()=>{\r\n\t\t\t\t\tthis._getNodeClientRect('.zp-n-list').then(node => {\r\n\t\t\t\t\t\tif (node) {\r\n\t\t\t\t\t\t\tthis.nRefresherWidth = node[0].width || this.nRefresherWidth;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t})\t\r\n\t\t}\r\n\t\t// #endif\r\n\t}\r\n}\r\n"], "names": ["uni"], "mappings": ";;AAQA,MAAe,aAAA;AAAA,EACd,OAAO,CAgDN;AAAA,EACD,OAAO;AACN,WAAO;AAAA,MACN,mBAAmB;AAAA,MACnB,iBAAiB;AAAA,MACjB,aAAa;AAAA,MACb,cAAc;AAAA,MACd,sBAAsB;AAAA,MACtB,yBAAyB;AAAA,MACzB,4BAA4B;AAAA,MAC5B,+BAA+B;AAAA,MAC/B,iBAAiBA,cAAAA,MAAI,OAAO,GAAG;AAAA,MAC/B,YAAY;AAAA,IACZ;AAAA,EACD;AAAA,EACD,UAAU,CA8CT;AAAA,EACD,UAAS;AAAA,EAOR;AAAA,EACD,SAAS,CA8HR;AACF;;"}