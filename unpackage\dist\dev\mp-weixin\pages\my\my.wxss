/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.vip-card-warp.data-v-2f1ef635 {
  background-color: #ffffff;
  padding-bottom: 20px;
  margin-bottom: 32rpx;
}
.vip-card.data-v-2f1ef635 {
  width: 690rpx;
  height: 220rpx;
  margin: 0 auto 0 auto;
  background: linear-gradient(90deg, #F9F0E3 0%, #F7DFC1 100%);
  border-radius: 25rpx;
  border: 1rpx solid #F4E5C1;
  display: flex;
  flex-direction: column;
}
.vip-card .info-box.data-v-2f1ef635 {
  display: flex;
  flex-direction: column;
  position: relative;
  flex: 1;
  justify-content: center;
  padding-left: 20px;
}
.vip-card .info-box .title.data-v-2f1ef635 {
  font-size: 30rpx;
  color: #54371A;
  margin-bottom: 10px;
}
.vip-card .info-box .title .day.data-v-2f1ef635 {
  font-size: 22rpx;
}
.vip-card .info-box .label.data-v-2f1ef635 {
  font-size: 22rpx;
  color: #9E876A;
}
.vip-card .info-box .icon-box.data-v-2f1ef635 {
  position: absolute;
  right: 20px;
  top: -26px;
}
.vip-card .info-box .icon-box .icon.data-v-2f1ef635 {
  width: 133rpx;
  height: 133rpx;
  display: block;
}
.vip-card .info-box .icon-box .text.data-v-2f1ef635 {
  width: 136rpx;
  height: 50rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 26rpx;
  color: #FDF0E0;
  background-color: #543718;
  position: absolute;
  right: 0;
  bottom: 0;
  border-radius: 33rpx;
}
.vip-card .dy-box.data-v-2f1ef635 {
  height: 70rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30rpx;
  color: #DECAAF;
  background: linear-gradient(90deg, #281905 0%, #533818 100%);
  border-bottom-right-radius: 25rpx;
  border-bottom-left-radius: 25rpx;
}
.vip-card .dy-box .left.data-v-2f1ef635 {
  font-size: 22rpx;
  display: flex;
  align-items: center;
}
.vip-card .dy-box .left .icon.data-v-2f1ef635 {
  display: block;
  width: 30rpx;
  height: 30rpx;
  margin-right: 6rpx;
}
.vip-card .dy-box .right.data-v-2f1ef635 {
  font-size: 25rpx;
  display: flex;
  align-items: center;
}
.vip-card .dy-box .right .icon.data-v-2f1ef635 {
  display: block;
  width: 25rpx;
  height: 25rpx;
  margin-left: 6rpx;
}
.modal-overlay.data-v-2f1ef635 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  overflow: hidden;
  touch-action: none;
}
.modal-overlay .modal-content.data-v-2f1ef635 {
  background: #ffffff;
  width: 90%;
  height: 600px;
  border-radius: 24rpx;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
.modal-overlay .modal-content .modal-header.data-v-2f1ef635 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1px solid #F0F0F0;
  flex-shrink: 0;
}
.modal-overlay .modal-content .modal-header .modal-title.data-v-2f1ef635 {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
}
.modal-overlay .modal-content .modal-header .close-btn.data-v-2f1ef635 {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #F5F5F5;
}
.modal-overlay .modal-content .modal-header .close-btn .close-text.data-v-2f1ef635 {
  font-size: 36rpx;
  color: #666666;
  line-height: 1;
}
.modal-overlay .modal-content .modal-body.data-v-2f1ef635 {
  flex: 1;
  height: 0;
  overflow: hidden;
}
.modal-overlay .modal-content .modal-body .rich-content.data-v-2f1ef635 {
  font-size: 28rpx;
  line-height: 1.6;
  color: #1a1a1a;
  word-break: break-all;
  padding: 32rpx;
}
.profile-page.data-v-2f1ef635 {
  background: #F5F5F5;
  min-height: 100vh;
}
.profile-page .header.data-v-2f1ef635 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 60rpx 32rpx 40rpx;
  background: #ffffff;
}
.profile-page .header .user-info.data-v-2f1ef635 {
  display: flex;
  align-items: center;
}
.profile-page .header .user-info .avatar.data-v-2f1ef635 {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
}
.profile-page .header .user-info .info.data-v-2f1ef635 {
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin-left: 10px;
}
.profile-page .header .user-info .info .name.data-v-2f1ef635 {
  font-size: 30rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 8rpx;
  display: block;
}
.profile-page .header .user-info .info .email.data-v-2f1ef635 {
  font-size: 28rpx;
  color: #999999;
  display: block;
}
.profile-page .header .edit-btn.data-v-2f1ef635 {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #F5F5F5;
  border-radius: 32rpx;
  border: none;
  width: 174rpx;
  height: 60rpx;
}
.profile-page .header .edit-btn .edit-text.data-v-2f1ef635 {
  font-size: 28rpx;
  color: #333;
  font-weight: 400;
}
.profile-page .tab-container.data-v-2f1ef635 {
  background: #ffffff;
  border-bottom: 1px solid #F0F0F0;
}
.profile-page .tab-container .tab-bar.data-v-2f1ef635 {
  display: flex;
  padding: 0 32rpx;
}
.profile-page .tab-container .tab-bar .tab.data-v-2f1ef635 {
  flex: 1;
  text-align: center;
  padding: 32rpx 0;
  position: relative;
}
.profile-page .tab-container .tab-bar .tab .tab-text.data-v-2f1ef635 {
  font-size: 32rpx;
  color: #999999;
  font-weight: 400;
  transition: all 0.3s ease;
}
.profile-page .tab-container .tab-bar .tab.active .tab-text.data-v-2f1ef635 {
  color: #1a1a1a;
  font-weight: 600;
}
.profile-page .tab-container .tab-bar .tab.active.data-v-2f1ef635::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 6rpx;
  background: #3478f6;
  border-radius: 3rpx;
}
.profile-page .tab-content.data-v-2f1ef635 {
  min-height: calc(100vh - 400rpx);
}
.profile-page .agents-tab.data-v-2f1ef635 {
  padding: 32rpx;
}
.profile-page .agents-tab .agents-list .agent-card.data-v-2f1ef635 {
  display: flex;
  align-items: center;
  background: #fff;
  border-radius: 24rpx;
  margin-bottom: 24rpx;
  padding: 32rpx;
  position: relative;
}
.profile-page .agents-tab .agents-list .agent-card .agent-avatar.data-v-2f1ef635 {
  width: 96rpx;
  height: 96rpx;
  border-radius: 50%;
  margin-right: 24rpx;
  flex-shrink: 0;
}
.profile-page .agents-tab .agents-list .agent-card .agent-info.data-v-2f1ef635 {
  flex: 1;
}
.profile-page .agents-tab .agents-list .agent-card .agent-info .agent-title.data-v-2f1ef635 {
  font-size: 32rpx;
  font-weight: 600;
  color: #222;
  margin-bottom: 8rpx;
}
.profile-page .agents-tab .agents-list .agent-card .agent-info .agent-desc.data-v-2f1ef635 {
  font-size: 24rpx;
  color: #999;
  line-height: 1.5;
  margin-bottom: 16rpx;
}
.profile-page .agents-tab .agents-list .agent-card .agent-info .agent-tags.data-v-2f1ef635 {
  display: flex;
  gap: 12rpx;
  flex-wrap: wrap;
  margin-top: 16rpx;
}
.profile-page .agents-tab .agents-list .agent-card .agent-info .agent-tags .tag.data-v-2f1ef635 {
  border-radius: 16rpx;
  padding: 8rpx 16rpx;
  font-size: 22rpx;
  font-weight: 500;
}
.profile-page .agents-tab .agents-list .agent-card .agent-info .agent-tags .tag.primary.data-v-2f1ef635 {
  background: #E6F0FF;
  color: #3478f6;
}
.profile-page .agents-tab .agents-list .agent-card .agent-info .agent-tags .tag.warning.data-v-2f1ef635 {
  background: #FFF7E6;
  color: #FF8C00;
}
.profile-page .agents-tab .agents-list .agent-card .agent-info .agent-tags .tag.success.data-v-2f1ef635 {
  background: #F0F9E6;
  color: #52C41A;
}
.profile-page .agents-tab .agents-list .agent-card .operate-box.data-v-2f1ef635 {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.profile-page .agents-tab .agents-list .agent-card .operate-box .edit.data-v-2f1ef635 {
  width: 40px;
  height: 24px;
  font-size: 24rpx;
  background-color: #3478f6;
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 16px;
}
.profile-page .agents-tab .agents-list .agent-card .operate-box .delete.data-v-2f1ef635 {
  width: 40px;
  height: 24px;
  font-size: 24rpx;
  background-color: #e60000;
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 16px;
}
.profile-page .agents-tab .agents-list .agent-card .status.data-v-2f1ef635 {
  width: 140rpx;
  height: 40rpx;
  position: absolute;
  right: 0;
  top: 0;
  border-radius: 0rpx 30rpx 0rpx 30rpx;
  background-color: rgba(253, 141, 43, 0.12);
  font-size: 20rpx;
  color: #FD8D2B;
  text-align: center;
  line-height: 40rpx;
}
.profile-page .agents-tab .agents-list .agent-card .status.success.data-v-2f1ef635 {
  background-color: #3478f6;
  color: #fff;
}
.profile-page .agents-tab .view-more-btn.data-v-2f1ef635 {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 24rpx;
  padding: 24rpx;
  margin: 24rpx 0;
}
.profile-page .agents-tab .view-more-btn .view-more-text.data-v-2f1ef635 {
  font-size: 28rpx;
  color: #3478f6;
  margin-right: 8rpx;
}
.profile-page .agents-tab .view-more-btn .arrow-icon.data-v-2f1ef635 {
  width: 24rpx;
  height: 24rpx;
}
.profile-page .agents-tab .create-agent.data-v-2f1ef635 {
  margin-bottom: 30rpx;
  display: flex;
  justify-content: center;
  width: 100%;
  bottom: 40px;
  left: 0;
}
.profile-page .agents-tab .create-agent .create-btn.data-v-2f1ef635 {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 90rpx;
  background: #3478f6;
  border-radius: 48rpx;
  border: none;
}
.profile-page .agents-tab .create-agent .create-btn .create-icon.data-v-2f1ef635 {
  width: 32rpx;
  height: 32rpx;
  margin-right: 12rpx;
}
.profile-page .agents-tab .create-agent .create-btn .create-text.data-v-2f1ef635 {
  font-size: 32rpx;
  color: #ffffff;
  line-height: 1;
}
.profile-page .income-tab.data-v-2f1ef635 {
  padding: 32rpx;
}
.profile-page .income-tab .income-content .section-title.data-v-2f1ef635 {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 24rpx;
  display: block;
}
.profile-page .income-tab .income-content .rules-section.data-v-2f1ef635 {
  margin-bottom: 40rpx;
  background-color: #fff;
  padding: 20rpx 10rpx;
  border: 30rpx;
}
.profile-page .income-tab .income-content .rules-section .rules-grid.data-v-2f1ef635 {
  display: flex;
  gap: 40rpx;
  justify-content: space-around;
  padding: 0 10rpx;
}
.profile-page .income-tab .income-content .rules-section .rules-grid .rule-item.data-v-2f1ef635 {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  padding: 10px 0;
  background-color: #F9FAFB;
  border: 30rpx;
}
.profile-page .income-tab .income-content .rules-section .rules-grid .rule-item .rule-icon.data-v-2f1ef635 {
  width: 64rpx;
  height: 64rpx;
  margin-bottom: 16rpx;
}
.profile-page .income-tab .income-content .rules-section .rules-grid .rule-item .rule-text.data-v-2f1ef635 {
  font-size: 26rpx;
  color: #666666;
}
.profile-page .income-tab .income-content .my-income-section.data-v-2f1ef635 {
  margin-bottom: 40rpx;
  background-color: #fff;
  padding: 20rpx 10rpx;
  border: 30rpx;
}
.profile-page .income-tab .income-content .my-income-section .income-card.data-v-2f1ef635 {
  background: #F8F9FA;
  border-radius: 24rpx;
  padding: 32rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 0 10px;
}
.profile-page .income-tab .income-content .my-income-section .income-card .income-amount .amount-value.data-v-2f1ef635 {
  font-size: 48rpx;
  font-weight: 700;
  color: #1a1a1a;
  display: block;
  margin-bottom: 8rpx;
}
.profile-page .income-tab .income-content .my-income-section .income-card .income-amount .amount-unit.data-v-2f1ef635 {
  font-size: 26rpx;
  color: #666666;
  display: block;
}
.profile-page .income-tab .income-content .my-income-section .income-card .withdraw-btn.data-v-2f1ef635 {
  background: #3478f6;
  border-radius: 32rpx;
  padding: 20rpx 40rpx;
  border: none;
}
.profile-page .income-tab .income-content .my-income-section .income-card .withdraw-btn .withdraw-text.data-v-2f1ef635 {
  font-size: 28rpx;
  color: #ffffff;
  font-weight: 600;
}
.profile-page .income-tab .income-content .overview-section.data-v-2f1ef635 {
  margin-bottom: 40rpx;
  background-color: #fff;
  padding: 20rpx 10rpx;
  border: 30rpx;
}
.profile-page .income-tab .income-content .overview-section .overview-grid.data-v-2f1ef635 {
  display: flex;
  gap: 24rpx;
  margin: 0 10px;
}
.profile-page .income-tab .income-content .overview-section .overview-grid .overview-item.data-v-2f1ef635 {
  flex: 1;
  background: #F8F9FA;
  border-radius: 24rpx;
  padding: 32rpx 24rpx;
  text-align: center;
}
.profile-page .income-tab .income-content .overview-section .overview-grid .overview-item .overview-value.data-v-2f1ef635 {
  font-size: 36rpx;
  font-weight: 700;
  color: #1a1a1a;
  display: block;
  margin-bottom: 8rpx;
}
.profile-page .income-tab .income-content .overview-section .overview-grid .overview-item .overview-label.data-v-2f1ef635 {
  font-size: 24rpx;
  color: #666666;
  display: block;
}
.profile-page .income-tab .income-content .flow-section.data-v-2f1ef635 {
  background-color: #fff;
  padding: 20rpx 10rpx;
  border: 30rpx;
}
.profile-page .income-tab .income-content .flow-section .flow-buttons.data-v-2f1ef635 {
  display: flex;
  gap: 24rpx;
  margin: 0 10px;
}
.profile-page .income-tab .income-content .flow-section .flow-buttons .flow-btn.data-v-2f1ef635 {
  flex: 1;
  background: #F8F9FA;
  border-radius: 24rpx;
  padding: 32rpx 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
}
.profile-page .income-tab .income-content .flow-section .flow-buttons .flow-btn .flow-icon.data-v-2f1ef635 {
  width: 48rpx;
  height: 48rpx;
  margin-right: 12rpx;
}
.profile-page .income-tab .income-content .flow-section .flow-buttons .flow-btn .flow-text.data-v-2f1ef635 {
  font-size: 26rpx;
  color: #666666;
}
.profile-page .workspace-tab.data-v-2f1ef635 {
  background: #F5F5F5;
  padding: 20rpx 30rpx;
}
.profile-page .workspace-tab .workspace-tab-container .workspace-tab-bar.data-v-2f1ef635 {
  display: flex;
  gap: 16rpx;
}
.profile-page .workspace-tab .workspace-tab-container .workspace-tab-bar .workspace-tab-item .tab-button.data-v-2f1ef635 {
  padding: 16rpx 32rpx;
  border-radius: 40rpx;
  background: #ffffff;
  transition: all 0.3s ease;
  line-height: 1;
}
.profile-page .workspace-tab .workspace-tab-container .workspace-tab-bar .workspace-tab-item .tab-button .workspace-tab-text.data-v-2f1ef635 {
  font-size: 26rpx;
  color: #999999;
  font-weight: 400;
  transition: all 0.3s ease;
}
.profile-page .workspace-tab .workspace-tab-container .workspace-tab-bar .workspace-tab-item.active .tab-button.data-v-2f1ef635 {
  background: #2B64F6;
}
.profile-page .workspace-tab .workspace-tab-container .workspace-tab-bar .workspace-tab-item.active .tab-button .workspace-tab-text.data-v-2f1ef635 {
  color: #ffffff;
  font-weight: 500;
}
.profile-page .workspace-tab .workspace-content.data-v-2f1ef635 {
  padding-top: 20rpx;
}
.profile-page .workspace-tab .workspace-content .favorites-content .favorite-card.data-v-2f1ef635 {
  background: #fff;
  border-radius: 24rpx;
  margin-bottom: 24rpx;
  padding: 32rpx;
}
.profile-page .workspace-tab .workspace-content .favorites-content .favorite-card .favorite-header.data-v-2f1ef635 {
  margin-bottom: 16rpx;
}
.profile-page .workspace-tab .workspace-content .favorites-content .favorite-card .favorite-header .favorite-date.data-v-2f1ef635 {
  font-size: 24rpx;
  color: #999999;
  display: block;
}
.profile-page .workspace-tab .workspace-content .favorites-content .favorite-card .favorite-content.data-v-2f1ef635 {
  margin-bottom: 24rpx;
}
.profile-page .workspace-tab .workspace-content .favorites-content .favorite-card .favorite-content .content-text.data-v-2f1ef635 {
  font-size: 28rpx;
  color: #1a1a1a;
  line-height: 1.6;
  display: block;
}
.profile-page .workspace-tab .workspace-content .favorites-content .favorite-card .favorite-actions.data-v-2f1ef635 {
  display: flex;
  gap: 16rpx;
  flex-wrap: wrap;
}
.profile-page .workspace-tab .workspace-content .favorites-content .favorite-card .favorite-actions .action-btn.data-v-2f1ef635 {
  display: flex;
  align-items: center;
  background: #E6F0FF;
  border-radius: 20rpx;
  padding: 12rpx 20rpx;
  border: none;
  flex-shrink: 0;
  min-width: 0;
}
.profile-page .workspace-tab .workspace-content .favorites-content .favorite-card .favorite-actions .action-btn .action-icon.data-v-2f1ef635 {
  width: 38rpx;
  height: 38rpx;
  margin-right: 8rpx;
  flex-shrink: 0;
}
.profile-page .workspace-tab .workspace-content .favorites-content .favorite-card .favorite-actions .action-btn .action-text.data-v-2f1ef635 {
  font-size: 24rpx;
  color: #222222;
  font-weight: 500;
  white-space: nowrap;
}
.profile-page .workspace-tab .workspace-content .favorites-content .view-more-btn.data-v-2f1ef635 {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 24rpx;
  padding: 24rpx;
  margin: 24rpx 0;
}
.profile-page .workspace-tab .workspace-content .favorites-content .view-more-btn .view-more-text.data-v-2f1ef635 {
  font-size: 28rpx;
  color: #3478f6;
  margin-right: 8rpx;
}
.profile-page .workspace-tab .workspace-content .favorites-content .view-more-btn .arrow-icon.data-v-2f1ef635 {
  width: 24rpx;
  height: 24rpx;
}
.profile-page .workspace-tab .workspace-content .favorites-content .empty-state.data-v-2f1ef635 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 200rpx 0;
}
.profile-page .workspace-tab .workspace-content .favorites-content .empty-state .empty-text.data-v-2f1ef635 {
  font-size: 28rpx;
  color: #999999;
}
.profile-page .digital-person-content .digital-person-banner.data-v-2f1ef635 {
  margin-bottom: 24rpx;
  overflow: hidden;
  position: relative;
}
.profile-page .digital-person-content .digital-person-banner.bottom-banner.data-v-2f1ef635 {
  margin-top: 0;
  margin-bottom: 0;
}
.profile-page .digital-person-content .digital-person-banner .banner-content .banner-image.data-v-2f1ef635 {
  width: 690rpx;
  height: 230rpx;
  display: block;
}
.profile-page .digital-person-content .digital-person-banner .banner-content .banner-image-radius.data-v-2f1ef635 {
  border-radius: 30rpx;
}
.profile-page .digital-person-content .function-area .function-row.data-v-2f1ef635 {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.profile-page .digital-person-content .function-area .function-row .function-item.data-v-2f1ef635 {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 20rpx 0 20rpx 30rpx;
  height: 160rpx;
  width: 330rpx;
  display: flex;
  align-items: center;
  position: relative;
  box-sizing: border-box;
}
.profile-page .digital-person-content .function-area .function-row .function-item .function-icon.data-v-2f1ef635 {
  position: absolute;
  right: 0;
  top: 20rpx;
  width: 120rpx;
  height: 120rpx;
}
.profile-page .digital-person-content .function-area .function-row .function-item .function-info.data-v-2f1ef635 {
  position: relative;
  z-index: 2;
}
.profile-page .digital-person-content .function-area .function-row .function-item .function-info .function-title.data-v-2f1ef635 {
  display: block;
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}
.profile-page .digital-person-content .function-area .function-row .function-item .function-info .function-desc.data-v-2f1ef635 {
  display: block;
  font-size: 24rpx;
  font-weight: 400;
  color: #5380F2;
}
.profile-page .digital-person-content .computing-power.data-v-2f1ef635 {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}
.profile-page .digital-person-content .computing-power .power-info.data-v-2f1ef635 {
  display: flex;
  align-items: center;
}
.profile-page .digital-person-content .computing-power .power-info .power-icon.data-v-2f1ef635 {
  width: 48rpx;
  height: 48rpx;
  margin-right: 16rpx;
}
.profile-page .digital-person-content .computing-power .power-info .power-text.data-v-2f1ef635 {
  font-size: 32rpx;
  color: #333333;
  font-weight: 400;
}
.profile-page .digital-person-content .computing-power .power-info .power-text .power-text_number.data-v-2f1ef635 {
  color: #2A64F6;
  font-weight: 500;
  font-size: 34rpx;
}
.profile-page .digital-person-content .computing-power .recharge-btn.data-v-2f1ef635 {
  background: #2A64F6;
  border-radius: 50rpx;
  width: 160rpx;
  height: 60rpx;
  line-height: 60rpx;
  font-size: 26rpx;
  color: #ffffff;
  text-align: center;
}
.profile-page .recharge-overlay.data-v-2f1ef635 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}
.profile-page .recharge-modal.data-v-2f1ef635 {
  background: linear-gradient(180deg, #EAF0FF 2%, #FFFFFF 20%);
  border-radius: 20rpx;
  padding: 75rpx 40rpx 60rpx;
  width: 560rpx;
  max-width: 90vw;
  position: relative;
}
.profile-page .recharge-modal .modal-header.data-v-2f1ef635 {
  text-align: center;
  margin-bottom: 58rpx;
}
.profile-page .recharge-modal .modal-header .modal-title.data-v-2f1ef635 {
  font-size: 35rpx;
  font-weight: 500;
  color: #5380F2;
}
.profile-page .recharge-modal .recharge-grid.data-v-2f1ef635 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 28rpx;
  margin-bottom: 60rpx;
}
.profile-page .recharge-modal .recharge-grid .recharge-item.data-v-2f1ef635 {
  background: #ffffff;
  border: 3rpx solid #E0E0E0;
  border-radius: 16rpx;
  padding: 32rpx 20rpx;
  text-align: center;
  transition: all 0.3s ease;
}
.profile-page .recharge-modal .recharge-grid .recharge-item.active.data-v-2f1ef635 {
  border-color: #2B64F6;
  background: #E6EEFC;
}
.profile-page .recharge-modal .recharge-grid .recharge-item.active .item-count.data-v-2f1ef635 {
  color: #2A64F6;
}
.profile-page .recharge-modal .recharge-grid .recharge-item .item-count.data-v-2f1ef635 {
  display: block;
  font-size: 35rpx;
  font-weight: 500;
  color: #656565;
  margin-bottom: 8rpx;
}
.profile-page .recharge-modal .recharge-grid .recharge-item .item-price.data-v-2f1ef635 {
  display: block;
  font-size: 20rpx;
  color: #FA5151;
  font-weight: 400;
}
.profile-page .recharge-modal .no-data.data-v-2f1ef635 {
  text-align: center;
  padding: 80rpx 0;
}
.profile-page .recharge-modal .no-data .no-data-text.data-v-2f1ef635 {
  color: #999999;
  font-size: 28rpx;
}
.profile-page .recharge-modal .payment-section.data-v-2f1ef635 {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
  font-weight: 500;
}
.profile-page .recharge-modal .payment-section .payment-text.data-v-2f1ef635 {
  font-size: 32rpx;
  color: #333333;
}
.profile-page .recharge-modal .payment-section .payment-price.data-v-2f1ef635 {
  font-size: 36rpx;
  font-weight: 700;
  color: #FA5151;
}
.profile-page .recharge-modal .pay-button.data-v-2f1ef635 {
  background: #2B64F6;
  border-radius: 48rpx;
  padding: 32rpx;
  text-align: center;
}
.profile-page .recharge-modal .pay-button .pay-text.data-v-2f1ef635 {
  font-size: 32rpx;
  color: #ffffff;
  font-weight: 500;
}
.profile-page .recharge-modal .close-icon-wrapper.data-v-2f1ef635 {
  position: absolute;
  bottom: -80rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 45rpx;
  height: 45rpx;
  border-radius: 50%;
}
.profile-page .recharge-modal .close-icon-wrapper .close-icon.data-v-2f1ef635 {
  width: 45rpx;
  height: 45rpx;
}