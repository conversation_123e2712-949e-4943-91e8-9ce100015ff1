{"version": 3, "file": "square.js", "sources": ["pages/square/square.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvc3F1YXJlL3NxdWFyZS52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"container\">\r\n    <!-- 顶部标签栏 -->\r\n    <view class=\"tabs-container\">\r\n      <uv-tabs :list=\"tabsList\" :current=\"currentTab\" @change=\"handleTabChange\" lineColor=\"#222222\"\r\n        keyName=\"categoryName\">\r\n        <template v-slot:right>\r\n          <view class=\"search-icon\" @tap=\"handleSearch\">\r\n            <image src=\"@/static/square/<EMAIL>\" class=\"icon\" mode=\"aspectFit\"></image>\r\n          </view>\r\n        </template>\r\n      </uv-tabs>\r\n    </view>\r\n\r\n    <!-- 列表内容 -->\r\n    <view class=\"content-container\" :style=\"{ height: containerHeight }\">\r\n      <swiper class=\"tab-swiper\" :current=\"currentTab\" :indicator-dots=\"false\" :autoplay=\"false\" :circular=\"false\"\r\n        @change=\"handleSwiperChange\" :style=\"{ height: containerHeight }\">\r\n        <!-- 为每个分类创建一个swiper-item -->\r\n        <swiper-item v-for=\"(tab, tabIndex) in tabsList\" :key=\"tab.guid\" :style=\"{ height: containerHeight }\">\r\n          <scroll-view scroll-y=\"true\" class=\"scroll-view\" :style=\"{ height: containerHeight }\">\r\n            <view class=\"agent-list\">\r\n              <view v-for=\"item in categoryAgentData[tabIndex] || []\" :key=\"item.guid\" class=\"agent-item\">\r\n                <!-- 头像 -->\r\n                <view class=\"avatar\" @click=\"handleAgentClick(item)\">\r\n                  <image :src=\"item.agentAvatar\" class=\"avatar-img\" mode=\"aspectFill\"></image>\r\n                </view>\r\n\r\n                <!-- 内容区域 -->\r\n                <view class=\"content\" @click=\"handleAgentClick(item)\">\r\n                  <view class=\"title\">{{ item.agentName }}</view>\r\n                  <view class=\"description\">{{ item.agentDesc }}</view>\r\n                  <view class=\"author\">@{{ item.creator.nickname }}</view>\r\n                </view>\r\n\r\n                <!-- 右侧按钮 -->\r\n                <view class=\"action-btn\" :class=\"{ subscribed: item.isSubscribed }\" @click=\"onSub(item)\">\r\n                  <text class=\"btn-text\">{{ item.isSubscribed ? '已合伙' : '去招募' }}</text>\r\n                </view>\r\n              </view>\r\n            </view>\r\n            <view class=\"empty-container\" v-if=\"categoryAgentData[tabIndex]?.length === 0\">\r\n              暂无智能体\r\n            </view>\r\n          </scroll-view>\r\n\r\n        </swiper-item>\r\n      </swiper>\r\n    </view>\r\n\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, watch, nextTick } from 'vue'\r\nimport {\r\n  onShow,\r\n  onLoad,\r\n  onShareAppMessage,\r\n  onShareTimeline\r\n} from '@dcloudio/uni-app'\r\nimport { useUserStore } from '@/stores/user.js'\r\nimport { getCategoryListApi, getAgentListApi, subscribeAgentApi, bindInvitationApi } from '@/api/index.js'\r\n\r\nconst userStore = useUserStore()\r\n// 当前选中的标签\r\nconst currentTab = ref(0)\r\n// 标签列表\r\nconst tabsList = ref([])\r\n// 动态计算的容器高度\r\nconst containerHeight = ref('calc(100vh - 88rpx)')\r\n\r\n// 初始化容器高度\r\nconst initContainerHeight = () => {\r\n  nextTick(() => {\r\n    uni.getSystemInfo({\r\n      success: (res) => {\r\n        // 获取系统信息\r\n        const windowHeight = res.windowHeight\r\n\r\n        // 计算可用高度，减去tabs高度(大约44px)\r\n        const availableHeight = windowHeight - 44\r\n\r\n        // 设置最小高度为600px，确保Android兼容性\r\n        const finalHeight = Math.max(availableHeight, 600)\r\n\r\n        containerHeight.value = `${finalHeight}px`\r\n\r\n        console.log('Container height set to:', containerHeight.value)\r\n      },\r\n      fail: () => {\r\n        // 失败时使用默认值\r\n        containerHeight.value = '600px'\r\n      }\r\n    })\r\n  })\r\n}\r\n\r\nconst getCategoryList = async () => {\r\n  let res = await getCategoryListApi({\r\n    merchantGuid: userStore.merchantGuid\r\n  })\r\n  tabsList.value = res.data;\r\n  // 初始化categoryAgentData数组\r\n  categoryAgentData.value = new Array(tabsList.value.length)\r\n\r\n  // 检查是否需要切换到指定分类\r\n  if (userStore.targetCategoryGuid) {\r\n    switchToCategory(userStore.targetCategoryGuid)\r\n    userStore.clear_target_category()\r\n  } else {\r\n    // 只加载第一个分类的数据\r\n    getAgentList()\r\n  }\r\n}\r\n// 按分类存储的数据，数组索引对应tabsList的索引\r\nconst categoryAgentData = ref([])\r\n\r\nconst getAgentList = async (categoryIndex = null) => {\r\n  if (tabsList.value.length > 0) {\r\n    const targetIndex = categoryIndex !== null ? categoryIndex : currentTab.value\r\n    let guid = tabsList.value[targetIndex].guid\r\n    let res = await getAgentListApi({\r\n      merchantGuid: userStore.merchantGuid,\r\n      categoryGuid: guid,\r\n      pageSize: 100\r\n    })\r\n    console.log('categoryAgentData before:', categoryAgentData.value)\r\n    // 确保数组有足够的长度\r\n    if (categoryAgentData.value.length <= targetIndex) {\r\n      categoryAgentData.value = [...categoryAgentData.value, ...new Array(targetIndex + 1 - categoryAgentData.value.length)]\r\n    }\r\n    categoryAgentData.value[targetIndex] = res.data.data;\r\n    console.log('categoryAgentData after:', categoryAgentData.value)\r\n    console.log('categoryAgentData length:', categoryAgentData.value[targetIndex]?.length)\r\n  }\r\n}\r\n\r\n\r\n\r\n// 标签切换\r\nconst handleTabChange = (event) => {\r\n  console.log('handleTabChange', event)\r\n  currentTab.value = event.index\r\n  // console.log('切换到标签:', tabsList.value[event.index]?.categoryName)\r\n  // 如果该分类数据还没有加载，则加载\r\n  if (!categoryAgentData.value[event.index]) {\r\n    getAgentList(event.index)\r\n  }\r\n}\r\n\r\n// swiper切换处理\r\nconst handleSwiperChange = (e) => {\r\n  currentTab.value = e.detail.current\r\n  // console.log('swiper切换到:', tabsList.value[e.detail.current]?.categoryName)\r\n  // 如果该分类数据还没有加载，则加载\r\n  if (!categoryAgentData.value[e.detail.current]) {\r\n    getAgentList(e.detail.current)\r\n  }\r\n}\r\n\r\n// 搜索功能\r\nconst handleSearch = () => {\r\n  console.log('点击搜索')\r\n  uni.navigateTo({\r\n    url: '/pages/square/search'\r\n  })\r\n}\r\n\r\n// 点击智能体\r\nconst handleAgentClick = (item) => {\r\n  console.log('点击智能体:', item.agentName)\r\n  // 跳转到详情页\r\n  uni.navigateTo({\r\n    url: `/pages/square/detail?sysId=${item.sysId}`\r\n  })\r\n}\r\nconst onSub = async (item) => {\r\n  if (item.isSubscribed) {\r\n    uni.navigateTo({\r\n      url: `/pages/msg/index?sessionGuid=${item.guid}`\r\n    })\r\n    return\r\n  } else {\r\n    let req = {\r\n      merchantGuid: userStore.merchantGuid,\r\n      agentGuid: item.guid\r\n    }\r\n    try {\r\n      await subscribeAgentApi(req)\r\n      getAgentList(currentTab.value)\r\n      uni.showToast({\r\n        title: '招募成功',\r\n        icon: 'none'\r\n      })\r\n    } catch (error) {\r\n      uni.showToast({\r\n        title: '招募失败',\r\n        icon: 'none'\r\n      })\r\n    }\r\n  }\r\n\r\n\r\n}\r\nonShareAppMessage(() => {\r\n  return {\r\n    title: userStore.appName || '智能体',\r\n    path: `/pages/square/square?invite=${userStore.invitationCode}`,\r\n    success(res) {\r\n      console.log('userStore.invitationCode', userStore.invitationCode)\r\n      uni.showToast({\r\n        title: '分享成功'\r\n      })\r\n    },\r\n    fail(res) {\r\n      uni.showToast({\r\n        title: '分享失败',\r\n        icon: 'none'\r\n      })\r\n    }\r\n  }\r\n})\r\n// 分享到朋友圈功能\r\n// onShareTimeline(() => {\r\n//   return {\r\n//     title: userStore.appName || '智能体',\r\n//     path: `/pages/square/square?invite=${userStore.invitationCode}`,\r\n//     success(res) {\r\n//       uni.showToast({\r\n//         title: '分享成功'\r\n//       })\r\n//     },\r\n//     fail(res) {\r\n//       uni.showToast({\r\n//         title: '分享失败',\r\n//         icon: 'none'\r\n//       })\r\n//     }\r\n//   }\r\n// })\r\n\r\n\r\n\r\n// 根据分类guid查找对应的索引\r\nconst findCategoryIndex = (categoryGuid) => {\r\n  return tabsList.value.findIndex(item => item.guid === categoryGuid)\r\n}\r\n\r\n// 切换到指定分类\r\nconst switchToCategory = (categoryGuid) => {\r\n  const index = findCategoryIndex(categoryGuid)\r\n  if (index !== -1) {\r\n    currentTab.value = index\r\n    // 如果该分类数据还没有加载，则加载\r\n    if (!categoryAgentData.value[index]) {\r\n      getAgentList(index)\r\n    }\r\n  }\r\n}\r\n\r\n// 页面显示时检查是否需要切换分类\r\nonShow(() => {\r\n  // 初始化容器高度\r\n  initContainerHeight()\r\n  if (userStore.targetCategoryGuid) {\r\n    console.log(tabsList.value, 'tabsList.valuetabsList.value')\r\n    // 如果分类列表已加载，直接切换\r\n    if (tabsList.value.length > 0) {\r\n      switchToCategory(userStore.targetCategoryGuid)\r\n      userStore.clear_target_category()\r\n    } else {\r\n      getCategoryList()\r\n    }\r\n  } else {\r\n    getCategoryList()\r\n  }\r\n})\r\n// let invite = ref('')\r\n// const bindInvitation = async () => {\r\n//   try {\r\n//     console.log('invite.value', invite.value)\r\n//     await bindInvitationApi({\r\n//       merchantGuid: userStore.merchantGuid,\r\n//       invitationCode: invite.value\r\n//     })\r\n//   } catch (error) {\r\n//     console.error('绑定邀请码失败:', error)\r\n//   }\r\n// }\r\n\r\n// onLoad((params) => {\r\n//   if (params.invite) {\r\n//     invite.value = params.invite;\r\n//     if (userStore.userToken) {\r\n//       bindInvitation()\r\n//     }\r\n//   }\r\n// });\r\nwatch(\r\n  () => userStore.userToken,\r\n  (newValue, oldValue) => {\r\n    console.log('userToken changed', newValue, oldValue)\r\n    if (newValue && oldValue === '') {\r\n      getCategoryList()\r\n      // bindInvitation()\r\n    }\r\n  }\r\n);\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.container {\r\n  background-color: #ffffff;\r\n  min-height: 100vh;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.empty-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  padding: 120rpx 0;\r\n  color: #999999;\r\n  font-size: 28rpx;\r\n}\r\n\r\n/* 顶部标签栏 */\r\n.tabs-container {\r\n  background-color: #ffffff;\r\n  padding: 0;\r\n\r\n  .search-icon {\r\n    padding: 0 20px;\r\n    display: flex;\r\n    align-items: center;\r\n\r\n    .icon {\r\n      width: 60rpx;\r\n      height: 60rpx;\r\n      display: block;\r\n    }\r\n  }\r\n}\r\n\r\n/* 内容容器 */\r\n.content-container {\r\n  flex: 1;\r\n  overflow: hidden;\r\n  background-color: #ffffff;\r\n  /* 默认高度，会被JavaScript动态设置覆盖 */\r\n  height: calc(100vh - 88rpx);\r\n  /* 为Android机型添加最小高度保障 */\r\n  min-height: 600px;\r\n}\r\n\r\n/* tab切换容器 */\r\n.tab-swiper {\r\n  width: 100%;\r\n  /* 默认高度，会被JavaScript动态设置覆盖 */\r\n  height: calc(100vh - 88rpx);\r\n  /* 为Android机型添加最小高度保障 */\r\n  min-height: 600px;\r\n}\r\n\r\n.scroll-view {\r\n  /* 默认高度，会被JavaScript动态设置覆盖 */\r\n  height: calc(100vh - 88rpx);\r\n  min-height: 600px;\r\n  width: 100%;\r\n}\r\n\r\n/* 确保swiper-item高度正确 */\r\nswiper-item {\r\n  /* 默认高度，会被JavaScript动态设置覆盖 */\r\n  height: calc(100vh - 88rpx);\r\n  min-height: 600px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  overflow: hidden;\r\n}\r\n\r\n/* 智能体列表 */\r\n.agent-list {\r\n  padding: 0 20px;\r\n  background-color: #ffffff;\r\n}\r\n\r\n.agent-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 20px 0;\r\n\r\n  &:last-child {\r\n    border-bottom: none;\r\n  }\r\n}\r\n\r\n.avatar {\r\n  width: 48px;\r\n  height: 48px;\r\n  margin-right: 16px;\r\n  flex-shrink: 0;\r\n\r\n  .avatar-img {\r\n    width: 100%;\r\n    height: 100%;\r\n    border-radius: 24px;\r\n  }\r\n}\r\n\r\n.content {\r\n  flex: 1;\r\n  margin-right: 16px;\r\n\r\n  .title {\r\n    font-size: 32rpx;\r\n    font-weight: 600;\r\n    color: #222;\r\n    margin-bottom: 6px;\r\n    line-height: 1.3;\r\n  }\r\n\r\n  .description {\r\n    font-size: 24rpx;\r\n    color: #333;\r\n    line-height: 1.4;\r\n    margin-bottom: 6px;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    display: -webkit-box;\r\n    -webkit-line-clamp: 2;\r\n    line-clamp: 2;\r\n    -webkit-box-orient: vertical;\r\n  }\r\n\r\n  .author {\r\n    font-size: 24rpx;\r\n    color: #999999;\r\n  }\r\n}\r\n\r\n.action-btn {\r\n  flex-shrink: 0;\r\n  height: 32px;\r\n  // padding: 0 20px;\r\n  border-radius: 20px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  min-width: 116rpx;\r\n  background-color: #007AFF;\r\n\r\n  .btn-text {\r\n    font-size: 28rpx;\r\n    font-weight: 500;\r\n    color: #ffffff;\r\n  }\r\n\r\n  // 可订阅状态 - 蓝色按钮\r\n  // &.available {\r\n\r\n\r\n  //   .btn-text {\r\n  //     color: #ffffff;\r\n  //   }\r\n  // }\r\n\r\n  // 已订阅状态 - 灰色按钮\r\n  &.subscribed {\r\n    background-color: #F2F2F7;\r\n    border: none;\r\n\r\n    .btn-text {\r\n      color: #8E8E93;\r\n    }\r\n  }\r\n\r\n  // 已激活状态 - 蓝色文字按钮\r\n  &.activated {\r\n    background-color: #E7EDFA;\r\n\r\n    .btn-text {\r\n      color: #2A64F6;\r\n    }\r\n  }\r\n}\r\n</style>", "import MiniProgramPage from 'E:/youngProject/agent-mini-ui/pages/square/square.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useUserStore", "ref", "nextTick", "uni", "getCategoryListApi", "getAgentListApi", "subscribeAgentApi", "onShareAppMessage", "onShow", "watch"], "mappings": ";;;;;;;;;;;;;;;;AAgEA,UAAA,YAAAA,YAAAA,aAAA;AAEA,UAAA,aAAAC,cAAA,IAAA,CAAA;AAEA,UAAA,WAAAA,cAAA,IAAA,EAAA;AAEA,UAAA,kBAAAA,cAAA,IAAA,qBAAA;AAGA,UAAA,sBAAA,MAAA;AACAC,oBAAAA,WAAA,MAAA;AACAC,sBAAAA,MAAA,cAAA;AAAA,UACA,SAAA,CAAA,QAAA;AAEA,kBAAA,eAAA,IAAA;AAGA,kBAAA,kBAAA,eAAA;AAGA,kBAAA,cAAA,KAAA,IAAA,iBAAA,GAAA;AAEA,4BAAA,QAAA,GAAA,WAAA;AAEAA,0BAAA,MAAA,MAAA,OAAA,iCAAA,4BAAA,gBAAA,KAAA;AAAA,UACA;AAAA,UACA,MAAA,MAAA;AAEA,4BAAA,QAAA;AAAA,UACA;AAAA,QACA,CAAA;AAAA,MACA,CAAA;AAAA,IACA;AAEA,UAAA,kBAAA,YAAA;AACA,UAAA,MAAA,MAAAC,6BAAA;AAAA,QACA,cAAA,UAAA;AAAA,MACA,CAAA;AACA,eAAA,QAAA,IAAA;AAEA,wBAAA,QAAA,IAAA,MAAA,SAAA,MAAA,MAAA;AAGA,UAAA,UAAA,oBAAA;AACA,yBAAA,UAAA,kBAAA;AACA,kBAAA,sBAAA;AAAA,MACA,OAAA;AAEA,qBAAA;AAAA,MACA;AAAA,IACA;AAEA,UAAA,oBAAAH,cAAA,IAAA,EAAA;AAEA,UAAA,eAAA,OAAA,gBAAA,SAAA;;AACA,UAAA,SAAA,MAAA,SAAA,GAAA;AACA,cAAA,cAAA,kBAAA,OAAA,gBAAA,WAAA;AACA,YAAA,OAAA,SAAA,MAAA,WAAA,EAAA;AACA,YAAA,MAAA,MAAAI,0BAAA;AAAA,UACA,cAAA,UAAA;AAAA,UACA,cAAA;AAAA,UACA,UAAA;AAAA,QACA,CAAA;AACAF,sBAAA,MAAA,MAAA,OAAA,kCAAA,6BAAA,kBAAA,KAAA;AAEA,YAAA,kBAAA,MAAA,UAAA,aAAA;AACA,4BAAA,QAAA,CAAA,GAAA,kBAAA,OAAA,GAAA,IAAA,MAAA,cAAA,IAAA,kBAAA,MAAA,MAAA,CAAA;AAAA,QACA;AACA,0BAAA,MAAA,WAAA,IAAA,IAAA,KAAA;AACAA,sBAAA,MAAA,MAAA,OAAA,kCAAA,4BAAA,kBAAA,KAAA;AACAA,4BAAA,MAAA,OAAA,kCAAA,8BAAA,uBAAA,MAAA,WAAA,MAAA,mBAAA,MAAA;AAAA,MACA;AAAA,IACA;AAKA,UAAA,kBAAA,CAAA,UAAA;AACAA,oBAAAA,MAAA,MAAA,OAAA,kCAAA,mBAAA,KAAA;AACA,iBAAA,QAAA,MAAA;AAGA,UAAA,CAAA,kBAAA,MAAA,MAAA,KAAA,GAAA;AACA,qBAAA,MAAA,KAAA;AAAA,MACA;AAAA,IACA;AAGA,UAAA,qBAAA,CAAA,MAAA;AACA,iBAAA,QAAA,EAAA,OAAA;AAGA,UAAA,CAAA,kBAAA,MAAA,EAAA,OAAA,OAAA,GAAA;AACA,qBAAA,EAAA,OAAA,OAAA;AAAA,MACA;AAAA,IACA;AAGA,UAAA,eAAA,MAAA;AACAA,oBAAAA,MAAA,MAAA,OAAA,kCAAA,MAAA;AACAA,oBAAAA,MAAA,WAAA;AAAA,QACA,KAAA;AAAA,MACA,CAAA;AAAA,IACA;AAGA,UAAA,mBAAA,CAAA,SAAA;AACAA,oBAAA,MAAA,MAAA,OAAA,kCAAA,UAAA,KAAA,SAAA;AAEAA,oBAAAA,MAAA,WAAA;AAAA,QACA,KAAA,8BAAA,KAAA,KAAA;AAAA,MACA,CAAA;AAAA,IACA;AACA,UAAA,QAAA,OAAA,SAAA;AACA,UAAA,KAAA,cAAA;AACAA,sBAAAA,MAAA,WAAA;AAAA,UACA,KAAA,gCAAA,KAAA,IAAA;AAAA,QACA,CAAA;AACA;AAAA,MACA,OAAA;AACA,YAAA,MAAA;AAAA,UACA,cAAA,UAAA;AAAA,UACA,WAAA,KAAA;AAAA,QACA;AACA,YAAA;AACA,gBAAAG,UAAAA,kBAAA,GAAA;AACA,uBAAA,WAAA,KAAA;AACAH,wBAAAA,MAAA,UAAA;AAAA,YACA,OAAA;AAAA,YACA,MAAA;AAAA,UACA,CAAA;AAAA,QACA,SAAA,OAAA;AACAA,wBAAAA,MAAA,UAAA;AAAA,YACA,OAAA;AAAA,YACA,MAAA;AAAA,UACA,CAAA;AAAA,QACA;AAAA,MACA;AAAA,IAGA;AACAI,kBAAAA,kBAAA,MAAA;AACA,aAAA;AAAA,QACA,OAAA,UAAA,WAAA;AAAA,QACA,MAAA,+BAAA,UAAA,cAAA;AAAA,QACA,QAAA,KAAA;AACAJ,wBAAA,MAAA,MAAA,OAAA,kCAAA,4BAAA,UAAA,cAAA;AACAA,wBAAAA,MAAA,UAAA;AAAA,YACA,OAAA;AAAA,UACA,CAAA;AAAA,QACA;AAAA,QACA,KAAA,KAAA;AACAA,wBAAAA,MAAA,UAAA;AAAA,YACA,OAAA;AAAA,YACA,MAAA;AAAA,UACA,CAAA;AAAA,QACA;AAAA,MACA;AAAA,IACA,CAAA;AAuBA,UAAA,oBAAA,CAAA,iBAAA;AACA,aAAA,SAAA,MAAA,UAAA,UAAA,KAAA,SAAA,YAAA;AAAA,IACA;AAGA,UAAA,mBAAA,CAAA,iBAAA;AACA,YAAA,QAAA,kBAAA,YAAA;AACA,UAAA,UAAA,IAAA;AACA,mBAAA,QAAA;AAEA,YAAA,CAAA,kBAAA,MAAA,KAAA,GAAA;AACA,uBAAA,KAAA;AAAA,QACA;AAAA,MACA;AAAA,IACA;AAGAK,kBAAAA,OAAA,MAAA;AAEA,0BAAA;AACA,UAAA,UAAA,oBAAA;AACAL,sBAAA,MAAA,MAAA,OAAA,kCAAA,SAAA,OAAA,8BAAA;AAEA,YAAA,SAAA,MAAA,SAAA,GAAA;AACA,2BAAA,UAAA,kBAAA;AACA,oBAAA,sBAAA;AAAA,QACA,OAAA;AACA,0BAAA;AAAA,QACA;AAAA,MACA,OAAA;AACA,wBAAA;AAAA,MACA;AAAA,IACA,CAAA;AAsBAM,kBAAA;AAAA,MACA,MAAA,UAAA;AAAA,MACA,CAAA,UAAA,aAAA;AACAN,sBAAA,MAAA,MAAA,OAAA,kCAAA,qBAAA,UAAA,QAAA;AACA,YAAA,YAAA,aAAA,IAAA;AACA,0BAAA;AAAA,QAEA;AAAA,MACA;AAAA,IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnTA,GAAG,WAAW,eAAe;"}