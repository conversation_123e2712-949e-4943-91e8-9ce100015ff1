<template>
	<image class="ball" :src="img" @click="onGotoMsg"></image>
</template>

<script setup>
	// const img = 'https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/b6ab29e4be724a1a817be21b4b0ade72.png'
	const img = 'https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/adc5fc7591bd4ac0bfef34e0390db139.png'
	const onGotoMsg = () => {
		uni.navigateTo({
			url: '/pages/msg/msg',
		});
	}
</script>

<style lang="scss" scoped>
	.ball {
		width: 150rpx;
		height: 150rpx;
		position: absolute;
		z-index: 9;
		animation: ball 60s infinite linear;
	}

	@keyframes ball {
		0% {
			top: 80vh;
			left: 85%;
		}

		10% {
			top: 10vh;
			left: 3%;
		}

		20% {
			top: 30vh;
			left: 85%;
		}

		30% {
			top: 60vh;
			left: 3%;
		}

		40% {
			top: 10vh;
			left: 85%;
		}

		50% {
			top: 50vh;
			left: 3%;
		}

		60% {
			top: 10vh;
			left: 85%;
		}

		70% {
			top: 63vh;
			left: 3%;
		}

		80% {
			top: 53vh;
			left: 85%;
		}

		90% {
			top: 10vh;
			left: 3%;
		}

		100% {
			top: 80vh;
			left: 85%;
		}
	}
</style>