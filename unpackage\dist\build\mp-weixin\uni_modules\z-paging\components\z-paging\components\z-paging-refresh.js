"use strict";const e=require("../../../../../common/vendor.js"),t=require("../js/z-paging-static.js"),i=require("../js/z-paging-utils.js"),s=require("../js/z-paging-enum.js"),r={name:"z-paging-refresh",data:()=>({R:s.Enum.Refresher,isIos:"ios"===e.index.getSystemInfoSync().platform,refresherTimeText:"",zTheme:{title:{white:"#efefef",black:"#555555"},arrow:{white:t.zStatic.base64ArrowWhite,black:t.zStatic.base64Arrow},flower:{white:t.zStatic.base64FlowerWhite,black:t.zStatic.base64Flower},success:{white:t.zStatic.base64SuccessWhite,black:t.zStatic.base64Success},indicator:{white:"#eeeeee",black:"#777777"}}}),props:["status","defaultThemeStyle","defaultText","pullingText","refreshingText","completeText","goF2Text","defaultImg","pullingImg","refreshingImg","completeImg","refreshingAnimated","showUpdateTime","updateTimeKey","imgStyle","titleStyle","updateTimeStyle","updateTimeTextMap","unit"],computed:{ts(){return this.defaultThemeStyle},statusTextArr(){return this.updateTime(),[this.defaultText,this.pullingText,this.refreshingText,this.completeText,this.goF2Text]},currentTitle(){return this.statusTextArr[this.status]||this.defaultText},leftImageClass(){const e=`zp-r-left-image-pre-size-${this.unit}`;return this.status===this.R.Complete?e:`zp-r-left-image ${e} ${this.status===this.R.Default?"zp-r-arrow-down":"zp-r-arrow-top"}`},leftImageStyle(){const e=this.showUpdateTime,t=e?i.u.addUnit(36,this.unit):i.u.addUnit(34,this.unit);return{width:t,height:t,"margin-right":e?i.u.addUnit(20,this.unit):i.u.addUnit(9,this.unit)}},leftImageSrc(){const e=this.R,t=this.status;return t===e.Default?this.defaultImg?this.defaultImg:this.zTheme.arrow[this.ts]:t===e.ReleaseToRefresh?this.pullingImg?this.pullingImg:this.defaultImg?this.defaultImg:this.zTheme.arrow[this.ts]:t===e.Loading?this.refreshingImg?this.refreshingImg:this.zTheme.flower[this.ts]:t===e.Complete?this.completeImg?this.completeImg:this.zTheme.success[this.ts]:t===e.GoF2?this.zTheme.arrow[this.ts]:""},rightTextStyle(){let e={};return e.color=this.zTheme.title[this.ts],e["font-size"]=i.u.addUnit(30,this.unit),e}},methods:{addUnit:(e,t)=>i.u.addUnit(e,t),updateTime(){this.showUpdateTime&&(this.refresherTimeText=i.u.getRefesrherFormatTimeByKey(this.updateTimeKey,this.updateTimeTextMap))}}};const a=e._export_sfc(r,[["render",function(t,i,s,r,a,h){return e.e({a:s.status!==a.R.Loading},s.status!==a.R.Loading?{b:e.n(h.leftImageClass),c:e.s(h.leftImageStyle),d:e.s(s.imgStyle),e:h.leftImageSrc}:{f:s.refreshingAnimated?1:"",g:"rpx"===s.unit?1:"",h:"px"===s.unit?1:"",i:e.s(h.leftImageStyle),j:e.s(s.imgStyle),k:h.leftImageSrc},{l:e.t(h.currentTitle),m:e.s(h.rightTextStyle),n:e.s(s.titleStyle),o:s.showUpdateTime&&a.refresherTimeText.length},s.showUpdateTime&&a.refresherTimeText.length?{p:e.t(a.refresherTimeText),q:"rpx"===s.unit?1:"",r:"px"===s.unit?1:"",s:e.s({color:a.zTheme.title[h.ts]}),t:e.s(s.updateTimeStyle)}:{},{v:e.n(s.showUpdateTime?"zp-r-container zp-r-container-padding":"zp-r-container")})}],["__scopeId","data-v-cc5294aa"]]);wx.createComponent(a);
