import base from '@/config/config.js';
export const uploadTextFun = function(size = 20) {
	let uploadFileSize = 1024 * 1024 * size;
	return new Promise((resolve, reject) => {
		// #ifdef MP-WEIXIN
		//pptx ppt
		wx.chooseMessageFile({
			count: 1,
			type: 'file',
			extension: ['.word', '.pdf', '.md', '.txt', '.xlsx', '.docx', '.doc', '.xls', 'word', 'pdf', 'md',
				'txt', 'xlsx', 'docx', 'doc', 'xls'
			],
			success: (res) => {
				uni.showLoading({
					title: '文件上传中',
					mask: true,
				});
				if (res.tempFiles[0].size > uploadFileSize) {
					uni.hideLoading();
					setTimeout(() => {
						uni.showToast({
							title: '上传文件过大',
							icon: 'none',
							duration: 2000,
						});
					}, 200)
					reject();
				}
				let txtSrc = res.tempFiles[0].path;
				let url = `${base.baseUrl}user/api.userinfo/uploadTxt`;
				let uploadFileRes = uni.uploadFile({
					// url: 'https://ai-api.deepcity.cn/user/api.userinfo/uploadTxt',
					url,
					name: 'txt',
					filePath: txtSrc,
					success: sucres => {
						uni.hideLoading();
						let txt = JSON.parse(sucres.data);
						let data = {
							name: res.tempFiles[0].name,
							path: txt
						}
						resolve(data)
					},
					fail: err => {
						uni.hideLoading();
						reject(err);
					}
				});
			},
			fail: (err) => {
				uni.showToast({
					title: '取消选择',
					icon: 'none',
					duration: 2000,
				});
				reject(err);
			},
		});
		// #endif
		// #ifdef H5
		uni.chooseFile({
			count: 1,
			extension: ['.txt'],
			success: (res) => {
				uni.showLoading({
					title: '文件上传中',
					mask: true,
				});
				let txtSrc = res.tempFiles[0].path;
				let url = `${base.baseUrl}user/api.userinfo/uploadTxt`;
				let uploadFileRes = uni.uploadFile({
					// url: 'https://ai-api.deepcity.cn/user/api.userinfo/uploadTxt',
					url,
					name: 'txt',
					filePath: txtSrc,
					success: res => {
						uni.hideLoading();
						let txt = JSON.parse(res.data);
						resolve(txt)
					},
					fail: err => {
						return reject(err);
						uni.hideLoading();
					}
				});
			},
			fail: (err) => {
				uni.showToast({
					title: '取消选择',
					icon: 'none',
					duration: 2000,
				});
				return reject(err);
			},
		});
		// #endif
	})

}
// let uploadFileRes = uni.uploadFile({
//           url: 'https://ai-api.deepcity.cn/user/api.userinfo/uploadVideo',
//           name: 'video',
//           fileType: 'video',
//           filePath: videoSrc,
//           success: res => {
//             uni.hideLoading();
//             uni.showToast({
//               title: '上传成功',
//               icon: 'success',
//               duration: 2000
//             });
//             let video = JSON.parse(res.data);
//             resolve(video)
//           },
//           fail: err => {
//             return reject(err);
//             uni.hideLoading();
//           }
//         });
export const miniPay = function(data) {
	return new Promise((resolve, reject) => {
		const miniPayData = {
			nonceStr: data.nonceStr,
			package: data.package,
			paySign: data.paySign,
			signType: data.signType,
			timeStamp: data.timeStamp,
		};
		uni.requestPayment({
			...miniPayData,
			success: function(wxPayRes) {
				resolve(wxPayRes);
			},
			fail: function(wxPayErr) {
				reject({
					msg: '支付失败',
				});
			},
			cancel: function() {
				reject({
					msg: '取消支付',
				});
			},
		});
	});
};

export const updataVideoFun = async function(data) {
	try {
		let url = `${base.baseUrl}user/api.userinfo/uploadVideo`;
		let uploadFileRes = uni.uploadFile({
			// url: 'https://ai-api.deepcity.cn/user/api.userinfo/uploadVideo',
			url,
			name: 'video',
			fileType: 'video',
			filePath: data
		});
		if (uploadFileRes.statusCode < 200 || uploadFileRes.statusCode > 300) {
			return Promise.reject({
				errMsg: 'statusCode is not 200 series',
			});
		} else {
			return uploadFileRes;
		}
	} catch (e) {
		uni.hideLoading();
		return Promise.reject(e);
	}
};

export const updataFileFun = async function(data) {
	try {
		let url = `${base.baseUrl}user/api.userinfo/uploadImg`;
		let uploadFileRes = uni.uploadFile({
			// url: 'https://ai-api.deepcity.cn/user/api.userinfo/uploadImg',
			url,
			name: 'img',
			filePath: data,
			header: {
				'content-type': 'multipart/form-data',
			},
		});
		if (uploadFileRes.statusCode < 200 || uploadFileRes.statusCode > 300) {
			return Promise.reject({
				errMsg: 'statusCode is not 200 series',
			});
		} else {
			return uploadFileRes;
		}
	} catch (e) {
		uni.hideLoading();
		return Promise.reject(e);
	}
};

export const uploadImageMp = function(count = 1) {
	return new Promise((resolve, reject) => {
		uni.chooseMedia({
			count: count,
			mediaType: ['image'],
			sourceType: ['album'],
			sizeType: ['compressed'],
			success: async (res) => {
				try {
					uni.showLoading({
						title: '上传中',
						mask: true,
					})
					let MPres = await updataFileFun(res.tempFiles[0].tempFilePath);
					uni.hideLoading()
					let data = JSON.parse(MPres.data);
					resolve(data)
				} catch (error) {
					// uni.$u.toast(error.msg ? error.msg : '上传失败');
					uni.showToast({
						title: error.msg ? error.msg : '上传失败',
						icon: 'none',
						duration: 2000,
					});
					uni.hideLoading()
				}
			},
			fail(err) {
				// uni.$u.toast(err.msg ? err.msg : '上传失败');
				uni.showToast({
					title: error.msg ? error.msg : '上传失败',
					icon: 'none',
					duration: 2000,
				});
				reject(err)
			}
		});
	})
}