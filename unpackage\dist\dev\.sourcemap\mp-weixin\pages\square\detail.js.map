{"version": 3, "file": "detail.js", "sources": ["pages/square/detail.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvc3F1YXJlL2RldGFpbC52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"agent-detail-page\">\r\n    <!-- 智能体信息 -->\r\n    <view class=\"agent-info\" v-if=\"agentDetail.agentName\">\r\n      <view class=\"avatar-section\">\r\n        <image class=\"agent-avatar\" :src=\"agentDetail.agentAvatar\" mode=\"aspectFill\" />\r\n        <!-- <view class=\"edit-icon\" v-if=\"showEditIcon\">\r\n          <image src=\"@/static/common/edit_icon.png\" class=\"edit-img\" mode=\"aspectFit\" />\r\n        </view> -->\r\n        <view class=\"nick-name\">@{{ agentDetail.creator.nickname }}</view>\r\n      </view>\r\n\r\n      <view class=\"info-section\">\r\n        <text class=\"agent-name\">{{ agentDetail.agentName }}</text>\r\n        <text class=\"agent-desc\">{{ agentDetail.agentDesc }}</text>\r\n      </view>\r\n\r\n      <view class=\"action-buttons\">\r\n        <view class=\"subscribe-btn\" v-if=\"!agentDetail.isSubscribed\" @click=\"handleSubscribeConfirm\">\r\n          <text class=\"btn-text\">立即招募</text>\r\n        </view>\r\n        <view class=\"chat-btn\" v-if=\"agentDetail.isSubscribed\" @click=\"handleSubscribe\">\r\n          <text class=\"btn-text\">去聊天</text>\r\n        </view>\r\n        <view class=\"share-btn\" @click=\"handleShare\">\r\n          <text class=\"btn-text\">生成分享海报</text>\r\n        </view>\r\n        <!-- <view class=\"promotion-link\" @tap=\"handlePromotionPlan\">\r\n          <text class=\"link-text\">《分享推广计划》</text>\r\n        </view> -->\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 加载状态 -->\r\n    <view class=\"loading-state\" v-else-if=\"loading\">\r\n      <text class=\"loading-text\">加载中...</text>\r\n    </view>\r\n\r\n    <!-- 空状态 -->\r\n    <view class=\"empty-state\" v-else>\r\n      <text class=\"empty-text\">智能体信息加载失败</text>\r\n    </view>\r\n  </view>\r\n\r\n\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, watch } from 'vue'\r\nimport {\r\n  onLoad, onShareAppMessage,\r\n  onShareTimeline,\r\n} from '@dcloudio/uni-app'\r\nimport { agentDetailApi, subscribeAgentApi, generateMiniCodeApi, getInvitationCodeApi, bindInvitationApi } from '@/api/index.js'\r\nimport { useUserStore } from '@/stores/user.js'\r\n// import SubscribePopup from '@/components/subscribe-popup/subscribe-popup.vue'\r\n\r\nconst userStore = useUserStore()\r\n\r\n// 页面参数\r\nconst agentGuid = ref('')\r\nconst sysId = ref('')\r\nconst loading = ref(false)\r\nconst showEditIcon = ref(false) // 根据设计图，这里可能需要显示编辑图标\r\nconst showSubscribeModal = ref(false) // 控制订阅弹窗显示\r\nconst showSharePoster = ref(false)\r\n// 智能体详情数据\r\nconst agentDetail = ref({\r\n  agentName: '',\r\n  agentDesc: '',\r\n  agentAvatar: '',\r\n  isPaid: 0,\r\n  price: 0,\r\n  isPublic: 1,\r\n  isSubscribed: false,\r\n})\r\n\r\n// 获取智能体详情\r\nconst getAgentDetail = async () => {\r\n  if (!sysId.value) {\r\n    uni.showToast({\r\n      title: '参数错误',\r\n      icon: 'none'\r\n    })\r\n    return\r\n  }\r\n\r\n  try {\r\n    loading.value = true\r\n    const res = await agentDetailApi({\r\n      merchantGuid: userStore.merchantGuid,\r\n      agentSysId: sysId.value\r\n    })\r\n\r\n    if (res.code === 0) {\r\n      agentDetail.value = res.data;\r\n      sysId.value = res.data.sysId;\r\n      agentGuid.value = res.data.guid\r\n    } else {\r\n      throw new Error(res.msg || '获取详情失败')\r\n    }\r\n  } catch (error) {\r\n    console.error('获取智能体详情失败:', error)\r\n    uni.showToast({\r\n      title: error.message || '加载失败',\r\n      icon: 'none'\r\n    })\r\n  } finally {\r\n    loading.value = false\r\n  }\r\n}\r\n\r\n// 订阅智能体\r\nconst handleSubscribe = async () => {\r\n  if (agentDetail.value.isSubscribed) {\r\n    uni.navigateTo({\r\n      url: `/pages/msg/index?sessionGuid=${agentGuid.value}`\r\n    })\r\n    return\r\n  }\r\n  try {\r\n    uni.showLoading({\r\n      title: '订阅中...',\r\n      mask: true\r\n    })\r\n\r\n    const res = await subscribeAgentApi({\r\n      merchantGuid: userStore.merchantGuid,\r\n      agentGuid: agentGuid.value\r\n    })\r\n\r\n    if (res.code === 0) {\r\n      uni.showToast({\r\n        title: '订阅成功',\r\n        icon: 'success'\r\n      })\r\n      // 跳转到对话页面\r\n      // setTimeout(() => {\r\n      uni.navigateTo({\r\n        url: `/pages/msg/index?sessionGuid=${agentGuid.value}`\r\n      })\r\n      // }, 1500)\r\n    } else {\r\n      throw new Error(res.msg || '订阅失败')\r\n    }\r\n  } catch (error) {\r\n    console.error('订阅失败:', error)\r\n    uni.showToast({\r\n      title: error.message || '订阅失败',\r\n      icon: 'none'\r\n    })\r\n  } finally {\r\n    uni.hideLoading()\r\n  }\r\n}\r\n\r\n// 生成分享海报\r\nconst handleShare = async () => {\r\n  // 确保二维码已生成\r\n  if (!qrcode.value) {\r\n    uni.showToast({\r\n      title: '正在生成二维码，请稍后...',\r\n      icon: 'none'\r\n    })\r\n    return\r\n  }\r\n\r\n  const params = {\r\n    agentName: agentDetail.value.agentName || '智能体名称',\r\n    agentDesc: agentDetail.value.agentDesc || '智能体描述',\r\n    agentAvatar: agentDetail.value.agentAvatar || '',\r\n    qrcode: qrcode.value || ''\r\n  }\r\n\r\n  console.log('传递给分享页的参数:', params)\r\n  uni.navigateTo({\r\n    url: `/pages/square/share?params=${encodeURIComponent(JSON.stringify(params))}`\r\n  })\r\n}\r\n\r\n// 查看推广计划\r\nconst handlePromotionPlan = () => {\r\n  uni.showToast({\r\n    title: '功能开发中',\r\n    icon: 'none'\r\n  })\r\n}\r\n\r\n\r\n// 确认订阅\r\nconst handleSubscribeConfirm = async (agentInfo) => {\r\n  try {\r\n    uni.showLoading({\r\n      title: '订阅中...',\r\n      mask: true\r\n    })\r\n\r\n    const res = await subscribeAgentApi({\r\n      merchantGuid: userStore.merchantGuid,\r\n      agentGuid: agentGuid.value\r\n    })\r\n\r\n    if (res.code === 0) {\r\n      uni.showToast({\r\n        title: '订阅成功',\r\n        icon: 'success'\r\n      })\r\n      // 更新智能体状态\r\n      agentDetail.value.isSubscribed = true\r\n      // 跳转到对话页面\r\n      setTimeout(() => {\r\n        uni.navigateTo({\r\n          url: `/pages/msg/index?sessionGuid=${agentGuid.value}`\r\n        })\r\n      }, 1500)\r\n    } else {\r\n      throw new Error(res.msg || '订阅失败')\r\n    }\r\n  } catch (error) {\r\n    console.error('订阅失败:', error)\r\n    uni.showToast({\r\n      title: error.message || '订阅失败',\r\n      icon: 'none'\r\n    })\r\n  } finally {\r\n    uni.hideLoading()\r\n  }\r\n}\r\nonShareAppMessage(() => {\r\n  return {\r\n    title: userStore.appName || '智能体',\r\n    path: `/pages/square/detail?invite=${invitationCode.value}`,\r\n    success(res) {\r\n      uni.showToast({\r\n        title: '分享成功'\r\n      })\r\n    },\r\n    fail(res) {\r\n      uni.showToast({\r\n        title: '分享失败',\r\n        icon: 'none'\r\n      })\r\n    }\r\n  }\r\n})\r\n// 分享到朋友圈功能\r\n// onShareTimeline(() => {\r\n//   return {\r\n//     title: userStore.appName || '智能体',\r\n//     path: `/pages/square/detail?invite=${userStore.invitationCode}`,\r\n//     success(res) {\r\n//       uni.showToast({\r\n//         title: '分享成功'\r\n//       })\r\n//     },\r\n//     fail(res) {\r\n//       uni.showToast({\r\n//         title: '分享失败',\r\n//         icon: 'none'\r\n//       })\r\n//     }\r\n//   }\r\n// })\r\nconst qrcode = ref('')\r\nconst generateMiniCode = async () => {\r\n  let query = `sysId=${sysId.value}&invite=${userStore.invitationCode}`\r\n  let res = await generateMiniCodeApi({\r\n    merchantGuid: userStore.merchantGuid,\r\n    miniPath: 'pages/square/detail',\r\n    pathQuery: query,\r\n  })\r\n  qrcode.value = res.data.miniCodeUrl\r\n}\r\nconst invitationCode = ref('')\r\nconst getInvitationCode = async () => {\r\n  let res = await getInvitationCodeApi({\r\n    merchantGuid: userStore.merchantGuid,\r\n  })\r\n  invitationCode.value = res.data.inviteCode;\r\n  generateMiniCode()\r\n}\r\n\r\n// let invite = ref('')\r\n// const bindInvitation = async () => {\r\n//   try {\r\n//     await bindInvitationApi({\r\n//       merchantGuid: userStore.merchantGuid,\r\n//       invitationCode: invite.value\r\n//     })\r\n//   } catch (error) {\r\n//     console.error('绑定邀请码失败detail:', error)\r\n//   }\r\n// }\r\nonLoad(async (params) => {\r\n  // if (params.invite) {\r\n  //   invite.value = params.invite;\r\n  //   if (userStore.userToken) {\r\n  //     bindInvitation()\r\n  //   }\r\n  // }\r\n  if (params.sysId) {\r\n    // agentGuid.value = params.agentGuid;\r\n    sysId.value = params.sysId\r\n    if (userStore.userToken) {\r\n      await getAgentDetail()\r\n      await getInvitationCode()\r\n    }\r\n  } else {\r\n    uni.showToast({\r\n      title: '参数错误',\r\n      icon: 'none'\r\n    })\r\n  }\r\n})\r\nwatch(\r\n  () => userStore.userToken,\r\n  async (newValue, oldValue) => {\r\n    if (newValue && oldValue === '') {\r\n      await getAgentDetail()\r\n      await getInvitationCode()\r\n      // bindInvitation()\r\n    }\r\n  }\r\n);\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.agent-detail-page {\r\n  background: #f5f5f5;\r\n  min-height: 100vh;\r\n  padding: 60rpx 32rpx;\r\n\r\n  .agent-info {\r\n    background: #ffffff;\r\n    border-radius: 24rpx;\r\n    padding: 60rpx 40rpx;\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    text-align: center;\r\n\r\n    .avatar-section {\r\n      position: relative;\r\n      margin-bottom: 40rpx;\r\n\r\n      .agent-avatar {\r\n        width: 200rpx;\r\n        height: 200rpx;\r\n        border-radius: 50%;\r\n        background: #f0f0f0;\r\n      }\r\n\r\n      .edit-icon {\r\n        position: absolute;\r\n        right: 0;\r\n        top: 0;\r\n        width: 60rpx;\r\n        height: 60rpx;\r\n        background: #3478f6;\r\n        border-radius: 50%;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n\r\n        .edit-img {\r\n          width: 32rpx;\r\n          height: 32rpx;\r\n        }\r\n      }\r\n\r\n      .nick-name {\r\n        font-size: 26rpx;\r\n        color: #666666;\r\n        text-align: center;\r\n        margin-top: 10px;\r\n      }\r\n    }\r\n\r\n    .info-section {\r\n      margin-bottom: 60rpx;\r\n\r\n      .agent-name {\r\n        font-size: 48rpx;\r\n        font-weight: 600;\r\n        color: #1a1a1a;\r\n        display: block;\r\n        margin-bottom: 24rpx;\r\n      }\r\n\r\n      .agent-desc {\r\n        font-size: 28rpx;\r\n        color: #666666;\r\n        line-height: 1.6;\r\n        display: block;\r\n      }\r\n    }\r\n\r\n    .action-buttons {\r\n      width: 100%;\r\n      display: flex;\r\n      flex-direction: column;\r\n      justify-content: center;\r\n      align-items: center;\r\n\r\n      .subscribe-btn {\r\n        width: 400rpx;\r\n        height: 90rpx;\r\n        background: #3478f6;\r\n        border-radius: 48rpx;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        margin-bottom: 24rpx;\r\n\r\n\r\n        .btn-text {\r\n          font-size: 30rpx;\r\n          color: #ffffff;\r\n          font-weight: 600;\r\n        }\r\n\r\n        &.subscribed {\r\n          background-color: #F2F2F7;\r\n          border: none;\r\n\r\n          .btn-text {\r\n            color: #8E8E93;\r\n          }\r\n        }\r\n      }\r\n\r\n      .chat-btn {\r\n        width: 400rpx;\r\n        height: 90rpx;\r\n        background: #40a266;\r\n        border-radius: 48rpx;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        margin-bottom: 24rpx;\r\n\r\n\r\n        .btn-text {\r\n          font-size: 30rpx;\r\n          color: #ffffff;\r\n          font-weight: 600;\r\n        }\r\n      }\r\n\r\n      .share-btn {\r\n        width: 400rpx;\r\n        height: 90rpx;\r\n        background: #3478f6;\r\n        border-radius: 48rpx;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        margin-bottom: 32rpx;\r\n\r\n        .btn-text {\r\n          font-size: 30rpx;\r\n          color: #ffffff;\r\n          font-weight: 600;\r\n        }\r\n      }\r\n\r\n      .promotion-link {\r\n        display: flex;\r\n        justify-content: center;\r\n\r\n        .link-text {\r\n          font-size: 28rpx;\r\n          color: #3478f6;\r\n          text-decoration: underline;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .loading-state {\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    height: 400rpx;\r\n\r\n    .loading-text {\r\n      font-size: 28rpx;\r\n      color: #999999;\r\n    }\r\n  }\r\n\r\n  .empty-state {\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    height: 400rpx;\r\n\r\n    .empty-text {\r\n      font-size: 28rpx;\r\n      color: #999999;\r\n    }\r\n  }\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'E:/youngProject/agent-mini-ui/pages/square/detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useUserStore", "ref", "uni", "agentDetailApi", "subscribeAgentApi", "onShareAppMessage", "generateMiniCodeApi", "getInvitationCodeApi", "onLoad", "watch"], "mappings": ";;;;;;;AAyDA,UAAA,YAAAA,YAAAA,aAAA;AAGA,UAAA,YAAAC,cAAA,IAAA,EAAA;AACA,UAAA,QAAAA,cAAA,IAAA,EAAA;AACA,UAAA,UAAAA,cAAA,IAAA,KAAA;AACAA,kBAAA,IAAA,KAAA;AACAA,kBAAA,IAAA,KAAA;AACAA,kBAAA,IAAA,KAAA;AAEA,UAAA,cAAAA,cAAAA,IAAA;AAAA,MACA,WAAA;AAAA,MACA,WAAA;AAAA,MACA,aAAA;AAAA,MACA,QAAA;AAAA,MACA,OAAA;AAAA,MACA,UAAA;AAAA,MACA,cAAA;AAAA,IACA,CAAA;AAGA,UAAA,iBAAA,YAAA;AACA,UAAA,CAAA,MAAA,OAAA;AACAC,sBAAAA,MAAA,UAAA;AAAA,UACA,OAAA;AAAA,UACA,MAAA;AAAA,QACA,CAAA;AACA;AAAA,MACA;AAEA,UAAA;AACA,gBAAA,QAAA;AACA,cAAA,MAAA,MAAAC,yBAAA;AAAA,UACA,cAAA,UAAA;AAAA,UACA,YAAA,MAAA;AAAA,QACA,CAAA;AAEA,YAAA,IAAA,SAAA,GAAA;AACA,sBAAA,QAAA,IAAA;AACA,gBAAA,QAAA,IAAA,KAAA;AACA,oBAAA,QAAA,IAAA,KAAA;AAAA,QACA,OAAA;AACA,gBAAA,IAAA,MAAA,IAAA,OAAA,QAAA;AAAA,QACA;AAAA,MACA,SAAA,OAAA;AACAD,sBAAAA,MAAA,MAAA,SAAA,kCAAA,cAAA,KAAA;AACAA,sBAAAA,MAAA,UAAA;AAAA,UACA,OAAA,MAAA,WAAA;AAAA,UACA,MAAA;AAAA,QACA,CAAA;AAAA,MACA,UAAA;AACA,gBAAA,QAAA;AAAA,MACA;AAAA,IACA;AAGA,UAAA,kBAAA,YAAA;AACA,UAAA,YAAA,MAAA,cAAA;AACAA,sBAAAA,MAAA,WAAA;AAAA,UACA,KAAA,gCAAA,UAAA,KAAA;AAAA,QACA,CAAA;AACA;AAAA,MACA;AACA,UAAA;AACAA,sBAAAA,MAAA,YAAA;AAAA,UACA,OAAA;AAAA,UACA,MAAA;AAAA,QACA,CAAA;AAEA,cAAA,MAAA,MAAAE,4BAAA;AAAA,UACA,cAAA,UAAA;AAAA,UACA,WAAA,UAAA;AAAA,QACA,CAAA;AAEA,YAAA,IAAA,SAAA,GAAA;AACAF,wBAAAA,MAAA,UAAA;AAAA,YACA,OAAA;AAAA,YACA,MAAA;AAAA,UACA,CAAA;AAGAA,wBAAAA,MAAA,WAAA;AAAA,YACA,KAAA,gCAAA,UAAA,KAAA;AAAA,UACA,CAAA;AAAA,QAEA,OAAA;AACA,gBAAA,IAAA,MAAA,IAAA,OAAA,MAAA;AAAA,QACA;AAAA,MACA,SAAA,OAAA;AACAA,sBAAAA,MAAA,MAAA,SAAA,kCAAA,SAAA,KAAA;AACAA,sBAAAA,MAAA,UAAA;AAAA,UACA,OAAA,MAAA,WAAA;AAAA,UACA,MAAA;AAAA,QACA,CAAA;AAAA,MACA,UAAA;AACAA,sBAAAA,MAAA,YAAA;AAAA,MACA;AAAA,IACA;AAGA,UAAA,cAAA,YAAA;AAEA,UAAA,CAAA,OAAA,OAAA;AACAA,sBAAAA,MAAA,UAAA;AAAA,UACA,OAAA;AAAA,UACA,MAAA;AAAA,QACA,CAAA;AACA;AAAA,MACA;AAEA,YAAA,SAAA;AAAA,QACA,WAAA,YAAA,MAAA,aAAA;AAAA,QACA,WAAA,YAAA,MAAA,aAAA;AAAA,QACA,aAAA,YAAA,MAAA,eAAA;AAAA,QACA,QAAA,OAAA,SAAA;AAAA,MACA;AAEAA,oBAAAA,MAAA,MAAA,OAAA,kCAAA,cAAA,MAAA;AACAA,oBAAAA,MAAA,WAAA;AAAA,QACA,KAAA,8BAAA,mBAAA,KAAA,UAAA,MAAA,CAAA,CAAA;AAAA,MACA,CAAA;AAAA,IACA;AAYA,UAAA,yBAAA,OAAA,cAAA;AACA,UAAA;AACAA,sBAAAA,MAAA,YAAA;AAAA,UACA,OAAA;AAAA,UACA,MAAA;AAAA,QACA,CAAA;AAEA,cAAA,MAAA,MAAAE,4BAAA;AAAA,UACA,cAAA,UAAA;AAAA,UACA,WAAA,UAAA;AAAA,QACA,CAAA;AAEA,YAAA,IAAA,SAAA,GAAA;AACAF,wBAAAA,MAAA,UAAA;AAAA,YACA,OAAA;AAAA,YACA,MAAA;AAAA,UACA,CAAA;AAEA,sBAAA,MAAA,eAAA;AAEA,qBAAA,MAAA;AACAA,0BAAAA,MAAA,WAAA;AAAA,cACA,KAAA,gCAAA,UAAA,KAAA;AAAA,YACA,CAAA;AAAA,UACA,GAAA,IAAA;AAAA,QACA,OAAA;AACA,gBAAA,IAAA,MAAA,IAAA,OAAA,MAAA;AAAA,QACA;AAAA,MACA,SAAA,OAAA;AACAA,sBAAAA,MAAA,MAAA,SAAA,kCAAA,SAAA,KAAA;AACAA,sBAAAA,MAAA,UAAA;AAAA,UACA,OAAA,MAAA,WAAA;AAAA,UACA,MAAA;AAAA,QACA,CAAA;AAAA,MACA,UAAA;AACAA,sBAAAA,MAAA,YAAA;AAAA,MACA;AAAA,IACA;AACAG,kBAAAA,kBAAA,MAAA;AACA,aAAA;AAAA,QACA,OAAA,UAAA,WAAA;AAAA,QACA,MAAA,+BAAA,eAAA,KAAA;AAAA,QACA,QAAA,KAAA;AACAH,wBAAAA,MAAA,UAAA;AAAA,YACA,OAAA;AAAA,UACA,CAAA;AAAA,QACA;AAAA,QACA,KAAA,KAAA;AACAA,wBAAAA,MAAA,UAAA;AAAA,YACA,OAAA;AAAA,YACA,MAAA;AAAA,UACA,CAAA;AAAA,QACA;AAAA,MACA;AAAA,IACA,CAAA;AAmBA,UAAA,SAAAD,cAAA,IAAA,EAAA;AACA,UAAA,mBAAA,YAAA;AACA,UAAA,QAAA,SAAA,MAAA,KAAA,WAAA,UAAA,cAAA;AACA,UAAA,MAAA,MAAAK,8BAAA;AAAA,QACA,cAAA,UAAA;AAAA,QACA,UAAA;AAAA,QACA,WAAA;AAAA,MACA,CAAA;AACA,aAAA,QAAA,IAAA,KAAA;AAAA,IACA;AACA,UAAA,iBAAAL,cAAA,IAAA,EAAA;AACA,UAAA,oBAAA,YAAA;AACA,UAAA,MAAA,MAAAM,+BAAA;AAAA,QACA,cAAA,UAAA;AAAA,MACA,CAAA;AACA,qBAAA,QAAA,IAAA,KAAA;AACA,uBAAA;AAAA,IACA;AAaAC,kBAAA,OAAA,OAAA,WAAA;AAOA,UAAA,OAAA,OAAA;AAEA,cAAA,QAAA,OAAA;AACA,YAAA,UAAA,WAAA;AACA,gBAAA,eAAA;AACA,gBAAA,kBAAA;AAAA,QACA;AAAA,MACA,OAAA;AACAN,sBAAAA,MAAA,UAAA;AAAA,UACA,OAAA;AAAA,UACA,MAAA;AAAA,QACA,CAAA;AAAA,MACA;AAAA,IACA,CAAA;AACAO,kBAAA;AAAA,MACA,MAAA,UAAA;AAAA,MACA,OAAA,UAAA,aAAA;AACA,YAAA,YAAA,aAAA,IAAA;AACA,gBAAA,eAAA;AACA,gBAAA,kBAAA;AAAA,QAEA;AAAA,MACA;AAAA,IACA;;;;;;;;;;;;;;;;;;;;;;;;;;AClUA,GAAG,WAAW,eAAe;"}