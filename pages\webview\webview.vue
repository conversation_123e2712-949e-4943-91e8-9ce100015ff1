<template>
  <view>
    <web-view :src="webViewUrl"></web-view>
  </view>
</template>

<script setup>
  import {
    ref,
    getCurrentInstance,
    onMounted
  } from 'vue';
  const webViewUrl = ref('')
  onMounted(() => {
    const instance = getCurrentInstance().proxy;
    const eventChannel = instance.getOpenerEventChannel();
    eventChannel.on('urlEvent', function(data) {
      if (data) {
        webViewUrl.value = data;
      }
    })
  })
</script>

<style lang="scss"></style>