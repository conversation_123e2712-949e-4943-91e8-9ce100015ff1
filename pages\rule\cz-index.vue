<template>
  <view class="rule-page">
    <!-- 背景图片 -->
    <image class="background-image" :src="ruleBg1" mode="aspectFill" />

    <!-- 标题图片 -->
    <view class="title-section">
      <image class="title-image" :src="ruleTitleBg1" mode="aspectFit" />
    </view>

    <!-- 规则内容 -->
    <view class="content-section">
      <view class="rule-content">
        <!-- <view class="rule-item" v-html="htmlContent"></view> -->
        <rich-text :nodes="htmlContent"></rich-text>
      </view>
    </view>

    <!-- 去创作按钮 -->
    <view class="action-section">
      <view class="create-btn" @click="handleGoCreate">
        <text class="create-text">去创作</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'
import { platformRulesApi } from '@/api/index.js'
import { useUserStore } from '@/stores/user.js'
const userStore = useUserStore()
const ruleTitleBg1 = 'https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/6886986325964ded9f4e231bd0c0e6ad.png'
// const ruleTitleBg2 = 'https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/3901e1ecde244f119393a75b5acf16b4.png'
// const ruleTitleBg3 = 'https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/306dd6d0badd46b697aadce17ef1d853.png'
const ruleBg1 = 'https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/134edc12f1494bb6b1c23098140f5610.png'
// const ruleBg2 = 'https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/9c3e9e617f9a46f38419cb9f0fd1632d.png'
// const ruleBg3 = 'https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/fab54f3a68b04bc5bf6df56a918d9a10.png'

// 去创作按钮点击事件
const handleGoCreate = () => {
  // 跳转到创建智能体页面
  uni.navigateTo({
    url: '/pages/create-agent/index'
  })
}
let htmlContent = ref('')
const platformRules = async () => {
  let res = await platformRulesApi({
    merchantGuid: userStore.merchantGuid
  })
  htmlContent.value = res.data.creationRules.content
}
platformRules()
</script>

<style lang="scss" scoped>
.rule-page {
  background: #ffffff;
  min-height: 100vh;
  position: relative;

  .background-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
  }

  .title-section {
    position: relative;
    z-index: 2;
    padding: 40rpx 32rpx 20rpx;

    .title-image {
      width: 560rpx;
      height: 60px;
    }
  }

  .content-section {
    position: relative;
    z-index: 2;
    margin: 20rpx 32rpx;
    background: #ffffff;
    border-radius: 24rpx;
    padding: 32rpx 32rpx 100px;
    margin-top: -20px;

    .rule-content {
      .rule-item {
        margin-bottom: 32rpx;

        &:last-child {
          margin-bottom: 0;
        }

        .rule-title {
          display: block;
          font-size: 32rpx;
          font-weight: 600;
          color: #1a1a1a;
          margin-bottom: 16rpx;
          line-height: 1.4;
        }

        .rule-text {
          display: block;
          font-size: 28rpx;
          color: #333333;
          line-height: 1.6;
          margin-bottom: 12rpx;

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }

  .action-section {
    position: fixed;
    bottom: 40rpx;
    left: 50%;
    transform: translateX(-50%);
    z-index: 3;

    .create-btn {
      width: 400rpx;
      height: 88rpx;
      background: #3478f6;
      border-radius: 44rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      .create-text {
        font-size: 32rpx;
        color: #ffffff;
        font-weight: 600;
      }
    }
  }
}
</style>