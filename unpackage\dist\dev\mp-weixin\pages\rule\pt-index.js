"use strict";
const common_vendor = require("../../common/vendor.js");
const api_index = require("../../api/index.js");
const stores_user = require("../../stores/user.js");
const ruleTitleBg3 = "https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/306dd6d0badd46b697aadce17ef1d853.png";
const ruleBg3 = "https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/fab54f3a68b04bc5bf6df56a918d9a10.png";
const _sfc_main = {
  __name: "pt-index",
  setup(__props) {
    const userStore = stores_user.useUserStore();
    let htmlContent = common_vendor.ref("");
    const platformRules = async () => {
      let res = await api_index.platformRulesApi({
        merchantGuid: userStore.merchantGuid
      });
      htmlContent.value = res.data.platformRules.content;
    };
    platformRules();
    return (_ctx, _cache) => {
      return {
        a: ruleBg3,
        b: ruleTitleBg3,
        c: common_vendor.unref(htmlContent)
      };
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-4fe0b264"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/rule/pt-index.js.map
