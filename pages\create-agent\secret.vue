<template>
  <view class="secret-edit-page">
    <!-- 表单区域 -->
    <view class="form-section">
      <!-- 秘钥 -->
      <view class="form-item">
        <input v-model="secretForm.secretKey" class="input" placeholder="输入秘钥" placeholder-class="placeholder"
          maxlength="20" />
      </view>
    </view>
    <!-- 保存按钮 -->
    <view class="save-section">
      <view class="save-btn" @tap="handleSave" >
        <text class="save-text">{{ saving ? '保存中...' : '保存修改' }}</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { getSecretKeyListApi, saveSecretKeyApi } from '@/api/index.js'
import { useUserStore } from '@/stores/user.js'

const userStore = useUserStore()
// 表单数据
let secretForm = reactive({
  secretKey: '',
  secretKeyType: 'coze_api_key',
})
const getSecretKeyList = async () => {
  let res = await getSecretKeyListApi({
    merchantGuid: userStore.merchantGuid,
  })
}
const handleSave=()=>{
  
}
// 初始化用户信息
onMounted(() => {
  getSecretKeyList()
})

</script>

<style lang="scss" scoped>
.secret-edit-page {
  background: #F5F5F5;
  min-height: 100vh;
}

.form-section {
  padding: 20px 32rpx;

  .form-item {
    display: flex;
    align-items: center;
    padding: 20rpx 16rpx;
    background: #ffffff;
    border-radius: 20rpx;
    margin-bottom: 20px;

    &:last-child {
      border-bottom: none;
    }

    .label {
      font-size: 30rpx;
      color: #1a1a1a;
      font-weight: 500;
      width: 120rpx;
      flex-shrink: 0;
    }

    .input {
      flex: 1;
      font-size: 28rpx;
      color: #1a1a1a;
      margin-left: 24rpx;
      height: 44rpx;
      line-height: 44rpx;

      &.placeholder {
        color: #CCCCCC;
      }
    }
  }
}

.save-section {
  padding: 80rpx 32rpx;
  padding-bottom: calc(80rpx + env(safe-area-inset-bottom));

  .save-btn {
    width: 100%;
    height: 96rpx;
    background: #3478f6;
    border-radius: 48rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;

    &.disabled {
      background: #CCCCCC;
    }

    .save-text {
      font-size: 32rpx;
      color: #ffffff;
      font-weight: 600;
    }
  }
}

/* 占位符样式 */
.placeholder {
  color: #CCCCCC !important;
}
</style>