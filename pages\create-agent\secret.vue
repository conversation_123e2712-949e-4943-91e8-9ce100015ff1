<template>
  <view class="secret-edit-page">
    <!-- 表单区域 -->
    <view class="form-section">
      <!-- 动态生成秘钥输入框 -->
      <view class="form-item" v-for="item in secretKeyList" :key="item.secretKeyType">
        <input v-model="item.secretKey" class="input" :placeholder="item.secretKeyTypeText"
          placeholder-class="placeholder" maxlength="200" />
      </view>
    </view>
    <!-- 保存按钮 -->
    <view class="save-section">
      <view class="save-btn" :class="{ disabled: saving }" @tap="handleSave">
        <text class="save-text">{{ saving ? '保存中...' : '保存修改' }}</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { getSecretKeyListApi, saveSecretKeyApi } from '@/api/index.js'
import { useUserStore } from '@/stores/user.js'

const userStore = useUserStore()

// 秘钥列表数据
const secretKeyList = ref([])

// 获取秘钥列表
const getSecretKeyList = async () => {
  try {
    let res = await getSecretKeyListApi({
      merchantGuid: userStore.merchantGuid,
    })

    if (res.code === 0 && res.data && res.data.length > 0) {
      // 直接使用返回的数据，确保每个项都有secretKey字段
      secretKeyList.value = res.data.map(item => ({
        ...item,
        secretKey: item.secretKey || '' // 如果没有secretKey则设为空字符串
      }))
    }
  } catch (error) {
    console.error('获取秘钥列表失败:', error)
    uni.showToast({
      title: '获取秘钥失败',
      icon: 'none'
    })
  }
}

const saving = ref(false)

// 保存秘钥
const handleSave = async () => {
  // 验证是否至少有一个秘钥被填写
  const hasValidKey = secretKeyList.value.some(item => item.secretKey && item.secretKey.trim())

  if (!hasValidKey) {
    uni.showToast({
      title: '请至少输入一个秘钥',
      icon: 'none'
    })
    return
  }

  saving.value = true

  try {
    // 只保存有值的秘钥
    const saveSecretKeyList = secretKeyList.value
      .filter(item => item.secretKey && item.secretKey.trim())
      .map(item => ({
        secretKeyType: item.secretKeyType,
        secretKey: item.secretKey.trim()
      }))

    const saveData = {
      merchantGuid: userStore.merchantGuid,
      saveSecretKeyList
    }

    let res = await saveSecretKeyApi(saveData)

    if (res.code === 0) {
      uni.showToast({
        title: '保存成功',
        icon: 'success'
      })

      // 延迟返回上一页
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    } else {
      uni.showToast({
        title: res.msg || '保存失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('保存秘钥失败:', error)
    uni.showToast({
      title: '保存失败',
      icon: 'none'
    })
  } finally {
    saving.value = false
  }
}

// 初始化秘钥配置
onMounted(() => {
  getSecretKeyList()
})

</script>

<style lang="scss" scoped>
.secret-edit-page {
  background: #F5F5F5;
  min-height: 100vh;
}

.form-section {
  padding: 20px 32rpx;

  .form-item {
    display: flex;
    align-items: center;
    padding: 20rpx 16rpx;
    background: #ffffff;
    border-radius: 20rpx;
    margin-bottom: 20px;

    &:last-child {
      border-bottom: none;
    }

    .label {
      font-size: 30rpx;
      color: #1a1a1a;
      font-weight: 500;
      width: 120rpx;
      flex-shrink: 0;
    }

    .input {
      flex: 1;
      font-size: 28rpx;
      color: #1a1a1a;
      height: 44rpx;
      line-height: 44rpx;

      &.placeholder {
        color: #CCCCCC;
      }
    }
  }
}

.save-section {
  padding: 80rpx 32rpx;
  padding-bottom: calc(80rpx + env(safe-area-inset-bottom));
  /* Android兼容性修复 */
  padding-bottom: 80rpx;

  .save-btn {
    width: 100%;
    height: 96rpx;
    background: #3478f6;
    border-radius: 48rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;

    &.disabled {
      background: #CCCCCC;
    }

    .save-text {
      font-size: 32rpx;
      color: #ffffff;
      font-weight: 600;
    }
  }
}

/* 占位符样式 */
.placeholder {
  color: #CCCCCC !important;
}
</style>