{"version": 3, "file": "video-create.js", "sources": ["pages/my/video-create.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvbXkvdmlkZW8tY3JlYXRlLnZ1ZQ"], "sourcesContent": ["<template>\r\n\t<view class=\"video-create-page\">\r\n\t\t<!-- 视频预览区域 -->\r\n\t\t<view class=\"video-preview-container\">\r\n\t\t\t<view class=\"video-preview-card\">\r\n\t\t\t\t<!-- 数字人形象 -->\r\n\t\t\t\t<view class=\"digital-person-image\">\r\n                    <view class=\"person-box\">\r\n\t\t\t\t\t    <image class=\"person-avatar\" :src=\"currentAvatar\" mode=\"aspectFill\" />\r\n                        <!-- 提示 -->\r\n\t\t\t\t\t\t<view class=\"video-tips\" v-if=\"showSubtitle\">请在脚本区编辑您的正文</view>\r\n                        <!-- 更换按钮 -->\r\n                        <view class=\"change-btn\" @tap=\"openVideoModal\">\r\n                            <image class=\"change-icon\" src=\"/static/my/template_change.png\" mode=\"aspectFit\" />\r\n\t\t\t\t\t\t\t<text>更换</text>\r\n                        </view>\r\n                    </view>\r\n\t\t\t\t\t<!-- 视频时长和字幕控制 -->\r\n\t\t\t\t\t<view class=\"video-controls\">\r\n\t\t\t\t\t\t<view class=\"video-duration\"></view>\r\n\t\t\t\t\t\t<view class=\"subtitle-checkbox\" @tap=\"onSubtitleChange\">\r\n\t\t\t\t\t\t\t<image\r\n\t\t\t\t\t\t\t\tclass=\"subtitle-icon\"\r\n\t\t\t\t\t\t\t\t:src=\"showSubtitle ? '/static/my/select-icon1.png' : '/static/my/select-icon2.png'\"\r\n\t\t\t\t\t\t\t\tmode=\"aspectFit\"\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t<text class=\"subtitle-text\">字幕</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 文本内容区域 -->\r\n\t\t\t\t<view class=\"text-content-area\">\r\n\t\t\t\t\t<textarea\r\n\t\t\t\t\t\tclass=\"content-textarea\"\r\n\t\t\t\t\t\tv-model=\"textContent\"\r\n\t\t\t\t\t\tplaceholder=\"请输入文本内容...\"\r\n\t\t\t\t\t\t:maxlength=\"4000\"\r\n\t\t\t\t\t\t:show-confirm-bar=\"false\"\r\n\t\t\t\t\t/>\r\n\r\n\t\t\t\t\t<!-- 底部操作按钮 -->\r\n\t\t\t\t\t<!-- <view class=\"bottom-actions\">\r\n\t\t\t\t\t\t<view class=\"action-btn\" @tap=\"handleRandom\">\r\n\t\t\t\t\t\t\t<image class=\"action-icon\" src=\"/static/my/template_random.png\" mode=\"aspectFit\" />\r\n\t\t\t\t\t\t\t<text class=\"action-text\">随机</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"action-btn\" @tap=\"handleVoice\">\r\n\t\t\t\t\t\t\t<image class=\"action-icon\" src=\"/static/my/template_voice.png\" mode=\"aspectFit\" />\r\n\t\t\t\t\t\t\t<text class=\"action-text\">{{ currentVoiceText }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view> -->\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 立即生成按钮 -->\r\n\t\t\t<view class=\"generate-btn\" @tap=\"handleGenerateVideo\">\r\n\t\t\t\t<text class=\"generate-text\">立即生成</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 视频模板选择弹窗 -->\r\n\t\t<view v-if=\"showVideoModal\" class=\"video-overlay\" @tap=\"closeVideoModal\">\r\n\t\t\t<view class=\"video-modal\" @tap.stop>\r\n\t\t\t\t<view class=\"video-header\">\r\n\t\t\t\t\t<view class=\"primary-tabs\">\r\n\t\t\t\t\t\t<view\r\n\t\t\t\t\t\t\tv-for=\"(primaryTab, index) in primaryTabs\"\r\n\t\t\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\t\t\tclass=\"primary-tab\"\r\n\t\t\t\t\t\t\t:class=\"{ active: activePrimaryTab === index }\"\r\n\t\t\t\t\t\t\t@tap=\"switchPrimaryTab(index)\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<text class=\"primary-tab-text\">{{ primaryTab }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"confirm-icon-wrapper\" @tap=\"confirmTemplateSelection\">\r\n\t\t\t\t\t\t<image class=\"confirm-icon\" src=\"/static/my/template_submit.png\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 模板内容 -->\r\n\t\t\t\t<scroll-view\r\n\t\t\t\t\tscroll-y=\"true\"\r\n\t\t\t\t\tclass=\"template-scroll\"\r\n\t\t\t\t\t@scrolltolower=\"loadMoreTemplates\"\r\n\t\t\t\t\t:lower-threshold=\"50\"\r\n\t\t\t\t>\r\n\t\t\t\t\t<!-- 空状态 -->\r\n\t\t\t\t\t<view class=\"empty-state\" v-if=\"templateList.length === 0 && currentPage === 1 && !isLoadingMore\">\r\n\t\t\t\t\t\t<view class=\"empty-icon-placeholder\">\r\n\t\t\t\t\t\t\t<text class=\"empty-icon-text\">📋</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<text class=\"empty-text\">暂无数据</text>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<!-- 模板列表 -->\r\n\t\t\t\t\t<view class=\"video-grid\" v-else>\r\n\t\t\t\t\t\t<view\r\n\t\t\t\t\t\t\tv-for=\"(template, index) in templateList\"\r\n\t\t\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\t\t\tclass=\"video-template\"\r\n\t\t\t\t\t\t\t:class=\"{ selected: selectedTemplate && selectedTemplate.id === template.id }\"\r\n\t\t\t\t\t\t\t@tap=\"selectTemplate(template)\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<image class=\"template-image\" :src=\"getTemplateImage(template)\" mode=\"aspectFill\" />\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<!-- 加载更多提示 -->\r\n\t\t\t\t\t<view class=\"load-more\" v-if=\"templateList.length > 0\">\r\n\t\t\t\t\t\t<view v-if=\"isLoadingMore\" class=\"loading-text\">加载中...</view>\r\n\t\t\t\t\t\t<view v-else-if=\"hasMoreData\" class=\"loading-text\">上拉加载更多</view>\r\n\t\t\t\t\t\t<view v-else class=\"loading-text\">没有更多了</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</scroll-view>\r\n\r\n\t\t\t\t<view class=\"close-icon-wrapper\" @tap=\"closeVideoModal\">\r\n\t\t\t\t\t<image class=\"close-icon\" src=\"/static/my/popup-close.png\" mode=\"aspectFit\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 配音选择弹窗 -->\r\n\t\t<view v-if=\"showVoiceModal\" class=\"voice-overlay\" @tap=\"closeVoiceModal\">\r\n\t\t\t<view class=\"voice-modal\" @tap.stop>\r\n\t\t\t\t<view class=\"voice-header\">\r\n\t\t\t\t\t<text class=\"voice-title\">配音</text>\r\n\t\t\t\t\t<view class=\"confirm-icon-wrapper\" @tap=\"confirmVoiceSelection\">\r\n\t\t\t\t\t\t<image class=\"confirm-icon\" src=\"/static/my/template_submit.png\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"voice-content\">\r\n\t\t\t\t\t<view class=\"voice-tip\">\r\n\t\t\t\t\t\t<text class=\"tip-text\">恢复形象默认声音</text>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view class=\"voice-tabs\">\r\n\t\t\t\t\t<view\r\n\t\t\t\t\t\tv-for=\"(tab, index) in voiceTabs\"\r\n\t\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\t\tclass=\"voice-tab\"\r\n\t\t\t\t\t\t:class=\"{ active: activeVoiceTab === index }\"\r\n\t\t\t\t\t\t@tap=\"switchVoiceTab(index)\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<text class=\"tab-text\">{{ tab }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 配音内容 -->\r\n\t\t\t\t<view class=\"voice-grid\">\r\n\t\t\t\t\t<view\r\n\t\t\t\t\t\tv-for=\"(voice, index) in currentVoices\"\r\n\t\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\t\tclass=\"voice-item\"\r\n\t\t\t\t\t\t:class=\"{ selected: selectedVoice && selectedVoice.id === voice.id }\"\r\n\t\t\t\t\t\t@tap=\"selectVoice(voice)\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<text class=\"voice-name\">{{ voice.name }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 隐私协议弹窗 -->\r\n\t\t<view v-if=\"showPrivacyModal\" class=\"privacy-overlay\" @tap=\"closePrivacyModal\">\r\n\t\t\t<view class=\"privacy-modal\" @tap.stop>\r\n\t\t\t\t<view class=\"privacy-header\">\r\n\t\t\t\t\t<text class=\"privacy-title\">隐私协议</text>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<scroll-view scroll-y=\"true\" class=\"privacy-content\" show-scrollbar=\"false\">\r\n\t\t\t\t\t<view class=\"privacy-rich-content\">\r\n\t\t\t\t\t\t<rich-text\r\n\t\t\t\t\t\t\t:nodes=\"privacyContent\"\r\n\t\t\t\t\t\t\tclass=\"privacy-rich-text\"\r\n\t\t\t\t\t\t\t:user-select=\"true\"\r\n\t\t\t\t\t\t></rich-text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</scroll-view>\r\n\r\n\t\t\t\t<view class=\"privacy-footer\">\r\n\t\t\t\t\t<view\r\n\t\t\t\t\t\tclass=\"privacy-btn\"\r\n\t\t\t\t\t\t:class=\"{ disabled: !canConfirm }\"\r\n\t\t\t\t\t\t@tap=\"confirmPrivacy\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<text class=\"privacy-btn-text\">\r\n\t\t\t\t\t\t\t{{ canConfirm ? '我同意' : `我同意(${countdown}s)` }}\r\n\t\t\t\t\t\t</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, computed, onMounted } from 'vue'\r\nimport { userVoicePrivacyApi, commonPersonListApi, getMyPersonListApi, createVideoTaskApi, getUserInfoApi, calculatePointsApi } from '@/api/index.js'\r\nimport { useUserStore } from '@/stores/user.js'\r\n\r\nconst userStore = useUserStore()\r\n\r\n// 视频模板弹窗相关状态\r\nconst showVideoModal = ref(false)\r\nconst activePrimaryTab = ref(0)\r\nconst primaryTabs = ref(['模板', '定制数字人'])\r\nconst selectedTemplate = ref(null)\r\n\r\n// 模板列表数据\r\nconst templateList = ref([])\r\nconst currentPage = ref(1)\r\nconst pageSize = ref(10)\r\nconst isLoadingMore = ref(false)\r\nconst hasMoreData = ref(true)\r\n\r\n// 配音弹窗相关状态\r\nconst showVoiceModal = ref(false)\r\nconst activeVoiceTab = ref(0)\r\nconst voiceTabs = ref(['我的', '全部', '男性', '女性'])\r\nconst selectedVoice = ref(null)\r\nconst currentVoiceText = ref('情感女生') // 当前选中的配音文案\r\n\r\n// 当前显示的头像\r\nconst currentAvatar = ref('')\r\n\r\n// 字幕显示控制\r\nconst showSubtitle = ref(true)\r\n\r\n// 隐私协议弹窗相关状态\r\nconst showPrivacyModal = ref(false)\r\nconst privacyContent = ref('')\r\nconst canConfirm = ref(false)\r\nconst countdown = ref(3)\r\n\r\n// 文本内容\r\nconst textContent = ref('')\r\n\r\n// 加载模板列表\r\nconst loadTemplateList = async (page = 1, isLoadMore = false, autoSelectFirst = false) => {\r\n\tif (isLoadingMore.value && isLoadMore) return\r\n\r\n\ttry {\r\n\t\tif (isLoadMore) {\r\n\t\t\tisLoadingMore.value = true\r\n\t\t}\r\n\r\n\t\tlet res\r\n\t\tif (activePrimaryTab.value === 0) {\r\n\t\t\t// 模板 - 调用commonPersonListApi\r\n\t\t\tres = await commonPersonListApi({\r\n\t\t\t\tmerchantGuid: userStore.merchantGuid,\r\n\t\t\t\tpage: page,\r\n\t\t\t\tpageSize: pageSize.value\r\n\t\t\t})\r\n\t\t} else {\r\n\t\t\t// 定制数字人 - 调用getMyPersonListApi\r\n\t\t\tres = await getMyPersonListApi({\r\n\t\t\t\tmerchantGuid: userStore.merchantGuid,\r\n\t\t\t\tpage: page,\r\n\t\t\t\tpageSize: pageSize.value\r\n\t\t\t})\r\n\t\t}\r\n\r\n\t\tconst newList = res.data.list || []\r\n\t\t\tconsole.log(newList)\r\n\r\n\t\tif (page === 1) {\r\n\t\t\t// 第一页，直接替换\r\n\t\t\ttemplateList.value = newList\r\n\t\t\tconsole.log(autoSelectFirst)\r\n\t\t\tconsole.log(templateList.value)\r\n\r\n\t\t\t// 如果需要自动选中第一个模板\r\n\t\t\tif (autoSelectFirst && newList.length > 0) {\r\n\t\t\t\tconsole.log(newList[0])\r\n\t\t\t\tselectedTemplate.value = newList[0]\r\n\t\t\t\tcurrentAvatar.value = getTemplateImage(newList[0])\r\n\t\t\t\tconsole.log(currentAvatar.value)\r\n\t\t\t\tconsole.log(selectedTemplate.value)\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\t// 后续页，追加到列表\r\n\t\t\ttemplateList.value = [...templateList.value, ...newList]\r\n\t\t}\r\n\r\n\t\t// 判断是否还有更多数据\r\n\t\tconst pageInfo = res.data.pageInfo || {}\r\n\t\thasMoreData.value = page < (pageInfo.totalPage || 1)\r\n\r\n\t} catch (error) {\r\n\t\tconsole.error('获取模板列表失败:', error)\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '加载失败',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t} finally {\r\n\t\tif (isLoadMore) {\r\n\t\t\tisLoadingMore.value = false\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// 加载更多模板\r\nconst loadMoreTemplates = () => {\r\n\tif (!hasMoreData.value || isLoadingMore.value) return\r\n\r\n\tcurrentPage.value++\r\n\tloadTemplateList(currentPage.value, true)\r\n}\r\n\r\n// 获取模板图片\r\nconst getTemplateImage = (template) => {\r\n\tif (activePrimaryTab.value === 0) {\r\n\t\t// 定制数字人数据结构：figures[0].cover\r\n\t\treturn template.figures?.[0]?.cover\r\n\t} else {\r\n\t\t// 模板数据结构：picUrl\r\n\t\treturn template.picUrl\r\n\t}\r\n}\r\n\r\n// 获取图片信息的通用函数\r\nconst getImageInfo = async (imageSrc) => {\r\n\ttry {\r\n\t\tconst result = await uni.getImageInfo({\r\n\t\t\tsrc: imageSrc\r\n\t\t})\r\n\t\tconsole.log('图片信息',result)\r\n\t\treturn {\r\n\t\t\twidth: result.width,\r\n\t\t\theight: result.height,\r\n\t\t\tpath: result.path,\r\n\t\t\torientation: result.orientation,\r\n\t\t\ttype: result.type\r\n\t\t}\r\n\t} catch (error) {\r\n\t\tconsole.error('获取图片信息失败:', error)\r\n\t\treturn null\r\n\t}\r\n}\r\n\r\n// 配音数据 - 固定内容\r\nconst allVoices = ref([\r\n\t{ id: 1, name: '说话大爷', category: 'my', type: 'male' },\r\n\t{ id: 2, name: '做饭小哥', category: 'all', type: 'male' },\r\n\t{ id: 3, name: '爽朗阿姨', category: 'all', type: 'female' },\r\n\t{ id: 4, name: '高冷学妹', category: 'all', type: 'female' },\r\n\t{ id: 5, name: '激情朗诵小哥', category: 'all', type: 'male' },\r\n\t{ id: 6, name: '演讲女孩', category: 'all', type: 'female' },\r\n\t{ id: 7, name: '做饭小哥', category: 'all', type: 'male' },\r\n\t{ id: 8, name: '爽朗阿姨', category: 'all', type: 'female' },\r\n\t{ id: 9, name: '高冷学妹', category: 'all', type: 'female' },\r\n\t{ id: 10, name: '科普男声', category: 'all', type: 'male' },\r\n\t{ id: 11, name: '演讲女孩', category: 'all', type: 'female' },\r\n\t{ id: 12, name: '做饭小哥', category: 'all', type: 'male' },\r\n\t{ id: 13, name: '爽朗阿姨', category: 'all', type: 'female' },\r\n\t{ id: 14, name: '高冷学妹', category: 'all', type: 'female' },\r\n\t{ id: 15, name: '科普男声', category: 'all', type: 'male' },\r\n\t{ id: 16, name: '演讲女孩', category: 'all', type: 'female' },\r\n\t{ id: 17, name: '做饭小哥', category: 'all', type: 'male' },\r\n\t{ id: 18, name: '爽朗阿姨', category: 'all', type: 'female' },\r\n\t{ id: 19, name: '高冷学妹', category: 'all', type: 'female' },\r\n\t{ id: 20, name: '科普男声', category: 'all', type: 'male' }\r\n])\r\n\r\n\r\n\r\n// 当前显示的配音\r\nconst currentVoices = computed(() => {\r\n\tif (activeVoiceTab.value === 0) {\r\n\t\treturn allVoices.value.filter(v => v.category === 'my')\r\n\t} else if (activeVoiceTab.value === 1) {\r\n\t\treturn allVoices.value\r\n\t} else if (activeVoiceTab.value === 2) {\r\n\t\treturn allVoices.value.filter(v => v.type === 'male')\r\n\t} else {\r\n\t\treturn allVoices.value.filter(v => v.type === 'female')\r\n\t}\r\n})\r\n\r\n// 视频模板弹窗相关方法\r\nconst openVideoModal = () => {\r\n\tshowVideoModal.value = true\r\n\tselectedTemplate.value = null // 重置选中状态\r\n\tactivePrimaryTab.value = 0 // 重置到模板页面\r\n\t// 重置分页状态并加载第一页数据\r\n\tcurrentPage.value = 1\r\n\thasMoreData.value = true\r\n\tloadTemplateList(1)\r\n}\r\n\r\nconst closeVideoModal = () => {\r\n\tshowVideoModal.value = false\r\n\tselectedTemplate.value = null // 清除选中状态\r\n}\r\n\r\n// 仅关闭弹窗，不清除选中状态\r\nconst closeVideoModalOnly = () => {\r\n\tshowVideoModal.value = false\r\n}\r\n\r\nconst switchPrimaryTab = (index) => {\r\n\tactivePrimaryTab.value = index\r\n\tselectedTemplate.value = null // 切换一级菜单时清除选中状态\r\n\t// 重置分页状态并重新加载数据\r\n\tcurrentPage.value = 1\r\n\thasMoreData.value = true\r\n\tloadTemplateList(1)\r\n}\r\n\r\nconst selectTemplate = (template) => {\r\n\tselectedTemplate.value = template\r\n\tconsole.log('选择模板:', template)\r\n}\r\n\r\nconst confirmTemplateSelection = () => {\r\n\tconsole.log(selectedTemplate.value)\r\n\tif (selectedTemplate.value) {\r\n\t\tconsole.log('确认选择模板:', selectedTemplate.value)\r\n\t\t// 更新当前显示的头像\r\n\t\tcurrentAvatar.value = getTemplateImage(selectedTemplate.value)\r\n\r\n\t\tuni.showToast({\r\n\t\t\ttitle: `切换成功`,\r\n\t\t\ticon: 'success',\r\n\t\t\tduration: 2000\r\n\t\t})\r\n\t\t// 仅关闭弹窗，保留选中状态\r\n\t\tcloseVideoModalOnly()\r\n\t} else {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请先选择一个模板',\r\n\t\t\ticon: 'none',\r\n\t\t\tduration: 2000\r\n\t\t})\r\n\t}\r\n}\r\n\r\n// 处理字幕变化\r\nconst onSubtitleChange = () => {\r\n\tshowSubtitle.value = !showSubtitle.value\r\n}\r\n\r\n// 处理随机按钮点击\r\nconst handleRandom = () => {\r\n\t// 这里可以添加随机生成文本的逻辑\r\n\tuni.showToast({\r\n\t\ttitle: '随机生成中...',\r\n\t\ticon: 'loading',\r\n\t\tduration: 1500\r\n\t})\r\n}\r\n\r\n// 处理情感女生按钮点击\r\nconst handleVoice = () => {\r\n\tshowVoiceModal.value = true\r\n\tselectedVoice.value = null // 重置选中状态\r\n\tactiveVoiceTab.value = 0 // 重置到我的页面\r\n}\r\n\r\n// 配音弹窗相关方法\r\nconst openVoiceModal = () => {\r\n\tshowVoiceModal.value = true\r\n\tselectedVoice.value = null // 重置选中状态\r\n\tactiveVoiceTab.value = 0 // 重置到我的页面\r\n}\r\n\r\nconst closeVoiceModal = () => {\r\n\tshowVoiceModal.value = false\r\n\tselectedVoice.value = null // 清除选中状态\r\n}\r\n\r\nconst switchVoiceTab = (index) => {\r\n\tactiveVoiceTab.value = index\r\n}\r\n\r\nconst selectVoice = (voice) => {\r\n\tselectedVoice.value = voice\r\n}\r\n\r\nconst confirmVoiceSelection = () => {\r\n\tif (selectedVoice.value) {\r\n\t\t// 更新当前显示的配音文案\r\n\t\tcurrentVoiceText.value = selectedVoice.value.name\r\n\t\tuni.showToast({\r\n\t\t\ttitle: `已选择：${selectedVoice.value.name}`,\r\n\t\t\ticon: 'success',\r\n\t\t\tduration: 2000\r\n\t\t})\r\n\t\tcloseVoiceModal()\r\n\t} else {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请先选择一个配音',\r\n\t\t\ticon: 'none',\r\n\t\t\tduration: 2000\r\n\t\t})\r\n\t}\r\n}\r\n\r\n// 获取隐私协议内容\r\nconst getPrivacyContent = async () => {\r\n\ttry {\r\n\t\tconst res = await userVoicePrivacyApi()\r\n\t\tprivacyContent.value = res.data\r\n\t} catch (error) {\r\n\t\tconsole.error('获取隐私协议失败:', error)\r\n\t\tprivacyContent.value = '<p style=\"color: #ff6b6b;text-align: center;\">获取隐私协议内容失败，请稍后重试。</p>'\r\n\t}\r\n}\r\n\r\n// 开始倒计时\r\nconst startCountdown = () => {\r\n\tcanConfirm.value = false\r\n\tcountdown.value = 3\r\n\r\n\tconst timer = setInterval(() => {\r\n\t\tcountdown.value--\r\n\t\tif (countdown.value <= 0) {\r\n\t\t\tclearInterval(timer)\r\n\t\t\tcanConfirm.value = true\r\n\t\t}\r\n\t}, 1000)\r\n}\r\n\r\n// 处理生成视频\r\nconst handleGenerateVideo = async () => {\r\n\tconsole.log(selectedTemplate.value)\r\n\tif (!selectedTemplate.value) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请先选择模板',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\tif (!textContent.value.trim()) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请输入文本内容',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\r\n\ttry {\r\n\t\t// 显示检查中提示\r\n\t\tuni.showLoading({\r\n\t\t\ttitle: '检查算力中...',\r\n\t\t\tmask: true\r\n\t\t})\r\n\r\n\t\t// 准备计算算力的参数（与创建视频任务相同的参数）\r\n\t\tconst calculateParams = {\r\n\t\t\tmerchantGuid: userStore.merchantGuid,\r\n\t\t\ttext: textContent.value,\r\n\t\t\taudioType: 'tts', // 默认音频类型\r\n\t\t}\r\n\r\n\t\tif (activePrimaryTab.value === 0) {\r\n\t\t\t// 模板数据结构\r\n\t\t\tcalculateParams.personId = selectedTemplate.value.id\r\n\t\t\tcalculateParams.audioManId = selectedTemplate.value.audioManId\r\n\t\t\tcalculateParams.figureType = selectedTemplate.value.figures[0]?.type\r\n\t\t} else {\r\n\t\t\t// 定制数字人数据结构\r\n\t\t\tcalculateParams.personId = selectedTemplate.value.chanjingPersonId\r\n\t\t\tcalculateParams.audioManId = selectedTemplate.value.audioManId\r\n\t\t\tcalculateParams.figureType = ''\r\n\t\t}\r\n\r\n\t\t// 并行调用两个接口\r\n\t\tconst [userInfoRes, calculateRes] = await Promise.all([\r\n\t\t\tgetUserInfoApi(),\r\n\t\t\tcalculatePointsApi(calculateParams)\r\n\t\t])\r\n\r\n\t\tuni.hideLoading()\r\n\r\n\t\t// 检查用户算力是否足够\r\n\t\tconst userPoints = userInfoRes.data.chat_count || 0\r\n\t\tconst requiredPoints = calculateRes.data.requiredPoints || 0\r\n\r\n\t\tif (userPoints < requiredPoints) {\r\n\t\t\tuni.showModal({\r\n\t\t\t\ttitle: '算力不足',\r\n\t\t\t\tcontent: `当前算力：${userPoints}，所需算力：${requiredPoints}，请先充值算力`,\r\n\t\t\t\tshowCancel: false,\r\n\t\t\t\tconfirmText: '去充值',\r\n\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\tif (res.confirm) {}\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\treturn\r\n\t\t}\r\n\r\n\t\t// 算力足够，显示隐私协议弹窗\r\n\t\tshowPrivacyModal.value = true\r\n\t\tawait getPrivacyContent()\r\n\t\tstartCountdown()\r\n\r\n\t} catch (error) {\r\n\t\tuni.hideLoading()\r\n\t\tconsole.error('检查算力失败:', error)\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '检查失败，请重试',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t}\r\n}\r\n\r\n// 确认隐私协议\r\nconst confirmPrivacy = async () => {\r\n\tif (!canConfirm.value) return\r\n\r\n\tshowPrivacyModal.value = false\r\n\r\n\ttry {\r\n\t\t// 显示创建中提示\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '创建中...',\r\n\t\t\ticon: 'loading',\r\n\t\t\tduration: 0, // 持续显示直到手动隐藏\r\n\t\t\tmask: true\r\n\t\t})\r\n\r\n\t\t// 准备创建视频任务的参数\r\n\t\tconst createParams = {\r\n\t\t\tmerchantGuid: userStore.merchantGuid,\r\n\t\t\ttext: textContent.value,\r\n\t\t\taudioType: 'tts', // 默认音频类型\r\n\t\t}\r\n\t\tlet previewUrl = '';\r\n\t\tif (activePrimaryTab.value === 0) {\r\n\t\t\t// 模板数据结构\r\n\t\t\tcreateParams.personId = selectedTemplate.value.id\r\n\t\t\tcreateParams.audioManId = selectedTemplate.value.audioManId\r\n\t\t\tcreateParams.figureType = selectedTemplate.value.figures[0]?.type\r\n\t\t\tpreviewUrl = selectedTemplate.value.figures[0]?.cover\r\n\t\t} else {\r\n\t\t\t// 定制数字人数据结构\r\n\t\t\tcreateParams.personId = selectedTemplate.value.chanjingPersonId\r\n\t\t\tcreateParams.audioManId = selectedTemplate.value.audioManId\r\n\t\t\tcreateParams.figureType = ''\r\n\t\t\tpreviewUrl = selectedTemplate.value.picUrl\r\n\t\t}\r\n\t\t// 获取图片的宽高\r\n\t\tconsole.log(previewUrl)\r\n\r\n\t\t// 获取封面图的宽高\r\n\t\tconst imageInfo = await getImageInfo(previewUrl)\r\n\t\tif (imageInfo) {\r\n\t\t\tcreateParams.personWidth = imageInfo.width\r\n\t\t\tcreateParams.personHeight = imageInfo.height\r\n\t\t}\r\n\r\n\t\t\r\n\t\t// 调用创建视频任务API\r\n\t\tconst res = await createVideoTaskApi(createParams)\r\n\t\t// 隐藏loading提示\r\n\t\tuni.hideToast()\r\n\r\n\t\tif (res.code === 0) {\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '视频任务创建成功',\r\n\t\t\t\ticon: 'success'\r\n\t\t\t})\r\n\t\t\t// 跳转到视频生成进度页面\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: `/pages/my/video-progress?orderNo=${res.data.orderNo}&previewUrl=${previewUrl}`\r\n\t\t\t})\r\n\t\t} else {\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: res.msg || '创建失败',\r\n\t\t\t\ticon: 'none'\r\n\t\t\t})\r\n\t\t}\r\n\t} catch (error) {\r\n\t\t// 隐藏loading提示\r\n\t\tuni.hideToast()\r\n\t\tconsole.error('创建视频任务失败:', error)\r\n\t\tuni.showToast({\r\n\t\t\ttitle: error.meg || '创建失败，请重试',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t}\r\n}\r\n\r\n// 关闭隐私协议弹窗\r\nconst closePrivacyModal = () => {\r\n\tshowPrivacyModal.value = false\r\n}\r\n\r\n// 页面初始化\r\nonMounted(() => {\r\n\t// 页面加载时默认加载模板列表并选中第一个\r\n\tcurrentPage.value = 1\r\n\thasMoreData.value = true\r\n\tactivePrimaryTab.value = 0 // 默认选中模板菜单\r\n\tloadTemplateList(1, false, true) // 第三个参数为true表示自动选中第一个\r\n})\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.video-create-page {\r\n\tbackground: #141115;\r\n\tmin-height: 100vh;\r\n\tpadding: 32rpx;\r\n    box-sizing: border-box;\r\n\r\n\t.video-preview-container {\r\n\t\t.video-preview-card {\r\n\t\t\tbackground: #141215;\r\n\t\t\tborder-radius: 24rpx;\r\n\t\t\tpadding: 0;\r\n\t\t\tmargin-bottom: 32rpx;\r\n\t\t\tposition: relative;\r\n\t\t\toverflow: hidden;\r\n\r\n\t\t\t.digital-person-image {\r\n\t\t\t\twidth: 100%;\r\n\r\n                .person-box{\r\n                    width: 390rpx;\r\n                    height: 690rpx;\r\n                    margin: auto;\r\n                    position: relative;\r\n                    \r\n                    .person-avatar {\r\n                        width: 100%;\r\n                        height: 100%;\r\n                        object-fit: cover;\r\n                    }\r\n\t\t\t\t\t.video-tips{\r\n\t\t\t\t\t\tposition: absolute;\r\n                        bottom: 80rpx;\r\n                        right: 0;\r\n\t\t\t\t\t\tleft: 0;\r\n\t\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\t\tcolor: white;\r\n\t\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t\t\t/* 文字描边效果 */\r\n\t\t\t\t\t\ttext-shadow:\r\n\t\t\t\t\t\t\t-1px -1px 0 #000,\r\n\t\t\t\t\t\t\t1px -1px 0 #000,\r\n\t\t\t\t\t\t\t-1px 1px 0 #000,\r\n\t\t\t\t\t\t\t1px 1px 0 #000,\r\n\t\t\t\t\t\t\t-2px 0 0 #000,\r\n\t\t\t\t\t\t\t2px 0 0 #000,\r\n\t\t\t\t\t\t\t0 -2px 0 #000,\r\n\t\t\t\t\t\t\t0 2px 0 #000;\r\n\t\t\t\t\t\t/* 或者使用webkit的描边属性（备选方案） */\r\n\t\t\t\t\t\t// -webkit-text-stroke: 1px #000;\r\n\t\t\t\t\t}\r\n                    .change-btn {\r\n                        position: absolute;\r\n                        bottom: 14rpx;\r\n                        right: 14rpx;\r\n                        padding: 7rpx 14rpx;\r\n                        background: rgba(0, 0, 0, 0.9);\r\n                        color: white;\r\n                        font-size: 24rpx;\r\n                        font-weight: 400;\r\n                        border-radius: 12rpx;\r\n                        display: flex;\r\n                        align-items: center;\r\n                        justify-content: center;\r\n\r\n                        .change-icon {\r\n                            width: 30rpx;\r\n                            height: 30rpx;\r\n                        }\r\n                    }\r\n                }\r\n\t\t\t\t\r\n\r\n\t\t\t\t.video-controls {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n                    justify-content: space-between;\r\n                    margin: 30rpx auto;\r\n\r\n\t\t\t\t\t.video-duration {\r\n\t\t\t\t\t\tcolor: #ffffff;\r\n\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.subtitle-checkbox {\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\talign-items: center;\r\n\r\n\t\t\t\t\t\t.subtitle-icon {\r\n\t\t\t\t\t\t\twidth: 30rpx;\r\n\t\t\t\t\t\t\theight: 30rpx;\r\n\t\t\t\t\t\t\tmargin-right: 8rpx;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t.subtitle-text {\r\n\t\t\t\t\t\t\tcolor: #ffffff;\r\n\t\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.text-content-area {\r\n\t\t\t\tbackground: #212121;\r\n                border-radius: 25rpx;\r\n\t\t\t\tpadding: 32rpx;\r\n\t\t\t\tmargin: 0;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tflex-direction: column;\r\n\r\n\t\t\t\t.content-textarea {\r\n\t\t\t\t\tfont-size: 28rpx;\r\n                    font-weight: 400;\r\n\t\t\t\t\tline-height: 1.6;\r\n\t\t\t\t\tcolor: #fff;\r\n                    width: 100%;\r\n\t\t\t\t\theight: 296rpx;\r\n\t\t\t\t\tborder: none;\r\n\t\t\t\t\toutline: none;\r\n\t\t\t\t\tresize: none;\r\n\t\t\t\t\tbackground: transparent;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.bottom-actions {\r\n\t\t\t\t\tdisplay: flex;\r\n                    align-items: center;\r\n\t\t\t\t\tjustify-content: space-between;\r\n                    margin-top: 20rpx;\r\n\r\n\t\t\t\t\t.action-btn {\r\n                        display: flex;\r\n                        align-items: center;\r\n                        justify-content: space-between;\r\n\t\t\t\t\t\tpadding: 8rpx 14rpx;\r\n\t\t\t\t\t\tbackground: #333333;\r\n\t\t\t\t\t\tborder-radius: 12rpx;\r\n                        color: white;\r\n\r\n\t\t\t\t\t\t&:active {\r\n\t\t\t\t\t\t\tbackground: #e9ecef;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t.action-icon {\r\n\t\t\t\t\t\t\twidth: 30rpx;\r\n\t\t\t\t\t\t\theight: 30rpx;\r\n                            margin-right: 5rpx;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t.action-text {\r\n\t\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\t\tcolor: white;\r\n\t\t\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.generate-btn {\r\n\t\t\tbackground: #3478f6;\r\n\t\t\tborder-radius: 48rpx;\r\n\t\t\tpadding: 32rpx;\r\n\t\t\ttext-align: center;\r\n\r\n\t\t\t.generate-text {\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tcolor: #ffffff;\r\n\t\t\t\tfont-weight: 600;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t// 视频模板弹窗样式\r\n\t.video-overlay {\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tbackground: rgba(0, 0, 0, 0.7);\r\n\t\tdisplay: flex;\r\n\t\talign-items: flex-end;\r\n\t\tjustify-content: center;\r\n\t\tz-index: 9999;\r\n\t}\r\n\r\n\t.video-modal {\r\n\t\tbackground: #232325;\r\n\t\tborder-radius: 32rpx 32rpx 0 0;\r\n\t\tpadding: 48rpx 30rpx 32rpx;\r\n\t\twidth: 100%;\r\n\t\tmax-height: 80vh;\r\n\t\tposition: relative;\r\n\t\tcolor: #ffffff;\r\n\r\n\t\t.video-header {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\tmargin-bottom: 32rpx;\r\n\t\t\tpadding: 0 16rpx;\r\n\t\t\tposition: relative;\r\n\r\n\t\t\t.primary-tabs {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\r\n\t\t\t\t.primary-tab {\r\n\t\t\t\t\tmargin-right: 32rpx;\r\n                    padding-bottom: 18rpx;\r\n\t\t\t\t\t&.active {\r\n\t\t\t\t\t\t.primary-tab-text {\r\n\t\t\t\t\t\t\tcolor: #ffffff;\r\n\t\t\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t\t\t\tborder-bottom: 3rpx solid #ffffff;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.primary-tab-text {\r\n\t\t\t\t\t\tfont-size: 30rpx;\r\n                        font-weight: 400;\r\n\t\t\t\t\t\tcolor: #999999;\r\n\t\t\t\t\t\ttransition: color 0.3s ease;\r\n\t\t\t\t\t\tpadding-bottom: 18rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.confirm-icon-wrapper {\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tright: 16rpx;\r\n\t\t\t\ttop: 50%;\r\n\t\t\t\ttransform: translateY(-50%);\r\n\t\t\t\twidth: 60rpx;\r\n\t\t\t\theight: 60rpx;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: center;\r\n\r\n\t\t\t\t.confirm-icon {\r\n\t\t\t\t\twidth: 60rpx;\r\n\t\t\t\t\theight: 60rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\r\n\r\n\t\t.template-scroll {\r\n\t\t\theight: 800rpx;\r\n\t\t\tpadding: 0 16rpx;\r\n\t\t}\r\n\r\n\t\t.empty-state {\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-direction: column;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\t\t\theight: 600rpx;\r\n\t\t\tpadding: 40rpx;\r\n\r\n\t\t\t.empty-icon-placeholder {\r\n\t\t\t\twidth: 120rpx;\r\n\t\t\t\theight: 120rpx;\r\n\t\t\t\tmargin-bottom: 24rpx;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\tborder-radius: 60rpx;\r\n\r\n\t\t\t\t.empty-icon-text {\r\n\t\t\t\t\tfont-size: 60rpx;\r\n\t\t\t\t\topacity: 0.6;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.empty-text {\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tcolor: #666666;\r\n\t\t\t\tmargin-bottom: 12rpx;\r\n\t\t\t\tfont-weight: 500;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.load-more {\r\n\t\t\tpadding: 32rpx 0;\r\n\t\t\ttext-align: center;\r\n\r\n\t\t\t.loading-text {\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tcolor: #999999;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.video-grid {\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-wrap: wrap;\r\n\t\t\talign-content: flex-start;\r\n\r\n\t\t\t.video-template {\r\n\t\t\t\twidth: 220rpx;\r\n\t\t\t\theight: 350rpx;\r\n\t\t\t\tborder-radius: 16rpx;\r\n\t\t\t\toverflow: hidden;\r\n\t\t\t\tborder: 4rpx solid transparent;\r\n\t\t\t\ttransition: border-color 0.3s ease;\r\n\t\t\t\tmargin-bottom: 16rpx;\r\n                box-sizing: border-box;\r\n\r\n\t\t\t\t&.selected {\r\n\t\t\t\t\tborder-color: #3478f6;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.template-image {\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\theight: 100%;\r\n\t\t\t\t\tobject-fit: cover;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t.video-template:not(:nth-child(3n)){\r\n\t\t\t\tmargin-right: 12rpx;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\r\n\r\n\t\t.close-icon-wrapper {\r\n\t\t\tposition: absolute;\r\n\t\t\tbottom: -80rpx;\r\n\t\t\tleft: 50%;\r\n\t\t\ttransform: translateX(-50%);\r\n\t\t\twidth: 45rpx;\r\n\t\t\theight: 45rpx;\r\n\t\t\tborder-radius: 50%;\r\n\r\n\t\t\t.close-icon {\r\n\t\t\t\twidth: 45rpx;\r\n\t\t\t\theight: 45rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n    // 配音弹窗样式\r\n    .voice-overlay {\r\n        position: fixed;\r\n        top: 0;\r\n        left: 0;\r\n        right: 0;\r\n        bottom: 0;\r\n        background: rgba(0, 0, 0, 0.7);\r\n        display: flex;\r\n        align-items: flex-end;\r\n        justify-content: center;\r\n        z-index: 9999;\r\n    }\r\n\r\n    .voice-modal {\r\n        background: #232325;\r\n        border-radius: 32rpx 32rpx 0 0;\r\n        padding: 0;\r\n        width: 100%;\r\n        max-height: 80vh;\r\n        position: relative;\r\n        color: #ffffff;\r\n\r\n        .voice-header {\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            padding: 32rpx 30rpx 28rpx;\r\n            position: relative;\r\n            border-bottom: 2rpx solid #2A2A2A;\r\n\r\n            .voice-title {\r\n                font-size: 30rpx;\r\n                font-weight: 500;\r\n                color: #ffffff;\r\n            }\r\n\r\n            .confirm-icon-wrapper {\r\n                position: absolute;\r\n                right: 30rpx;\r\n                bottom: 8rpx;\r\n                width: 60rpx;\r\n                height: 60rpx;\r\n\r\n                .confirm-icon {\r\n                    width: 100%;\r\n                    height: 100%;\r\n                    display: block;\r\n                }\r\n            }\r\n        }\r\n\r\n        .voice-content {\r\n            padding: 32rpx 30rpx;\r\n\r\n            .voice-tip {\r\n                margin-bottom: 32rpx;\r\n\r\n                .tip-text {\r\n                    font-size: 24rpx;\r\n                    color: #999999;\r\n                    font-weight: 400;\r\n                }\r\n            }\r\n        }\r\n\r\n        .voice-tabs {\r\n            display: flex;\r\n            margin-bottom: 32rpx;\r\n\r\n            .voice-tab {\r\n                background: #333333;\r\n                border-radius: 32rpx;\r\n                padding: 6rpx 18rpx;\r\n                margin-right: 16rpx;\r\n\r\n                &.active {\r\n                    background: #585858;\r\n                    border: 2rpx solid #8D8D8D;\r\n\r\n                    .tab-text {\r\n                        font-weight: 500;\r\n                        font-size: 30rpx;\r\n                    }\r\n                }\r\n\r\n                .tab-text {\r\n                    font-weight: 400;\r\n                    font-size: 26rpx;\r\n                    color: #ffffff;\r\n                }\r\n            }\r\n        }\r\n\r\n        .voice-grid {\r\n            display: flex;\r\n            align-content: flex-start;\r\n            flex-wrap: wrap;\r\n            height: 600rpx;\r\n            overflow-y: auto;\r\n\r\n            .voice-item {\r\n                background: #333333;\r\n                border-radius: 16rpx;\r\n                padding: 20rpx 6rpx;\r\n                text-align: center;\r\n                border: 4rpx solid transparent;\r\n                transition: all 0.3s ease;\r\n                box-sizing: border-box;\r\n                width: 120rpx;\r\n                height: 120rpx;\r\n                overflow: hidden;\r\n                display: flex;\r\n                align-items: center;\r\n                margin-bottom: 20rpx;\r\n\r\n                &.selected {\r\n                    border-color: #3478f6;\r\n\r\n                    .voice-name {\r\n                        color: #AFC6FF;\r\n                    }\r\n                }\r\n\r\n                .voice-name {\r\n                    font-size: 24rpx;\r\n                    color: #ffffff;\r\n                    font-weight: 400;\r\n                    word-break: break-all;\r\n                }\r\n            }\r\n            .voice-item:not(:nth-child(5n)){\r\n                margin-right: 22rpx;\r\n            }\r\n        }\r\n    }\r\n\r\n\t// 隐私协议弹窗样式\r\n\t.privacy-overlay {\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tbackground: rgba(0, 0, 0, 0.7);\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tz-index: 9999;\r\n\t}\r\n\r\n\t.privacy-modal {\r\n\t\tbackground: #ffffff;\r\n\t\tborder-radius: 24rpx;\r\n\t\twidth: 600rpx;\r\n\t\tmax-width: 90vw;\r\n\t\tmax-height: 80vh;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\toverflow: hidden;\r\n\r\n\t\t.privacy-header {\r\n\t\t\tpadding: 40rpx 40rpx 20rpx;\r\n\t\t\ttext-align: center;\r\n\r\n\t\t\t.privacy-title {\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tfont-weight: 600;\r\n\t\t\t\tcolor: #333333;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.privacy-content {\r\n\t\t\tflex: 1;\r\n\t\t\tpadding: 20rpx 40rpx 30rpx;\r\n\t\t\tmax-height: 600rpx;\r\n\t\t\tbox-sizing: border-box;\r\n\t\t\toverflow: hidden;\r\n\r\n\t\t\t.privacy-rich-content {\r\n\t\t\t\t.privacy-rich-text {\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\tline-height: 1.6;\r\n\t\t\t\t\tcolor: #666666;\r\n\t\t\t\t\tword-break: break-word;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.privacy-footer {\r\n\t\t\tpadding: 30rpx 40rpx 40rpx;\r\n\r\n\t\t\t.privacy-btn {\r\n\t\t\t\tbackground: #3478f6;\r\n\t\t\t\tborder-radius: 48rpx;\r\n\t\t\t\tpadding: 24rpx;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\ttransition: all 0.3s ease;\r\n\r\n\t\t\t\t&.disabled {\r\n\t\t\t\t\tbackground: #cccccc;\r\n\t\t\t\t\tcursor: not-allowed;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.privacy-btn-text {\r\n\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\tcolor: #ffffff;\r\n\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n</style>", "import MiniProgramPage from 'E:/youngProject/agent-mini-ui/pages/my/video-create.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useUserStore", "ref", "commonPersonListApi", "getMyPersonListApi", "uni", "computed", "userVoicePrivacyApi", "getUserInfoApi", "calculatePointsApi", "createVideoTaskApi", "onMounted"], "mappings": ";;;;;;;;AA6MA,UAAM,YAAYA,YAAAA,aAAc;AAGhC,UAAM,iBAAiBC,cAAG,IAAC,KAAK;AAChC,UAAM,mBAAmBA,cAAG,IAAC,CAAC;AAC9B,UAAM,cAAcA,cAAG,IAAC,CAAC,MAAM,OAAO,CAAC;AACvC,UAAM,mBAAmBA,cAAG,IAAC,IAAI;AAGjC,UAAM,eAAeA,cAAG,IAAC,EAAE;AAC3B,UAAM,cAAcA,cAAG,IAAC,CAAC;AACzB,UAAM,WAAWA,cAAG,IAAC,EAAE;AACvB,UAAM,gBAAgBA,cAAG,IAAC,KAAK;AAC/B,UAAM,cAAcA,cAAG,IAAC,IAAI;AAG5B,UAAM,iBAAiBA,cAAG,IAAC,KAAK;AAChC,UAAM,iBAAiBA,cAAG,IAAC,CAAC;AAC5B,UAAM,YAAYA,cAAG,IAAC,CAAC,MAAM,MAAM,MAAM,IAAI,CAAC;AAC9C,UAAM,gBAAgBA,cAAG,IAAC,IAAI;AAC9B,UAAM,mBAAmBA,cAAG,IAAC,MAAM;AAGnC,UAAM,gBAAgBA,cAAG,IAAC,EAAE;AAG5B,UAAM,eAAeA,cAAG,IAAC,IAAI;AAG7B,UAAM,mBAAmBA,cAAG,IAAC,KAAK;AAClC,UAAM,iBAAiBA,cAAG,IAAC,EAAE;AAC7B,UAAM,aAAaA,cAAG,IAAC,KAAK;AAC5B,UAAM,YAAYA,cAAG,IAAC,CAAC;AAGvB,UAAM,cAAcA,cAAG,IAAC,EAAE;AAG1B,UAAM,mBAAmB,OAAO,OAAO,GAAG,aAAa,OAAO,kBAAkB,UAAU;AACzF,UAAI,cAAc,SAAS;AAAY;AAEvC,UAAI;AACH,YAAI,YAAY;AACf,wBAAc,QAAQ;AAAA,QACtB;AAED,YAAI;AACJ,YAAI,iBAAiB,UAAU,GAAG;AAEjC,gBAAM,MAAMC,UAAAA,oBAAoB;AAAA,YAC/B,cAAc,UAAU;AAAA,YACxB;AAAA,YACA,UAAU,SAAS;AAAA,UACvB,CAAI;AAAA,QACJ,OAAS;AAEN,gBAAM,MAAMC,UAAAA,mBAAmB;AAAA,YAC9B,cAAc,UAAU;AAAA,YACxB;AAAA,YACA,UAAU,SAAS;AAAA,UACvB,CAAI;AAAA,QACD;AAED,cAAM,UAAU,IAAI,KAAK,QAAQ,CAAE;AAClCC,sBAAAA,MAAA,MAAA,OAAA,oCAAY,OAAO;AAEpB,YAAI,SAAS,GAAG;AAEf,uBAAa,QAAQ;AACrBA,wBAAAA,MAAA,MAAA,OAAA,oCAAY,eAAe;AAC3BA,wBAAAA,MAAY,MAAA,OAAA,oCAAA,aAAa,KAAK;AAG9B,cAAI,mBAAmB,QAAQ,SAAS,GAAG;AAC1CA,0BAAA,MAAA,MAAA,OAAA,oCAAY,QAAQ,CAAC,CAAC;AACtB,6BAAiB,QAAQ,QAAQ,CAAC;AAClC,0BAAc,QAAQ,iBAAiB,QAAQ,CAAC,CAAC;AACjDA,0BAAAA,MAAY,MAAA,OAAA,oCAAA,cAAc,KAAK;AAC/BA,0BAAAA,MAAY,MAAA,OAAA,oCAAA,iBAAiB,KAAK;AAAA,UAClC;AAAA,QACJ,OAAS;AAEN,uBAAa,QAAQ,CAAC,GAAG,aAAa,OAAO,GAAG,OAAO;AAAA,QACvD;AAGD,cAAM,WAAW,IAAI,KAAK,YAAY,CAAE;AACxC,oBAAY,QAAQ,QAAQ,SAAS,aAAa;AAAA,MAElD,SAAQ,OAAO;AACfA,sBAAAA,yDAAc,aAAa,KAAK;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACT,CAAG;AAAA,MACH,UAAW;AACT,YAAI,YAAY;AACf,wBAAc,QAAQ;AAAA,QACtB;AAAA,MACD;AAAA,IACF;AAGA,UAAM,oBAAoB,MAAM;AAC/B,UAAI,CAAC,YAAY,SAAS,cAAc;AAAO;AAE/C,kBAAY;AACZ,uBAAiB,YAAY,OAAO,IAAI;AAAA,IACzC;AAGA,UAAM,mBAAmB,CAAC,aAAa;;AACtC,UAAI,iBAAiB,UAAU,GAAG;AAEjC,gBAAO,oBAAS,YAAT,mBAAmB,OAAnB,mBAAuB;AAAA,MAChC,OAAQ;AAEN,eAAO,SAAS;AAAA,MAChB;AAAA,IACF;AAGA,UAAM,eAAe,OAAO,aAAa;AACxC,UAAI;AACH,cAAM,SAAS,MAAMA,cAAG,MAAC,aAAa;AAAA,UACrC,KAAK;AAAA,QACR,CAAG;AACDA,sBAAAA,MAAY,MAAA,OAAA,oCAAA,QAAO,MAAM;AACzB,eAAO;AAAA,UACN,OAAO,OAAO;AAAA,UACd,QAAQ,OAAO;AAAA,UACf,MAAM,OAAO;AAAA,UACb,aAAa,OAAO;AAAA,UACpB,MAAM,OAAO;AAAA,QACb;AAAA,MACD,SAAQ,OAAO;AACfA,sBAAAA,yDAAc,aAAa,KAAK;AAChC,eAAO;AAAA,MACP;AAAA,IACF;AAGA,UAAM,YAAYH,cAAAA,IAAI;AAAA,MACrB,EAAE,IAAI,GAAG,MAAM,QAAQ,UAAU,MAAM,MAAM,OAAQ;AAAA,MACrD,EAAE,IAAI,GAAG,MAAM,QAAQ,UAAU,OAAO,MAAM,OAAQ;AAAA,MACtD,EAAE,IAAI,GAAG,MAAM,QAAQ,UAAU,OAAO,MAAM,SAAU;AAAA,MACxD,EAAE,IAAI,GAAG,MAAM,QAAQ,UAAU,OAAO,MAAM,SAAU;AAAA,MACxD,EAAE,IAAI,GAAG,MAAM,UAAU,UAAU,OAAO,MAAM,OAAQ;AAAA,MACxD,EAAE,IAAI,GAAG,MAAM,QAAQ,UAAU,OAAO,MAAM,SAAU;AAAA,MACxD,EAAE,IAAI,GAAG,MAAM,QAAQ,UAAU,OAAO,MAAM,OAAQ;AAAA,MACtD,EAAE,IAAI,GAAG,MAAM,QAAQ,UAAU,OAAO,MAAM,SAAU;AAAA,MACxD,EAAE,IAAI,GAAG,MAAM,QAAQ,UAAU,OAAO,MAAM,SAAU;AAAA,MACxD,EAAE,IAAI,IAAI,MAAM,QAAQ,UAAU,OAAO,MAAM,OAAQ;AAAA,MACvD,EAAE,IAAI,IAAI,MAAM,QAAQ,UAAU,OAAO,MAAM,SAAU;AAAA,MACzD,EAAE,IAAI,IAAI,MAAM,QAAQ,UAAU,OAAO,MAAM,OAAQ;AAAA,MACvD,EAAE,IAAI,IAAI,MAAM,QAAQ,UAAU,OAAO,MAAM,SAAU;AAAA,MACzD,EAAE,IAAI,IAAI,MAAM,QAAQ,UAAU,OAAO,MAAM,SAAU;AAAA,MACzD,EAAE,IAAI,IAAI,MAAM,QAAQ,UAAU,OAAO,MAAM,OAAQ;AAAA,MACvD,EAAE,IAAI,IAAI,MAAM,QAAQ,UAAU,OAAO,MAAM,SAAU;AAAA,MACzD,EAAE,IAAI,IAAI,MAAM,QAAQ,UAAU,OAAO,MAAM,OAAQ;AAAA,MACvD,EAAE,IAAI,IAAI,MAAM,QAAQ,UAAU,OAAO,MAAM,SAAU;AAAA,MACzD,EAAE,IAAI,IAAI,MAAM,QAAQ,UAAU,OAAO,MAAM,SAAU;AAAA,MACzD,EAAE,IAAI,IAAI,MAAM,QAAQ,UAAU,OAAO,MAAM,OAAQ;AAAA,IACxD,CAAC;AAKD,UAAM,gBAAgBI,cAAQ,SAAC,MAAM;AACpC,UAAI,eAAe,UAAU,GAAG;AAC/B,eAAO,UAAU,MAAM,OAAO,OAAK,EAAE,aAAa,IAAI;AAAA,MACxD,WAAY,eAAe,UAAU,GAAG;AACtC,eAAO,UAAU;AAAA,MACnB,WAAY,eAAe,UAAU,GAAG;AACtC,eAAO,UAAU,MAAM,OAAO,OAAK,EAAE,SAAS,MAAM;AAAA,MACtD,OAAQ;AACN,eAAO,UAAU,MAAM,OAAO,OAAK,EAAE,SAAS,QAAQ;AAAA,MACtD;AAAA,IACF,CAAC;AAGD,UAAM,iBAAiB,MAAM;AAC5B,qBAAe,QAAQ;AACvB,uBAAiB,QAAQ;AACzB,uBAAiB,QAAQ;AAEzB,kBAAY,QAAQ;AACpB,kBAAY,QAAQ;AACpB,uBAAiB,CAAC;AAAA,IACnB;AAEA,UAAM,kBAAkB,MAAM;AAC7B,qBAAe,QAAQ;AACvB,uBAAiB,QAAQ;AAAA,IAC1B;AAGA,UAAM,sBAAsB,MAAM;AACjC,qBAAe,QAAQ;AAAA,IACxB;AAEA,UAAM,mBAAmB,CAAC,UAAU;AACnC,uBAAiB,QAAQ;AACzB,uBAAiB,QAAQ;AAEzB,kBAAY,QAAQ;AACpB,kBAAY,QAAQ;AACpB,uBAAiB,CAAC;AAAA,IACnB;AAEA,UAAM,iBAAiB,CAAC,aAAa;AACpC,uBAAiB,QAAQ;AACzBD,oBAAAA,uDAAY,SAAS,QAAQ;AAAA,IAC9B;AAEA,UAAM,2BAA2B,MAAM;AACtCA,oBAAAA,MAAA,MAAA,OAAA,oCAAY,iBAAiB,KAAK;AAClC,UAAI,iBAAiB,OAAO;AAC3BA,sBAAY,MAAA,MAAA,OAAA,oCAAA,WAAW,iBAAiB,KAAK;AAE7C,sBAAc,QAAQ,iBAAiB,iBAAiB,KAAK;AAE7DA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QACb,CAAG;AAED,4BAAqB;AAAA,MACvB,OAAQ;AACNA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QACb,CAAG;AAAA,MACD;AAAA,IACF;AAGA,UAAM,mBAAmB,MAAM;AAC9B,mBAAa,QAAQ,CAAC,aAAa;AAAA,IACpC;AA0BA,UAAM,kBAAkB,MAAM;AAC7B,qBAAe,QAAQ;AACvB,oBAAc,QAAQ;AAAA,IACvB;AAEA,UAAM,iBAAiB,CAAC,UAAU;AACjC,qBAAe,QAAQ;AAAA,IACxB;AAEA,UAAM,cAAc,CAAC,UAAU;AAC9B,oBAAc,QAAQ;AAAA,IACvB;AAEA,UAAM,wBAAwB,MAAM;AACnC,UAAI,cAAc,OAAO;AAExB,yBAAiB,QAAQ,cAAc,MAAM;AAC7CA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO,OAAO,cAAc,MAAM,IAAI;AAAA,UACtC,MAAM;AAAA,UACN,UAAU;AAAA,QACb,CAAG;AACD,wBAAiB;AAAA,MACnB,OAAQ;AACNA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QACb,CAAG;AAAA,MACD;AAAA,IACF;AAGA,UAAM,oBAAoB,YAAY;AACrC,UAAI;AACH,cAAM,MAAM,MAAME,8BAAqB;AACvC,uBAAe,QAAQ,IAAI;AAAA,MAC3B,SAAQ,OAAO;AACfF,sBAAAA,yDAAc,aAAa,KAAK;AAChC,uBAAe,QAAQ;AAAA,MACvB;AAAA,IACF;AAGA,UAAM,iBAAiB,MAAM;AAC5B,iBAAW,QAAQ;AACnB,gBAAU,QAAQ;AAElB,YAAM,QAAQ,YAAY,MAAM;AAC/B,kBAAU;AACV,YAAI,UAAU,SAAS,GAAG;AACzB,wBAAc,KAAK;AACnB,qBAAW,QAAQ;AAAA,QACnB;AAAA,MACD,GAAE,GAAI;AAAA,IACR;AAGA,UAAM,sBAAsB,YAAY;;AACvCA,oBAAAA,MAAA,MAAA,OAAA,oCAAY,iBAAiB,KAAK;AAClC,UAAI,CAAC,iBAAiB,OAAO;AAC5BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACT,CAAG;AACD;AAAA,MACA;AACD,UAAI,CAAC,YAAY,MAAM,QAAQ;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACT,CAAG;AACD;AAAA,MACA;AAED,UAAI;AAEHA,sBAAAA,MAAI,YAAY;AAAA,UACf,OAAO;AAAA,UACP,MAAM;AAAA,QACT,CAAG;AAGD,cAAM,kBAAkB;AAAA,UACvB,cAAc,UAAU;AAAA,UACxB,MAAM,YAAY;AAAA,UAClB,WAAW;AAAA;AAAA,QACX;AAED,YAAI,iBAAiB,UAAU,GAAG;AAEjC,0BAAgB,WAAW,iBAAiB,MAAM;AAClD,0BAAgB,aAAa,iBAAiB,MAAM;AACpD,0BAAgB,cAAa,sBAAiB,MAAM,QAAQ,CAAC,MAAhC,mBAAmC;AAAA,QACnE,OAAS;AAEN,0BAAgB,WAAW,iBAAiB,MAAM;AAClD,0BAAgB,aAAa,iBAAiB,MAAM;AACpD,0BAAgB,aAAa;AAAA,QAC7B;AAGD,cAAM,CAAC,aAAa,YAAY,IAAI,MAAM,QAAQ,IAAI;AAAA,UACrDG,yBAAgB;AAAA,UAChBC,UAAAA,mBAAmB,eAAe;AAAA,QACrC,CAAG;AAEDJ,sBAAAA,MAAI,YAAa;AAGjB,cAAM,aAAa,YAAY,KAAK,cAAc;AAClD,cAAM,iBAAiB,aAAa,KAAK,kBAAkB;AAE3D,YAAI,aAAa,gBAAgB;AAChCA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,SAAS,QAAQ,UAAU,SAAS,cAAc;AAAA,YAClD,YAAY;AAAA,YACZ,aAAa;AAAA,YACb,SAAS,CAAC,QAAQ;AACjB,kBAAI,IAAI,SAAS;AAAA,cAAE;AAAA,YACnB;AAAA,UACL,CAAI;AACD;AAAA,QACA;AAGD,yBAAiB,QAAQ;AACzB,cAAM,kBAAmB;AACzB,uBAAgB;AAAA,MAEhB,SAAQ,OAAO;AACfA,sBAAAA,MAAI,YAAa;AACjBA,sBAAAA,MAAc,MAAA,SAAA,oCAAA,WAAW,KAAK;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACT,CAAG;AAAA,MACD;AAAA,IACF;AAGA,UAAM,iBAAiB,YAAY;;AAClC,UAAI,CAAC,WAAW;AAAO;AAEvB,uBAAiB,QAAQ;AAEzB,UAAI;AAEHA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA;AAAA,UACV,MAAM;AAAA,QACT,CAAG;AAGD,cAAM,eAAe;AAAA,UACpB,cAAc,UAAU;AAAA,UACxB,MAAM,YAAY;AAAA,UAClB,WAAW;AAAA;AAAA,QACX;AACD,YAAI,aAAa;AACjB,YAAI,iBAAiB,UAAU,GAAG;AAEjC,uBAAa,WAAW,iBAAiB,MAAM;AAC/C,uBAAa,aAAa,iBAAiB,MAAM;AACjD,uBAAa,cAAa,sBAAiB,MAAM,QAAQ,CAAC,MAAhC,mBAAmC;AAC7D,wBAAa,sBAAiB,MAAM,QAAQ,CAAC,MAAhC,mBAAmC;AAAA,QACnD,OAAS;AAEN,uBAAa,WAAW,iBAAiB,MAAM;AAC/C,uBAAa,aAAa,iBAAiB,MAAM;AACjD,uBAAa,aAAa;AAC1B,uBAAa,iBAAiB,MAAM;AAAA,QACpC;AAEDA,sBAAAA,MAAA,MAAA,OAAA,oCAAY,UAAU;AAGtB,cAAM,YAAY,MAAM,aAAa,UAAU;AAC/C,YAAI,WAAW;AACd,uBAAa,cAAc,UAAU;AACrC,uBAAa,eAAe,UAAU;AAAA,QACtC;AAID,cAAM,MAAM,MAAMK,UAAkB,mBAAC,YAAY;AAEjDL,sBAAAA,MAAI,UAAW;AAEf,YAAI,IAAI,SAAS,GAAG;AACnBA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACV,CAAI;AAEDA,wBAAAA,MAAI,WAAW;AAAA,YACd,KAAK,oCAAoC,IAAI,KAAK,OAAO,eAAe,UAAU;AAAA,UACtF,CAAI;AAAA,QACJ,OAAS;AACNA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO,IAAI,OAAO;AAAA,YAClB,MAAM;AAAA,UACV,CAAI;AAAA,QACD;AAAA,MACD,SAAQ,OAAO;AAEfA,sBAAAA,MAAI,UAAW;AACfA,sBAAAA,yDAAc,aAAa,KAAK;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO,MAAM,OAAO;AAAA,UACpB,MAAM;AAAA,QACT,CAAG;AAAA,MACD;AAAA,IACF;AAGA,UAAM,oBAAoB,MAAM;AAC/B,uBAAiB,QAAQ;AAAA,IAC1B;AAGAM,kBAAAA,UAAU,MAAM;AAEf,kBAAY,QAAQ;AACpB,kBAAY,QAAQ;AACpB,uBAAiB,QAAQ;AACzB,uBAAiB,GAAG,OAAO,IAAI;AAAA,IAChC,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7rBD,GAAG,WAAW,eAAe;"}