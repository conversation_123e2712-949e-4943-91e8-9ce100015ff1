import request from "@/request/request.js";
// import config from '@/config/config.js'

//小程序静默登录
export const mpLoginApi = (data) => {
	return request({
		url: 'user/api.user/xcxSilenceLogin',
		method: 'POST',
		data
	})
}
//更新用户默认数据
export const updateUserDefaultProfileApi = (data) => {
	return request({
		url: 'useragent/api.AiAgentSquare/updateUserDefaultProfile',
		method: 'POST',
		data
	})
}

// 小程序手机登录
export const phoneAuthLoginApi = (data) => {
	return request({
		url: 'user/api.user/phoneAuthLogin',
		method: 'POST',
		data
	})
}

// 获取默认配置
export const showConfigsApi = (data) => {
	return request({
		url: 'zhanhui/api.index/showConfigs',
		method: 'POST',
		data
	})
}
//获取首页数据
export const getHomeDataApi = (data) => {
	return request({
		url: 'useragent/api.AiAgentPublic/homeData',
		method: 'POST',
		data
	})
}
//获取智能体分类列表
export const getCategoryListApi = (data) => {
	return request({
		url: 'useragent/api.AiAgentPublic/categoryList',
		method: 'POST',
		data
	})
}
//获取会话列表
export const getMySessionListApi = (data) => {
	return request({
		url: 'useragent/api.AiAgentChat/mySessionList',
		method: 'POST',
		data
	})
}
//通过分类获取智能体列表
export const getAgentListApi = (data) => {
	return request({
		url: 'useragent/api.AiAgentSquare/agentList',
		method: 'POST',
		data
	})
}
//订阅智能体
export const subscribeAgentApi = (data) => {
	return request({
		url: 'useragent/api.AiAgentSquare/subscribeAgent',
		method: 'POST',
		data
	})
}
//修改会话标题
export const updateSessionTitleApi = (data) => {
	return request({
		url: 'useragent/api.AiAgentChat/updateSessionTitle',
		method: 'POST',
		data
	})
}
//删除会话
export const deleteSessionApi = (data) => {
	return request({
		url: 'useragent/api.AiAgentChat/deleteSession',
		method: 'POST',
		data
	})
}
//修改用户信息
export const updateUserInfoApi = (data) => {
	return request({
		url: 'user/api.userinfo/update',
		method: 'POST',
		data
	})
}
//获取用户信息
export const getUserInfoApi = (data) => {
	return request({
		url: 'user/api.userinfo/index',
		method: 'POST',
		data
	})
}

//获取我的智能体列表
export const getMyAgentListApi = (data) => {
	return request({
		url: 'useragent/api.AiAgent/myList',
		method: 'POST',
		data
	})
}
//创建智能体
export const createAgentApi = (data) => {
	return request({
		url: 'useragent/api.AiAgent/create',
		method: 'POST',
		data
	})
}
//Ai生成头像
export const generateAvatarApi = (data) => {
	return request({
		url: 'useragent/api.AiAgent/generateAvatar',
		method: 'POST',
		data
	})
}
//获取我的智能体详情
export const getMyDetailApi = (data) => {
	return request({
		url: 'useragent/api.AiAgent/myDetail',
		method: 'POST',
		data
	})
}
//修改我的智能体
export const updateMyAgentApi = (data) => {
	return request({
		url: 'useragent/api.AiAgent/update',
		method: 'POST',
		data
	})
}

//获取会话历史消息
export const getMessageHistoryApi = (data) => {
	return request({
		url: 'useragent/api.AiAgentChat/messageHistory',
		method: 'POST',
		data
	})
}
//获取会话历史消息
export const saveMsgApi = (data) => {
	return request({
		url: 'useragent/api.AiAgentChat/saveMsg',
		method: 'POST',
		data
	})
}
//获取规则信息
export const platformRulesApi = (data) => {
	return request({
		url: 'useragent/api.AiAgentPublic/platformRules',
		method: 'POST',
		data
	})
}

//获取智能体详情
export const agentDetailApi = (data) => {
	return request({
		url: 'useragent/api.AiAgent/agentDetail',
		method: 'POST',
		data
	})
}
//删除会话所有消息记录
export const deleteAllMessagesApi = (data) => {
	return request({
		url: 'useragent/api.AiAgentChat/deleteAllMessages',
		method: 'POST',
		data
	})
}
//删除单条消息记录
export const deleteMessageApi = (data) => {
	return request({
		url: 'useragent/api.AiAgentChat/deleteMessage',
		method: 'POST',
		data
	})
}
//收藏消息
export const collectMessageApi = (data) => {
	return request({
		url: 'useragent/api.AiAgentChat/collectMessage',
		method: 'POST',
		data
	})
}
//取消收藏消息
export const cancelCollectMessageApi = (data) => {
	return request({
		url: 'useragent/api.AiAgentChat/uncollectMessage',
		method: 'POST',
		data
	})
}

//获取我的收藏列表
export const getMyCollectionListApi = (data) => {
	return request({
		url: 'useragent/api.AiAgentChat/myCollectionList',
		method: 'POST',
		data
	})
}

//创建购买订单
export const createPurchaseOrderApi = (data) => {
	return request({
		url: 'useragent/api.AiAgentSquare/createPurchaseOrder',
		method: 'POST',
		data
	})
}
//查询购买订单
export const queryPurchaseOrderApi = (data) => {
	return request({
		url: 'useragent/api.AiAgentSquare/queryPurchaseOrder',
		method: 'POST',
		data
	})
}

//创建会员套餐订阅订单
export const createVipOrderApi = (data) => {
	return request({
		url: 'useragent/api.AiAgentMembership/purchasePackage',
		method: 'POST',
		data
	})
}

//获取会员套餐列表
export const getVipPackageListApi = (data) => {
	return request({
		url: 'useragent/api.AiAgentMembership/packageList',
		method: 'POST',
		data
	})
}

//获取用户会员信息
export const getUserVipInfoApi = (data) => {
	return request({
		url: 'useragent/api.AiAgentMembership/myMembership',
		method: 'POST',
		data
	})
}

//查询会员订阅订单
export const queryVipOrderApi = (data) => {
	return request({
		url: 'useragent/api.AiAgentMembership/queryPayment',
		method: 'POST',
		data
	})
}


//获取邀请码
export const getInvitationCodeApi = (data) => {
	return request({
		url: 'user/api.userinfo/getInvitationCode',
		method: 'POST',
		data
	})
}
//绑定邀请码
export const bindInvitationApi = (data) => {
	return request({
		url: 'useragent/api.AiAgent/bindInvitation',
		method: 'POST',
		data
	})
}
//生成小程序码
export const generateMiniCodeApi = (data) => {
	return request({
		url: 'useragent/api.AiAgent/generateMiniCode',
		method: 'POST',
		data
	})
}

//获取订阅列表
export const getSubscriptionListApi = (data) => {
	return request({
		url: 'useragent/api.AiAgentCreatorSubscription/creatorList',
		method: 'POST',
		data
	})
}

//订阅智能体创作者
export const subscribeCreatorApi = (data) => {
	return request({
		url: 'useragent/api.AiAgentCreatorSubscription/purchaseCreatorSubscription',
		method: 'POST',
		data
	})
}

//查询订阅订单
export const querySubscriptionOrderApi = (data) => {
	return request({
		url: 'useragent/api.AiAgentCreatorSubscription/queryPayment',
		method: 'POST',
		data
	})
}

//获取用户订阅列表
export const getMySubscriptionListApi = (data) => {
	return request({
		url: 'useragent/api.AiAgentCreatorSubscription/myCreatorSubscriptions',
		method: 'POST',
		data
	})
}

//获取订阅规则信息
export const getSubscriptionRuleApi = (data) => {
	return request({
		url: 'useragent/api.AiAgentMembership/subscriptionRule',
		method: 'POST',
		data
	})
}

//获取banner图
export const showBannerUrlsApi = (data) => {
	return request({
		url: 'useragent/api.AiAgentPublic/showBannerUrls',
		method: 'POST',
		data
	})
}

//设置会话置顶
export const setSessionTopApi = (data) => {
	return request({
		url: 'useragent/api.AiAgentChat/setSessionTop',
		method: 'POST',
		data
	})
}

//删除智能体
export const deleteAgentApi = (data) => {
	return request({
		url: 'useragent/api.AiAgent/delete',
		method: 'POST',
		data
	})
}
//获取banner列表
export const getBannerListApi = (data) => {
	return request({
		url: 'merchant/api.index/bannerList',
		method: 'POST',
		data
	})
}
//获取我的收益
export const getMyEarningsApi = (data) => {
	return request({
		url: 'useragent/api.AiAgentFinance/myEarnings',
		method: 'POST',
		data
	})
}
// 聊天点数商品列表
export const getChatGoodsApi = (data) => {
	return request({
		url: 'square/api.chatGoods/index',
		method: 'POST',
		data
	})
}

// 购买聊天点数
export const buyChatGoodsApi = (data) => {
	return request({
		url: 'square/api.chatGoods/buy',
		method: 'POST',
		data
	})
}

// 查询购买聊天点数结果
export const queryPayChatStautsApi = (data) => {
	return request({
		url: 'square/api.chatGoods/buyQuery',
		method: 'POST',
		data
	})
}

// 数字人创作隐私协议
export const userVoicePrivacyApi = (data) => {
	return request({
		url: 'user/api.userWork/voicePrivacy',
		method: 'POST',
		data
	})
}

// 获取公共形象列表
export const commonPersonListApi = (data) => {
	return request({
		url: 'useragent/api.ChanjingDigitalHuman/getPersonList',
		method: 'POST',
		data
	})
}

// 创建数字人视频任务
export const createVideoTaskApi = (data) => {
	return request({
		url: 'useragent/api.ChanjingDigitalHuman/createVideo',
		method: 'POST',
		data
	})
}

// 获取视频详情
export const getVideoDetailApi = (data) => {
	return request({
		url: 'useragent/api.ChanjingDigitalHuman/getVideoDetail',
		method: 'POST',
		data
	})
}

// 我的数字人作品列表
export const worksListApi = (data) => {
	return request({
		url: 'useragent/api.ChanjingDigitalHuman/getMyWorks',
		method: 'POST',
		data
	})
}

// 批量删除数字人作品
export const deleteWorksApi = (data) => {
	return request({
		url: 'useragent/api.ChanjingDigitalHuman/batchDeleteWorks',
		method: 'POST',
		data
	})
}

// 创建定制数字人形象
export const createPersonkApi = (data) => {
	return request({
		url: 'useragent/api.ChanjingDigitalHuman/createPerson',
		method: 'POST',
		data
	})
}

// 我的数字人形象列表
export const getMyPersonListApi = (data) => {
	return request({
		url: 'useragent/api.ChanjingDigitalHuman/getMyPersonList',
		method: 'POST',
		data
	})
}

// 删除我的数字人形象
export const deletePersonApi = (data) => {
	return request({
		url: 'useragent/api.ChanjingDigitalHuman/deletePerson',
		method: 'POST',
		data
	})
}

// 视频上传
export const uploadVideoApi = (data) => {
	return request({
		url: 'user/api.userinfo/uploadVideo',
		method: 'POST',
		data
	})
}

// 计算数字人视频创作所需算力
export const calculatePointsApi = (data) => {
	return request({
		url: 'useragent/api.ChanjingDigitalHuman/calculatePoints',
		method: 'POST',
		data
	})
}
//保存秘钥
export const saveSecretKeyApi = (data) => {
	return request({
		url: 'userinfo/api.userinfo/saveSecretKey',
		method: 'POST',
		data
	})
}
//获取秘钥列表
export const getSecretKeyListApi = (data) => {
	return request({
		url: 'user/api.userinfo/getSecretKeyList',
		method: 'POST',
		data
	})
}
