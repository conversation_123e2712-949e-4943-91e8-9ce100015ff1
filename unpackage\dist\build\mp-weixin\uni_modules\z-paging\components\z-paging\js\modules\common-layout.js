"use strict";const e=require("../../../../../../common/vendor.js"),t={data:()=>({systemInfo:null,cssSafeAreaInsetBottom:-1,isReadyDestroy:!1}),computed:{windowTop(){return this.systemInfo&&this.systemInfo.windowTop||0},safeAreaBottom(){if(!this.systemInfo)return 0;let e=0;return e=Math.max(this.cssSafeAreaInsetBottom,0),e},isOldWebView(){try{const t=e.index.getSystemInfoSync().system.split(" "),s=t[0],o=parseInt(t[1]);if("iOS"===s&&o<=10||"Android"===s&&o<=6)return!0}catch(t){return!1}return!1},zSlots(){return this.$slots}},beforeDestroy(){this.isReadyDestroy=!0},unmounted(){this.isReadyDestroy=!0},methods:{updateFixedLayout(){this.fixed&&this.$nextTick((()=>{this.systemInfo=e.index.getSystemInfoSync()}))},_getNodeClientRect(t,s=!0,o=!1){if(this.isReadyDestroy)return Promise.resolve(!1);let i=s?e.index.createSelectorQuery().in(!0===s?this:s):e.index.createSelectorQuery();return o?i.select(t).scrollOffset():i.select(t).boundingClientRect(),new Promise(((e,t)=>{i.exec((t=>{e(!(!t||""==t||null==t||!t.length)&&t)}))}))},_updateLeftAndRightWidth(e,t){this.$nextTick((()=>{setTimeout((()=>{["left","right"].map((s=>{this._getNodeClientRect(`.${t}-${s}`).then((t=>{this.$set(e,s,t?t[0].width+"px":"0px")}))}))}),0)}))},_getCssSafeAreaInsetBottom(e){this._getNodeClientRect(".zp-safe-area-inset-bottom").then((t=>{this.cssSafeAreaInsetBottom=t?t[0].height:-1,t&&e&&e()}))}}};exports.commonLayoutModule=t;
