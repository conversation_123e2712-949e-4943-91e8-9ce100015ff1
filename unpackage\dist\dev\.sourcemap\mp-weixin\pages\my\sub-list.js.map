{"version": 3, "file": "sub-list.js", "sources": ["pages/my/sub-list.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvbXkvc3ViLWxpc3QudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"sub-page\">\r\n    <!-- 专属订阅说明 -->\r\n    <view class=\"sub-intro\">\r\n      <text class=\"intro-title\">专属订阅</text>\r\n\r\n      <!-- 专属订阅1 -->\r\n      <view class=\"intro-item\">\r\n        <view class=\"icon-box\">\r\n          <image class=\"icon\" :src=\"icon1\"></image>\r\n        </view>\r\n        <view class=\"intro-content\">\r\n          <text class=\"intro-label\">专属订阅1:</text>\r\n          <text class=\"intro-desc\">{{ rule.rule1 }}</text>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 专属订阅2 -->\r\n      <view class=\"intro-item\">\r\n        <view class=\"icon-box\">\r\n          <image class=\"icon\" :src=\"icon2\"></image>\r\n        </view>\r\n        <view class=\"intro-content\">\r\n          <text class=\"intro-label\">专属订阅2:</text>\r\n          <text class=\"intro-desc\">{{ rule.rule2 }}</text>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 使用规则 -->\r\n      <view class=\"intro-item\">\r\n        <view class=\"icon-box\">\r\n          <image class=\"icon\" :src=\"icon3\"></image>\r\n        </view>\r\n        <view class=\"intro-content\">\r\n          <text class=\"intro-label\">使用规则:</text>\r\n          <text class=\"intro-desc\">{{ rule.rule_notice }}</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 用户信息卡片 -->\r\n    <view class=\"user-card\">\r\n      <view class=\"user-avatar\">\r\n        <image class=\"avatar-img\" :src=\"userInfo.headImgUrl || defaultAvatar\" mode=\"aspectFill\" />\r\n      </view>\r\n      <view class=\"user-info\">\r\n        <text class=\"user-name\">{{ userInfo.nickname || '用户Aric' }}</text>\r\n        <text class=\"user-points\">我的通用AI点数：{{ userInfo.chat_count }}</text>\r\n      </view>\r\n      <!-- <view class=\"buy-points-btn\">\r\n        购买点数\r\n      </view> -->\r\n    </view>\r\n\r\n    <!-- 创作者订阅 -->\r\n    <view class=\"subscription-section\">\r\n      <view class=\"section-header\">\r\n        <text class=\"section-title\">创作者订阅：{{ creatorSubscriptions.length }}个</text>\r\n        <view class=\"subscribe-btn\" @tap=\"showCreatorModal\">\r\n          订阅创作者\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 订阅创作者弹窗 -->\r\n    <view v-if=\"showSubscribeModal\" class=\"modal-overlay\" @tap=\"closeModal\" catchtouchmove=\"true\">\r\n      <view class=\"modal-content\" @tap.stop>\r\n        <!-- 关闭按钮 -->\r\n        <view class=\"modal-close\" @tap=\"closeModal\">\r\n          <text class=\"close-icon\">×</text>\r\n        </view>\r\n\r\n        <!-- 标题 -->\r\n        <view class=\"modal-header\">\r\n          <text class=\"modal-title\">专属订阅</text>\r\n        </view>\r\n\r\n        <!-- 创作者列表 -->\r\n        <scroll-view class=\"creator-list\" scroll-y>\r\n          <view v-for=\"creator in creatorList\" :key=\"creator.guid\" class=\"creator-item\">\r\n            <!-- 创作者头像 -->\r\n            <view class=\"creator-avatar\">\r\n              <image class=\"avatar-img\" :src=\"creator.creatorAvatar\" mode=\"aspectFill\" />\r\n            </view>\r\n\r\n            <!-- 创作者信息 -->\r\n            <view class=\"creator-info\">\r\n              <text class=\"creator-name\">{{ creator.creatorName }}</text>\r\n              <text class=\"creator-desc\">{{ creator.creatorDesc }}</text>\r\n\r\n              <!-- 统计信息 -->\r\n              <view class=\"creator-stats\">\r\n                <text class=\"stats-text\">{{ creator.agentCount }}个</text>\r\n                <text class=\"stats-label\">已上架智能体个数</text>\r\n                <text class=\"stats-price\">¥{{ creator.subscriptionPriceYuan }}</text>\r\n                <text class=\"stats-label\">订阅金额</text>\r\n              </view>\r\n            </view>\r\n            <!-- 操作按钮 -->\r\n            <view class=\"creator-actions\">\r\n              <view class=\"action-btn detail-btn\" @tap=\"viewCreatorDetail(creator)\">\r\n                查看详情\r\n              </view>\r\n              <view class=\"action-btn subscribe-btn\"\r\n                :class=\"{ 'subscribed': creator.isSubscribed, 'ios-disabled': isIos }\"\r\n                @tap=\"handleCreatorSubscribe(creator)\">\r\n                <text class=\"btn-text\">{{ creator.isSubscribed ? '已订阅' : (isIos ? 'IOS暂不支持' : '订阅') }}</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </scroll-view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 分类订阅 -->\r\n    <!-- <view class=\"subscription-section\">\r\n      <view class=\"section-header\">\r\n        <text class=\"section-title\">分类订阅：{{ categorySubscriptions.length }}个</text>\r\n        <view class=\"subscribe-btn\">\r\n          订阅分类\r\n        </view>\r\n      </view>\r\n    </view> -->\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, reactive } from 'vue'\r\nimport { onLoad, onShow } from '@dcloudio/uni-app'\r\nimport { getSubscriptionListApi, querySubscriptionOrderApi, subscribeCreatorApi, getUserInfoApi, getMySubscriptionListApi, getSubscriptionRuleApi } from '@/api/index.js'\r\nimport { useUserStore } from '@/stores/user.js'\r\nimport { miniPay } from '@/api/common.js'\r\n\r\nconst userStore = useUserStore()\r\n\r\n// 默认头像\r\nconst defaultAvatar = 'https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/c4d5b3ada53740d5b862f274ce48ef0c.png'\r\n\r\nconst icon3 = 'https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/46348d225bb54770a614dba856c5193e.png';\r\nconst icon2 = 'https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/77e643ee1cd3492ba370158addccd825.png';\r\nconst icon1 = 'https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/84d756efe28f4116a271e357f48d04e6.png'\r\n\r\n// 用户信息\r\nconst userInfo = reactive({\r\n  headImgUrl: '',\r\n  nickname: '',\r\n  chat_count: 0\r\n})\r\n\r\n// 创作者订阅列表\r\nconst creatorSubscriptions = ref([])\r\n\r\n// 分类订阅列表\r\nconst categorySubscriptions = ref([])\r\n\r\n// 弹窗相关状态\r\nconst showSubscribeModal = ref(false)\r\nconst creatorList = ref([])\r\nconst isIos = ref(false)\r\nconst queryStatusNum = ref(0)\r\n\r\n// 获取用户信息\r\nconst getUserInfo = async () => {\r\n  try {\r\n    const res = await getUserInfoApi({\r\n      merchantGuid: userStore.merchantGuid\r\n    })\r\n    if (res.code === 0) {\r\n      Object.assign(userInfo, res.data)\r\n    }\r\n  } catch (error) {\r\n    console.error('获取用户信息失败:', error)\r\n  }\r\n}\r\n\r\n// 获取用户订阅信息\r\n// const getUserSubscription = async () => {\r\n//   try {\r\n//     const res = await getSubscriptionListApi({\r\n//       merchantGuid: userStore.merchantGuid\r\n//     })\r\n//     if (res.code === 0) {\r\n//       creatorSubscriptions.value = res.data.creators || []\r\n//       // categorySubscriptions.value = res.data.categorySubscriptions || []\r\n//     }\r\n//   } catch (error) {\r\n//     console.error('获取订阅信息失败:', error)\r\n//   }\r\n// }\r\n\r\n// 获取创作者列表\r\nconst getCreatorList = async () => {\r\n  try {\r\n    const res = await getSubscriptionListApi({\r\n      merchantGuid: userStore.merchantGuid\r\n    })\r\n    if (res.code === 0) {\r\n      creatorList.value = res.data.creators\r\n    }\r\n  } catch (error) {\r\n    console.error('获取创作者列表失败:', error)\r\n  }\r\n}\r\n//获取规则\r\nconst rule = reactive({\r\n  rule1: '',\r\n  rule2: '',\r\n  rule_notice: ''\r\n})\r\nconst getSubscriptionRule = async () => {\r\n  try {\r\n    const res = await getSubscriptionRuleApi({\r\n      merchantGuid: userStore.merchantGuid\r\n    })\r\n    if (res.code === 0) {\r\n      rule.rule1 = res.data.zhuanshu.rule1\r\n      rule.rule2 = res.data.zhuanshu.rule2\r\n      rule.rule_notice = res.data.zhuanshu.rule_notice\r\n    }\r\n  } catch (error) {\r\n    console.error('获取规则失败:', error)\r\n  }\r\n}\r\n// 显示订阅创作者弹窗\r\nconst showCreatorModal = async () => {\r\n  uni.showToast({\r\n    title: '暂未开放',\r\n    icon: 'none'\r\n  })\r\n  return\r\n\r\n  showSubscribeModal.value = true\r\n  await getCreatorList()\r\n}\r\n\r\n// 关闭弹窗\r\nconst closeModal = () => {\r\n  showSubscribeModal.value = false\r\n}\r\n\r\n// 查看创作者详情\r\nconst viewCreatorDetail = (creator) => {\r\n  // 这里可以跳转到创作者详情页面或显示详情弹窗\r\n  console.log('查看创作者详情:', creator)\r\n}\r\n\r\n// 订阅创作者\r\nconst handleCreatorSubscribe = async (creator) => {\r\n  if (isIos.value) {\r\n    uni.showToast({\r\n      title: 'IOS暂不支持',\r\n      icon: 'none'\r\n    })\r\n    return\r\n  }\r\n\r\n  if (creator.isSubscribed) {\r\n    uni.showToast({\r\n      title: '已订阅该创作者',\r\n      icon: 'none'\r\n    })\r\n    return\r\n  }\r\n\r\n  if (!userStore.userToken) {\r\n    uni.showToast({\r\n      title: '请先登录',\r\n      icon: 'none'\r\n    })\r\n    return\r\n  }\r\n\r\n  try {\r\n    uni.showLoading({\r\n      title: '正在创建订单...',\r\n      mask: true\r\n    })\r\n\r\n    // 创建订阅订单\r\n    const payInfo = await subscribeCreatorApi({\r\n      merchantGuid: userStore.merchantGuid,\r\n      creatorGuid: creator.guid,\r\n      payEnv: 'xcx'\r\n    })\r\n\r\n    uni.hideLoading()\r\n\r\n    // 调用微信支付\r\n    miniPay(payInfo.data.payInfo).then(\r\n      async () => {\r\n        queryPayStatus(payInfo.data.orderNo, queryStatusNum.value)\r\n      },\r\n      (res) => {\r\n        uni.showToast({\r\n          title: res.msg || '支付失败',\r\n          icon: 'none'\r\n        })\r\n      }\r\n    )\r\n  } catch (error) {\r\n    uni.hideLoading()\r\n    console.error('创建订单失败:', error)\r\n    uni.showToast({\r\n      title: error.message || '创建订单失败',\r\n      icon: 'none'\r\n    })\r\n  }\r\n}\r\n\r\n// 查询支付状态\r\nconst queryPayStatus = async (orderNo, number) => {\r\n  number++\r\n  try {\r\n    const orderInfo = await querySubscriptionOrderApi({\r\n      orderNo\r\n    })\r\n\r\n    if (orderInfo.data.isPaid) {\r\n      uni.showToast({\r\n        title: '订阅成功',\r\n        icon: 'success'\r\n      })\r\n      // 刷新数据\r\n      // getUserSubscription()\r\n      getCreatorList()\r\n      closeModal()\r\n    } else {\r\n      if (number > 12) {\r\n        uni.showToast({\r\n          title: '支付超时',\r\n          icon: 'none'\r\n        })\r\n      } else {\r\n        setTimeout(() => {\r\n          queryPayStatus(orderNo, number)\r\n        }, 2000)\r\n      }\r\n    }\r\n  } catch (error) {\r\n    uni.showToast({\r\n      title: error.msg || '查询支付状态失败',\r\n      icon: 'none'\r\n    })\r\n  }\r\n}\r\n\r\nonLoad(() => {\r\n  // 检测系统类型\r\n  const systemInfo = uni.getSystemInfoSync()\r\n  if (systemInfo.osName === 'ios') {\r\n    isIos.value = true\r\n  }\r\n\r\n  if (userStore.userToken) {\r\n    getUserInfo()\r\n    getSubscriptionRule()\r\n    // getUserSubscription()\r\n  }\r\n})\r\n\r\nonShow(() => {\r\n  if (userStore.userToken) {\r\n    getUserInfo()\r\n    getSubscriptionRule()\r\n    // getUserSubscription()\r\n  }\r\n})\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.sub-page {\r\n  background: #F8F9FA;\r\n  padding: 32rpx 32rpx 0;\r\n  min-height: 100vh;\r\n}\r\n\r\n.sub-intro {\r\n  background: #FFFFFF;\r\n  border-radius: 20rpx;\r\n  padding: 32rpx;\r\n  margin-bottom: 32rpx;\r\n}\r\n\r\n.intro-title {\r\n  font-size: 36rpx;\r\n  font-weight: 600;\r\n  color: #333333;\r\n  margin-bottom: 32rpx;\r\n  display: block;\r\n}\r\n\r\n.intro-item {\r\n  display: flex;\r\n  margin-bottom: 32rpx;\r\n\r\n  &:last-child {\r\n    margin-bottom: 0;\r\n  }\r\n}\r\n\r\n.icon-box {\r\n  width: 60rpx;\r\n  height: 60rpx;\r\n  border-radius: 50%;\r\n  margin-right: 24rpx;\r\n  flex-shrink: 0;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n\r\n  .icon {\r\n    width: 60rpx;\r\n    height: 60rpx;\r\n    display: block;\r\n  }\r\n\r\n}\r\n\r\n.intro-content {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.intro-label {\r\n  font-size: 28rpx;\r\n  font-weight: 600;\r\n  color: #333333;\r\n  margin-bottom: 8rpx;\r\n}\r\n\r\n.intro-desc {\r\n  font-size: 24rpx;\r\n  color: #666666;\r\n  line-height: 1.5;\r\n}\r\n\r\n.user-card {\r\n  background: #FFFFFF;\r\n  border-radius: 20rpx;\r\n  padding: 32rpx;\r\n  margin-bottom: 32rpx;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.user-avatar {\r\n  width: 80rpx;\r\n  height: 80rpx;\r\n  border-radius: 50%;\r\n  overflow: hidden;\r\n  margin-right: 24rpx;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.avatar-img {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.user-info {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.user-name {\r\n  font-size: 28rpx;\r\n  font-weight: 600;\r\n  color: #333333;\r\n  margin-bottom: 8rpx;\r\n}\r\n\r\n.user-points {\r\n  font-size: 24rpx;\r\n  color: #1E90FF;\r\n}\r\n\r\n.buy-points-btn {\r\n  background: #1E90FF;\r\n  border-radius: 40rpx;\r\n  padding: 16rpx 32rpx;\r\n  font-size: 24rpx;\r\n  color: #FFFFFF;\r\n  line-height: 1;\r\n}\r\n\r\n.subscription-section {\r\n  background: #FFFFFF;\r\n  border-radius: 20rpx;\r\n  padding: 32rpx;\r\n  margin-bottom: 32rpx;\r\n}\r\n\r\n.section-header {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n}\r\n\r\n.section-title {\r\n  font-size: 28rpx;\r\n  font-weight: 600;\r\n  color: #333333;\r\n}\r\n\r\n.subscribe-btn {\r\n  background: #333333;\r\n  border-radius: 40rpx;\r\n  padding: 16rpx 32rpx;\r\n  font-size: 24rpx;\r\n  color: #FFFFFF;\r\n  line-height: 1;\r\n}\r\n\r\n// 弹窗样式\r\n.modal-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background-color: rgba(0, 0, 0, 0.6);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  z-index: 999;\r\n}\r\n\r\n.modal-content {\r\n  width: 90%;\r\n  height: 600rpx;\r\n  background: #FFFFFF;\r\n  border-radius: 20rpx;\r\n  position: relative;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.modal-close {\r\n  position: absolute;\r\n  top: 20rpx;\r\n  right: 20rpx;\r\n  width: 60rpx;\r\n  height: 60rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  z-index: 1000;\r\n}\r\n\r\n.close-icon {\r\n  font-size: 40rpx;\r\n  color: #999999;\r\n  font-weight: 300;\r\n}\r\n\r\n.modal-header {\r\n  padding: 40rpx 32rpx 20rpx;\r\n  text-align: center;\r\n  border-bottom: 1rpx solid #F0F0F0;\r\n}\r\n\r\n.modal-title {\r\n  font-size: 32rpx;\r\n  font-weight: 600;\r\n  color: #333333;\r\n}\r\n\r\n.creator-list {\r\n  flex: 1;\r\n  padding: 20rpx 0;\r\n}\r\n\r\n.creator-item {\r\n  display: flex;\r\n  padding: 32rpx;\r\n  border-bottom: 1rpx solid #F8F9FA;\r\n\r\n  &:last-child {\r\n    border-bottom: none;\r\n  }\r\n}\r\n\r\n.creator-avatar {\r\n  width: 120rpx;\r\n  height: 120rpx;\r\n  border-radius: 50%;\r\n  overflow: hidden;\r\n  margin-right: 24rpx;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.avatar-img {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.creator-info {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.creator-name {\r\n  font-size: 28rpx;\r\n  font-weight: 600;\r\n  color: #333333;\r\n  margin-bottom: 8rpx;\r\n}\r\n\r\n.creator-desc {\r\n  font-size: 24rpx;\r\n  color: #666666;\r\n  line-height: 1.4;\r\n  margin-bottom: 16rpx;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 2;\r\n  line-clamp: 2;\r\n  -webkit-box-orient: vertical;\r\n  overflow: hidden;\r\n}\r\n\r\n.creator-stats {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 20rpx;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.stats-text {\r\n  font-size: 24rpx;\r\n  font-weight: 600;\r\n  color: #333333;\r\n  margin-right: 8rpx;\r\n}\r\n\r\n.stats-label {\r\n  font-size: 20rpx;\r\n  color: #999999;\r\n  margin-right: 32rpx;\r\n}\r\n\r\n.stats-price {\r\n  font-size: 28rpx;\r\n  font-weight: 600;\r\n  color: #1E90FF;\r\n  margin-right: 8rpx;\r\n}\r\n\r\n.creator-actions {\r\n  display: flex;\r\n  gap: 16rpx;\r\n}\r\n\r\n.action-btn {\r\n  padding: 12rpx 24rpx;\r\n  border-radius: 40rpx;\r\n  font-size: 24rpx;\r\n  text-align: center;\r\n  line-height: 1;\r\n}\r\n\r\n.detail-btn {\r\n  background: #F8F9FA;\r\n  color: #666666;\r\n  border: 1rpx solid #E5E5E5;\r\n}\r\n\r\n.subscribe-btn {\r\n  background: #1E90FF;\r\n  color: #FFFFFF;\r\n\r\n  &.subscribed {\r\n    background: #E5E5E5;\r\n    color: #999999;\r\n  }\r\n\r\n  &.ios-disabled {\r\n    background: #999999;\r\n    color: #FFFFFF;\r\n  }\r\n}\r\n\r\n.btn-text {\r\n  font-size: 24rpx;\r\n}\r\n</style>", "import MiniProgramPage from 'E:/youngProject/agent-mini-ui/pages/my/sub-list.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useUserStore", "reactive", "ref", "getUserInfoApi", "uni", "getSubscriptionListApi", "getSubscriptionRuleApi", "subscribeCreatorApi", "miniPay", "querySubscriptionOrderApi", "onLoad", "onShow"], "mappings": ";;;;;AAwIA,MAAM,gBAAgB;AAEtB,MAAM,QAAQ;AACd,MAAM,QAAQ;AACd,MAAM,QAAQ;;;;AAPd,UAAM,YAAYA,YAAAA,aAAc;AAUhC,UAAM,WAAWC,cAAAA,SAAS;AAAA,MACxB,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAGD,UAAM,uBAAuBC,cAAG,IAAC,EAAE;AAGLA,kBAAG,IAAC,EAAE;AAGpC,UAAM,qBAAqBA,cAAG,IAAC,KAAK;AACpC,UAAM,cAAcA,cAAG,IAAC,EAAE;AAC1B,UAAM,QAAQA,cAAG,IAAC,KAAK;AACvB,UAAM,iBAAiBA,cAAG,IAAC,CAAC;AAG5B,UAAM,cAAc,YAAY;AAC9B,UAAI;AACF,cAAM,MAAM,MAAMC,yBAAe;AAAA,UAC/B,cAAc,UAAU;AAAA,QAC9B,CAAK;AACD,YAAI,IAAI,SAAS,GAAG;AAClB,iBAAO,OAAO,UAAU,IAAI,IAAI;AAAA,QACjC;AAAA,MACF,SAAQ,OAAO;AACdC,sBAAAA,MAAA,MAAA,SAAA,gCAAc,aAAa,KAAK;AAAA,MACjC;AAAA,IACH;AAkBA,UAAM,iBAAiB,YAAY;AACjC,UAAI;AACF,cAAM,MAAM,MAAMC,iCAAuB;AAAA,UACvC,cAAc,UAAU;AAAA,QAC9B,CAAK;AACD,YAAI,IAAI,SAAS,GAAG;AAClB,sBAAY,QAAQ,IAAI,KAAK;AAAA,QAC9B;AAAA,MACF,SAAQ,OAAO;AACdD,sBAAAA,MAAc,MAAA,SAAA,gCAAA,cAAc,KAAK;AAAA,MAClC;AAAA,IACH;AAEA,UAAM,OAAOH,cAAAA,SAAS;AAAA,MACpB,OAAO;AAAA,MACP,OAAO;AAAA,MACP,aAAa;AAAA,IACf,CAAC;AACD,UAAM,sBAAsB,YAAY;AACtC,UAAI;AACF,cAAM,MAAM,MAAMK,iCAAuB;AAAA,UACvC,cAAc,UAAU;AAAA,QAC9B,CAAK;AACD,YAAI,IAAI,SAAS,GAAG;AAClB,eAAK,QAAQ,IAAI,KAAK,SAAS;AAC/B,eAAK,QAAQ,IAAI,KAAK,SAAS;AAC/B,eAAK,cAAc,IAAI,KAAK,SAAS;AAAA,QACtC;AAAA,MACF,SAAQ,OAAO;AACdF,sBAAAA,MAAA,MAAA,SAAA,gCAAc,WAAW,KAAK;AAAA,MAC/B;AAAA,IACH;AAEA,UAAM,mBAAmB,YAAY;AACnCA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAG;AACD;AAAA,IAIF;AAGA,UAAM,aAAa,MAAM;AACvB,yBAAmB,QAAQ;AAAA,IAC7B;AAGA,UAAM,oBAAoB,CAAC,YAAY;AAErCA,oBAAAA,mDAAY,YAAY,OAAO;AAAA,IACjC;AAGA,UAAM,yBAAyB,OAAO,YAAY;AAChD,UAAI,MAAM,OAAO;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAED,UAAI,QAAQ,cAAc;AACxBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAED,UAAI,CAAC,UAAU,WAAW;AACxBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAED,UAAI;AACFA,sBAAAA,MAAI,YAAY;AAAA,UACd,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAGD,cAAM,UAAU,MAAMG,8BAAoB;AAAA,UACxC,cAAc,UAAU;AAAA,UACxB,aAAa,QAAQ;AAAA,UACrB,QAAQ;AAAA,QACd,CAAK;AAEDH,sBAAAA,MAAI,YAAa;AAGjBI,mBAAAA,QAAQ,QAAQ,KAAK,OAAO,EAAE;AAAA,UAC5B,YAAY;AACV,2BAAe,QAAQ,KAAK,SAAS,eAAe,KAAK;AAAA,UAC1D;AAAA,UACD,CAAC,QAAQ;AACPJ,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO,IAAI,OAAO;AAAA,cAClB,MAAM;AAAA,YAChB,CAAS;AAAA,UACF;AAAA,QACF;AAAA,MACF,SAAQ,OAAO;AACdA,sBAAAA,MAAI,YAAa;AACjBA,sBAAAA,MAAA,MAAA,SAAA,gCAAc,WAAW,KAAK;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,MAAM,WAAW;AAAA,UACxB,MAAM;AAAA,QACZ,CAAK;AAAA,MACF;AAAA,IACH;AAGA,UAAM,iBAAiB,OAAO,SAAS,WAAW;AAChD;AACA,UAAI;AACF,cAAM,YAAY,MAAMK,oCAA0B;AAAA,UAChD;AAAA,QACN,CAAK;AAED,YAAI,UAAU,KAAK,QAAQ;AACzBL,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AAGD,yBAAgB;AAChB,qBAAY;AAAA,QAClB,OAAW;AACL,cAAI,SAAS,IAAI;AACfA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YAChB,CAAS;AAAA,UACT,OAAa;AACL,uBAAW,MAAM;AACf,6BAAe,SAAS,MAAM;AAAA,YAC/B,GAAE,GAAI;AAAA,UACR;AAAA,QACF;AAAA,MACF,SAAQ,OAAO;AACdA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,MAAM,OAAO;AAAA,UACpB,MAAM;AAAA,QACZ,CAAK;AAAA,MACF;AAAA,IACH;AAEAM,kBAAAA,OAAO,MAAM;AAEX,YAAM,aAAaN,cAAG,MAAC,kBAAmB;AAC1C,UAAI,WAAW,WAAW,OAAO;AAC/B,cAAM,QAAQ;AAAA,MACf;AAED,UAAI,UAAU,WAAW;AACvB,oBAAa;AACb,4BAAqB;AAAA,MAEtB;AAAA,IACH,CAAC;AAEDO,kBAAAA,OAAO,MAAM;AACX,UAAI,UAAU,WAAW;AACvB,oBAAa;AACb,4BAAqB;AAAA,MAEtB;AAAA,IACH,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7WD,GAAG,WAAW,eAAe;"}