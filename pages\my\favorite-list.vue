<template>
  <view class="favorite-list-page">
    <!-- 收藏列表 -->
    <z-paging ref="paging" v-model="favoritesList" @query="queryList" :auto="true" :auto-clean-list-when-reload="false">
      <view class="favorites-content">
        <view class="favorite-card" v-for="(item, index) in favoritesList" :key="index">
          <view class="favorite-header">
            <text class="favorite-date">{{ item.collectTime }}</text>
          </view>
          <view class="favorite-content">
            <text class="content-text">{{ item.contentPreview }}</text>
          </view>
          <view class="favorite-actions">
            <view class="action-btn" @tap="handleCopy(item)">
              <image src="@/static/my/<EMAIL>" class="action-icon" mode="aspectFit" />
              <text class="action-text">复制</text>
            </view>
            <view class="action-btn" @tap="handleViewAll(item)">
              <image src="@/static/my/<EMAIL>" class="action-icon" mode="aspectFit" />
              <text class="action-text">查看全部</text>
            </view>
            <view class="action-btn" @tap="handleUnfavorite(item)">
              <image src="@/static/my/<EMAIL>" class="action-icon" mode="aspectFit" />
              <text class="action-text">取消收藏</text>
            </view>
          </view>
        </view>
      </view>
    </z-paging>

    <!-- 查看全部弹窗 -->
    <view v-if="showContentModal" class="modal-overlay" @tap="closeContentModal" catchtouchmove="true">
      <view class="modal-content" @tap.stop>
        <view class="modal-header">
          <text class="modal-title">消息详情</text>
          <view class="close-btn" @tap="closeContentModal">
            <text class="close-text">×</text>
          </view>
        </view>
        <scroll-view scroll-y="true" class="modal-body" show-scrollbar="false">
          <view class="rich-content">
            <rich-text :nodes="currentContent"></rich-text>
          </view>
        </scroll-view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'
import { getMyCollectionListApi, cancelCollectMessageApi } from '@/api/index.js'
import { useUserStore } from '@/stores/user.js'

const userStore = useUserStore()
const favoritesList = ref([])
const paging = ref(null)

// 弹窗相关状态
const showContentModal = ref(false)
const currentContent = ref('')

// 分页查询 - 沿用my页面的数据格式
const queryList = async (page, pageSize) => {
  try {
    let res = await getMyCollectionListApi({
      merchantGuid: userStore.merchantGuid,
      page: page,
      pageSize: pageSize
    })
    // 使用z-paging的complete方法处理数据
    paging.value.complete(res.data.list || [])
  } catch (error) {
    console.error('获取收藏列表失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    })
    paging.value.complete(false)
  }
}

// 事件处理

const handleCopy = (item) => {
  uni.setClipboardData({
    data: item.messageContent,
    success() {
      uni.showToast({
        title: '复制成功',
        icon: 'none'
      })
    }
  })
}

const handleViewAll = (item) => {
  currentContent.value = item.messageContent || ''
  showContentModal.value = true
}

const closeContentModal = () => {
  showContentModal.value = false
  currentContent.value = ''
}

const handleUnfavorite = async (item) => {
  try {
    await cancelCollectMessageApi({
      merchantGuid: userStore.merchantGuid,
      messageGuid: item.messageGuid
    })
    uni.showToast({
      title: '取消收藏成功',
      icon: 'none'
    })
    // 刷新列表
    if (paging.value) {
      paging.value.reload()
    }
  } catch (error) {
    console.error('取消收藏失败:', error)
    uni.showToast({
      title: '操作失败',
      icon: 'none'
    })
  }
}
</script>

<style lang="scss" scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  overflow: hidden;
  touch-action: none;

  .modal-content {
    background: #ffffff;
    width: 90%;
    height: 600px;
    border-radius: 24rpx;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .modal-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 32rpx;
      border-bottom: 1px solid #F0F0F0;
      flex-shrink: 0;

      .modal-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #1a1a1a;
      }

      .close-btn {
        width: 48rpx;
        height: 48rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        background: #F5F5F5;

        .close-text {
          font-size: 36rpx;
          color: #666666;
          line-height: 1;
        }
      }
    }

    .modal-body {
      flex: 1;
      height: 0;
      overflow: hidden;

      .rich-content {
        font-size: 28rpx;
        line-height: 1.6;
        color: #1a1a1a;
        word-break: break-all;
        padding: 32rpx;
      }
    }
  }
}

.favorite-list-page {
  background: #F5F5F5;
  min-height: 100vh;

  .favorites-content {
    padding: 32rpx;

    .favorite-card {
      background: #fff;
      border-radius: 24rpx;
      margin-bottom: 24rpx;
      padding: 32rpx;

      .favorite-header {
        margin-bottom: 16rpx;

        .favorite-date {
          font-size: 24rpx;
          color: #999999;
          display: block;
        }
      }

      .favorite-content {
        margin-bottom: 24rpx;

        .content-text {
          font-size: 28rpx;
          color: #1a1a1a;
          line-height: 1.6;
          display: block;
        }
      }

      .favorite-actions {
        display: flex;
        gap: 16rpx;
        flex-wrap: wrap;

        .action-btn {
          display: flex;
          align-items: center;
          background: #E6F0FF;
          border-radius: 20rpx;
          padding: 12rpx 20rpx;
          border: none;
          flex-shrink: 0;
          min-width: 0;

          .action-icon {
            width: 38rpx;
            height: 38rpx;
            margin-right: 8rpx;
            flex-shrink: 0;
          }

          .action-text {
            font-size: 24rpx;
            color: #222222;
            font-weight: 500;
            white-space: nowrap;
          }
        }
      }
    }
  }
}
</style>