/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.container.data-v-1cf27b2a {
  background-color: #ffffff;
  min-height: 100vh;
  padding: 0 16px;
}

/* 顶部问候区域 */
.greeting-section.data-v-1cf27b2a {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 0 16px;
}
.greeting-section .greeting-text.data-v-1cf27b2a {
  font-size: 30rpx;
  font-weight: 500;
  color: #66648A;
}
.greeting-section .notification-icon .bell-icon.data-v-1cf27b2a {
  width: 60rpx;
  height: 60rpx;
  display: block;
}

/* 推荐卡片区域 */
.recommend-section.data-v-1cf27b2a {
  margin-bottom: 24px;
}
.recommend-section .section-title.data-v-1cf27b2a {
  font-size: 34rpx;
  font-weight: 600;
  color: #222;
  margin-bottom: 20rpx;
  display: flex;
  justify-content: space-between;
}
.recommend-section .section-title .sq-btn.data-v-1cf27b2a {
  width: 189rpx;
  height: 55rpx;
  display: flex;
  align-items: center;
  background: #E8ECFC;
  border-radius: 28rpx;
  position: relative;
  font-size: 20rpx;
  color: #4F72F6;
  justify-content: flex-end;
}
.recommend-section .section-title .sq-btn .arrow.data-v-1cf27b2a {
  display: block;
  width: 30rpx;
  height: 30rpx;
  margin-left: 2px;
}
.recommend-section .section-title .sq-btn .bg.data-v-1cf27b2a {
  position: absolute;
  top: -30rpx;
  left: 0;
  width: 81rpx;
  height: 81rpx;
}
.recommend-section .recommend-swiper-container.data-v-1cf27b2a {
  overflow: hidden;
}
.recommend-section .recommend-swiper.data-v-1cf27b2a {
  height: 140px;
}
.recommend-section .recommend-swiper swiper-item.data-v-1cf27b2a {
  padding-right: 12px;
  box-sizing: border-box;
}
.recommend-section .recommend-swiper .recommend-card.data-v-1cf27b2a {
  background: #F8F9FA;
  border-radius: 12px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
  box-sizing: border-box;
  width: calc(100% - 12px);
  margin-right: 12px;
}
.recommend-section .recommend-swiper .recommend-card .card-content.data-v-1cf27b2a {
  flex: 1;
}
.recommend-section .recommend-swiper .recommend-card .card-content .card-title.data-v-1cf27b2a {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
}
.recommend-section .recommend-swiper .recommend-card .card-content .card-title .icon.data-v-1cf27b2a {
  display: block;
  margin-right: 15rpx;
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
}
.recommend-section .recommend-swiper .recommend-card .card-content .card-desc.data-v-1cf27b2a {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
  margin-bottom: 12px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
}
.recommend-section .recommend-swiper .recommend-card .card-content .card-author.data-v-1cf27b2a {
  font-size: 11px;
  color: #999;
}

/* 分类标签区域 */
.category-section.data-v-1cf27b2a {
  margin-bottom: 24px;
}
.category-section .section-title.data-v-1cf27b2a {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 34rpx;
  font-weight: bold;
  color: #222;
  margin-bottom: 20rpx;
}
.category-section .section-title .more-btn.data-v-1cf27b2a {
  font-size: 30rpx;
  color: #999999;
  font-weight: normal;
  display: flex;
  align-items: center;
}
.category-section .section-title .more-btn .icon.data-v-1cf27b2a {
  width: 40rpx;
  height: 40rpx;
  display: block;
}
.category-section .category-tags.data-v-1cf27b2a {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}
.category-section .category-tags .tag-item.data-v-1cf27b2a {
  padding: 8px 16px;
  background: #F4F7FF;
  border-radius: 20px;
  font-size: 26rpx;
  color: #666;
  border: 1px solid #E7EDFA;
  color: #5380F2;
}

/* AI智能体众创计划横幅 */
.banner-section.data-v-1cf27b2a {
  margin-bottom: 24px;
}
.banner-section .ai-banner.data-v-1cf27b2a {
  position: relative;
  border-radius: 16px;
  overflow: hidden;
  min-height: 380rpx;
}
.banner-section .ai-banner .banner-content.data-v-1cf27b2a {
  position: relative;
  z-index: 2;
}
.banner-section .ai-banner .banner-content .banner-title.data-v-1cf27b2a {
  font-size: 24px;
  font-weight: 700;
  color: #fff;
  margin-bottom: 8px;
}
.banner-section .ai-banner .banner-content .banner-subtitle.data-v-1cf27b2a {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 16px;
  line-height: 1.4;
}
.banner-section .ai-banner .banner-content .banner-btn.data-v-1cf27b2a {
  display: inline-block;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 20px;
  padding: 8px 20px;
  font-size: 14px;
  color: #fff;
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
}
.banner-section .ai-banner .swiper.data-v-1cf27b2a {
  height: 380rpx;
}
.banner-section .ai-banner .banner-image.data-v-1cf27b2a {
  height: 100%;
  width: 100%;
}

/* 创作大神推荐列表 */
.creator-section.data-v-1cf27b2a {
  padding-bottom: 30rpx;
}
.creator-section .section-title.data-v-1cf27b2a {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
}
.creator-section .creator-list .creator-item.data-v-1cf27b2a {
  background: #F8F9FA;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 12px;
}
.creator-section .creator-list .creator-item .creator-icon .icon.data-v-1cf27b2a {
  width: 60rpx;
  height: 60rpx;
  border-radius: 8px;
}
.creator-section .creator-list .creator-item .creator-content.data-v-1cf27b2a {
  flex: 1;
}
.creator-section .creator-list .creator-item .creator-content .creator-title.data-v-1cf27b2a {
  width: 540rpx;
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
  line-height: 1.3;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
.creator-section .creator-list .creator-item .creator-content .creator-desc.data-v-1cf27b2a {
  width: 560rpx;
  font-size: 26rpx;
  color: #999;
  line-height: 1.4;
}
.creator-section .creator-list .creator-item .creator-content .creator-stats.data-v-1cf27b2a {
  font-size: 26rpx;
  color: #999;
  margin-top: 4px;
}

/* 图片弹窗样式 */
.image-modal-overlay.data-v-1cf27b2a {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}
.image-modal-content.data-v-1cf27b2a {
  width: 90%;
  height: 80vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}
.modal-image.data-v-1cf27b2a {
  max-width: 100%;
  max-height: 200px;
  width: 100%;
  height: 300px;
  display: block;
  object-fit: contain;
}