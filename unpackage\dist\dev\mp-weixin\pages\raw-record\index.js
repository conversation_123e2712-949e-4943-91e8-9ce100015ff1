"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  __name: "index",
  setup(__props) {
    const recordList = common_vendor.ref([
      // {
      //   amount: '-2000.00',
      //   status: 'success',
      //   statusText: '成功',
      //   withdrawTime: '2025-05-18 12:56:00'
      // },
      // {
      //   amount: '-2000.00',
      //   status: 'failed',
      //   statusText: '打款失败，已退回余额',
      //   withdrawTime: '2025-05-18 12:56:00'
      // },
      // {
      //   amount: '-2000.00',
      //   status: 'processing',
      //   statusText: '打款中',
      //   withdrawTime: '2025-05-18 12:56:00'
      // }
    ]);
    const getStatusClass = (status) => {
      switch (status) {
        case "success":
          return "status-success";
        case "failed":
          return "status-failed";
        case "processing":
          return "status-processing";
        default:
          return "";
      }
    };
    const loadRecordList = async () => {
      try {
        common_vendor.index.__f__("log", "at pages/raw-record/index.vue:75", "加载提现记录");
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/raw-record/index.vue:77", "加载提现记录失败:", error);
        common_vendor.index.showToast({
          title: "加载失败",
          icon: "none"
        });
      }
    };
    common_vendor.onLoad(() => {
      loadRecordList();
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.f(recordList.value, (item, index, i0) => {
          return {
            a: common_vendor.t(item.amount),
            b: common_vendor.t(item.statusText),
            c: common_vendor.n(getStatusClass(item.status)),
            d: common_vendor.t(item.withdrawTime),
            e: index
          };
        }),
        b: recordList.value.length === 0
      }, recordList.value.length === 0 ? {
        c: common_assets._imports_0$10
      } : {});
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-e241807e"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/raw-record/index.js.map
