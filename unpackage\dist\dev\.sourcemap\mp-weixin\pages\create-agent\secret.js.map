{"version": 3, "file": "secret.js", "sources": ["pages/create-agent/secret.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvY3JlYXRlLWFnZW50L3NlY3JldC52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"secret-edit-page\">\r\n    <!-- 表单区域 -->\r\n    <view class=\"form-section\">\r\n      <!-- 秘钥 -->\r\n      <view class=\"form-item\">\r\n        <input v-model=\"secretForm.secretKey\" class=\"input\" placeholder=\"请输入扣子智能体密钥\" placeholder-class=\"placeholder\"\r\n          maxlength=\"200\" />\r\n      </view>\r\n    </view>\r\n    <!-- 保存按钮 -->\r\n    <view class=\"save-section\">\r\n      <view class=\"save-btn\" :class=\"{ disabled: saving }\" @tap=\"handleSave\">\r\n        <text class=\"save-text\">{{ saving ? '保存中...' : '保存修改' }}</text>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, reactive, onMounted } from 'vue'\r\nimport { getSecretKeyListApi, saveSecretKeyApi } from '@/api/index.js'\r\nimport { useUserStore } from '@/stores/user.js'\r\n\r\nconst userStore = useUserStore()\r\n\r\n// 表单数据\r\nlet secretForm = reactive({\r\n  secretKey: '',\r\n  secretKeyType: 'coze_api_key',\r\n})\r\n\r\n// 获取秘钥列表\r\nconst getSecretKeyList = async () => {\r\n  try {\r\n    let res = await getSecretKeyListApi({\r\n      merchantGuid: userStore.merchantGuid,\r\n    })\r\n\r\n    if (res.code === 0 && res.data && res.data.length > 0) {\r\n      // 查找扣子智能体密钥\r\n      const cozeKey = res.data.find(item => item.secretKeyType === 'coze_api_key')\r\n      if (cozeKey) {\r\n        secretForm.secretKey = cozeKey.secretKey\r\n      }\r\n    }\r\n  } catch (error) {\r\n    console.error('获取秘钥列表失败:', error)\r\n    uni.showToast({\r\n      title: '获取秘钥失败',\r\n      icon: 'none'\r\n    })\r\n  }\r\n}\r\n\r\nconst saving = ref(false)\r\n\r\n// 保存秘钥\r\nconst handleSave = async () => {\r\n  if (!secretForm.secretKey.trim()) {\r\n    uni.showToast({\r\n      title: '请输入秘钥',\r\n      icon: 'none'\r\n    })\r\n    return\r\n  }\r\n\r\n  saving.value = true\r\n\r\n  try {\r\n    const saveData = {\r\n      merchantGuid: userStore.merchantGuid,\r\n      saveSecretKeyList: [\r\n        {\r\n          secretKeyType: secretForm.secretKeyType,\r\n          secretKey: secretForm.secretKey.trim()\r\n        }\r\n      ]\r\n    }\r\n\r\n    let res = await saveSecretKeyApi(saveData)\r\n\r\n    if (res.code === 0) {\r\n      uni.showToast({\r\n        title: '保存成功',\r\n        icon: 'success'\r\n      })\r\n\r\n      // 延迟返回上一页\r\n      setTimeout(() => {\r\n        uni.navigateBack()\r\n      }, 1500)\r\n    } else {\r\n      uni.showToast({\r\n        title: res.msg || '保存失败',\r\n        icon: 'none'\r\n      })\r\n    }\r\n  } catch (error) {\r\n    console.error('保存秘钥失败:', error)\r\n    uni.showToast({\r\n      title: '保存失败',\r\n      icon: 'none'\r\n    })\r\n  } finally {\r\n    saving.value = false\r\n  }\r\n}\r\n\r\n// 初始化秘钥配置\r\nonMounted(() => {\r\n  getSecretKeyList()\r\n})\r\n\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.secret-edit-page {\r\n  background: #F5F5F5;\r\n  min-height: 100vh;\r\n}\r\n\r\n.form-section {\r\n  padding: 20px 32rpx;\r\n\r\n  .form-item {\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 20rpx 16rpx;\r\n    background: #ffffff;\r\n    border-radius: 20rpx;\r\n    margin-bottom: 20px;\r\n\r\n    &:last-child {\r\n      border-bottom: none;\r\n    }\r\n\r\n    .label {\r\n      font-size: 30rpx;\r\n      color: #1a1a1a;\r\n      font-weight: 500;\r\n      width: 120rpx;\r\n      flex-shrink: 0;\r\n    }\r\n\r\n    .input {\r\n      flex: 1;\r\n      font-size: 28rpx;\r\n      color: #1a1a1a;\r\n      height: 44rpx;\r\n      line-height: 44rpx;\r\n\r\n      &.placeholder {\r\n        color: #CCCCCC;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.save-section {\r\n  padding: 80rpx 32rpx;\r\n  padding-bottom: calc(80rpx + env(safe-area-inset-bottom));\r\n  /* Android兼容性修复 */\r\n  padding-bottom: 80rpx;\r\n\r\n  .save-btn {\r\n    width: 100%;\r\n    height: 96rpx;\r\n    background: #3478f6;\r\n    border-radius: 48rpx;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    transition: all 0.3s ease;\r\n\r\n    &.disabled {\r\n      background: #CCCCCC;\r\n    }\r\n\r\n    .save-text {\r\n      font-size: 32rpx;\r\n      color: #ffffff;\r\n      font-weight: 600;\r\n    }\r\n  }\r\n}\r\n\r\n/* 占位符样式 */\r\n.placeholder {\r\n  color: #CCCCCC !important;\r\n}\r\n</style>", "import MiniProgramPage from 'E:/youngProject/agent-mini-ui/pages/create-agent/secret.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useUserStore", "reactive", "getSecretKeyListApi", "uni", "ref", "saveSecretKeyApi", "onMounted"], "mappings": ";;;;;;;AAwBA,UAAM,YAAYA,YAAAA,aAAc;AAGhC,QAAI,aAAaC,cAAAA,SAAS;AAAA,MACxB,WAAW;AAAA,MACX,eAAe;AAAA,IACjB,CAAC;AAGD,UAAM,mBAAmB,YAAY;AACnC,UAAI;AACF,YAAI,MAAM,MAAMC,8BAAoB;AAAA,UAClC,cAAc,UAAU;AAAA,QAC9B,CAAK;AAED,YAAI,IAAI,SAAS,KAAK,IAAI,QAAQ,IAAI,KAAK,SAAS,GAAG;AAErD,gBAAM,UAAU,IAAI,KAAK,KAAK,UAAQ,KAAK,kBAAkB,cAAc;AAC3E,cAAI,SAAS;AACX,uBAAW,YAAY,QAAQ;AAAA,UAChC;AAAA,QACF;AAAA,MACF,SAAQ,OAAO;AACdC,sBAAAA,MAAA,MAAA,SAAA,uCAAc,aAAa,KAAK;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACF;AAAA,IACH;AAEA,UAAM,SAASC,cAAG,IAAC,KAAK;AAGxB,UAAM,aAAa,YAAY;AAC7B,UAAI,CAAC,WAAW,UAAU,QAAQ;AAChCD,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAED,aAAO,QAAQ;AAEf,UAAI;AACF,cAAM,WAAW;AAAA,UACf,cAAc,UAAU;AAAA,UACxB,mBAAmB;AAAA,YACjB;AAAA,cACE,eAAe,WAAW;AAAA,cAC1B,WAAW,WAAW,UAAU,KAAM;AAAA,YACvC;AAAA,UACF;AAAA,QACF;AAED,YAAI,MAAM,MAAME,UAAgB,iBAAC,QAAQ;AAEzC,YAAI,IAAI,SAAS,GAAG;AAClBF,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AAGD,qBAAW,MAAM;AACfA,0BAAAA,MAAI,aAAc;AAAA,UACnB,GAAE,IAAI;AAAA,QACb,OAAW;AACLA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO,IAAI,OAAO;AAAA,YAClB,MAAM;AAAA,UACd,CAAO;AAAA,QACF;AAAA,MACF,SAAQ,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,wCAAc,WAAW,KAAK;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACL,UAAY;AACR,eAAO,QAAQ;AAAA,MAChB;AAAA,IACH;AAGAG,kBAAAA,UAAU,MAAM;AACd,uBAAkB;AAAA,IACpB,CAAC;;;;;;;;;;;;;AC/GD,GAAG,WAAW,eAAe;"}