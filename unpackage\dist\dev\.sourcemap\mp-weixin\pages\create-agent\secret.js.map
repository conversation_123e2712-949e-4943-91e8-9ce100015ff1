{"version": 3, "file": "secret.js", "sources": ["pages/create-agent/secret.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvY3JlYXRlLWFnZW50L3NlY3JldC52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"secret-edit-page\">\r\n    <!-- 表单区域 -->\r\n    <view class=\"form-section\">\r\n      <!-- 秘钥 -->\r\n      <view class=\"form-item\">\r\n        <input v-model=\"secretForm.secretKey\" class=\"input\" placeholder=\"输入秘钥\" placeholder-class=\"placeholder\"\r\n          maxlength=\"20\" />\r\n      </view>\r\n    </view>\r\n    <!-- 保存按钮 -->\r\n    <view class=\"save-section\">\r\n      <view class=\"save-btn\" @tap=\"handleSave\">\r\n        <text class=\"save-text\">{{ saving ? '保存中...' : '保存修改' }}</text>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, reactive, onMounted } from 'vue'\r\nimport { getSecretKeyListApi, saveSecretKeyApi } from '@/api/index.js'\r\nimport { useUserStore } from '@/stores/user.js'\r\n\r\nconst userStore = useUserStore()\r\n\r\n\r\nlet secretForm = reactive({\r\n  secretKey: '',\r\n  secretKeyType: 'coze_api_key',\r\n})\r\nconst getSecretKeyList = async () => {\r\n  let res = await getSecretKeyListApi({\r\n    merchantGuid: userStore.merchantGuid,\r\n  })\r\n}\r\nconst saving = ref(false)\r\nconst handleSave = () => {\r\n\r\n}\r\n// 初始化秘钥配置\r\nonMounted(() => {\r\n  getSecretKeyList()\r\n})\r\n\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.secret-edit-page {\r\n  background: #F5F5F5;\r\n  min-height: 100vh;\r\n}\r\n\r\n.form-section {\r\n  padding: 20px 32rpx;\r\n\r\n  .form-item {\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 20rpx 16rpx;\r\n    background: #ffffff;\r\n    border-radius: 20rpx;\r\n    margin-bottom: 20px;\r\n\r\n    &:last-child {\r\n      border-bottom: none;\r\n    }\r\n\r\n    .label {\r\n      font-size: 30rpx;\r\n      color: #1a1a1a;\r\n      font-weight: 500;\r\n      width: 120rpx;\r\n      flex-shrink: 0;\r\n    }\r\n\r\n    .input {\r\n      flex: 1;\r\n      font-size: 28rpx;\r\n      color: #1a1a1a;\r\n      margin-left: 24rpx;\r\n      height: 44rpx;\r\n      line-height: 44rpx;\r\n\r\n      &.placeholder {\r\n        color: #CCCCCC;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.save-section {\r\n  padding: 80rpx 32rpx;\r\n  padding-bottom: calc(80rpx + env(safe-area-inset-bottom));\r\n\r\n  .save-btn {\r\n    width: 100%;\r\n    height: 96rpx;\r\n    background: #3478f6;\r\n    border-radius: 48rpx;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    transition: all 0.3s ease;\r\n\r\n    &.disabled {\r\n      background: #CCCCCC;\r\n    }\r\n\r\n    .save-text {\r\n      font-size: 32rpx;\r\n      color: #ffffff;\r\n      font-weight: 600;\r\n    }\r\n  }\r\n}\r\n\r\n/* 占位符样式 */\r\n.placeholder {\r\n  color: #CCCCCC !important;\r\n}\r\n</style>", "import MiniProgramPage from 'E:/youngProject/agent-mini-ui/pages/create-agent/secret.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useUserStore", "reactive", "getSecretKeyListApi", "ref", "onMounted"], "mappings": ";;;;;;;AAwBA,UAAM,YAAYA,YAAAA,aAAc;AAGhC,QAAI,aAAaC,cAAAA,SAAS;AAAA,MACxB,WAAW;AAAA,MACX,eAAe;AAAA,IACjB,CAAC;AACD,UAAM,mBAAmB,YAAY;AACzB,YAAMC,8BAAoB;AAAA,QAClC,cAAc,UAAU;AAAA,MAC5B,CAAG;AAAA,IACH;AACA,UAAM,SAASC,cAAG,IAAC,KAAK;AACxB,UAAM,aAAa,MAAM;AAAA,IAEzB;AAEAC,kBAAAA,UAAU,MAAM;AACd,uBAAkB;AAAA,IACpB,CAAC;;;;;;;;;;;;AC1CD,GAAG,WAAW,eAAe;"}