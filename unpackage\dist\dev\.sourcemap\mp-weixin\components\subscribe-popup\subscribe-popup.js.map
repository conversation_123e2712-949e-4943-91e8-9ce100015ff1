{"version": 3, "file": "subscribe-popup.js", "sources": ["components/subscribe-popup/subscribe-popup.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RToveW91bmdQcm9qZWN0L2FnZW50LW1pbmktdWkvY29tcG9uZW50cy9zdWJzY3JpYmUtcG9wdXAvc3Vic2NyaWJlLXBvcHVwLnZ1ZQ"], "sourcesContent": ["<template>\n  <view v-if=\"showPopup\" class=\"subscribe-popup-overlay\" @tap=\"closePopup\" catchtouchmove=\"true\">\n    <view class=\"subscribe-popup-content\" @tap.stop>\n      <image class=\"bg\" :src=\"bg\" />\n      <!-- 关闭按钮 -->\n      <view class=\"close-btn\" @click=\"closePopup\">\n        <image class=\"icon\" src=\"@/static/msg/<EMAIL>\" mode=\"aspectFit\" />\n      </view>\n      <view class=\"content-box\">\n        <!-- 智能体头像 -->\n        <view class=\"agent-avatar-section\">\n          <image class=\"agent-avatar\" :src=\"agentInfo.agentAvatar\" mode=\"aspectFill\" />\n        </view>\n\n        <!-- 智能体信息 -->\n        <view class=\"agent-info-section\">\n          <text class=\"agent-name\">智能体名称：{{ agentInfo.agentName }}</text>\n          <text class=\"agent-desc\">智能体介绍：{{ agentInfo.agentDesc }}</text>\n        </view>\n\n        <!-- 价格信息 -->\n        <view class=\"price-section\">\n          <text class=\"price-label\">订阅价格：</text>\n          <text class=\"price-value\">¥ {{ agentInfo.price }}</text>\n        </view>\n\n\n        <!-- 订阅按钮 -->\n        <view class=\"subscribe-btn ios\" v-if=\"isIos\">\n          <text class=\"subscribe-text\">IOS不可使用</text>\n        </view>\n        <view class=\"subscribe-btn\" @tap=\"handleSubscribe\" v-else>\n          <text class=\"subscribe-text\">立即订阅</text>\n        </view>\n\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, watch, defineProps, onMounted } from 'vue'\nconst bg = 'https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/b9ed244f8bd849d48375bd6c29b48129.png'\n\n// 定义 props\nconst props = defineProps({\n  show: {\n    type: Boolean,\n    default: false\n  },\n  agentInfo: {\n    type: Object,\n    default: () => ({\n      agentName: '',\n      agentDesc: '',\n      agentAvatar: '',\n      price: 0,\n    })\n  }\n})\nconst isIos = ref(false)\n// 定义 emits\nconst emit = defineEmits(['close', 'subscribe'])\n\n// 响应式数据\nconst showPopup = ref(props.show)\n\n// 监听 props.show 变化\nwatch(() => props.show, (newVal) => {\n  showPopup.value = newVal\n})\n\n// 关闭弹窗\nconst closePopup = () => {\n  showPopup.value = false\n  emit('close')\n}\n\n// 处理订阅\nconst handleSubscribe = () => {\n  emit('subscribe', props.agentInfo)\n}\nonMounted(() => {\n  let systemInfomations = uni.getSystemInfoSync()\n  if (systemInfomations.osName === 'ios') {\n    isIos.value = true\n  }\n})\n\n</script>\n\n<style lang=\"scss\" scoped>\n.subscribe-popup-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background-color: rgba(0, 0, 0, 0.8);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  z-index: 999;\n}\n\n.subscribe-popup-content {\n  height: 1058rpx;\n  width: 750rpx;\n  position: relative;\n\n\n  .bg {\n    width: 100%;\n    height: 100%;\n  }\n\n  .content-box {\n    position: absolute;\n    left: 74rpx;\n    top: 185rpx;\n    width: 600rpx;\n    height: 642rpx;\n    // background-color: rgba(0, 0, 0, 0.3);\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    padding: 0 30rpx;\n    box-sizing: border-box;\n  }\n}\n\n.close-btn {\n  position: absolute;\n  bottom: 116rpx;\n  left: 50%;\n  transform: translateX(-50%);\n  width: 60rpx;\n  height: 60rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n\n  .icon {\n    width: 60rpx;\n    height: 60rpx;\n    display: block;\n  }\n}\n\n.agent-avatar-section {\n  margin-top: -90rpx;\n\n  .agent-avatar {\n    width: 160rpx;\n    height: 160rpx;\n    border-radius: 50%;\n    background: #f0f0f0;\n    border: 6rpx solid #4CAF50;\n  }\n}\n\n.agent-info-section {\n  width: 100%;\n  margin-bottom: 40rpx;\n  text-align: center;\n  margin-top: 60rpx;\n\n  .agent-name {\n    display: block;\n    font-size: 32rpx;\n    color: #333333;\n    font-weight: 600;\n    margin-bottom: 20rpx;\n  }\n\n  .agent-desc {\n    display: block;\n    font-size: 28rpx;\n    color: #666666;\n    line-height: 1.5;\n  }\n}\n\n.price-section {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 30rpx;\n\n  .price-label {\n    font-size: 28rpx;\n    color: #333333;\n    margin-right: 10rpx;\n  }\n\n  .price-value {\n    font-size: 48rpx;\n    color: #3478f6;\n    font-weight: 700;\n  }\n}\n\n\n.subscribe-btn {\n  width: 310rpx;\n  height: 100rpx;\n  background: linear-gradient(99deg, #2A5AF6 0%, #1198FF 100%);\n  border-radius: 50rpx 50rpx 50rpx 50rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-shadow: 0 8rpx 24rpx rgba(52, 120, 246, 0.3);\n\n  &.ios {\n    background: #999;\n  }\n\n  .subscribe-text {\n    font-size: 32rpx;\n    color: #ffffff;\n    font-weight: 600;\n  }\n}\n</style>\n", "import Component from 'E:/youngProject/agent-mini-ui/components/subscribe-popup/subscribe-popup.vue'\nwx.createComponent(Component)"], "names": ["ref", "watch", "onMounted", "uni"], "mappings": ";;;AA0CA,MAAM,KAAK;;;;;;;;;;;;;;;;;;;;AAGX,UAAM,QAAQ;AAed,UAAM,QAAQA,cAAG,IAAC,KAAK;AAEvB,UAAM,OAAO;AAGb,UAAM,YAAYA,cAAAA,IAAI,MAAM,IAAI;AAGhCC,kBAAK,MAAC,MAAM,MAAM,MAAM,CAAC,WAAW;AAClC,gBAAU,QAAQ;AAAA,IACpB,CAAC;AAGD,UAAM,aAAa,MAAM;AACvB,gBAAU,QAAQ;AAClB,WAAK,OAAO;AAAA,IACd;AAGA,UAAM,kBAAkB,MAAM;AAC5B,WAAK,aAAa,MAAM,SAAS;AAAA,IACnC;AACAC,kBAAAA,UAAU,MAAM;AACd,UAAI,oBAAoBC,cAAG,MAAC,kBAAmB;AAC/C,UAAI,kBAAkB,WAAW,OAAO;AACtC,cAAM,QAAQ;AAAA,MACf;AAAA,IACH,CAAC;;;;;;;;;;;;;;;;;;;;;;;;ACtFD,GAAG,gBAAgB,SAAS;"}