{"version": 3, "file": "markdown-it.min.js", "sources": ["components/ua-markdown/lib/markdown-it.min.js"], "sourcesContent": ["function e(e){if(e.__esModule)return e;var r=Object.defineProperty({},\"__esModule\",{value:!0});return Object.keys(e).forEach((function(t){var n=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,n.get?n:{enumerable:!0,get:function(){return e[t]}})})),r}var r={},t={Aacute:\"Á\",aacute:\"á\",Abreve:\"Ă\",abreve:\"ă\",ac:\"∾\",acd:\"∿\",acE:\"∾̳\",Acirc:\"Â\",acirc:\"â\",acute:\"´\",Acy:\"А\",acy:\"а\",AElig:\"Æ\",aelig:\"æ\",af:\"⁡\",Afr:\"𝔄\",afr:\"𝔞\",Agrave:\"À\",agrave:\"à\",alefsym:\"ℵ\",aleph:\"ℵ\",Alpha:\"Α\",alpha:\"α\",Amacr:\"Ā\",amacr:\"ā\",amalg:\"⨿\",amp:\"&\",AMP:\"&\",andand:\"⩕\",And:\"⩓\",and:\"∧\",andd:\"⩜\",andslope:\"⩘\",andv:\"⩚\",ang:\"∠\",ange:\"⦤\",angle:\"∠\",angmsdaa:\"⦨\",angmsdab:\"⦩\",angmsdac:\"⦪\",angmsdad:\"⦫\",angmsdae:\"⦬\",angmsdaf:\"⦭\",angmsdag:\"⦮\",angmsdah:\"⦯\",angmsd:\"∡\",angrt:\"∟\",angrtvb:\"⊾\",angrtvbd:\"⦝\",angsph:\"∢\",angst:\"Å\",angzarr:\"⍼\",Aogon:\"Ą\",aogon:\"ą\",Aopf:\"𝔸\",aopf:\"𝕒\",apacir:\"⩯\",ap:\"≈\",apE:\"⩰\",ape:\"≊\",apid:\"≋\",apos:\"'\",ApplyFunction:\"⁡\",approx:\"≈\",approxeq:\"≊\",Aring:\"Å\",aring:\"å\",Ascr:\"𝒜\",ascr:\"𝒶\",Assign:\"≔\",ast:\"*\",asymp:\"≈\",asympeq:\"≍\",Atilde:\"Ã\",atilde:\"ã\",Auml:\"Ä\",auml:\"ä\",awconint:\"∳\",awint:\"⨑\",backcong:\"≌\",backepsilon:\"϶\",backprime:\"‵\",backsim:\"∽\",backsimeq:\"⋍\",Backslash:\"∖\",Barv:\"⫧\",barvee:\"⊽\",barwed:\"⌅\",Barwed:\"⌆\",barwedge:\"⌅\",bbrk:\"⎵\",bbrktbrk:\"⎶\",bcong:\"≌\",Bcy:\"Б\",bcy:\"б\",bdquo:\"„\",becaus:\"∵\",because:\"∵\",Because:\"∵\",bemptyv:\"⦰\",bepsi:\"϶\",bernou:\"ℬ\",Bernoullis:\"ℬ\",Beta:\"Β\",beta:\"β\",beth:\"ℶ\",between:\"≬\",Bfr:\"𝔅\",bfr:\"𝔟\",bigcap:\"⋂\",bigcirc:\"◯\",bigcup:\"⋃\",bigodot:\"⨀\",bigoplus:\"⨁\",bigotimes:\"⨂\",bigsqcup:\"⨆\",bigstar:\"★\",bigtriangledown:\"▽\",bigtriangleup:\"△\",biguplus:\"⨄\",bigvee:\"⋁\",bigwedge:\"⋀\",bkarow:\"⤍\",blacklozenge:\"⧫\",blacksquare:\"▪\",blacktriangle:\"▴\",blacktriangledown:\"▾\",blacktriangleleft:\"◂\",blacktriangleright:\"▸\",blank:\"␣\",blk12:\"▒\",blk14:\"░\",blk34:\"▓\",block:\"█\",bne:\"=⃥\",bnequiv:\"≡⃥\",bNot:\"⫭\",bnot:\"⌐\",Bopf:\"𝔹\",bopf:\"𝕓\",bot:\"⊥\",bottom:\"⊥\",bowtie:\"⋈\",boxbox:\"⧉\",boxdl:\"┐\",boxdL:\"╕\",boxDl:\"╖\",boxDL:\"╗\",boxdr:\"┌\",boxdR:\"╒\",boxDr:\"╓\",boxDR:\"╔\",boxh:\"─\",boxH:\"═\",boxhd:\"┬\",boxHd:\"╤\",boxhD:\"╥\",boxHD:\"╦\",boxhu:\"┴\",boxHu:\"╧\",boxhU:\"╨\",boxHU:\"╩\",boxminus:\"⊟\",boxplus:\"⊞\",boxtimes:\"⊠\",boxul:\"┘\",boxuL:\"╛\",boxUl:\"╜\",boxUL:\"╝\",boxur:\"└\",boxuR:\"╘\",boxUr:\"╙\",boxUR:\"╚\",boxv:\"│\",boxV:\"║\",boxvh:\"┼\",boxvH:\"╪\",boxVh:\"╫\",boxVH:\"╬\",boxvl:\"┤\",boxvL:\"╡\",boxVl:\"╢\",boxVL:\"╣\",boxvr:\"├\",boxvR:\"╞\",boxVr:\"╟\",boxVR:\"╠\",bprime:\"‵\",breve:\"˘\",Breve:\"˘\",brvbar:\"¦\",bscr:\"𝒷\",Bscr:\"ℬ\",bsemi:\"⁏\",bsim:\"∽\",bsime:\"⋍\",bsolb:\"⧅\",bsol:\"\\\\\",bsolhsub:\"⟈\",bull:\"•\",bullet:\"•\",bump:\"≎\",bumpE:\"⪮\",bumpe:\"≏\",Bumpeq:\"≎\",bumpeq:\"≏\",Cacute:\"Ć\",cacute:\"ć\",capand:\"⩄\",capbrcup:\"⩉\",capcap:\"⩋\",cap:\"∩\",Cap:\"⋒\",capcup:\"⩇\",capdot:\"⩀\",CapitalDifferentialD:\"ⅅ\",caps:\"∩︀\",caret:\"⁁\",caron:\"ˇ\",Cayleys:\"ℭ\",ccaps:\"⩍\",Ccaron:\"Č\",ccaron:\"č\",Ccedil:\"Ç\",ccedil:\"ç\",Ccirc:\"Ĉ\",ccirc:\"ĉ\",Cconint:\"∰\",ccups:\"⩌\",ccupssm:\"⩐\",Cdot:\"Ċ\",cdot:\"ċ\",cedil:\"¸\",Cedilla:\"¸\",cemptyv:\"⦲\",cent:\"¢\",centerdot:\"·\",CenterDot:\"·\",cfr:\"𝔠\",Cfr:\"ℭ\",CHcy:\"Ч\",chcy:\"ч\",check:\"✓\",checkmark:\"✓\",Chi:\"Χ\",chi:\"χ\",circ:\"ˆ\",circeq:\"≗\",circlearrowleft:\"↺\",circlearrowright:\"↻\",circledast:\"⊛\",circledcirc:\"⊚\",circleddash:\"⊝\",CircleDot:\"⊙\",circledR:\"®\",circledS:\"Ⓢ\",CircleMinus:\"⊖\",CirclePlus:\"⊕\",CircleTimes:\"⊗\",cir:\"○\",cirE:\"⧃\",cire:\"≗\",cirfnint:\"⨐\",cirmid:\"⫯\",cirscir:\"⧂\",ClockwiseContourIntegral:\"∲\",CloseCurlyDoubleQuote:\"”\",CloseCurlyQuote:\"’\",clubs:\"♣\",clubsuit:\"♣\",colon:\":\",Colon:\"∷\",Colone:\"⩴\",colone:\"≔\",coloneq:\"≔\",comma:\",\",commat:\"@\",comp:\"∁\",compfn:\"∘\",complement:\"∁\",complexes:\"ℂ\",cong:\"≅\",congdot:\"⩭\",Congruent:\"≡\",conint:\"∮\",Conint:\"∯\",ContourIntegral:\"∮\",copf:\"𝕔\",Copf:\"ℂ\",coprod:\"∐\",Coproduct:\"∐\",copy:\"©\",COPY:\"©\",copysr:\"℗\",CounterClockwiseContourIntegral:\"∳\",crarr:\"↵\",cross:\"✗\",Cross:\"⨯\",Cscr:\"𝒞\",cscr:\"𝒸\",csub:\"⫏\",csube:\"⫑\",csup:\"⫐\",csupe:\"⫒\",ctdot:\"⋯\",cudarrl:\"⤸\",cudarrr:\"⤵\",cuepr:\"⋞\",cuesc:\"⋟\",cularr:\"↶\",cularrp:\"⤽\",cupbrcap:\"⩈\",cupcap:\"⩆\",CupCap:\"≍\",cup:\"∪\",Cup:\"⋓\",cupcup:\"⩊\",cupdot:\"⊍\",cupor:\"⩅\",cups:\"∪︀\",curarr:\"↷\",curarrm:\"⤼\",curlyeqprec:\"⋞\",curlyeqsucc:\"⋟\",curlyvee:\"⋎\",curlywedge:\"⋏\",curren:\"¤\",curvearrowleft:\"↶\",curvearrowright:\"↷\",cuvee:\"⋎\",cuwed:\"⋏\",cwconint:\"∲\",cwint:\"∱\",cylcty:\"⌭\",dagger:\"†\",Dagger:\"‡\",daleth:\"ℸ\",darr:\"↓\",Darr:\"↡\",dArr:\"⇓\",dash:\"‐\",Dashv:\"⫤\",dashv:\"⊣\",dbkarow:\"⤏\",dblac:\"˝\",Dcaron:\"Ď\",dcaron:\"ď\",Dcy:\"Д\",dcy:\"д\",ddagger:\"‡\",ddarr:\"⇊\",DD:\"ⅅ\",dd:\"ⅆ\",DDotrahd:\"⤑\",ddotseq:\"⩷\",deg:\"°\",Del:\"∇\",Delta:\"Δ\",delta:\"δ\",demptyv:\"⦱\",dfisht:\"⥿\",Dfr:\"𝔇\",dfr:\"𝔡\",dHar:\"⥥\",dharl:\"⇃\",dharr:\"⇂\",DiacriticalAcute:\"´\",DiacriticalDot:\"˙\",DiacriticalDoubleAcute:\"˝\",DiacriticalGrave:\"`\",DiacriticalTilde:\"˜\",diam:\"⋄\",diamond:\"⋄\",Diamond:\"⋄\",diamondsuit:\"♦\",diams:\"♦\",die:\"¨\",DifferentialD:\"ⅆ\",digamma:\"ϝ\",disin:\"⋲\",div:\"÷\",divide:\"÷\",divideontimes:\"⋇\",divonx:\"⋇\",DJcy:\"Ђ\",djcy:\"ђ\",dlcorn:\"⌞\",dlcrop:\"⌍\",dollar:\"$\",Dopf:\"𝔻\",dopf:\"𝕕\",Dot:\"¨\",dot:\"˙\",DotDot:\"⃜\",doteq:\"≐\",doteqdot:\"≑\",DotEqual:\"≐\",dotminus:\"∸\",dotplus:\"∔\",dotsquare:\"⊡\",doublebarwedge:\"⌆\",DoubleContourIntegral:\"∯\",DoubleDot:\"¨\",DoubleDownArrow:\"⇓\",DoubleLeftArrow:\"⇐\",DoubleLeftRightArrow:\"⇔\",DoubleLeftTee:\"⫤\",DoubleLongLeftArrow:\"⟸\",DoubleLongLeftRightArrow:\"⟺\",DoubleLongRightArrow:\"⟹\",DoubleRightArrow:\"⇒\",DoubleRightTee:\"⊨\",DoubleUpArrow:\"⇑\",DoubleUpDownArrow:\"⇕\",DoubleVerticalBar:\"∥\",DownArrowBar:\"⤓\",downarrow:\"↓\",DownArrow:\"↓\",Downarrow:\"⇓\",DownArrowUpArrow:\"⇵\",DownBreve:\"̑\",downdownarrows:\"⇊\",downharpoonleft:\"⇃\",downharpoonright:\"⇂\",DownLeftRightVector:\"⥐\",DownLeftTeeVector:\"⥞\",DownLeftVectorBar:\"⥖\",DownLeftVector:\"↽\",DownRightTeeVector:\"⥟\",DownRightVectorBar:\"⥗\",DownRightVector:\"⇁\",DownTeeArrow:\"↧\",DownTee:\"⊤\",drbkarow:\"⤐\",drcorn:\"⌟\",drcrop:\"⌌\",Dscr:\"𝒟\",dscr:\"𝒹\",DScy:\"Ѕ\",dscy:\"ѕ\",dsol:\"⧶\",Dstrok:\"Đ\",dstrok:\"đ\",dtdot:\"⋱\",dtri:\"▿\",dtrif:\"▾\",duarr:\"⇵\",duhar:\"⥯\",dwangle:\"⦦\",DZcy:\"Џ\",dzcy:\"џ\",dzigrarr:\"⟿\",Eacute:\"É\",eacute:\"é\",easter:\"⩮\",Ecaron:\"Ě\",ecaron:\"ě\",Ecirc:\"Ê\",ecirc:\"ê\",ecir:\"≖\",ecolon:\"≕\",Ecy:\"Э\",ecy:\"э\",eDDot:\"⩷\",Edot:\"Ė\",edot:\"ė\",eDot:\"≑\",ee:\"ⅇ\",efDot:\"≒\",Efr:\"𝔈\",efr:\"𝔢\",eg:\"⪚\",Egrave:\"È\",egrave:\"è\",egs:\"⪖\",egsdot:\"⪘\",el:\"⪙\",Element:\"∈\",elinters:\"⏧\",ell:\"ℓ\",els:\"⪕\",elsdot:\"⪗\",Emacr:\"Ē\",emacr:\"ē\",empty:\"∅\",emptyset:\"∅\",EmptySmallSquare:\"◻\",emptyv:\"∅\",EmptyVerySmallSquare:\"▫\",emsp13:\" \",emsp14:\" \",emsp:\" \",ENG:\"Ŋ\",eng:\"ŋ\",ensp:\" \",Eogon:\"Ę\",eogon:\"ę\",Eopf:\"𝔼\",eopf:\"𝕖\",epar:\"⋕\",eparsl:\"⧣\",eplus:\"⩱\",epsi:\"ε\",Epsilon:\"Ε\",epsilon:\"ε\",epsiv:\"ϵ\",eqcirc:\"≖\",eqcolon:\"≕\",eqsim:\"≂\",eqslantgtr:\"⪖\",eqslantless:\"⪕\",Equal:\"⩵\",equals:\"=\",EqualTilde:\"≂\",equest:\"≟\",Equilibrium:\"⇌\",equiv:\"≡\",equivDD:\"⩸\",eqvparsl:\"⧥\",erarr:\"⥱\",erDot:\"≓\",escr:\"ℯ\",Escr:\"ℰ\",esdot:\"≐\",Esim:\"⩳\",esim:\"≂\",Eta:\"Η\",eta:\"η\",ETH:\"Ð\",eth:\"ð\",Euml:\"Ë\",euml:\"ë\",euro:\"€\",excl:\"!\",exist:\"∃\",Exists:\"∃\",expectation:\"ℰ\",exponentiale:\"ⅇ\",ExponentialE:\"ⅇ\",fallingdotseq:\"≒\",Fcy:\"Ф\",fcy:\"ф\",female:\"♀\",ffilig:\"ﬃ\",fflig:\"ﬀ\",ffllig:\"ﬄ\",Ffr:\"𝔉\",ffr:\"𝔣\",filig:\"ﬁ\",FilledSmallSquare:\"◼\",FilledVerySmallSquare:\"▪\",fjlig:\"fj\",flat:\"♭\",fllig:\"ﬂ\",fltns:\"▱\",fnof:\"ƒ\",Fopf:\"𝔽\",fopf:\"𝕗\",forall:\"∀\",ForAll:\"∀\",fork:\"⋔\",forkv:\"⫙\",Fouriertrf:\"ℱ\",fpartint:\"⨍\",frac12:\"½\",frac13:\"⅓\",frac14:\"¼\",frac15:\"⅕\",frac16:\"⅙\",frac18:\"⅛\",frac23:\"⅔\",frac25:\"⅖\",frac34:\"¾\",frac35:\"⅗\",frac38:\"⅜\",frac45:\"⅘\",frac56:\"⅚\",frac58:\"⅝\",frac78:\"⅞\",frasl:\"⁄\",frown:\"⌢\",fscr:\"𝒻\",Fscr:\"ℱ\",gacute:\"ǵ\",Gamma:\"Γ\",gamma:\"γ\",Gammad:\"Ϝ\",gammad:\"ϝ\",gap:\"⪆\",Gbreve:\"Ğ\",gbreve:\"ğ\",Gcedil:\"Ģ\",Gcirc:\"Ĝ\",gcirc:\"ĝ\",Gcy:\"Г\",gcy:\"г\",Gdot:\"Ġ\",gdot:\"ġ\",ge:\"≥\",gE:\"≧\",gEl:\"⪌\",gel:\"⋛\",geq:\"≥\",geqq:\"≧\",geqslant:\"⩾\",gescc:\"⪩\",ges:\"⩾\",gesdot:\"⪀\",gesdoto:\"⪂\",gesdotol:\"⪄\",gesl:\"⋛︀\",gesles:\"⪔\",Gfr:\"𝔊\",gfr:\"𝔤\",gg:\"≫\",Gg:\"⋙\",ggg:\"⋙\",gimel:\"ℷ\",GJcy:\"Ѓ\",gjcy:\"ѓ\",gla:\"⪥\",gl:\"≷\",glE:\"⪒\",glj:\"⪤\",gnap:\"⪊\",gnapprox:\"⪊\",gne:\"⪈\",gnE:\"≩\",gneq:\"⪈\",gneqq:\"≩\",gnsim:\"⋧\",Gopf:\"𝔾\",gopf:\"𝕘\",grave:\"`\",GreaterEqual:\"≥\",GreaterEqualLess:\"⋛\",GreaterFullEqual:\"≧\",GreaterGreater:\"⪢\",GreaterLess:\"≷\",GreaterSlantEqual:\"⩾\",GreaterTilde:\"≳\",Gscr:\"𝒢\",gscr:\"ℊ\",gsim:\"≳\",gsime:\"⪎\",gsiml:\"⪐\",gtcc:\"⪧\",gtcir:\"⩺\",gt:\">\",GT:\">\",Gt:\"≫\",gtdot:\"⋗\",gtlPar:\"⦕\",gtquest:\"⩼\",gtrapprox:\"⪆\",gtrarr:\"⥸\",gtrdot:\"⋗\",gtreqless:\"⋛\",gtreqqless:\"⪌\",gtrless:\"≷\",gtrsim:\"≳\",gvertneqq:\"≩︀\",gvnE:\"≩︀\",Hacek:\"ˇ\",hairsp:\" \",half:\"½\",hamilt:\"ℋ\",HARDcy:\"Ъ\",hardcy:\"ъ\",harrcir:\"⥈\",harr:\"↔\",hArr:\"⇔\",harrw:\"↭\",Hat:\"^\",hbar:\"ℏ\",Hcirc:\"Ĥ\",hcirc:\"ĥ\",hearts:\"♥\",heartsuit:\"♥\",hellip:\"…\",hercon:\"⊹\",hfr:\"𝔥\",Hfr:\"ℌ\",HilbertSpace:\"ℋ\",hksearow:\"⤥\",hkswarow:\"⤦\",hoarr:\"⇿\",homtht:\"∻\",hookleftarrow:\"↩\",hookrightarrow:\"↪\",hopf:\"𝕙\",Hopf:\"ℍ\",horbar:\"―\",HorizontalLine:\"─\",hscr:\"𝒽\",Hscr:\"ℋ\",hslash:\"ℏ\",Hstrok:\"Ħ\",hstrok:\"ħ\",HumpDownHump:\"≎\",HumpEqual:\"≏\",hybull:\"⁃\",hyphen:\"‐\",Iacute:\"Í\",iacute:\"í\",ic:\"⁣\",Icirc:\"Î\",icirc:\"î\",Icy:\"И\",icy:\"и\",Idot:\"İ\",IEcy:\"Е\",iecy:\"е\",iexcl:\"¡\",iff:\"⇔\",ifr:\"𝔦\",Ifr:\"ℑ\",Igrave:\"Ì\",igrave:\"ì\",ii:\"ⅈ\",iiiint:\"⨌\",iiint:\"∭\",iinfin:\"⧜\",iiota:\"℩\",IJlig:\"Ĳ\",ijlig:\"ĳ\",Imacr:\"Ī\",imacr:\"ī\",image:\"ℑ\",ImaginaryI:\"ⅈ\",imagline:\"ℐ\",imagpart:\"ℑ\",imath:\"ı\",Im:\"ℑ\",imof:\"⊷\",imped:\"Ƶ\",Implies:\"⇒\",incare:\"℅\",in:\"∈\",infin:\"∞\",infintie:\"⧝\",inodot:\"ı\",intcal:\"⊺\",int:\"∫\",Int:\"∬\",integers:\"ℤ\",Integral:\"∫\",intercal:\"⊺\",Intersection:\"⋂\",intlarhk:\"⨗\",intprod:\"⨼\",InvisibleComma:\"⁣\",InvisibleTimes:\"⁢\",IOcy:\"Ё\",iocy:\"ё\",Iogon:\"Į\",iogon:\"į\",Iopf:\"𝕀\",iopf:\"𝕚\",Iota:\"Ι\",iota:\"ι\",iprod:\"⨼\",iquest:\"¿\",iscr:\"𝒾\",Iscr:\"ℐ\",isin:\"∈\",isindot:\"⋵\",isinE:\"⋹\",isins:\"⋴\",isinsv:\"⋳\",isinv:\"∈\",it:\"⁢\",Itilde:\"Ĩ\",itilde:\"ĩ\",Iukcy:\"І\",iukcy:\"і\",Iuml:\"Ï\",iuml:\"ï\",Jcirc:\"Ĵ\",jcirc:\"ĵ\",Jcy:\"Й\",jcy:\"й\",Jfr:\"𝔍\",jfr:\"𝔧\",jmath:\"ȷ\",Jopf:\"𝕁\",jopf:\"𝕛\",Jscr:\"𝒥\",jscr:\"𝒿\",Jsercy:\"Ј\",jsercy:\"ј\",Jukcy:\"Є\",jukcy:\"є\",Kappa:\"Κ\",kappa:\"κ\",kappav:\"ϰ\",Kcedil:\"Ķ\",kcedil:\"ķ\",Kcy:\"К\",kcy:\"к\",Kfr:\"𝔎\",kfr:\"𝔨\",kgreen:\"ĸ\",KHcy:\"Х\",khcy:\"х\",KJcy:\"Ќ\",kjcy:\"ќ\",Kopf:\"𝕂\",kopf:\"𝕜\",Kscr:\"𝒦\",kscr:\"𝓀\",lAarr:\"⇚\",Lacute:\"Ĺ\",lacute:\"ĺ\",laemptyv:\"⦴\",lagran:\"ℒ\",Lambda:\"Λ\",lambda:\"λ\",lang:\"⟨\",Lang:\"⟪\",langd:\"⦑\",langle:\"⟨\",lap:\"⪅\",Laplacetrf:\"ℒ\",laquo:\"«\",larrb:\"⇤\",larrbfs:\"⤟\",larr:\"←\",Larr:\"↞\",lArr:\"⇐\",larrfs:\"⤝\",larrhk:\"↩\",larrlp:\"↫\",larrpl:\"⤹\",larrsim:\"⥳\",larrtl:\"↢\",latail:\"⤙\",lAtail:\"⤛\",lat:\"⪫\",late:\"⪭\",lates:\"⪭︀\",lbarr:\"⤌\",lBarr:\"⤎\",lbbrk:\"❲\",lbrace:\"{\",lbrack:\"[\",lbrke:\"⦋\",lbrksld:\"⦏\",lbrkslu:\"⦍\",Lcaron:\"Ľ\",lcaron:\"ľ\",Lcedil:\"Ļ\",lcedil:\"ļ\",lceil:\"⌈\",lcub:\"{\",Lcy:\"Л\",lcy:\"л\",ldca:\"⤶\",ldquo:\"“\",ldquor:\"„\",ldrdhar:\"⥧\",ldrushar:\"⥋\",ldsh:\"↲\",le:\"≤\",lE:\"≦\",LeftAngleBracket:\"⟨\",LeftArrowBar:\"⇤\",leftarrow:\"←\",LeftArrow:\"←\",Leftarrow:\"⇐\",LeftArrowRightArrow:\"⇆\",leftarrowtail:\"↢\",LeftCeiling:\"⌈\",LeftDoubleBracket:\"⟦\",LeftDownTeeVector:\"⥡\",LeftDownVectorBar:\"⥙\",LeftDownVector:\"⇃\",LeftFloor:\"⌊\",leftharpoondown:\"↽\",leftharpoonup:\"↼\",leftleftarrows:\"⇇\",leftrightarrow:\"↔\",LeftRightArrow:\"↔\",Leftrightarrow:\"⇔\",leftrightarrows:\"⇆\",leftrightharpoons:\"⇋\",leftrightsquigarrow:\"↭\",LeftRightVector:\"⥎\",LeftTeeArrow:\"↤\",LeftTee:\"⊣\",LeftTeeVector:\"⥚\",leftthreetimes:\"⋋\",LeftTriangleBar:\"⧏\",LeftTriangle:\"⊲\",LeftTriangleEqual:\"⊴\",LeftUpDownVector:\"⥑\",LeftUpTeeVector:\"⥠\",LeftUpVectorBar:\"⥘\",LeftUpVector:\"↿\",LeftVectorBar:\"⥒\",LeftVector:\"↼\",lEg:\"⪋\",leg:\"⋚\",leq:\"≤\",leqq:\"≦\",leqslant:\"⩽\",lescc:\"⪨\",les:\"⩽\",lesdot:\"⩿\",lesdoto:\"⪁\",lesdotor:\"⪃\",lesg:\"⋚︀\",lesges:\"⪓\",lessapprox:\"⪅\",lessdot:\"⋖\",lesseqgtr:\"⋚\",lesseqqgtr:\"⪋\",LessEqualGreater:\"⋚\",LessFullEqual:\"≦\",LessGreater:\"≶\",lessgtr:\"≶\",LessLess:\"⪡\",lesssim:\"≲\",LessSlantEqual:\"⩽\",LessTilde:\"≲\",lfisht:\"⥼\",lfloor:\"⌊\",Lfr:\"𝔏\",lfr:\"𝔩\",lg:\"≶\",lgE:\"⪑\",lHar:\"⥢\",lhard:\"↽\",lharu:\"↼\",lharul:\"⥪\",lhblk:\"▄\",LJcy:\"Љ\",ljcy:\"љ\",llarr:\"⇇\",ll:\"≪\",Ll:\"⋘\",llcorner:\"⌞\",Lleftarrow:\"⇚\",llhard:\"⥫\",lltri:\"◺\",Lmidot:\"Ŀ\",lmidot:\"ŀ\",lmoustache:\"⎰\",lmoust:\"⎰\",lnap:\"⪉\",lnapprox:\"⪉\",lne:\"⪇\",lnE:\"≨\",lneq:\"⪇\",lneqq:\"≨\",lnsim:\"⋦\",loang:\"⟬\",loarr:\"⇽\",lobrk:\"⟦\",longleftarrow:\"⟵\",LongLeftArrow:\"⟵\",Longleftarrow:\"⟸\",longleftrightarrow:\"⟷\",LongLeftRightArrow:\"⟷\",Longleftrightarrow:\"⟺\",longmapsto:\"⟼\",longrightarrow:\"⟶\",LongRightArrow:\"⟶\",Longrightarrow:\"⟹\",looparrowleft:\"↫\",looparrowright:\"↬\",lopar:\"⦅\",Lopf:\"𝕃\",lopf:\"𝕝\",loplus:\"⨭\",lotimes:\"⨴\",lowast:\"∗\",lowbar:\"_\",LowerLeftArrow:\"↙\",LowerRightArrow:\"↘\",loz:\"◊\",lozenge:\"◊\",lozf:\"⧫\",lpar:\"(\",lparlt:\"⦓\",lrarr:\"⇆\",lrcorner:\"⌟\",lrhar:\"⇋\",lrhard:\"⥭\",lrm:\"‎\",lrtri:\"⊿\",lsaquo:\"‹\",lscr:\"𝓁\",Lscr:\"ℒ\",lsh:\"↰\",Lsh:\"↰\",lsim:\"≲\",lsime:\"⪍\",lsimg:\"⪏\",lsqb:\"[\",lsquo:\"‘\",lsquor:\"‚\",Lstrok:\"Ł\",lstrok:\"ł\",ltcc:\"⪦\",ltcir:\"⩹\",lt:\"<\",LT:\"<\",Lt:\"≪\",ltdot:\"⋖\",lthree:\"⋋\",ltimes:\"⋉\",ltlarr:\"⥶\",ltquest:\"⩻\",ltri:\"◃\",ltrie:\"⊴\",ltrif:\"◂\",ltrPar:\"⦖\",lurdshar:\"⥊\",luruhar:\"⥦\",lvertneqq:\"≨︀\",lvnE:\"≨︀\",macr:\"¯\",male:\"♂\",malt:\"✠\",maltese:\"✠\",Map:\"⤅\",map:\"↦\",mapsto:\"↦\",mapstodown:\"↧\",mapstoleft:\"↤\",mapstoup:\"↥\",marker:\"▮\",mcomma:\"⨩\",Mcy:\"М\",mcy:\"м\",mdash:\"—\",mDDot:\"∺\",measuredangle:\"∡\",MediumSpace:\" \",Mellintrf:\"ℳ\",Mfr:\"𝔐\",mfr:\"𝔪\",mho:\"℧\",micro:\"µ\",midast:\"*\",midcir:\"⫰\",mid:\"∣\",middot:\"·\",minusb:\"⊟\",minus:\"−\",minusd:\"∸\",minusdu:\"⨪\",MinusPlus:\"∓\",mlcp:\"⫛\",mldr:\"…\",mnplus:\"∓\",models:\"⊧\",Mopf:\"𝕄\",mopf:\"𝕞\",mp:\"∓\",mscr:\"𝓂\",Mscr:\"ℳ\",mstpos:\"∾\",Mu:\"Μ\",mu:\"μ\",multimap:\"⊸\",mumap:\"⊸\",nabla:\"∇\",Nacute:\"Ń\",nacute:\"ń\",nang:\"∠⃒\",nap:\"≉\",napE:\"⩰̸\",napid:\"≋̸\",napos:\"ŉ\",napprox:\"≉\",natural:\"♮\",naturals:\"ℕ\",natur:\"♮\",nbsp:\" \",nbump:\"≎̸\",nbumpe:\"≏̸\",ncap:\"⩃\",Ncaron:\"Ň\",ncaron:\"ň\",Ncedil:\"Ņ\",ncedil:\"ņ\",ncong:\"≇\",ncongdot:\"⩭̸\",ncup:\"⩂\",Ncy:\"Н\",ncy:\"н\",ndash:\"–\",nearhk:\"⤤\",nearr:\"↗\",neArr:\"⇗\",nearrow:\"↗\",ne:\"≠\",nedot:\"≐̸\",NegativeMediumSpace:\"​\",NegativeThickSpace:\"​\",NegativeThinSpace:\"​\",NegativeVeryThinSpace:\"​\",nequiv:\"≢\",nesear:\"⤨\",nesim:\"≂̸\",NestedGreaterGreater:\"≫\",NestedLessLess:\"≪\",NewLine:\"\\n\",nexist:\"∄\",nexists:\"∄\",Nfr:\"𝔑\",nfr:\"𝔫\",ngE:\"≧̸\",nge:\"≱\",ngeq:\"≱\",ngeqq:\"≧̸\",ngeqslant:\"⩾̸\",nges:\"⩾̸\",nGg:\"⋙̸\",ngsim:\"≵\",nGt:\"≫⃒\",ngt:\"≯\",ngtr:\"≯\",nGtv:\"≫̸\",nharr:\"↮\",nhArr:\"⇎\",nhpar:\"⫲\",ni:\"∋\",nis:\"⋼\",nisd:\"⋺\",niv:\"∋\",NJcy:\"Њ\",njcy:\"њ\",nlarr:\"↚\",nlArr:\"⇍\",nldr:\"‥\",nlE:\"≦̸\",nle:\"≰\",nleftarrow:\"↚\",nLeftarrow:\"⇍\",nleftrightarrow:\"↮\",nLeftrightarrow:\"⇎\",nleq:\"≰\",nleqq:\"≦̸\",nleqslant:\"⩽̸\",nles:\"⩽̸\",nless:\"≮\",nLl:\"⋘̸\",nlsim:\"≴\",nLt:\"≪⃒\",nlt:\"≮\",nltri:\"⋪\",nltrie:\"⋬\",nLtv:\"≪̸\",nmid:\"∤\",NoBreak:\"⁠\",NonBreakingSpace:\" \",nopf:\"𝕟\",Nopf:\"ℕ\",Not:\"⫬\",not:\"¬\",NotCongruent:\"≢\",NotCupCap:\"≭\",NotDoubleVerticalBar:\"∦\",NotElement:\"∉\",NotEqual:\"≠\",NotEqualTilde:\"≂̸\",NotExists:\"∄\",NotGreater:\"≯\",NotGreaterEqual:\"≱\",NotGreaterFullEqual:\"≧̸\",NotGreaterGreater:\"≫̸\",NotGreaterLess:\"≹\",NotGreaterSlantEqual:\"⩾̸\",NotGreaterTilde:\"≵\",NotHumpDownHump:\"≎̸\",NotHumpEqual:\"≏̸\",notin:\"∉\",notindot:\"⋵̸\",notinE:\"⋹̸\",notinva:\"∉\",notinvb:\"⋷\",notinvc:\"⋶\",NotLeftTriangleBar:\"⧏̸\",NotLeftTriangle:\"⋪\",NotLeftTriangleEqual:\"⋬\",NotLess:\"≮\",NotLessEqual:\"≰\",NotLessGreater:\"≸\",NotLessLess:\"≪̸\",NotLessSlantEqual:\"⩽̸\",NotLessTilde:\"≴\",NotNestedGreaterGreater:\"⪢̸\",NotNestedLessLess:\"⪡̸\",notni:\"∌\",notniva:\"∌\",notnivb:\"⋾\",notnivc:\"⋽\",NotPrecedes:\"⊀\",NotPrecedesEqual:\"⪯̸\",NotPrecedesSlantEqual:\"⋠\",NotReverseElement:\"∌\",NotRightTriangleBar:\"⧐̸\",NotRightTriangle:\"⋫\",NotRightTriangleEqual:\"⋭\",NotSquareSubset:\"⊏̸\",NotSquareSubsetEqual:\"⋢\",NotSquareSuperset:\"⊐̸\",NotSquareSupersetEqual:\"⋣\",NotSubset:\"⊂⃒\",NotSubsetEqual:\"⊈\",NotSucceeds:\"⊁\",NotSucceedsEqual:\"⪰̸\",NotSucceedsSlantEqual:\"⋡\",NotSucceedsTilde:\"≿̸\",NotSuperset:\"⊃⃒\",NotSupersetEqual:\"⊉\",NotTilde:\"≁\",NotTildeEqual:\"≄\",NotTildeFullEqual:\"≇\",NotTildeTilde:\"≉\",NotVerticalBar:\"∤\",nparallel:\"∦\",npar:\"∦\",nparsl:\"⫽⃥\",npart:\"∂̸\",npolint:\"⨔\",npr:\"⊀\",nprcue:\"⋠\",nprec:\"⊀\",npreceq:\"⪯̸\",npre:\"⪯̸\",nrarrc:\"⤳̸\",nrarr:\"↛\",nrArr:\"⇏\",nrarrw:\"↝̸\",nrightarrow:\"↛\",nRightarrow:\"⇏\",nrtri:\"⋫\",nrtrie:\"⋭\",nsc:\"⊁\",nsccue:\"⋡\",nsce:\"⪰̸\",Nscr:\"𝒩\",nscr:\"𝓃\",nshortmid:\"∤\",nshortparallel:\"∦\",nsim:\"≁\",nsime:\"≄\",nsimeq:\"≄\",nsmid:\"∤\",nspar:\"∦\",nsqsube:\"⋢\",nsqsupe:\"⋣\",nsub:\"⊄\",nsubE:\"⫅̸\",nsube:\"⊈\",nsubset:\"⊂⃒\",nsubseteq:\"⊈\",nsubseteqq:\"⫅̸\",nsucc:\"⊁\",nsucceq:\"⪰̸\",nsup:\"⊅\",nsupE:\"⫆̸\",nsupe:\"⊉\",nsupset:\"⊃⃒\",nsupseteq:\"⊉\",nsupseteqq:\"⫆̸\",ntgl:\"≹\",Ntilde:\"Ñ\",ntilde:\"ñ\",ntlg:\"≸\",ntriangleleft:\"⋪\",ntrianglelefteq:\"⋬\",ntriangleright:\"⋫\",ntrianglerighteq:\"⋭\",Nu:\"Ν\",nu:\"ν\",num:\"#\",numero:\"№\",numsp:\" \",nvap:\"≍⃒\",nvdash:\"⊬\",nvDash:\"⊭\",nVdash:\"⊮\",nVDash:\"⊯\",nvge:\"≥⃒\",nvgt:\">⃒\",nvHarr:\"⤄\",nvinfin:\"⧞\",nvlArr:\"⤂\",nvle:\"≤⃒\",nvlt:\"<⃒\",nvltrie:\"⊴⃒\",nvrArr:\"⤃\",nvrtrie:\"⊵⃒\",nvsim:\"∼⃒\",nwarhk:\"⤣\",nwarr:\"↖\",nwArr:\"⇖\",nwarrow:\"↖\",nwnear:\"⤧\",Oacute:\"Ó\",oacute:\"ó\",oast:\"⊛\",Ocirc:\"Ô\",ocirc:\"ô\",ocir:\"⊚\",Ocy:\"О\",ocy:\"о\",odash:\"⊝\",Odblac:\"Ő\",odblac:\"ő\",odiv:\"⨸\",odot:\"⊙\",odsold:\"⦼\",OElig:\"Œ\",oelig:\"œ\",ofcir:\"⦿\",Ofr:\"𝔒\",ofr:\"𝔬\",ogon:\"˛\",Ograve:\"Ò\",ograve:\"ò\",ogt:\"⧁\",ohbar:\"⦵\",ohm:\"Ω\",oint:\"∮\",olarr:\"↺\",olcir:\"⦾\",olcross:\"⦻\",oline:\"‾\",olt:\"⧀\",Omacr:\"Ō\",omacr:\"ō\",Omega:\"Ω\",omega:\"ω\",Omicron:\"Ο\",omicron:\"ο\",omid:\"⦶\",ominus:\"⊖\",Oopf:\"𝕆\",oopf:\"𝕠\",opar:\"⦷\",OpenCurlyDoubleQuote:\"“\",OpenCurlyQuote:\"‘\",operp:\"⦹\",oplus:\"⊕\",orarr:\"↻\",Or:\"⩔\",or:\"∨\",ord:\"⩝\",order:\"ℴ\",orderof:\"ℴ\",ordf:\"ª\",ordm:\"º\",origof:\"⊶\",oror:\"⩖\",orslope:\"⩗\",orv:\"⩛\",oS:\"Ⓢ\",Oscr:\"𝒪\",oscr:\"ℴ\",Oslash:\"Ø\",oslash:\"ø\",osol:\"⊘\",Otilde:\"Õ\",otilde:\"õ\",otimesas:\"⨶\",Otimes:\"⨷\",otimes:\"⊗\",Ouml:\"Ö\",ouml:\"ö\",ovbar:\"⌽\",OverBar:\"‾\",OverBrace:\"⏞\",OverBracket:\"⎴\",OverParenthesis:\"⏜\",para:\"¶\",parallel:\"∥\",par:\"∥\",parsim:\"⫳\",parsl:\"⫽\",part:\"∂\",PartialD:\"∂\",Pcy:\"П\",pcy:\"п\",percnt:\"%\",period:\".\",permil:\"‰\",perp:\"⊥\",pertenk:\"‱\",Pfr:\"𝔓\",pfr:\"𝔭\",Phi:\"Φ\",phi:\"φ\",phiv:\"ϕ\",phmmat:\"ℳ\",phone:\"☎\",Pi:\"Π\",pi:\"π\",pitchfork:\"⋔\",piv:\"ϖ\",planck:\"ℏ\",planckh:\"ℎ\",plankv:\"ℏ\",plusacir:\"⨣\",plusb:\"⊞\",pluscir:\"⨢\",plus:\"+\",plusdo:\"∔\",plusdu:\"⨥\",pluse:\"⩲\",PlusMinus:\"±\",plusmn:\"±\",plussim:\"⨦\",plustwo:\"⨧\",pm:\"±\",Poincareplane:\"ℌ\",pointint:\"⨕\",popf:\"𝕡\",Popf:\"ℙ\",pound:\"£\",prap:\"⪷\",Pr:\"⪻\",pr:\"≺\",prcue:\"≼\",precapprox:\"⪷\",prec:\"≺\",preccurlyeq:\"≼\",Precedes:\"≺\",PrecedesEqual:\"⪯\",PrecedesSlantEqual:\"≼\",PrecedesTilde:\"≾\",preceq:\"⪯\",precnapprox:\"⪹\",precneqq:\"⪵\",precnsim:\"⋨\",pre:\"⪯\",prE:\"⪳\",precsim:\"≾\",prime:\"′\",Prime:\"″\",primes:\"ℙ\",prnap:\"⪹\",prnE:\"⪵\",prnsim:\"⋨\",prod:\"∏\",Product:\"∏\",profalar:\"⌮\",profline:\"⌒\",profsurf:\"⌓\",prop:\"∝\",Proportional:\"∝\",Proportion:\"∷\",propto:\"∝\",prsim:\"≾\",prurel:\"⊰\",Pscr:\"𝒫\",pscr:\"𝓅\",Psi:\"Ψ\",psi:\"ψ\",puncsp:\" \",Qfr:\"𝔔\",qfr:\"𝔮\",qint:\"⨌\",qopf:\"𝕢\",Qopf:\"ℚ\",qprime:\"⁗\",Qscr:\"𝒬\",qscr:\"𝓆\",quaternions:\"ℍ\",quatint:\"⨖\",quest:\"?\",questeq:\"≟\",quot:'\"',QUOT:'\"',rAarr:\"⇛\",race:\"∽̱\",Racute:\"Ŕ\",racute:\"ŕ\",radic:\"√\",raemptyv:\"⦳\",rang:\"⟩\",Rang:\"⟫\",rangd:\"⦒\",range:\"⦥\",rangle:\"⟩\",raquo:\"»\",rarrap:\"⥵\",rarrb:\"⇥\",rarrbfs:\"⤠\",rarrc:\"⤳\",rarr:\"→\",Rarr:\"↠\",rArr:\"⇒\",rarrfs:\"⤞\",rarrhk:\"↪\",rarrlp:\"↬\",rarrpl:\"⥅\",rarrsim:\"⥴\",Rarrtl:\"⤖\",rarrtl:\"↣\",rarrw:\"↝\",ratail:\"⤚\",rAtail:\"⤜\",ratio:\"∶\",rationals:\"ℚ\",rbarr:\"⤍\",rBarr:\"⤏\",RBarr:\"⤐\",rbbrk:\"❳\",rbrace:\"}\",rbrack:\"]\",rbrke:\"⦌\",rbrksld:\"⦎\",rbrkslu:\"⦐\",Rcaron:\"Ř\",rcaron:\"ř\",Rcedil:\"Ŗ\",rcedil:\"ŗ\",rceil:\"⌉\",rcub:\"}\",Rcy:\"Р\",rcy:\"р\",rdca:\"⤷\",rdldhar:\"⥩\",rdquo:\"”\",rdquor:\"”\",rdsh:\"↳\",real:\"ℜ\",realine:\"ℛ\",realpart:\"ℜ\",reals:\"ℝ\",Re:\"ℜ\",rect:\"▭\",reg:\"®\",REG:\"®\",ReverseElement:\"∋\",ReverseEquilibrium:\"⇋\",ReverseUpEquilibrium:\"⥯\",rfisht:\"⥽\",rfloor:\"⌋\",rfr:\"𝔯\",Rfr:\"ℜ\",rHar:\"⥤\",rhard:\"⇁\",rharu:\"⇀\",rharul:\"⥬\",Rho:\"Ρ\",rho:\"ρ\",rhov:\"ϱ\",RightAngleBracket:\"⟩\",RightArrowBar:\"⇥\",rightarrow:\"→\",RightArrow:\"→\",Rightarrow:\"⇒\",RightArrowLeftArrow:\"⇄\",rightarrowtail:\"↣\",RightCeiling:\"⌉\",RightDoubleBracket:\"⟧\",RightDownTeeVector:\"⥝\",RightDownVectorBar:\"⥕\",RightDownVector:\"⇂\",RightFloor:\"⌋\",rightharpoondown:\"⇁\",rightharpoonup:\"⇀\",rightleftarrows:\"⇄\",rightleftharpoons:\"⇌\",rightrightarrows:\"⇉\",rightsquigarrow:\"↝\",RightTeeArrow:\"↦\",RightTee:\"⊢\",RightTeeVector:\"⥛\",rightthreetimes:\"⋌\",RightTriangleBar:\"⧐\",RightTriangle:\"⊳\",RightTriangleEqual:\"⊵\",RightUpDownVector:\"⥏\",RightUpTeeVector:\"⥜\",RightUpVectorBar:\"⥔\",RightUpVector:\"↾\",RightVectorBar:\"⥓\",RightVector:\"⇀\",ring:\"˚\",risingdotseq:\"≓\",rlarr:\"⇄\",rlhar:\"⇌\",rlm:\"‏\",rmoustache:\"⎱\",rmoust:\"⎱\",rnmid:\"⫮\",roang:\"⟭\",roarr:\"⇾\",robrk:\"⟧\",ropar:\"⦆\",ropf:\"𝕣\",Ropf:\"ℝ\",roplus:\"⨮\",rotimes:\"⨵\",RoundImplies:\"⥰\",rpar:\")\",rpargt:\"⦔\",rppolint:\"⨒\",rrarr:\"⇉\",Rrightarrow:\"⇛\",rsaquo:\"›\",rscr:\"𝓇\",Rscr:\"ℛ\",rsh:\"↱\",Rsh:\"↱\",rsqb:\"]\",rsquo:\"’\",rsquor:\"’\",rthree:\"⋌\",rtimes:\"⋊\",rtri:\"▹\",rtrie:\"⊵\",rtrif:\"▸\",rtriltri:\"⧎\",RuleDelayed:\"⧴\",ruluhar:\"⥨\",rx:\"℞\",Sacute:\"Ś\",sacute:\"ś\",sbquo:\"‚\",scap:\"⪸\",Scaron:\"Š\",scaron:\"š\",Sc:\"⪼\",sc:\"≻\",sccue:\"≽\",sce:\"⪰\",scE:\"⪴\",Scedil:\"Ş\",scedil:\"ş\",Scirc:\"Ŝ\",scirc:\"ŝ\",scnap:\"⪺\",scnE:\"⪶\",scnsim:\"⋩\",scpolint:\"⨓\",scsim:\"≿\",Scy:\"С\",scy:\"с\",sdotb:\"⊡\",sdot:\"⋅\",sdote:\"⩦\",searhk:\"⤥\",searr:\"↘\",seArr:\"⇘\",searrow:\"↘\",sect:\"§\",semi:\";\",seswar:\"⤩\",setminus:\"∖\",setmn:\"∖\",sext:\"✶\",Sfr:\"𝔖\",sfr:\"𝔰\",sfrown:\"⌢\",sharp:\"♯\",SHCHcy:\"Щ\",shchcy:\"щ\",SHcy:\"Ш\",shcy:\"ш\",ShortDownArrow:\"↓\",ShortLeftArrow:\"←\",shortmid:\"∣\",shortparallel:\"∥\",ShortRightArrow:\"→\",ShortUpArrow:\"↑\",shy:\"­\",Sigma:\"Σ\",sigma:\"σ\",sigmaf:\"ς\",sigmav:\"ς\",sim:\"∼\",simdot:\"⩪\",sime:\"≃\",simeq:\"≃\",simg:\"⪞\",simgE:\"⪠\",siml:\"⪝\",simlE:\"⪟\",simne:\"≆\",simplus:\"⨤\",simrarr:\"⥲\",slarr:\"←\",SmallCircle:\"∘\",smallsetminus:\"∖\",smashp:\"⨳\",smeparsl:\"⧤\",smid:\"∣\",smile:\"⌣\",smt:\"⪪\",smte:\"⪬\",smtes:\"⪬︀\",SOFTcy:\"Ь\",softcy:\"ь\",solbar:\"⌿\",solb:\"⧄\",sol:\"/\",Sopf:\"𝕊\",sopf:\"𝕤\",spades:\"♠\",spadesuit:\"♠\",spar:\"∥\",sqcap:\"⊓\",sqcaps:\"⊓︀\",sqcup:\"⊔\",sqcups:\"⊔︀\",Sqrt:\"√\",sqsub:\"⊏\",sqsube:\"⊑\",sqsubset:\"⊏\",sqsubseteq:\"⊑\",sqsup:\"⊐\",sqsupe:\"⊒\",sqsupset:\"⊐\",sqsupseteq:\"⊒\",square:\"□\",Square:\"□\",SquareIntersection:\"⊓\",SquareSubset:\"⊏\",SquareSubsetEqual:\"⊑\",SquareSuperset:\"⊐\",SquareSupersetEqual:\"⊒\",SquareUnion:\"⊔\",squarf:\"▪\",squ:\"□\",squf:\"▪\",srarr:\"→\",Sscr:\"𝒮\",sscr:\"𝓈\",ssetmn:\"∖\",ssmile:\"⌣\",sstarf:\"⋆\",Star:\"⋆\",star:\"☆\",starf:\"★\",straightepsilon:\"ϵ\",straightphi:\"ϕ\",strns:\"¯\",sub:\"⊂\",Sub:\"⋐\",subdot:\"⪽\",subE:\"⫅\",sube:\"⊆\",subedot:\"⫃\",submult:\"⫁\",subnE:\"⫋\",subne:\"⊊\",subplus:\"⪿\",subrarr:\"⥹\",subset:\"⊂\",Subset:\"⋐\",subseteq:\"⊆\",subseteqq:\"⫅\",SubsetEqual:\"⊆\",subsetneq:\"⊊\",subsetneqq:\"⫋\",subsim:\"⫇\",subsub:\"⫕\",subsup:\"⫓\",succapprox:\"⪸\",succ:\"≻\",succcurlyeq:\"≽\",Succeeds:\"≻\",SucceedsEqual:\"⪰\",SucceedsSlantEqual:\"≽\",SucceedsTilde:\"≿\",succeq:\"⪰\",succnapprox:\"⪺\",succneqq:\"⪶\",succnsim:\"⋩\",succsim:\"≿\",SuchThat:\"∋\",sum:\"∑\",Sum:\"∑\",sung:\"♪\",sup1:\"¹\",sup2:\"²\",sup3:\"³\",sup:\"⊃\",Sup:\"⋑\",supdot:\"⪾\",supdsub:\"⫘\",supE:\"⫆\",supe:\"⊇\",supedot:\"⫄\",Superset:\"⊃\",SupersetEqual:\"⊇\",suphsol:\"⟉\",suphsub:\"⫗\",suplarr:\"⥻\",supmult:\"⫂\",supnE:\"⫌\",supne:\"⊋\",supplus:\"⫀\",supset:\"⊃\",Supset:\"⋑\",supseteq:\"⊇\",supseteqq:\"⫆\",supsetneq:\"⊋\",supsetneqq:\"⫌\",supsim:\"⫈\",supsub:\"⫔\",supsup:\"⫖\",swarhk:\"⤦\",swarr:\"↙\",swArr:\"⇙\",swarrow:\"↙\",swnwar:\"⤪\",szlig:\"ß\",Tab:\"\\t\",target:\"⌖\",Tau:\"Τ\",tau:\"τ\",tbrk:\"⎴\",Tcaron:\"Ť\",tcaron:\"ť\",Tcedil:\"Ţ\",tcedil:\"ţ\",Tcy:\"Т\",tcy:\"т\",tdot:\"⃛\",telrec:\"⌕\",Tfr:\"𝔗\",tfr:\"𝔱\",there4:\"∴\",therefore:\"∴\",Therefore:\"∴\",Theta:\"Θ\",theta:\"θ\",thetasym:\"ϑ\",thetav:\"ϑ\",thickapprox:\"≈\",thicksim:\"∼\",ThickSpace:\"  \",ThinSpace:\" \",thinsp:\" \",thkap:\"≈\",thksim:\"∼\",THORN:\"Þ\",thorn:\"þ\",tilde:\"˜\",Tilde:\"∼\",TildeEqual:\"≃\",TildeFullEqual:\"≅\",TildeTilde:\"≈\",timesbar:\"⨱\",timesb:\"⊠\",times:\"×\",timesd:\"⨰\",tint:\"∭\",toea:\"⤨\",topbot:\"⌶\",topcir:\"⫱\",top:\"⊤\",Topf:\"𝕋\",topf:\"𝕥\",topfork:\"⫚\",tosa:\"⤩\",tprime:\"‴\",trade:\"™\",TRADE:\"™\",triangle:\"▵\",triangledown:\"▿\",triangleleft:\"◃\",trianglelefteq:\"⊴\",triangleq:\"≜\",triangleright:\"▹\",trianglerighteq:\"⊵\",tridot:\"◬\",trie:\"≜\",triminus:\"⨺\",TripleDot:\"⃛\",triplus:\"⨹\",trisb:\"⧍\",tritime:\"⨻\",trpezium:\"⏢\",Tscr:\"𝒯\",tscr:\"𝓉\",TScy:\"Ц\",tscy:\"ц\",TSHcy:\"Ћ\",tshcy:\"ћ\",Tstrok:\"Ŧ\",tstrok:\"ŧ\",twixt:\"≬\",twoheadleftarrow:\"↞\",twoheadrightarrow:\"↠\",Uacute:\"Ú\",uacute:\"ú\",uarr:\"↑\",Uarr:\"↟\",uArr:\"⇑\",Uarrocir:\"⥉\",Ubrcy:\"Ў\",ubrcy:\"ў\",Ubreve:\"Ŭ\",ubreve:\"ŭ\",Ucirc:\"Û\",ucirc:\"û\",Ucy:\"У\",ucy:\"у\",udarr:\"⇅\",Udblac:\"Ű\",udblac:\"ű\",udhar:\"⥮\",ufisht:\"⥾\",Ufr:\"𝔘\",ufr:\"𝔲\",Ugrave:\"Ù\",ugrave:\"ù\",uHar:\"⥣\",uharl:\"↿\",uharr:\"↾\",uhblk:\"▀\",ulcorn:\"⌜\",ulcorner:\"⌜\",ulcrop:\"⌏\",ultri:\"◸\",Umacr:\"Ū\",umacr:\"ū\",uml:\"¨\",UnderBar:\"_\",UnderBrace:\"⏟\",UnderBracket:\"⎵\",UnderParenthesis:\"⏝\",Union:\"⋃\",UnionPlus:\"⊎\",Uogon:\"Ų\",uogon:\"ų\",Uopf:\"𝕌\",uopf:\"𝕦\",UpArrowBar:\"⤒\",uparrow:\"↑\",UpArrow:\"↑\",Uparrow:\"⇑\",UpArrowDownArrow:\"⇅\",updownarrow:\"↕\",UpDownArrow:\"↕\",Updownarrow:\"⇕\",UpEquilibrium:\"⥮\",upharpoonleft:\"↿\",upharpoonright:\"↾\",uplus:\"⊎\",UpperLeftArrow:\"↖\",UpperRightArrow:\"↗\",upsi:\"υ\",Upsi:\"ϒ\",upsih:\"ϒ\",Upsilon:\"Υ\",upsilon:\"υ\",UpTeeArrow:\"↥\",UpTee:\"⊥\",upuparrows:\"⇈\",urcorn:\"⌝\",urcorner:\"⌝\",urcrop:\"⌎\",Uring:\"Ů\",uring:\"ů\",urtri:\"◹\",Uscr:\"𝒰\",uscr:\"𝓊\",utdot:\"⋰\",Utilde:\"Ũ\",utilde:\"ũ\",utri:\"▵\",utrif:\"▴\",uuarr:\"⇈\",Uuml:\"Ü\",uuml:\"ü\",uwangle:\"⦧\",vangrt:\"⦜\",varepsilon:\"ϵ\",varkappa:\"ϰ\",varnothing:\"∅\",varphi:\"ϕ\",varpi:\"ϖ\",varpropto:\"∝\",varr:\"↕\",vArr:\"⇕\",varrho:\"ϱ\",varsigma:\"ς\",varsubsetneq:\"⊊︀\",varsubsetneqq:\"⫋︀\",varsupsetneq:\"⊋︀\",varsupsetneqq:\"⫌︀\",vartheta:\"ϑ\",vartriangleleft:\"⊲\",vartriangleright:\"⊳\",vBar:\"⫨\",Vbar:\"⫫\",vBarv:\"⫩\",Vcy:\"В\",vcy:\"в\",vdash:\"⊢\",vDash:\"⊨\",Vdash:\"⊩\",VDash:\"⊫\",Vdashl:\"⫦\",veebar:\"⊻\",vee:\"∨\",Vee:\"⋁\",veeeq:\"≚\",vellip:\"⋮\",verbar:\"|\",Verbar:\"‖\",vert:\"|\",Vert:\"‖\",VerticalBar:\"∣\",VerticalLine:\"|\",VerticalSeparator:\"❘\",VerticalTilde:\"≀\",VeryThinSpace:\" \",Vfr:\"𝔙\",vfr:\"𝔳\",vltri:\"⊲\",vnsub:\"⊂⃒\",vnsup:\"⊃⃒\",Vopf:\"𝕍\",vopf:\"𝕧\",vprop:\"∝\",vrtri:\"⊳\",Vscr:\"𝒱\",vscr:\"𝓋\",vsubnE:\"⫋︀\",vsubne:\"⊊︀\",vsupnE:\"⫌︀\",vsupne:\"⊋︀\",Vvdash:\"⊪\",vzigzag:\"⦚\",Wcirc:\"Ŵ\",wcirc:\"ŵ\",wedbar:\"⩟\",wedge:\"∧\",Wedge:\"⋀\",wedgeq:\"≙\",weierp:\"℘\",Wfr:\"𝔚\",wfr:\"𝔴\",Wopf:\"𝕎\",wopf:\"𝕨\",wp:\"℘\",wr:\"≀\",wreath:\"≀\",Wscr:\"𝒲\",wscr:\"𝓌\",xcap:\"⋂\",xcirc:\"◯\",xcup:\"⋃\",xdtri:\"▽\",Xfr:\"𝔛\",xfr:\"𝔵\",xharr:\"⟷\",xhArr:\"⟺\",Xi:\"Ξ\",xi:\"ξ\",xlarr:\"⟵\",xlArr:\"⟸\",xmap:\"⟼\",xnis:\"⋻\",xodot:\"⨀\",Xopf:\"𝕏\",xopf:\"𝕩\",xoplus:\"⨁\",xotime:\"⨂\",xrarr:\"⟶\",xrArr:\"⟹\",Xscr:\"𝒳\",xscr:\"𝓍\",xsqcup:\"⨆\",xuplus:\"⨄\",xutri:\"△\",xvee:\"⋁\",xwedge:\"⋀\",Yacute:\"Ý\",yacute:\"ý\",YAcy:\"Я\",yacy:\"я\",Ycirc:\"Ŷ\",ycirc:\"ŷ\",Ycy:\"Ы\",ycy:\"ы\",yen:\"¥\",Yfr:\"𝔜\",yfr:\"𝔶\",YIcy:\"Ї\",yicy:\"ї\",Yopf:\"𝕐\",yopf:\"𝕪\",Yscr:\"𝒴\",yscr:\"𝓎\",YUcy:\"Ю\",yucy:\"ю\",yuml:\"ÿ\",Yuml:\"Ÿ\",Zacute:\"Ź\",zacute:\"ź\",Zcaron:\"Ž\",zcaron:\"ž\",Zcy:\"З\",zcy:\"з\",Zdot:\"Ż\",zdot:\"ż\",zeetrf:\"ℨ\",ZeroWidthSpace:\"​\",Zeta:\"Ζ\",zeta:\"ζ\",zfr:\"𝔷\",Zfr:\"ℨ\",ZHcy:\"Ж\",zhcy:\"ж\",zigrarr:\"⇝\",zopf:\"𝕫\",Zopf:\"ℤ\",Zscr:\"𝒵\",zscr:\"𝓏\",zwj:\"‍\",zwnj:\"‌\"},n=/[!-#%-\\*,-\\/:;\\?@\\[-\\]_\\{\\}\\xA1\\xA7\\xAB\\xB6\\xB7\\xBB\\xBF\\u037E\\u0387\\u055A-\\u055F\\u0589\\u058A\\u05BE\\u05C0\\u05C3\\u05C6\\u05F3\\u05F4\\u0609\\u060A\\u060C\\u060D\\u061B\\u061E\\u061F\\u066A-\\u066D\\u06D4\\u0700-\\u070D\\u07F7-\\u07F9\\u0830-\\u083E\\u085E\\u0964\\u0965\\u0970\\u09FD\\u0A76\\u0AF0\\u0C84\\u0DF4\\u0E4F\\u0E5A\\u0E5B\\u0F04-\\u0F12\\u0F14\\u0F3A-\\u0F3D\\u0F85\\u0FD0-\\u0FD4\\u0FD9\\u0FDA\\u104A-\\u104F\\u10FB\\u1360-\\u1368\\u1400\\u166D\\u166E\\u169B\\u169C\\u16EB-\\u16ED\\u1735\\u1736\\u17D4-\\u17D6\\u17D8-\\u17DA\\u1800-\\u180A\\u1944\\u1945\\u1A1E\\u1A1F\\u1AA0-\\u1AA6\\u1AA8-\\u1AAD\\u1B5A-\\u1B60\\u1BFC-\\u1BFF\\u1C3B-\\u1C3F\\u1C7E\\u1C7F\\u1CC0-\\u1CC7\\u1CD3\\u2010-\\u2027\\u2030-\\u2043\\u2045-\\u2051\\u2053-\\u205E\\u207D\\u207E\\u208D\\u208E\\u2308-\\u230B\\u2329\\u232A\\u2768-\\u2775\\u27C5\\u27C6\\u27E6-\\u27EF\\u2983-\\u2998\\u29D8-\\u29DB\\u29FC\\u29FD\\u2CF9-\\u2CFC\\u2CFE\\u2CFF\\u2D70\\u2E00-\\u2E2E\\u2E30-\\u2E4E\\u3001-\\u3003\\u3008-\\u3011\\u3014-\\u301F\\u3030\\u303D\\u30A0\\u30FB\\uA4FE\\uA4FF\\uA60D-\\uA60F\\uA673\\uA67E\\uA6F2-\\uA6F7\\uA874-\\uA877\\uA8CE\\uA8CF\\uA8F8-\\uA8FA\\uA8FC\\uA92E\\uA92F\\uA95F\\uA9C1-\\uA9CD\\uA9DE\\uA9DF\\uAA5C-\\uAA5F\\uAADE\\uAADF\\uAAF0\\uAAF1\\uABEB\\uFD3E\\uFD3F\\uFE10-\\uFE19\\uFE30-\\uFE52\\uFE54-\\uFE61\\uFE63\\uFE68\\uFE6A\\uFE6B\\uFF01-\\uFF03\\uFF05-\\uFF0A\\uFF0C-\\uFF0F\\uFF1A\\uFF1B\\uFF1F\\uFF20\\uFF3B-\\uFF3D\\uFF3F\\uFF5B\\uFF5D\\uFF5F-\\uFF65]|\\uD800[\\uDD00-\\uDD02\\uDF9F\\uDFD0]|\\uD801\\uDD6F|\\uD802[\\uDC57\\uDD1F\\uDD3F\\uDE50-\\uDE58\\uDE7F\\uDEF0-\\uDEF6\\uDF39-\\uDF3F\\uDF99-\\uDF9C]|\\uD803[\\uDF55-\\uDF59]|\\uD804[\\uDC47-\\uDC4D\\uDCBB\\uDCBC\\uDCBE-\\uDCC1\\uDD40-\\uDD43\\uDD74\\uDD75\\uDDC5-\\uDDC8\\uDDCD\\uDDDB\\uDDDD-\\uDDDF\\uDE38-\\uDE3D\\uDEA9]|\\uD805[\\uDC4B-\\uDC4F\\uDC5B\\uDC5D\\uDCC6\\uDDC1-\\uDDD7\\uDE41-\\uDE43\\uDE60-\\uDE6C\\uDF3C-\\uDF3E]|\\uD806[\\uDC3B\\uDE3F-\\uDE46\\uDE9A-\\uDE9C\\uDE9E-\\uDEA2]|\\uD807[\\uDC41-\\uDC45\\uDC70\\uDC71\\uDEF7\\uDEF8]|\\uD809[\\uDC70-\\uDC74]|\\uD81A[\\uDE6E\\uDE6F\\uDEF5\\uDF37-\\uDF3B\\uDF44]|\\uD81B[\\uDE97-\\uDE9A]|\\uD82F\\uDC9F|\\uD836[\\uDE87-\\uDE8B]|\\uD83A[\\uDD5E\\uDD5F]/,s={},o={};function i(e,r,t){var n,s,a,c,l,u=\"\";for(\"string\"!=typeof r&&(t=r,r=i.defaultChars),void 0===t&&(t=!0),l=function(e){var r,t,n=o[e];if(n)return n;for(n=o[e]=[],r=0;r<128;r++)t=String.fromCharCode(r),/^[0-9a-z]$/i.test(t)?n.push(t):n.push(\"%\"+(\"0\"+r.toString(16).toUpperCase()).slice(-2));for(r=0;r<e.length;r++)n[e.charCodeAt(r)]=e[r];return n}(r),n=0,s=e.length;n<s;n++)if(a=e.charCodeAt(n),t&&37===a&&n+2<s&&/^[0-9a-f]{2}$/i.test(e.slice(n+1,n+3)))u+=e.slice(n,n+3),n+=2;else if(a<128)u+=l[a];else if(a>=55296&&a<=57343){if(a>=55296&&a<=56319&&n+1<s&&(c=e.charCodeAt(n+1))>=56320&&c<=57343){u+=encodeURIComponent(e[n]+e[n+1]),n++;continue}u+=\"%EF%BF%BD\"}else u+=encodeURIComponent(e[n]);return u}i.defaultChars=\";/?:@&=+$,-_.!~*'()#\",i.componentChars=\"-_.!~*'()\";var a=i,c={};function l(e,r){var t;return\"string\"!=typeof r&&(r=l.defaultChars),t=function(e){var r,t,n=c[e];if(n)return n;for(n=c[e]=[],r=0;r<128;r++)t=String.fromCharCode(r),n.push(t);for(r=0;r<e.length;r++)n[t=e.charCodeAt(r)]=\"%\"+(\"0\"+t.toString(16).toUpperCase()).slice(-2);return n}(r),e.replace(/(%[a-f0-9]{2})+/gi,(function(e){var r,n,s,o,i,a,c,l=\"\";for(r=0,n=e.length;r<n;r+=3)(s=parseInt(e.slice(r+1,r+3),16))<128?l+=t[s]:192==(224&s)&&r+3<n&&128==(192&(o=parseInt(e.slice(r+4,r+6),16)))?(l+=(c=s<<6&1984|63&o)<128?\"��\":String.fromCharCode(c),r+=3):224==(240&s)&&r+6<n&&(o=parseInt(e.slice(r+4,r+6),16),i=parseInt(e.slice(r+7,r+9),16),128==(192&o)&&128==(192&i))?(l+=(c=s<<12&61440|o<<6&4032|63&i)<2048||c>=55296&&c<=57343?\"���\":String.fromCharCode(c),r+=6):240==(248&s)&&r+9<n&&(o=parseInt(e.slice(r+4,r+6),16),i=parseInt(e.slice(r+7,r+9),16),a=parseInt(e.slice(r+10,r+12),16),128==(192&o)&&128==(192&i)&&128==(192&a))?((c=s<<18&1835008|o<<12&258048|i<<6&4032|63&a)<65536||c>1114111?l+=\"����\":(c-=65536,l+=String.fromCharCode(55296+(c>>10),56320+(1023&c))),r+=9):l+=\"�\";return l}))}l.defaultChars=\";/?:@&=+$,#\",l.componentChars=\"\";var u=l;function p(){this.protocol=null,this.slashes=null,this.auth=null,this.port=null,this.hostname=null,this.hash=null,this.search=null,this.pathname=null}var h=/^([a-z0-9.+-]+:)/i,f=/:[0-9]*$/,d=/^(\\/\\/?(?!\\/)[^\\?\\s]*)(\\?[^\\s]*)?$/,m=[\"{\",\"}\",\"|\",\"\\\\\",\"^\",\"`\"].concat([\"<\",\">\",'\"',\"`\",\" \",\"\\r\",\"\\n\",\"\\t\"]),g=[\"'\"].concat(m),_=[\"%\",\"/\",\"?\",\";\",\"#\"].concat(g),k=[\"/\",\"?\",\"#\"],b=/^[+a-z0-9A-Z_-]{0,63}$/,v=/^([+a-z0-9A-Z_-]{0,63})(.*)$/,C={javascript:!0,\"javascript:\":!0},y={http:!0,https:!0,ftp:!0,gopher:!0,file:!0,\"http:\":!0,\"https:\":!0,\"ftp:\":!0,\"gopher:\":!0,\"file:\":!0};p.prototype.parse=function(e,r){var t,n,s,o,i,a=e;if(a=a.trim(),!r&&1===e.split(\"#\").length){var c=d.exec(a);if(c)return this.pathname=c[1],c[2]&&(this.search=c[2]),this}var l=h.exec(a);if(l&&(s=(l=l[0]).toLowerCase(),this.protocol=l,a=a.substr(l.length)),(r||l||a.match(/^\\/\\/[^@\\/]+@[^@\\/]+/))&&(!(i=\"//\"===a.substr(0,2))||l&&C[l]||(a=a.substr(2),this.slashes=!0)),!C[l]&&(i||l&&!y[l])){var u,p,f=-1;for(t=0;t<k.length;t++)-1!==(o=a.indexOf(k[t]))&&(-1===f||o<f)&&(f=o);for(-1!==(p=-1===f?a.lastIndexOf(\"@\"):a.lastIndexOf(\"@\",f))&&(u=a.slice(0,p),a=a.slice(p+1),this.auth=u),f=-1,t=0;t<_.length;t++)-1!==(o=a.indexOf(_[t]))&&(-1===f||o<f)&&(f=o);-1===f&&(f=a.length),\":\"===a[f-1]&&f--;var m=a.slice(0,f);a=a.slice(f),this.parseHost(m),this.hostname=this.hostname||\"\";var g=\"[\"===this.hostname[0]&&\"]\"===this.hostname[this.hostname.length-1];if(!g){var A=this.hostname.split(/\\./);for(t=0,n=A.length;t<n;t++){var x=A[t];if(x&&!x.match(b)){for(var D=\"\",w=0,E=x.length;w<E;w++)x.charCodeAt(w)>127?D+=\"x\":D+=x[w];if(!D.match(b)){var q=A.slice(0,t),S=A.slice(t+1),F=x.match(v);F&&(q.push(F[1]),S.unshift(F[2])),S.length&&(a=S.join(\".\")+a),this.hostname=q.join(\".\");break}}}}this.hostname.length>255&&(this.hostname=\"\"),g&&(this.hostname=this.hostname.substr(1,this.hostname.length-2))}var L=a.indexOf(\"#\");-1!==L&&(this.hash=a.substr(L),a=a.slice(0,L));var z=a.indexOf(\"?\");return-1!==z&&(this.search=a.substr(z),a=a.slice(0,z)),a&&(this.pathname=a),y[s]&&this.hostname&&!this.pathname&&(this.pathname=\"\"),this},p.prototype.parseHost=function(e){var r=f.exec(e);r&&(\":\"!==(r=r[0])&&(this.port=r.substr(1)),e=e.substr(0,e.length-r.length)),e&&(this.hostname=e)};var A=function(e,r){if(e&&e instanceof p)return e;var t=new p;return t.parse(e,r),t};s.encode=a,s.decode=u,s.format=function(e){var r=\"\";return r+=e.protocol||\"\",r+=e.slashes?\"//\":\"\",r+=e.auth?e.auth+\"@\":\"\",e.hostname&&-1!==e.hostname.indexOf(\":\")?r+=\"[\"+e.hostname+\"]\":r+=e.hostname||\"\",r+=e.port?\":\"+e.port:\"\",r+=e.pathname||\"\",r+=e.search||\"\",r+=e.hash||\"\"},s.parse=A;var x={},D=/[\\0-\\uD7FF\\uE000-\\uFFFF]|[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]|[\\uD800-\\uDBFF](?![\\uDC00-\\uDFFF])|(?:[^\\uD800-\\uDBFF]|^)[\\uDC00-\\uDFFF]/,w=/[\\0-\\x1F\\x7F-\\x9F]/,E=/[ \\xA0\\u1680\\u2000-\\u200A\\u2028\\u2029\\u202F\\u205F\\u3000]/;x.Any=D,x.Cc=w,x.Cf=/[\\xAD\\u0600-\\u0605\\u061C\\u06DD\\u070F\\u08E2\\u180E\\u200B-\\u200F\\u202A-\\u202E\\u2060-\\u2064\\u2066-\\u206F\\uFEFF\\uFFF9-\\uFFFB]|\\uD804[\\uDCBD\\uDCCD]|\\uD82F[\\uDCA0-\\uDCA3]|\\uD834[\\uDD73-\\uDD7A]|\\uDB40[\\uDC01\\uDC20-\\uDC7F]/,x.P=n,x.Z=E,function(e){var r=Object.prototype.hasOwnProperty;function o(e,t){return r.call(e,t)}function i(e){return!(e>=55296&&e<=57343)&&(!(e>=64976&&e<=65007)&&(65535!=(65535&e)&&65534!=(65535&e)&&(!(e>=0&&e<=8)&&(11!==e&&(!(e>=14&&e<=31)&&(!(e>=127&&e<=159)&&!(e>1114111)))))))}function a(e){if(e>65535){var r=55296+((e-=65536)>>10),t=56320+(1023&e);return String.fromCharCode(r,t)}return String.fromCharCode(e)}var c=/\\\\([!\"#$%&'()*+,\\-.\\/:;<=>?@[\\\\\\]^_`{|}~])/g,l=new RegExp(c.source+\"|\"+/&([a-z#][a-z0-9]{1,31});/gi.source,\"gi\"),u=/^#((?:x[a-f0-9]{1,8}|[0-9]{1,8}))/i,p=t;var h=/[&<>\"]/,f=/[&<>\"]/g,d={\"&\":\"&amp;\",\"<\":\"&lt;\",\">\":\"&gt;\",'\"':\"&quot;\"};function m(e){return d[e]}var g=/[.?*+^$[\\]\\\\(){}|-]/g;var _=n;e.lib={},e.lib.mdurl=s,e.lib.ucmicro=x,e.assign=function(e){var r=Array.prototype.slice.call(arguments,1);return r.forEach((function(r){if(r){if(\"object\"!=typeof r)throw new TypeError(r+\"must be object\");Object.keys(r).forEach((function(t){e[t]=r[t]}))}})),e},e.isString=function(e){return\"[object String]\"===function(e){return Object.prototype.toString.call(e)}(e)},e.has=o,e.unescapeMd=function(e){return e.indexOf(\"\\\\\")<0?e:e.replace(c,\"$1\")},e.unescapeAll=function(e){return e.indexOf(\"\\\\\")<0&&e.indexOf(\"&\")<0?e:e.replace(l,(function(e,r,t){return r||function(e,r){var t=0;return o(p,r)?p[r]:35===r.charCodeAt(0)&&u.test(r)&&i(t=\"x\"===r[1].toLowerCase()?parseInt(r.slice(2),16):parseInt(r.slice(1),10))?a(t):e}(e,t)}))},e.isValidEntityCode=i,e.fromCodePoint=a,e.escapeHtml=function(e){return h.test(e)?e.replace(f,m):e},e.arrayReplaceAt=function(e,r,t){return[].concat(e.slice(0,r),t,e.slice(r+1))},e.isSpace=function(e){switch(e){case 9:case 32:return!0}return!1},e.isWhiteSpace=function(e){if(e>=8192&&e<=8202)return!0;switch(e){case 9:case 10:case 11:case 12:case 13:case 32:case 160:case 5760:case 8239:case 8287:case 12288:return!0}return!1},e.isMdAsciiPunct=function(e){switch(e){case 33:case 34:case 35:case 36:case 37:case 38:case 39:case 40:case 41:case 42:case 43:case 44:case 45:case 46:case 47:case 58:case 59:case 60:case 61:case 62:case 63:case 64:case 91:case 92:case 93:case 94:case 95:case 96:case 123:case 124:case 125:case 126:return!0;default:return!1}},e.isPunctChar=function(e){return _.test(e)},e.escapeRE=function(e){return e.replace(g,\"\\\\$&\")},e.normalizeReference=function(e){return e=e.trim().replace(/\\s+/g,\" \"),\"Ṿ\"===\"ẞ\".toLowerCase()&&(e=e.replace(/ẞ/g,\"ß\")),e.toLowerCase().toUpperCase()}}(r);var q={},S=r.unescapeAll,F=r.unescapeAll;q.parseLinkLabel=function(e,r,t){var n,s,o,i,a=-1,c=e.posMax,l=e.pos;for(e.pos=r+1,n=1;e.pos<c;){if(93===(o=e.src.charCodeAt(e.pos))&&0===--n){s=!0;break}if(i=e.pos,e.md.inline.skipToken(e),91===o)if(i===e.pos-1)n++;else if(t)return e.pos=l,-1}return s&&(a=e.pos),e.pos=l,a},q.parseLinkDestination=function(e,r,t){var n,s,o=r,i={ok:!1,pos:0,lines:0,str:\"\"};if(60===e.charCodeAt(r)){for(r++;r<t;){if(10===(n=e.charCodeAt(r)))return i;if(60===n)return i;if(62===n)return i.pos=r+1,i.str=S(e.slice(o+1,r)),i.ok=!0,i;92===n&&r+1<t?r+=2:r++}return i}for(s=0;r<t&&32!==(n=e.charCodeAt(r))&&!(n<32||127===n);)if(92===n&&r+1<t){if(32===e.charCodeAt(r+1))break;r+=2}else{if(40===n&&++s>32)return i;if(41===n){if(0===s)break;s--}r++}return o===r||0!==s||(i.str=S(e.slice(o,r)),i.lines=0,i.pos=r,i.ok=!0),i},q.parseLinkTitle=function(e,r,t){var n,s,o=0,i=r,a={ok:!1,pos:0,lines:0,str:\"\"};if(r>=t)return a;if(34!==(s=e.charCodeAt(r))&&39!==s&&40!==s)return a;for(r++,40===s&&(s=41);r<t;){if((n=e.charCodeAt(r))===s)return a.pos=r+1,a.lines=o,a.str=F(e.slice(i+1,r)),a.ok=!0,a;if(40===n&&41===s)return a;10===n?o++:92===n&&r+1<t&&(r++,10===e.charCodeAt(r)&&o++),r++}return a};var L=r.assign,z=r.unescapeAll,T=r.escapeHtml,I={};function M(){this.rules=L({},I)}I.code_inline=function(e,r,t,n,s){var o=e[r];return\"<code\"+s.renderAttrs(o)+\">\"+T(e[r].content)+\"</code>\"},I.code_block=function(e,r,t,n,s){var o=e[r];return\"<pre\"+s.renderAttrs(o)+\"><code>\"+T(e[r].content)+\"</code></pre>\\n\"},I.fence=function(e,r,t,n,s){var o,i,a,c,l,u=e[r],p=u.info?z(u.info).trim():\"\",h=\"\",f=\"\";return p&&(h=(a=p.split(/(\\s+)/g))[0],f=a.slice(2).join(\"\")),0===(o=t.highlight&&t.highlight(u.content,h,f)||T(u.content)).indexOf(\"<pre\")?o+\"\\n\":p?(i=u.attrIndex(\"class\"),c=u.attrs?u.attrs.slice():[],i<0?c.push([\"class\",t.langPrefix+h]):(c[i]=c[i].slice(),c[i][1]+=\" \"+t.langPrefix+h),l={attrs:c},\"<pre><code\"+s.renderAttrs(l)+\">\"+o+\"</code></pre>\\n\"):\"<pre><code\"+s.renderAttrs(u)+\">\"+o+\"</code></pre>\\n\"},I.image=function(e,r,t,n,s){var o=e[r];return o.attrs[o.attrIndex(\"alt\")][1]=s.renderInlineAsText(o.children,t,n),s.renderToken(e,r,t)},I.hardbreak=function(e,r,t){return t.xhtmlOut?\"<br />\\n\":\"<br>\\n\"},I.softbreak=function(e,r,t){return t.breaks?t.xhtmlOut?\"<br />\\n\":\"<br>\\n\":\"\\n\"},I.text=function(e,r){return T(e[r].content)},I.html_block=function(e,r){return e[r].content},I.html_inline=function(e,r){return e[r].content},M.prototype.renderAttrs=function(e){var r,t,n;if(!e.attrs)return\"\";for(n=\"\",r=0,t=e.attrs.length;r<t;r++)n+=\" \"+T(e.attrs[r][0])+'=\"'+T(e.attrs[r][1])+'\"';return n},M.prototype.renderToken=function(e,r,t){var n,s=\"\",o=!1,i=e[r];return i.hidden?\"\":(i.block&&-1!==i.nesting&&r&&e[r-1].hidden&&(s+=\"\\n\"),s+=(-1===i.nesting?\"</\":\"<\")+i.tag,s+=this.renderAttrs(i),0===i.nesting&&t.xhtmlOut&&(s+=\" /\"),i.block&&(o=!0,1===i.nesting&&r+1<e.length&&(\"inline\"===(n=e[r+1]).type||n.hidden||-1===n.nesting&&n.tag===i.tag)&&(o=!1)),s+=o?\">\\n\":\">\")},M.prototype.renderInline=function(e,r,t){for(var n,s=\"\",o=this.rules,i=0,a=e.length;i<a;i++)void 0!==o[n=e[i].type]?s+=o[n](e,i,r,t,this):s+=this.renderToken(e,i,r);return s},M.prototype.renderInlineAsText=function(e,r,t){for(var n=\"\",s=0,o=e.length;s<o;s++)\"text\"===e[s].type?n+=e[s].content:\"image\"===e[s].type?n+=this.renderInlineAsText(e[s].children,r,t):\"softbreak\"===e[s].type&&(n+=\"\\n\");return n},M.prototype.render=function(e,r,t){var n,s,o,i=\"\",a=this.rules;for(n=0,s=e.length;n<s;n++)\"inline\"===(o=e[n].type)?i+=this.renderInline(e[n].children,r,t):void 0!==a[o]?i+=a[e[n].type](e,n,r,t,this):i+=this.renderToken(e,n,r,t);return i};var R=M;function B(){this.__rules__=[],this.__cache__=null}B.prototype.__find__=function(e){for(var r=0;r<this.__rules__.length;r++)if(this.__rules__[r].name===e)return r;return-1},B.prototype.__compile__=function(){var e=this,r=[\"\"];e.__rules__.forEach((function(e){e.enabled&&e.alt.forEach((function(e){r.indexOf(e)<0&&r.push(e)}))})),e.__cache__={},r.forEach((function(r){e.__cache__[r]=[],e.__rules__.forEach((function(t){t.enabled&&(r&&t.alt.indexOf(r)<0||e.__cache__[r].push(t.fn))}))}))},B.prototype.at=function(e,r,t){var n=this.__find__(e),s=t||{};if(-1===n)throw new Error(\"Parser rule not found: \"+e);this.__rules__[n].fn=r,this.__rules__[n].alt=s.alt||[],this.__cache__=null},B.prototype.before=function(e,r,t,n){var s=this.__find__(e),o=n||{};if(-1===s)throw new Error(\"Parser rule not found: \"+e);this.__rules__.splice(s,0,{name:r,enabled:!0,fn:t,alt:o.alt||[]}),this.__cache__=null},B.prototype.after=function(e,r,t,n){var s=this.__find__(e),o=n||{};if(-1===s)throw new Error(\"Parser rule not found: \"+e);this.__rules__.splice(s+1,0,{name:r,enabled:!0,fn:t,alt:o.alt||[]}),this.__cache__=null},B.prototype.push=function(e,r,t){var n=t||{};this.__rules__.push({name:e,enabled:!0,fn:r,alt:n.alt||[]}),this.__cache__=null},B.prototype.enable=function(e,r){Array.isArray(e)||(e=[e]);var t=[];return e.forEach((function(e){var n=this.__find__(e);if(n<0){if(r)return;throw new Error(\"Rules manager: invalid rule name \"+e)}this.__rules__[n].enabled=!0,t.push(e)}),this),this.__cache__=null,t},B.prototype.enableOnly=function(e,r){Array.isArray(e)||(e=[e]),this.__rules__.forEach((function(e){e.enabled=!1})),this.enable(e,r)},B.prototype.disable=function(e,r){Array.isArray(e)||(e=[e]);var t=[];return e.forEach((function(e){var n=this.__find__(e);if(n<0){if(r)return;throw new Error(\"Rules manager: invalid rule name \"+e)}this.__rules__[n].enabled=!1,t.push(e)}),this),this.__cache__=null,t},B.prototype.getRules=function(e){return null===this.__cache__&&this.__compile__(),this.__cache__[e]||[]};var N=B,O=/\\r\\n?|\\n/g,P=/\\0/g,j=r.arrayReplaceAt;function U(e){return/^<\\/a\\s*>/i.test(e)}var V=/\\+-|\\.\\.|\\?\\?\\?\\?|!!!!|,,|--/,Z=/\\((c|tm|r)\\)/i,$=/\\((c|tm|r)\\)/gi,G={c:\"©\",r:\"®\",tm:\"™\"};function H(e,r){return G[r.toLowerCase()]}function J(e){var r,t,n=0;for(r=e.length-1;r>=0;r--)\"text\"!==(t=e[r]).type||n||(t.content=t.content.replace($,H)),\"link_open\"===t.type&&\"auto\"===t.info&&n--,\"link_close\"===t.type&&\"auto\"===t.info&&n++}function W(e){var r,t,n=0;for(r=e.length-1;r>=0;r--)\"text\"!==(t=e[r]).type||n||V.test(t.content)&&(t.content=t.content.replace(/\\+-/g,\"±\").replace(/\\.{2,}/g,\"…\").replace(/([?!])…/g,\"$1..\").replace(/([?!]){4,}/g,\"$1$1$1\").replace(/,{2,}/g,\",\").replace(/(^|[^-])---(?=[^-]|$)/gm,\"$1—\").replace(/(^|\\s)--(?=\\s|$)/gm,\"$1–\").replace(/(^|[^-\\s])--(?=[^-\\s]|$)/gm,\"$1–\")),\"link_open\"===t.type&&\"auto\"===t.info&&n--,\"link_close\"===t.type&&\"auto\"===t.info&&n++}var Y=r.isWhiteSpace,K=r.isPunctChar,Q=r.isMdAsciiPunct,X=/['\"]/,ee=/['\"]/g;function re(e,r,t){return e.slice(0,r)+t+e.slice(r+1)}function te(e,r){var t,n,s,o,i,a,c,l,u,p,h,f,d,m,g,_,k,b,v,C,y;for(v=[],t=0;t<e.length;t++){for(n=e[t],c=e[t].level,k=v.length-1;k>=0&&!(v[k].level<=c);k--);if(v.length=k+1,\"text\"===n.type){i=0,a=(s=n.content).length;e:for(;i<a&&(ee.lastIndex=i,o=ee.exec(s));){if(g=_=!0,i=o.index+1,b=\"'\"===o[0],u=32,o.index-1>=0)u=s.charCodeAt(o.index-1);else for(k=t-1;k>=0&&(\"softbreak\"!==e[k].type&&\"hardbreak\"!==e[k].type);k--)if(e[k].content){u=e[k].content.charCodeAt(e[k].content.length-1);break}if(p=32,i<a)p=s.charCodeAt(i);else for(k=t+1;k<e.length&&(\"softbreak\"!==e[k].type&&\"hardbreak\"!==e[k].type);k++)if(e[k].content){p=e[k].content.charCodeAt(0);break}if(h=Q(u)||K(String.fromCharCode(u)),f=Q(p)||K(String.fromCharCode(p)),d=Y(u),(m=Y(p))?g=!1:f&&(d||h||(g=!1)),d?_=!1:h&&(m||f||(_=!1)),34===p&&'\"'===o[0]&&u>=48&&u<=57&&(_=g=!1),g&&_&&(g=h,_=f),g||_){if(_)for(k=v.length-1;k>=0&&(l=v[k],!(v[k].level<c));k--)if(l.single===b&&v[k].level===c){l=v[k],b?(C=r.md.options.quotes[2],y=r.md.options.quotes[3]):(C=r.md.options.quotes[0],y=r.md.options.quotes[1]),n.content=re(n.content,o.index,y),e[l.token].content=re(e[l.token].content,l.pos,C),i+=y.length-1,l.token===t&&(i+=C.length-1),a=(s=n.content).length,v.length=k;continue e}g?v.push({token:t,pos:o.index,single:b,level:c}):_&&b&&(n.content=re(n.content,o.index,\"’\"))}else b&&(n.content=re(n.content,o.index,\"’\"))}}}}function ne(e,r,t){this.type=e,this.tag=r,this.attrs=null,this.map=null,this.nesting=t,this.level=0,this.children=null,this.content=\"\",this.markup=\"\",this.info=\"\",this.meta=null,this.block=!1,this.hidden=!1}ne.prototype.attrIndex=function(e){var r,t,n;if(!this.attrs)return-1;for(t=0,n=(r=this.attrs).length;t<n;t++)if(r[t][0]===e)return t;return-1},ne.prototype.attrPush=function(e){this.attrs?this.attrs.push(e):this.attrs=[e]},ne.prototype.attrSet=function(e,r){var t=this.attrIndex(e),n=[e,r];t<0?this.attrPush(n):this.attrs[t]=n},ne.prototype.attrGet=function(e){var r=this.attrIndex(e),t=null;return r>=0&&(t=this.attrs[r][1]),t},ne.prototype.attrJoin=function(e,r){var t=this.attrIndex(e);t<0?this.attrPush([e,r]):this.attrs[t][1]=this.attrs[t][1]+\" \"+r};var se=ne,oe=se;function ie(e,r,t){this.src=e,this.env=t,this.tokens=[],this.inlineMode=!1,this.md=r}ie.prototype.Token=oe;var ae=ie,ce=N,le=[[\"normalize\",function(e){var r;r=(r=e.src.replace(O,\"\\n\")).replace(P,\"�\"),e.src=r}],[\"block\",function(e){var r;e.inlineMode?((r=new e.Token(\"inline\",\"\",0)).content=e.src,r.map=[0,1],r.children=[],e.tokens.push(r)):e.md.block.parse(e.src,e.md,e.env,e.tokens)}],[\"inline\",function(e){var r,t,n,s=e.tokens;for(t=0,n=s.length;t<n;t++)\"inline\"===(r=s[t]).type&&e.md.inline.parse(r.content,e.md,e.env,r.children)}],[\"linkify\",function(e){var r,t,n,s,o,i,a,c,l,u,p,h,f,d,m,g,_,k,b=e.tokens;if(e.md.options.linkify)for(t=0,n=b.length;t<n;t++)if(\"inline\"===b[t].type&&e.md.linkify.pretest(b[t].content))for(f=0,r=(s=b[t].children).length-1;r>=0;r--)if(\"link_close\"!==(i=s[r]).type){if(\"html_inline\"===i.type&&(k=i.content,/^<a[>\\s]/i.test(k)&&f>0&&f--,U(i.content)&&f++),!(f>0)&&\"text\"===i.type&&e.md.linkify.test(i.content)){for(l=i.content,_=e.md.linkify.match(l),a=[],h=i.level,p=0,_.length>0&&0===_[0].index&&r>0&&\"text_special\"===s[r-1].type&&(_=_.slice(1)),c=0;c<_.length;c++)d=_[c].url,m=e.md.normalizeLink(d),e.md.validateLink(m)&&(g=_[c].text,g=_[c].schema?\"mailto:\"!==_[c].schema||/^mailto:/i.test(g)?e.md.normalizeLinkText(g):e.md.normalizeLinkText(\"mailto:\"+g).replace(/^mailto:/,\"\"):e.md.normalizeLinkText(\"http://\"+g).replace(/^http:\\/\\//,\"\"),(u=_[c].index)>p&&((o=new e.Token(\"text\",\"\",0)).content=l.slice(p,u),o.level=h,a.push(o)),(o=new e.Token(\"link_open\",\"a\",1)).attrs=[[\"href\",m]],o.level=h++,o.markup=\"linkify\",o.info=\"auto\",a.push(o),(o=new e.Token(\"text\",\"\",0)).content=g,o.level=h,a.push(o),(o=new e.Token(\"link_close\",\"a\",-1)).level=--h,o.markup=\"linkify\",o.info=\"auto\",a.push(o),p=_[c].lastIndex);p<l.length&&((o=new e.Token(\"text\",\"\",0)).content=l.slice(p),o.level=h,a.push(o)),b[t].children=s=j(s,r,a)}}else for(r--;s[r].level!==i.level&&\"link_open\"!==s[r].type;)r--}],[\"replacements\",function(e){var r;if(e.md.options.typographer)for(r=e.tokens.length-1;r>=0;r--)\"inline\"===e.tokens[r].type&&(Z.test(e.tokens[r].content)&&J(e.tokens[r].children),V.test(e.tokens[r].content)&&W(e.tokens[r].children))}],[\"smartquotes\",function(e){var r;if(e.md.options.typographer)for(r=e.tokens.length-1;r>=0;r--)\"inline\"===e.tokens[r].type&&X.test(e.tokens[r].content)&&te(e.tokens[r].children,e)}],[\"text_join\",function(e){var r,t,n,s,o,i,a=e.tokens;for(r=0,t=a.length;r<t;r++)if(\"inline\"===a[r].type){for(o=(n=a[r].children).length,s=0;s<o;s++)\"text_special\"===n[s].type&&(n[s].type=\"text\");for(s=i=0;s<o;s++)\"text\"===n[s].type&&s+1<o&&\"text\"===n[s+1].type?n[s+1].content=n[s].content+n[s+1].content:(s!==i&&(n[i]=n[s]),i++);s!==i&&(n.length=i)}}]];function ue(){this.ruler=new ce;for(var e=0;e<le.length;e++)this.ruler.push(le[e][0],le[e][1])}ue.prototype.process=function(e){var r,t,n;for(r=0,t=(n=this.ruler.getRules(\"\")).length;r<t;r++)n[r](e)},ue.prototype.State=ae;var pe=ue,he=r.isSpace;function fe(e,r){var t=e.bMarks[r]+e.tShift[r],n=e.eMarks[r];return e.src.slice(t,n)}function de(e){var r,t=[],n=0,s=e.length,o=!1,i=0,a=\"\";for(r=e.charCodeAt(n);n<s;)124===r&&(o?(a+=e.substring(i,n-1),i=n):(t.push(a+e.substring(i,n)),a=\"\",i=n+1)),o=92===r,n++,r=e.charCodeAt(n);return t.push(a+e.substring(i)),t}var me=r.isSpace,ge=r.isSpace,_e=r.isSpace;function ke(e,r){var t,n,s,o;return n=e.bMarks[r]+e.tShift[r],s=e.eMarks[r],42!==(t=e.src.charCodeAt(n++))&&45!==t&&43!==t||n<s&&(o=e.src.charCodeAt(n),!_e(o))?-1:n}function be(e,r){var t,n=e.bMarks[r]+e.tShift[r],s=n,o=e.eMarks[r];if(s+1>=o)return-1;if((t=e.src.charCodeAt(s++))<48||t>57)return-1;for(;;){if(s>=o)return-1;if(!((t=e.src.charCodeAt(s++))>=48&&t<=57)){if(41===t||46===t)break;return-1}if(s-n>=10)return-1}return s<o&&(t=e.src.charCodeAt(s),!_e(t))?-1:s}var ve=r.normalizeReference,Ce=r.isSpace,ye={},Ae=\"<[A-Za-z][A-Za-z0-9\\\\-]*(?:\\\\s+[a-zA-Z_:][a-zA-Z0-9:._-]*(?:\\\\s*=\\\\s*(?:[^\\\"'=<>`\\\\x00-\\\\x20]+|'[^']*'|\\\"[^\\\"]*\\\"))?)*\\\\s*\\\\/?>\",xe=\"<\\\\/[A-Za-z][A-Za-z0-9\\\\-]*\\\\s*>\",De=new RegExp(\"^(?:\"+Ae+\"|\"+xe+\"|\\x3c!----\\x3e|\\x3c!--(?:-?[^>-])(?:-?[^-])*--\\x3e|<[?][\\\\s\\\\S]*?[?]>|<![A-Z]+\\\\s+[^>]*>|<!\\\\[CDATA\\\\[[\\\\s\\\\S]*?\\\\]\\\\]>)\"),we=new RegExp(\"^(?:\"+Ae+\"|\"+xe+\")\");ye.HTML_TAG_RE=De,ye.HTML_OPEN_CLOSE_TAG_RE=we;var Ee=[\"address\",\"article\",\"aside\",\"base\",\"basefont\",\"blockquote\",\"body\",\"caption\",\"center\",\"col\",\"colgroup\",\"dd\",\"details\",\"dialog\",\"dir\",\"div\",\"dl\",\"dt\",\"fieldset\",\"figcaption\",\"figure\",\"footer\",\"form\",\"frame\",\"frameset\",\"h1\",\"h2\",\"h3\",\"h4\",\"h5\",\"h6\",\"head\",\"header\",\"hr\",\"html\",\"iframe\",\"legend\",\"li\",\"link\",\"main\",\"menu\",\"menuitem\",\"nav\",\"noframes\",\"ol\",\"optgroup\",\"option\",\"p\",\"param\",\"section\",\"source\",\"summary\",\"table\",\"tbody\",\"td\",\"tfoot\",\"th\",\"thead\",\"title\",\"tr\",\"track\",\"ul\"],qe=ye.HTML_OPEN_CLOSE_TAG_RE,Se=[[/^<(script|pre|style|textarea)(?=(\\s|>|$))/i,/<\\/(script|pre|style|textarea)>/i,!0],[/^<!--/,/-->/,!0],[/^<\\?/,/\\?>/,!0],[/^<![A-Z]/,/>/,!0],[/^<!\\[CDATA\\[/,/\\]\\]>/,!0],[new RegExp(\"^</?(\"+Ee.join(\"|\")+\")(?=(\\\\s|/?>|$))\",\"i\"),/^$/,!0],[new RegExp(qe.source+\"\\\\s*$\"),/^$/,!1]],Fe=r.isSpace,Le=se,ze=r.isSpace;function Te(e,r,t,n){var s,o,i,a,c,l,u,p;for(this.src=e,this.md=r,this.env=t,this.tokens=n,this.bMarks=[],this.eMarks=[],this.tShift=[],this.sCount=[],this.bsCount=[],this.blkIndent=0,this.line=0,this.lineMax=0,this.tight=!1,this.ddIndent=-1,this.listIndent=-1,this.parentType=\"root\",this.level=0,this.result=\"\",p=!1,i=a=l=u=0,c=(o=this.src).length;a<c;a++){if(s=o.charCodeAt(a),!p){if(ze(s)){l++,9===s?u+=4-u%4:u++;continue}p=!0}10!==s&&a!==c-1||(10!==s&&a++,this.bMarks.push(i),this.eMarks.push(a),this.tShift.push(l),this.sCount.push(u),this.bsCount.push(0),p=!1,l=0,u=0,i=a+1)}this.bMarks.push(o.length),this.eMarks.push(o.length),this.tShift.push(0),this.sCount.push(0),this.bsCount.push(0),this.lineMax=this.bMarks.length-1}Te.prototype.push=function(e,r,t){var n=new Le(e,r,t);return n.block=!0,t<0&&this.level--,n.level=this.level,t>0&&this.level++,this.tokens.push(n),n},Te.prototype.isEmpty=function(e){return this.bMarks[e]+this.tShift[e]>=this.eMarks[e]},Te.prototype.skipEmptyLines=function(e){for(var r=this.lineMax;e<r&&!(this.bMarks[e]+this.tShift[e]<this.eMarks[e]);e++);return e},Te.prototype.skipSpaces=function(e){for(var r,t=this.src.length;e<t&&(r=this.src.charCodeAt(e),ze(r));e++);return e},Te.prototype.skipSpacesBack=function(e,r){if(e<=r)return e;for(;e>r;)if(!ze(this.src.charCodeAt(--e)))return e+1;return e},Te.prototype.skipChars=function(e,r){for(var t=this.src.length;e<t&&this.src.charCodeAt(e)===r;e++);return e},Te.prototype.skipCharsBack=function(e,r,t){if(e<=t)return e;for(;e>t;)if(r!==this.src.charCodeAt(--e))return e+1;return e},Te.prototype.getLines=function(e,r,t,n){var s,o,i,a,c,l,u,p=e;if(e>=r)return\"\";for(l=new Array(r-e),s=0;p<r;p++,s++){for(o=0,u=a=this.bMarks[p],c=p+1<r||n?this.eMarks[p]+1:this.eMarks[p];a<c&&o<t;){if(i=this.src.charCodeAt(a),ze(i))9===i?o+=4-(o+this.bsCount[p])%4:o++;else{if(!(a-u<this.tShift[p]))break;o++}a++}l[s]=o>t?new Array(o-t+1).join(\" \")+this.src.slice(a,c):this.src.slice(a,c)}return l.join(\"\")},Te.prototype.Token=Le;var Ie=Te,Me=N,Re=[[\"table\",function(e,r,t,n){var s,o,i,a,c,l,u,p,h,f,d,m,g,_,k,b,v,C;if(r+2>t)return!1;if(l=r+1,e.sCount[l]<e.blkIndent)return!1;if(e.sCount[l]-e.blkIndent>=4)return!1;if((i=e.bMarks[l]+e.tShift[l])>=e.eMarks[l])return!1;if(124!==(v=e.src.charCodeAt(i++))&&45!==v&&58!==v)return!1;if(i>=e.eMarks[l])return!1;if(124!==(C=e.src.charCodeAt(i++))&&45!==C&&58!==C&&!he(C))return!1;if(45===v&&he(C))return!1;for(;i<e.eMarks[l];){if(124!==(s=e.src.charCodeAt(i))&&45!==s&&58!==s&&!he(s))return!1;i++}for(u=(o=fe(e,r+1)).split(\"|\"),f=[],a=0;a<u.length;a++){if(!(d=u[a].trim())){if(0===a||a===u.length-1)continue;return!1}if(!/^:?-+:?$/.test(d))return!1;58===d.charCodeAt(d.length-1)?f.push(58===d.charCodeAt(0)?\"center\":\"right\"):58===d.charCodeAt(0)?f.push(\"left\"):f.push(\"\")}if(-1===(o=fe(e,r).trim()).indexOf(\"|\"))return!1;if(e.sCount[r]-e.blkIndent>=4)return!1;if((u=de(o)).length&&\"\"===u[0]&&u.shift(),u.length&&\"\"===u[u.length-1]&&u.pop(),0===(p=u.length)||p!==f.length)return!1;if(n)return!0;for(_=e.parentType,e.parentType=\"table\",b=e.md.block.ruler.getRules(\"blockquote\"),(h=e.push(\"table_open\",\"table\",1)).map=m=[r,0],(h=e.push(\"thead_open\",\"thead\",1)).map=[r,r+1],(h=e.push(\"tr_open\",\"tr\",1)).map=[r,r+1],a=0;a<u.length;a++)h=e.push(\"th_open\",\"th\",1),f[a]&&(h.attrs=[[\"style\",\"text-align:\"+f[a]]]),(h=e.push(\"inline\",\"\",0)).content=u[a].trim(),h.children=[],h=e.push(\"th_close\",\"th\",-1);for(h=e.push(\"tr_close\",\"tr\",-1),h=e.push(\"thead_close\",\"thead\",-1),l=r+2;l<t&&!(e.sCount[l]<e.blkIndent);l++){for(k=!1,a=0,c=b.length;a<c;a++)if(b[a](e,l,t,!0)){k=!0;break}if(k)break;if(!(o=fe(e,l).trim()))break;if(e.sCount[l]-e.blkIndent>=4)break;for((u=de(o)).length&&\"\"===u[0]&&u.shift(),u.length&&\"\"===u[u.length-1]&&u.pop(),l===r+2&&((h=e.push(\"tbody_open\",\"tbody\",1)).map=g=[r+2,0]),(h=e.push(\"tr_open\",\"tr\",1)).map=[l,l+1],a=0;a<p;a++)h=e.push(\"td_open\",\"td\",1),f[a]&&(h.attrs=[[\"style\",\"text-align:\"+f[a]]]),(h=e.push(\"inline\",\"\",0)).content=u[a]?u[a].trim():\"\",h.children=[],h=e.push(\"td_close\",\"td\",-1);h=e.push(\"tr_close\",\"tr\",-1)}return g&&(h=e.push(\"tbody_close\",\"tbody\",-1),g[1]=l),h=e.push(\"table_close\",\"table\",-1),m[1]=l,e.parentType=_,e.line=l,!0},[\"paragraph\",\"reference\"]],[\"code\",function(e,r,t){var n,s,o;if(e.sCount[r]-e.blkIndent<4)return!1;for(s=n=r+1;n<t;)if(e.isEmpty(n))n++;else{if(!(e.sCount[n]-e.blkIndent>=4))break;s=++n}return e.line=s,(o=e.push(\"code_block\",\"code\",0)).content=e.getLines(r,s,4+e.blkIndent,!1)+\"\\n\",o.map=[r,e.line],!0}],[\"fence\",function(e,r,t,n){var s,o,i,a,c,l,u,p=!1,h=e.bMarks[r]+e.tShift[r],f=e.eMarks[r];if(e.sCount[r]-e.blkIndent>=4)return!1;if(h+3>f)return!1;if(126!==(s=e.src.charCodeAt(h))&&96!==s)return!1;if(c=h,(o=(h=e.skipChars(h,s))-c)<3)return!1;if(u=e.src.slice(c,h),i=e.src.slice(h,f),96===s&&i.indexOf(String.fromCharCode(s))>=0)return!1;if(n)return!0;for(a=r;!(++a>=t)&&!((h=c=e.bMarks[a]+e.tShift[a])<(f=e.eMarks[a])&&e.sCount[a]<e.blkIndent);)if(e.src.charCodeAt(h)===s&&!(e.sCount[a]-e.blkIndent>=4||(h=e.skipChars(h,s))-c<o||(h=e.skipSpaces(h))<f)){p=!0;break}return o=e.sCount[r],e.line=a+(p?1:0),(l=e.push(\"fence\",\"code\",0)).info=i,l.content=e.getLines(r+1,a,o,!0),l.markup=u,l.map=[r,e.line],!0},[\"paragraph\",\"reference\",\"blockquote\",\"list\"]],[\"blockquote\",function(e,r,t,n){var s,o,i,a,c,l,u,p,h,f,d,m,g,_,k,b,v,C,y,A,x=e.lineMax,D=e.bMarks[r]+e.tShift[r],w=e.eMarks[r];if(e.sCount[r]-e.blkIndent>=4)return!1;if(62!==e.src.charCodeAt(D++))return!1;if(n)return!0;for(a=h=e.sCount[r]+1,32===e.src.charCodeAt(D)?(D++,a++,h++,s=!1,b=!0):9===e.src.charCodeAt(D)?(b=!0,(e.bsCount[r]+h)%4==3?(D++,a++,h++,s=!1):s=!0):b=!1,f=[e.bMarks[r]],e.bMarks[r]=D;D<w&&(o=e.src.charCodeAt(D),me(o));)9===o?h+=4-(h+e.bsCount[r]+(s?1:0))%4:h++,D++;for(d=[e.bsCount[r]],e.bsCount[r]=e.sCount[r]+1+(b?1:0),l=D>=w,_=[e.sCount[r]],e.sCount[r]=h-a,k=[e.tShift[r]],e.tShift[r]=D-e.bMarks[r],C=e.md.block.ruler.getRules(\"blockquote\"),g=e.parentType,e.parentType=\"blockquote\",p=r+1;p<t&&(A=e.sCount[p]<e.blkIndent,!((D=e.bMarks[p]+e.tShift[p])>=(w=e.eMarks[p])));p++)if(62!==e.src.charCodeAt(D++)||A){if(l)break;for(v=!1,i=0,c=C.length;i<c;i++)if(C[i](e,p,t,!0)){v=!0;break}if(v){e.lineMax=p,0!==e.blkIndent&&(f.push(e.bMarks[p]),d.push(e.bsCount[p]),k.push(e.tShift[p]),_.push(e.sCount[p]),e.sCount[p]-=e.blkIndent);break}f.push(e.bMarks[p]),d.push(e.bsCount[p]),k.push(e.tShift[p]),_.push(e.sCount[p]),e.sCount[p]=-1}else{for(a=h=e.sCount[p]+1,32===e.src.charCodeAt(D)?(D++,a++,h++,s=!1,b=!0):9===e.src.charCodeAt(D)?(b=!0,(e.bsCount[p]+h)%4==3?(D++,a++,h++,s=!1):s=!0):b=!1,f.push(e.bMarks[p]),e.bMarks[p]=D;D<w&&(o=e.src.charCodeAt(D),me(o));)9===o?h+=4-(h+e.bsCount[p]+(s?1:0))%4:h++,D++;l=D>=w,d.push(e.bsCount[p]),e.bsCount[p]=e.sCount[p]+1+(b?1:0),_.push(e.sCount[p]),e.sCount[p]=h-a,k.push(e.tShift[p]),e.tShift[p]=D-e.bMarks[p]}for(m=e.blkIndent,e.blkIndent=0,(y=e.push(\"blockquote_open\",\"blockquote\",1)).markup=\">\",y.map=u=[r,0],e.md.block.tokenize(e,r,p),(y=e.push(\"blockquote_close\",\"blockquote\",-1)).markup=\">\",e.lineMax=x,e.parentType=g,u[1]=e.line,i=0;i<k.length;i++)e.bMarks[i+r]=f[i],e.tShift[i+r]=k[i],e.sCount[i+r]=_[i],e.bsCount[i+r]=d[i];return e.blkIndent=m,!0},[\"paragraph\",\"reference\",\"blockquote\",\"list\"]],[\"hr\",function(e,r,t,n){var s,o,i,a,c=e.bMarks[r]+e.tShift[r],l=e.eMarks[r];if(e.sCount[r]-e.blkIndent>=4)return!1;if(42!==(s=e.src.charCodeAt(c++))&&45!==s&&95!==s)return!1;for(o=1;c<l;){if((i=e.src.charCodeAt(c++))!==s&&!ge(i))return!1;i===s&&o++}return!(o<3)&&(n||(e.line=r+1,(a=e.push(\"hr\",\"hr\",0)).map=[r,e.line],a.markup=Array(o+1).join(String.fromCharCode(s))),!0)},[\"paragraph\",\"reference\",\"blockquote\",\"list\"]],[\"list\",function(e,r,t,n){var s,o,i,a,c,l,u,p,h,f,d,m,g,_,k,b,v,C,y,A,x,D,w,E,q,S,F,L,z=!1,T=!0;if(e.sCount[r]-e.blkIndent>=4)return!1;if(e.listIndent>=0&&e.sCount[r]-e.listIndent>=4&&e.sCount[r]<e.blkIndent)return!1;if(n&&\"paragraph\"===e.parentType&&e.sCount[r]>=e.blkIndent&&(z=!0),(w=be(e,r))>=0){if(u=!0,q=e.bMarks[r]+e.tShift[r],g=Number(e.src.slice(q,w-1)),z&&1!==g)return!1}else{if(!((w=ke(e,r))>=0))return!1;u=!1}if(z&&e.skipSpaces(w)>=e.eMarks[r])return!1;if(m=e.src.charCodeAt(w-1),n)return!0;for(d=e.tokens.length,u?(L=e.push(\"ordered_list_open\",\"ol\",1),1!==g&&(L.attrs=[[\"start\",g]])):L=e.push(\"bullet_list_open\",\"ul\",1),L.map=f=[r,0],L.markup=String.fromCharCode(m),k=r,E=!1,F=e.md.block.ruler.getRules(\"list\"),C=e.parentType,e.parentType=\"list\";k<t;){for(D=w,_=e.eMarks[k],l=b=e.sCount[k]+w-(e.bMarks[r]+e.tShift[r]);D<_;){if(9===(s=e.src.charCodeAt(D)))b+=4-(b+e.bsCount[k])%4;else{if(32!==s)break;b++}D++}if((c=(o=D)>=_?1:b-l)>4&&(c=1),a=l+c,(L=e.push(\"list_item_open\",\"li\",1)).markup=String.fromCharCode(m),L.map=p=[r,0],u&&(L.info=e.src.slice(q,w-1)),x=e.tight,A=e.tShift[r],y=e.sCount[r],v=e.listIndent,e.listIndent=e.blkIndent,e.blkIndent=a,e.tight=!0,e.tShift[r]=o-e.bMarks[r],e.sCount[r]=b,o>=_&&e.isEmpty(r+1)?e.line=Math.min(e.line+2,t):e.md.block.tokenize(e,r,t,!0),e.tight&&!E||(T=!1),E=e.line-r>1&&e.isEmpty(e.line-1),e.blkIndent=e.listIndent,e.listIndent=v,e.tShift[r]=A,e.sCount[r]=y,e.tight=x,(L=e.push(\"list_item_close\",\"li\",-1)).markup=String.fromCharCode(m),k=r=e.line,p[1]=k,o=e.bMarks[r],k>=t)break;if(e.sCount[k]<e.blkIndent)break;if(e.sCount[r]-e.blkIndent>=4)break;for(S=!1,i=0,h=F.length;i<h;i++)if(F[i](e,k,t,!0)){S=!0;break}if(S)break;if(u){if((w=be(e,k))<0)break;q=e.bMarks[k]+e.tShift[k]}else if((w=ke(e,k))<0)break;if(m!==e.src.charCodeAt(w-1))break}return(L=u?e.push(\"ordered_list_close\",\"ol\",-1):e.push(\"bullet_list_close\",\"ul\",-1)).markup=String.fromCharCode(m),f[1]=k,e.line=k,e.parentType=C,T&&function(e,r){var t,n,s=e.level+2;for(t=r+2,n=e.tokens.length-2;t<n;t++)e.tokens[t].level===s&&\"paragraph_open\"===e.tokens[t].type&&(e.tokens[t+2].hidden=!0,e.tokens[t].hidden=!0,t+=2)}(e,d),!0},[\"paragraph\",\"reference\",\"blockquote\"]],[\"reference\",function(e,r,t,n){var s,o,i,a,c,l,u,p,h,f,d,m,g,_,k,b,v=0,C=e.bMarks[r]+e.tShift[r],y=e.eMarks[r],A=r+1;if(e.sCount[r]-e.blkIndent>=4)return!1;if(91!==e.src.charCodeAt(C))return!1;for(;++C<y;)if(93===e.src.charCodeAt(C)&&92!==e.src.charCodeAt(C-1)){if(C+1===y)return!1;if(58!==e.src.charCodeAt(C+1))return!1;break}for(a=e.lineMax,k=e.md.block.ruler.getRules(\"reference\"),f=e.parentType,e.parentType=\"reference\";A<a&&!e.isEmpty(A);A++)if(!(e.sCount[A]-e.blkIndent>3||e.sCount[A]<0)){for(_=!1,l=0,u=k.length;l<u;l++)if(k[l](e,A,a,!0)){_=!0;break}if(_)break}for(y=(g=e.getLines(r,A,e.blkIndent,!1).trim()).length,C=1;C<y;C++){if(91===(s=g.charCodeAt(C)))return!1;if(93===s){h=C;break}(10===s||92===s&&++C<y&&10===g.charCodeAt(C))&&v++}if(h<0||58!==g.charCodeAt(h+1))return!1;for(C=h+2;C<y;C++)if(10===(s=g.charCodeAt(C)))v++;else if(!Ce(s))break;if(!(d=e.md.helpers.parseLinkDestination(g,C,y)).ok)return!1;if(c=e.md.normalizeLink(d.str),!e.md.validateLink(c))return!1;for(o=C=d.pos,i=v+=d.lines,m=C;C<y;C++)if(10===(s=g.charCodeAt(C)))v++;else if(!Ce(s))break;for(d=e.md.helpers.parseLinkTitle(g,C,y),C<y&&m!==C&&d.ok?(b=d.str,C=d.pos,v+=d.lines):(b=\"\",C=o,v=i);C<y&&(s=g.charCodeAt(C),Ce(s));)C++;if(C<y&&10!==g.charCodeAt(C)&&b)for(b=\"\",C=o,v=i;C<y&&(s=g.charCodeAt(C),Ce(s));)C++;return!(C<y&&10!==g.charCodeAt(C))&&(!!(p=ve(g.slice(1,h)))&&(n||(void 0===e.env.references&&(e.env.references={}),void 0===e.env.references[p]&&(e.env.references[p]={title:b,href:c}),e.parentType=f,e.line=r+v+1),!0))}],[\"html_block\",function(e,r,t,n){var s,o,i,a,c=e.bMarks[r]+e.tShift[r],l=e.eMarks[r];if(e.sCount[r]-e.blkIndent>=4)return!1;if(!e.md.options.html)return!1;if(60!==e.src.charCodeAt(c))return!1;for(a=e.src.slice(c,l),s=0;s<Se.length&&!Se[s][0].test(a);s++);if(s===Se.length)return!1;if(n)return Se[s][2];if(o=r+1,!Se[s][1].test(a))for(;o<t&&!(e.sCount[o]<e.blkIndent);o++)if(c=e.bMarks[o]+e.tShift[o],l=e.eMarks[o],a=e.src.slice(c,l),Se[s][1].test(a)){0!==a.length&&o++;break}return e.line=o,(i=e.push(\"html_block\",\"\",0)).map=[r,o],i.content=e.getLines(r,o,e.blkIndent,!0),!0},[\"paragraph\",\"reference\",\"blockquote\"]],[\"heading\",function(e,r,t,n){var s,o,i,a,c=e.bMarks[r]+e.tShift[r],l=e.eMarks[r];if(e.sCount[r]-e.blkIndent>=4)return!1;if(35!==(s=e.src.charCodeAt(c))||c>=l)return!1;for(o=1,s=e.src.charCodeAt(++c);35===s&&c<l&&o<=6;)o++,s=e.src.charCodeAt(++c);return!(o>6||c<l&&!Fe(s))&&(n||(l=e.skipSpacesBack(l,c),(i=e.skipCharsBack(l,35,c))>c&&Fe(e.src.charCodeAt(i-1))&&(l=i),e.line=r+1,(a=e.push(\"heading_open\",\"h\"+String(o),1)).markup=\"########\".slice(0,o),a.map=[r,e.line],(a=e.push(\"inline\",\"\",0)).content=e.src.slice(c,l).trim(),a.map=[r,e.line],a.children=[],(a=e.push(\"heading_close\",\"h\"+String(o),-1)).markup=\"########\".slice(0,o)),!0)},[\"paragraph\",\"reference\",\"blockquote\"]],[\"lheading\",function(e,r,t){var n,s,o,i,a,c,l,u,p,h,f=r+1,d=e.md.block.ruler.getRules(\"paragraph\");if(e.sCount[r]-e.blkIndent>=4)return!1;for(h=e.parentType,e.parentType=\"paragraph\";f<t&&!e.isEmpty(f);f++)if(!(e.sCount[f]-e.blkIndent>3)){if(e.sCount[f]>=e.blkIndent&&(c=e.bMarks[f]+e.tShift[f])<(l=e.eMarks[f])&&(45===(p=e.src.charCodeAt(c))||61===p)&&(c=e.skipChars(c,p),(c=e.skipSpaces(c))>=l)){u=61===p?1:2;break}if(!(e.sCount[f]<0)){for(s=!1,o=0,i=d.length;o<i;o++)if(d[o](e,f,t,!0)){s=!0;break}if(s)break}}return!!u&&(n=e.getLines(r,f,e.blkIndent,!1).trim(),e.line=f+1,(a=e.push(\"heading_open\",\"h\"+String(u),1)).markup=String.fromCharCode(p),a.map=[r,e.line],(a=e.push(\"inline\",\"\",0)).content=n,a.map=[r,e.line-1],a.children=[],(a=e.push(\"heading_close\",\"h\"+String(u),-1)).markup=String.fromCharCode(p),e.parentType=h,!0)}],[\"paragraph\",function(e,r){var t,n,s,o,i,a,c=r+1,l=e.md.block.ruler.getRules(\"paragraph\"),u=e.lineMax;for(a=e.parentType,e.parentType=\"paragraph\";c<u&&!e.isEmpty(c);c++)if(!(e.sCount[c]-e.blkIndent>3||e.sCount[c]<0)){for(n=!1,s=0,o=l.length;s<o;s++)if(l[s](e,c,u,!0)){n=!0;break}if(n)break}return t=e.getLines(r,c,e.blkIndent,!1).trim(),e.line=c,(i=e.push(\"paragraph_open\",\"p\",1)).map=[r,e.line],(i=e.push(\"inline\",\"\",0)).content=t,i.map=[r,e.line],i.children=[],i=e.push(\"paragraph_close\",\"p\",-1),e.parentType=a,!0}]];function Be(){this.ruler=new Me;for(var e=0;e<Re.length;e++)this.ruler.push(Re[e][0],Re[e][1],{alt:(Re[e][2]||[]).slice()})}Be.prototype.tokenize=function(e,r,t){for(var n,s=this.ruler.getRules(\"\"),o=s.length,i=r,a=!1,c=e.md.options.maxNesting;i<t&&(e.line=i=e.skipEmptyLines(i),!(i>=t))&&!(e.sCount[i]<e.blkIndent);){if(e.level>=c){e.line=t;break}for(n=0;n<o&&!s[n](e,i,t,!1);n++);e.tight=!a,e.isEmpty(e.line-1)&&(a=!0),(i=e.line)<t&&e.isEmpty(i)&&(a=!0,i++,e.line=i)}},Be.prototype.parse=function(e,r,t,n){var s;e&&(s=new this.State(e,r,t,n),this.tokenize(s,s.line,s.lineMax))},Be.prototype.State=Ie;var Ne=Be;function Oe(e){switch(e){case 10:case 33:case 35:case 36:case 37:case 38:case 42:case 43:case 45:case 58:case 60:case 61:case 62:case 64:case 91:case 92:case 93:case 94:case 95:case 96:case 123:case 125:case 126:return!0;default:return!1}}for(var Pe=/(?:^|[^a-z0-9.+-])([a-z][a-z0-9.+-]*)$/i,je=r.isSpace,Ue=r.isSpace,Ve=[],Ze=0;Ze<256;Ze++)Ve.push(0);\"\\\\!\\\"#$%&'()*+,./:;<=>?@[]^_`{|}~-\".split(\"\").forEach((function(e){Ve[e.charCodeAt(0)]=1}));var $e={};function Ge(e,r){var t,n,s,o,i,a=[],c=r.length;for(t=0;t<c;t++)126===(s=r[t]).marker&&-1!==s.end&&(o=r[s.end],(i=e.tokens[s.token]).type=\"s_open\",i.tag=\"s\",i.nesting=1,i.markup=\"~~\",i.content=\"\",(i=e.tokens[o.token]).type=\"s_close\",i.tag=\"s\",i.nesting=-1,i.markup=\"~~\",i.content=\"\",\"text\"===e.tokens[o.token-1].type&&\"~\"===e.tokens[o.token-1].content&&a.push(o.token-1));for(;a.length;){for(n=(t=a.pop())+1;n<e.tokens.length&&\"s_close\"===e.tokens[n].type;)n++;t!==--n&&(i=e.tokens[n],e.tokens[n]=e.tokens[t],e.tokens[t]=i)}}$e.tokenize=function(e,r){var t,n,s,o,i=e.pos,a=e.src.charCodeAt(i);if(r)return!1;if(126!==a)return!1;if(s=(n=e.scanDelims(e.pos,!0)).length,o=String.fromCharCode(a),s<2)return!1;for(s%2&&(e.push(\"text\",\"\",0).content=o,s--),t=0;t<s;t+=2)e.push(\"text\",\"\",0).content=o+o,e.delimiters.push({marker:a,length:0,token:e.tokens.length-1,end:-1,open:n.can_open,close:n.can_close});return e.pos+=n.length,!0},$e.postProcess=function(e){var r,t=e.tokens_meta,n=e.tokens_meta.length;for(Ge(e,e.delimiters),r=0;r<n;r++)t[r]&&t[r].delimiters&&Ge(e,t[r].delimiters)};var He={};function Je(e,r){var t,n,s,o,i,a;for(t=r.length-1;t>=0;t--)95!==(n=r[t]).marker&&42!==n.marker||-1!==n.end&&(s=r[n.end],a=t>0&&r[t-1].end===n.end+1&&r[t-1].marker===n.marker&&r[t-1].token===n.token-1&&r[n.end+1].token===s.token+1,i=String.fromCharCode(n.marker),(o=e.tokens[n.token]).type=a?\"strong_open\":\"em_open\",o.tag=a?\"strong\":\"em\",o.nesting=1,o.markup=a?i+i:i,o.content=\"\",(o=e.tokens[s.token]).type=a?\"strong_close\":\"em_close\",o.tag=a?\"strong\":\"em\",o.nesting=-1,o.markup=a?i+i:i,o.content=\"\",a&&(e.tokens[r[t-1].token].content=\"\",e.tokens[r[n.end+1].token].content=\"\",t--))}He.tokenize=function(e,r){var t,n,s=e.pos,o=e.src.charCodeAt(s);if(r)return!1;if(95!==o&&42!==o)return!1;for(n=e.scanDelims(e.pos,42===o),t=0;t<n.length;t++)e.push(\"text\",\"\",0).content=String.fromCharCode(o),e.delimiters.push({marker:o,length:n.length,token:e.tokens.length-1,end:-1,open:n.can_open,close:n.can_close});return e.pos+=n.length,!0},He.postProcess=function(e){var r,t=e.tokens_meta,n=e.tokens_meta.length;for(Je(e,e.delimiters),r=0;r<n;r++)t[r]&&t[r].delimiters&&Je(e,t[r].delimiters)};var We=r.normalizeReference,Ye=r.isSpace,Ke=r.normalizeReference,Qe=r.isSpace,Xe=/^([a-zA-Z0-9.!#$%&'*+\\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*)$/,er=/^([a-zA-Z][a-zA-Z0-9+.\\-]{1,31}):([^<>\\x00-\\x20]*)$/,rr=ye.HTML_TAG_RE;var tr=t,nr=r.has,sr=r.isValidEntityCode,or=r.fromCodePoint,ir=/^&#((?:x[a-f0-9]{1,6}|[0-9]{1,7}));/i,ar=/^&([a-z][a-z0-9]{1,31});/i;function cr(e,r){var t,n,s,o,i,a,c,l,u={},p=r.length;if(p){var h=0,f=-2,d=[];for(t=0;t<p;t++)if(s=r[t],d.push(0),r[h].marker===s.marker&&f===s.token-1||(h=t),f=s.token,s.length=s.length||0,s.close){for(u.hasOwnProperty(s.marker)||(u[s.marker]=[-1,-1,-1,-1,-1,-1]),i=u[s.marker][(s.open?3:0)+s.length%3],a=n=h-d[h]-1;n>i;n-=d[n]+1)if((o=r[n]).marker===s.marker&&o.open&&o.end<0&&(c=!1,(o.close||s.open)&&(o.length+s.length)%3==0&&(o.length%3==0&&s.length%3==0||(c=!0)),!c)){l=n>0&&!r[n-1].open?d[n-1]+1:0,d[t]=t-n+l,d[n]=l,s.open=!1,o.end=t,o.close=!1,a=-1,f=-2;break}-1!==a&&(u[s.marker][(s.open?3:0)+(s.length||0)%3]=a)}}}var lr=se,ur=r.isWhiteSpace,pr=r.isPunctChar,hr=r.isMdAsciiPunct;function fr(e,r,t,n){this.src=e,this.env=t,this.md=r,this.tokens=n,this.tokens_meta=Array(n.length),this.pos=0,this.posMax=this.src.length,this.level=0,this.pending=\"\",this.pendingLevel=0,this.cache={},this.delimiters=[],this._prev_delimiters=[],this.backticks={},this.backticksScanned=!1,this.linkLevel=0}fr.prototype.pushPending=function(){var e=new lr(\"text\",\"\",0);return e.content=this.pending,e.level=this.pendingLevel,this.tokens.push(e),this.pending=\"\",e},fr.prototype.push=function(e,r,t){this.pending&&this.pushPending();var n=new lr(e,r,t),s=null;return t<0&&(this.level--,this.delimiters=this._prev_delimiters.pop()),n.level=this.level,t>0&&(this.level++,this._prev_delimiters.push(this.delimiters),this.delimiters=[],s={delimiters:this.delimiters}),this.pendingLevel=this.level,this.tokens.push(n),this.tokens_meta.push(s),n},fr.prototype.scanDelims=function(e,r){var t,n,s,o,i,a,c,l,u,p=e,h=!0,f=!0,d=this.posMax,m=this.src.charCodeAt(e);for(t=e>0?this.src.charCodeAt(e-1):32;p<d&&this.src.charCodeAt(p)===m;)p++;return s=p-e,n=p<d?this.src.charCodeAt(p):32,c=hr(t)||pr(String.fromCharCode(t)),u=hr(n)||pr(String.fromCharCode(n)),a=ur(t),(l=ur(n))?h=!1:u&&(a||c||(h=!1)),a?f=!1:c&&(l||u||(f=!1)),r?(o=h,i=f):(o=h&&(!f||c),i=f&&(!h||u)),{can_open:o,can_close:i,length:s}},fr.prototype.Token=lr;var dr=fr,mr=N,gr=[[\"text\",function(e,r){for(var t=e.pos;t<e.posMax&&!Oe(e.src.charCodeAt(t));)t++;return t!==e.pos&&(r||(e.pending+=e.src.slice(e.pos,t)),e.pos=t,!0)}],[\"linkify\",function(e,r){var t,n,s,o,i,a,c;return!!e.md.options.linkify&&(!(e.linkLevel>0)&&(!((t=e.pos)+3>e.posMax)&&(58===e.src.charCodeAt(t)&&(47===e.src.charCodeAt(t+1)&&(47===e.src.charCodeAt(t+2)&&(!!(n=e.pending.match(Pe))&&(s=n[1],!!(o=e.md.linkify.matchAtStart(e.src.slice(t-s.length)))&&(i=(i=o.url).replace(/\\*+$/,\"\"),a=e.md.normalizeLink(i),!!e.md.validateLink(a)&&(r||(e.pending=e.pending.slice(0,-s.length),(c=e.push(\"link_open\",\"a\",1)).attrs=[[\"href\",a]],c.markup=\"linkify\",c.info=\"auto\",(c=e.push(\"text\",\"\",0)).content=e.md.normalizeLinkText(i),(c=e.push(\"link_close\",\"a\",-1)).markup=\"linkify\",c.info=\"auto\"),e.pos+=i.length-s.length,!0)))))))))}],[\"newline\",function(e,r){var t,n,s,o=e.pos;if(10!==e.src.charCodeAt(o))return!1;if(t=e.pending.length-1,n=e.posMax,!r)if(t>=0&&32===e.pending.charCodeAt(t))if(t>=1&&32===e.pending.charCodeAt(t-1)){for(s=t-1;s>=1&&32===e.pending.charCodeAt(s-1);)s--;e.pending=e.pending.slice(0,s),e.push(\"hardbreak\",\"br\",0)}else e.pending=e.pending.slice(0,-1),e.push(\"softbreak\",\"br\",0);else e.push(\"softbreak\",\"br\",0);for(o++;o<n&&je(e.src.charCodeAt(o));)o++;return e.pos=o,!0}],[\"escape\",function(e,r){var t,n,s,o,i,a=e.pos,c=e.posMax;if(92!==e.src.charCodeAt(a))return!1;if(++a>=c)return!1;if(10===(t=e.src.charCodeAt(a))){for(r||e.push(\"hardbreak\",\"br\",0),a++;a<c&&(t=e.src.charCodeAt(a),Ue(t));)a++;return e.pos=a,!0}return o=e.src[a],t>=55296&&t<=56319&&a+1<c&&(n=e.src.charCodeAt(a+1))>=56320&&n<=57343&&(o+=e.src[a+1],a++),s=\"\\\\\"+o,r||(i=e.push(\"text_special\",\"\",0),t<256&&0!==Ve[t]?i.content=o:i.content=s,i.markup=s,i.info=\"escape\"),e.pos=a+1,!0}],[\"backticks\",function(e,r){var t,n,s,o,i,a,c,l,u=e.pos;if(96!==e.src.charCodeAt(u))return!1;for(t=u,u++,n=e.posMax;u<n&&96===e.src.charCodeAt(u);)u++;if(c=(s=e.src.slice(t,u)).length,e.backticksScanned&&(e.backticks[c]||0)<=t)return r||(e.pending+=s),e.pos+=c,!0;for(i=a=u;-1!==(i=e.src.indexOf(\"`\",a));){for(a=i+1;a<n&&96===e.src.charCodeAt(a);)a++;if((l=a-i)===c)return r||((o=e.push(\"code_inline\",\"code\",0)).markup=s,o.content=e.src.slice(u,i).replace(/\\n/g,\" \").replace(/^ (.+) $/,\"$1\")),e.pos=a,!0;e.backticks[l]=i}return e.backticksScanned=!0,r||(e.pending+=s),e.pos+=c,!0}],[\"strikethrough\",$e.tokenize],[\"emphasis\",He.tokenize],[\"link\",function(e,r){var t,n,s,o,i,a,c,l,u=\"\",p=\"\",h=e.pos,f=e.posMax,d=e.pos,m=!0;if(91!==e.src.charCodeAt(e.pos))return!1;if(i=e.pos+1,(o=e.md.helpers.parseLinkLabel(e,e.pos,!0))<0)return!1;if((a=o+1)<f&&40===e.src.charCodeAt(a)){for(m=!1,a++;a<f&&(n=e.src.charCodeAt(a),Ye(n)||10===n);a++);if(a>=f)return!1;if(d=a,(c=e.md.helpers.parseLinkDestination(e.src,a,e.posMax)).ok){for(u=e.md.normalizeLink(c.str),e.md.validateLink(u)?a=c.pos:u=\"\",d=a;a<f&&(n=e.src.charCodeAt(a),Ye(n)||10===n);a++);if(c=e.md.helpers.parseLinkTitle(e.src,a,e.posMax),a<f&&d!==a&&c.ok)for(p=c.str,a=c.pos;a<f&&(n=e.src.charCodeAt(a),Ye(n)||10===n);a++);}(a>=f||41!==e.src.charCodeAt(a))&&(m=!0),a++}if(m){if(void 0===e.env.references)return!1;if(a<f&&91===e.src.charCodeAt(a)?(d=a+1,(a=e.md.helpers.parseLinkLabel(e,a))>=0?s=e.src.slice(d,a++):a=o+1):a=o+1,s||(s=e.src.slice(i,o)),!(l=e.env.references[We(s)]))return e.pos=h,!1;u=l.href,p=l.title}return r||(e.pos=i,e.posMax=o,e.push(\"link_open\",\"a\",1).attrs=t=[[\"href\",u]],p&&t.push([\"title\",p]),e.linkLevel++,e.md.inline.tokenize(e),e.linkLevel--,e.push(\"link_close\",\"a\",-1)),e.pos=a,e.posMax=f,!0}],[\"image\",function(e,r){var t,n,s,o,i,a,c,l,u,p,h,f,d,m=\"\",g=e.pos,_=e.posMax;if(33!==e.src.charCodeAt(e.pos))return!1;if(91!==e.src.charCodeAt(e.pos+1))return!1;if(a=e.pos+2,(i=e.md.helpers.parseLinkLabel(e,e.pos+1,!1))<0)return!1;if((c=i+1)<_&&40===e.src.charCodeAt(c)){for(c++;c<_&&(n=e.src.charCodeAt(c),Qe(n)||10===n);c++);if(c>=_)return!1;for(d=c,(u=e.md.helpers.parseLinkDestination(e.src,c,e.posMax)).ok&&(m=e.md.normalizeLink(u.str),e.md.validateLink(m)?c=u.pos:m=\"\"),d=c;c<_&&(n=e.src.charCodeAt(c),Qe(n)||10===n);c++);if(u=e.md.helpers.parseLinkTitle(e.src,c,e.posMax),c<_&&d!==c&&u.ok)for(p=u.str,c=u.pos;c<_&&(n=e.src.charCodeAt(c),Qe(n)||10===n);c++);else p=\"\";if(c>=_||41!==e.src.charCodeAt(c))return e.pos=g,!1;c++}else{if(void 0===e.env.references)return!1;if(c<_&&91===e.src.charCodeAt(c)?(d=c+1,(c=e.md.helpers.parseLinkLabel(e,c))>=0?o=e.src.slice(d,c++):c=i+1):c=i+1,o||(o=e.src.slice(a,i)),!(l=e.env.references[Ke(o)]))return e.pos=g,!1;m=l.href,p=l.title}return r||(s=e.src.slice(a,i),e.md.inline.parse(s,e.md,e.env,f=[]),(h=e.push(\"image\",\"img\",0)).attrs=t=[[\"src\",m],[\"alt\",\"\"]],h.children=f,h.content=s,p&&t.push([\"title\",p])),e.pos=c,e.posMax=_,!0}],[\"autolink\",function(e,r){var t,n,s,o,i,a,c=e.pos;if(60!==e.src.charCodeAt(c))return!1;for(i=e.pos,a=e.posMax;;){if(++c>=a)return!1;if(60===(o=e.src.charCodeAt(c)))return!1;if(62===o)break}return t=e.src.slice(i+1,c),er.test(t)?(n=e.md.normalizeLink(t),!!e.md.validateLink(n)&&(r||((s=e.push(\"link_open\",\"a\",1)).attrs=[[\"href\",n]],s.markup=\"autolink\",s.info=\"auto\",(s=e.push(\"text\",\"\",0)).content=e.md.normalizeLinkText(t),(s=e.push(\"link_close\",\"a\",-1)).markup=\"autolink\",s.info=\"auto\"),e.pos+=t.length+2,!0)):!!Xe.test(t)&&(n=e.md.normalizeLink(\"mailto:\"+t),!!e.md.validateLink(n)&&(r||((s=e.push(\"link_open\",\"a\",1)).attrs=[[\"href\",n]],s.markup=\"autolink\",s.info=\"auto\",(s=e.push(\"text\",\"\",0)).content=e.md.normalizeLinkText(t),(s=e.push(\"link_close\",\"a\",-1)).markup=\"autolink\",s.info=\"auto\"),e.pos+=t.length+2,!0))}],[\"html_inline\",function(e,r){var t,n,s,o,i,a=e.pos;return!!e.md.options.html&&(s=e.posMax,!(60!==e.src.charCodeAt(a)||a+2>=s)&&(!(33!==(t=e.src.charCodeAt(a+1))&&63!==t&&47!==t&&!function(e){var r=32|e;return r>=97&&r<=122}(t))&&(!!(n=e.src.slice(a).match(rr))&&(r||((o=e.push(\"html_inline\",\"\",0)).content=e.src.slice(a,a+n[0].length),i=o.content,/^<a[>\\s]/i.test(i)&&e.linkLevel++,function(e){return/^<\\/a\\s*>/i.test(e)}(o.content)&&e.linkLevel--),e.pos+=n[0].length,!0))))}],[\"entity\",function(e,r){var t,n,s,o=e.pos,i=e.posMax;if(38!==e.src.charCodeAt(o))return!1;if(o+1>=i)return!1;if(35===e.src.charCodeAt(o+1)){if(n=e.src.slice(o).match(ir))return r||(t=\"x\"===n[1][0].toLowerCase()?parseInt(n[1].slice(1),16):parseInt(n[1],10),(s=e.push(\"text_special\",\"\",0)).content=sr(t)?or(t):or(65533),s.markup=n[0],s.info=\"entity\"),e.pos+=n[0].length,!0}else if((n=e.src.slice(o).match(ar))&&nr(tr,n[1]))return r||((s=e.push(\"text_special\",\"\",0)).content=tr[n[1]],s.markup=n[0],s.info=\"entity\"),e.pos+=n[0].length,!0;return!1}]],_r=[[\"balance_pairs\",function(e){var r,t=e.tokens_meta,n=e.tokens_meta.length;for(cr(0,e.delimiters),r=0;r<n;r++)t[r]&&t[r].delimiters&&cr(0,t[r].delimiters)}],[\"strikethrough\",$e.postProcess],[\"emphasis\",He.postProcess],[\"fragments_join\",function(e){var r,t,n=0,s=e.tokens,o=e.tokens.length;for(r=t=0;r<o;r++)s[r].nesting<0&&n--,s[r].level=n,s[r].nesting>0&&n++,\"text\"===s[r].type&&r+1<o&&\"text\"===s[r+1].type?s[r+1].content=s[r].content+s[r+1].content:(r!==t&&(s[t]=s[r]),t++);r!==t&&(s.length=t)}]];function kr(){var e;for(this.ruler=new mr,e=0;e<gr.length;e++)this.ruler.push(gr[e][0],gr[e][1]);for(this.ruler2=new mr,e=0;e<_r.length;e++)this.ruler2.push(_r[e][0],_r[e][1])}kr.prototype.skipToken=function(e){var r,t,n=e.pos,s=this.ruler.getRules(\"\"),o=s.length,i=e.md.options.maxNesting,a=e.cache;if(void 0===a[n]){if(e.level<i)for(t=0;t<o&&(e.level++,r=s[t](e,!0),e.level--,!r);t++);else e.pos=e.posMax;r||e.pos++,a[n]=e.pos}else e.pos=a[n]},kr.prototype.tokenize=function(e){for(var r,t,n=this.ruler.getRules(\"\"),s=n.length,o=e.posMax,i=e.md.options.maxNesting;e.pos<o;){if(e.level<i)for(t=0;t<s&&!(r=n[t](e,!1));t++);if(r){if(e.pos>=o)break}else e.pending+=e.src[e.pos++]}e.pending&&e.pushPending()},kr.prototype.parse=function(e,r,t,n){var s,o,i,a=new this.State(e,r,t,n);for(this.tokenize(a),i=(o=this.ruler2.getRules(\"\")).length,s=0;s<i;s++)o[s](a)},kr.prototype.State=dr;var br=kr;function vr(e){var r=Array.prototype.slice.call(arguments,1);return r.forEach((function(r){r&&Object.keys(r).forEach((function(t){e[t]=r[t]}))})),e}function Cr(e){return Object.prototype.toString.call(e)}function yr(e){return\"[object Function]\"===Cr(e)}function Ar(e){return e.replace(/[.?*+^$[\\]\\\\(){}|-]/g,\"\\\\$&\")}var xr={fuzzyLink:!0,fuzzyEmail:!0,fuzzyIP:!1};var Dr={\"http:\":{validate:function(e,r,t){var n=e.slice(r);return t.re.http||(t.re.http=new RegExp(\"^\\\\/\\\\/\"+t.re.src_auth+t.re.src_host_port_strict+t.re.src_path,\"i\")),t.re.http.test(n)?n.match(t.re.http)[0].length:0}},\"https:\":\"http:\",\"ftp:\":\"http:\",\"//\":{validate:function(e,r,t){var n=e.slice(r);return t.re.no_http||(t.re.no_http=new RegExp(\"^\"+t.re.src_auth+\"(?:localhost|(?:(?:\"+t.re.src_domain+\")\\\\.)+\"+t.re.src_domain_root+\")\"+t.re.src_port+t.re.src_host_terminator+t.re.src_path,\"i\")),t.re.no_http.test(n)?r>=3&&\":\"===e[r-3]||r>=3&&\"/\"===e[r-3]?0:n.match(t.re.no_http)[0].length:0}},\"mailto:\":{validate:function(e,r,t){var n=e.slice(r);return t.re.mailto||(t.re.mailto=new RegExp(\"^\"+t.re.src_email_name+\"@\"+t.re.src_host_strict,\"i\")),t.re.mailto.test(n)?n.match(t.re.mailto)[0].length:0}}},wr=\"biz|com|edu|gov|net|org|pro|web|xxx|aero|asia|coop|info|museum|name|shop|рф\".split(\"|\");function Er(e){var r=e.re=function(e){var r={};return e=e||{},r.src_Any=D.source,r.src_Cc=w.source,r.src_Z=E.source,r.src_P=n.source,r.src_ZPCc=[r.src_Z,r.src_P,r.src_Cc].join(\"|\"),r.src_ZCc=[r.src_Z,r.src_Cc].join(\"|\"),r.src_pseudo_letter=\"(?:(?![><｜]|\"+r.src_ZPCc+\")\"+r.src_Any+\")\",r.src_ip4=\"(?:(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\",r.src_auth=\"(?:(?:(?!\"+r.src_ZCc+\"|[@/\\\\[\\\\]()]).)+@)?\",r.src_port=\"(?::(?:6(?:[0-4]\\\\d{3}|5(?:[0-4]\\\\d{2}|5(?:[0-2]\\\\d|3[0-5])))|[1-5]?\\\\d{1,4}))?\",r.src_host_terminator=\"(?=$|[><｜]|\"+r.src_ZPCc+\")(?!\"+(e[\"---\"]?\"-(?!--)|\":\"-|\")+\"_|:\\\\d|\\\\.-|\\\\.(?!$|\"+r.src_ZPCc+\"))\",r.src_path=\"(?:[/?#](?:(?!\"+r.src_ZCc+\"|[><｜]|[()[\\\\]{}.,\\\"'?!\\\\-;]).|\\\\[(?:(?!\"+r.src_ZCc+\"|\\\\]).)*\\\\]|\\\\((?:(?!\"+r.src_ZCc+\"|[)]).)*\\\\)|\\\\{(?:(?!\"+r.src_ZCc+'|[}]).)*\\\\}|\\\\\"(?:(?!'+r.src_ZCc+'|[\"]).)+\\\\\"|\\\\\\'(?:(?!'+r.src_ZCc+\"|[']).)+\\\\'|\\\\'(?=\"+r.src_pseudo_letter+\"|[-])|\\\\.{2,}[a-zA-Z0-9%/&]|\\\\.(?!\"+r.src_ZCc+\"|[.]|$)|\"+(e[\"---\"]?\"\\\\-(?!--(?:[^-]|$))(?:-*)|\":\"\\\\-+|\")+\",(?!\"+r.src_ZCc+\"|$)|;(?!\"+r.src_ZCc+\"|$)|\\\\!+(?!\"+r.src_ZCc+\"|[!]|$)|\\\\?(?!\"+r.src_ZCc+\"|[?]|$))+|\\\\/)?\",r.src_email_name='[\\\\-;:&=\\\\+\\\\$,\\\\.a-zA-Z0-9_][\\\\-;:&=\\\\+\\\\$,\\\\\"\\\\.a-zA-Z0-9_]*',r.src_xn=\"xn--[a-z0-9\\\\-]{1,59}\",r.src_domain_root=\"(?:\"+r.src_xn+\"|\"+r.src_pseudo_letter+\"{1,63})\",r.src_domain=\"(?:\"+r.src_xn+\"|(?:\"+r.src_pseudo_letter+\")|(?:\"+r.src_pseudo_letter+\"(?:-|\"+r.src_pseudo_letter+\"){0,61}\"+r.src_pseudo_letter+\"))\",r.src_host=\"(?:(?:(?:(?:\"+r.src_domain+\")\\\\.)*\"+r.src_domain+\"))\",r.tpl_host_fuzzy=\"(?:\"+r.src_ip4+\"|(?:(?:(?:\"+r.src_domain+\")\\\\.)+(?:%TLDS%)))\",r.tpl_host_no_ip_fuzzy=\"(?:(?:(?:\"+r.src_domain+\")\\\\.)+(?:%TLDS%))\",r.src_host_strict=r.src_host+r.src_host_terminator,r.tpl_host_fuzzy_strict=r.tpl_host_fuzzy+r.src_host_terminator,r.src_host_port_strict=r.src_host+r.src_port+r.src_host_terminator,r.tpl_host_port_fuzzy_strict=r.tpl_host_fuzzy+r.src_port+r.src_host_terminator,r.tpl_host_port_no_ip_fuzzy_strict=r.tpl_host_no_ip_fuzzy+r.src_port+r.src_host_terminator,r.tpl_host_fuzzy_test=\"localhost|www\\\\.|\\\\.\\\\d{1,3}\\\\.|(?:\\\\.(?:%TLDS%)(?:\"+r.src_ZPCc+\"|>|$))\",r.tpl_email_fuzzy='(^|[><｜]|\"|\\\\(|'+r.src_ZCc+\")(\"+r.src_email_name+\"@\"+r.tpl_host_fuzzy_strict+\")\",r.tpl_link_fuzzy=\"(^|(?![.:/\\\\-_@])(?:[$+<=>^`|｜]|\"+r.src_ZPCc+\"))((?![$+<=>^`|｜])\"+r.tpl_host_port_fuzzy_strict+r.src_path+\")\",r.tpl_link_no_ip_fuzzy=\"(^|(?![.:/\\\\-_@])(?:[$+<=>^`|｜]|\"+r.src_ZPCc+\"))((?![$+<=>^`|｜])\"+r.tpl_host_port_no_ip_fuzzy_strict+r.src_path+\")\",r}(e.__opts__),t=e.__tlds__.slice();function s(e){return e.replace(\"%TLDS%\",r.src_tlds)}e.onCompile(),e.__tlds_replaced__||t.push(\"a[cdefgilmnoqrstuwxz]|b[abdefghijmnorstvwyz]|c[acdfghiklmnoruvwxyz]|d[ejkmoz]|e[cegrstu]|f[ijkmor]|g[abdefghilmnpqrstuwy]|h[kmnrtu]|i[delmnoqrst]|j[emop]|k[eghimnprwyz]|l[abcikrstuvy]|m[acdeghklmnopqrstuvwxyz]|n[acefgilopruz]|om|p[aefghklmnrstwy]|qa|r[eosuw]|s[abcdeghijklmnortuvxyz]|t[cdfghjklmnortvwz]|u[agksyz]|v[aceginu]|w[fs]|y[et]|z[amw]\"),t.push(r.src_xn),r.src_tlds=t.join(\"|\"),r.email_fuzzy=RegExp(s(r.tpl_email_fuzzy),\"i\"),r.link_fuzzy=RegExp(s(r.tpl_link_fuzzy),\"i\"),r.link_no_ip_fuzzy=RegExp(s(r.tpl_link_no_ip_fuzzy),\"i\"),r.host_fuzzy_test=RegExp(s(r.tpl_host_fuzzy_test),\"i\");var o=[];function i(e,r){throw new Error('(LinkifyIt) Invalid schema \"'+e+'\": '+r)}e.__compiled__={},Object.keys(e.__schemas__).forEach((function(r){var t=e.__schemas__[r];if(null!==t){var n={validate:null,link:null};if(e.__compiled__[r]=n,\"[object Object]\"===Cr(t))return!function(e){return\"[object RegExp]\"===Cr(e)}(t.validate)?yr(t.validate)?n.validate=t.validate:i(r,t):n.validate=function(e){return function(r,t){var n=r.slice(t);return e.test(n)?n.match(e)[0].length:0}}(t.validate),void(yr(t.normalize)?n.normalize=t.normalize:t.normalize?i(r,t):n.normalize=function(e,r){r.normalize(e)});!function(e){return\"[object String]\"===Cr(e)}(t)?i(r,t):o.push(r)}})),o.forEach((function(r){e.__compiled__[e.__schemas__[r]]&&(e.__compiled__[r].validate=e.__compiled__[e.__schemas__[r]].validate,e.__compiled__[r].normalize=e.__compiled__[e.__schemas__[r]].normalize)})),e.__compiled__[\"\"]={validate:null,normalize:function(e,r){r.normalize(e)}};var a=Object.keys(e.__compiled__).filter((function(r){return r.length>0&&e.__compiled__[r]})).map(Ar).join(\"|\");e.re.schema_test=RegExp(\"(^|(?!_)(?:[><｜]|\"+r.src_ZPCc+\"))(\"+a+\")\",\"i\"),e.re.schema_search=RegExp(\"(^|(?!_)(?:[><｜]|\"+r.src_ZPCc+\"))(\"+a+\")\",\"ig\"),e.re.schema_at_start=RegExp(\"^\"+e.re.schema_search.source,\"i\"),e.re.pretest=RegExp(\"(\"+e.re.schema_test.source+\")|(\"+e.re.host_fuzzy_test.source+\")|@\",\"i\"),function(e){e.__index__=-1,e.__text_cache__=\"\"}(e)}function qr(e,r){var t=e.__index__,n=e.__last_index__,s=e.__text_cache__.slice(t,n);this.schema=e.__schema__.toLowerCase(),this.index=t+r,this.lastIndex=n+r,this.raw=s,this.text=s,this.url=s}function Sr(e,r){var t=new qr(e,r);return e.__compiled__[t.schema].normalize(t,e),t}function Fr(e,r){if(!(this instanceof Fr))return new Fr(e,r);var t;r||(t=e,Object.keys(t||{}).reduce((function(e,r){return e||xr.hasOwnProperty(r)}),!1)&&(r=e,e={})),this.__opts__=vr({},xr,r),this.__index__=-1,this.__last_index__=-1,this.__schema__=\"\",this.__text_cache__=\"\",this.__schemas__=vr({},Dr,e),this.__compiled__={},this.__tlds__=wr,this.__tlds_replaced__=!1,this.re={},Er(this)}Fr.prototype.add=function(e,r){return this.__schemas__[e]=r,Er(this),this},Fr.prototype.set=function(e){return this.__opts__=vr(this.__opts__,e),this},Fr.prototype.test=function(e){if(this.__text_cache__=e,this.__index__=-1,!e.length)return!1;var r,t,n,s,o,i,a,c;if(this.re.schema_test.test(e))for((a=this.re.schema_search).lastIndex=0;null!==(r=a.exec(e));)if(s=this.testSchemaAt(e,r[2],a.lastIndex)){this.__schema__=r[2],this.__index__=r.index+r[1].length,this.__last_index__=r.index+r[0].length+s;break}return this.__opts__.fuzzyLink&&this.__compiled__[\"http:\"]&&(c=e.search(this.re.host_fuzzy_test))>=0&&(this.__index__<0||c<this.__index__)&&null!==(t=e.match(this.__opts__.fuzzyIP?this.re.link_fuzzy:this.re.link_no_ip_fuzzy))&&(o=t.index+t[1].length,(this.__index__<0||o<this.__index__)&&(this.__schema__=\"\",this.__index__=o,this.__last_index__=t.index+t[0].length)),this.__opts__.fuzzyEmail&&this.__compiled__[\"mailto:\"]&&e.indexOf(\"@\")>=0&&null!==(n=e.match(this.re.email_fuzzy))&&(o=n.index+n[1].length,i=n.index+n[0].length,(this.__index__<0||o<this.__index__||o===this.__index__&&i>this.__last_index__)&&(this.__schema__=\"mailto:\",this.__index__=o,this.__last_index__=i)),this.__index__>=0},Fr.prototype.pretest=function(e){return this.re.pretest.test(e)},Fr.prototype.testSchemaAt=function(e,r,t){return this.__compiled__[r.toLowerCase()]?this.__compiled__[r.toLowerCase()].validate(e,t,this):0},Fr.prototype.match=function(e){var r=0,t=[];this.__index__>=0&&this.__text_cache__===e&&(t.push(Sr(this,r)),r=this.__last_index__);for(var n=r?e.slice(r):e;this.test(n);)t.push(Sr(this,r)),n=n.slice(this.__last_index__),r+=this.__last_index__;return t.length?t:null},Fr.prototype.matchAtStart=function(e){if(this.__text_cache__=e,this.__index__=-1,!e.length)return null;var r=this.re.schema_at_start.exec(e);if(!r)return null;var t=this.testSchemaAt(e,r[2],r[0].length);return t?(this.__schema__=r[2],this.__index__=r.index+r[1].length,this.__last_index__=r.index+r[0].length+t,Sr(this,0)):null},Fr.prototype.tlds=function(e,r){return e=Array.isArray(e)?e:[e],r?(this.__tlds__=this.__tlds__.concat(e).sort().filter((function(e,r,t){return e!==t[r-1]})).reverse(),Er(this),this):(this.__tlds__=e.slice(),this.__tlds_replaced__=!0,Er(this),this)},Fr.prototype.normalize=function(e){e.schema||(e.url=\"http://\"+e.url),\"mailto:\"!==e.schema||/^mailto:/i.test(e.url)||(e.url=\"mailto:\"+e.url)},Fr.prototype.onCompile=function(){};var Lr=Fr,zr=2147483647,Tr=/^xn--/,Ir=/[^\\x20-\\x7E]/,Mr=/[\\x2E\\u3002\\uFF0E\\uFF61]/g,Rr={overflow:\"Overflow: input needs wider integers to process\",\"not-basic\":\"Illegal input >= 0x80 (not a basic code point)\",\"invalid-input\":\"Invalid input\"},Br=Math.floor,Nr=String.fromCharCode;\n/*! https://mths.be/punycode v1.4.1 by @mathias */function Or(e){throw new RangeError(Rr[e])}function Pr(e,r){for(var t=e.length,n=[];t--;)n[t]=r(e[t]);return n}function jr(e,r){var t=e.split(\"@\"),n=\"\";return t.length>1&&(n=t[0]+\"@\",e=t[1]),n+Pr((e=e.replace(Mr,\".\")).split(\".\"),r).join(\".\")}function Ur(e){for(var r,t,n=[],s=0,o=e.length;s<o;)(r=e.charCodeAt(s++))>=55296&&r<=56319&&s<o?56320==(64512&(t=e.charCodeAt(s++)))?n.push(((1023&r)<<10)+(1023&t)+65536):(n.push(r),s--):n.push(r);return n}function Vr(e){return Pr(e,(function(e){var r=\"\";return e>65535&&(r+=Nr((e-=65536)>>>10&1023|55296),e=56320|1023&e),r+=Nr(e)})).join(\"\")}function Zr(e,r){return e+22+75*(e<26)-((0!=r)<<5)}function $r(e,r,t){var n=0;for(e=t?Br(e/700):e>>1,e+=Br(e/r);e>455;n+=36)e=Br(e/35);return Br(n+36*e/(e+38))}function Gr(e){var r,t,n,s,o,i,a,c,l,u,p,h=[],f=e.length,d=0,m=128,g=72;for((t=e.lastIndexOf(\"-\"))<0&&(t=0),n=0;n<t;++n)e.charCodeAt(n)>=128&&Or(\"not-basic\"),h.push(e.charCodeAt(n));for(s=t>0?t+1:0;s<f;){for(o=d,i=1,a=36;s>=f&&Or(\"invalid-input\"),((c=(p=e.charCodeAt(s++))-48<10?p-22:p-65<26?p-65:p-97<26?p-97:36)>=36||c>Br((zr-d)/i))&&Or(\"overflow\"),d+=c*i,!(c<(l=a<=g?1:a>=g+26?26:a-g));a+=36)i>Br(zr/(u=36-l))&&Or(\"overflow\"),i*=u;g=$r(d-o,r=h.length+1,0==o),Br(d/r)>zr-m&&Or(\"overflow\"),m+=Br(d/r),d%=r,h.splice(d++,0,m)}return Vr(h)}function Hr(e){var r,t,n,s,o,i,a,c,l,u,p,h,f,d,m,g=[];for(h=(e=Ur(e)).length,r=128,t=0,o=72,i=0;i<h;++i)(p=e[i])<128&&g.push(Nr(p));for(n=s=g.length,s&&g.push(\"-\");n<h;){for(a=zr,i=0;i<h;++i)(p=e[i])>=r&&p<a&&(a=p);for(a-r>Br((zr-t)/(f=n+1))&&Or(\"overflow\"),t+=(a-r)*f,r=a,i=0;i<h;++i)if((p=e[i])<r&&++t>zr&&Or(\"overflow\"),p==r){for(c=t,l=36;!(c<(u=l<=o?1:l>=o+26?26:l-o));l+=36)m=c-u,d=36-u,g.push(Nr(Zr(u+m%d,0))),c=Br(m/d);g.push(Nr(Zr(c,0))),o=$r(t,f,n==s),t=0,++n}++t,++r}return g.join(\"\")}function Jr(e){return jr(e,(function(e){return Tr.test(e)?Gr(e.slice(4).toLowerCase()):e}))}function Wr(e){return jr(e,(function(e){return Ir.test(e)?\"xn--\"+Hr(e):e}))}var Yr={decode:Ur,encode:Vr},Kr={version:\"1.4.1\",ucs2:Yr,toASCII:Wr,toUnicode:Jr,encode:Hr,decode:Gr},Qr=r,Xr=q,et=R,rt=pe,tt=Ne,nt=br,st=Lr,ot=s,it=e(Object.freeze({__proto__:null,decode:Gr,encode:Hr,toUnicode:Jr,toASCII:Wr,version:\"1.4.1\",ucs2:Yr,default:Kr})),at={default:{options:{html:!1,xhtmlOut:!1,breaks:!1,langPrefix:\"language-\",linkify:!1,typographer:!1,quotes:\"“”‘’\",highlight:null,maxNesting:100},components:{core:{},block:{},inline:{}}},zero:{options:{html:!1,xhtmlOut:!1,breaks:!1,langPrefix:\"language-\",linkify:!1,typographer:!1,quotes:\"“”‘’\",highlight:null,maxNesting:20},components:{core:{rules:[\"normalize\",\"block\",\"inline\",\"text_join\"]},block:{rules:[\"paragraph\"]},inline:{rules:[\"text\"],rules2:[\"balance_pairs\",\"fragments_join\"]}}},commonmark:{options:{html:!0,xhtmlOut:!0,breaks:!1,langPrefix:\"language-\",linkify:!1,typographer:!1,quotes:\"“”‘’\",highlight:null,maxNesting:20},components:{core:{rules:[\"normalize\",\"block\",\"inline\",\"text_join\"]},block:{rules:[\"blockquote\",\"code\",\"fence\",\"heading\",\"hr\",\"html_block\",\"lheading\",\"list\",\"reference\",\"paragraph\"]},inline:{rules:[\"autolink\",\"backticks\",\"emphasis\",\"entity\",\"escape\",\"html_inline\",\"image\",\"link\",\"newline\",\"text\"],rules2:[\"balance_pairs\",\"emphasis\",\"fragments_join\"]}}}},ct=/^(vbscript|javascript|file|data):/,lt=/^data:image\\/(gif|png|jpeg|webp);/;function ut(e){var r=e.trim().toLowerCase();return!ct.test(r)||!!lt.test(r)}var pt=[\"http:\",\"https:\",\"mailto:\"];function ht(e){var r=ot.parse(e,!0);if(r.hostname&&(!r.protocol||pt.indexOf(r.protocol)>=0))try{r.hostname=it.toASCII(r.hostname)}catch(e){}return ot.encode(ot.format(r))}function ft(e){var r=ot.parse(e,!0);if(r.hostname&&(!r.protocol||pt.indexOf(r.protocol)>=0))try{r.hostname=it.toUnicode(r.hostname)}catch(e){}return ot.decode(ot.format(r),ot.decode.defaultChars+\"%\")}function dt(e,r){if(!(this instanceof dt))return new dt(e,r);r||Qr.isString(e)||(r=e||{},e=\"default\"),this.inline=new nt,this.block=new tt,this.core=new rt,this.renderer=new et,this.linkify=new st,this.validateLink=ut,this.normalizeLink=ht,this.normalizeLinkText=ft,this.utils=Qr,this.helpers=Qr.assign({},Xr),this.options={},this.configure(e),r&&this.set(r)}dt.prototype.set=function(e){return Qr.assign(this.options,e),this},dt.prototype.configure=function(e){var r,t=this;if(Qr.isString(e)&&!(e=at[r=e]))throw new Error('Wrong `markdown-it` preset \"'+r+'\", check name');if(!e)throw new Error(\"Wrong `markdown-it` preset, can't be empty\");return e.options&&t.set(e.options),e.components&&Object.keys(e.components).forEach((function(r){e.components[r].rules&&t[r].ruler.enableOnly(e.components[r].rules),e.components[r].rules2&&t[r].ruler2.enableOnly(e.components[r].rules2)})),this},dt.prototype.enable=function(e,r){var t=[];Array.isArray(e)||(e=[e]),[\"core\",\"block\",\"inline\"].forEach((function(r){t=t.concat(this[r].ruler.enable(e,!0))}),this),t=t.concat(this.inline.ruler2.enable(e,!0));var n=e.filter((function(e){return t.indexOf(e)<0}));if(n.length&&!r)throw new Error(\"MarkdownIt. Failed to enable unknown rule(s): \"+n);return this},dt.prototype.disable=function(e,r){var t=[];Array.isArray(e)||(e=[e]),[\"core\",\"block\",\"inline\"].forEach((function(r){t=t.concat(this[r].ruler.disable(e,!0))}),this),t=t.concat(this.inline.ruler2.disable(e,!0));var n=e.filter((function(e){return t.indexOf(e)<0}));if(n.length&&!r)throw new Error(\"MarkdownIt. Failed to disable unknown rule(s): \"+n);return this},dt.prototype.use=function(e){var r=[this].concat(Array.prototype.slice.call(arguments,1));return e.apply(e,r),this},dt.prototype.parse=function(e,r){if(\"string\"!=typeof e)throw new Error(\"Input data should be a String\");var t=new this.core.State(e,this,r);return this.core.process(t),t.tokens},dt.prototype.render=function(e,r){return r=r||{},this.renderer.render(this.parse(e,r),this.options,r)},dt.prototype.parseInline=function(e,r){var t=new this.core.State(e,this,r);return t.inlineMode=!0,this.core.process(t),t.tokens},dt.prototype.renderInline=function(e,r){return r=r||{},this.renderer.render(this.parseInline(e,r),this.options,r)};var mt=dt;export default mt;\n"], "names": ["e", "r", "t", "n", "s", "a", "c", "l", "u", "o", "i", "p", "f", "m", "g", "A", "x", "D", "w", "E", "q", "S", "F", "L", "z", "h", "d", "_", "k", "b", "v", "C", "y", "T"], "mappings": ";AAAA,SAAS,EAAEA,IAAE;AAAC,MAAGA,GAAE;AAAW,WAAOA;AAAE,MAAIC,KAAE,OAAO,eAAe,CAAA,GAAG,cAAa,EAAC,OAAM,KAAE,CAAC;AAAE,SAAO,OAAO,KAAKD,EAAC,EAAE,QAAS,SAASE,IAAE;AAAC,QAAIC,KAAE,OAAO,yBAAyBH,IAAEE,EAAC;AAAE,WAAO,eAAeD,IAAEC,IAAEC,GAAE,MAAIA,KAAE,EAAC,YAAW,MAAG,KAAI,WAAU;AAAC,aAAOH,GAAEE,EAAC;AAAA,IAAC,EAAC,CAAC;AAAA,EAAC,CAAG,GAACD;AAAC;AAAC,IAAI,IAAE,CAAE,GAAC,IAAE,EAAC,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,IAAG,KAAI,KAAI,KAAI,KAAI,MAAK,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,KAAI,KAAI,KAAI,KAAI,OAAM,KAAI,OAAM,KAAI,IAAG,KAAI,KAAI,MAAK,KAAI,MAAK,QAAO,KAAI,QAAO,KAAI,SAAQ,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,KAAI,KAAI,KAAI,KAAI,QAAO,KAAI,KAAI,KAAI,KAAI,KAAI,MAAK,KAAI,UAAS,KAAI,MAAK,KAAI,KAAI,KAAI,MAAK,KAAI,OAAM,KAAI,UAAS,KAAI,UAAS,KAAI,UAAS,KAAI,UAAS,KAAI,UAAS,KAAI,UAAS,KAAI,UAAS,KAAI,UAAS,KAAI,QAAO,KAAI,OAAM,KAAI,SAAQ,KAAI,UAAS,KAAI,QAAO,KAAI,OAAM,KAAI,SAAQ,KAAI,OAAM,KAAI,OAAM,KAAI,MAAK,MAAK,MAAK,MAAK,QAAO,KAAI,IAAG,KAAI,KAAI,KAAI,KAAI,KAAI,MAAK,KAAI,MAAK,KAAI,eAAc,KAAI,QAAO,KAAI,UAAS,KAAI,OAAM,KAAI,OAAM,KAAI,MAAK,MAAK,MAAK,MAAK,QAAO,KAAI,KAAI,KAAI,OAAM,KAAI,SAAQ,KAAI,QAAO,KAAI,QAAO,KAAI,MAAK,KAAI,MAAK,KAAI,UAAS,KAAI,OAAM,KAAI,UAAS,KAAI,aAAY,KAAI,WAAU,KAAI,SAAQ,KAAI,WAAU,KAAI,WAAU,KAAI,MAAK,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,UAAS,KAAI,MAAK,KAAI,UAAS,KAAI,OAAM,KAAI,KAAI,KAAI,KAAI,KAAI,OAAM,KAAI,QAAO,KAAI,SAAQ,KAAI,SAAQ,KAAI,SAAQ,KAAI,OAAM,KAAI,QAAO,KAAI,YAAW,KAAI,MAAK,KAAI,MAAK,KAAI,MAAK,KAAI,SAAQ,KAAI,KAAI,MAAK,KAAI,MAAK,QAAO,KAAI,SAAQ,KAAI,QAAO,KAAI,SAAQ,KAAI,UAAS,KAAI,WAAU,KAAI,UAAS,KAAI,SAAQ,KAAI,iBAAgB,KAAI,eAAc,KAAI,UAAS,KAAI,QAAO,KAAI,UAAS,KAAI,QAAO,KAAI,cAAa,KAAI,aAAY,KAAI,eAAc,KAAI,mBAAkB,KAAI,mBAAkB,KAAI,oBAAmB,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,KAAI,MAAK,SAAQ,MAAK,MAAK,KAAI,MAAK,KAAI,MAAK,MAAK,MAAK,MAAK,KAAI,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,MAAK,KAAI,MAAK,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,UAAS,KAAI,SAAQ,KAAI,UAAS,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,MAAK,KAAI,MAAK,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,QAAO,KAAI,OAAM,KAAI,OAAM,KAAI,QAAO,KAAI,MAAK,MAAK,MAAK,KAAI,OAAM,KAAI,MAAK,KAAI,OAAM,KAAI,OAAM,KAAI,MAAK,MAAK,UAAS,KAAI,MAAK,KAAI,QAAO,KAAI,MAAK,KAAI,OAAM,KAAI,OAAM,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,UAAS,KAAI,QAAO,KAAI,KAAI,KAAI,KAAI,KAAI,QAAO,KAAI,QAAO,KAAI,sBAAqB,KAAI,MAAK,MAAK,OAAM,KAAI,OAAM,KAAI,SAAQ,KAAI,OAAM,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,OAAM,KAAI,OAAM,KAAI,SAAQ,KAAI,OAAM,KAAI,SAAQ,KAAI,MAAK,KAAI,MAAK,KAAI,OAAM,KAAI,SAAQ,KAAI,SAAQ,KAAI,MAAK,KAAI,WAAU,KAAI,WAAU,KAAI,KAAI,MAAK,KAAI,KAAI,MAAK,KAAI,MAAK,KAAI,OAAM,KAAI,WAAU,KAAI,KAAI,KAAI,KAAI,KAAI,MAAK,KAAI,QAAO,KAAI,iBAAgB,KAAI,kBAAiB,KAAI,YAAW,KAAI,aAAY,KAAI,aAAY,KAAI,WAAU,KAAI,UAAS,KAAI,UAAS,KAAI,aAAY,KAAI,YAAW,KAAI,aAAY,KAAI,KAAI,KAAI,MAAK,KAAI,MAAK,KAAI,UAAS,KAAI,QAAO,KAAI,SAAQ,KAAI,0BAAyB,KAAI,uBAAsB,KAAI,iBAAgB,KAAI,OAAM,KAAI,UAAS,KAAI,OAAM,KAAI,OAAM,KAAI,QAAO,KAAI,QAAO,KAAI,SAAQ,KAAI,OAAM,KAAI,QAAO,KAAI,MAAK,KAAI,QAAO,KAAI,YAAW,KAAI,WAAU,KAAI,MAAK,KAAI,SAAQ,KAAI,WAAU,KAAI,QAAO,KAAI,QAAO,KAAI,iBAAgB,KAAI,MAAK,MAAK,MAAK,KAAI,QAAO,KAAI,WAAU,KAAI,MAAK,KAAI,MAAK,KAAI,QAAO,KAAI,iCAAgC,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,MAAK,MAAK,MAAK,MAAK,MAAK,KAAI,OAAM,KAAI,MAAK,KAAI,OAAM,KAAI,OAAM,KAAI,SAAQ,KAAI,SAAQ,KAAI,OAAM,KAAI,OAAM,KAAI,QAAO,KAAI,SAAQ,KAAI,UAAS,KAAI,QAAO,KAAI,QAAO,KAAI,KAAI,KAAI,KAAI,KAAI,QAAO,KAAI,QAAO,KAAI,OAAM,KAAI,MAAK,MAAK,QAAO,KAAI,SAAQ,KAAI,aAAY,KAAI,aAAY,KAAI,UAAS,KAAI,YAAW,KAAI,QAAO,KAAI,gBAAe,KAAI,iBAAgB,KAAI,OAAM,KAAI,OAAM,KAAI,UAAS,KAAI,OAAM,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,MAAK,KAAI,MAAK,KAAI,MAAK,KAAI,MAAK,KAAI,OAAM,KAAI,OAAM,KAAI,SAAQ,KAAI,OAAM,KAAI,QAAO,KAAI,QAAO,KAAI,KAAI,KAAI,KAAI,KAAI,SAAQ,KAAI,OAAM,KAAI,IAAG,KAAI,IAAG,KAAI,UAAS,KAAI,SAAQ,KAAI,KAAI,KAAI,KAAI,KAAI,OAAM,KAAI,OAAM,KAAI,SAAQ,KAAI,QAAO,KAAI,KAAI,MAAK,KAAI,MAAK,MAAK,KAAI,OAAM,KAAI,OAAM,KAAI,kBAAiB,KAAI,gBAAe,KAAI,wBAAuB,KAAI,kBAAiB,KAAI,kBAAiB,KAAI,MAAK,KAAI,SAAQ,KAAI,SAAQ,KAAI,aAAY,KAAI,OAAM,KAAI,KAAI,KAAI,eAAc,KAAI,SAAQ,KAAI,OAAM,KAAI,KAAI,KAAI,QAAO,KAAI,eAAc,KAAI,QAAO,KAAI,MAAK,KAAI,MAAK,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,MAAK,MAAK,MAAK,MAAK,KAAI,KAAI,KAAI,KAAI,QAAO,KAAI,OAAM,KAAI,UAAS,KAAI,UAAS,KAAI,UAAS,KAAI,SAAQ,KAAI,WAAU,KAAI,gBAAe,KAAI,uBAAsB,KAAI,WAAU,KAAI,iBAAgB,KAAI,iBAAgB,KAAI,sBAAqB,KAAI,eAAc,KAAI,qBAAoB,KAAI,0BAAyB,KAAI,sBAAqB,KAAI,kBAAiB,KAAI,gBAAe,KAAI,eAAc,KAAI,mBAAkB,KAAI,mBAAkB,KAAI,cAAa,KAAI,WAAU,KAAI,WAAU,KAAI,WAAU,KAAI,kBAAiB,KAAI,WAAU,KAAI,gBAAe,KAAI,iBAAgB,KAAI,kBAAiB,KAAI,qBAAoB,KAAI,mBAAkB,KAAI,mBAAkB,KAAI,gBAAe,KAAI,oBAAmB,KAAI,oBAAmB,KAAI,iBAAgB,KAAI,cAAa,KAAI,SAAQ,KAAI,UAAS,KAAI,QAAO,KAAI,QAAO,KAAI,MAAK,MAAK,MAAK,MAAK,MAAK,KAAI,MAAK,KAAI,MAAK,KAAI,QAAO,KAAI,QAAO,KAAI,OAAM,KAAI,MAAK,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,SAAQ,KAAI,MAAK,KAAI,MAAK,KAAI,UAAS,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,OAAM,KAAI,OAAM,KAAI,MAAK,KAAI,QAAO,KAAI,KAAI,KAAI,KAAI,KAAI,OAAM,KAAI,MAAK,KAAI,MAAK,KAAI,MAAK,KAAI,IAAG,KAAI,OAAM,KAAI,KAAI,MAAK,KAAI,MAAK,IAAG,KAAI,QAAO,KAAI,QAAO,KAAI,KAAI,KAAI,QAAO,KAAI,IAAG,KAAI,SAAQ,KAAI,UAAS,KAAI,KAAI,KAAI,KAAI,KAAI,QAAO,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,UAAS,KAAI,kBAAiB,KAAI,QAAO,KAAI,sBAAqB,KAAI,QAAO,KAAI,QAAO,KAAI,MAAK,KAAI,KAAI,KAAI,KAAI,KAAI,MAAK,KAAI,OAAM,KAAI,OAAM,KAAI,MAAK,MAAK,MAAK,MAAK,MAAK,KAAI,QAAO,KAAI,OAAM,KAAI,MAAK,KAAI,SAAQ,KAAI,SAAQ,KAAI,OAAM,KAAI,QAAO,KAAI,SAAQ,KAAI,OAAM,KAAI,YAAW,KAAI,aAAY,KAAI,OAAM,KAAI,QAAO,KAAI,YAAW,KAAI,QAAO,KAAI,aAAY,KAAI,OAAM,KAAI,SAAQ,KAAI,UAAS,KAAI,OAAM,KAAI,OAAM,KAAI,MAAK,KAAI,MAAK,KAAI,OAAM,KAAI,MAAK,KAAI,MAAK,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,MAAK,KAAI,MAAK,KAAI,MAAK,KAAI,MAAK,KAAI,OAAM,KAAI,QAAO,KAAI,aAAY,KAAI,cAAa,KAAI,cAAa,KAAI,eAAc,KAAI,KAAI,KAAI,KAAI,KAAI,QAAO,KAAI,QAAO,KAAI,OAAM,KAAI,QAAO,KAAI,KAAI,MAAK,KAAI,MAAK,OAAM,KAAI,mBAAkB,KAAI,uBAAsB,KAAI,OAAM,MAAK,MAAK,KAAI,OAAM,KAAI,OAAM,KAAI,MAAK,KAAI,MAAK,MAAK,MAAK,MAAK,QAAO,KAAI,QAAO,KAAI,MAAK,KAAI,OAAM,KAAI,YAAW,KAAI,UAAS,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,OAAM,KAAI,OAAM,KAAI,MAAK,MAAK,MAAK,KAAI,QAAO,KAAI,OAAM,KAAI,OAAM,KAAI,QAAO,KAAI,QAAO,KAAI,KAAI,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,OAAM,KAAI,OAAM,KAAI,KAAI,KAAI,KAAI,KAAI,MAAK,KAAI,MAAK,KAAI,IAAG,KAAI,IAAG,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,MAAK,KAAI,UAAS,KAAI,OAAM,KAAI,KAAI,KAAI,QAAO,KAAI,SAAQ,KAAI,UAAS,KAAI,MAAK,MAAK,QAAO,KAAI,KAAI,MAAK,KAAI,MAAK,IAAG,KAAI,IAAG,KAAI,KAAI,KAAI,OAAM,KAAI,MAAK,KAAI,MAAK,KAAI,KAAI,KAAI,IAAG,KAAI,KAAI,KAAI,KAAI,KAAI,MAAK,KAAI,UAAS,KAAI,KAAI,KAAI,KAAI,KAAI,MAAK,KAAI,OAAM,KAAI,OAAM,KAAI,MAAK,MAAK,MAAK,MAAK,OAAM,KAAI,cAAa,KAAI,kBAAiB,KAAI,kBAAiB,KAAI,gBAAe,KAAI,aAAY,KAAI,mBAAkB,KAAI,cAAa,KAAI,MAAK,MAAK,MAAK,KAAI,MAAK,KAAI,OAAM,KAAI,OAAM,KAAI,MAAK,KAAI,OAAM,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,OAAM,KAAI,QAAO,KAAI,SAAQ,KAAI,WAAU,KAAI,QAAO,KAAI,QAAO,KAAI,WAAU,KAAI,YAAW,KAAI,SAAQ,KAAI,QAAO,KAAI,WAAU,MAAK,MAAK,MAAK,OAAM,KAAI,QAAO,KAAI,MAAK,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,SAAQ,KAAI,MAAK,KAAI,MAAK,KAAI,OAAM,KAAI,KAAI,KAAI,MAAK,KAAI,OAAM,KAAI,OAAM,KAAI,QAAO,KAAI,WAAU,KAAI,QAAO,KAAI,QAAO,KAAI,KAAI,MAAK,KAAI,KAAI,cAAa,KAAI,UAAS,KAAI,UAAS,KAAI,OAAM,KAAI,QAAO,KAAI,eAAc,KAAI,gBAAe,KAAI,MAAK,MAAK,MAAK,KAAI,QAAO,KAAI,gBAAe,KAAI,MAAK,MAAK,MAAK,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,cAAa,KAAI,WAAU,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,IAAG,KAAI,OAAM,KAAI,OAAM,KAAI,KAAI,KAAI,KAAI,KAAI,MAAK,KAAI,MAAK,KAAI,MAAK,KAAI,OAAM,KAAI,KAAI,KAAI,KAAI,MAAK,KAAI,KAAI,QAAO,KAAI,QAAO,KAAI,IAAG,KAAI,QAAO,KAAI,OAAM,KAAI,QAAO,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,YAAW,KAAI,UAAS,KAAI,UAAS,KAAI,OAAM,KAAI,IAAG,KAAI,MAAK,KAAI,OAAM,KAAI,SAAQ,KAAI,QAAO,KAAI,IAAG,KAAI,OAAM,KAAI,UAAS,KAAI,QAAO,KAAI,QAAO,KAAI,KAAI,KAAI,KAAI,KAAI,UAAS,KAAI,UAAS,KAAI,UAAS,KAAI,cAAa,KAAI,UAAS,KAAI,SAAQ,KAAI,gBAAe,KAAI,gBAAe,KAAI,MAAK,KAAI,MAAK,KAAI,OAAM,KAAI,OAAM,KAAI,MAAK,MAAK,MAAK,MAAK,MAAK,KAAI,MAAK,KAAI,OAAM,KAAI,QAAO,KAAI,MAAK,MAAK,MAAK,KAAI,MAAK,KAAI,SAAQ,KAAI,OAAM,KAAI,OAAM,KAAI,QAAO,KAAI,OAAM,KAAI,IAAG,KAAI,QAAO,KAAI,QAAO,KAAI,OAAM,KAAI,OAAM,KAAI,MAAK,KAAI,MAAK,KAAI,OAAM,KAAI,OAAM,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,MAAK,KAAI,MAAK,OAAM,KAAI,MAAK,MAAK,MAAK,MAAK,MAAK,MAAK,MAAK,MAAK,QAAO,KAAI,QAAO,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,MAAK,KAAI,MAAK,QAAO,KAAI,MAAK,KAAI,MAAK,KAAI,MAAK,KAAI,MAAK,KAAI,MAAK,MAAK,MAAK,MAAK,MAAK,MAAK,MAAK,MAAK,OAAM,KAAI,QAAO,KAAI,QAAO,KAAI,UAAS,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,MAAK,KAAI,MAAK,KAAI,OAAM,KAAI,QAAO,KAAI,KAAI,KAAI,YAAW,KAAI,OAAM,KAAI,OAAM,KAAI,SAAQ,KAAI,MAAK,KAAI,MAAK,KAAI,MAAK,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,SAAQ,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,KAAI,KAAI,MAAK,KAAI,OAAM,MAAK,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,QAAO,KAAI,QAAO,KAAI,OAAM,KAAI,SAAQ,KAAI,SAAQ,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,OAAM,KAAI,MAAK,KAAI,KAAI,KAAI,KAAI,KAAI,MAAK,KAAI,OAAM,KAAI,QAAO,KAAI,SAAQ,KAAI,UAAS,KAAI,MAAK,KAAI,IAAG,KAAI,IAAG,KAAI,kBAAiB,KAAI,cAAa,KAAI,WAAU,KAAI,WAAU,KAAI,WAAU,KAAI,qBAAoB,KAAI,eAAc,KAAI,aAAY,KAAI,mBAAkB,KAAI,mBAAkB,KAAI,mBAAkB,KAAI,gBAAe,KAAI,WAAU,KAAI,iBAAgB,KAAI,eAAc,KAAI,gBAAe,KAAI,gBAAe,KAAI,gBAAe,KAAI,gBAAe,KAAI,iBAAgB,KAAI,mBAAkB,KAAI,qBAAoB,KAAI,iBAAgB,KAAI,cAAa,KAAI,SAAQ,KAAI,eAAc,KAAI,gBAAe,KAAI,iBAAgB,KAAI,cAAa,KAAI,mBAAkB,KAAI,kBAAiB,KAAI,iBAAgB,KAAI,iBAAgB,KAAI,cAAa,KAAI,eAAc,KAAI,YAAW,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,MAAK,KAAI,UAAS,KAAI,OAAM,KAAI,KAAI,KAAI,QAAO,KAAI,SAAQ,KAAI,UAAS,KAAI,MAAK,MAAK,QAAO,KAAI,YAAW,KAAI,SAAQ,KAAI,WAAU,KAAI,YAAW,KAAI,kBAAiB,KAAI,eAAc,KAAI,aAAY,KAAI,SAAQ,KAAI,UAAS,KAAI,SAAQ,KAAI,gBAAe,KAAI,WAAU,KAAI,QAAO,KAAI,QAAO,KAAI,KAAI,MAAK,KAAI,MAAK,IAAG,KAAI,KAAI,KAAI,MAAK,KAAI,OAAM,KAAI,OAAM,KAAI,QAAO,KAAI,OAAM,KAAI,MAAK,KAAI,MAAK,KAAI,OAAM,KAAI,IAAG,KAAI,IAAG,KAAI,UAAS,KAAI,YAAW,KAAI,QAAO,KAAI,OAAM,KAAI,QAAO,KAAI,QAAO,KAAI,YAAW,KAAI,QAAO,KAAI,MAAK,KAAI,UAAS,KAAI,KAAI,KAAI,KAAI,KAAI,MAAK,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,eAAc,KAAI,eAAc,KAAI,eAAc,KAAI,oBAAmB,KAAI,oBAAmB,KAAI,oBAAmB,KAAI,YAAW,KAAI,gBAAe,KAAI,gBAAe,KAAI,gBAAe,KAAI,eAAc,KAAI,gBAAe,KAAI,OAAM,KAAI,MAAK,MAAK,MAAK,MAAK,QAAO,KAAI,SAAQ,KAAI,QAAO,KAAI,QAAO,KAAI,gBAAe,KAAI,iBAAgB,KAAI,KAAI,KAAI,SAAQ,KAAI,MAAK,KAAI,MAAK,KAAI,QAAO,KAAI,OAAM,KAAI,UAAS,KAAI,OAAM,KAAI,QAAO,KAAI,KAAI,KAAI,OAAM,KAAI,QAAO,KAAI,MAAK,MAAK,MAAK,KAAI,KAAI,KAAI,KAAI,KAAI,MAAK,KAAI,OAAM,KAAI,OAAM,KAAI,MAAK,KAAI,OAAM,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,MAAK,KAAI,OAAM,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,OAAM,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,SAAQ,KAAI,MAAK,KAAI,OAAM,KAAI,OAAM,KAAI,QAAO,KAAI,UAAS,KAAI,SAAQ,KAAI,WAAU,MAAK,MAAK,MAAK,MAAK,KAAI,MAAK,KAAI,MAAK,KAAI,SAAQ,KAAI,KAAI,KAAI,KAAI,KAAI,QAAO,KAAI,YAAW,KAAI,YAAW,KAAI,UAAS,KAAI,QAAO,KAAI,QAAO,KAAI,KAAI,KAAI,KAAI,KAAI,OAAM,KAAI,OAAM,KAAI,eAAc,KAAI,aAAY,KAAI,WAAU,KAAI,KAAI,MAAK,KAAI,MAAK,KAAI,KAAI,OAAM,KAAI,QAAO,KAAI,QAAO,KAAI,KAAI,KAAI,QAAO,KAAI,QAAO,KAAI,OAAM,KAAI,QAAO,KAAI,SAAQ,KAAI,WAAU,KAAI,MAAK,KAAI,MAAK,KAAI,QAAO,KAAI,QAAO,KAAI,MAAK,MAAK,MAAK,MAAK,IAAG,KAAI,MAAK,MAAK,MAAK,KAAI,QAAO,KAAI,IAAG,KAAI,IAAG,KAAI,UAAS,KAAI,OAAM,KAAI,OAAM,KAAI,QAAO,KAAI,QAAO,KAAI,MAAK,MAAK,KAAI,KAAI,MAAK,MAAK,OAAM,MAAK,OAAM,KAAI,SAAQ,KAAI,SAAQ,KAAI,UAAS,KAAI,OAAM,KAAI,MAAK,KAAI,OAAM,MAAK,QAAO,MAAK,MAAK,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,OAAM,KAAI,UAAS,MAAK,MAAK,KAAI,KAAI,KAAI,KAAI,KAAI,OAAM,KAAI,QAAO,KAAI,OAAM,KAAI,OAAM,KAAI,SAAQ,KAAI,IAAG,KAAI,OAAM,MAAK,qBAAoB,KAAI,oBAAmB,KAAI,mBAAkB,KAAI,uBAAsB,KAAI,QAAO,KAAI,QAAO,KAAI,OAAM,MAAK,sBAAqB,KAAI,gBAAe,KAAI,SAAQ,MAAK,QAAO,KAAI,SAAQ,KAAI,KAAI,MAAK,KAAI,MAAK,KAAI,MAAK,KAAI,KAAI,MAAK,KAAI,OAAM,MAAK,WAAU,MAAK,MAAK,MAAK,KAAI,MAAK,OAAM,KAAI,KAAI,MAAK,KAAI,KAAI,MAAK,KAAI,MAAK,MAAK,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,IAAG,KAAI,KAAI,KAAI,MAAK,KAAI,KAAI,KAAI,MAAK,KAAI,MAAK,KAAI,OAAM,KAAI,OAAM,KAAI,MAAK,KAAI,KAAI,MAAK,KAAI,KAAI,YAAW,KAAI,YAAW,KAAI,iBAAgB,KAAI,iBAAgB,KAAI,MAAK,KAAI,OAAM,MAAK,WAAU,MAAK,MAAK,MAAK,OAAM,KAAI,KAAI,MAAK,OAAM,KAAI,KAAI,MAAK,KAAI,KAAI,OAAM,KAAI,QAAO,KAAI,MAAK,MAAK,MAAK,KAAI,SAAQ,KAAI,kBAAiB,KAAI,MAAK,MAAK,MAAK,KAAI,KAAI,KAAI,KAAI,KAAI,cAAa,KAAI,WAAU,KAAI,sBAAqB,KAAI,YAAW,KAAI,UAAS,KAAI,eAAc,MAAK,WAAU,KAAI,YAAW,KAAI,iBAAgB,KAAI,qBAAoB,MAAK,mBAAkB,MAAK,gBAAe,KAAI,sBAAqB,MAAK,iBAAgB,KAAI,iBAAgB,MAAK,cAAa,MAAK,OAAM,KAAI,UAAS,MAAK,QAAO,MAAK,SAAQ,KAAI,SAAQ,KAAI,SAAQ,KAAI,oBAAmB,MAAK,iBAAgB,KAAI,sBAAqB,KAAI,SAAQ,KAAI,cAAa,KAAI,gBAAe,KAAI,aAAY,MAAK,mBAAkB,MAAK,cAAa,KAAI,yBAAwB,MAAK,mBAAkB,MAAK,OAAM,KAAI,SAAQ,KAAI,SAAQ,KAAI,SAAQ,KAAI,aAAY,KAAI,kBAAiB,MAAK,uBAAsB,KAAI,mBAAkB,KAAI,qBAAoB,MAAK,kBAAiB,KAAI,uBAAsB,KAAI,iBAAgB,MAAK,sBAAqB,KAAI,mBAAkB,MAAK,wBAAuB,KAAI,WAAU,MAAK,gBAAe,KAAI,aAAY,KAAI,kBAAiB,MAAK,uBAAsB,KAAI,kBAAiB,MAAK,aAAY,MAAK,kBAAiB,KAAI,UAAS,KAAI,eAAc,KAAI,mBAAkB,KAAI,eAAc,KAAI,gBAAe,KAAI,WAAU,KAAI,MAAK,KAAI,QAAO,MAAK,OAAM,MAAK,SAAQ,KAAI,KAAI,KAAI,QAAO,KAAI,OAAM,KAAI,SAAQ,MAAK,MAAK,MAAK,QAAO,MAAK,OAAM,KAAI,OAAM,KAAI,QAAO,MAAK,aAAY,KAAI,aAAY,KAAI,OAAM,KAAI,QAAO,KAAI,KAAI,KAAI,QAAO,KAAI,MAAK,MAAK,MAAK,MAAK,MAAK,MAAK,WAAU,KAAI,gBAAe,KAAI,MAAK,KAAI,OAAM,KAAI,QAAO,KAAI,OAAM,KAAI,OAAM,KAAI,SAAQ,KAAI,SAAQ,KAAI,MAAK,KAAI,OAAM,MAAK,OAAM,KAAI,SAAQ,MAAK,WAAU,KAAI,YAAW,MAAK,OAAM,KAAI,SAAQ,MAAK,MAAK,KAAI,OAAM,MAAK,OAAM,KAAI,SAAQ,MAAK,WAAU,KAAI,YAAW,MAAK,MAAK,KAAI,QAAO,KAAI,QAAO,KAAI,MAAK,KAAI,eAAc,KAAI,iBAAgB,KAAI,gBAAe,KAAI,kBAAiB,KAAI,IAAG,KAAI,IAAG,KAAI,KAAI,KAAI,QAAO,KAAI,OAAM,KAAI,MAAK,MAAK,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,MAAK,MAAK,MAAK,MAAK,QAAO,KAAI,SAAQ,KAAI,QAAO,KAAI,MAAK,MAAK,MAAK,MAAK,SAAQ,MAAK,QAAO,KAAI,SAAQ,MAAK,OAAM,MAAK,QAAO,KAAI,OAAM,KAAI,OAAM,KAAI,SAAQ,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,MAAK,KAAI,OAAM,KAAI,OAAM,KAAI,MAAK,KAAI,KAAI,KAAI,KAAI,KAAI,OAAM,KAAI,QAAO,KAAI,QAAO,KAAI,MAAK,KAAI,MAAK,KAAI,QAAO,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,KAAI,MAAK,KAAI,MAAK,MAAK,KAAI,QAAO,KAAI,QAAO,KAAI,KAAI,KAAI,OAAM,KAAI,KAAI,KAAI,MAAK,KAAI,OAAM,KAAI,OAAM,KAAI,SAAQ,KAAI,OAAM,KAAI,KAAI,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,SAAQ,KAAI,SAAQ,KAAI,MAAK,KAAI,QAAO,KAAI,MAAK,MAAK,MAAK,MAAK,MAAK,KAAI,sBAAqB,KAAI,gBAAe,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,IAAG,KAAI,IAAG,KAAI,KAAI,KAAI,OAAM,KAAI,SAAQ,KAAI,MAAK,KAAI,MAAK,KAAI,QAAO,KAAI,MAAK,KAAI,SAAQ,KAAI,KAAI,KAAI,IAAG,KAAI,MAAK,MAAK,MAAK,KAAI,QAAO,KAAI,QAAO,KAAI,MAAK,KAAI,QAAO,KAAI,QAAO,KAAI,UAAS,KAAI,QAAO,KAAI,QAAO,KAAI,MAAK,KAAI,MAAK,KAAI,OAAM,KAAI,SAAQ,KAAI,WAAU,KAAI,aAAY,KAAI,iBAAgB,KAAI,MAAK,KAAI,UAAS,KAAI,KAAI,KAAI,QAAO,KAAI,OAAM,KAAI,MAAK,KAAI,UAAS,KAAI,KAAI,KAAI,KAAI,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,MAAK,KAAI,SAAQ,KAAI,KAAI,MAAK,KAAI,MAAK,KAAI,KAAI,KAAI,KAAI,MAAK,KAAI,QAAO,KAAI,OAAM,KAAI,IAAG,KAAI,IAAG,KAAI,WAAU,KAAI,KAAI,KAAI,QAAO,KAAI,SAAQ,KAAI,QAAO,KAAI,UAAS,KAAI,OAAM,KAAI,SAAQ,KAAI,MAAK,KAAI,QAAO,KAAI,QAAO,KAAI,OAAM,KAAI,WAAU,KAAI,QAAO,KAAI,SAAQ,KAAI,SAAQ,KAAI,IAAG,KAAI,eAAc,KAAI,UAAS,KAAI,MAAK,MAAK,MAAK,KAAI,OAAM,KAAI,MAAK,KAAI,IAAG,KAAI,IAAG,KAAI,OAAM,KAAI,YAAW,KAAI,MAAK,KAAI,aAAY,KAAI,UAAS,KAAI,eAAc,KAAI,oBAAmB,KAAI,eAAc,KAAI,QAAO,KAAI,aAAY,KAAI,UAAS,KAAI,UAAS,KAAI,KAAI,KAAI,KAAI,KAAI,SAAQ,KAAI,OAAM,KAAI,OAAM,KAAI,QAAO,KAAI,OAAM,KAAI,MAAK,KAAI,QAAO,KAAI,MAAK,KAAI,SAAQ,KAAI,UAAS,KAAI,UAAS,KAAI,UAAS,KAAI,MAAK,KAAI,cAAa,KAAI,YAAW,KAAI,QAAO,KAAI,OAAM,KAAI,QAAO,KAAI,MAAK,MAAK,MAAK,MAAK,KAAI,KAAI,KAAI,KAAI,QAAO,KAAI,KAAI,MAAK,KAAI,MAAK,MAAK,KAAI,MAAK,MAAK,MAAK,KAAI,QAAO,KAAI,MAAK,MAAK,MAAK,MAAK,aAAY,KAAI,SAAQ,KAAI,OAAM,KAAI,SAAQ,KAAI,MAAK,KAAI,MAAK,KAAI,OAAM,KAAI,MAAK,MAAK,QAAO,KAAI,QAAO,KAAI,OAAM,KAAI,UAAS,KAAI,MAAK,KAAI,MAAK,KAAI,OAAM,KAAI,OAAM,KAAI,QAAO,KAAI,OAAM,KAAI,QAAO,KAAI,OAAM,KAAI,SAAQ,KAAI,OAAM,KAAI,MAAK,KAAI,MAAK,KAAI,MAAK,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,SAAQ,KAAI,QAAO,KAAI,QAAO,KAAI,OAAM,KAAI,QAAO,KAAI,QAAO,KAAI,OAAM,KAAI,WAAU,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,QAAO,KAAI,QAAO,KAAI,OAAM,KAAI,SAAQ,KAAI,SAAQ,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,OAAM,KAAI,MAAK,KAAI,KAAI,KAAI,KAAI,KAAI,MAAK,KAAI,SAAQ,KAAI,OAAM,KAAI,QAAO,KAAI,MAAK,KAAI,MAAK,KAAI,SAAQ,KAAI,UAAS,KAAI,OAAM,KAAI,IAAG,KAAI,MAAK,KAAI,KAAI,KAAI,KAAI,KAAI,gBAAe,KAAI,oBAAmB,KAAI,sBAAqB,KAAI,QAAO,KAAI,QAAO,KAAI,KAAI,MAAK,KAAI,KAAI,MAAK,KAAI,OAAM,KAAI,OAAM,KAAI,QAAO,KAAI,KAAI,KAAI,KAAI,KAAI,MAAK,KAAI,mBAAkB,KAAI,eAAc,KAAI,YAAW,KAAI,YAAW,KAAI,YAAW,KAAI,qBAAoB,KAAI,gBAAe,KAAI,cAAa,KAAI,oBAAmB,KAAI,oBAAmB,KAAI,oBAAmB,KAAI,iBAAgB,KAAI,YAAW,KAAI,kBAAiB,KAAI,gBAAe,KAAI,iBAAgB,KAAI,mBAAkB,KAAI,kBAAiB,KAAI,iBAAgB,KAAI,eAAc,KAAI,UAAS,KAAI,gBAAe,KAAI,iBAAgB,KAAI,kBAAiB,KAAI,eAAc,KAAI,oBAAmB,KAAI,mBAAkB,KAAI,kBAAiB,KAAI,kBAAiB,KAAI,eAAc,KAAI,gBAAe,KAAI,aAAY,KAAI,MAAK,KAAI,cAAa,KAAI,OAAM,KAAI,OAAM,KAAI,KAAI,KAAI,YAAW,KAAI,QAAO,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,MAAK,MAAK,MAAK,KAAI,QAAO,KAAI,SAAQ,KAAI,cAAa,KAAI,MAAK,KAAI,QAAO,KAAI,UAAS,KAAI,OAAM,KAAI,aAAY,KAAI,QAAO,KAAI,MAAK,MAAK,MAAK,KAAI,KAAI,KAAI,KAAI,KAAI,MAAK,KAAI,OAAM,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,MAAK,KAAI,OAAM,KAAI,OAAM,KAAI,UAAS,KAAI,aAAY,KAAI,SAAQ,KAAI,IAAG,KAAI,QAAO,KAAI,QAAO,KAAI,OAAM,KAAI,MAAK,KAAI,QAAO,KAAI,QAAO,KAAI,IAAG,KAAI,IAAG,KAAI,OAAM,KAAI,KAAI,KAAI,KAAI,KAAI,QAAO,KAAI,QAAO,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,MAAK,KAAI,QAAO,KAAI,UAAS,KAAI,OAAM,KAAI,KAAI,KAAI,KAAI,KAAI,OAAM,KAAI,MAAK,KAAI,OAAM,KAAI,QAAO,KAAI,OAAM,KAAI,OAAM,KAAI,SAAQ,KAAI,MAAK,KAAI,MAAK,KAAI,QAAO,KAAI,UAAS,KAAI,OAAM,KAAI,MAAK,KAAI,KAAI,MAAK,KAAI,MAAK,QAAO,KAAI,OAAM,KAAI,QAAO,KAAI,QAAO,KAAI,MAAK,KAAI,MAAK,KAAI,gBAAe,KAAI,gBAAe,KAAI,UAAS,KAAI,eAAc,KAAI,iBAAgB,KAAI,cAAa,KAAI,KAAI,KAAI,OAAM,KAAI,OAAM,KAAI,QAAO,KAAI,QAAO,KAAI,KAAI,KAAI,QAAO,KAAI,MAAK,KAAI,OAAM,KAAI,MAAK,KAAI,OAAM,KAAI,MAAK,KAAI,OAAM,KAAI,OAAM,KAAI,SAAQ,KAAI,SAAQ,KAAI,OAAM,KAAI,aAAY,KAAI,eAAc,KAAI,QAAO,KAAI,UAAS,KAAI,MAAK,KAAI,OAAM,KAAI,KAAI,KAAI,MAAK,KAAI,OAAM,MAAK,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,MAAK,KAAI,KAAI,KAAI,MAAK,MAAK,MAAK,MAAK,QAAO,KAAI,WAAU,KAAI,MAAK,KAAI,OAAM,KAAI,QAAO,MAAK,OAAM,KAAI,QAAO,MAAK,MAAK,KAAI,OAAM,KAAI,QAAO,KAAI,UAAS,KAAI,YAAW,KAAI,OAAM,KAAI,QAAO,KAAI,UAAS,KAAI,YAAW,KAAI,QAAO,KAAI,QAAO,KAAI,oBAAmB,KAAI,cAAa,KAAI,mBAAkB,KAAI,gBAAe,KAAI,qBAAoB,KAAI,aAAY,KAAI,QAAO,KAAI,KAAI,KAAI,MAAK,KAAI,OAAM,KAAI,MAAK,MAAK,MAAK,MAAK,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,MAAK,KAAI,MAAK,KAAI,OAAM,KAAI,iBAAgB,KAAI,aAAY,KAAI,OAAM,KAAI,KAAI,KAAI,KAAI,KAAI,QAAO,KAAI,MAAK,KAAI,MAAK,KAAI,SAAQ,KAAI,SAAQ,KAAI,OAAM,KAAI,OAAM,KAAI,SAAQ,KAAI,SAAQ,KAAI,QAAO,KAAI,QAAO,KAAI,UAAS,KAAI,WAAU,KAAI,aAAY,KAAI,WAAU,KAAI,YAAW,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,YAAW,KAAI,MAAK,KAAI,aAAY,KAAI,UAAS,KAAI,eAAc,KAAI,oBAAmB,KAAI,eAAc,KAAI,QAAO,KAAI,aAAY,KAAI,UAAS,KAAI,UAAS,KAAI,SAAQ,KAAI,UAAS,KAAI,KAAI,KAAI,KAAI,KAAI,MAAK,KAAI,MAAK,KAAI,MAAK,KAAI,MAAK,KAAI,KAAI,KAAI,KAAI,KAAI,QAAO,KAAI,SAAQ,KAAI,MAAK,KAAI,MAAK,KAAI,SAAQ,KAAI,UAAS,KAAI,eAAc,KAAI,SAAQ,KAAI,SAAQ,KAAI,SAAQ,KAAI,SAAQ,KAAI,OAAM,KAAI,OAAM,KAAI,SAAQ,KAAI,QAAO,KAAI,QAAO,KAAI,UAAS,KAAI,WAAU,KAAI,WAAU,KAAI,YAAW,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,OAAM,KAAI,OAAM,KAAI,SAAQ,KAAI,QAAO,KAAI,OAAM,KAAI,KAAI,KAAK,QAAO,KAAI,KAAI,KAAI,KAAI,KAAI,MAAK,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,KAAI,KAAI,KAAI,KAAI,MAAK,KAAI,QAAO,KAAI,KAAI,MAAK,KAAI,MAAK,QAAO,KAAI,WAAU,KAAI,WAAU,KAAI,OAAM,KAAI,OAAM,KAAI,UAAS,KAAI,QAAO,KAAI,aAAY,KAAI,UAAS,KAAI,YAAW,MAAK,WAAU,KAAI,QAAO,KAAI,OAAM,KAAI,QAAO,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,YAAW,KAAI,gBAAe,KAAI,YAAW,KAAI,UAAS,KAAI,QAAO,KAAI,OAAM,KAAI,QAAO,KAAI,MAAK,KAAI,MAAK,KAAI,QAAO,KAAI,QAAO,KAAI,KAAI,KAAI,MAAK,MAAK,MAAK,MAAK,SAAQ,KAAI,MAAK,KAAI,QAAO,KAAI,OAAM,KAAI,OAAM,KAAI,UAAS,KAAI,cAAa,KAAI,cAAa,KAAI,gBAAe,KAAI,WAAU,KAAI,eAAc,KAAI,iBAAgB,KAAI,QAAO,KAAI,MAAK,KAAI,UAAS,KAAI,WAAU,KAAI,SAAQ,KAAI,OAAM,KAAI,SAAQ,KAAI,UAAS,KAAI,MAAK,MAAK,MAAK,MAAK,MAAK,KAAI,MAAK,KAAI,OAAM,KAAI,OAAM,KAAI,QAAO,KAAI,QAAO,KAAI,OAAM,KAAI,kBAAiB,KAAI,mBAAkB,KAAI,QAAO,KAAI,QAAO,KAAI,MAAK,KAAI,MAAK,KAAI,MAAK,KAAI,UAAS,KAAI,OAAM,KAAI,OAAM,KAAI,QAAO,KAAI,QAAO,KAAI,OAAM,KAAI,OAAM,KAAI,KAAI,KAAI,KAAI,KAAI,OAAM,KAAI,QAAO,KAAI,QAAO,KAAI,OAAM,KAAI,QAAO,KAAI,KAAI,MAAK,KAAI,MAAK,QAAO,KAAI,QAAO,KAAI,MAAK,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,QAAO,KAAI,UAAS,KAAI,QAAO,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,KAAI,KAAI,UAAS,KAAI,YAAW,KAAI,cAAa,KAAI,kBAAiB,KAAI,OAAM,KAAI,WAAU,KAAI,OAAM,KAAI,OAAM,KAAI,MAAK,MAAK,MAAK,MAAK,YAAW,KAAI,SAAQ,KAAI,SAAQ,KAAI,SAAQ,KAAI,kBAAiB,KAAI,aAAY,KAAI,aAAY,KAAI,aAAY,KAAI,eAAc,KAAI,eAAc,KAAI,gBAAe,KAAI,OAAM,KAAI,gBAAe,KAAI,iBAAgB,KAAI,MAAK,KAAI,MAAK,KAAI,OAAM,KAAI,SAAQ,KAAI,SAAQ,KAAI,YAAW,KAAI,OAAM,KAAI,YAAW,KAAI,QAAO,KAAI,UAAS,KAAI,QAAO,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,MAAK,MAAK,MAAK,MAAK,OAAM,KAAI,QAAO,KAAI,QAAO,KAAI,MAAK,KAAI,OAAM,KAAI,OAAM,KAAI,MAAK,KAAI,MAAK,KAAI,SAAQ,KAAI,QAAO,KAAI,YAAW,KAAI,UAAS,KAAI,YAAW,KAAI,QAAO,KAAI,OAAM,KAAI,WAAU,KAAI,MAAK,KAAI,MAAK,KAAI,QAAO,KAAI,UAAS,KAAI,cAAa,MAAK,eAAc,MAAK,cAAa,MAAK,eAAc,MAAK,UAAS,KAAI,iBAAgB,KAAI,kBAAiB,KAAI,MAAK,KAAI,MAAK,KAAI,OAAM,KAAI,KAAI,KAAI,KAAI,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,QAAO,KAAI,QAAO,KAAI,KAAI,KAAI,KAAI,KAAI,OAAM,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,MAAK,KAAI,MAAK,KAAI,aAAY,KAAI,cAAa,KAAI,mBAAkB,KAAI,eAAc,KAAI,eAAc,KAAI,KAAI,MAAK,KAAI,MAAK,OAAM,KAAI,OAAM,MAAK,OAAM,MAAK,MAAK,MAAK,MAAK,MAAK,OAAM,KAAI,OAAM,KAAI,MAAK,MAAK,MAAK,MAAK,QAAO,MAAK,QAAO,MAAK,QAAO,MAAK,QAAO,MAAK,QAAO,KAAI,SAAQ,KAAI,OAAM,KAAI,OAAM,KAAI,QAAO,KAAI,OAAM,KAAI,OAAM,KAAI,QAAO,KAAI,QAAO,KAAI,KAAI,MAAK,KAAI,MAAK,MAAK,MAAK,MAAK,MAAK,IAAG,KAAI,IAAG,KAAI,QAAO,KAAI,MAAK,MAAK,MAAK,MAAK,MAAK,KAAI,OAAM,KAAI,MAAK,KAAI,OAAM,KAAI,KAAI,MAAK,KAAI,MAAK,OAAM,KAAI,OAAM,KAAI,IAAG,KAAI,IAAG,KAAI,OAAM,KAAI,OAAM,KAAI,MAAK,KAAI,MAAK,KAAI,OAAM,KAAI,MAAK,MAAK,MAAK,MAAK,QAAO,KAAI,QAAO,KAAI,OAAM,KAAI,OAAM,KAAI,MAAK,MAAK,MAAK,MAAK,QAAO,KAAI,QAAO,KAAI,OAAM,KAAI,MAAK,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,MAAK,KAAI,MAAK,KAAI,OAAM,KAAI,OAAM,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,MAAK,KAAI,MAAK,MAAK,KAAI,MAAK,KAAI,MAAK,MAAK,MAAK,MAAK,MAAK,MAAK,MAAK,MAAK,MAAK,KAAI,MAAK,KAAI,MAAK,KAAI,MAAK,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,KAAI,KAAI,KAAI,KAAI,MAAK,KAAI,MAAK,KAAI,QAAO,KAAI,gBAAe,KAAI,MAAK,KAAI,MAAK,KAAI,KAAI,MAAK,KAAI,KAAI,MAAK,KAAI,MAAK,KAAI,SAAQ,KAAI,MAAK,MAAK,MAAK,KAAI,MAAK,MAAK,MAAK,MAAK,KAAI,KAAI,MAAK,IAAG,GAAE,IAAE,s2DAAq2D,IAAE,CAAE,GAAC,IAAE,CAAE;AAAC,SAAS,EAAED,IAAEC,IAAEC,IAAE;AAAC,MAAIC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,KAAE;AAAG,OAAI,YAAU,OAAOP,OAAIC,KAAED,IAAEA,KAAE,EAAE,eAAc,WAASC,OAAIA,KAAE,OAAIK,KAAE,SAASP,IAAE;AAAC,QAAIC,IAAEC,IAAEC,KAAE,EAAEH,EAAC;AAAE,QAAGG;AAAE,aAAOA;AAAE,SAAIA,KAAE,EAAEH,EAAC,IAAE,CAAA,GAAGC,KAAE,GAAEA,KAAE,KAAIA;AAAI,MAAAC,KAAE,OAAO,aAAaD,EAAC,GAAE,cAAc,KAAKC,EAAC,IAAEC,GAAE,KAAKD,EAAC,IAAEC,GAAE,KAAK,OAAK,MAAIF,GAAE,SAAS,EAAE,EAAE,YAAa,GAAE,MAAM,EAAE,CAAC;AAAE,SAAIA,KAAE,GAAEA,KAAED,GAAE,QAAOC;AAAI,MAAAE,GAAEH,GAAE,WAAWC,EAAC,CAAC,IAAED,GAAEC,EAAC;AAAE,WAAOE;AAAA,EAAC,EAAEF,EAAC,GAAEE,KAAE,GAAEC,KAAEJ,GAAE,QAAOG,KAAEC,IAAED;AAAI,QAAGE,KAAEL,GAAE,WAAWG,EAAC,GAAED,MAAG,OAAKG,MAAGF,KAAE,IAAEC,MAAG,iBAAiB,KAAKJ,GAAE,MAAMG,KAAE,GAAEA,KAAE,CAAC,CAAC;AAAE,MAAAK,MAAGR,GAAE,MAAMG,IAAEA,KAAE,CAAC,GAAEA,MAAG;AAAA,aAAUE,KAAE;AAAI,MAAAG,MAAGD,GAAEF,EAAC;AAAA,aAAUA,MAAG,SAAOA,MAAG,OAAM;AAAC,UAAGA,MAAG,SAAOA,MAAG,SAAOF,KAAE,IAAEC,OAAIE,KAAEN,GAAE,WAAWG,KAAE,CAAC,MAAI,SAAOG,MAAG,OAAM;AAAC,QAAAE,MAAG,mBAAmBR,GAAEG,EAAC,IAAEH,GAAEG,KAAE,CAAC,CAAC,GAAEA;AAAI;AAAA,MAAQ;AAAC,MAAAK,MAAG;AAAA,IAAW;AAAM,MAAAA,MAAG,mBAAmBR,GAAEG,EAAC,CAAC;AAAE,SAAOK;AAAC;AAAC,EAAE,eAAa,wBAAuB,EAAE,iBAAe;AAAY,IAAI,IAAE,GAAE,IAAE,CAAE;AAAC,SAAS,EAAER,IAAEC,IAAE;AAAC,MAAIC;AAAE,SAAM,YAAU,OAAOD,OAAIA,KAAE,EAAE,eAAcC,KAAE,SAASF,IAAE;AAAC,QAAIC,IAAEC,IAAEC,KAAE,EAAEH,EAAC;AAAE,QAAGG;AAAE,aAAOA;AAAE,SAAIA,KAAE,EAAEH,EAAC,IAAE,CAAE,GAACC,KAAE,GAAEA,KAAE,KAAIA;AAAI,MAAAC,KAAE,OAAO,aAAaD,EAAC,GAAEE,GAAE,KAAKD,EAAC;AAAE,SAAID,KAAE,GAAEA,KAAED,GAAE,QAAOC;AAAI,MAAAE,GAAED,KAAEF,GAAE,WAAWC,EAAC,CAAC,IAAE,OAAK,MAAIC,GAAE,SAAS,EAAE,EAAE,YAAa,GAAE,MAAM,EAAE;AAAE,WAAOC;AAAA,EAAC,EAAEF,EAAC,GAAED,GAAE,QAAQ,qBAAqB,SAASA,IAAE;AAAC,QAAIC,IAAEE,IAAEC,IAAEK,IAAEC,IAAEL,IAAEC,IAAEC,KAAE;AAAG,SAAIN,KAAE,GAAEE,KAAEH,GAAE,QAAOC,KAAEE,IAAEF,MAAG;AAAE,OAACG,KAAE,SAASJ,GAAE,MAAMC,KAAE,GAAEA,KAAE,CAAC,GAAE,EAAE,KAAG,MAAIM,MAAGL,GAAEE,EAAC,IAAE,QAAM,MAAIA,OAAIH,KAAE,IAAEE,MAAG,QAAM,OAAKM,KAAE,SAAST,GAAE,MAAMC,KAAE,GAAEA,KAAE,CAAC,GAAE,EAAE,OAAKM,OAAID,KAAEF,MAAG,IAAE,OAAK,KAAGK,MAAG,MAAI,OAAK,OAAO,aAAaH,EAAC,GAAEL,MAAG,KAAG,QAAM,MAAIG,OAAIH,KAAE,IAAEE,OAAIM,KAAE,SAAST,GAAE,MAAMC,KAAE,GAAEA,KAAE,CAAC,GAAE,EAAE,GAAES,KAAE,SAASV,GAAE,MAAMC,KAAE,GAAEA,KAAE,CAAC,GAAE,EAAE,GAAE,QAAM,MAAIQ,OAAI,QAAM,MAAIC,QAAKH,OAAID,KAAEF,MAAG,KAAG,QAAMK,MAAG,IAAE,OAAK,KAAGC,MAAG,QAAMJ,MAAG,SAAOA,MAAG,QAAM,QAAM,OAAO,aAAaA,EAAC,GAAEL,MAAG,KAAG,QAAM,MAAIG,OAAIH,KAAE,IAAEE,OAAIM,KAAE,SAAST,GAAE,MAAMC,KAAE,GAAEA,KAAE,CAAC,GAAE,EAAE,GAAES,KAAE,SAASV,GAAE,MAAMC,KAAE,GAAEA,KAAE,CAAC,GAAE,EAAE,GAAEI,KAAE,SAASL,GAAE,MAAMC,KAAE,IAAGA,KAAE,EAAE,GAAE,EAAE,GAAE,QAAM,MAAIQ,OAAI,QAAM,MAAIC,OAAI,QAAM,MAAIL,SAAMC,KAAEF,MAAG,KAAG,UAAQK,MAAG,KAAG,SAAOC,MAAG,IAAE,OAAK,KAAGL,MAAG,SAAOC,KAAE,UAAQC,MAAG,UAAQD,MAAG,OAAMC,MAAG,OAAO,aAAa,SAAOD,MAAG,KAAI,SAAO,OAAKA,GAAE,IAAGL,MAAG,KAAGM,MAAG;AAAI,WAAOA;AAAA,EAAC,CAAC;AAAE;AAAC,EAAE,eAAa,eAAc,EAAE,iBAAe;AAAG,IAAI,IAAE;AAAE,SAAS,IAAG;AAAC,OAAK,WAAS,MAAK,KAAK,UAAQ,MAAK,KAAK,OAAK,MAAK,KAAK,OAAK,MAAK,KAAK,WAAS,MAAK,KAAK,OAAK,MAAK,KAAK,SAAO,MAAK,KAAK,WAAS;AAAI;AAAC,IAAI,IAAE,qBAAoB,IAAE,YAAW,IAAE,sCAAqC,IAAE,CAAC,KAAI,KAAI,KAAI,MAAK,KAAI,GAAG,EAAE,OAAO,CAAC,KAAI,KAAI,KAAI,KAAI,KAAI,MAAK,MAAK,GAAI,CAAC,GAAE,IAAE,CAAC,GAAG,EAAE,OAAO,CAAC,GAAE,IAAE,CAAC,KAAI,KAAI,KAAI,KAAI,GAAG,EAAE,OAAO,CAAC,GAAE,IAAE,CAAC,KAAI,KAAI,GAAG,GAAE,IAAE,0BAAyB,IAAE,gCAA+B,IAAE,EAAC,YAAW,MAAG,eAAc,KAAE,GAAE,IAAE,EAAC,MAAK,MAAG,OAAM,MAAG,KAAI,MAAG,QAAO,MAAG,MAAK,MAAG,SAAQ,MAAG,UAAS,MAAG,QAAO,MAAG,WAAU,MAAG,SAAQ,KAAE;AAAE,EAAE,UAAU,QAAM,SAASP,IAAEC,IAAE;AAAC,MAAIC,IAAEC,IAAEC,IAAEK,IAAEC,IAAEL,KAAEL;AAAE,MAAGK,KAAEA,GAAE,KAAM,GAAC,CAACJ,MAAG,MAAID,GAAE,MAAM,GAAG,EAAE,QAAO;AAAC,QAAIM,KAAE,EAAE,KAAKD,EAAC;AAAE,QAAGC;AAAE,aAAO,KAAK,WAASA,GAAE,CAAC,GAAEA,GAAE,CAAC,MAAI,KAAK,SAAOA,GAAE,CAAC,IAAG;AAAA,EAAI;AAAC,MAAIC,KAAE,EAAE,KAAKF,EAAC;AAAE,MAAGE,OAAIH,MAAGG,KAAEA,GAAE,CAAC,GAAG,YAAa,GAAC,KAAK,WAASA,IAAEF,KAAEA,GAAE,OAAOE,GAAE,MAAM,KAAIN,MAAGM,MAAGF,GAAE,MAAM,sBAAsB,OAAK,EAAEK,KAAE,SAAOL,GAAE,OAAO,GAAE,CAAC,MAAIE,MAAG,EAAEA,EAAC,MAAIF,KAAEA,GAAE,OAAO,CAAC,GAAE,KAAK,UAAQ,QAAK,CAAC,EAAEE,EAAC,MAAIG,MAAGH,MAAG,CAAC,EAAEA,EAAC,IAAG;AAAC,QAAIC,IAAEG,IAAEC,KAAE;AAAG,SAAIV,KAAE,GAAEA,KAAE,EAAE,QAAOA;AAAI,cAAMO,KAAEJ,GAAE,QAAQ,EAAEH,EAAC,CAAC,OAAK,OAAKU,MAAGH,KAAEG,QAAKA,KAAEH;AAAG,SAAI,QAAME,KAAE,OAAKC,KAAEP,GAAE,YAAY,GAAG,IAAEA,GAAE,YAAY,KAAIO,EAAC,OAAKJ,KAAEH,GAAE,MAAM,GAAEM,EAAC,GAAEN,KAAEA,GAAE,MAAMM,KAAE,CAAC,GAAE,KAAK,OAAKH,KAAGI,KAAE,IAAGV,KAAE,GAAEA,KAAE,EAAE,QAAOA;AAAI,cAAMO,KAAEJ,GAAE,QAAQ,EAAEH,EAAC,CAAC,OAAK,OAAKU,MAAGH,KAAEG,QAAKA,KAAEH;AAAG,WAAKG,OAAIA,KAAEP,GAAE,SAAQ,QAAMA,GAAEO,KAAE,CAAC,KAAGA;AAAI,QAAIC,KAAER,GAAE,MAAM,GAAEO,EAAC;AAAE,IAAAP,KAAEA,GAAE,MAAMO,EAAC,GAAE,KAAK,UAAUC,EAAC,GAAE,KAAK,WAAS,KAAK,YAAU;AAAG,QAAIC,KAAE,QAAM,KAAK,SAAS,CAAC,KAAG,QAAM,KAAK,SAAS,KAAK,SAAS,SAAO,CAAC;AAAE,QAAG,CAACA,IAAE;AAAC,UAAIC,KAAE,KAAK,SAAS,MAAM,IAAI;AAAE,WAAIb,KAAE,GAAEC,KAAEY,GAAE,QAAOb,KAAEC,IAAED,MAAI;AAAC,YAAIc,KAAED,GAAEb,EAAC;AAAE,YAAGc,MAAG,CAACA,GAAE,MAAM,CAAC,GAAE;AAAC,mBAAQC,KAAE,IAAGC,KAAE,GAAEC,KAAEH,GAAE,QAAOE,KAAEC,IAAED;AAAI,YAAAF,GAAE,WAAWE,EAAC,IAAE,MAAID,MAAG,MAAIA,MAAGD,GAAEE,EAAC;AAAE,cAAG,CAACD,GAAE,MAAM,CAAC,GAAE;AAAC,gBAAIG,KAAEL,GAAE,MAAM,GAAEb,EAAC,GAAEmB,KAAEN,GAAE,MAAMb,KAAE,CAAC,GAAEoB,KAAEN,GAAE,MAAM,CAAC;AAAE,YAAAM,OAAIF,GAAE,KAAKE,GAAE,CAAC,CAAC,GAAED,GAAE,QAAQC,GAAE,CAAC,CAAC,IAAGD,GAAE,WAAShB,KAAEgB,GAAE,KAAK,GAAG,IAAEhB,KAAG,KAAK,WAASe,GAAE,KAAK,GAAG;AAAE;AAAA,UAAK;AAAA,QAAC;AAAA,MAAC;AAAA,IAAC;AAAC,SAAK,SAAS,SAAO,QAAM,KAAK,WAAS,KAAIN,OAAI,KAAK,WAAS,KAAK,SAAS,OAAO,GAAE,KAAK,SAAS,SAAO,CAAC;AAAA,EAAE;AAAC,MAAIS,KAAElB,GAAE,QAAQ,GAAG;AAAE,SAAKkB,OAAI,KAAK,OAAKlB,GAAE,OAAOkB,EAAC,GAAElB,KAAEA,GAAE,MAAM,GAAEkB,EAAC;AAAG,MAAIC,KAAEnB,GAAE,QAAQ,GAAG;AAAE,SAAM,OAAKmB,OAAI,KAAK,SAAOnB,GAAE,OAAOmB,EAAC,GAAEnB,KAAEA,GAAE,MAAM,GAAEmB,EAAC,IAAGnB,OAAI,KAAK,WAASA,KAAG,EAAED,EAAC,KAAG,KAAK,YAAU,CAAC,KAAK,aAAW,KAAK,WAAS,KAAI;AAAI,GAAE,EAAE,UAAU,YAAU,SAASJ,IAAE;AAAC,MAAIC,KAAE,EAAE,KAAKD,EAAC;AAAE,EAAAC,OAAI,SAAOA,KAAEA,GAAE,CAAC,OAAK,KAAK,OAAKA,GAAE,OAAO,CAAC,IAAGD,KAAEA,GAAE,OAAO,GAAEA,GAAE,SAAOC,GAAE,MAAM,IAAGD,OAAI,KAAK,WAASA;AAAE;AAAE,IAAI,IAAE,SAASA,IAAEC,IAAE;AAAC,MAAGD,MAAGA,cAAa;AAAE,WAAOA;AAAE,MAAIE,KAAE,IAAI;AAAE,SAAOA,GAAE,MAAMF,IAAEC,EAAC,GAAEC;AAAC;AAAE,EAAE,SAAO,GAAE,EAAE,SAAO,GAAE,EAAE,SAAO,SAASF,IAAE;AAAC,MAAIC,KAAE;AAAG,SAAOA,MAAGD,GAAE,YAAU,IAAGC,MAAGD,GAAE,UAAQ,OAAK,IAAGC,MAAGD,GAAE,OAAKA,GAAE,OAAK,MAAI,IAAGA,GAAE,YAAU,OAAKA,GAAE,SAAS,QAAQ,GAAG,IAAEC,MAAG,MAAID,GAAE,WAAS,MAAIC,MAAGD,GAAE,YAAU,IAAGC,MAAGD,GAAE,OAAK,MAAIA,GAAE,OAAK,IAAGC,MAAGD,GAAE,YAAU,IAAGC,MAAGD,GAAE,UAAQ,IAAGC,MAAGD,GAAE,QAAM;AAAE,GAAE,EAAE,QAAM;AAAE,IAAI,IAAE,CAAE,GAAC,IAAE,oIAAmI,IAAE,sBAAqB,IAAE;AAA2D,EAAE,MAAI,GAAE,EAAE,KAAG,GAAE,EAAE,KAAG,yNAAwN,EAAE,IAAE,GAAE,EAAE,IAAE,GAAE,SAASA,IAAE;AAAC,MAAIC,KAAE,OAAO,UAAU;AAAe,WAASQ,GAAET,IAAEE,IAAE;AAAC,WAAOD,GAAE,KAAKD,IAAEE,EAAC;AAAA,EAAC;AAAC,WAASQ,GAAEV,IAAE;AAAC,WAAM,EAAEA,MAAG,SAAOA,MAAG,WAAS,EAAEA,MAAG,SAAOA,MAAG,WAAS,UAAQ,QAAMA,OAAI,UAAQ,QAAMA,QAAK,EAAEA,MAAG,KAAGA,MAAG,OAAK,OAAKA,OAAI,EAAEA,MAAG,MAAIA,MAAG,QAAM,EAAEA,MAAG,OAAKA,MAAG,QAAM,EAAEA,KAAE;AAAA,EAAc;AAAC,WAASK,GAAEL,IAAE;AAAC,QAAGA,KAAE,OAAM;AAAC,UAAIC,KAAE,UAAQD,MAAG,UAAQ,KAAIE,KAAE,SAAO,OAAKF;AAAG,aAAO,OAAO,aAAaC,IAAEC,EAAC;AAAA,IAAC;AAAC,WAAO,OAAO,aAAaF,EAAC;AAAA,EAAC;AAAC,MAAIM,KAAE,+CAA8CC,KAAE,IAAI,OAAOD,GAAE,SAAO,MAAI,6BAA6B,QAAO,IAAI,GAAEE,KAAE,sCAAqCG,KAAE;AAAE,MAAIc,KAAE,UAASb,KAAE,WAAUc,KAAE,EAAC,KAAI,SAAQ,KAAI,QAAO,KAAI,QAAO,KAAI,SAAQ;AAAE,WAASb,GAAEb,IAAE;AAAC,WAAO0B,GAAE1B,EAAC;AAAA,EAAC;AAAC,MAAIc,KAAE;AAAuB,MAAIa,KAAE;AAAE,EAAA3B,GAAE,MAAI,CAAE,GAACA,GAAE,IAAI,QAAM,GAAEA,GAAE,IAAI,UAAQ,GAAEA,GAAE,SAAO,SAASA,IAAE;AAAC,QAAIC,KAAE,MAAM,UAAU,MAAM,KAAK,WAAU,CAAC;AAAE,WAAOA,GAAE,QAAS,SAASA,IAAE;AAAC,UAAGA,IAAE;AAAC,YAAG,YAAU,OAAOA;AAAE,gBAAM,IAAI,UAAUA,KAAE,gBAAgB;AAAE,eAAO,KAAKA,EAAC,EAAE,QAAS,SAASC,IAAE;AAAC,UAAAF,GAAEE,EAAC,IAAED,GAAEC,EAAC;AAAA,QAAC,CAAC;AAAA,MAAE;AAAA,IAAC,CAAG,GAACF;AAAA,EAAC,GAAEA,GAAE,WAAS,SAASA,IAAE;AAAC,WAAM,sBAAoB,SAASA,IAAE;AAAC,aAAO,OAAO,UAAU,SAAS,KAAKA,EAAC;AAAA,IAAC,EAAEA,EAAC;AAAA,EAAC,GAAEA,GAAE,MAAIS,IAAET,GAAE,aAAW,SAASA,IAAE;AAAC,WAAOA,GAAE,QAAQ,IAAI,IAAE,IAAEA,KAAEA,GAAE,QAAQM,IAAE,IAAI;AAAA,EAAC,GAAEN,GAAE,cAAY,SAASA,IAAE;AAAC,WAAOA,GAAE,QAAQ,IAAI,IAAE,KAAGA,GAAE,QAAQ,GAAG,IAAE,IAAEA,KAAEA,GAAE,QAAQO,IAAG,SAASP,IAAEC,IAAEC,IAAE;AAAC,aAAOD,MAAG,SAASD,IAAEC,IAAE;AAAC,YAAIC,KAAE;AAAE,eAAOO,GAAEE,IAAEV,EAAC,IAAEU,GAAEV,EAAC,IAAE,OAAKA,GAAE,WAAW,CAAC,KAAGO,GAAE,KAAKP,EAAC,KAAGS,GAAER,KAAE,QAAMD,GAAE,CAAC,EAAE,YAAW,IAAG,SAASA,GAAE,MAAM,CAAC,GAAE,EAAE,IAAE,SAASA,GAAE,MAAM,CAAC,GAAE,EAAE,CAAC,IAAEI,GAAEH,EAAC,IAAEF;AAAA,MAAC,EAAEA,IAAEE,EAAC;AAAA,IAAC,CAAG;AAAA,EAAA,GAAEF,GAAE,oBAAkBU,IAAEV,GAAE,gBAAcK,IAAEL,GAAE,aAAW,SAASA,IAAE;AAAC,WAAOyB,GAAE,KAAKzB,EAAC,IAAEA,GAAE,QAAQY,IAAEC,EAAC,IAAEb;AAAA,EAAC,GAAEA,GAAE,iBAAe,SAASA,IAAEC,IAAEC,IAAE;AAAC,WAAM,CAAA,EAAG,OAAOF,GAAE,MAAM,GAAEC,EAAC,GAAEC,IAAEF,GAAE,MAAMC,KAAE,CAAC,CAAC;AAAA,EAAC,GAAED,GAAE,UAAQ,SAASA,IAAE;AAAC,YAAOA;MAAG,KAAK;AAAA,MAAE,KAAK;AAAG,eAAM;AAAA,IAAE;AAAC,WAAM;AAAA,EAAE,GAAEA,GAAE,eAAa,SAASA,IAAE;AAAC,QAAGA,MAAG,QAAMA,MAAG;AAAK,aAAM;AAAG,YAAOA,IAAG;AAAA,MAAA,KAAK;AAAA,MAAE,KAAK;AAAA,MAAG,KAAK;AAAA,MAAG,KAAK;AAAA,MAAG,KAAK;AAAA,MAAG,KAAK;AAAA,MAAG,KAAK;AAAA,MAAI,KAAK;AAAA,MAAK,KAAK;AAAA,MAAK,KAAK;AAAA,MAAK,KAAK;AAAM,eAAM;AAAA,IAAE;AAAC,WAAM;AAAA,EAAE,GAAEA,GAAE,iBAAe,SAASA,IAAE;AAAC,YAAOA,IAAG;AAAA,MAAA,KAAK;AAAA,MAAG,KAAK;AAAA,MAAG,KAAK;AAAA,MAAG,KAAK;AAAA,MAAG,KAAK;AAAA,MAAG,KAAK;AAAA,MAAG,KAAK;AAAA,MAAG,KAAK;AAAA,MAAG,KAAK;AAAA,MAAG,KAAK;AAAA,MAAG,KAAK;AAAA,MAAG,KAAK;AAAA,MAAG,KAAK;AAAA,MAAG,KAAK;AAAA,MAAG,KAAK;AAAA,MAAG,KAAK;AAAA,MAAG,KAAK;AAAA,MAAG,KAAK;AAAA,MAAG,KAAK;AAAA,MAAG,KAAK;AAAA,MAAG,KAAK;AAAA,MAAG,KAAK;AAAA,MAAG,KAAK;AAAA,MAAG,KAAK;AAAA,MAAG,KAAK;AAAA,MAAG,KAAK;AAAA,MAAG,KAAK;AAAA,MAAG,KAAK;AAAA,MAAG,KAAK;AAAA,MAAI,KAAK;AAAA,MAAI,KAAK;AAAA,MAAI,KAAK;AAAI,eAAM;AAAA,MAAG;AAAQ,eAAM;AAAA,IAAE;AAAA,EAAC,GAAEA,GAAE,cAAY,SAASA,IAAE;AAAC,WAAO2B,GAAE,KAAK3B,EAAC;AAAA,EAAC,GAAEA,GAAE,WAAS,SAASA,IAAE;AAAC,WAAOA,GAAE,QAAQc,IAAE,MAAM;AAAA,EAAC,GAAEd,GAAE,qBAAmB,SAASA,IAAE;AAAC,WAAOA,KAAEA,GAAE,KAAI,EAAG,QAAQ,QAAO,GAAG,GAAE,QAAM,IAAI,YAAa,MAAGA,KAAEA,GAAE,QAAQ,MAAK,GAAG,IAAGA,GAAE,YAAa,EAAC,YAAW;AAAA,EAAE;AAAC,EAAE,CAAC;AAAE,IAAI,IAAE,CAAE,GAAC,IAAE,EAAE,aAAY,IAAE,EAAE;AAAY,EAAE,iBAAe,SAASA,IAAEC,IAAEC,IAAE;AAAC,MAAIC,IAAEC,IAAEK,IAAEC,IAAEL,KAAE,IAAGC,KAAEN,GAAE,QAAOO,KAAEP,GAAE;AAAI,OAAIA,GAAE,MAAIC,KAAE,GAAEE,KAAE,GAAEH,GAAE,MAAIM,MAAG;AAAC,QAAG,QAAMG,KAAET,GAAE,IAAI,WAAWA,GAAE,GAAG,MAAI,MAAI,EAAEG,IAAE;AAAC,MAAAC,KAAE;AAAG;AAAA,IAAK;AAAC,QAAGM,KAAEV,GAAE,KAAIA,GAAE,GAAG,OAAO,UAAUA,EAAC,GAAE,OAAKS;AAAE,UAAGC,OAAIV,GAAE,MAAI;AAAE,QAAAG;AAAA,eAAYD;AAAE,eAAOF,GAAE,MAAIO,IAAE;AAAA;AAAA,EAAE;AAAC,SAAOH,OAAIC,KAAEL,GAAE,MAAKA,GAAE,MAAIO,IAAEF;AAAC,GAAE,EAAE,uBAAqB,SAASL,IAAEC,IAAEC,IAAE;AAAC,MAAIC,IAAEC,IAAEK,KAAER,IAAES,KAAE,EAAC,IAAG,OAAG,KAAI,GAAE,OAAM,GAAE,KAAI,GAAE;AAAE,MAAG,OAAKV,GAAE,WAAWC,EAAC,GAAE;AAAC,SAAIA,MAAIA,KAAEC,MAAG;AAAC,UAAG,QAAMC,KAAEH,GAAE,WAAWC,EAAC;AAAG,eAAOS;AAAE,UAAG,OAAKP;AAAE,eAAOO;AAAE,UAAG,OAAKP;AAAE,eAAOO,GAAE,MAAIT,KAAE,GAAES,GAAE,MAAI,EAAEV,GAAE,MAAMS,KAAE,GAAER,EAAC,CAAC,GAAES,GAAE,KAAG,MAAGA;AAAE,aAAKP,MAAGF,KAAE,IAAEC,KAAED,MAAG,IAAEA;AAAA,IAAG;AAAC,WAAOS;AAAA,EAAC;AAAC,OAAIN,KAAE,GAAEH,KAAEC,MAAG,QAAMC,KAAEH,GAAE,WAAWC,EAAC,MAAI,EAAEE,KAAE,MAAI,QAAMA;AAAI,QAAG,OAAKA,MAAGF,KAAE,IAAEC,IAAE;AAAC,UAAG,OAAKF,GAAE,WAAWC,KAAE,CAAC;AAAE;AAAM,MAAAA,MAAG;AAAA,IAAC,OAAK;AAAC,UAAG,OAAKE,MAAG,EAAEC,KAAE;AAAG,eAAOM;AAAE,UAAG,OAAKP,IAAE;AAAC,YAAG,MAAIC;AAAE;AAAM,QAAAA;AAAA,MAAG;AAAC,MAAAH;AAAA,IAAG;AAAC,SAAOQ,OAAIR,MAAG,MAAIG,OAAIM,GAAE,MAAI,EAAEV,GAAE,MAAMS,IAAER,EAAC,CAAC,GAAES,GAAE,QAAM,GAAEA,GAAE,MAAIT,IAAES,GAAE,KAAG,OAAIA;AAAC,GAAE,EAAE,iBAAe,SAASV,IAAEC,IAAEC,IAAE;AAAC,MAAIC,IAAEC,IAAEK,KAAE,GAAEC,KAAET,IAAEI,KAAE,EAAC,IAAG,OAAG,KAAI,GAAE,OAAM,GAAE,KAAI,GAAE;AAAE,MAAGJ,MAAGC;AAAE,WAAOG;AAAE,MAAG,QAAMD,KAAEJ,GAAE,WAAWC,EAAC,MAAI,OAAKG,MAAG,OAAKA;AAAE,WAAOC;AAAE,OAAIJ,MAAI,OAAKG,OAAIA,KAAE,KAAIH,KAAEC,MAAG;AAAC,SAAIC,KAAEH,GAAE,WAAWC,EAAC,OAAKG;AAAE,aAAOC,GAAE,MAAIJ,KAAE,GAAEI,GAAE,QAAMI,IAAEJ,GAAE,MAAI,EAAEL,GAAE,MAAMU,KAAE,GAAET,EAAC,CAAC,GAAEI,GAAE,KAAG,MAAGA;AAAE,QAAG,OAAKF,MAAG,OAAKC;AAAE,aAAOC;AAAE,WAAKF,KAAEM,OAAI,OAAKN,MAAGF,KAAE,IAAEC,OAAID,MAAI,OAAKD,GAAE,WAAWC,EAAC,KAAGQ,OAAKR;AAAA,EAAG;AAAC,SAAOI;AAAC;AAAE,IAAI,IAAE,EAAE,QAAO,IAAE,EAAE,aAAY,IAAE,EAAE,YAAW,IAAE,CAAE;AAAC,SAAS,IAAG;AAAC,OAAK,QAAM,EAAE,CAAA,GAAG,CAAC;AAAC;AAAC,EAAE,cAAY,SAASL,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,MAAIK,KAAET,GAAEC,EAAC;AAAE,SAAM,UAAQG,GAAE,YAAYK,EAAC,IAAE,MAAI,EAAET,GAAEC,EAAC,EAAE,OAAO,IAAE;AAAS,GAAE,EAAE,aAAW,SAASD,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,MAAIK,KAAET,GAAEC,EAAC;AAAE,SAAM,SAAOG,GAAE,YAAYK,EAAC,IAAE,YAAU,EAAET,GAAEC,EAAC,EAAE,OAAO,IAAE;AAAiB,GAAE,EAAE,QAAM,SAASD,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,MAAIK,IAAEC,IAAEL,IAAEC,IAAEC,IAAEC,KAAER,GAAEC,EAAC,GAAEU,KAAEH,GAAE,OAAK,EAAEA,GAAE,IAAI,EAAE,KAAI,IAAG,IAAGiB,KAAE,IAAGb,KAAE;AAAG,SAAOD,OAAIc,MAAGpB,KAAEM,GAAE,MAAM,QAAQ,GAAG,CAAC,GAAEC,KAAEP,GAAE,MAAM,CAAC,EAAE,KAAK,EAAE,IAAG,OAAKI,KAAEP,GAAE,aAAWA,GAAE,UAAUM,GAAE,SAAQiB,IAAEb,EAAC,KAAG,EAAEJ,GAAE,OAAO,GAAG,QAAQ,MAAM,IAAEC,KAAE,OAAKE,MAAGD,KAAEF,GAAE,UAAU,OAAO,GAAEF,KAAEE,GAAE,QAAMA,GAAE,MAAM,MAAK,IAAG,CAAA,GAAGE,KAAE,IAAEJ,GAAE,KAAK,CAAC,SAAQJ,GAAE,aAAWuB,EAAC,CAAC,KAAGnB,GAAEI,EAAC,IAAEJ,GAAEI,EAAC,EAAE,MAAK,GAAGJ,GAAEI,EAAC,EAAE,CAAC,KAAG,MAAIR,GAAE,aAAWuB,KAAGlB,KAAE,EAAC,OAAMD,GAAC,GAAE,eAAaF,GAAE,YAAYG,EAAC,IAAE,MAAIE,KAAE,qBAAmB,eAAaL,GAAE,YAAYI,EAAC,IAAE,MAAIC,KAAE;AAAiB,GAAE,EAAE,QAAM,SAAST,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,MAAIK,KAAET,GAAEC,EAAC;AAAE,SAAOQ,GAAE,MAAMA,GAAE,UAAU,KAAK,CAAC,EAAE,CAAC,IAAEL,GAAE,mBAAmBK,GAAE,UAASP,IAAEC,EAAC,GAAEC,GAAE,YAAYJ,IAAEC,IAAEC,EAAC;AAAC,GAAE,EAAE,YAAU,SAASF,IAAEC,IAAEC,IAAE;AAAC,SAAOA,GAAE,WAAS,aAAW;AAAQ,GAAE,EAAE,YAAU,SAASF,IAAEC,IAAEC,IAAE;AAAC,SAAOA,GAAE,SAAOA,GAAE,WAAS,aAAW,WAAS;AAAI,GAAE,EAAE,OAAK,SAASF,IAAEC,IAAE;AAAC,SAAO,EAAED,GAAEC,EAAC,EAAE,OAAO;AAAC,GAAE,EAAE,aAAW,SAASD,IAAEC,IAAE;AAAC,SAAOD,GAAEC,EAAC,EAAE;AAAO,GAAE,EAAE,cAAY,SAASD,IAAEC,IAAE;AAAC,SAAOD,GAAEC,EAAC,EAAE;AAAO,GAAE,EAAE,UAAU,cAAY,SAASD,IAAE;AAAC,MAAIC,IAAEC,IAAEC;AAAE,MAAG,CAACH,GAAE;AAAM,WAAM;AAAG,OAAIG,KAAE,IAAGF,KAAE,GAAEC,KAAEF,GAAE,MAAM,QAAOC,KAAEC,IAAED;AAAI,IAAAE,MAAG,MAAI,EAAEH,GAAE,MAAMC,EAAC,EAAE,CAAC,CAAC,IAAE,OAAK,EAAED,GAAE,MAAMC,EAAC,EAAE,CAAC,CAAC,IAAE;AAAI,SAAOE;AAAC,GAAE,EAAE,UAAU,cAAY,SAASH,IAAEC,IAAEC,IAAE;AAAC,MAAIC,IAAEC,KAAE,IAAGK,KAAE,OAAGC,KAAEV,GAAEC,EAAC;AAAE,SAAOS,GAAE,SAAO,MAAIA,GAAE,SAAO,OAAKA,GAAE,WAAST,MAAGD,GAAEC,KAAE,CAAC,EAAE,WAASG,MAAG,OAAMA,OAAI,OAAKM,GAAE,UAAQ,OAAK,OAAKA,GAAE,KAAIN,MAAG,KAAK,YAAYM,EAAC,GAAE,MAAIA,GAAE,WAASR,GAAE,aAAWE,MAAG,OAAMM,GAAE,UAAQD,KAAE,MAAG,MAAIC,GAAE,WAAST,KAAE,IAAED,GAAE,WAAS,cAAYG,KAAEH,GAAEC,KAAE,CAAC,GAAG,QAAME,GAAE,UAAQ,OAAKA,GAAE,WAASA,GAAE,QAAMO,GAAE,SAAOD,KAAE,SAAKL,MAAGK,KAAE,QAAM;AAAI,GAAE,EAAE,UAAU,eAAa,SAAST,IAAEC,IAAEC,IAAE;AAAC,WAAQC,IAAEC,KAAE,IAAGK,KAAE,KAAK,OAAMC,KAAE,GAAEL,KAAEL,GAAE,QAAOU,KAAEL,IAAEK;AAAI,eAASD,GAAEN,KAAEH,GAAEU,EAAC,EAAE,IAAI,IAAEN,MAAGK,GAAEN,EAAC,EAAEH,IAAEU,IAAET,IAAEC,IAAE,IAAI,IAAEE,MAAG,KAAK,YAAYJ,IAAEU,IAAET,EAAC;AAAE,SAAOG;AAAC,GAAE,EAAE,UAAU,qBAAmB,SAASJ,IAAEC,IAAEC,IAAE;AAAC,WAAQC,KAAE,IAAGC,KAAE,GAAEK,KAAET,GAAE,QAAOI,KAAEK,IAAEL;AAAI,eAASJ,GAAEI,EAAC,EAAE,OAAKD,MAAGH,GAAEI,EAAC,EAAE,UAAQ,YAAUJ,GAAEI,EAAC,EAAE,OAAKD,MAAG,KAAK,mBAAmBH,GAAEI,EAAC,EAAE,UAASH,IAAEC,EAAC,IAAE,gBAAcF,GAAEI,EAAC,EAAE,SAAOD,MAAG;AAAM,SAAOA;AAAC,GAAE,EAAE,UAAU,SAAO,SAASH,IAAEC,IAAEC,IAAE;AAAC,MAAIC,IAAEC,IAAEK,IAAEC,KAAE,IAAGL,KAAE,KAAK;AAAM,OAAIF,KAAE,GAAEC,KAAEJ,GAAE,QAAOG,KAAEC,IAAED;AAAI,kBAAYM,KAAET,GAAEG,EAAC,EAAE,QAAMO,MAAG,KAAK,aAAaV,GAAEG,EAAC,EAAE,UAASF,IAAEC,EAAC,IAAE,WAASG,GAAEI,EAAC,IAAEC,MAAGL,GAAEL,GAAEG,EAAC,EAAE,IAAI,EAAEH,IAAEG,IAAEF,IAAEC,IAAE,IAAI,IAAEQ,MAAG,KAAK,YAAYV,IAAEG,IAAEF,IAAEC,EAAC;AAAE,SAAOQ;AAAC;AAAE,IAAI,IAAE;AAAE,SAAS,IAAG;AAAC,OAAK,YAAU,CAAE,GAAC,KAAK,YAAU;AAAI;AAAC,EAAE,UAAU,WAAS,SAASV,IAAE;AAAC,WAAQC,KAAE,GAAEA,KAAE,KAAK,UAAU,QAAOA;AAAI,QAAG,KAAK,UAAUA,EAAC,EAAE,SAAOD;AAAE,aAAOC;AAAE,SAAM;AAAE,GAAE,EAAE,UAAU,cAAY,WAAU;AAAC,MAAID,KAAE,MAAKC,KAAE,CAAC,EAAE;AAAE,EAAAD,GAAE,UAAU,QAAS,SAASA,IAAE;AAAC,IAAAA,GAAE,WAASA,GAAE,IAAI,QAAS,SAASA,IAAE;AAAC,MAAAC,GAAE,QAAQD,EAAC,IAAE,KAAGC,GAAE,KAAKD,EAAC;AAAA,IAAC,CAAG;AAAA,EAAA,CAAG,GAACA,GAAE,YAAU,CAAE,GAACC,GAAE,QAAS,SAASA,IAAE;AAAC,IAAAD,GAAE,UAAUC,EAAC,IAAE,CAAE,GAACD,GAAE,UAAU,QAAS,SAASE,IAAE;AAAC,MAAAA,GAAE,YAAUD,MAAGC,GAAE,IAAI,QAAQD,EAAC,IAAE,KAAGD,GAAE,UAAUC,EAAC,EAAE,KAAKC,GAAE,EAAE;AAAA,IAAE,CAAC;AAAA,EAAE,CAAC;AAAE,GAAE,EAAE,UAAU,KAAG,SAASF,IAAEC,IAAEC,IAAE;AAAC,MAAIC,KAAE,KAAK,SAASH,EAAC,GAAEI,KAAEF,MAAG;AAAG,MAAG,OAAKC;AAAE,UAAM,IAAI,MAAM,4BAA0BH,EAAC;AAAE,OAAK,UAAUG,EAAC,EAAE,KAAGF,IAAE,KAAK,UAAUE,EAAC,EAAE,MAAIC,GAAE,OAAK,CAAE,GAAC,KAAK,YAAU;AAAI,GAAE,EAAE,UAAU,SAAO,SAASJ,IAAEC,IAAEC,IAAEC,IAAE;AAAC,MAAIC,KAAE,KAAK,SAASJ,EAAC,GAAES,KAAEN,MAAG,CAAA;AAAG,MAAG,OAAKC;AAAE,UAAM,IAAI,MAAM,4BAA0BJ,EAAC;AAAE,OAAK,UAAU,OAAOI,IAAE,GAAE,EAAC,MAAKH,IAAE,SAAQ,MAAG,IAAGC,IAAE,KAAIO,GAAE,OAAK,CAAE,EAAA,CAAC,GAAE,KAAK,YAAU;AAAI,GAAE,EAAE,UAAU,QAAM,SAAST,IAAEC,IAAEC,IAAEC,IAAE;AAAC,MAAIC,KAAE,KAAK,SAASJ,EAAC,GAAES,KAAEN,MAAG,CAAA;AAAG,MAAG,OAAKC;AAAE,UAAM,IAAI,MAAM,4BAA0BJ,EAAC;AAAE,OAAK,UAAU,OAAOI,KAAE,GAAE,GAAE,EAAC,MAAKH,IAAE,SAAQ,MAAG,IAAGC,IAAE,KAAIO,GAAE,OAAK,CAAA,EAAE,CAAC,GAAE,KAAK,YAAU;AAAI,GAAE,EAAE,UAAU,OAAK,SAAST,IAAEC,IAAEC,IAAE;AAAC,MAAIC,KAAED,MAAG,CAAA;AAAG,OAAK,UAAU,KAAK,EAAC,MAAKF,IAAE,SAAQ,MAAG,IAAGC,IAAE,KAAIE,GAAE,OAAK,CAAE,EAAA,CAAC,GAAE,KAAK,YAAU;AAAI,GAAE,EAAE,UAAU,SAAO,SAASH,IAAEC,IAAE;AAAC,QAAM,QAAQD,EAAC,MAAIA,KAAE,CAACA,EAAC;AAAG,MAAIE,KAAE,CAAE;AAAC,SAAOF,GAAE,QAAS,SAASA,IAAE;AAAC,QAAIG,KAAE,KAAK,SAASH,EAAC;AAAE,QAAGG,KAAE,GAAE;AAAC,UAAGF;AAAE;AAAO,YAAM,IAAI,MAAM,sCAAoCD,EAAC;AAAA,IAAC;AAAC,SAAK,UAAUG,EAAC,EAAE,UAAQ,MAAGD,GAAE,KAAKF,EAAC;AAAA,EAAC,GAAG,IAAI,GAAE,KAAK,YAAU,MAAKE;AAAC,GAAE,EAAE,UAAU,aAAW,SAASF,IAAEC,IAAE;AAAC,QAAM,QAAQD,EAAC,MAAIA,KAAE,CAACA,EAAC,IAAG,KAAK,UAAU,QAAS,SAASA,IAAE;AAAC,IAAAA,GAAE,UAAQ;AAAA,EAAE,CAAG,GAAC,KAAK,OAAOA,IAAEC,EAAC;AAAC,GAAE,EAAE,UAAU,UAAQ,SAASD,IAAEC,IAAE;AAAC,QAAM,QAAQD,EAAC,MAAIA,KAAE,CAACA,EAAC;AAAG,MAAIE,KAAE,CAAA;AAAG,SAAOF,GAAE,QAAS,SAASA,IAAE;AAAC,QAAIG,KAAE,KAAK,SAASH,EAAC;AAAE,QAAGG,KAAE,GAAE;AAAC,UAAGF;AAAE;AAAO,YAAM,IAAI,MAAM,sCAAoCD,EAAC;AAAA,IAAC;AAAC,SAAK,UAAUG,EAAC,EAAE,UAAQ,OAAGD,GAAE,KAAKF,EAAC;AAAA,EAAC,GAAG,IAAI,GAAE,KAAK,YAAU,MAAKE;AAAC,GAAE,EAAE,UAAU,WAAS,SAASF,IAAE;AAAC,SAAO,SAAO,KAAK,aAAW,KAAK,YAAW,GAAG,KAAK,UAAUA,EAAC,KAAG,CAAA;AAAE;AAAE,IAAI,IAAE,GAAE,IAAE,aAAY,IAAE,OAAM,IAAE,EAAE;AAAe,SAAS,EAAEA,IAAE;AAAC,SAAM,aAAa,KAAKA,EAAC;AAAC;AAAC,IAAI,IAAE,gCAA+B,IAAE,iBAAgB,IAAE,kBAAiB,IAAE,EAAC,GAAE,KAAI,GAAE,KAAI,IAAG,IAAG;AAAE,SAAS,EAAEA,IAAEC,IAAE;AAAC,SAAO,EAAEA,GAAE,aAAa;AAAC;AAAC,SAAS,EAAED,IAAE;AAAC,MAAIC,IAAEC,IAAEC,KAAE;AAAE,OAAIF,KAAED,GAAE,SAAO,GAAEC,MAAG,GAAEA;AAAI,gBAAUC,KAAEF,GAAEC,EAAC,GAAG,QAAME,OAAID,GAAE,UAAQA,GAAE,QAAQ,QAAQ,GAAE,CAAC,IAAG,gBAAcA,GAAE,QAAM,WAASA,GAAE,QAAMC,MAAI,iBAAeD,GAAE,QAAM,WAASA,GAAE,QAAMC;AAAG;AAAC,SAAS,EAAEH,IAAE;AAAC,MAAIC,IAAEC,IAAEC,KAAE;AAAE,OAAIF,KAAED,GAAE,SAAO,GAAEC,MAAG,GAAEA;AAAI,gBAAUC,KAAEF,GAAEC,EAAC,GAAG,QAAME,MAAG,EAAE,KAAKD,GAAE,OAAO,MAAIA,GAAE,UAAQA,GAAE,QAAQ,QAAQ,QAAO,GAAG,EAAE,QAAQ,WAAU,GAAG,EAAE,QAAQ,YAAW,MAAM,EAAE,QAAQ,eAAc,QAAQ,EAAE,QAAQ,UAAS,GAAG,EAAE,QAAQ,2BAA0B,KAAK,EAAE,QAAQ,sBAAqB,KAAK,EAAE,QAAQ,8BAA6B,KAAK,IAAG,gBAAcA,GAAE,QAAM,WAASA,GAAE,QAAMC,MAAI,iBAAeD,GAAE,QAAM,WAASA,GAAE,QAAMC;AAAG;AAAC,IAAI,IAAE,EAAE,cAAa,IAAE,EAAE,aAAY,IAAE,EAAE,gBAAe,IAAE,QAAO,KAAG;AAAQ,SAAS,GAAGH,IAAEC,IAAEC,IAAE;AAAC,SAAOF,GAAE,MAAM,GAAEC,EAAC,IAAEC,KAAEF,GAAE,MAAMC,KAAE,CAAC;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAE;AAAC,MAAIC,IAAEC,IAAEC,IAAEK,IAAEC,IAAEL,IAAEC,IAAEC,IAAEC,IAAEG,IAAEc,IAAEb,IAAEc,IAAEb,IAAEC,IAAEa,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC;AAAE,OAAIF,KAAE,CAAE,GAAC5B,KAAE,GAAEA,KAAEF,GAAE,QAAOE,MAAI;AAAC,SAAIC,KAAEH,GAAEE,EAAC,GAAEI,KAAEN,GAAEE,EAAC,EAAE,OAAM0B,KAAEE,GAAE,SAAO,GAAEF,MAAG,KAAG,EAAEE,GAAEF,EAAC,EAAE,SAAOtB,KAAGsB;AAAI;AAAC,QAAGE,GAAE,SAAOF,KAAE,GAAE,WAASzB,GAAE,MAAK;AAAC,MAAAO,KAAE,GAAEL,MAAGD,KAAED,GAAE,SAAS;AAAO;AAAE,eAAKO,KAAEL,OAAI,GAAG,YAAUK,IAAED,KAAE,GAAG,KAAKL,EAAC,MAAI;AAAC,cAAGU,KAAEa,KAAE,MAAGjB,KAAED,GAAE,QAAM,GAAEoB,KAAE,QAAMpB,GAAE,CAAC,GAAED,KAAE,IAAGC,GAAE,QAAM,KAAG;AAAE,YAAAD,KAAEJ,GAAE,WAAWK,GAAE,QAAM,CAAC;AAAA;AAAO,iBAAImB,KAAE1B,KAAE,GAAE0B,MAAG,MAAI,gBAAc5B,GAAE4B,EAAC,EAAE,QAAM,gBAAc5B,GAAE4B,EAAC,EAAE,OAAMA;AAAI,kBAAG5B,GAAE4B,EAAC,EAAE,SAAQ;AAAC,gBAAApB,KAAER,GAAE4B,EAAC,EAAE,QAAQ,WAAW5B,GAAE4B,EAAC,EAAE,QAAQ,SAAO,CAAC;AAAE;AAAA,cAAK;AAAC,cAAGjB,KAAE,IAAGD,KAAEL;AAAE,YAAAM,KAAEP,GAAE,WAAWM,EAAC;AAAA;AAAO,iBAAIkB,KAAE1B,KAAE,GAAE0B,KAAE5B,GAAE,WAAS,gBAAcA,GAAE4B,EAAC,EAAE,QAAM,gBAAc5B,GAAE4B,EAAC,EAAE,OAAMA;AAAI,kBAAG5B,GAAE4B,EAAC,EAAE,SAAQ;AAAC,gBAAAjB,KAAEX,GAAE4B,EAAC,EAAE,QAAQ,WAAW,CAAC;AAAE;AAAA,cAAK;AAAC,cAAGH,KAAE,EAAEjB,EAAC,KAAG,EAAE,OAAO,aAAaA,EAAC,CAAC,GAAEI,KAAE,EAAED,EAAC,KAAG,EAAE,OAAO,aAAaA,EAAC,CAAC,GAAEe,KAAE,EAAElB,EAAC,IAAGK,KAAE,EAAEF,EAAC,KAAGG,KAAE,QAAGF,OAAIc,MAAGD,OAAIX,KAAE,SAAKY,KAAEC,KAAE,QAAGF,OAAIZ,MAAGD,OAAIe,KAAE,SAAK,OAAKhB,MAAG,QAAMF,GAAE,CAAC,KAAGD,MAAG,MAAIA,MAAG,OAAKmB,KAAEb,KAAE,QAAIA,MAAGa,OAAIb,KAAEW,IAAEE,KAAEf,KAAGE,MAAGa,IAAE;AAAC,gBAAGA;AAAE,mBAAIC,KAAEE,GAAE,SAAO,GAAEF,MAAG,MAAIrB,KAAEuB,GAAEF,EAAC,GAAE,EAAEE,GAAEF,EAAC,EAAE,QAAMtB,MAAIsB;AAAI,oBAAGrB,GAAE,WAASsB,MAAGC,GAAEF,EAAC,EAAE,UAAQtB,IAAE;AAAC,kBAAAC,KAAEuB,GAAEF,EAAC,GAAEC,MAAGE,KAAE9B,GAAE,GAAG,QAAQ,OAAO,CAAC,GAAE+B,KAAE/B,GAAE,GAAG,QAAQ,OAAO,CAAC,MAAI8B,KAAE9B,GAAE,GAAG,QAAQ,OAAO,CAAC,GAAE+B,KAAE/B,GAAE,GAAG,QAAQ,OAAO,CAAC,IAAGE,GAAE,UAAQ,GAAGA,GAAE,SAAQM,GAAE,OAAMuB,EAAC,GAAEhC,GAAEO,GAAE,KAAK,EAAE,UAAQ,GAAGP,GAAEO,GAAE,KAAK,EAAE,SAAQA,GAAE,KAAIwB,EAAC,GAAErB,MAAGsB,GAAE,SAAO,GAAEzB,GAAE,UAAQL,OAAIQ,MAAGqB,GAAE,SAAO,IAAG1B,MAAGD,KAAED,GAAE,SAAS,QAAO2B,GAAE,SAAOF;AAAE,2BAAS;AAAA,gBAAC;AAAA;AAAC,YAAAd,KAAEgB,GAAE,KAAK,EAAC,OAAM5B,IAAE,KAAIO,GAAE,OAAM,QAAOoB,IAAE,OAAMvB,GAAC,CAAC,IAAEqB,MAAGE,OAAI1B,GAAE,UAAQ,GAAGA,GAAE,SAAQM,GAAE,OAAM,GAAG;AAAA,UAAE;AAAM,YAAAoB,OAAI1B,GAAE,UAAQ,GAAGA,GAAE,SAAQM,GAAE,OAAM,GAAG;AAAA,QAAE;AAAA,IAAC;AAAA,EAAC;AAAC;AAAC,SAAS,GAAGT,IAAEC,IAAEC,IAAE;AAAC,OAAK,OAAKF,IAAE,KAAK,MAAIC,IAAE,KAAK,QAAM,MAAK,KAAK,MAAI,MAAK,KAAK,UAAQC,IAAE,KAAK,QAAM,GAAE,KAAK,WAAS,MAAK,KAAK,UAAQ,IAAG,KAAK,SAAO,IAAG,KAAK,OAAK,IAAG,KAAK,OAAK,MAAK,KAAK,QAAM,OAAG,KAAK,SAAO;AAAE;AAAC,GAAG,UAAU,YAAU,SAASF,IAAE;AAAC,MAAIC,IAAEC,IAAEC;AAAE,MAAG,CAAC,KAAK;AAAM,WAAM;AAAG,OAAID,KAAE,GAAEC,MAAGF,KAAE,KAAK,OAAO,QAAOC,KAAEC,IAAED;AAAI,QAAGD,GAAEC,EAAC,EAAE,CAAC,MAAIF;AAAE,aAAOE;AAAE,SAAM;AAAE,GAAE,GAAG,UAAU,WAAS,SAASF,IAAE;AAAC,OAAK,QAAM,KAAK,MAAM,KAAKA,EAAC,IAAE,KAAK,QAAM,CAACA,EAAC;AAAC,GAAE,GAAG,UAAU,UAAQ,SAASA,IAAEC,IAAE;AAAC,MAAIC,KAAE,KAAK,UAAUF,EAAC,GAAEG,KAAE,CAACH,IAAEC,EAAC;AAAE,EAAAC,KAAE,IAAE,KAAK,SAASC,EAAC,IAAE,KAAK,MAAMD,EAAC,IAAEC;AAAC,GAAE,GAAG,UAAU,UAAQ,SAASH,IAAE;AAAC,MAAIC,KAAE,KAAK,UAAUD,EAAC,GAAEE,KAAE;AAAK,SAAOD,MAAG,MAAIC,KAAE,KAAK,MAAMD,EAAC,EAAE,CAAC,IAAGC;AAAC,GAAE,GAAG,UAAU,WAAS,SAASF,IAAEC,IAAE;AAAC,MAAIC,KAAE,KAAK,UAAUF,EAAC;AAAE,EAAAE,KAAE,IAAE,KAAK,SAAS,CAACF,IAAEC,EAAC,CAAC,IAAE,KAAK,MAAMC,EAAC,EAAE,CAAC,IAAE,KAAK,MAAMA,EAAC,EAAE,CAAC,IAAE,MAAID;AAAC;AAAE,IAAI,KAAG,IAAG,KAAG;AAAG,SAAS,GAAGD,IAAEC,IAAEC,IAAE;AAAC,OAAK,MAAIF,IAAE,KAAK,MAAIE,IAAE,KAAK,SAAO,CAAA,GAAG,KAAK,aAAW,OAAG,KAAK,KAAGD;AAAC;AAAC,GAAG,UAAU,QAAM;AAAG,IAAI,KAAG,IAAG,KAAG,GAAE,KAAG,CAAC,CAAC,aAAY,SAASD,IAAE;AAAC,MAAIC;AAAE,EAAAA,MAAGA,KAAED,GAAE,IAAI,QAAQ,GAAE,IAAI,GAAG,QAAQ,GAAE,GAAG,GAAEA,GAAE,MAAIC;AAAC,CAAC,GAAE,CAAC,SAAQ,SAASD,IAAE;AAAC,MAAIC;AAAE,EAAAD,GAAE,eAAaC,KAAE,IAAID,GAAE,MAAM,UAAS,IAAG,CAAC,GAAG,UAAQA,GAAE,KAAIC,GAAE,MAAI,CAAC,GAAE,CAAC,GAAEA,GAAE,WAAS,CAAE,GAACD,GAAE,OAAO,KAAKC,EAAC,KAAGD,GAAE,GAAG,MAAM,MAAMA,GAAE,KAAIA,GAAE,IAAGA,GAAE,KAAIA,GAAE,MAAM;AAAC,CAAC,GAAE,CAAC,UAAS,SAASA,IAAE;AAAC,MAAIC,IAAEC,IAAEC,IAAEC,KAAEJ,GAAE;AAAO,OAAIE,KAAE,GAAEC,KAAEC,GAAE,QAAOF,KAAEC,IAAED;AAAI,kBAAYD,KAAEG,GAAEF,EAAC,GAAG,QAAMF,GAAE,GAAG,OAAO,MAAMC,GAAE,SAAQD,GAAE,IAAGA,GAAE,KAAIC,GAAE,QAAQ;AAAC,CAAC,GAAE,CAAC,WAAU,SAASD,IAAE;AAAC,MAAIC,IAAEC,IAAEC,IAAEC,IAAEK,IAAEC,IAAEL,IAAEC,IAAEC,IAAEC,IAAEG,IAAEc,IAAEb,IAAEc,IAAEb,IAAEC,IAAEa,IAAEC,IAAEC,KAAE7B,GAAE;AAAO,MAAGA,GAAE,GAAG,QAAQ;AAAQ,SAAIE,KAAE,GAAEC,KAAE0B,GAAE,QAAO3B,KAAEC,IAAED;AAAI,UAAG,aAAW2B,GAAE3B,EAAC,EAAE,QAAMF,GAAE,GAAG,QAAQ,QAAQ6B,GAAE3B,EAAC,EAAE,OAAO;AAAE,aAAIU,KAAE,GAAEX,MAAGG,KAAEyB,GAAE3B,EAAC,EAAE,UAAU,SAAO,GAAED,MAAG,GAAEA;AAAI,cAAG,kBAAgBS,KAAEN,GAAEH,EAAC,GAAG,MAAK;AAAC,gBAAG,kBAAgBS,GAAE,SAAOkB,KAAElB,GAAE,SAAQ,YAAY,KAAKkB,EAAC,KAAGhB,KAAE,KAAGA,MAAI,EAAEF,GAAE,OAAO,KAAGE,OAAK,EAAEA,KAAE,MAAI,WAASF,GAAE,QAAMV,GAAE,GAAG,QAAQ,KAAKU,GAAE,OAAO,GAAE;AAAC,mBAAIH,KAAEG,GAAE,SAAQiB,KAAE3B,GAAE,GAAG,QAAQ,MAAMO,EAAC,GAAEF,KAAE,CAAE,GAACoB,KAAEf,GAAE,OAAMC,KAAE,GAAEgB,GAAE,SAAO,KAAG,MAAIA,GAAE,CAAC,EAAE,SAAO1B,KAAE,KAAG,mBAAiBG,GAAEH,KAAE,CAAC,EAAE,SAAO0B,KAAEA,GAAE,MAAM,CAAC,IAAGrB,KAAE,GAAEA,KAAEqB,GAAE,QAAOrB;AAAI,gBAAAoB,KAAEC,GAAErB,EAAC,EAAE,KAAIO,KAAEb,GAAE,GAAG,cAAc0B,EAAC,GAAE1B,GAAE,GAAG,aAAaa,EAAC,MAAIC,KAAEa,GAAErB,EAAC,EAAE,MAAKQ,KAAEa,GAAErB,EAAC,EAAE,SAAO,cAAYqB,GAAErB,EAAC,EAAE,UAAQ,YAAY,KAAKQ,EAAC,IAAEd,GAAE,GAAG,kBAAkBc,EAAC,IAAEd,GAAE,GAAG,kBAAkB,YAAUc,EAAC,EAAE,QAAQ,YAAW,EAAE,IAAEd,GAAE,GAAG,kBAAkB,YAAUc,EAAC,EAAE,QAAQ,cAAa,EAAE,IAAGN,KAAEmB,GAAErB,EAAC,EAAE,SAAOK,QAAKF,KAAE,IAAIT,GAAE,MAAM,QAAO,IAAG,CAAC,GAAG,UAAQO,GAAE,MAAMI,IAAEH,EAAC,GAAEC,GAAE,QAAMgB,IAAEpB,GAAE,KAAKI,EAAC,KAAIA,KAAE,IAAIT,GAAE,MAAM,aAAY,KAAI,CAAC,GAAG,QAAM,CAAC,CAAC,QAAOa,EAAC,CAAC,GAAEJ,GAAE,QAAMgB,MAAIhB,GAAE,SAAO,WAAUA,GAAE,OAAK,QAAOJ,GAAE,KAAKI,EAAC,IAAGA,KAAE,IAAIT,GAAE,MAAM,QAAO,IAAG,CAAC,GAAG,UAAQc,IAAEL,GAAE,QAAMgB,IAAEpB,GAAE,KAAKI,EAAC,IAAGA,KAAE,IAAIT,GAAE,MAAM,cAAa,KAAI,EAAE,GAAG,QAAM,EAAEyB,IAAEhB,GAAE,SAAO,WAAUA,GAAE,OAAK,QAAOJ,GAAE,KAAKI,EAAC,GAAEE,KAAEgB,GAAErB,EAAC,EAAE;AAAW,cAAAK,KAAEJ,GAAE,YAAUE,KAAE,IAAIT,GAAE,MAAM,QAAO,IAAG,CAAC,GAAG,UAAQO,GAAE,MAAMI,EAAC,GAAEF,GAAE,QAAMgB,IAAEpB,GAAE,KAAKI,EAAC,IAAGoB,GAAE3B,EAAC,EAAE,WAASE,KAAE,EAAEA,IAAEH,IAAEI,EAAC;AAAA,YAAC;AAAA,UAAC;AAAM,iBAAIJ,MAAIG,GAAEH,EAAC,EAAE,UAAQS,GAAE,SAAO,gBAAcN,GAAEH,EAAC,EAAE;AAAM,cAAAA;AAAA;AAAG,CAAC,GAAE,CAAC,gBAAe,SAASD,IAAE;AAAC,MAAIC;AAAE,MAAGD,GAAE,GAAG,QAAQ;AAAY,SAAIC,KAAED,GAAE,OAAO,SAAO,GAAEC,MAAG,GAAEA;AAAI,mBAAWD,GAAE,OAAOC,EAAC,EAAE,SAAO,EAAE,KAAKD,GAAE,OAAOC,EAAC,EAAE,OAAO,KAAG,EAAED,GAAE,OAAOC,EAAC,EAAE,QAAQ,GAAE,EAAE,KAAKD,GAAE,OAAOC,EAAC,EAAE,OAAO,KAAG,EAAED,GAAE,OAAOC,EAAC,EAAE,QAAQ;AAAE,CAAC,GAAE,CAAC,eAAc,SAASD,IAAE;AAAC,MAAIC;AAAE,MAAGD,GAAE,GAAG,QAAQ;AAAY,SAAIC,KAAED,GAAE,OAAO,SAAO,GAAEC,MAAG,GAAEA;AAAI,mBAAWD,GAAE,OAAOC,EAAC,EAAE,QAAM,EAAE,KAAKD,GAAE,OAAOC,EAAC,EAAE,OAAO,KAAG,GAAGD,GAAE,OAAOC,EAAC,EAAE,UAASD,EAAC;AAAC,CAAC,GAAE,CAAC,aAAY,SAASA,IAAE;AAAC,MAAIC,IAAEC,IAAEC,IAAEC,IAAEK,IAAEC,IAAEL,KAAEL,GAAE;AAAO,OAAIC,KAAE,GAAEC,KAAEG,GAAE,QAAOJ,KAAEC,IAAED;AAAI,QAAG,aAAWI,GAAEJ,EAAC,EAAE,MAAK;AAAC,WAAIQ,MAAGN,KAAEE,GAAEJ,EAAC,EAAE,UAAU,QAAOG,KAAE,GAAEA,KAAEK,IAAEL;AAAI,2BAAiBD,GAAEC,EAAC,EAAE,SAAOD,GAAEC,EAAC,EAAE,OAAK;AAAQ,WAAIA,KAAEM,KAAE,GAAEN,KAAEK,IAAEL;AAAI,mBAASD,GAAEC,EAAC,EAAE,QAAMA,KAAE,IAAEK,MAAG,WAASN,GAAEC,KAAE,CAAC,EAAE,OAAKD,GAAEC,KAAE,CAAC,EAAE,UAAQD,GAAEC,EAAC,EAAE,UAAQD,GAAEC,KAAE,CAAC,EAAE,WAASA,OAAIM,OAAIP,GAAEO,EAAC,IAAEP,GAAEC,EAAC,IAAGM;AAAK,MAAAN,OAAIM,OAAIP,GAAE,SAAOO;AAAA,IAAE;AAAC,CAAC,CAAC;AAAE,SAAS,KAAI;AAAC,OAAK,QAAM,IAAI;AAAG,WAAQV,KAAE,GAAEA,KAAE,GAAG,QAAOA;AAAI,SAAK,MAAM,KAAK,GAAGA,EAAC,EAAE,CAAC,GAAE,GAAGA,EAAC,EAAE,CAAC,CAAC;AAAC;AAAC,GAAG,UAAU,UAAQ,SAASA,IAAE;AAAC,MAAIC,IAAEC,IAAEC;AAAE,OAAIF,KAAE,GAAEC,MAAGC,KAAE,KAAK,MAAM,SAAS,EAAE,GAAG,QAAOF,KAAEC,IAAED;AAAI,IAAAE,GAAEF,EAAC,EAAED,EAAC;AAAC,GAAE,GAAG,UAAU,QAAM;AAAG,IAAI,KAAG,IAAG,KAAG,EAAE;AAAQ,SAAS,GAAGA,IAAEC,IAAE;AAAC,MAAIC,KAAEF,GAAE,OAAOC,EAAC,IAAED,GAAE,OAAOC,EAAC,GAAEE,KAAEH,GAAE,OAAOC,EAAC;AAAE,SAAOD,GAAE,IAAI,MAAME,IAAEC,EAAC;AAAC;AAAC,SAAS,GAAGH,IAAE;AAAC,MAAIC,IAAEC,KAAE,CAAE,GAACC,KAAE,GAAEC,KAAEJ,GAAE,QAAOS,KAAE,OAAGC,KAAE,GAAEL,KAAE;AAAG,OAAIJ,KAAED,GAAE,WAAWG,EAAC,GAAEA,KAAEC;AAAG,YAAMH,OAAIQ,MAAGJ,MAAGL,GAAE,UAAUU,IAAEP,KAAE,CAAC,GAAEO,KAAEP,OAAID,GAAE,KAAKG,KAAEL,GAAE,UAAUU,IAAEP,EAAC,CAAC,GAAEE,KAAE,IAAGK,KAAEP,KAAE,KAAIM,KAAE,OAAKR,IAAEE,MAAIF,KAAED,GAAE,WAAWG,EAAC;AAAE,SAAOD,GAAE,KAAKG,KAAEL,GAAE,UAAUU,EAAC,CAAC,GAAER;AAAC;AAAC,IAAI,KAAG,EAAE,SAAQ,KAAG,EAAE,SAAQ,KAAG,EAAE;AAAQ,SAAS,GAAGF,IAAEC,IAAE;AAAC,MAAIC,IAAEC,IAAEC,IAAEK;AAAE,SAAON,KAAEH,GAAE,OAAOC,EAAC,IAAED,GAAE,OAAOC,EAAC,GAAEG,KAAEJ,GAAE,OAAOC,EAAC,GAAE,QAAMC,KAAEF,GAAE,IAAI,WAAWG,IAAG,MAAI,OAAKD,MAAG,OAAKA,MAAGC,KAAEC,OAAIK,KAAET,GAAE,IAAI,WAAWG,EAAC,GAAE,CAAC,GAAGM,EAAC,KAAG,KAAGN;AAAC;AAAC,SAAS,GAAGH,IAAEC,IAAE;AAAC,MAAIC,IAAEC,KAAEH,GAAE,OAAOC,EAAC,IAAED,GAAE,OAAOC,EAAC,GAAEG,KAAED,IAAEM,KAAET,GAAE,OAAOC,EAAC;AAAE,MAAGG,KAAE,KAAGK;AAAE,WAAM;AAAG,OAAIP,KAAEF,GAAE,IAAI,WAAWI,IAAG,KAAG,MAAIF,KAAE;AAAG,WAAM;AAAG,aAAO;AAAC,QAAGE,MAAGK;AAAE,aAAM;AAAG,QAAG,GAAGP,KAAEF,GAAE,IAAI,WAAWI,IAAG,MAAI,MAAIF,MAAG,KAAI;AAAC,UAAG,OAAKA,MAAG,OAAKA;AAAE;AAAM,aAAM;AAAA,IAAE;AAAC,QAAGE,KAAED,MAAG;AAAG,aAAM;AAAA,EAAE;AAAC,SAAOC,KAAEK,OAAIP,KAAEF,GAAE,IAAI,WAAWI,EAAC,GAAE,CAAC,GAAGF,EAAC,KAAG,KAAGE;AAAC;AAAC,IAAI,KAAG,EAAE,oBAAmB,KAAG,EAAE,SAAQ,KAAG,CAAE,GAAC,KAAG,gIAAkI,KAAG,oCAAmC,KAAG,IAAI,OAAO,SAAO,KAAG,MAAI,KAAG,8GAA0H,GAAE,KAAG,IAAI,OAAO,SAAO,KAAG,MAAI,KAAG,GAAG;AAAE,GAAG,cAAY,IAAG,GAAG,yBAAuB;AAAG,IAAI,KAAG,CAAC,WAAU,WAAU,SAAQ,QAAO,YAAW,cAAa,QAAO,WAAU,UAAS,OAAM,YAAW,MAAK,WAAU,UAAS,OAAM,OAAM,MAAK,MAAK,YAAW,cAAa,UAAS,UAAS,QAAO,SAAQ,YAAW,MAAK,MAAK,MAAK,MAAK,MAAK,MAAK,QAAO,UAAS,MAAK,QAAO,UAAS,UAAS,MAAK,QAAO,QAAO,QAAO,YAAW,OAAM,YAAW,MAAK,YAAW,UAAS,KAAI,SAAQ,WAAU,UAAS,WAAU,SAAQ,SAAQ,MAAK,SAAQ,MAAK,SAAQ,SAAQ,MAAK,SAAQ,IAAI,GAAE,KAAG,GAAG,wBAAuB,KAAG,CAAC,CAAC,8CAA6C,oCAAmC,IAAE,GAAE,CAAC,SAAQ,OAAM,IAAE,GAAE,CAAC,QAAO,OAAM,IAAE,GAAE,CAAC,YAAW,KAAI,IAAE,GAAE,CAAC,gBAAe,SAAQ,IAAE,GAAE,CAAC,IAAI,OAAO,UAAQ,GAAG,KAAK,GAAG,IAAE,oBAAmB,GAAG,GAAE,MAAK,IAAE,GAAE,CAAC,IAAI,OAAO,GAAG,SAAO,OAAO,GAAE,MAAK,KAAE,CAAC,GAAE,KAAG,EAAE,SAAQ,KAAG,IAAG,KAAG,EAAE;AAAQ,SAAS,GAAGJ,IAAEC,IAAEC,IAAEC,IAAE;AAAC,MAAIC,IAAEK,IAAEC,IAAEL,IAAEC,IAAEC,IAAEC,IAAEG;AAAE,OAAI,KAAK,MAAIX,IAAE,KAAK,KAAGC,IAAE,KAAK,MAAIC,IAAE,KAAK,SAAOC,IAAE,KAAK,SAAO,CAAA,GAAG,KAAK,SAAO,CAAA,GAAG,KAAK,SAAO,CAAE,GAAC,KAAK,SAAO,CAAA,GAAG,KAAK,UAAQ,CAAA,GAAG,KAAK,YAAU,GAAE,KAAK,OAAK,GAAE,KAAK,UAAQ,GAAE,KAAK,QAAM,OAAG,KAAK,WAAS,IAAG,KAAK,aAAW,IAAG,KAAK,aAAW,QAAO,KAAK,QAAM,GAAE,KAAK,SAAO,IAAGQ,KAAE,OAAGD,KAAEL,KAAEE,KAAEC,KAAE,GAAEF,MAAGG,KAAE,KAAK,KAAK,QAAOJ,KAAEC,IAAED,MAAI;AAAC,QAAGD,KAAEK,GAAE,WAAWJ,EAAC,GAAE,CAACM,IAAE;AAAC,UAAG,GAAGP,EAAC,GAAE;AAAC,QAAAG,MAAI,MAAIH,KAAEI,MAAG,IAAEA,KAAE,IAAEA;AAAI;AAAA,MAAQ;AAAC,MAAAG,KAAE;AAAA,IAAE;AAAC,WAAKP,MAAGC,OAAIC,KAAE,MAAI,OAAKF,MAAGC,MAAI,KAAK,OAAO,KAAKK,EAAC,GAAE,KAAK,OAAO,KAAKL,EAAC,GAAE,KAAK,OAAO,KAAKE,EAAC,GAAE,KAAK,OAAO,KAAKC,EAAC,GAAE,KAAK,QAAQ,KAAK,CAAC,GAAEG,KAAE,OAAGJ,KAAE,GAAEC,KAAE,GAAEE,KAAEL,KAAE;AAAA,EAAE;AAAC,OAAK,OAAO,KAAKI,GAAE,MAAM,GAAE,KAAK,OAAO,KAAKA,GAAE,MAAM,GAAE,KAAK,OAAO,KAAK,CAAC,GAAE,KAAK,OAAO,KAAK,CAAC,GAAE,KAAK,QAAQ,KAAK,CAAC,GAAE,KAAK,UAAQ,KAAK,OAAO,SAAO;AAAC;AAAC,GAAG,UAAU,OAAK,SAAST,IAAEC,IAAEC,IAAE;AAAC,MAAIC,KAAE,IAAI,GAAGH,IAAEC,IAAEC,EAAC;AAAE,SAAOC,GAAE,QAAM,MAAGD,KAAE,KAAG,KAAK,SAAQC,GAAE,QAAM,KAAK,OAAMD,KAAE,KAAG,KAAK,SAAQ,KAAK,OAAO,KAAKC,EAAC,GAAEA;AAAC,GAAE,GAAG,UAAU,UAAQ,SAASH,IAAE;AAAC,SAAO,KAAK,OAAOA,EAAC,IAAE,KAAK,OAAOA,EAAC,KAAG,KAAK,OAAOA,EAAC;AAAC,GAAE,GAAG,UAAU,iBAAe,SAASA,IAAE;AAAC,WAAQC,KAAE,KAAK,SAAQD,KAAEC,MAAG,EAAE,KAAK,OAAOD,EAAC,IAAE,KAAK,OAAOA,EAAC,IAAE,KAAK,OAAOA,EAAC,IAAGA;AAAI;AAAC,SAAOA;AAAC,GAAE,GAAG,UAAU,aAAW,SAASA,IAAE;AAAC,WAAQC,IAAEC,KAAE,KAAK,IAAI,QAAOF,KAAEE,OAAID,KAAE,KAAK,IAAI,WAAWD,EAAC,GAAE,GAAGC,EAAC,IAAGD;AAAI;AAAC,SAAOA;AAAC,GAAE,GAAG,UAAU,iBAAe,SAASA,IAAEC,IAAE;AAAC,MAAGD,MAAGC;AAAE,WAAOD;AAAE,SAAKA,KAAEC;AAAG,QAAG,CAAC,GAAG,KAAK,IAAI,WAAW,EAAED,EAAC,CAAC;AAAE,aAAOA,KAAE;AAAE,SAAOA;AAAC,GAAE,GAAG,UAAU,YAAU,SAASA,IAAEC,IAAE;AAAC,WAAQC,KAAE,KAAK,IAAI,QAAOF,KAAEE,MAAG,KAAK,IAAI,WAAWF,EAAC,MAAIC,IAAED;AAAI;AAAC,SAAOA;AAAC,GAAE,GAAG,UAAU,gBAAc,SAASA,IAAEC,IAAEC,IAAE;AAAC,MAAGF,MAAGE;AAAE,WAAOF;AAAE,SAAKA,KAAEE;AAAG,QAAGD,OAAI,KAAK,IAAI,WAAW,EAAED,EAAC;AAAE,aAAOA,KAAE;AAAE,SAAOA;AAAC,GAAE,GAAG,UAAU,WAAS,SAASA,IAAEC,IAAEC,IAAEC,IAAE;AAAC,MAAIC,IAAEK,IAAEC,IAAEL,IAAEC,IAAEC,IAAEC,IAAEG,KAAEX;AAAE,MAAGA,MAAGC;AAAE,WAAM;AAAG,OAAIM,KAAE,IAAI,MAAMN,KAAED,EAAC,GAAEI,KAAE,GAAEO,KAAEV,IAAEU,MAAIP,MAAI;AAAC,SAAIK,KAAE,GAAED,KAAEH,KAAE,KAAK,OAAOM,EAAC,GAAEL,KAAEK,KAAE,IAAEV,MAAGE,KAAE,KAAK,OAAOQ,EAAC,IAAE,IAAE,KAAK,OAAOA,EAAC,GAAEN,KAAEC,MAAGG,KAAEP,MAAG;AAAC,UAAGQ,KAAE,KAAK,IAAI,WAAWL,EAAC,GAAE,GAAGK,EAAC;AAAE,cAAIA,KAAED,MAAG,KAAGA,KAAE,KAAK,QAAQE,EAAC,KAAG,IAAEF;AAAA,WAAQ;AAAC,YAAG,EAAEJ,KAAEG,KAAE,KAAK,OAAOG,EAAC;AAAG;AAAM,QAAAF;AAAA,MAAG;AAAC,MAAAJ;AAAA,IAAG;AAAC,IAAAE,GAAEH,EAAC,IAAEK,KAAEP,KAAE,IAAI,MAAMO,KAAEP,KAAE,CAAC,EAAE,KAAK,GAAG,IAAE,KAAK,IAAI,MAAMG,IAAEC,EAAC,IAAE,KAAK,IAAI,MAAMD,IAAEC,EAAC;AAAA,EAAC;AAAC,SAAOC,GAAE,KAAK,EAAE;AAAC,GAAE,GAAG,UAAU,QAAM;AAAG,IAAI,KAAG,IAAG,KAAG,GAAE,KAAG,CAAC,CAAC,SAAQ,SAASP,IAAEC,IAAEC,IAAEC,IAAE;AAAC,MAAIC,IAAEK,IAAEC,IAAEL,IAAEC,IAAEC,IAAEC,IAAEG,IAAEc,IAAEb,IAAEc,IAAEb,IAAEC,IAAEa,IAAEC,IAAEC,IAAEC,IAAEC;AAAE,MAAG9B,KAAE,IAAEC;AAAE,WAAM;AAAG,MAAGK,KAAEN,KAAE,GAAED,GAAE,OAAOO,EAAC,IAAEP,GAAE;AAAU,WAAM;AAAG,MAAGA,GAAE,OAAOO,EAAC,IAAEP,GAAE,aAAW;AAAE,WAAM;AAAG,OAAIU,KAAEV,GAAE,OAAOO,EAAC,IAAEP,GAAE,OAAOO,EAAC,MAAIP,GAAE,OAAOO,EAAC;AAAE,WAAM;AAAG,MAAG,SAAOuB,KAAE9B,GAAE,IAAI,WAAWU,IAAG,MAAI,OAAKoB,MAAG,OAAKA;AAAE,WAAM;AAAG,MAAGpB,MAAGV,GAAE,OAAOO,EAAC;AAAE,WAAM;AAAG,MAAG,SAAOwB,KAAE/B,GAAE,IAAI,WAAWU,IAAG,MAAI,OAAKqB,MAAG,OAAKA,MAAG,CAAC,GAAGA,EAAC;AAAE,WAAM;AAAG,MAAG,OAAKD,MAAG,GAAGC,EAAC;AAAE,WAAM;AAAG,SAAKrB,KAAEV,GAAE,OAAOO,EAAC,KAAG;AAAC,QAAG,SAAOH,KAAEJ,GAAE,IAAI,WAAWU,EAAC,MAAI,OAAKN,MAAG,OAAKA,MAAG,CAAC,GAAGA,EAAC;AAAE,aAAM;AAAG,IAAAM;AAAA,EAAG;AAAC,OAAIF,MAAGC,KAAE,GAAGT,IAAEC,KAAE,CAAC,GAAG,MAAM,GAAG,GAAEW,KAAE,CAAE,GAACP,KAAE,GAAEA,KAAEG,GAAE,QAAOH,MAAI;AAAC,QAAG,EAAEqB,KAAElB,GAAEH,EAAC,EAAE,KAAI,IAAI;AAAC,UAAG,MAAIA,MAAGA,OAAIG,GAAE,SAAO;AAAE;AAAS,aAAM;AAAA,IAAE;AAAC,QAAG,CAAC,WAAW,KAAKkB,EAAC;AAAE,aAAM;AAAG,WAAKA,GAAE,WAAWA,GAAE,SAAO,CAAC,IAAEd,GAAE,KAAK,OAAKc,GAAE,WAAW,CAAC,IAAE,WAAS,OAAO,IAAE,OAAKA,GAAE,WAAW,CAAC,IAAEd,GAAE,KAAK,MAAM,IAAEA,GAAE,KAAK,EAAE;AAAA,EAAC;AAAC,MAAG,QAAMH,KAAE,GAAGT,IAAEC,EAAC,EAAE,QAAQ,QAAQ,GAAG;AAAE,WAAM;AAAG,MAAGD,GAAE,OAAOC,EAAC,IAAED,GAAE,aAAW;AAAE,WAAM;AAAG,OAAIQ,KAAE,GAAGC,EAAC,GAAG,UAAQ,OAAKD,GAAE,CAAC,KAAGA,GAAE,MAAO,GAACA,GAAE,UAAQ,OAAKA,GAAEA,GAAE,SAAO,CAAC,KAAGA,GAAE,IAAK,GAAC,OAAKG,KAAEH,GAAE,WAASG,OAAIC,GAAE;AAAO,WAAM;AAAG,MAAGT;AAAE,WAAM;AAAG,OAAIwB,KAAE3B,GAAE,YAAWA,GAAE,aAAW,SAAQ6B,KAAE7B,GAAE,GAAG,MAAM,MAAM,SAAS,YAAY,IAAGyB,KAAEzB,GAAE,KAAK,cAAa,SAAQ,CAAC,GAAG,MAAIa,KAAE,CAACZ,IAAE,CAAC,IAAGwB,KAAEzB,GAAE,KAAK,cAAa,SAAQ,CAAC,GAAG,MAAI,CAACC,IAAEA,KAAE,CAAC,IAAGwB,KAAEzB,GAAE,KAAK,WAAU,MAAK,CAAC,GAAG,MAAI,CAACC,IAAEA,KAAE,CAAC,GAAEI,KAAE,GAAEA,KAAEG,GAAE,QAAOH;AAAI,IAAAoB,KAAEzB,GAAE,KAAK,WAAU,MAAK,CAAC,GAAEY,GAAEP,EAAC,MAAIoB,GAAE,QAAM,CAAC,CAAC,SAAQ,gBAAcb,GAAEP,EAAC,CAAC,CAAC,KAAIoB,KAAEzB,GAAE,KAAK,UAAS,IAAG,CAAC,GAAG,UAAQQ,GAAEH,EAAC,EAAE,KAAM,GAACoB,GAAE,WAAS,IAAGA,KAAEzB,GAAE,KAAK,YAAW,MAAK,EAAE;AAAE,OAAIyB,KAAEzB,GAAE,KAAK,YAAW,MAAK,EAAE,GAAEyB,KAAEzB,GAAE,KAAK,eAAc,SAAQ,EAAE,GAAEO,KAAEN,KAAE,GAAEM,KAAEL,MAAG,EAAEF,GAAE,OAAOO,EAAC,IAAEP,GAAE,YAAWO,MAAI;AAAC,SAAIqB,KAAE,OAAGvB,KAAE,GAAEC,KAAEuB,GAAE,QAAOxB,KAAEC,IAAED;AAAI,UAAGwB,GAAExB,EAAC,EAAEL,IAAEO,IAAEL,IAAE,IAAE,GAAE;AAAC,QAAA0B,KAAE;AAAG;AAAA,MAAK;AAAC,QAAGA;AAAE;AAAM,QAAG,EAAEnB,KAAE,GAAGT,IAAEO,EAAC,EAAE,KAAM;AAAE;AAAM,QAAGP,GAAE,OAAOO,EAAC,IAAEP,GAAE,aAAW;AAAE;AAAM,UAAKQ,KAAE,GAAGC,EAAC,GAAG,UAAQ,OAAKD,GAAE,CAAC,KAAGA,GAAE,MAAK,GAAGA,GAAE,UAAQ,OAAKA,GAAEA,GAAE,SAAO,CAAC,KAAGA,GAAE,IAAG,GAAGD,OAAIN,KAAE,OAAKwB,KAAEzB,GAAE,KAAK,cAAa,SAAQ,CAAC,GAAG,MAAIc,KAAE,CAACb,KAAE,GAAE,CAAC,KAAIwB,KAAEzB,GAAE,KAAK,WAAU,MAAK,CAAC,GAAG,MAAI,CAACO,IAAEA,KAAE,CAAC,GAAEF,KAAE,GAAEA,KAAEM,IAAEN;AAAI,MAAAoB,KAAEzB,GAAE,KAAK,WAAU,MAAK,CAAC,GAAEY,GAAEP,EAAC,MAAIoB,GAAE,QAAM,CAAC,CAAC,SAAQ,gBAAcb,GAAEP,EAAC,CAAC,CAAC,KAAIoB,KAAEzB,GAAE,KAAK,UAAS,IAAG,CAAC,GAAG,UAAQQ,GAAEH,EAAC,IAAEG,GAAEH,EAAC,EAAE,KAAI,IAAG,IAAGoB,GAAE,WAAS,CAAA,GAAGA,KAAEzB,GAAE,KAAK,YAAW,MAAK,EAAE;AAAE,IAAAyB,KAAEzB,GAAE,KAAK,YAAW,MAAK,EAAE;AAAA,EAAC;AAAC,SAAOc,OAAIW,KAAEzB,GAAE,KAAK,eAAc,SAAQ,EAAE,GAAEc,GAAE,CAAC,IAAEP,KAAGkB,KAAEzB,GAAE,KAAK,eAAc,SAAQ,EAAE,GAAEa,GAAE,CAAC,IAAEN,IAAEP,GAAE,aAAW2B,IAAE3B,GAAE,OAAKO,IAAE;AAAE,GAAE,CAAC,aAAY,WAAW,CAAC,GAAE,CAAC,QAAO,SAASP,IAAEC,IAAEC,IAAE;AAAC,MAAIC,IAAEC,IAAEK;AAAE,MAAGT,GAAE,OAAOC,EAAC,IAAED,GAAE,YAAU;AAAE,WAAM;AAAG,OAAII,KAAED,KAAEF,KAAE,GAAEE,KAAED;AAAG,QAAGF,GAAE,QAAQG,EAAC;AAAE,MAAAA;AAAA,SAAQ;AAAC,UAAG,EAAEH,GAAE,OAAOG,EAAC,IAAEH,GAAE,aAAW;AAAG;AAAM,MAAAI,KAAE,EAAED;AAAA,IAAC;AAAC,SAAOH,GAAE,OAAKI,KAAGK,KAAET,GAAE,KAAK,cAAa,QAAO,CAAC,GAAG,UAAQA,GAAE,SAASC,IAAEG,IAAE,IAAEJ,GAAE,WAAU,KAAE,IAAE,MAAKS,GAAE,MAAI,CAACR,IAAED,GAAE,IAAI,GAAE;AAAE,CAAC,GAAE,CAAC,SAAQ,SAASA,IAAEC,IAAEC,IAAEC,IAAE;AAAC,MAAIC,IAAEK,IAAEC,IAAEL,IAAEC,IAAEC,IAAEC,IAAEG,KAAE,OAAGc,KAAEzB,GAAE,OAAOC,EAAC,IAAED,GAAE,OAAOC,EAAC,GAAEW,KAAEZ,GAAE,OAAOC,EAAC;AAAE,MAAGD,GAAE,OAAOC,EAAC,IAAED,GAAE,aAAW;AAAE,WAAM;AAAG,MAAGyB,KAAE,IAAEb;AAAE,WAAM;AAAG,MAAG,SAAOR,KAAEJ,GAAE,IAAI,WAAWyB,EAAC,MAAI,OAAKrB;AAAE,WAAM;AAAG,MAAGE,KAAEmB,KAAGhB,MAAGgB,KAAEzB,GAAE,UAAUyB,IAAErB,EAAC,KAAGE,MAAG;AAAE,WAAM;AAAG,MAAGE,KAAER,GAAE,IAAI,MAAMM,IAAEmB,EAAC,GAAEf,KAAEV,GAAE,IAAI,MAAMyB,IAAEb,EAAC,GAAE,OAAKR,MAAGM,GAAE,QAAQ,OAAO,aAAaN,EAAC,CAAC,KAAG;AAAE,WAAM;AAAG,MAAGD;AAAE,WAAM;AAAG,OAAIE,KAAEJ,IAAE,EAAE,EAAEI,MAAGH,OAAI,GAAGuB,KAAEnB,KAAEN,GAAE,OAAOK,EAAC,IAAEL,GAAE,OAAOK,EAAC,MAAIO,KAAEZ,GAAE,OAAOK,EAAC,MAAIL,GAAE,OAAOK,EAAC,IAAEL,GAAE;AAAY,QAAGA,GAAE,IAAI,WAAWyB,EAAC,MAAIrB,MAAG,EAAEJ,GAAE,OAAOK,EAAC,IAAEL,GAAE,aAAW,MAAIyB,KAAEzB,GAAE,UAAUyB,IAAErB,EAAC,KAAGE,KAAEG,OAAIgB,KAAEzB,GAAE,WAAWyB,EAAC,KAAGb,KAAG;AAAC,MAAAD,KAAE;AAAG;AAAA,IAAK;AAAC,SAAOF,KAAET,GAAE,OAAOC,EAAC,GAAED,GAAE,OAAKK,MAAGM,KAAE,IAAE,KAAIJ,KAAEP,GAAE,KAAK,SAAQ,QAAO,CAAC,GAAG,OAAKU,IAAEH,GAAE,UAAQP,GAAE,SAASC,KAAE,GAAEI,IAAEI,IAAE,IAAE,GAAEF,GAAE,SAAOC,IAAED,GAAE,MAAI,CAACN,IAAED,GAAE,IAAI,GAAE;AAAE,GAAE,CAAC,aAAY,aAAY,cAAa,MAAM,CAAC,GAAE,CAAC,cAAa,SAASA,IAAEC,IAAEC,IAAEC,IAAE;AAAC,MAAIC,IAAEK,IAAEC,IAAEL,IAAEC,IAAEC,IAAEC,IAAEG,IAAEc,IAAEb,IAAEc,IAAEb,IAAEC,IAAEa,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEjB,IAAEC,KAAEhB,GAAE,SAAQiB,KAAEjB,GAAE,OAAOC,EAAC,IAAED,GAAE,OAAOC,EAAC,GAAEiB,KAAElB,GAAE,OAAOC,EAAC;AAAE,MAAGD,GAAE,OAAOC,EAAC,IAAED,GAAE,aAAW;AAAE,WAAM;AAAG,MAAG,OAAKA,GAAE,IAAI,WAAWiB,IAAG;AAAE,WAAM;AAAG,MAAGd;AAAE,WAAM;AAAG,OAAIE,KAAEoB,KAAEzB,GAAE,OAAOC,EAAC,IAAE,GAAE,OAAKD,GAAE,IAAI,WAAWiB,EAAC,KAAGA,MAAIZ,MAAIoB,MAAIrB,KAAE,OAAGyB,KAAE,QAAI,MAAI7B,GAAE,IAAI,WAAWiB,EAAC,KAAGY,KAAE,OAAI7B,GAAE,QAAQC,EAAC,IAAEwB,MAAG,KAAG,KAAGR,MAAIZ,MAAIoB,MAAIrB,KAAE,SAAIA,KAAE,QAAIyB,KAAE,OAAGjB,KAAE,CAACZ,GAAE,OAAOC,EAAC,CAAC,GAAED,GAAE,OAAOC,EAAC,IAAEgB,IAAEA,KAAEC,OAAIT,KAAET,GAAE,IAAI,WAAWiB,EAAC,GAAE,GAAGR,EAAC;AAAI,UAAIA,KAAEgB,MAAG,KAAGA,KAAEzB,GAAE,QAAQC,EAAC,KAAGG,KAAE,IAAE,MAAI,IAAEqB,MAAIR;AAAI,OAAIS,KAAE,CAAC1B,GAAE,QAAQC,EAAC,CAAC,GAAED,GAAE,QAAQC,EAAC,IAAED,GAAE,OAAOC,EAAC,IAAE,KAAG4B,KAAE,IAAE,IAAGtB,KAAEU,MAAGC,IAAES,KAAE,CAAC3B,GAAE,OAAOC,EAAC,CAAC,GAAED,GAAE,OAAOC,EAAC,IAAEwB,KAAEpB,IAAEuB,KAAE,CAAC5B,GAAE,OAAOC,EAAC,CAAC,GAAED,GAAE,OAAOC,EAAC,IAAEgB,KAAEjB,GAAE,OAAOC,EAAC,GAAE8B,KAAE/B,GAAE,GAAG,MAAM,MAAM,SAAS,YAAY,GAAEc,KAAEd,GAAE,YAAWA,GAAE,aAAW,cAAaW,KAAEV,KAAE,GAAEU,KAAET,OAAIa,KAAEf,GAAE,OAAOW,EAAC,IAAEX,GAAE,WAAU,GAAGiB,KAAEjB,GAAE,OAAOW,EAAC,IAAEX,GAAE,OAAOW,EAAC,OAAKO,KAAElB,GAAE,OAAOW,EAAC,MAAKA;AAAI,QAAG,OAAKX,GAAE,IAAI,WAAWiB,IAAG,KAAGF,IAAE;AAAC,UAAGR;AAAE;AAAM,WAAIuB,KAAE,OAAGpB,KAAE,GAAEJ,KAAEyB,GAAE,QAAOrB,KAAEJ,IAAEI;AAAI,YAAGqB,GAAErB,EAAC,EAAEV,IAAEW,IAAET,IAAE,IAAE,GAAE;AAAC,UAAA4B,KAAE;AAAG;AAAA,QAAK;AAAC,UAAGA,IAAE;AAAC,QAAA9B,GAAE,UAAQW,IAAE,MAAIX,GAAE,cAAYY,GAAE,KAAKZ,GAAE,OAAOW,EAAC,CAAC,GAAEe,GAAE,KAAK1B,GAAE,QAAQW,EAAC,CAAC,GAAEiB,GAAE,KAAK5B,GAAE,OAAOW,EAAC,CAAC,GAAEgB,GAAE,KAAK3B,GAAE,OAAOW,EAAC,CAAC,GAAEX,GAAE,OAAOW,EAAC,KAAGX,GAAE;AAAW;AAAA,MAAK;AAAC,MAAAY,GAAE,KAAKZ,GAAE,OAAOW,EAAC,CAAC,GAAEe,GAAE,KAAK1B,GAAE,QAAQW,EAAC,CAAC,GAAEiB,GAAE,KAAK5B,GAAE,OAAOW,EAAC,CAAC,GAAEgB,GAAE,KAAK3B,GAAE,OAAOW,EAAC,CAAC,GAAEX,GAAE,OAAOW,EAAC,IAAE;AAAA,IAAE,OAAK;AAAC,WAAIN,KAAEoB,KAAEzB,GAAE,OAAOW,EAAC,IAAE,GAAE,OAAKX,GAAE,IAAI,WAAWiB,EAAC,KAAGA,MAAIZ,MAAIoB,MAAIrB,KAAE,OAAGyB,KAAE,QAAI,MAAI7B,GAAE,IAAI,WAAWiB,EAAC,KAAGY,KAAE,OAAI7B,GAAE,QAAQW,EAAC,IAAEc,MAAG,KAAG,KAAGR,MAAIZ,MAAIoB,MAAIrB,KAAE,SAAIA,KAAE,QAAIyB,KAAE,OAAGjB,GAAE,KAAKZ,GAAE,OAAOW,EAAC,CAAC,GAAEX,GAAE,OAAOW,EAAC,IAAEM,IAAEA,KAAEC,OAAIT,KAAET,GAAE,IAAI,WAAWiB,EAAC,GAAE,GAAGR,EAAC;AAAI,cAAIA,KAAEgB,MAAG,KAAGA,KAAEzB,GAAE,QAAQW,EAAC,KAAGP,KAAE,IAAE,MAAI,IAAEqB,MAAIR;AAAI,MAAAV,KAAEU,MAAGC,IAAEQ,GAAE,KAAK1B,GAAE,QAAQW,EAAC,CAAC,GAAEX,GAAE,QAAQW,EAAC,IAAEX,GAAE,OAAOW,EAAC,IAAE,KAAGkB,KAAE,IAAE,IAAGF,GAAE,KAAK3B,GAAE,OAAOW,EAAC,CAAC,GAAEX,GAAE,OAAOW,EAAC,IAAEc,KAAEpB,IAAEuB,GAAE,KAAK5B,GAAE,OAAOW,EAAC,CAAC,GAAEX,GAAE,OAAOW,EAAC,IAAEM,KAAEjB,GAAE,OAAOW,EAAC;AAAA,IAAC;AAAC,OAAIE,KAAEb,GAAE,WAAUA,GAAE,YAAU,IAAGgC,KAAEhC,GAAE,KAAK,mBAAkB,cAAa,CAAC,GAAG,SAAO,KAAIgC,GAAE,MAAIxB,KAAE,CAACP,IAAE,CAAC,GAAED,GAAE,GAAG,MAAM,SAASA,IAAEC,IAAEU,EAAC,IAAGqB,KAAEhC,GAAE,KAAK,oBAAmB,cAAa,EAAE,GAAG,SAAO,KAAIA,GAAE,UAAQgB,IAAEhB,GAAE,aAAWc,IAAEN,GAAE,CAAC,IAAER,GAAE,MAAKU,KAAE,GAAEA,KAAEkB,GAAE,QAAOlB;AAAI,IAAAV,GAAE,OAAOU,KAAET,EAAC,IAAEW,GAAEF,EAAC,GAAEV,GAAE,OAAOU,KAAET,EAAC,IAAE2B,GAAElB,EAAC,GAAEV,GAAE,OAAOU,KAAET,EAAC,IAAE0B,GAAEjB,EAAC,GAAEV,GAAE,QAAQU,KAAET,EAAC,IAAEyB,GAAEhB,EAAC;AAAE,SAAOV,GAAE,YAAUa,IAAE;AAAE,GAAE,CAAC,aAAY,aAAY,cAAa,MAAM,CAAC,GAAE,CAAC,MAAK,SAASb,IAAEC,IAAEC,IAAEC,IAAE;AAAC,MAAIC,IAAEK,IAAEC,IAAEL,IAAEC,KAAEN,GAAE,OAAOC,EAAC,IAAED,GAAE,OAAOC,EAAC,GAAEM,KAAEP,GAAE,OAAOC,EAAC;AAAE,MAAGD,GAAE,OAAOC,EAAC,IAAED,GAAE,aAAW;AAAE,WAAM;AAAG,MAAG,QAAMI,KAAEJ,GAAE,IAAI,WAAWM,IAAG,MAAI,OAAKF,MAAG,OAAKA;AAAE,WAAM;AAAG,OAAIK,KAAE,GAAEH,KAAEC,MAAG;AAAC,SAAIG,KAAEV,GAAE,IAAI,WAAWM,IAAG,OAAKF,MAAG,CAAC,GAAGM,EAAC;AAAE,aAAM;AAAG,IAAAA,OAAIN,MAAGK;AAAA,EAAG;AAAC,SAAM,EAAEA,KAAE,OAAKN,OAAIH,GAAE,OAAKC,KAAE,IAAGI,KAAEL,GAAE,KAAK,MAAK,MAAK,CAAC,GAAG,MAAI,CAACC,IAAED,GAAE,IAAI,GAAEK,GAAE,SAAO,MAAMI,KAAE,CAAC,EAAE,KAAK,OAAO,aAAaL,EAAC,CAAC,IAAG;AAAG,GAAE,CAAC,aAAY,aAAY,cAAa,MAAM,CAAC,GAAE,CAAC,QAAO,SAASJ,IAAEC,IAAEC,IAAEC,IAAE;AAAC,MAAIC,IAAEK,IAAEC,IAAEL,IAAEC,IAAEC,IAAEC,IAAEG,IAAEc,IAAEb,IAAEc,IAAEb,IAAEC,IAAEa,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEjB,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,KAAE,OAAGS,KAAE;AAAG,MAAGjC,GAAE,OAAOC,EAAC,IAAED,GAAE,aAAW;AAAE,WAAM;AAAG,MAAGA,GAAE,cAAY,KAAGA,GAAE,OAAOC,EAAC,IAAED,GAAE,cAAY,KAAGA,GAAE,OAAOC,EAAC,IAAED,GAAE;AAAU,WAAM;AAAG,MAAGG,MAAG,gBAAcH,GAAE,cAAYA,GAAE,OAAOC,EAAC,KAAGD,GAAE,cAAYwB,KAAE,QAAKN,KAAE,GAAGlB,IAAEC,EAAC,MAAI,GAAE;AAAC,QAAGO,KAAE,MAAGY,KAAEpB,GAAE,OAAOC,EAAC,IAAED,GAAE,OAAOC,EAAC,GAAEa,KAAE,OAAOd,GAAE,IAAI,MAAMoB,IAAEF,KAAE,CAAC,CAAC,GAAEM,MAAG,MAAIV;AAAE,aAAM;AAAA,EAAE,OAAK;AAAC,QAAG,GAAGI,KAAE,GAAGlB,IAAEC,EAAC,MAAI;AAAG,aAAM;AAAG,IAAAO,KAAE;AAAA,EAAE;AAAC,MAAGgB,MAAGxB,GAAE,WAAWkB,EAAC,KAAGlB,GAAE,OAAOC,EAAC;AAAE,WAAM;AAAG,MAAGY,KAAEb,GAAE,IAAI,WAAWkB,KAAE,CAAC,GAAEf;AAAE,WAAM;AAAG,OAAIuB,KAAE1B,GAAE,OAAO,QAAOQ,MAAGe,KAAEvB,GAAE,KAAK,qBAAoB,MAAK,CAAC,GAAE,MAAIc,OAAIS,GAAE,QAAM,CAAC,CAAC,SAAQT,EAAC,CAAC,MAAIS,KAAEvB,GAAE,KAAK,oBAAmB,MAAK,CAAC,GAAEuB,GAAE,MAAIX,KAAE,CAACX,IAAE,CAAC,GAAEsB,GAAE,SAAO,OAAO,aAAaV,EAAC,GAAEe,KAAE3B,IAAEkB,KAAE,OAAGG,KAAEtB,GAAE,GAAG,MAAM,MAAM,SAAS,MAAM,GAAE+B,KAAE/B,GAAE,YAAWA,GAAE,aAAW,QAAO4B,KAAE1B,MAAG;AAAC,SAAIe,KAAEC,IAAES,KAAE3B,GAAE,OAAO4B,EAAC,GAAErB,KAAEsB,KAAE7B,GAAE,OAAO4B,EAAC,IAAEV,MAAGlB,GAAE,OAAOC,EAAC,IAAED,GAAE,OAAOC,EAAC,IAAGgB,KAAEU,MAAG;AAAC,UAAG,OAAKvB,KAAEJ,GAAE,IAAI,WAAWiB,EAAC;AAAG,QAAAY,MAAG,KAAGA,KAAE7B,GAAE,QAAQ4B,EAAC,KAAG;AAAA,WAAM;AAAC,YAAG,OAAKxB;AAAE;AAAM,QAAAyB;AAAA,MAAG;AAAC,MAAAZ;AAAA,IAAG;AAAC,SAAIX,MAAGG,KAAEQ,OAAIU,KAAE,IAAEE,KAAEtB,MAAG,MAAID,KAAE,IAAGD,KAAEE,KAAED,KAAGiB,KAAEvB,GAAE,KAAK,kBAAiB,MAAK,CAAC,GAAG,SAAO,OAAO,aAAaa,EAAC,GAAEU,GAAE,MAAIZ,KAAE,CAACV,IAAE,CAAC,GAAEO,OAAIe,GAAE,OAAKvB,GAAE,IAAI,MAAMoB,IAAEF,KAAE,CAAC,IAAGF,KAAEhB,GAAE,OAAMe,KAAEf,GAAE,OAAOC,EAAC,GAAE+B,KAAEhC,GAAE,OAAOC,EAAC,GAAE6B,KAAE9B,GAAE,YAAWA,GAAE,aAAWA,GAAE,WAAUA,GAAE,YAAUK,IAAEL,GAAE,QAAM,MAAGA,GAAE,OAAOC,EAAC,IAAEQ,KAAET,GAAE,OAAOC,EAAC,GAAED,GAAE,OAAOC,EAAC,IAAE4B,IAAEpB,MAAGkB,MAAG3B,GAAE,QAAQC,KAAE,CAAC,IAAED,GAAE,OAAK,KAAK,IAAIA,GAAE,OAAK,GAAEE,EAAC,IAAEF,GAAE,GAAG,MAAM,SAASA,IAAEC,IAAEC,IAAE,IAAE,GAAEF,GAAE,SAAO,CAACmB,OAAIc,KAAE,QAAId,KAAEnB,GAAE,OAAKC,KAAE,KAAGD,GAAE,QAAQA,GAAE,OAAK,CAAC,GAAEA,GAAE,YAAUA,GAAE,YAAWA,GAAE,aAAW8B,IAAE9B,GAAE,OAAOC,EAAC,IAAEc,IAAEf,GAAE,OAAOC,EAAC,IAAE+B,IAAEhC,GAAE,QAAMgB,KAAGO,KAAEvB,GAAE,KAAK,mBAAkB,MAAK,EAAE,GAAG,SAAO,OAAO,aAAaa,EAAC,GAAEe,KAAE3B,KAAED,GAAE,MAAKW,GAAE,CAAC,IAAEiB,IAAEnB,KAAET,GAAE,OAAOC,EAAC,GAAE2B,MAAG1B;AAAE;AAAM,QAAGF,GAAE,OAAO4B,EAAC,IAAE5B,GAAE;AAAU;AAAM,QAAGA,GAAE,OAAOC,EAAC,IAAED,GAAE,aAAW;AAAE;AAAM,SAAIqB,KAAE,OAAGX,KAAE,GAAEe,KAAEH,GAAE,QAAOZ,KAAEe,IAAEf;AAAI,UAAGY,GAAEZ,EAAC,EAAEV,IAAE4B,IAAE1B,IAAE,IAAE,GAAE;AAAC,QAAAmB,KAAE;AAAG;AAAA,MAAK;AAAC,QAAGA;AAAE;AAAM,QAAGb,IAAE;AAAC,WAAIU,KAAE,GAAGlB,IAAE4B,EAAC,KAAG;AAAE;AAAM,MAAAR,KAAEpB,GAAE,OAAO4B,EAAC,IAAE5B,GAAE,OAAO4B,EAAC;AAAA,IAAC,YAAUV,KAAE,GAAGlB,IAAE4B,EAAC,KAAG;AAAE;AAAM,QAAGf,OAAIb,GAAE,IAAI,WAAWkB,KAAE,CAAC;AAAE;AAAA,EAAK;AAAC,UAAOK,KAAEf,KAAER,GAAE,KAAK,sBAAqB,MAAK,EAAE,IAAEA,GAAE,KAAK,qBAAoB,MAAK,EAAE,GAAG,SAAO,OAAO,aAAaa,EAAC,GAAED,GAAE,CAAC,IAAEgB,IAAE5B,GAAE,OAAK4B,IAAE5B,GAAE,aAAW+B,IAAEE,MAAG,SAASjC,IAAEC,IAAE;AAAC,QAAIC,IAAEC,IAAEC,KAAEJ,GAAE,QAAM;AAAE,SAAIE,KAAED,KAAE,GAAEE,KAAEH,GAAE,OAAO,SAAO,GAAEE,KAAEC,IAAED;AAAI,MAAAF,GAAE,OAAOE,EAAC,EAAE,UAAQE,MAAG,qBAAmBJ,GAAE,OAAOE,EAAC,EAAE,SAAOF,GAAE,OAAOE,KAAE,CAAC,EAAE,SAAO,MAAGF,GAAE,OAAOE,EAAC,EAAE,SAAO,MAAGA,MAAG;AAAA,EAAE,EAAEF,IAAE0B,EAAC,GAAE;AAAE,GAAE,CAAC,aAAY,aAAY,YAAY,CAAC,GAAE,CAAC,aAAY,SAAS1B,IAAEC,IAAEC,IAAEC,IAAE;AAAC,MAAIC,IAAEK,IAAEC,IAAEL,IAAEC,IAAEC,IAAEC,IAAEG,IAAEc,IAAEb,IAAEc,IAAEb,IAAEC,IAAEa,IAAEC,IAAEC,IAAEC,KAAE,GAAEC,KAAE/B,GAAE,OAAOC,EAAC,IAAED,GAAE,OAAOC,EAAC,GAAE+B,KAAEhC,GAAE,OAAOC,EAAC,GAAEc,KAAEd,KAAE;AAAE,MAAGD,GAAE,OAAOC,EAAC,IAAED,GAAE,aAAW;AAAE,WAAM;AAAG,MAAG,OAAKA,GAAE,IAAI,WAAW+B,EAAC;AAAE,WAAM;AAAG,SAAK,EAAEA,KAAEC;AAAG,QAAG,OAAKhC,GAAE,IAAI,WAAW+B,EAAC,KAAG,OAAK/B,GAAE,IAAI,WAAW+B,KAAE,CAAC,GAAE;AAAC,UAAGA,KAAE,MAAIC;AAAE,eAAM;AAAG,UAAG,OAAKhC,GAAE,IAAI,WAAW+B,KAAE,CAAC;AAAE,eAAM;AAAG;AAAA,IAAK;AAAC,OAAI1B,KAAEL,GAAE,SAAQ4B,KAAE5B,GAAE,GAAG,MAAM,MAAM,SAAS,WAAW,GAAEY,KAAEZ,GAAE,YAAWA,GAAE,aAAW,aAAYe,KAAEV,MAAG,CAACL,GAAE,QAAQe,EAAC,GAAEA;AAAI,QAAG,EAAEf,GAAE,OAAOe,EAAC,IAAEf,GAAE,YAAU,KAAGA,GAAE,OAAOe,EAAC,IAAE,IAAG;AAAC,WAAIY,KAAE,OAAGpB,KAAE,GAAEC,KAAEoB,GAAE,QAAOrB,KAAEC,IAAED;AAAI,YAAGqB,GAAErB,EAAC,EAAEP,IAAEe,IAAEV,IAAE,IAAE,GAAE;AAAC,UAAAsB,KAAE;AAAG;AAAA,QAAK;AAAC,UAAGA;AAAE;AAAA,IAAK;AAAC,OAAIK,MAAGlB,KAAEd,GAAE,SAASC,IAAEc,IAAEf,GAAE,WAAU,KAAE,EAAE,KAAM,GAAE,QAAO+B,KAAE,GAAEA,KAAEC,IAAED,MAAI;AAAC,QAAG,QAAM3B,KAAEU,GAAE,WAAWiB,EAAC;AAAG,aAAM;AAAG,QAAG,OAAK3B,IAAE;AAAC,MAAAqB,KAAEM;AAAE;AAAA,IAAK;AAAC,KAAC,OAAK3B,MAAG,OAAKA,MAAG,EAAE2B,KAAEC,MAAG,OAAKlB,GAAE,WAAWiB,EAAC,MAAID;AAAA,EAAG;AAAC,MAAGL,KAAE,KAAG,OAAKX,GAAE,WAAWW,KAAE,CAAC;AAAE,WAAM;AAAG,OAAIM,KAAEN,KAAE,GAAEM,KAAEC,IAAED;AAAI,QAAG,QAAM3B,KAAEU,GAAE,WAAWiB,EAAC;AAAG,MAAAD;AAAA,aAAY,CAAC,GAAG1B,EAAC;AAAE;AAAM,MAAG,EAAEsB,KAAE1B,GAAE,GAAG,QAAQ,qBAAqBc,IAAEiB,IAAEC,EAAC,GAAG;AAAG,WAAM;AAAG,MAAG1B,KAAEN,GAAE,GAAG,cAAc0B,GAAE,GAAG,GAAE,CAAC1B,GAAE,GAAG,aAAaM,EAAC;AAAE,WAAM;AAAG,OAAIG,KAAEsB,KAAEL,GAAE,KAAIhB,KAAEoB,MAAGJ,GAAE,OAAMb,KAAEkB,IAAEA,KAAEC,IAAED;AAAI,QAAG,QAAM3B,KAAEU,GAAE,WAAWiB,EAAC;AAAG,MAAAD;AAAA,aAAY,CAAC,GAAG1B,EAAC;AAAE;AAAM,OAAIsB,KAAE1B,GAAE,GAAG,QAAQ,eAAec,IAAEiB,IAAEC,EAAC,GAAED,KAAEC,MAAGnB,OAAIkB,MAAGL,GAAE,MAAIG,KAAEH,GAAE,KAAIK,KAAEL,GAAE,KAAII,MAAGJ,GAAE,UAAQG,KAAE,IAAGE,KAAEtB,IAAEqB,KAAEpB,KAAGqB,KAAEC,OAAI5B,KAAEU,GAAE,WAAWiB,EAAC,GAAE,GAAG3B,EAAC;AAAI,IAAA2B;AAAI,MAAGA,KAAEC,MAAG,OAAKlB,GAAE,WAAWiB,EAAC,KAAGF;AAAE,SAAIA,KAAE,IAAGE,KAAEtB,IAAEqB,KAAEpB,IAAEqB,KAAEC,OAAI5B,KAAEU,GAAE,WAAWiB,EAAC,GAAE,GAAG3B,EAAC;AAAI,MAAA2B;AAAI,SAAM,EAAEA,KAAEC,MAAG,OAAKlB,GAAE,WAAWiB,EAAC,OAAK,CAAC,EAAEpB,KAAE,GAAGG,GAAE,MAAM,GAAEW,EAAC,CAAC,OAAKtB,OAAI,WAASH,GAAE,IAAI,eAAaA,GAAE,IAAI,aAAW,CAAA,IAAI,WAASA,GAAE,IAAI,WAAWW,EAAC,MAAIX,GAAE,IAAI,WAAWW,EAAC,IAAE,EAAC,OAAMkB,IAAE,MAAKvB,GAAC,IAAGN,GAAE,aAAWY,IAAEZ,GAAE,OAAKC,KAAE6B,KAAE,IAAG;AAAI,CAAC,GAAE,CAAC,cAAa,SAAS9B,IAAEC,IAAEC,IAAEC,IAAE;AAAC,MAAIC,IAAEK,IAAEC,IAAEL,IAAEC,KAAEN,GAAE,OAAOC,EAAC,IAAED,GAAE,OAAOC,EAAC,GAAEM,KAAEP,GAAE,OAAOC,EAAC;AAAE,MAAGD,GAAE,OAAOC,EAAC,IAAED,GAAE,aAAW;AAAE,WAAM;AAAG,MAAG,CAACA,GAAE,GAAG,QAAQ;AAAK,WAAM;AAAG,MAAG,OAAKA,GAAE,IAAI,WAAWM,EAAC;AAAE,WAAM;AAAG,OAAID,KAAEL,GAAE,IAAI,MAAMM,IAAEC,EAAC,GAAEH,KAAE,GAAEA,KAAE,GAAG,UAAQ,CAAC,GAAGA,EAAC,EAAE,CAAC,EAAE,KAAKC,EAAC,GAAED;AAAI;AAAC,MAAGA,OAAI,GAAG;AAAO,WAAM;AAAG,MAAGD;AAAE,WAAO,GAAGC,EAAC,EAAE,CAAC;AAAE,MAAGK,KAAER,KAAE,GAAE,CAAC,GAAGG,EAAC,EAAE,CAAC,EAAE,KAAKC,EAAC;AAAE,WAAKI,KAAEP,MAAG,EAAEF,GAAE,OAAOS,EAAC,IAAET,GAAE,YAAWS;AAAI,UAAGH,KAAEN,GAAE,OAAOS,EAAC,IAAET,GAAE,OAAOS,EAAC,GAAEF,KAAEP,GAAE,OAAOS,EAAC,GAAEJ,KAAEL,GAAE,IAAI,MAAMM,IAAEC,EAAC,GAAE,GAAGH,EAAC,EAAE,CAAC,EAAE,KAAKC,EAAC,GAAE;AAAC,cAAIA,GAAE,UAAQI;AAAI;AAAA,MAAK;AAAA;AAAC,SAAOT,GAAE,OAAKS,KAAGC,KAAEV,GAAE,KAAK,cAAa,IAAG,CAAC,GAAG,MAAI,CAACC,IAAEQ,EAAC,GAAEC,GAAE,UAAQV,GAAE,SAASC,IAAEQ,IAAET,GAAE,WAAU,IAAE,GAAE;AAAE,GAAE,CAAC,aAAY,aAAY,YAAY,CAAC,GAAE,CAAC,WAAU,SAASA,IAAEC,IAAEC,IAAEC,IAAE;AAAC,MAAIC,IAAEK,IAAEC,IAAEL,IAAEC,KAAEN,GAAE,OAAOC,EAAC,IAAED,GAAE,OAAOC,EAAC,GAAEM,KAAEP,GAAE,OAAOC,EAAC;AAAE,MAAGD,GAAE,OAAOC,EAAC,IAAED,GAAE,aAAW;AAAE,WAAM;AAAG,MAAG,QAAMI,KAAEJ,GAAE,IAAI,WAAWM,EAAC,MAAIA,MAAGC;AAAE,WAAM;AAAG,OAAIE,KAAE,GAAEL,KAAEJ,GAAE,IAAI,WAAW,EAAEM,EAAC,GAAE,OAAKF,MAAGE,KAAEC,MAAGE,MAAG;AAAG,IAAAA,MAAIL,KAAEJ,GAAE,IAAI,WAAW,EAAEM,EAAC;AAAE,SAAM,EAAEG,KAAE,KAAGH,KAAEC,MAAG,CAAC,GAAGH,EAAC,OAAKD,OAAII,KAAEP,GAAE,eAAeO,IAAED,EAAC,IAAGI,KAAEV,GAAE,cAAcO,IAAE,IAAGD,EAAC,KAAGA,MAAG,GAAGN,GAAE,IAAI,WAAWU,KAAE,CAAC,CAAC,MAAIH,KAAEG,KAAGV,GAAE,OAAKC,KAAE,IAAGI,KAAEL,GAAE,KAAK,gBAAe,MAAI,OAAOS,EAAC,GAAE,CAAC,GAAG,SAAO,WAAW,MAAM,GAAEA,EAAC,GAAEJ,GAAE,MAAI,CAACJ,IAAED,GAAE,IAAI,IAAGK,KAAEL,GAAE,KAAK,UAAS,IAAG,CAAC,GAAG,UAAQA,GAAE,IAAI,MAAMM,IAAEC,EAAC,EAAE,KAAM,GAACF,GAAE,MAAI,CAACJ,IAAED,GAAE,IAAI,GAAEK,GAAE,WAAS,CAAA,IAAIA,KAAEL,GAAE,KAAK,iBAAgB,MAAI,OAAOS,EAAC,GAAE,EAAE,GAAG,SAAO,WAAW,MAAM,GAAEA,EAAC,IAAG;AAAG,GAAE,CAAC,aAAY,aAAY,YAAY,CAAC,GAAE,CAAC,YAAW,SAAST,IAAEC,IAAEC,IAAE;AAAC,MAAIC,IAAEC,IAAEK,IAAEC,IAAEL,IAAEC,IAAEC,IAAEC,IAAEG,IAAEc,IAAEb,KAAEX,KAAE,GAAEyB,KAAE1B,GAAE,GAAG,MAAM,MAAM,SAAS,WAAW;AAAE,MAAGA,GAAE,OAAOC,EAAC,IAAED,GAAE,aAAW;AAAE,WAAM;AAAG,OAAIyB,KAAEzB,GAAE,YAAWA,GAAE,aAAW,aAAYY,KAAEV,MAAG,CAACF,GAAE,QAAQY,EAAC,GAAEA;AAAI,QAAG,EAAEZ,GAAE,OAAOY,EAAC,IAAEZ,GAAE,YAAU,IAAG;AAAC,UAAGA,GAAE,OAAOY,EAAC,KAAGZ,GAAE,cAAYM,KAAEN,GAAE,OAAOY,EAAC,IAAEZ,GAAE,OAAOY,EAAC,MAAIL,KAAEP,GAAE,OAAOY,EAAC,OAAK,QAAMD,KAAEX,GAAE,IAAI,WAAWM,EAAC,MAAI,OAAKK,QAAKL,KAAEN,GAAE,UAAUM,IAAEK,EAAC,IAAGL,KAAEN,GAAE,WAAWM,EAAC,MAAIC,KAAG;AAAC,QAAAC,KAAE,OAAKG,KAAE,IAAE;AAAE;AAAA,MAAK;AAAC,UAAG,EAAEX,GAAE,OAAOY,EAAC,IAAE,IAAG;AAAC,aAAIR,KAAE,OAAGK,KAAE,GAAEC,KAAEgB,GAAE,QAAOjB,KAAEC,IAAED;AAAI,cAAGiB,GAAEjB,EAAC,EAAET,IAAEY,IAAEV,IAAE,IAAE,GAAE;AAAC,YAAAE,KAAE;AAAG;AAAA,UAAK;AAAC,YAAGA;AAAE;AAAA,MAAK;AAAA,IAAC;AAAC,SAAM,CAAC,CAACI,OAAIL,KAAEH,GAAE,SAASC,IAAEW,IAAEZ,GAAE,WAAU,KAAE,EAAE,KAAI,GAAGA,GAAE,OAAKY,KAAE,IAAGP,KAAEL,GAAE,KAAK,gBAAe,MAAI,OAAOQ,EAAC,GAAE,CAAC,GAAG,SAAO,OAAO,aAAaG,EAAC,GAAEN,GAAE,MAAI,CAACJ,IAAED,GAAE,IAAI,IAAGK,KAAEL,GAAE,KAAK,UAAS,IAAG,CAAC,GAAG,UAAQG,IAAEE,GAAE,MAAI,CAACJ,IAAED,GAAE,OAAK,CAAC,GAAEK,GAAE,WAAS,CAAA,IAAIA,KAAEL,GAAE,KAAK,iBAAgB,MAAI,OAAOQ,EAAC,GAAE,EAAE,GAAG,SAAO,OAAO,aAAaG,EAAC,GAAEX,GAAE,aAAWyB,IAAE;AAAG,CAAC,GAAE,CAAC,aAAY,SAASzB,IAAEC,IAAE;AAAC,MAAIC,IAAEC,IAAEC,IAAEK,IAAEC,IAAEL,IAAEC,KAAEL,KAAE,GAAEM,KAAEP,GAAE,GAAG,MAAM,MAAM,SAAS,WAAW,GAAEQ,KAAER,GAAE;AAAQ,OAAIK,KAAEL,GAAE,YAAWA,GAAE,aAAW,aAAYM,KAAEE,MAAG,CAACR,GAAE,QAAQM,EAAC,GAAEA;AAAI,QAAG,EAAEN,GAAE,OAAOM,EAAC,IAAEN,GAAE,YAAU,KAAGA,GAAE,OAAOM,EAAC,IAAE,IAAG;AAAC,WAAIH,KAAE,OAAGC,KAAE,GAAEK,KAAEF,GAAE,QAAOH,KAAEK,IAAEL;AAAI,YAAGG,GAAEH,EAAC,EAAEJ,IAAEM,IAAEE,IAAE,IAAE,GAAE;AAAC,UAAAL,KAAE;AAAG;AAAA,QAAK;AAAC,UAAGA;AAAE;AAAA,IAAK;AAAC,SAAOD,KAAEF,GAAE,SAASC,IAAEK,IAAEN,GAAE,WAAU,KAAE,EAAE,KAAM,GAACA,GAAE,OAAKM,KAAGI,KAAEV,GAAE,KAAK,kBAAiB,KAAI,CAAC,GAAG,MAAI,CAACC,IAAED,GAAE,IAAI,IAAGU,KAAEV,GAAE,KAAK,UAAS,IAAG,CAAC,GAAG,UAAQE,IAAEQ,GAAE,MAAI,CAACT,IAAED,GAAE,IAAI,GAAEU,GAAE,WAAS,CAAE,GAACA,KAAEV,GAAE,KAAK,mBAAkB,KAAI,EAAE,GAAEA,GAAE,aAAWK,IAAE;AAAE,CAAC,CAAC;AAAE,SAAS,KAAI;AAAC,OAAK,QAAM,IAAI;AAAG,WAAQL,KAAE,GAAEA,KAAE,GAAG,QAAOA;AAAI,SAAK,MAAM,KAAK,GAAGA,EAAC,EAAE,CAAC,GAAE,GAAGA,EAAC,EAAE,CAAC,GAAE,EAAC,MAAK,GAAGA,EAAC,EAAE,CAAC,KAAG,CAAE,GAAE,MAAK,EAAE,CAAC;AAAC;AAAC,GAAG,UAAU,WAAS,SAASA,IAAEC,IAAEC,IAAE;AAAC,WAAQC,IAAEC,KAAE,KAAK,MAAM,SAAS,EAAE,GAAEK,KAAEL,GAAE,QAAOM,KAAET,IAAEI,KAAE,OAAGC,KAAEN,GAAE,GAAG,QAAQ,YAAWU,KAAER,OAAIF,GAAE,OAAKU,KAAEV,GAAE,eAAeU,EAAC,GAAE,EAAEA,MAAGR,QAAK,EAAEF,GAAE,OAAOU,EAAC,IAAEV,GAAE,cAAY;AAAC,QAAGA,GAAE,SAAOM,IAAE;AAAC,MAAAN,GAAE,OAAKE;AAAE;AAAA,IAAK;AAAC,SAAIC,KAAE,GAAEA,KAAEM,MAAG,CAACL,GAAED,EAAC,EAAEH,IAAEU,IAAER,IAAE,KAAE,GAAEC;AAAI;AAAC,IAAAH,GAAE,QAAM,CAACK,IAAEL,GAAE,QAAQA,GAAE,OAAK,CAAC,MAAIK,KAAE,QAAKK,KAAEV,GAAE,QAAME,MAAGF,GAAE,QAAQU,EAAC,MAAIL,KAAE,MAAGK,MAAIV,GAAE,OAAKU;AAAA,EAAE;AAAC,GAAE,GAAG,UAAU,QAAM,SAASV,IAAEC,IAAEC,IAAEC,IAAE;AAAC,MAAIC;AAAE,EAAAJ,OAAII,KAAE,IAAI,KAAK,MAAMJ,IAAEC,IAAEC,IAAEC,EAAC,GAAE,KAAK,SAASC,IAAEA,GAAE,MAAKA,GAAE,OAAO;AAAE,GAAE,GAAG,UAAU,QAAM;AAAG,IAAI,KAAG;AAAG,SAAS,GAAGJ,IAAE;AAAC,UAAOA,IAAC;AAAA,IAAE,KAAK;AAAA,IAAG,KAAK;AAAA,IAAG,KAAK;AAAA,IAAG,KAAK;AAAA,IAAG,KAAK;AAAA,IAAG,KAAK;AAAA,IAAG,KAAK;AAAA,IAAG,KAAK;AAAA,IAAG,KAAK;AAAA,IAAG,KAAK;AAAA,IAAG,KAAK;AAAA,IAAG,KAAK;AAAA,IAAG,KAAK;AAAA,IAAG,KAAK;AAAA,IAAG,KAAK;AAAA,IAAG,KAAK;AAAA,IAAG,KAAK;AAAA,IAAG,KAAK;AAAA,IAAG,KAAK;AAAA,IAAG,KAAK;AAAA,IAAG,KAAK;AAAA,IAAI,KAAK;AAAA,IAAI,KAAK;AAAI,aAAM;AAAA,IAAG;AAAQ,aAAM;AAAA,EAAE;AAAC;AAAC,SAAQ,KAAG,2CAA0C,KAAG,EAAE,SAAQ,KAAG,EAAE,SAAQ,KAAG,CAAE,GAAC,KAAG,GAAE,KAAG,KAAI;AAAK,KAAG,KAAK,CAAC;AAAE,qCAAqC,MAAM,EAAE,EAAE,QAAS,SAASA,IAAE;AAAC,KAAGA,GAAE,WAAW,CAAC,CAAC,IAAE;AAAC,CAAC;AAAG,IAAI,KAAG,CAAA;AAAG,SAAS,GAAGA,IAAEC,IAAE;AAAC,MAAIC,IAAEC,IAAEC,IAAEK,IAAEC,IAAEL,KAAE,CAAE,GAACC,KAAEL,GAAE;AAAO,OAAIC,KAAE,GAAEA,KAAEI,IAAEJ;AAAI,aAAOE,KAAEH,GAAEC,EAAC,GAAG,UAAQ,OAAKE,GAAE,QAAMK,KAAER,GAAEG,GAAE,GAAG,IAAGM,KAAEV,GAAE,OAAOI,GAAE,KAAK,GAAG,OAAK,UAASM,GAAE,MAAI,KAAIA,GAAE,UAAQ,GAAEA,GAAE,SAAO,MAAKA,GAAE,UAAQ,KAAIA,KAAEV,GAAE,OAAOS,GAAE,KAAK,GAAG,OAAK,WAAUC,GAAE,MAAI,KAAIA,GAAE,UAAQ,IAAGA,GAAE,SAAO,MAAKA,GAAE,UAAQ,IAAG,WAASV,GAAE,OAAOS,GAAE,QAAM,CAAC,EAAE,QAAM,QAAMT,GAAE,OAAOS,GAAE,QAAM,CAAC,EAAE,WAASJ,GAAE,KAAKI,GAAE,QAAM,CAAC;AAAG,SAAKJ,GAAE,UAAQ;AAAC,SAAIF,MAAGD,KAAEG,GAAE,IAAK,KAAE,GAAEF,KAAEH,GAAE,OAAO,UAAQ,cAAYA,GAAE,OAAOG,EAAC,EAAE;AAAM,MAAAA;AAAI,IAAAD,OAAI,EAAEC,OAAIO,KAAEV,GAAE,OAAOG,EAAC,GAAEH,GAAE,OAAOG,EAAC,IAAEH,GAAE,OAAOE,EAAC,GAAEF,GAAE,OAAOE,EAAC,IAAEQ;AAAA,EAAE;AAAC;AAAC,GAAG,WAAS,SAASV,IAAEC,IAAE;AAAC,MAAIC,IAAEC,IAAEC,IAAEK,IAAEC,KAAEV,GAAE,KAAIK,KAAEL,GAAE,IAAI,WAAWU,EAAC;AAAE,MAAGT;AAAE,WAAM;AAAG,MAAG,QAAMI;AAAE,WAAM;AAAG,MAAGD,MAAGD,KAAEH,GAAE,WAAWA,GAAE,KAAI,IAAE,GAAG,QAAOS,KAAE,OAAO,aAAaJ,EAAC,GAAED,KAAE;AAAE,WAAM;AAAG,OAAIA,KAAE,MAAIJ,GAAE,KAAK,QAAO,IAAG,CAAC,EAAE,UAAQS,IAAEL,OAAKF,KAAE,GAAEA,KAAEE,IAAEF,MAAG;AAAE,IAAAF,GAAE,KAAK,QAAO,IAAG,CAAC,EAAE,UAAQS,KAAEA,IAAET,GAAE,WAAW,KAAK,EAAC,QAAOK,IAAE,QAAO,GAAE,OAAML,GAAE,OAAO,SAAO,GAAE,KAAI,IAAG,MAAKG,GAAE,UAAS,OAAMA,GAAE,UAAS,CAAC;AAAE,SAAOH,GAAE,OAAKG,GAAE,QAAO;AAAE,GAAE,GAAG,cAAY,SAASH,IAAE;AAAC,MAAIC,IAAEC,KAAEF,GAAE,aAAYG,KAAEH,GAAE,YAAY;AAAO,OAAI,GAAGA,IAAEA,GAAE,UAAU,GAAEC,KAAE,GAAEA,KAAEE,IAAEF;AAAI,IAAAC,GAAED,EAAC,KAAGC,GAAED,EAAC,EAAE,cAAY,GAAGD,IAAEE,GAAED,EAAC,EAAE,UAAU;AAAC;AAAE,IAAI,KAAG,CAAA;AAAG,SAAS,GAAGD,IAAEC,IAAE;AAAC,MAAIC,IAAEC,IAAEC,IAAEK,IAAEC,IAAEL;AAAE,OAAIH,KAAED,GAAE,SAAO,GAAEC,MAAG,GAAEA;AAAI,YAAMC,KAAEF,GAAEC,EAAC,GAAG,UAAQ,OAAKC,GAAE,UAAQ,OAAKA,GAAE,QAAMC,KAAEH,GAAEE,GAAE,GAAG,GAAEE,KAAEH,KAAE,KAAGD,GAAEC,KAAE,CAAC,EAAE,QAAMC,GAAE,MAAI,KAAGF,GAAEC,KAAE,CAAC,EAAE,WAASC,GAAE,UAAQF,GAAEC,KAAE,CAAC,EAAE,UAAQC,GAAE,QAAM,KAAGF,GAAEE,GAAE,MAAI,CAAC,EAAE,UAAQC,GAAE,QAAM,GAAEM,KAAE,OAAO,aAAaP,GAAE,MAAM,IAAGM,KAAET,GAAE,OAAOG,GAAE,KAAK,GAAG,OAAKE,KAAE,gBAAc,WAAUI,GAAE,MAAIJ,KAAE,WAAS,MAAKI,GAAE,UAAQ,GAAEA,GAAE,SAAOJ,KAAEK,KAAEA,KAAEA,IAAED,GAAE,UAAQ,KAAIA,KAAET,GAAE,OAAOI,GAAE,KAAK,GAAG,OAAKC,KAAE,iBAAe,YAAWI,GAAE,MAAIJ,KAAE,WAAS,MAAKI,GAAE,UAAQ,IAAGA,GAAE,SAAOJ,KAAEK,KAAEA,KAAEA,IAAED,GAAE,UAAQ,IAAGJ,OAAIL,GAAE,OAAOC,GAAEC,KAAE,CAAC,EAAE,KAAK,EAAE,UAAQ,IAAGF,GAAE,OAAOC,GAAEE,GAAE,MAAI,CAAC,EAAE,KAAK,EAAE,UAAQ,IAAGD;AAAK;AAAC,GAAG,WAAS,SAASF,IAAEC,IAAE;AAAC,MAAIC,IAAEC,IAAEC,KAAEJ,GAAE,KAAIS,KAAET,GAAE,IAAI,WAAWI,EAAC;AAAE,MAAGH;AAAE,WAAM;AAAG,MAAG,OAAKQ,MAAG,OAAKA;AAAE,WAAM;AAAG,OAAIN,KAAEH,GAAE,WAAWA,GAAE,KAAI,OAAKS,EAAC,GAAEP,KAAE,GAAEA,KAAEC,GAAE,QAAOD;AAAI,IAAAF,GAAE,KAAK,QAAO,IAAG,CAAC,EAAE,UAAQ,OAAO,aAAaS,EAAC,GAAET,GAAE,WAAW,KAAK,EAAC,QAAOS,IAAE,QAAON,GAAE,QAAO,OAAMH,GAAE,OAAO,SAAO,GAAE,KAAI,IAAG,MAAKG,GAAE,UAAS,OAAMA,GAAE,UAAS,CAAC;AAAE,SAAOH,GAAE,OAAKG,GAAE,QAAO;AAAE,GAAE,GAAG,cAAY,SAASH,IAAE;AAAC,MAAIC,IAAEC,KAAEF,GAAE,aAAYG,KAAEH,GAAE,YAAY;AAAO,OAAI,GAAGA,IAAEA,GAAE,UAAU,GAAEC,KAAE,GAAEA,KAAEE,IAAEF;AAAI,IAAAC,GAAED,EAAC,KAAGC,GAAED,EAAC,EAAE,cAAY,GAAGD,IAAEE,GAAED,EAAC,EAAE,UAAU;AAAC;AAAE,IAAI,KAAG,EAAE,oBAAmB,KAAG,EAAE,SAAQ,KAAG,EAAE,oBAAmB,KAAG,EAAE,SAAQ,KAAG,2IAA0I,KAAG,uDAAsD,KAAG,GAAG;AAAY,IAAI,KAAG,GAAE,KAAG,EAAE,KAAI,KAAG,EAAE,mBAAkB,KAAG,EAAE,eAAc,KAAG,wCAAuC,KAAG;AAA4B,SAAS,GAAGD,IAAEC,IAAE;AAAC,MAAIC,IAAEC,IAAEC,IAAEK,IAAEC,IAAEL,IAAEC,IAAEC,IAAEC,KAAE,CAAE,GAACG,KAAEV,GAAE;AAAO,MAAGU,IAAE;AAAC,QAAIc,KAAE,GAAEb,KAAE,IAAGc,KAAE,CAAE;AAAC,SAAIxB,KAAE,GAAEA,KAAES,IAAET;AAAI,UAAGE,KAAEH,GAAEC,EAAC,GAAEwB,GAAE,KAAK,CAAC,GAAEzB,GAAEwB,EAAC,EAAE,WAASrB,GAAE,UAAQQ,OAAIR,GAAE,QAAM,MAAIqB,KAAEvB,KAAGU,KAAER,GAAE,OAAMA,GAAE,SAAOA,GAAE,UAAQ,GAAEA,GAAE,OAAM;AAAC,aAAII,GAAE,eAAeJ,GAAE,MAAM,MAAII,GAAEJ,GAAE,MAAM,IAAE,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,IAAGM,KAAEF,GAAEJ,GAAE,MAAM,GAAGA,GAAE,OAAK,IAAE,KAAGA,GAAE,SAAO,CAAC,GAAEC,KAAEF,KAAEsB,KAAEC,GAAED,EAAC,IAAE,GAAEtB,KAAEO,IAAEP,MAAGuB,GAAEvB,EAAC,IAAE;AAAE,eAAIM,KAAER,GAAEE,EAAC,GAAG,WAASC,GAAE,UAAQK,GAAE,QAAMA,GAAE,MAAI,MAAIH,KAAE,QAAIG,GAAE,SAAOL,GAAE,UAAQK,GAAE,SAAOL,GAAE,UAAQ,KAAG,MAAIK,GAAE,SAAO,KAAG,KAAGL,GAAE,SAAO,KAAG,MAAIE,KAAE,QAAK,CAACA,KAAG;AAAC,YAAAC,KAAEJ,KAAE,KAAG,CAACF,GAAEE,KAAE,CAAC,EAAE,OAAKuB,GAAEvB,KAAE,CAAC,IAAE,IAAE,GAAEuB,GAAExB,EAAC,IAAEA,KAAEC,KAAEI,IAAEmB,GAAEvB,EAAC,IAAEI,IAAEH,GAAE,OAAK,OAAGK,GAAE,MAAIP,IAAEO,GAAE,QAAM,OAAGJ,KAAE,IAAGO,KAAE;AAAG;AAAA,UAAK;AAAC,eAAKP,OAAIG,GAAEJ,GAAE,MAAM,GAAGA,GAAE,OAAK,IAAE,MAAIA,GAAE,UAAQ,KAAG,CAAC,IAAEC;AAAA,MAAE;AAAA,EAAC;AAAC;AAAC,IAAI,KAAG,IAAG,KAAG,EAAE,cAAa,KAAG,EAAE,aAAY,KAAG,EAAE;AAAe,SAAS,GAAGL,IAAEC,IAAEC,IAAEC,IAAE;AAAC,OAAK,MAAIH,IAAE,KAAK,MAAIE,IAAE,KAAK,KAAGD,IAAE,KAAK,SAAOE,IAAE,KAAK,cAAY,MAAMA,GAAE,MAAM,GAAE,KAAK,MAAI,GAAE,KAAK,SAAO,KAAK,IAAI,QAAO,KAAK,QAAM,GAAE,KAAK,UAAQ,IAAG,KAAK,eAAa,GAAE,KAAK,QAAM,CAAA,GAAG,KAAK,aAAW,CAAA,GAAG,KAAK,mBAAiB,CAAA,GAAG,KAAK,YAAU,CAAE,GAAC,KAAK,mBAAiB,OAAG,KAAK,YAAU;AAAC;AAAC,GAAG,UAAU,cAAY,WAAU;AAAC,MAAIH,KAAE,IAAI,GAAG,QAAO,IAAG,CAAC;AAAE,SAAOA,GAAE,UAAQ,KAAK,SAAQA,GAAE,QAAM,KAAK,cAAa,KAAK,OAAO,KAAKA,EAAC,GAAE,KAAK,UAAQ,IAAGA;AAAC,GAAE,GAAG,UAAU,OAAK,SAASA,IAAEC,IAAEC,IAAE;AAAC,OAAK,WAAS,KAAK,YAAa;AAAC,MAAIC,KAAE,IAAI,GAAGH,IAAEC,IAAEC,EAAC,GAAEE,KAAE;AAAK,SAAOF,KAAE,MAAI,KAAK,SAAQ,KAAK,aAAW,KAAK,iBAAiB,IAAK,IAAEC,GAAE,QAAM,KAAK,OAAMD,KAAE,MAAI,KAAK,SAAQ,KAAK,iBAAiB,KAAK,KAAK,UAAU,GAAE,KAAK,aAAW,CAAA,GAAGE,KAAE,EAAC,YAAW,KAAK,WAAU,IAAG,KAAK,eAAa,KAAK,OAAM,KAAK,OAAO,KAAKD,EAAC,GAAE,KAAK,YAAY,KAAKC,EAAC,GAAED;AAAC,GAAE,GAAG,UAAU,aAAW,SAASH,IAAEC,IAAE;AAAC,MAAIC,IAAEC,IAAEC,IAAEK,IAAEC,IAAEL,IAAEC,IAAEC,IAAEC,IAAEG,KAAEX,IAAEyB,KAAE,MAAGb,KAAE,MAAGc,KAAE,KAAK,QAAOb,KAAE,KAAK,IAAI,WAAWb,EAAC;AAAE,OAAIE,KAAEF,KAAE,IAAE,KAAK,IAAI,WAAWA,KAAE,CAAC,IAAE,IAAGW,KAAEe,MAAG,KAAK,IAAI,WAAWf,EAAC,MAAIE;AAAG,IAAAF;AAAI,SAAOP,KAAEO,KAAEX,IAAEG,KAAEQ,KAAEe,KAAE,KAAK,IAAI,WAAWf,EAAC,IAAE,IAAGL,KAAE,GAAGJ,EAAC,KAAG,GAAG,OAAO,aAAaA,EAAC,CAAC,GAAEM,KAAE,GAAGL,EAAC,KAAG,GAAG,OAAO,aAAaA,EAAC,CAAC,GAAEE,KAAE,GAAGH,EAAC,IAAGK,KAAE,GAAGJ,EAAC,KAAGsB,KAAE,QAAGjB,OAAIH,MAAGC,OAAImB,KAAE,SAAKpB,KAAEO,KAAE,QAAGN,OAAIC,MAAGC,OAAII,KAAE,SAAKX,MAAGQ,KAAEgB,IAAEf,KAAEE,OAAIH,KAAEgB,OAAI,CAACb,MAAGN,KAAGI,KAAEE,OAAI,CAACa,MAAGjB,MAAI,EAAC,UAASC,IAAE,WAAUC,IAAE,QAAON,GAAC;AAAC,GAAE,GAAG,UAAU,QAAM;AAAG,IAAI,KAAG,IAAG,KAAG,GAAE,KAAG,CAAC,CAAC,QAAO,SAASJ,IAAEC,IAAE;AAAC,WAAQC,KAAEF,GAAE,KAAIE,KAAEF,GAAE,UAAQ,CAAC,GAAGA,GAAE,IAAI,WAAWE,EAAC,CAAC;AAAG,IAAAA;AAAI,SAAOA,OAAIF,GAAE,QAAMC,OAAID,GAAE,WAASA,GAAE,IAAI,MAAMA,GAAE,KAAIE,EAAC,IAAGF,GAAE,MAAIE,IAAE;AAAG,CAAC,GAAE,CAAC,WAAU,SAASF,IAAEC,IAAE;AAAC,MAAIC,IAAEC,IAAEC,IAAEK,IAAEC,IAAEL,IAAEC;AAAE,SAAM,CAAC,CAACN,GAAE,GAAG,QAAQ,YAAU,EAAEA,GAAE,YAAU,OAAK,GAAGE,KAAEF,GAAE,OAAK,IAAEA,GAAE,YAAU,OAAKA,GAAE,IAAI,WAAWE,EAAC,MAAI,OAAKF,GAAE,IAAI,WAAWE,KAAE,CAAC,MAAI,OAAKF,GAAE,IAAI,WAAWE,KAAE,CAAC,MAAI,CAAC,EAAEC,KAAEH,GAAE,QAAQ,MAAM,EAAE,OAAKI,KAAED,GAAE,CAAC,GAAE,CAAC,EAAEM,KAAET,GAAE,GAAG,QAAQ,aAAaA,GAAE,IAAI,MAAME,KAAEE,GAAE,MAAM,CAAC,OAAKM,MAAGA,KAAED,GAAE,KAAK,QAAQ,QAAO,EAAE,GAAEJ,KAAEL,GAAE,GAAG,cAAcU,EAAC,GAAE,CAAC,CAACV,GAAE,GAAG,aAAaK,EAAC,MAAIJ,OAAID,GAAE,UAAQA,GAAE,QAAQ,MAAM,GAAE,CAACI,GAAE,MAAM,IAAGE,KAAEN,GAAE,KAAK,aAAY,KAAI,CAAC,GAAG,QAAM,CAAC,CAAC,QAAOK,EAAC,CAAC,GAAEC,GAAE,SAAO,WAAUA,GAAE,OAAK,SAAQA,KAAEN,GAAE,KAAK,QAAO,IAAG,CAAC,GAAG,UAAQA,GAAE,GAAG,kBAAkBU,EAAC,IAAGJ,KAAEN,GAAE,KAAK,cAAa,KAAI,EAAE,GAAG,SAAO,WAAUM,GAAE,OAAK,SAAQN,GAAE,OAAKU,GAAE,SAAON,GAAE,QAAO;AAAW,CAAC,GAAE,CAAC,WAAU,SAASJ,IAAEC,IAAE;AAAC,MAAIC,IAAEC,IAAEC,IAAEK,KAAET,GAAE;AAAI,MAAG,OAAKA,GAAE,IAAI,WAAWS,EAAC;AAAE,WAAM;AAAG,MAAGP,KAAEF,GAAE,QAAQ,SAAO,GAAEG,KAAEH,GAAE,QAAO,CAACC;AAAE,QAAGC,MAAG,KAAG,OAAKF,GAAE,QAAQ,WAAWE,EAAC;AAAE,UAAGA,MAAG,KAAG,OAAKF,GAAE,QAAQ,WAAWE,KAAE,CAAC,GAAE;AAAC,aAAIE,KAAEF,KAAE,GAAEE,MAAG,KAAG,OAAKJ,GAAE,QAAQ,WAAWI,KAAE,CAAC;AAAG,UAAAA;AAAI,QAAAJ,GAAE,UAAQA,GAAE,QAAQ,MAAM,GAAEI,EAAC,GAAEJ,GAAE,KAAK,aAAY,MAAK,CAAC;AAAA,MAAC;AAAM,QAAAA,GAAE,UAAQA,GAAE,QAAQ,MAAM,GAAE,EAAE,GAAEA,GAAE,KAAK,aAAY,MAAK,CAAC;AAAA;AAAO,MAAAA,GAAE,KAAK,aAAY,MAAK,CAAC;AAAE,OAAIS,MAAIA,KAAEN,MAAG,GAAGH,GAAE,IAAI,WAAWS,EAAC,CAAC;AAAG,IAAAA;AAAI,SAAOT,GAAE,MAAIS,IAAE;AAAE,CAAC,GAAE,CAAC,UAAS,SAAST,IAAEC,IAAE;AAAC,MAAIC,IAAEC,IAAEC,IAAEK,IAAEC,IAAEL,KAAEL,GAAE,KAAIM,KAAEN,GAAE;AAAO,MAAG,OAAKA,GAAE,IAAI,WAAWK,EAAC;AAAE,WAAM;AAAG,MAAG,EAAEA,MAAGC;AAAE,WAAM;AAAG,MAAG,QAAMJ,KAAEF,GAAE,IAAI,WAAWK,EAAC,IAAG;AAAC,SAAIJ,MAAGD,GAAE,KAAK,aAAY,MAAK,CAAC,GAAEK,MAAIA,KAAEC,OAAIJ,KAAEF,GAAE,IAAI,WAAWK,EAAC,GAAE,GAAGH,EAAC;AAAI,MAAAG;AAAI,WAAOL,GAAE,MAAIK,IAAE;AAAA,EAAE;AAAC,SAAOI,KAAET,GAAE,IAAIK,EAAC,GAAEH,MAAG,SAAOA,MAAG,SAAOG,KAAE,IAAEC,OAAIH,KAAEH,GAAE,IAAI,WAAWK,KAAE,CAAC,MAAI,SAAOF,MAAG,UAAQM,MAAGT,GAAE,IAAIK,KAAE,CAAC,GAAEA,OAAKD,KAAE,OAAKK,IAAER,OAAIS,KAAEV,GAAE,KAAK,gBAAe,IAAG,CAAC,GAAEE,KAAE,OAAK,MAAI,GAAGA,EAAC,IAAEQ,GAAE,UAAQD,KAAEC,GAAE,UAAQN,IAAEM,GAAE,SAAON,IAAEM,GAAE,OAAK,WAAUV,GAAE,MAAIK,KAAE,GAAE;AAAE,CAAC,GAAE,CAAC,aAAY,SAASL,IAAEC,IAAE;AAAC,MAAIC,IAAEC,IAAEC,IAAEK,IAAEC,IAAEL,IAAEC,IAAEC,IAAEC,KAAER,GAAE;AAAI,MAAG,OAAKA,GAAE,IAAI,WAAWQ,EAAC;AAAE,WAAM;AAAG,OAAIN,KAAEM,IAAEA,MAAIL,KAAEH,GAAE,QAAOQ,KAAEL,MAAG,OAAKH,GAAE,IAAI,WAAWQ,EAAC;AAAG,IAAAA;AAAI,MAAGF,MAAGF,KAAEJ,GAAE,IAAI,MAAME,IAAEM,EAAC,GAAG,QAAOR,GAAE,qBAAmBA,GAAE,UAAUM,EAAC,KAAG,MAAIJ;AAAE,WAAOD,OAAID,GAAE,WAASI,KAAGJ,GAAE,OAAKM,IAAE;AAAG,OAAII,KAAEL,KAAEG,IAAE,QAAME,KAAEV,GAAE,IAAI,QAAQ,KAAIK,EAAC,MAAI;AAAC,SAAIA,KAAEK,KAAE,GAAEL,KAAEF,MAAG,OAAKH,GAAE,IAAI,WAAWK,EAAC;AAAG,MAAAA;AAAI,SAAIE,KAAEF,KAAEK,QAAKJ;AAAE,aAAOL,QAAKQ,KAAET,GAAE,KAAK,eAAc,QAAO,CAAC,GAAG,SAAOI,IAAEK,GAAE,UAAQT,GAAE,IAAI,MAAMQ,IAAEE,EAAC,EAAE,QAAQ,OAAM,GAAG,EAAE,QAAQ,YAAW,IAAI,IAAGV,GAAE,MAAIK,IAAE;AAAG,IAAAL,GAAE,UAAUO,EAAC,IAAEG;AAAA,EAAC;AAAC,SAAOV,GAAE,mBAAiB,MAAGC,OAAID,GAAE,WAASI,KAAGJ,GAAE,OAAKM,IAAE;AAAE,CAAC,GAAE,CAAC,iBAAgB,GAAG,QAAQ,GAAE,CAAC,YAAW,GAAG,QAAQ,GAAE,CAAC,QAAO,SAASN,IAAEC,IAAE;AAAC,MAAIC,IAAEC,IAAEC,IAAEK,IAAEC,IAAEL,IAAEC,IAAEC,IAAEC,KAAE,IAAGG,KAAE,IAAGc,KAAEzB,GAAE,KAAIY,KAAEZ,GAAE,QAAO0B,KAAE1B,GAAE,KAAIa,KAAE;AAAG,MAAG,OAAKb,GAAE,IAAI,WAAWA,GAAE,GAAG;AAAE,WAAM;AAAG,MAAGU,KAAEV,GAAE,MAAI,IAAGS,KAAET,GAAE,GAAG,QAAQ,eAAeA,IAAEA,GAAE,KAAI,IAAE,KAAG;AAAE,WAAM;AAAG,OAAIK,KAAEI,KAAE,KAAGG,MAAG,OAAKZ,GAAE,IAAI,WAAWK,EAAC,GAAE;AAAC,SAAIQ,KAAE,OAAGR,MAAIA,KAAEO,OAAIT,KAAEH,GAAE,IAAI,WAAWK,EAAC,GAAE,GAAGF,EAAC,KAAG,OAAKA,KAAGE;AAAI;AAAC,QAAGA,MAAGO;AAAE,aAAM;AAAG,QAAGc,KAAErB,KAAGC,KAAEN,GAAE,GAAG,QAAQ,qBAAqBA,GAAE,KAAIK,IAAEL,GAAE,MAAM,GAAG,IAAG;AAAC,WAAIQ,KAAER,GAAE,GAAG,cAAcM,GAAE,GAAG,GAAEN,GAAE,GAAG,aAAaQ,EAAC,IAAEH,KAAEC,GAAE,MAAIE,KAAE,IAAGkB,KAAErB,IAAEA,KAAEO,OAAIT,KAAEH,GAAE,IAAI,WAAWK,EAAC,GAAE,GAAGF,EAAC,KAAG,OAAKA,KAAGE;AAAI;AAAC,UAAGC,KAAEN,GAAE,GAAG,QAAQ,eAAeA,GAAE,KAAIK,IAAEL,GAAE,MAAM,GAAEK,KAAEO,MAAGc,OAAIrB,MAAGC,GAAE;AAAG,aAAIK,KAAEL,GAAE,KAAID,KAAEC,GAAE,KAAID,KAAEO,OAAIT,KAAEH,GAAE,IAAI,WAAWK,EAAC,GAAE,GAAGF,EAAC,KAAG,OAAKA,KAAGE;AAAI;AAAA,IAAC;AAAC,KAACA,MAAGO,MAAG,OAAKZ,GAAE,IAAI,WAAWK,EAAC,OAAKQ,KAAE,OAAIR;AAAA,EAAG;AAAC,MAAGQ,IAAE;AAAC,QAAG,WAASb,GAAE,IAAI;AAAW,aAAM;AAAG,QAAGK,KAAEO,MAAG,OAAKZ,GAAE,IAAI,WAAWK,EAAC,KAAGqB,KAAErB,KAAE,IAAGA,KAAEL,GAAE,GAAG,QAAQ,eAAeA,IAAEK,EAAC,MAAI,IAAED,KAAEJ,GAAE,IAAI,MAAM0B,IAAErB,IAAG,IAAEA,KAAEI,KAAE,KAAGJ,KAAEI,KAAE,GAAEL,OAAIA,KAAEJ,GAAE,IAAI,MAAMU,IAAED,EAAC,IAAG,EAAEF,KAAEP,GAAE,IAAI,WAAW,GAAGI,EAAC,CAAC;AAAG,aAAOJ,GAAE,MAAIyB,IAAE;AAAG,IAAAjB,KAAED,GAAE,MAAKI,KAAEJ,GAAE;AAAA,EAAK;AAAC,SAAON,OAAID,GAAE,MAAIU,IAAEV,GAAE,SAAOS,IAAET,GAAE,KAAK,aAAY,KAAI,CAAC,EAAE,QAAME,KAAE,CAAC,CAAC,QAAOM,EAAC,CAAC,GAAEG,MAAGT,GAAE,KAAK,CAAC,SAAQS,EAAC,CAAC,GAAEX,GAAE,aAAYA,GAAE,GAAG,OAAO,SAASA,EAAC,GAAEA,GAAE,aAAYA,GAAE,KAAK,cAAa,KAAI,EAAE,IAAGA,GAAE,MAAIK,IAAEL,GAAE,SAAOY,IAAE;AAAE,CAAC,GAAE,CAAC,SAAQ,SAASZ,IAAEC,IAAE;AAAC,MAAIC,IAAEC,IAAEC,IAAEK,IAAEC,IAAEL,IAAEC,IAAEC,IAAEC,IAAEG,IAAEc,IAAEb,IAAEc,IAAEb,KAAE,IAAGC,KAAEd,GAAE,KAAI2B,KAAE3B,GAAE;AAAO,MAAG,OAAKA,GAAE,IAAI,WAAWA,GAAE,GAAG;AAAE,WAAM;AAAG,MAAG,OAAKA,GAAE,IAAI,WAAWA,GAAE,MAAI,CAAC;AAAE,WAAM;AAAG,MAAGK,KAAEL,GAAE,MAAI,IAAGU,KAAEV,GAAE,GAAG,QAAQ,eAAeA,IAAEA,GAAE,MAAI,GAAE,KAAE,KAAG;AAAE,WAAM;AAAG,OAAIM,KAAEI,KAAE,KAAGiB,MAAG,OAAK3B,GAAE,IAAI,WAAWM,EAAC,GAAE;AAAC,SAAIA,MAAIA,KAAEqB,OAAIxB,KAAEH,GAAE,IAAI,WAAWM,EAAC,GAAE,GAAGH,EAAC,KAAG,OAAKA,KAAGG;AAAI;AAAC,QAAGA,MAAGqB;AAAE,aAAM;AAAG,SAAID,KAAEpB,KAAGE,KAAER,GAAE,GAAG,QAAQ,qBAAqBA,GAAE,KAAIM,IAAEN,GAAE,MAAM,GAAG,OAAKa,KAAEb,GAAE,GAAG,cAAcQ,GAAE,GAAG,GAAER,GAAE,GAAG,aAAaa,EAAC,IAAEP,KAAEE,GAAE,MAAIK,KAAE,KAAIa,KAAEpB,IAAEA,KAAEqB,OAAIxB,KAAEH,GAAE,IAAI,WAAWM,EAAC,GAAE,GAAGH,EAAC,KAAG,OAAKA,KAAGG;AAAI;AAAC,QAAGE,KAAER,GAAE,GAAG,QAAQ,eAAeA,GAAE,KAAIM,IAAEN,GAAE,MAAM,GAAEM,KAAEqB,MAAGD,OAAIpB,MAAGE,GAAE;AAAG,WAAIG,KAAEH,GAAE,KAAIF,KAAEE,GAAE,KAAIF,KAAEqB,OAAIxB,KAAEH,GAAE,IAAI,WAAWM,EAAC,GAAE,GAAGH,EAAC,KAAG,OAAKA,KAAGG;AAAI;AAAA;AAAM,MAAAK,KAAE;AAAG,QAAGL,MAAGqB,MAAG,OAAK3B,GAAE,IAAI,WAAWM,EAAC;AAAE,aAAON,GAAE,MAAIc,IAAE;AAAG,IAAAR;AAAA,EAAG,OAAK;AAAC,QAAG,WAASN,GAAE,IAAI;AAAW,aAAM;AAAG,QAAGM,KAAEqB,MAAG,OAAK3B,GAAE,IAAI,WAAWM,EAAC,KAAGoB,KAAEpB,KAAE,IAAGA,KAAEN,GAAE,GAAG,QAAQ,eAAeA,IAAEM,EAAC,MAAI,IAAEG,KAAET,GAAE,IAAI,MAAM0B,IAAEpB,IAAG,IAAEA,KAAEI,KAAE,KAAGJ,KAAEI,KAAE,GAAED,OAAIA,KAAET,GAAE,IAAI,MAAMK,IAAEK,EAAC,IAAG,EAAEH,KAAEP,GAAE,IAAI,WAAW,GAAGS,EAAC,CAAC;AAAG,aAAOT,GAAE,MAAIc,IAAE;AAAG,IAAAD,KAAEN,GAAE,MAAKI,KAAEJ,GAAE;AAAA,EAAK;AAAC,SAAON,OAAIG,KAAEJ,GAAE,IAAI,MAAMK,IAAEK,EAAC,GAAEV,GAAE,GAAG,OAAO,MAAMI,IAAEJ,GAAE,IAAGA,GAAE,KAAIY,KAAE,CAAE,CAAA,IAAGa,KAAEzB,GAAE,KAAK,SAAQ,OAAM,CAAC,GAAG,QAAME,KAAE,CAAC,CAAC,OAAMW,EAAC,GAAE,CAAC,OAAM,EAAE,CAAC,GAAEY,GAAE,WAASb,IAAEa,GAAE,UAAQrB,IAAEO,MAAGT,GAAE,KAAK,CAAC,SAAQS,EAAC,CAAC,IAAGX,GAAE,MAAIM,IAAEN,GAAE,SAAO2B,IAAE;AAAE,CAAC,GAAE,CAAC,YAAW,SAAS3B,IAAEC,IAAE;AAAC,MAAIC,IAAEC,IAAEC,IAAEK,IAAEC,IAAEL,IAAEC,KAAEN,GAAE;AAAI,MAAG,OAAKA,GAAE,IAAI,WAAWM,EAAC;AAAE,WAAM;AAAG,OAAII,KAAEV,GAAE,KAAIK,KAAEL,GAAE,YAAS;AAAC,QAAG,EAAEM,MAAGD;AAAE,aAAM;AAAG,QAAG,QAAMI,KAAET,GAAE,IAAI,WAAWM,EAAC;AAAG,aAAM;AAAG,QAAG,OAAKG;AAAE;AAAA,EAAK;AAAC,SAAOP,KAAEF,GAAE,IAAI,MAAMU,KAAE,GAAEJ,EAAC,GAAE,GAAG,KAAKJ,EAAC,KAAGC,KAAEH,GAAE,GAAG,cAAcE,EAAC,GAAE,CAAC,CAACF,GAAE,GAAG,aAAaG,EAAC,MAAIF,QAAKG,KAAEJ,GAAE,KAAK,aAAY,KAAI,CAAC,GAAG,QAAM,CAAC,CAAC,QAAOG,EAAC,CAAC,GAAEC,GAAE,SAAO,YAAWA,GAAE,OAAK,SAAQA,KAAEJ,GAAE,KAAK,QAAO,IAAG,CAAC,GAAG,UAAQA,GAAE,GAAG,kBAAkBE,EAAC,IAAGE,KAAEJ,GAAE,KAAK,cAAa,KAAI,EAAE,GAAG,SAAO,YAAWI,GAAE,OAAK,SAAQJ,GAAE,OAAKE,GAAE,SAAO,GAAE,SAAK,CAAC,CAAC,GAAG,KAAKA,EAAC,MAAIC,KAAEH,GAAE,GAAG,cAAc,YAAUE,EAAC,GAAE,CAAC,CAACF,GAAE,GAAG,aAAaG,EAAC,MAAIF,QAAKG,KAAEJ,GAAE,KAAK,aAAY,KAAI,CAAC,GAAG,QAAM,CAAC,CAAC,QAAOG,EAAC,CAAC,GAAEC,GAAE,SAAO,YAAWA,GAAE,OAAK,SAAQA,KAAEJ,GAAE,KAAK,QAAO,IAAG,CAAC,GAAG,UAAQA,GAAE,GAAG,kBAAkBE,EAAC,IAAGE,KAAEJ,GAAE,KAAK,cAAa,KAAI,EAAE,GAAG,SAAO,YAAWI,GAAE,OAAK,SAAQJ,GAAE,OAAKE,GAAE,SAAO,GAAE;AAAI,CAAC,GAAE,CAAC,eAAc,SAASF,IAAEC,IAAE;AAAC,MAAIC,IAAEC,IAAEC,IAAEK,IAAEC,IAAEL,KAAEL,GAAE;AAAI,SAAM,CAAC,CAACA,GAAE,GAAG,QAAQ,SAAOI,KAAEJ,GAAE,QAAO,EAAE,OAAKA,GAAE,IAAI,WAAWK,EAAC,KAAGA,KAAE,KAAGD,QAAK,EAAE,QAAMF,KAAEF,GAAE,IAAI,WAAWK,KAAE,CAAC,MAAI,OAAKH,MAAG,OAAKA,MAAG,CAAC,SAASF,IAAE;AAAC,QAAIC,KAAE,KAAGD;AAAE,WAAOC,MAAG,MAAIA,MAAG;AAAA,EAAG,EAAEC,EAAC,OAAK,CAAC,EAAEC,KAAEH,GAAE,IAAI,MAAMK,EAAC,EAAE,MAAM,EAAE,OAAKJ,QAAKQ,KAAET,GAAE,KAAK,eAAc,IAAG,CAAC,GAAG,UAAQA,GAAE,IAAI,MAAMK,IAAEA,KAAEF,GAAE,CAAC,EAAE,MAAM,GAAEO,KAAED,GAAE,SAAQ,YAAY,KAAKC,EAAC,KAAGV,GAAE,aAAY,SAASA,IAAE;AAAC,WAAM,aAAa,KAAKA,EAAC;AAAA,EAAC,EAAES,GAAE,OAAO,KAAGT,GAAE,cAAaA,GAAE,OAAKG,GAAE,CAAC,EAAE,QAAO;AAAM,CAAC,GAAE,CAAC,UAAS,SAASH,IAAEC,IAAE;AAAC,MAAIC,IAAEC,IAAEC,IAAEK,KAAET,GAAE,KAAIU,KAAEV,GAAE;AAAO,MAAG,OAAKA,GAAE,IAAI,WAAWS,EAAC;AAAE,WAAM;AAAG,MAAGA,KAAE,KAAGC;AAAE,WAAM;AAAG,MAAG,OAAKV,GAAE,IAAI,WAAWS,KAAE,CAAC,GAAE;AAAC,QAAGN,KAAEH,GAAE,IAAI,MAAMS,EAAC,EAAE,MAAM,EAAE;AAAE,aAAOR,OAAIC,KAAE,QAAMC,GAAE,CAAC,EAAE,CAAC,EAAE,YAAa,IAAC,SAASA,GAAE,CAAC,EAAE,MAAM,CAAC,GAAE,EAAE,IAAE,SAASA,GAAE,CAAC,GAAE,EAAE,IAAGC,KAAEJ,GAAE,KAAK,gBAAe,IAAG,CAAC,GAAG,UAAQ,GAAGE,EAAC,IAAE,GAAGA,EAAC,IAAE,GAAG,KAAK,GAAEE,GAAE,SAAOD,GAAE,CAAC,GAAEC,GAAE,OAAK,WAAUJ,GAAE,OAAKG,GAAE,CAAC,EAAE,QAAO;AAAA,EAAE,YAAUA,KAAEH,GAAE,IAAI,MAAMS,EAAC,EAAE,MAAM,EAAE,MAAI,GAAG,IAAGN,GAAE,CAAC,CAAC;AAAE,WAAOF,QAAKG,KAAEJ,GAAE,KAAK,gBAAe,IAAG,CAAC,GAAG,UAAQ,GAAGG,GAAE,CAAC,CAAC,GAAEC,GAAE,SAAOD,GAAE,CAAC,GAAEC,GAAE,OAAK,WAAUJ,GAAE,OAAKG,GAAE,CAAC,EAAE,QAAO;AAAG,SAAM;AAAE,CAAC,CAAC,GAAE,KAAG,CAAC,CAAC,iBAAgB,SAASH,IAAE;AAAC,MAAIC,IAAEC,KAAEF,GAAE,aAAYG,KAAEH,GAAE,YAAY;AAAO,OAAI,GAAG,GAAEA,GAAE,UAAU,GAAEC,KAAE,GAAEA,KAAEE,IAAEF;AAAI,IAAAC,GAAED,EAAC,KAAGC,GAAED,EAAC,EAAE,cAAY,GAAG,GAAEC,GAAED,EAAC,EAAE,UAAU;AAAC,CAAC,GAAE,CAAC,iBAAgB,GAAG,WAAW,GAAE,CAAC,YAAW,GAAG,WAAW,GAAE,CAAC,kBAAiB,SAASD,IAAE;AAAC,MAAIC,IAAEC,IAAEC,KAAE,GAAEC,KAAEJ,GAAE,QAAOS,KAAET,GAAE,OAAO;AAAO,OAAIC,KAAEC,KAAE,GAAED,KAAEQ,IAAER;AAAI,IAAAG,GAAEH,EAAC,EAAE,UAAQ,KAAGE,MAAIC,GAAEH,EAAC,EAAE,QAAME,IAAEC,GAAEH,EAAC,EAAE,UAAQ,KAAGE,MAAI,WAASC,GAAEH,EAAC,EAAE,QAAMA,KAAE,IAAEQ,MAAG,WAASL,GAAEH,KAAE,CAAC,EAAE,OAAKG,GAAEH,KAAE,CAAC,EAAE,UAAQG,GAAEH,EAAC,EAAE,UAAQG,GAAEH,KAAE,CAAC,EAAE,WAASA,OAAIC,OAAIE,GAAEF,EAAC,IAAEE,GAAEH,EAAC,IAAGC;AAAK,EAAAD,OAAIC,OAAIE,GAAE,SAAOF;AAAE,CAAC,CAAC;AAAE,SAAS,KAAI;AAAC,MAAIF;AAAE,OAAI,KAAK,QAAM,IAAI,MAAGA,KAAE,GAAEA,KAAE,GAAG,QAAOA;AAAI,SAAK,MAAM,KAAK,GAAGA,EAAC,EAAE,CAAC,GAAE,GAAGA,EAAC,EAAE,CAAC,CAAC;AAAE,OAAI,KAAK,SAAO,IAAI,MAAGA,KAAE,GAAEA,KAAE,GAAG,QAAOA;AAAI,SAAK,OAAO,KAAK,GAAGA,EAAC,EAAE,CAAC,GAAE,GAAGA,EAAC,EAAE,CAAC,CAAC;AAAC;AAAC,GAAG,UAAU,YAAU,SAASA,IAAE;AAAC,MAAIC,IAAEC,IAAEC,KAAEH,GAAE,KAAII,KAAE,KAAK,MAAM,SAAS,EAAE,GAAEK,KAAEL,GAAE,QAAOM,KAAEV,GAAE,GAAG,QAAQ,YAAWK,KAAEL,GAAE;AAAM,MAAG,WAASK,GAAEF,EAAC,GAAE;AAAC,QAAGH,GAAE,QAAMU;AAAE,WAAIR,KAAE,GAAEA,KAAEO,OAAIT,GAAE,SAAQC,KAAEG,GAAEF,EAAC,EAAEF,IAAE,IAAE,GAAEA,GAAE,SAAQ,CAACC,KAAGC;AAAI;AAAA;AAAM,MAAAF,GAAE,MAAIA,GAAE;AAAO,IAAAC,MAAGD,GAAE,OAAMK,GAAEF,EAAC,IAAEH,GAAE;AAAA,EAAG;AAAM,IAAAA,GAAE,MAAIK,GAAEF,EAAC;AAAC,GAAE,GAAG,UAAU,WAAS,SAASH,IAAE;AAAC,WAAQC,IAAEC,IAAEC,KAAE,KAAK,MAAM,SAAS,EAAE,GAAEC,KAAED,GAAE,QAAOM,KAAET,GAAE,QAAOU,KAAEV,GAAE,GAAG,QAAQ,YAAWA,GAAE,MAAIS,MAAG;AAAC,QAAGT,GAAE,QAAMU;AAAE,WAAIR,KAAE,GAAEA,KAAEE,MAAG,EAAEH,KAAEE,GAAED,EAAC,EAAEF,IAAE,KAAE,IAAGE;AAAI;AAAC,QAAGD,IAAE;AAAC,UAAGD,GAAE,OAAKS;AAAE;AAAA,IAAK;AAAM,MAAAT,GAAE,WAASA,GAAE,IAAIA,GAAE,KAAK;AAAA,EAAC;AAAC,EAAAA,GAAE,WAASA,GAAE,YAAW;AAAE,GAAE,GAAG,UAAU,QAAM,SAASA,IAAEC,IAAEC,IAAEC,IAAE;AAAC,MAAIC,IAAEK,IAAEC,IAAEL,KAAE,IAAI,KAAK,MAAML,IAAEC,IAAEC,IAAEC,EAAC;AAAE,OAAI,KAAK,SAASE,EAAC,GAAEK,MAAGD,KAAE,KAAK,OAAO,SAAS,EAAE,GAAG,QAAOL,KAAE,GAAEA,KAAEM,IAAEN;AAAI,IAAAK,GAAEL,EAAC,EAAEC,EAAC;AAAC,GAAE,GAAG,UAAU,QAAM;AAAG,IAAI,KAAG;AAAG,SAAS,GAAGL,IAAE;AAAC,MAAIC,KAAE,MAAM,UAAU,MAAM,KAAK,WAAU,CAAC;AAAE,SAAOA,GAAE,QAAS,SAASA,IAAE;AAAC,IAAAA,MAAG,OAAO,KAAKA,EAAC,EAAE,QAAS,SAASC,IAAE;AAAC,MAAAF,GAAEE,EAAC,IAAED,GAAEC,EAAC;AAAA,IAAC,CAAG;AAAA,EAAA,IAAIF;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,SAAO,OAAO,UAAU,SAAS,KAAKA,EAAC;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,SAAM,wBAAsB,GAAGA,EAAC;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,SAAOA,GAAE,QAAQ,wBAAuB,MAAM;AAAC;AAAC,IAAI,KAAG,EAAC,WAAU,MAAG,YAAW,MAAG,SAAQ,MAAE;AAAE,IAAI,KAAG,EAAC,SAAQ,EAAC,UAAS,SAASA,IAAEC,IAAEC,IAAE;AAAC,MAAIC,KAAEH,GAAE,MAAMC,EAAC;AAAE,SAAOC,GAAE,GAAG,SAAOA,GAAE,GAAG,OAAK,IAAI,OAAO,YAAUA,GAAE,GAAG,WAASA,GAAE,GAAG,uBAAqBA,GAAE,GAAG,UAAS,GAAG,IAAGA,GAAE,GAAG,KAAK,KAAKC,EAAC,IAAEA,GAAE,MAAMD,GAAE,GAAG,IAAI,EAAE,CAAC,EAAE,SAAO;AAAC,EAAC,GAAE,UAAS,SAAQ,QAAO,SAAQ,MAAK,EAAC,UAAS,SAASF,IAAEC,IAAEC,IAAE;AAAC,MAAIC,KAAEH,GAAE,MAAMC,EAAC;AAAE,SAAOC,GAAE,GAAG,YAAUA,GAAE,GAAG,UAAQ,IAAI,OAAO,MAAIA,GAAE,GAAG,WAAS,wBAAsBA,GAAE,GAAG,aAAW,WAASA,GAAE,GAAG,kBAAgB,MAAIA,GAAE,GAAG,WAASA,GAAE,GAAG,sBAAoBA,GAAE,GAAG,UAAS,GAAG,IAAGA,GAAE,GAAG,QAAQ,KAAKC,EAAC,IAAEF,MAAG,KAAG,QAAMD,GAAEC,KAAE,CAAC,KAAGA,MAAG,KAAG,QAAMD,GAAEC,KAAE,CAAC,IAAE,IAAEE,GAAE,MAAMD,GAAE,GAAG,OAAO,EAAE,CAAC,EAAE,SAAO;AAAC,EAAC,GAAE,WAAU,EAAC,UAAS,SAASF,IAAEC,IAAEC,IAAE;AAAC,MAAIC,KAAEH,GAAE,MAAMC,EAAC;AAAE,SAAOC,GAAE,GAAG,WAASA,GAAE,GAAG,SAAO,IAAI,OAAO,MAAIA,GAAE,GAAG,iBAAe,MAAIA,GAAE,GAAG,iBAAgB,GAAG,IAAGA,GAAE,GAAG,OAAO,KAAKC,EAAC,IAAEA,GAAE,MAAMD,GAAE,GAAG,MAAM,EAAE,CAAC,EAAE,SAAO;AAAC,EAAC,EAAC,GAAE,KAAG,8EAA8E,MAAM,GAAG;AAAE,SAAS,GAAGF,IAAE;AAAC,MAAIC,KAAED,GAAE,KAAG,SAASA,IAAE;AAAC,QAAIC,KAAE,CAAE;AAAC,WAAOD,KAAEA,MAAG,CAAE,GAACC,GAAE,UAAQ,EAAE,QAAOA,GAAE,SAAO,EAAE,QAAOA,GAAE,QAAM,EAAE,QAAOA,GAAE,QAAM,EAAE,QAAOA,GAAE,WAAS,CAACA,GAAE,OAAMA,GAAE,OAAMA,GAAE,MAAM,EAAE,KAAK,GAAG,GAAEA,GAAE,UAAQ,CAACA,GAAE,OAAMA,GAAE,MAAM,EAAE,KAAK,GAAG,GAAEA,GAAE,oBAAkB,iBAAeA,GAAE,WAAS,MAAIA,GAAE,UAAQ,KAAIA,GAAE,UAAQ,0FAAyFA,GAAE,WAAS,cAAYA,GAAE,UAAQ,wBAAuBA,GAAE,WAAS,mFAAkFA,GAAE,sBAAoB,gBAAcA,GAAE,WAAS,UAAQD,GAAE,KAAK,IAAE,aAAW,QAAM,yBAAuBC,GAAE,WAAS,MAAKA,GAAE,WAAS,mBAAiBA,GAAE,UAAQ,4CAA2CA,GAAE,UAAQ,0BAAwBA,GAAE,UAAQ,0BAAwBA,GAAE,UAAQ,0BAAwBA,GAAE,UAAQ,0BAAyBA,GAAE,UAAQ,uBAAqBA,GAAE,oBAAkB,uCAAqCA,GAAE,UAAQ,cAAYD,GAAE,KAAK,IAAE,+BAA6B,WAAS,SAAOC,GAAE,UAAQ,aAAWA,GAAE,UAAQ,gBAAcA,GAAE,UAAQ,mBAAiBA,GAAE,UAAQ,mBAAkBA,GAAE,iBAAe,kEAAiEA,GAAE,SAAO,yBAAwBA,GAAE,kBAAgB,QAAMA,GAAE,SAAO,MAAIA,GAAE,oBAAkB,WAAUA,GAAE,aAAW,QAAMA,GAAE,SAAO,SAAOA,GAAE,oBAAkB,UAAQA,GAAE,oBAAkB,UAAQA,GAAE,oBAAkB,YAAUA,GAAE,oBAAkB,MAAKA,GAAE,WAAS,iBAAeA,GAAE,aAAW,WAASA,GAAE,aAAW,MAAKA,GAAE,iBAAe,QAAMA,GAAE,UAAQ,eAAaA,GAAE,aAAW,sBAAqBA,GAAE,uBAAqB,cAAYA,GAAE,aAAW,qBAAoBA,GAAE,kBAAgBA,GAAE,WAASA,GAAE,qBAAoBA,GAAE,wBAAsBA,GAAE,iBAAeA,GAAE,qBAAoBA,GAAE,uBAAqBA,GAAE,WAASA,GAAE,WAASA,GAAE,qBAAoBA,GAAE,6BAA2BA,GAAE,iBAAeA,GAAE,WAASA,GAAE,qBAAoBA,GAAE,mCAAiCA,GAAE,uBAAqBA,GAAE,WAASA,GAAE,qBAAoBA,GAAE,sBAAoB,wDAAsDA,GAAE,WAAS,UAASA,GAAE,kBAAgB,oBAAkBA,GAAE,UAAQ,OAAKA,GAAE,iBAAe,MAAIA,GAAE,wBAAsB,KAAIA,GAAE,iBAAe,qCAAmCA,GAAE,WAAS,uBAAqBA,GAAE,6BAA2BA,GAAE,WAAS,KAAIA,GAAE,uBAAqB,qCAAmCA,GAAE,WAAS,uBAAqBA,GAAE,mCAAiCA,GAAE,WAAS,KAAIA;AAAA,EAAC,EAAED,GAAE,QAAQ,GAAEE,KAAEF,GAAE,SAAS,MAAO;AAAC,WAASI,GAAEJ,IAAE;AAAC,WAAOA,GAAE,QAAQ,UAASC,GAAE,QAAQ;AAAA,EAAC;AAAC,EAAAD,GAAE,UAAW,GAACA,GAAE,qBAAmBE,GAAE,KAAK,yVAAyV,GAAEA,GAAE,KAAKD,GAAE,MAAM,GAAEA,GAAE,WAASC,GAAE,KAAK,GAAG,GAAED,GAAE,cAAY,OAAOG,GAAEH,GAAE,eAAe,GAAE,GAAG,GAAEA,GAAE,aAAW,OAAOG,GAAEH,GAAE,cAAc,GAAE,GAAG,GAAEA,GAAE,mBAAiB,OAAOG,GAAEH,GAAE,oBAAoB,GAAE,GAAG,GAAEA,GAAE,kBAAgB,OAAOG,GAAEH,GAAE,mBAAmB,GAAE,GAAG;AAAE,MAAIQ,KAAE,CAAA;AAAG,WAASC,GAAEV,IAAEC,IAAE;AAAC,UAAM,IAAI,MAAM,iCAA+BD,KAAE,QAAMC,EAAC;AAAA,EAAC;AAAC,EAAAD,GAAE,eAAa,CAAE,GAAC,OAAO,KAAKA,GAAE,WAAW,EAAE,QAAS,SAASC,IAAE;AAAC,QAAIC,KAAEF,GAAE,YAAYC,EAAC;AAAE,QAAG,SAAOC,IAAE;AAAC,UAAIC,KAAE,EAAC,UAAS,MAAK,MAAK,KAAI;AAAE,UAAGH,GAAE,aAAaC,EAAC,IAAEE,IAAE,sBAAoB,GAAGD,EAAC;AAAE,eAAM,CAAC,SAASF,IAAE;AAAC,iBAAM,sBAAoB,GAAGA,EAAC;AAAA,QAAC,EAAEE,GAAE,QAAQ,IAAE,GAAGA,GAAE,QAAQ,IAAEC,GAAE,WAASD,GAAE,WAASQ,GAAET,IAAEC,EAAC,IAAEC,GAAE,WAAS,yBAASH,IAAE;AAAC,iBAAO,SAASC,IAAEC,IAAE;AAAC,gBAAIC,KAAEF,GAAE,MAAMC,EAAC;AAAE,mBAAOF,GAAE,KAAKG,EAAC,IAAEA,GAAE,MAAMH,EAAC,EAAE,CAAC,EAAE,SAAO;AAAA,UAAC;AAAA,QAAC,EAAEE,GAAE,QAAQ,GAAE,MAAK,GAAGA,GAAE,SAAS,IAAEC,GAAE,YAAUD,GAAE,YAAUA,GAAE,YAAUQ,GAAET,IAAEC,EAAC,IAAEC,GAAE,YAAU,SAASH,IAAEC,IAAE;AAAC,UAAAA,GAAE,UAAUD,EAAC;AAAA,QAAC;AAAG,OAAC,SAASA,IAAE;AAAC,eAAM,sBAAoB,GAAGA,EAAC;AAAA,MAAC,EAAEE,EAAC,IAAEQ,GAAET,IAAEC,EAAC,IAAEO,GAAE,KAAKR,EAAC;AAAA,IAAC;AAAA,EAAC,CAAG,GAACQ,GAAE,QAAS,SAASR,IAAE;AAAC,IAAAD,GAAE,aAAaA,GAAE,YAAYC,EAAC,CAAC,MAAID,GAAE,aAAaC,EAAC,EAAE,WAASD,GAAE,aAAaA,GAAE,YAAYC,EAAC,CAAC,EAAE,UAASD,GAAE,aAAaC,EAAC,EAAE,YAAUD,GAAE,aAAaA,GAAE,YAAYC,EAAC,CAAC,EAAE;AAAA,EAAU,CAAC,GAAGD,GAAE,aAAa,EAAE,IAAE,EAAC,UAAS,MAAK,WAAU,SAASA,IAAEC,IAAE;AAAC,IAAAA,GAAE,UAAUD,EAAC;AAAA,EAAC,EAAC;AAAE,MAAIK,KAAE,OAAO,KAAKL,GAAE,YAAY,EAAE,OAAQ,SAASC,IAAE;AAAC,WAAOA,GAAE,SAAO,KAAGD,GAAE,aAAaC,EAAC;AAAA,EAAC,CAAG,EAAC,IAAI,EAAE,EAAE,KAAK,GAAG;AAAE,EAAAD,GAAE,GAAG,cAAY,OAAO,sBAAoBC,GAAE,WAAS,QAAMI,KAAE,KAAI,GAAG,GAAEL,GAAE,GAAG,gBAAc,OAAO,sBAAoBC,GAAE,WAAS,QAAMI,KAAE,KAAI,IAAI,GAAEL,GAAE,GAAG,kBAAgB,OAAO,MAAIA,GAAE,GAAG,cAAc,QAAO,GAAG,GAAEA,GAAE,GAAG,UAAQ,OAAO,MAAIA,GAAE,GAAG,YAAY,SAAO,QAAMA,GAAE,GAAG,gBAAgB,SAAO,OAAM,GAAG,GAAE,SAASA,IAAE;AAAC,IAAAA,GAAE,YAAU,IAAGA,GAAE,iBAAe;AAAA,EAAE,EAAEA,EAAC;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAE;AAAC,MAAIC,KAAEF,GAAE,WAAUG,KAAEH,GAAE,gBAAeI,KAAEJ,GAAE,eAAe,MAAME,IAAEC,EAAC;AAAE,OAAK,SAAOH,GAAE,WAAW,YAAa,GAAC,KAAK,QAAME,KAAED,IAAE,KAAK,YAAUE,KAAEF,IAAE,KAAK,MAAIG,IAAE,KAAK,OAAKA,IAAE,KAAK,MAAIA;AAAC;AAAC,SAAS,GAAGJ,IAAEC,IAAE;AAAC,MAAIC,KAAE,IAAI,GAAGF,IAAEC,EAAC;AAAE,SAAOD,GAAE,aAAaE,GAAE,MAAM,EAAE,UAAUA,IAAEF,EAAC,GAAEE;AAAC;AAAC,SAAS,GAAGF,IAAEC,IAAE;AAAC,MAAG,EAAE,gBAAgB;AAAI,WAAO,IAAI,GAAGD,IAAEC,EAAC;AAAE,MAAIC;AAAE,EAAAD,OAAIC,KAAEF,IAAE,OAAO,KAAKE,MAAG,CAAA,CAAE,EAAE,OAAQ,SAASF,IAAEC,IAAE;AAAC,WAAOD,MAAG,GAAG,eAAeC,EAAC;AAAA,EAAC,GAAG,KAAE,MAAIA,KAAED,IAAEA,KAAE,CAAA,KAAK,KAAK,WAAS,GAAG,CAAE,GAAC,IAAGC,EAAC,GAAE,KAAK,YAAU,IAAG,KAAK,iBAAe,IAAG,KAAK,aAAW,IAAG,KAAK,iBAAe,IAAG,KAAK,cAAY,GAAG,CAAA,GAAG,IAAGD,EAAC,GAAE,KAAK,eAAa,IAAG,KAAK,WAAS,IAAG,KAAK,oBAAkB,OAAG,KAAK,KAAG,CAAA,GAAG,GAAG,IAAI;AAAC;AAAC,GAAG,UAAU,MAAI,SAASA,IAAEC,IAAE;AAAC,SAAO,KAAK,YAAYD,EAAC,IAAEC,IAAE,GAAG,IAAI,GAAE;AAAI,GAAE,GAAG,UAAU,MAAI,SAASD,IAAE;AAAC,SAAO,KAAK,WAAS,GAAG,KAAK,UAASA,EAAC,GAAE;AAAI,GAAE,GAAG,UAAU,OAAK,SAASA,IAAE;AAAC,MAAG,KAAK,iBAAeA,IAAE,KAAK,YAAU,IAAG,CAACA,GAAE;AAAO,WAAM;AAAG,MAAIC,IAAEC,IAAEC,IAAEC,IAAEK,IAAEC,IAAEL,IAAEC;AAAE,MAAG,KAAK,GAAG,YAAY,KAAKN,EAAC;AAAE,UAAKK,KAAE,KAAK,GAAG,eAAe,YAAU,GAAE,UAAQJ,KAAEI,GAAE,KAAKL,EAAC;AAAI,UAAGI,KAAE,KAAK,aAAaJ,IAAEC,GAAE,CAAC,GAAEI,GAAE,SAAS,GAAE;AAAC,aAAK,aAAWJ,GAAE,CAAC,GAAE,KAAK,YAAUA,GAAE,QAAMA,GAAE,CAAC,EAAE,QAAO,KAAK,iBAAeA,GAAE,QAAMA,GAAE,CAAC,EAAE,SAAOG;AAAE;AAAA,MAAK;AAAA;AAAC,SAAO,KAAK,SAAS,aAAW,KAAK,aAAa,OAAO,MAAIE,KAAEN,GAAE,OAAO,KAAK,GAAG,eAAe,MAAI,MAAI,KAAK,YAAU,KAAGM,KAAE,KAAK,cAAY,UAAQJ,KAAEF,GAAE,MAAM,KAAK,SAAS,UAAQ,KAAK,GAAG,aAAW,KAAK,GAAG,gBAAgB,OAAKS,KAAEP,GAAE,QAAMA,GAAE,CAAC,EAAE,SAAQ,KAAK,YAAU,KAAGO,KAAE,KAAK,eAAa,KAAK,aAAW,IAAG,KAAK,YAAUA,IAAE,KAAK,iBAAeP,GAAE,QAAMA,GAAE,CAAC,EAAE,UAAS,KAAK,SAAS,cAAY,KAAK,aAAa,SAAS,KAAGF,GAAE,QAAQ,GAAG,KAAG,KAAG,UAAQG,KAAEH,GAAE,MAAM,KAAK,GAAG,WAAW,OAAKS,KAAEN,GAAE,QAAMA,GAAE,CAAC,EAAE,QAAOO,KAAEP,GAAE,QAAMA,GAAE,CAAC,EAAE,SAAQ,KAAK,YAAU,KAAGM,KAAE,KAAK,aAAWA,OAAI,KAAK,aAAWC,KAAE,KAAK,oBAAkB,KAAK,aAAW,WAAU,KAAK,YAAUD,IAAE,KAAK,iBAAeC,MAAI,KAAK,aAAW;AAAC,GAAE,GAAG,UAAU,UAAQ,SAASV,IAAE;AAAC,SAAO,KAAK,GAAG,QAAQ,KAAKA,EAAC;AAAC,GAAE,GAAG,UAAU,eAAa,SAASA,IAAEC,IAAEC,IAAE;AAAC,SAAO,KAAK,aAAaD,GAAE,YAAW,CAAE,IAAE,KAAK,aAAaA,GAAE,YAAW,CAAE,EAAE,SAASD,IAAEE,IAAE,IAAI,IAAE;AAAC,GAAE,GAAG,UAAU,QAAM,SAASF,IAAE;AAAC,MAAIC,KAAE,GAAEC,KAAE,CAAA;AAAG,OAAK,aAAW,KAAG,KAAK,mBAAiBF,OAAIE,GAAE,KAAK,GAAG,MAAKD,EAAC,CAAC,GAAEA,KAAE,KAAK;AAAgB,WAAQE,KAAEF,KAAED,GAAE,MAAMC,EAAC,IAAED,IAAE,KAAK,KAAKG,EAAC;AAAG,IAAAD,GAAE,KAAK,GAAG,MAAKD,EAAC,CAAC,GAAEE,KAAEA,GAAE,MAAM,KAAK,cAAc,GAAEF,MAAG,KAAK;AAAe,SAAOC,GAAE,SAAOA,KAAE;AAAI,GAAE,GAAG,UAAU,eAAa,SAASF,IAAE;AAAC,MAAG,KAAK,iBAAeA,IAAE,KAAK,YAAU,IAAG,CAACA,GAAE;AAAO,WAAO;AAAK,MAAIC,KAAE,KAAK,GAAG,gBAAgB,KAAKD,EAAC;AAAE,MAAG,CAACC;AAAE,WAAO;AAAK,MAAIC,KAAE,KAAK,aAAaF,IAAEC,GAAE,CAAC,GAAEA,GAAE,CAAC,EAAE,MAAM;AAAE,SAAOC,MAAG,KAAK,aAAWD,GAAE,CAAC,GAAE,KAAK,YAAUA,GAAE,QAAMA,GAAE,CAAC,EAAE,QAAO,KAAK,iBAAeA,GAAE,QAAMA,GAAE,CAAC,EAAE,SAAOC,IAAE,GAAG,MAAK,CAAC,KAAG;AAAI,GAAE,GAAG,UAAU,OAAK,SAASF,IAAEC,IAAE;AAAC,SAAOD,KAAE,MAAM,QAAQA,EAAC,IAAEA,KAAE,CAACA,EAAC,GAAEC,MAAG,KAAK,WAAS,KAAK,SAAS,OAAOD,EAAC,EAAE,KAAI,EAAG,OAAQ,SAASA,IAAEC,IAAEC,IAAE;AAAC,WAAOF,OAAIE,GAAED,KAAE,CAAC;AAAA,EAAC,CAAC,EAAG,QAAS,GAAC,GAAG,IAAI,GAAE,SAAO,KAAK,WAASD,GAAE,MAAK,GAAG,KAAK,oBAAkB,MAAG,GAAG,IAAI,GAAE;AAAK,GAAE,GAAG,UAAU,YAAU,SAASA,IAAE;AAAC,EAAAA,GAAE,WAASA,GAAE,MAAI,YAAUA,GAAE,MAAK,cAAYA,GAAE,UAAQ,YAAY,KAAKA,GAAE,GAAG,MAAIA,GAAE,MAAI,YAAUA,GAAE;AAAI,GAAE,GAAG,UAAU,YAAU,WAAU;AAAE;AAAC,IAAI,KAAG,IAAG,KAAG,YAAW,KAAG,SAAQ,KAAG,gBAAe,KAAG,6BAA4B,KAAG,EAAC,UAAS,mDAAkD,aAAY,kDAAiD,iBAAgB,gBAAe,GAAE,KAAG,KAAK,OAAM,KAAG,OAAO;AACvkmF;AAAkD,SAAS,GAAGA,IAAE;AAAC,QAAM,IAAI,WAAW,GAAGA,EAAC,CAAC;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAE;AAAC,WAAQC,KAAEF,GAAE,QAAOG,KAAE,CAAE,GAACD;AAAK,IAAAC,GAAED,EAAC,IAAED,GAAED,GAAEE,EAAC,CAAC;AAAE,SAAOC;AAAC;AAAC,SAAS,GAAGH,IAAEC,IAAE;AAAC,MAAIC,KAAEF,GAAE,MAAM,GAAG,GAAEG,KAAE;AAAG,SAAOD,GAAE,SAAO,MAAIC,KAAED,GAAE,CAAC,IAAE,KAAIF,KAAEE,GAAE,CAAC,IAAGC,KAAE,IAAIH,KAAEA,GAAE,QAAQ,IAAG,GAAG,GAAG,MAAM,GAAG,GAAEC,EAAC,EAAE,KAAK,GAAG;AAAC;AAAC,SAAS,GAAGD,IAAE;AAAC,WAAQC,IAAEC,IAAEC,KAAE,CAAE,GAACC,KAAE,GAAEK,KAAET,GAAE,QAAOI,KAAEK;AAAG,KAACR,KAAED,GAAE,WAAWI,IAAG,MAAI,SAAOH,MAAG,SAAOG,KAAEK,KAAE,UAAQ,SAAOP,KAAEF,GAAE,WAAWI,IAAG,MAAID,GAAE,OAAO,OAAKF,OAAI,OAAK,OAAKC,MAAG,KAAK,KAAGC,GAAE,KAAKF,EAAC,GAAEG,QAAKD,GAAE,KAAKF,EAAC;AAAE,SAAOE;AAAC;AAAC,SAAS,GAAGH,IAAE;AAAC,SAAO,GAAGA,IAAG,SAASA,IAAE;AAAC,QAAIC,KAAE;AAAG,WAAOD,KAAE,UAAQC,MAAG,IAAID,MAAG,WAAS,KAAG,OAAK,KAAK,GAAEA,KAAE,QAAM,OAAKA,KAAGC,MAAG,GAAGD,EAAC;AAAA,EAAC,CAAG,EAAC,KAAK,EAAE;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAE;AAAC,SAAOD,KAAE,KAAG,MAAIA,KAAE,QAAM,KAAGC,OAAI;AAAE;AAAC,SAAS,GAAGD,IAAEC,IAAEC,IAAE;AAAC,MAAIC,KAAE;AAAE,OAAIH,KAAEE,KAAE,GAAGF,KAAE,GAAG,IAAEA,MAAG,GAAEA,MAAG,GAAGA,KAAEC,EAAC,GAAED,KAAE,KAAIG,MAAG;AAAG,IAAAH,KAAE,GAAGA,KAAE,EAAE;AAAE,SAAO,GAAGG,KAAE,KAAGH,MAAGA,KAAE,GAAG;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,MAAIC,IAAEC,IAAEC,IAAEC,IAAEK,IAAEC,IAAEL,IAAEC,IAAEC,IAAEC,IAAEG,IAAEc,KAAE,CAAE,GAACb,KAAEZ,GAAE,QAAO0B,KAAE,GAAEb,KAAE,KAAIC,KAAE;AAAG,QAAKZ,KAAEF,GAAE,YAAY,GAAG,KAAG,MAAIE,KAAE,IAAGC,KAAE,GAAEA,KAAED,IAAE,EAAEC;AAAE,IAAAH,GAAE,WAAWG,EAAC,KAAG,OAAK,GAAG,WAAW,GAAEsB,GAAE,KAAKzB,GAAE,WAAWG,EAAC,CAAC;AAAE,OAAIC,KAAEF,KAAE,IAAEA,KAAE,IAAE,GAAEE,KAAEQ,MAAG;AAAC,SAAIH,KAAEiB,IAAEhB,KAAE,GAAEL,KAAE,IAAGD,MAAGQ,MAAG,GAAG,eAAe,KAAIN,MAAGK,KAAEX,GAAE,WAAWI,IAAG,KAAG,KAAG,KAAGO,KAAE,KAAGA,KAAE,KAAG,KAAGA,KAAE,KAAGA,KAAE,KAAG,KAAGA,KAAE,KAAG,OAAK,MAAIL,KAAE,IAAI,KAAGoB,MAAGhB,EAAC,MAAI,GAAG,UAAU,GAAEgB,MAAGpB,KAAEI,IAAE,EAAEJ,MAAGC,KAAEF,MAAGS,KAAE,IAAET,MAAGS,KAAE,KAAG,KAAGT,KAAES,MAAIT,MAAG;AAAG,MAAAK,KAAE,GAAG,MAAIF,KAAE,KAAGD,GAAE,KAAG,GAAG,UAAU,GAAEG,MAAGF;AAAE,IAAAM,KAAE,GAAGY,KAAEjB,IAAER,KAAEwB,GAAE,SAAO,GAAE,KAAGhB,EAAC,GAAE,GAAGiB,KAAEzB,EAAC,IAAE,KAAGY,MAAG,GAAG,UAAU,GAAEA,MAAG,GAAGa,KAAEzB,EAAC,GAAEyB,MAAGzB,IAAEwB,GAAE,OAAOC,MAAI,GAAEb,EAAC;AAAA,EAAC;AAAC,SAAO,GAAGY,EAAC;AAAC;AAAC,SAAS,GAAGzB,IAAE;AAAC,MAAIC,IAAEC,IAAEC,IAAEC,IAAEK,IAAEC,IAAEL,IAAEC,IAAEC,IAAEC,IAAEG,IAAEc,IAAEb,IAAEc,IAAEb,IAAEC,KAAE,CAAE;AAAC,OAAIW,MAAGzB,KAAE,GAAGA,EAAC,GAAG,QAAOC,KAAE,KAAIC,KAAE,GAAEO,KAAE,IAAGC,KAAE,GAAEA,KAAEe,IAAE,EAAEf;AAAE,KAACC,KAAEX,GAAEU,EAAC,KAAG,OAAKI,GAAE,KAAK,GAAGH,EAAC,CAAC;AAAE,OAAIR,KAAEC,KAAEU,GAAE,QAAOV,MAAGU,GAAE,KAAK,GAAG,GAAEX,KAAEsB,MAAG;AAAC,SAAIpB,KAAE,IAAGK,KAAE,GAAEA,KAAEe,IAAE,EAAEf;AAAE,OAACC,KAAEX,GAAEU,EAAC,MAAIT,MAAGU,KAAEN,OAAIA,KAAEM;AAAG,SAAIN,KAAEJ,KAAE,IAAI,KAAGC,OAAIU,KAAET,KAAE,EAAE,KAAG,GAAG,UAAU,GAAED,OAAIG,KAAEJ,MAAGW,IAAEX,KAAEI,IAAEK,KAAE,GAAEA,KAAEe,IAAE,EAAEf;AAAE,WAAIC,KAAEX,GAAEU,EAAC,KAAGT,MAAG,EAAEC,KAAE,MAAI,GAAG,UAAU,GAAES,MAAGV,IAAE;AAAC,aAAIK,KAAEJ,IAAEK,KAAE,IAAG,EAAED,MAAGE,KAAED,MAAGE,KAAE,IAAEF,MAAGE,KAAE,KAAG,KAAGF,KAAEE,MAAIF,MAAG;AAAG,UAAAM,KAAEP,KAAEE,IAAEkB,KAAE,KAAGlB,IAAEM,GAAE,KAAK,GAAG,GAAGN,KAAEK,KAAEa,IAAE,CAAC,CAAC,CAAC,GAAEpB,KAAE,GAAGO,KAAEa,EAAC;AAAE,QAAAZ,GAAE,KAAK,GAAG,GAAGR,IAAE,CAAC,CAAC,CAAC,GAAEG,KAAE,GAAGP,IAAEU,IAAET,MAAGC,EAAC,GAAEF,KAAE,GAAE,EAAEC;AAAA,MAAC;AAAC,MAAED,IAAE,EAAED;AAAA,EAAC;AAAC,SAAOa,GAAE,KAAK,EAAE;AAAC;AAAC,SAAS,GAAGd,IAAE;AAAC,SAAO,GAAGA,IAAG,SAASA,IAAE;AAAC,WAAO,GAAG,KAAKA,EAAC,IAAE,GAAGA,GAAE,MAAM,CAAC,EAAE,YAAa,CAAA,IAAEA;AAAA,EAAC,CAAG;AAAA;AAAC,SAAS,GAAGA,IAAE;AAAC,SAAO,GAAGA,IAAG,SAASA,IAAE;AAAC,WAAO,GAAG,KAAKA,EAAC,IAAE,SAAO,GAAGA,EAAC,IAAEA;AAAA,EAAC,CAAC;AAAE;AAAC,IAAI,KAAG,EAAC,QAAO,IAAG,QAAO,GAAE,GAAE,KAAG,EAAC,SAAQ,SAAQ,MAAK,IAAG,SAAQ,IAAG,WAAU,IAAG,QAAO,IAAG,QAAO,GAAE,GAAE,KAAG,GAAE,KAAG,GAAE,KAAG,GAAE,KAAG,IAAG,KAAG,IAAG,KAAG,IAAG,KAAG,IAAG,KAAG,GAAE,KAAG,EAAE,OAAO,OAAO,EAAC,WAAU,MAAK,QAAO,IAAG,QAAO,IAAG,WAAU,IAAG,SAAQ,IAAG,SAAQ,SAAQ,MAAK,IAAG,SAAQ,GAAE,CAAC,CAAC,GAAE,KAAG,EAAC,SAAQ,EAAC,SAAQ,EAAC,MAAK,OAAG,UAAS,OAAG,QAAO,OAAG,YAAW,aAAY,SAAQ,OAAG,aAAY,OAAG,QAAO,QAAO,WAAU,MAAK,YAAW,IAAG,GAAE,YAAW,EAAC,MAAK,CAAA,GAAG,OAAM,IAAG,QAAO,CAAE,EAAA,EAAC,GAAE,MAAK,EAAC,SAAQ,EAAC,MAAK,OAAG,UAAS,OAAG,QAAO,OAAG,YAAW,aAAY,SAAQ,OAAG,aAAY,OAAG,QAAO,QAAO,WAAU,MAAK,YAAW,GAAE,GAAE,YAAW,EAAC,MAAK,EAAC,OAAM,CAAC,aAAY,SAAQ,UAAS,WAAW,EAAC,GAAE,OAAM,EAAC,OAAM,CAAC,WAAW,EAAC,GAAE,QAAO,EAAC,OAAM,CAAC,MAAM,GAAE,QAAO,CAAC,iBAAgB,gBAAgB,EAAC,EAAC,EAAC,GAAE,YAAW,EAAC,SAAQ,EAAC,MAAK,MAAG,UAAS,MAAG,QAAO,OAAG,YAAW,aAAY,SAAQ,OAAG,aAAY,OAAG,QAAO,QAAO,WAAU,MAAK,YAAW,GAAE,GAAE,YAAW,EAAC,MAAK,EAAC,OAAM,CAAC,aAAY,SAAQ,UAAS,WAAW,EAAC,GAAE,OAAM,EAAC,OAAM,CAAC,cAAa,QAAO,SAAQ,WAAU,MAAK,cAAa,YAAW,QAAO,aAAY,WAAW,EAAC,GAAE,QAAO,EAAC,OAAM,CAAC,YAAW,aAAY,YAAW,UAAS,UAAS,eAAc,SAAQ,QAAO,WAAU,MAAM,GAAE,QAAO,CAAC,iBAAgB,YAAW,gBAAgB,EAAC,EAAC,EAAC,EAAC,GAAE,KAAG,qCAAoC,KAAG;AAAoC,SAAS,GAAGA,IAAE;AAAC,MAAIC,KAAED,GAAE,KAAI,EAAG;AAAc,SAAM,CAAC,GAAG,KAAKC,EAAC,KAAG,CAAC,CAAC,GAAG,KAAKA,EAAC;AAAC;AAAC,IAAI,KAAG,CAAC,SAAQ,UAAS,SAAS;AAAE,SAAS,GAAGD,IAAE;AAAC,MAAIC,KAAE,GAAG,MAAMD,IAAE,IAAE;AAAE,MAAGC,GAAE,aAAW,CAACA,GAAE,YAAU,GAAG,QAAQA,GAAE,QAAQ,KAAG;AAAG,QAAG;AAAC,MAAAA,GAAE,WAAS,GAAG,QAAQA,GAAE,QAAQ;AAAA,IAAC,SAAOD,IAAE;AAAA,IAAE;AAAA,SAAO,GAAG,OAAO,GAAG,OAAOC,EAAC,CAAC;AAAC;AAAC,SAAS,GAAGD,IAAE;AAAC,MAAIC,KAAE,GAAG,MAAMD,IAAE,IAAE;AAAE,MAAGC,GAAE,aAAW,CAACA,GAAE,YAAU,GAAG,QAAQA,GAAE,QAAQ,KAAG;AAAG,QAAG;AAAC,MAAAA,GAAE,WAAS,GAAG,UAAUA,GAAE,QAAQ;AAAA,IAAC,SAAOD,IAAE;AAAA,IAAA;AAAE,SAAO,GAAG,OAAO,GAAG,OAAOC,EAAC,GAAE,GAAG,OAAO,eAAa,GAAG;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAE;AAAC,MAAG,EAAE,gBAAgB;AAAI,WAAO,IAAI,GAAGD,IAAEC,EAAC;AAAE,EAAAA,MAAG,GAAG,SAASD,EAAC,MAAIC,KAAED,MAAG,CAAA,GAAGA,KAAE,YAAW,KAAK,SAAO,IAAI,MAAG,KAAK,QAAM,IAAI,MAAG,KAAK,OAAK,IAAI,MAAG,KAAK,WAAS,IAAI,MAAG,KAAK,UAAQ,IAAI,MAAG,KAAK,eAAa,IAAG,KAAK,gBAAc,IAAG,KAAK,oBAAkB,IAAG,KAAK,QAAM,IAAG,KAAK,UAAQ,GAAG,OAAO,CAAA,GAAG,EAAE,GAAE,KAAK,UAAQ,CAAE,GAAC,KAAK,UAAUA,EAAC,GAAEC,MAAG,KAAK,IAAIA,EAAC;AAAC;AAAC,GAAG,UAAU,MAAI,SAASD,IAAE;AAAC,SAAO,GAAG,OAAO,KAAK,SAAQA,EAAC,GAAE;AAAI,GAAE,GAAG,UAAU,YAAU,SAASA,IAAE;AAAC,MAAIC,IAAEC,KAAE;AAAK,MAAG,GAAG,SAASF,EAAC,KAAG,EAAEA,KAAE,GAAGC,KAAED,EAAC;AAAG,UAAM,IAAI,MAAM,iCAA+BC,KAAE,eAAe;AAAE,MAAG,CAACD;AAAE,UAAM,IAAI,MAAM,4CAA4C;AAAE,SAAOA,GAAE,WAASE,GAAE,IAAIF,GAAE,OAAO,GAAEA,GAAE,cAAY,OAAO,KAAKA,GAAE,UAAU,EAAE,QAAS,SAASC,IAAE;AAAC,IAAAD,GAAE,WAAWC,EAAC,EAAE,SAAOC,GAAED,EAAC,EAAE,MAAM,WAAWD,GAAE,WAAWC,EAAC,EAAE,KAAK,GAAED,GAAE,WAAWC,EAAC,EAAE,UAAQC,GAAED,EAAC,EAAE,OAAO,WAAWD,GAAE,WAAWC,EAAC,EAAE,MAAM;AAAA,EAAC,CAAG,GAAC;AAAI,GAAE,GAAG,UAAU,SAAO,SAASD,IAAEC,IAAE;AAAC,MAAIC,KAAE;AAAG,QAAM,QAAQF,EAAC,MAAIA,KAAE,CAACA,EAAC,IAAG,CAAC,QAAO,SAAQ,QAAQ,EAAE,QAAS,SAASC,IAAE;AAAC,IAAAC,KAAEA,GAAE,OAAO,KAAKD,EAAC,EAAE,MAAM,OAAOD,IAAE,IAAE,CAAC;AAAA,EAAC,GAAG,IAAI,GAAEE,KAAEA,GAAE,OAAO,KAAK,OAAO,OAAO,OAAOF,IAAE,IAAE,CAAC;AAAE,MAAIG,KAAEH,GAAE,OAAQ,SAASA,IAAE;AAAC,WAAOE,GAAE,QAAQF,EAAC,IAAE;AAAA,EAAC,CAAG;AAAC,MAAGG,GAAE,UAAQ,CAACF;AAAE,UAAM,IAAI,MAAM,mDAAiDE,EAAC;AAAE,SAAO;AAAI,GAAE,GAAG,UAAU,UAAQ,SAASH,IAAEC,IAAE;AAAC,MAAIC,KAAE;AAAG,QAAM,QAAQF,EAAC,MAAIA,KAAE,CAACA,EAAC,IAAG,CAAC,QAAO,SAAQ,QAAQ,EAAE,QAAS,SAASC,IAAE;AAAC,IAAAC,KAAEA,GAAE,OAAO,KAAKD,EAAC,EAAE,MAAM,QAAQD,IAAE,IAAE,CAAC;AAAA,EAAC,GAAG,IAAI,GAAEE,KAAEA,GAAE,OAAO,KAAK,OAAO,OAAO,QAAQF,IAAE,IAAE,CAAC;AAAE,MAAIG,KAAEH,GAAE,OAAQ,SAASA,IAAE;AAAC,WAAOE,GAAE,QAAQF,EAAC,IAAE;AAAA,EAAC,CAAG;AAAC,MAAGG,GAAE,UAAQ,CAACF;AAAE,UAAM,IAAI,MAAM,oDAAkDE,EAAC;AAAE,SAAO;AAAI,GAAE,GAAG,UAAU,MAAI,SAASH,IAAE;AAAC,MAAIC,KAAE,CAAC,IAAI,EAAE,OAAO,MAAM,UAAU,MAAM,KAAK,WAAU,CAAC,CAAC;AAAE,SAAOD,GAAE,MAAMA,IAAEC,EAAC,GAAE;AAAI,GAAE,GAAG,UAAU,QAAM,SAASD,IAAEC,IAAE;AAAC,MAAG,YAAU,OAAOD;AAAE,UAAM,IAAI,MAAM,+BAA+B;AAAE,MAAIE,KAAE,IAAI,KAAK,KAAK,MAAMF,IAAE,MAAKC,EAAC;AAAE,SAAO,KAAK,KAAK,QAAQC,EAAC,GAAEA,GAAE;AAAM,GAAE,GAAG,UAAU,SAAO,SAASF,IAAEC,IAAE;AAAC,SAAOA,KAAEA,MAAG,IAAG,KAAK,SAAS,OAAO,KAAK,MAAMD,IAAEC,EAAC,GAAE,KAAK,SAAQA,EAAC;AAAC,GAAE,GAAG,UAAU,cAAY,SAASD,IAAEC,IAAE;AAAC,MAAIC,KAAE,IAAI,KAAK,KAAK,MAAMF,IAAE,MAAKC,EAAC;AAAE,SAAOC,GAAE,aAAW,MAAG,KAAK,KAAK,QAAQA,EAAC,GAAEA,GAAE;AAAM,GAAE,GAAG,UAAU,eAAa,SAASF,IAAEC,IAAE;AAAC,SAAOA,KAAEA,MAAG,CAAE,GAAC,KAAK,SAAS,OAAO,KAAK,YAAYD,IAAEC,EAAC,GAAE,KAAK,SAAQA,EAAC;AAAC;AAAK,IAAC,KAAG;;"}