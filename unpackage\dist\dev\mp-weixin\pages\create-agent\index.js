"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const api_index = require("../../api/index.js");
const stores_user = require("../../stores/user.js");
const api_common = require("../../api/common.js");
const _sfc_main = {
  __name: "index",
  setup(__props) {
    const userStore = stores_user.useUserStore();
    const agentForm = common_vendor.reactive({
      merchantGuid: userStore.merchantGuid,
      agentName: "",
      agentType: 1,
      // 默认内部类型
      categoryGuid: "",
      // 需要从分类列表获取
      promptContent: "",
      agentDesc: "",
      agentAvatar: "",
      isPaid: 0,
      // 默认免费
      price: "",
      trial_chat_count: 0,
      // 试用聊天次数
      isPublic: 1,
      // 默认公开
      agentConfig: {
        // model: 'gpt-4',
        // temperature: 0.7,
        // max_tokens: 2000,
        // api_key: '',
        // workflow_id: '',
        deploy_address: "",
        coze_sign: "",
        secret_token: "",
        secret_key_type: "default"
      },
      knowledgeBaseIds: [],
      welcomeMessage: "",
      commonQuestions: ["", "", ""]
      // 默认3个问题
    });
    const creating = common_vendor.ref(false);
    const uploading = common_vendor.ref(false);
    const generating = common_vendor.ref(false);
    const isEditMode = common_vendor.ref(false);
    const editGuid = common_vendor.ref("");
    const selectedCategory = common_vendor.ref(null);
    const categoryList = common_vendor.ref([]);
    const showCategoryModal = common_vendor.ref(false);
    const tempSelectedCategory = common_vendor.ref(null);
    const searchKeyword = common_vendor.ref("");
    const filteredCategories = common_vendor.computed(() => {
      if (!searchKeyword.value) {
        return categoryList.value;
      }
      return categoryList.value.filter(
        (item) => item.categoryName.toLowerCase().includes(searchKeyword.value.toLowerCase())
      );
    });
    const agentTypes = common_vendor.ref([
      { label: "内部", value: 1 },
      { label: "dify", value: 2 },
      { label: "coze", value: 3 }
      // { label: '阿里云百炼', value: 4 }
    ]);
    const secretTypes = common_vendor.ref([
      { label: "自定义输入", value: "default" },
      { label: "读取用户保存密钥", value: "user" }
    ]);
    common_vendor.ref("default");
    const visibilityTypes = common_vendor.ref([
      { label: "免费", value: 0 },
      { label: "付费", value: 1 }
    ]);
    const onSecret = () => {
      common_vendor.index.__f__("log", "at pages/create-agent/index.vue:299", "跳转秘钥管理");
      common_vendor.index.navigateTo({
        url: "/pages/create-agent/secret"
      });
    };
    const chooseAvatar = () => {
      if (uploading.value)
        return;
      common_vendor.index.chooseImage({
        count: 1,
        sizeType: ["compressed"],
        sourceType: ["album", "camera"],
        success: async (res) => {
          const tempFilePath = res.tempFilePaths[0];
          await uploadAvatar(tempFilePath);
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/create-agent/index.vue:318", "选择图片失败:", err);
        }
      });
    };
    const uploadAvatar = async (filePath) => {
      try {
        uploading.value = true;
        common_vendor.index.showLoading({
          title: "上传中...",
          mask: true
        });
        const uploadRes = await api_common.updataFileFun(filePath);
        const result = JSON.parse(uploadRes.data);
        if (result.code === 0) {
          agentForm.agentAvatar = result.data;
          common_vendor.index.showToast({
            title: "头像上传成功",
            icon: "success"
          });
        } else {
          throw new Error(result.msg || "上传失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/create-agent/index.vue:345", "上传头像失败:", error);
        common_vendor.index.showToast({
          title: "上传失败",
          icon: "none"
        });
      } finally {
        uploading.value = false;
        common_vendor.index.hideLoading();
      }
    };
    const onCreateLogo = async () => {
      if (!agentForm.agentName.trim()) {
        common_vendor.index.showToast({
          title: "请先输入智能体名称",
          icon: "none"
        });
        return;
      }
      if (!agentForm.agentDesc.trim()) {
        common_vendor.index.showToast({
          title: "请先输入智能体描述",
          icon: "none"
        });
        return;
      }
      if (generating.value)
        return;
      try {
        generating.value = true;
        common_vendor.index.showLoading({
          title: "AI生成中...",
          mask: true
        });
        const generateData = {
          merchantGuid: userStore.merchantGuid,
          agentName: agentForm.agentName.trim(),
          agentDesc: agentForm.agentDesc.trim()
        };
        const res = await api_index.generateAvatarApi(generateData);
        if (res.code === 0) {
          agentForm.agentAvatar = res.data.data.imageUrl;
          common_vendor.index.showToast({
            title: "头像生成成功",
            icon: "success"
          });
        } else {
          throw new Error(res.msg || "生成失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/create-agent/index.vue:401", "AI生成头像失败:", error);
        common_vendor.index.showToast({
          title: error.message || "生成失败",
          icon: "none"
        });
      } finally {
        generating.value = false;
        common_vendor.index.hideLoading();
      }
    };
    const selectAgentType = (value) => {
      agentForm.agentType = value;
    };
    const selectVisibility = (value) => {
      agentForm.isPaid = value;
    };
    const selectSecretType = (value) => {
      agentForm.agentConfig.secret_key_type = value;
    };
    const getCategoryList = async () => {
      try {
        const res = await api_index.getCategoryListApi({
          merchantGuid: userStore.merchantGuid
        });
        if (res.code === 0) {
          categoryList.value = res.data || [];
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/create-agent/index.vue:434", "获取分类列表失败:", error);
      }
    };
    const showCategoryPicker = () => {
      if (categoryList.value.length === 0) {
        getCategoryList();
      }
      showCategoryModal.value = true;
    };
    const closeCategoryModal = () => {
      showCategoryModal.value = false;
      searchKeyword.value = "";
    };
    const handleSearch = () => {
    };
    const clearSearch = () => {
      searchKeyword.value = "";
    };
    const selectTempCategory = (category) => {
      tempSelectedCategory.value = category;
    };
    const confirmSelectCategory = () => {
      if (!tempSelectedCategory.value) {
        common_vendor.index.showToast({
          title: "请选择分类",
          icon: "none"
        });
        return;
      }
      selectedCategory.value = tempSelectedCategory.value;
      agentForm.categoryGuid = tempSelectedCategory.value.guid;
      closeCategoryModal();
    };
    const getAgentDetail = async (guid) => {
      try {
        common_vendor.index.showLoading({
          title: "加载中...",
          mask: true
        });
        const res = await api_index.getMyDetailApi({ guid });
        if (res.code === 0) {
          const agentData = res.data;
          agentForm.agentName = agentData.agentName || "";
          agentForm.agentType = agentData.agentType || 1;
          agentForm.categoryGuid = agentData.categoryGuid || "";
          agentForm.promptContent = agentData.promptContent || "";
          agentForm.agentDesc = agentData.agentDesc || "";
          agentForm.agentAvatar = agentData.agentAvatar || "";
          agentForm.isPaid = agentData.isPaid || 0;
          agentForm.price = agentData.price || "";
          agentForm.trial_chat_count = agentData.trialChatCount || 0;
          agentForm.isPublic = agentData.isPublic || 1;
          agentForm.agentConfig = agentData.agentConfig || { secret_token: "", secret_key_type: "default", deploy_address: "" };
          agentForm.knowledgeBaseIds = agentData.knowledgeBaseIds || [];
          agentForm.welcomeMessage = agentData.welcomeMessage || "";
          agentForm.commonQuestions = agentData.commonQuestions.length > 0 ? agentData.commonQuestions : ["", "", ""];
          if (agentData.category) {
            selectedCategory.value = agentData.category;
          }
          editGuid.value = guid;
        } else {
          throw new Error(res.msg || "获取智能体详情失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/create-agent/index.vue:525", "获取智能体详情失败:", error);
        common_vendor.index.showToast({
          title: error.message || "获取智能体详情失败",
          icon: "none"
        });
        setTimeout(() => {
          common_vendor.index.navigateBack();
        }, 1500);
      } finally {
        common_vendor.index.hideLoading();
      }
    };
    common_vendor.onLoad((options) => {
      if (options.guid) {
        isEditMode.value = true;
        getAgentDetail(options.guid);
      }
    });
    common_vendor.onMounted(() => {
      getCategoryList();
    });
    const handleCreate = async () => {
      if (creating.value)
        return;
      if (!agentForm.categoryGuid) {
        common_vendor.index.showToast({
          title: "请选择分类",
          icon: "none"
        });
        return;
      }
      if (!agentForm.agentName.trim()) {
        common_vendor.index.showToast({
          title: "请输入智能体名称",
          icon: "none"
        });
        return;
      }
      try {
        creating.value = true;
        common_vendor.index.showLoading({
          title: isEditMode.value ? "更新中..." : "创建中...",
          mask: true
        });
        if (agentForm.isPaid === 1 && !agentForm.price) {
          common_vendor.index.showToast({
            title: "付费智能体请输入价格",
            icon: "none"
          });
          creating.value = false;
          common_vendor.index.hideLoading();
          return;
        }
        const filteredQuestions = agentForm.commonQuestions.filter((q) => q.trim());
        const submitData = {
          agentName: agentForm.agentName.trim(),
          agentType: agentForm.agentType,
          categoryGuid: agentForm.categoryGuid,
          promptContent: agentForm.promptContent.trim(),
          agentDesc: agentForm.agentDesc.trim(),
          agentAvatar: agentForm.agentAvatar,
          isPaid: agentForm.isPaid,
          price: agentForm.isPaid === 1 ? parseFloat(agentForm.price) : 0,
          isPublic: 1,
          // 暂时都设为公开
          trial_chat_count: agentForm.trial_chat_count,
          // 试用聊天次数
          agentConfig: agentForm.agentConfig,
          knowledgeBaseIds: agentForm.knowledgeBaseIds,
          welcomeMessage: agentForm.welcomeMessage.trim(),
          commonQuestions: filteredQuestions
        };
        if (isEditMode.value) {
          submitData.guid = editGuid.value;
          common_vendor.index.__f__("log", "at pages/create-agent/index.vue:622", "更新智能体数据:", submitData);
          const res = await api_index.updateMyAgentApi(submitData);
          if (res.code === 0) {
            common_vendor.index.showToast({
              title: "更新成功",
              icon: "success"
            });
            setTimeout(() => {
              common_vendor.index.navigateBack();
            }, 1500);
          } else {
            throw new Error(res.msg || "更新失败");
          }
        } else {
          submitData.merchantGuid = agentForm.merchantGuid;
          common_vendor.index.__f__("log", "at pages/create-agent/index.vue:638", "创建智能体数据:", submitData);
          const res = await api_index.createAgentApi(submitData);
          if (res.code === 0) {
            common_vendor.index.showToast({
              title: "创建成功",
              icon: "success"
            });
            setTimeout(() => {
              common_vendor.index.navigateBack();
            }, 1500);
          } else {
            throw new Error(res.msg || "创建失败");
          }
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/create-agent/index.vue:655", isEditMode.value ? "更新失败:" : "创建失败:", error);
        common_vendor.index.showToast({
          title: error.msg || (isEditMode.value ? "更新失败" : "创建失败"),
          icon: "none"
        });
      } finally {
        creating.value = false;
        common_vendor.index.hideLoading();
      }
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: agentForm.agentAvatar,
        b: !generating.value
      }, !generating.value ? {
        c: common_assets._imports_0$1
      } : {}, {
        d: generating.value
      }, generating.value ? {} : {}, {
        e: common_vendor.o(chooseAvatar),
        f: common_vendor.t(generating.value ? "AI生成中..." : "AI生成形象"),
        g: common_vendor.o(onCreateLogo),
        h: generating.value ? 1 : "",
        i: common_vendor.o(onSecret),
        j: common_vendor.t(selectedCategory.value ? selectedCategory.value.categoryName : "请选择分类"),
        k: !selectedCategory.value ? 1 : "",
        l: common_vendor.o(showCategoryPicker),
        m: agentForm.agentName,
        n: common_vendor.o(($event) => agentForm.agentName = $event.detail.value),
        o: agentForm.agentDesc,
        p: common_vendor.o(($event) => agentForm.agentDesc = $event.detail.value),
        q: common_vendor.f(agentTypes.value, (type, k0, i0) => {
          return common_vendor.e({
            a: agentForm.agentType === type.value
          }, agentForm.agentType === type.value ? {} : {}, {
            b: agentForm.agentType === type.value ? 1 : "",
            c: common_vendor.t(type.label),
            d: type.value,
            e: common_vendor.o(($event) => selectAgentType(type.value), type.value)
          });
        }),
        r: agentForm.agentType === 1
      }, agentForm.agentType === 1 ? {
        s: agentForm.promptContent,
        t: common_vendor.o(($event) => agentForm.promptContent = $event.detail.value)
      } : {}, {
        v: common_vendor.f(secretTypes.value, (visibility, k0, i0) => {
          return common_vendor.e({
            a: agentForm.agentConfig.secret_key_type === visibility.value
          }, agentForm.agentConfig.secret_key_type === visibility.value ? {} : {}, {
            b: agentForm.agentConfig.secret_key_type === visibility.value ? 1 : "",
            c: common_vendor.t(visibility.label),
            d: visibility.value,
            e: common_vendor.o(($event) => selectSecretType(visibility.value), visibility.value)
          });
        }),
        w: agentForm.agentType === 3
      }, agentForm.agentType === 3 ? {
        x: agentForm.agentConfig.coze_sign,
        y: common_vendor.o(($event) => agentForm.agentConfig.coze_sign = $event.detail.value)
      } : {}, {
        z: agentForm.agentType !== 1
      }, agentForm.agentType !== 1 ? {
        A: agentForm.agentConfig.secret_token,
        B: common_vendor.o(($event) => agentForm.agentConfig.secret_token = $event.detail.value)
      } : {}, {
        C: agentForm.agentType === 2
      }, agentForm.agentType === 2 ? {
        D: agentForm.agentConfig.deploy_address,
        E: common_vendor.o(($event) => agentForm.agentConfig.deploy_address = $event.detail.value)
      } : {}, {
        F: agentForm.welcomeMessage,
        G: common_vendor.o(($event) => agentForm.welcomeMessage = $event.detail.value),
        H: common_vendor.f(agentForm.commonQuestions, (question, index, i0) => {
          return {
            a: `请输入引导问题${index + 1}`,
            b: agentForm.commonQuestions[index],
            c: common_vendor.o(($event) => agentForm.commonQuestions[index] = $event.detail.value, index),
            d: index
          };
        }),
        I: common_vendor.f(visibilityTypes.value, (visibility, k0, i0) => {
          return common_vendor.e({
            a: agentForm.isPaid === visibility.value
          }, agentForm.isPaid === visibility.value ? {} : {}, {
            b: agentForm.isPaid === visibility.value ? 1 : "",
            c: common_vendor.t(visibility.label),
            d: visibility.value,
            e: common_vendor.o(($event) => selectVisibility(visibility.value), visibility.value)
          });
        }),
        J: agentForm.isPaid === 1
      }, agentForm.isPaid === 1 ? {
        K: agentForm.price,
        L: common_vendor.o(($event) => agentForm.price = $event.detail.value)
      } : {}, {
        M: agentForm.isPaid === 1
      }, agentForm.isPaid === 1 ? {
        N: agentForm.trial_chat_count,
        O: common_vendor.o(($event) => agentForm.trial_chat_count = $event.detail.value)
      } : {}, {
        P: common_vendor.t(creating.value ? isEditMode.value ? "更新中..." : "创建中..." : isEditMode.value ? "完成" : "创建AI智能体"),
        Q: common_vendor.o(handleCreate),
        R: creating.value ? 1 : "",
        S: showCategoryModal.value
      }, showCategoryModal.value ? common_vendor.e({
        T: common_assets._imports_1$6,
        U: common_vendor.o([($event) => searchKeyword.value = $event.detail.value, handleSearch]),
        V: searchKeyword.value,
        W: searchKeyword.value
      }, searchKeyword.value ? {
        X: common_assets._imports_2$5,
        Y: common_vendor.o(clearSearch)
      } : {}, {
        Z: common_vendor.f(filteredCategories.value, (category, index, i0) => {
          var _a, _b, _c;
          return common_vendor.e({
            a: ((_a = tempSelectedCategory.value) == null ? void 0 : _a.guid) === category.guid
          }, ((_b = tempSelectedCategory.value) == null ? void 0 : _b.guid) === category.guid ? {} : {}, {
            b: ((_c = tempSelectedCategory.value) == null ? void 0 : _c.guid) === category.guid ? 1 : "",
            c: common_vendor.t(category.categoryName),
            d: category.guid,
            e: common_vendor.o(($event) => selectTempCategory(category), category.guid)
          });
        }),
        aa: filteredCategories.value.length === 0
      }, filteredCategories.value.length === 0 ? {} : {}, {
        ab: common_vendor.o(closeCategoryModal),
        ac: common_vendor.o(confirmSelectCategory),
        ad: !tempSelectedCategory.value ? 1 : "",
        ae: common_vendor.o(() => {
        }),
        af: common_vendor.o(closeCategoryModal)
      }) : {});
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-6484fb96"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/create-agent/index.js.map
