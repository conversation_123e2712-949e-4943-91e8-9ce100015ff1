/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 图片弹窗样式 */
.image-modal-overlay.data-v-21e058a8 {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}
.image-modal-content.data-v-21e058a8 {
  width: 90%;
  height: 80vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}
.modal-image.data-v-21e058a8 {
  max-width: 100%;
  max-height: 200px;
  width: 100%;
  height: 300px;
  display: block;
  object-fit: contain;
}
.vip-page.data-v-21e058a8 {
  height: 100vh;
  background-color: #1E2541;
  padding-top: 40rpx;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}
.card-top.data-v-21e058a8 {
  padding: 0 32rpx;
}
.user-card.data-v-21e058a8 {
  display: flex;
  flex-direction: column;
  height: 508rpx;
  width: 100%;
  justify-content: center;
  align-items: center;
  text-align: center;
  background-image: url();
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
.user-avatar.data-v-21e058a8 {
  width: 220rpx;
  height: 220rpx;
  border-radius: 50%;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.2);
  margin-bottom: 24rpx;
}
.avatar-img.data-v-21e058a8 {
  width: 100%;
  height: 100%;
}
.user-info.data-v-21e058a8 {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.user-name.data-v-21e058a8 {
  font-size: 35rpx;
  font-weight: 500;
  color: #F7D4BE;
  margin-bottom: 12rpx;
}
.user-status.data-v-21e058a8 {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 8rpx;
}
.user-desc.data-v-21e058a8 {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
}
.tip-text.data-v-21e058a8 {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
  padding: 50rpx 20rpx 30rpx 20rpx;
  line-height: 1.5;
  background-color: #252D44;
  margin-top: 20rpx;
  border-radius: 25rpx 25rpx 25rpx 25rpx;
  margin-bottom: 20px;
}
.vip-plans.data-v-21e058a8 {
  display: flex;
  flex: 1;
  flex-direction: column;
  justify-content: space-between;
  background-color: #fff;
  gap: 20rpx;
  padding-bottom: 80rpx;
}
.plan-box.data-v-21e058a8 {
  display: flex;
  justify-content: space-around;
  margin-top: 30px;
  flex-wrap: wrap;
  padding: 0 30rpx;
}
.plan-card.data-v-21e058a8 {
  background: #FFFFFF;
  border-radius: 25rpx;
  border: 3rpx solid #EDEFF0;
  text-align: center;
  position: relative;
  transition: all 0.3s ease;
  width: 210rpx;
  height: 260rpx;
  box-sizing: border-box;
  overflow: hidden;
  margin-bottom: 20rpx;
}
.plan-card.selected.data-v-21e058a8 {
  border-color: #DAAA76;
  background: #FDF6EB;
}
.plan-card.selected .plan-name.data-v-21e058a8 {
  background: #DAAA76;
  color: #FDF6EB;
}
.plan-card.selected .price-amount.data-v-21e058a8 {
  color: #CB8D59;
}
.plan-card.selected .plan-daily.data-v-21e058a8 {
  background-color: #FBE9DA;
  color: #CD8D57;
}
.plan-header.data-v-21e058a8 {
  margin-bottom: 20rpx;
  display: flex;
  justify-content: center;
}
.plan-name.data-v-21e058a8 {
  font-size: 24rpx;
  color: #303649;
  background: #EDEFF0;
  border-radius: 0rpx 0rpx 20rpx 20rpx;
  width: -webkit-fit-content;
  width: fit-content;
  height: 48rpx;
  display: flex;
  align-items: center;
  padding: 0 4px;
}
.plan-price.data-v-21e058a8 {
  display: flex;
  align-items: baseline;
  justify-content: center;
  margin-bottom: 12rpx;
}
.price-symbol.data-v-21e058a8 {
  font-size: 24rpx;
  color: #CB8D59;
  font-weight: 600;
}
.price-amount.data-v-21e058a8 {
  font-size: 48rpx;
  font-weight: 700;
  color: #1D253B;
}
.plan-duration.data-v-21e058a8 {
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 8rpx;
}
.plan-daily.data-v-21e058a8 {
  font-size: 18rpx;
  color: #999999;
  background-color: #F6F8FA;
  width: 100%;
  height: 56rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  left: 0;
  bottom: 0;
}
.subscribe-btn.data-v-21e058a8 {
  width: 530rpx;
  margin: 0 auto;
  height: 100rpx;
  background: #2A64F6;
  border-radius: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.subscribe-btn.ios.data-v-21e058a8 {
  background: #999;
}
.btn-text.data-v-21e058a8 {
  font-size: 32rpx;
  font-weight: 600;
  color: #FFFFFF;
}