{"id": "lime-painter", "displayName": "海报画板", "version": "*******", "description": "一款canvas海报组件，更优雅的海报生成方案，有限的支持富文本", "keywords": ["海报", "富文本", "生成海报", "生成二维码", "JSON"], "repository": "https://gitee.com/liangei/lime-painter", "engines": {"HBuilderX": "^3.4.14"}, "dcloudext": {"sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": "305716444"}, "declaration": {"ads": "无", "data": "无", "permissions": "无"}, "npmurl": "", "type": "component-vue"}, "uni_modules": {"dependencies": [], "encrypt": [], "platforms": {"cloud": {"tcb": "y", "aliyun": "y", "alipay": "n"}, "client": {"App": {"app-vue": "y", "app-nvue": "y"}, "H5-mobile": {"Safari": "y", "Android Browser": "y", "微信浏览器(Android)": "y", "QQ浏览器(Android)": "y"}, "H5-pc": {"Chrome": "y", "IE": "u", "Edge": "u", "Firefox": "u", "Safari": "y"}, "小程序": {"微信": "y", "阿里": "y", "百度": "y", "字节跳动": "y", "QQ": "y", "钉钉": "u", "快手": "u", "飞书": "u", "京东": "u"}, "快应用": {"华为": "u", "联盟": "u"}, "Vue": {"vue2": "y", "vue3": "y"}}}}, "name": "lime-painter", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "ISC"}