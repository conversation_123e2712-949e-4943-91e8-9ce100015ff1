<wxs src="./wx.wxs" module="wxsswipe"/>
<view class="uni-swipe"><view class="uni-swipe_box" change:prop="{{wxsswipe.showWatch}}" prop="{{c}}" data-threshold="{{d}}" data-disabled="{{e}}" bindtouchstart="{{wxsswipe.touchstart}}" bindtouchmove="{{wxsswipe.touchmove}}" bindtouchend="{{wxsswipe.touchend}}"><view class="uni-swipe_button-group button-group--left"><block wx:if="{{$slots.left}}"><slot name="left"></slot></block><block wx:else><view wx:for="{{a}}" wx:for-item="item" wx:key="d" style="{{'background-color:' + item.e}}" class="uni-swipe_button button-hock" catchtouchstart="{{item.f}}" catchtouchend="{{item.g}}" catchtap="{{item.h}}"><text class="uni-swipe_button-text" style="{{'color:' + item.b + ';' + ('font-size:' + item.c)}}">{{item.a}}</text></view></block></view><view class="uni-swipe_text--center"><slot></slot></view><view class="uni-swipe_button-group button-group--right"><block wx:if="{{$slots.right}}"><slot name="right"></slot></block><block wx:else><view wx:for="{{b}}" wx:for-item="item" wx:key="d" style="{{'background-color:' + item.e}}" class="uni-swipe_button button-hock" catchtouchstart="{{item.f}}" catchtouchend="{{item.g}}" catchtap="{{item.h}}"><text class="uni-swipe_button-text" style="{{'color:' + item.b + ';' + ('font-size:' + item.c)}}">{{item.a}}</text></view></block></view></view></view>