"use strict";const e=(e="")=>e.split(";").filter((e=>e&&!/^[\n\s]+$/.test(e))).map((e=>{const t=e.slice(0,e.indexOf(":")),i=e.slice(e.indexOf(":")+1);return{[t.replace(/-([a-z])/g,(function(){return arguments[1].toUpperCase()})).replace(/\s+/g,"")]:i.replace(/^\s+/,"").replace(/\s+$/,"")||""}}));exports.children=function(t,i={}){return i.indexKey,{inject:{[t]:{default:null}},watch:{el:{handler(e,t){JSON.stringify(e)!=JSON.stringify(t)&&this.bindRelation()},deep:!0,immediate:!0},src:{handler(e,t){e!=t&&this.bindRelation()},immediate:!0},text:{handler(e,t){e!=t&&this.bindRelation()},immediate:!0},css:{handler(t,i){t!=i&&(this.el.css=("object"==typeof t?t:t&&Object.assign(...e(t)))||{})},immediate:!0},replace:{handler(e,t){JSON.stringify(e)!=JSON.stringify(t)&&this.bindRelation()},deep:!0,immediate:!0}},created(){this._uid||(this._uid=this._.uid),Object.defineProperty(this,"parent",{get:()=>this[t]||[]}),Object.defineProperty(this,"index",{get:()=>{this.bindRelation();const{parent:{el:{views:e=[]}={}}={}}=this;return e.indexOf(this.el)}}),this.el.type=this.type,this.uid&&(this.el.uid=this.uid),this.bindRelation()},beforeUnmount(){this.removeEl()},methods:{removeEl(){this.parent&&(this.parent.el.views=this.parent.el.views.filter((e=>e._uid!==this._uid)))},bindRelation(){if(this.el._uid||(this.el._uid=this._uid),["text","qrcode"].includes(this.type)&&(this.el.text=this.$slots&&this.$slots.default&&this.$slots.default[0].text||`${this.text||""}`.replace(/\\n/g,"\n")),"image"==this.type&&(this.el.src=this.src),!this.parent)return;let e=this.parent.el.views||[];-1!==e.indexOf(this.el)?this.parent.el.views=e.map((e=>e._uid==this._uid?this.el:e)):this.parent.el.views=[...e,this.el]}},mounted(){}}},exports.parent=function(t){return{provide(){return{[t]:this}},data:()=>({el:{id:null,css:{},views:[]}}),watch:{css:{handler(t){this.canvasId&&(this.el.css=("object"==typeof t?t:t&&Object.assign(...e(t)))||{},this.canvasWidth=this.el.css&&this.el.css.width||this.canvasWidth,this.canvasHeight=this.el.css&&this.el.css.height||this.canvasHeight)},immediate:!0}}}};
