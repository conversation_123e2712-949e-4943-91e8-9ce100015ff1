"use strict";const e=require("../common/vendor.js"),r=function(e,r){var t=e,n=r;return e<r&&(t=r,n=e),parseInt(Math.random()*(t-n))+n};var t=new e.Hashes.MD5,n=new e.Hashes.SHA1;exports.autographFun=e=>{let r,o=e,u={app_guid:o.urlSuffix.app_guid,app_type:o.urlSuffix.app_type,token:o.urlSuffix.token},f=t.hex(JSON.stringify(u));if("POST"===o.method){let e=o.data?t.hex(JSON.stringify(o.data)):"";r=o.method+"\n"+e+"\n"+f+"\napplication/json\n"+o.urlSuffix.expires+"\n"+o.urlSuffix.noncestr+"\n/"+o.url.toLowerCase()}else r=o.method+"\n"+f+"\n"+o.urlSuffix.expires+"\n"+o.urlSuffix.noncestr+"\n/"+o.url.toLowerCase();return n.b64_hmac(o.urlSuffix.token,r)},exports.randomStr=function(e,t,n,o){let u=[],f=[],i=[];if(e)for(let r=0;r<=9;r++)u.push(r);if(t)for(let r=65;r<=90;r++)f.push(r);if(n)for(let r=97;r<=122;r++)i.push(r);if(!o)return void console.log("生成位数必传");let a=u.concat(f).concat(i),s=a.length,l="";for(let p=0;p<o;p++){let e="",t=r(0,s);a[t]<=9?e=a[t]:a[t]>9&&(e=String.fromCharCode(a[t])),l+=e}return l};
