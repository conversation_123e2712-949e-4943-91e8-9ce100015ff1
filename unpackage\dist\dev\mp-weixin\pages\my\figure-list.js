"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const api_index = require("../../api/index.js");
const stores_user = require("../../stores/user.js");
const config_config = require("../../config/config.js");
if (!Array) {
  const _easycom_z_paging2 = common_vendor.resolveComponent("z-paging");
  _easycom_z_paging2();
}
const _easycom_z_paging = () => "../../uni_modules/z-paging/components/z-paging/z-paging.js";
if (!Math) {
  _easycom_z_paging();
}
const _sfc_main = {
  __name: "figure-list",
  setup(__props) {
    const userStore = stores_user.useUserStore();
    const showUploadPopup = common_vendor.ref(false);
    const figureList = common_vendor.ref([]);
    const paging = common_vendor.ref(null);
    const isEditMode = common_vendor.ref(false);
    const selectedFigure = common_vendor.ref(null);
    const queryList = async (page, pageSize) => {
      try {
        const res = await api_index.getMyPersonListApi({
          merchantGuid: userStore.merchantGuid,
          page,
          pageSize
        });
        paging.value.complete(res.data.list || []);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/my/figure-list.vue:134", "获取形象列表失败:", error);
        common_vendor.index.showToast({
          title: "加载失败",
          icon: "none"
        });
        paging.value.complete(false);
      }
    };
    const enterEditMode = () => {
      isEditMode.value = true;
      selectedFigure.value = null;
    };
    const cancelEdit = () => {
      isEditMode.value = false;
      selectedFigure.value = null;
    };
    const handleFigureTap = (figure) => {
      if (!isEditMode.value) {
        return;
      }
      const personGuid = figure.personGuid;
      if (selectedFigure.value === personGuid) {
        selectedFigure.value = null;
      } else {
        selectedFigure.value = personGuid;
      }
    };
    const deleteSelected = () => {
      if (!selectedFigure.value) {
        common_vendor.index.showToast({
          title: "请先选择要删除的形象",
          icon: "none"
        });
        return;
      }
      common_vendor.index.showModal({
        title: "确认删除",
        content: "确定要删除选中的形象吗？",
        success: async (res) => {
          if (res.confirm) {
            try {
              await api_index.deletePersonApi({
                merchantGuid: userStore.merchantGuid,
                personGuid: selectedFigure.value
              });
              selectedFigure.value = null;
              isEditMode.value = false;
              paging.value.reload();
              common_vendor.index.showToast({
                title: "删除成功",
                icon: "success"
              });
            } catch (error) {
              common_vendor.index.__f__("error", "at pages/my/figure-list.vue:204", "删除形象失败:", error);
              common_vendor.index.showToast({
                title: "删除失败",
                icon: "none"
              });
            }
          }
        }
      });
    };
    const showUploadModal = () => {
      showUploadPopup.value = true;
    };
    const hideUploadModal = () => {
      showUploadPopup.value = false;
    };
    const uploadVideo = () => {
      common_vendor.index.chooseVideo({
        sourceType: ["camera", "album"],
        maxDuration: 60,
        success: async (res) => {
          common_vendor.index.__f__("log", "at pages/my/figure-list.vue:231", "选择的视频:", res.tempFilePath);
          common_vendor.index.showLoading({
            title: "上传中...",
            mask: true
          });
          try {
            const uploadRes = await new Promise((resolve, reject) => {
              common_vendor.index.uploadFile({
                url: `${config_config.base.baseUrl}user/api.userinfo/uploadVideo`,
                name: "video",
                fileType: "video",
                filePath: res.tempFilePath,
                success: (uploadResult) => {
                  resolve(uploadResult);
                },
                fail: (error) => {
                  reject(error);
                }
              });
            });
            const uploadData = JSON.parse(uploadRes.data);
            common_vendor.index.__f__("log", "at pages/my/figure-list.vue:258", "视频上传结果:", uploadData);
            if (uploadData.code === 0) {
              const createRes = await api_index.createPersonkApi({
                merchantGuid: userStore.merchantGuid,
                materialVideoUrl: uploadData.data,
                // 使用上传返回的视频URL
                personName: `定制形象`
                // 生成默认名称
              });
              if (createRes.code === 0) {
                common_vendor.index.hideLoading();
                common_vendor.index.showToast({
                  title: "形象创建成功",
                  icon: "success"
                });
                paging.value.reload();
                hideUploadModal();
              } else {
                throw new Error(createRes.msg || "创建形象失败");
              }
            } else {
              throw new Error(uploadData.msg || "视频上传失败");
            }
          } catch (error) {
            common_vendor.index.hideLoading();
            common_vendor.index.__f__("error", "at pages/my/figure-list.vue:286", "上传或创建失败:", error);
            common_vendor.index.showToast({
              title: error.message || "操作失败，请重试",
              icon: "none"
            });
          }
        },
        fail: (err) => {
          common_vendor.index.__f__("log", "at pages/my/figure-list.vue:294", "选择视频失败:", err);
          common_vendor.index.showToast({
            title: "选择视频失败",
            icon: "none"
          });
        }
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$4,
        b: common_vendor.o(showUploadModal),
        c: common_vendor.f(figureList.value, (figure, k0, i0) => {
          return common_vendor.e({
            a: figure.picUrl,
            b: common_vendor.t(figure.statusText)
          }, isEditMode.value ? common_vendor.e({
            c: selectedFigure.value === figure.personGuid
          }, selectedFigure.value === figure.personGuid ? {
            d: common_assets._imports_1$2
          } : {
            e: common_assets._imports_2$3
          }) : {}, {
            f: isEditMode.value && selectedFigure.value === figure.personGuid ? 1 : "",
            g: common_vendor.t(figure.personName),
            h: figure.personGuid,
            i: common_vendor.o(($event) => handleFigureTap(figure), figure.personGuid)
          });
        }),
        d: isEditMode.value,
        e: common_vendor.sr(paging, "da8e5c67-0", {
          "k": "paging"
        }),
        f: common_vendor.o(queryList),
        g: common_vendor.o(($event) => figureList.value = $event),
        h: common_vendor.p({
          ["refresher-enabled"]: true,
          auto: true,
          modelValue: figureList.value
        }),
        i: !isEditMode.value
      }, !isEditMode.value ? {
        j: common_assets._imports_3$1,
        k: common_vendor.o(enterEditMode)
      } : {
        l: common_vendor.o(cancelEdit),
        m: common_vendor.o(deleteSelected)
      }, {
        n: showUploadPopup.value
      }, showUploadPopup.value ? {
        o: common_assets._imports_2$1,
        p: common_vendor.o(hideUploadModal),
        q: common_vendor.o(uploadVideo),
        r: common_vendor.o(() => {
        })
      } : {});
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-da8e5c67"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/my/figure-list.js.map
