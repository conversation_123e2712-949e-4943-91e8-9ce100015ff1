/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.my-works-container.data-v-5332091b {
  min-height: 100vh;
  background: #ffffff;
  padding-bottom: 160rpx;
  display: flex;
  flex-direction: column;
}
.works-content.data-v-5332091b {
  padding: 32rpx 30rpx;
}
.avatar-grid.data-v-5332091b {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 60rpx;
}
.avatar-card.data-v-5332091b {
  position: relative;
  overflow: hidden;
  width: 220rpx;
  height: 350rpx;
  border-radius: 15rpx;
  margin-bottom: 15rpx;
  border: 3rpx solid #ffffff00;
  box-sizing: border-box;
}
.avatar-card .avatar-image.data-v-5332091b {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.avatar-card .check-mark.data-v-5332091b {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  width: 48rpx;
  height: 48rpx;
  background: #5380F2;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.avatar-card .check-mark .check-icon.data-v-5332091b {
  width: 40rpx;
  height: 40rpx;
}
.avatar-card.selected.data-v-5332091b {
  border: 3rpx solid #5380F2;
}
.avatar-card.data-v-5332091b:not(:nth-child(3n)) {
  margin-right: 15rpx;
}
.bottom-actions.data-v-5332091b {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  background: #ffffff;
  padding: 32rpx;
  border-top: 1rpx solid #f0f0f0;
  gap: 24rpx;
}
.bottom-actions .action-button.data-v-5332091b {
  flex: 1;
  height: 96rpx;
  border-radius: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
}
.bottom-actions .action-button.edit .edit-icon.data-v-5332091b {
  width: 32rpx;
  height: 32rpx;
}
.bottom-actions .action-button.edit .action-text.data-v-5332091b {
  color: #333333;
}
.bottom-actions .action-button.cancel.data-v-5332091b {
  background: #f8f8f8;
}
.bottom-actions .action-button.cancel .action-text.data-v-5332091b {
  color: #666666;
}
.bottom-actions .action-button.delete.data-v-5332091b {
  background: #ffffff;
  border: 2rpx solid #FF4757;
}
.bottom-actions .action-button.delete .action-text.data-v-5332091b {
  color: #FF4757;
}
.bottom-actions .action-button .action-text.data-v-5332091b {
  font-size: 32rpx;
  font-weight: 500;
}