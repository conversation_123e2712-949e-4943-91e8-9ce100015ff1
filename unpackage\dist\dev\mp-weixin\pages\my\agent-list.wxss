/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.agent-list-page.data-v-e7ab54ac {
  background: #F5F5F5;
  min-height: 100vh;
}
.agent-list-page .agents-content.data-v-e7ab54ac {
  padding: 32rpx;
}
.agent-list-page .agents-content .agent-card.data-v-e7ab54ac {
  display: flex;
  align-items: center;
  background: #fff;
  border-radius: 24rpx;
  margin-bottom: 24rpx;
  padding: 32rpx;
  position: relative;
}
.agent-list-page .agents-content .agent-card .agent-avatar.data-v-e7ab54ac {
  width: 96rpx;
  height: 96rpx;
  border-radius: 50%;
  margin-right: 24rpx;
  flex-shrink: 0;
}
.agent-list-page .agents-content .agent-card .agent-info.data-v-e7ab54ac {
  flex: 1;
}
.agent-list-page .agents-content .agent-card .agent-info .agent-title.data-v-e7ab54ac {
  font-size: 32rpx;
  font-weight: 600;
  color: #222;
  margin-bottom: 8rpx;
}
.agent-list-page .agents-content .agent-card .agent-info .agent-desc.data-v-e7ab54ac {
  font-size: 24rpx;
  color: #999;
  line-height: 1.5;
  margin-bottom: 16rpx;
}
.agent-list-page .agents-content .agent-card .agent-info .agent-tags.data-v-e7ab54ac {
  display: flex;
  gap: 12rpx;
  flex-wrap: wrap;
  margin-top: 16rpx;
}
.agent-list-page .agents-content .agent-card .agent-info .agent-tags .tag.data-v-e7ab54ac {
  border-radius: 16rpx;
  padding: 8rpx 16rpx;
  font-size: 22rpx;
  font-weight: 500;
}
.agent-list-page .agents-content .agent-card .agent-info .agent-tags .tag.primary.data-v-e7ab54ac {
  background: #E6F0FF;
  color: #3478f6;
}
.agent-list-page .agents-content .agent-card .operate-box.data-v-e7ab54ac {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.agent-list-page .agents-content .agent-card .operate-box .edit.data-v-e7ab54ac {
  width: 50px;
  height: 30px;
  font-size: 24rpx;
  background-color: #3478f6;
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 16px;
}
.agent-list-page .agents-content .agent-card .operate-box .delete.data-v-e7ab54ac {
  width: 40px;
  height: 24px;
  font-size: 24rpx;
  background-color: #e60000;
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 16px;
}
.agent-list-page .agents-content .agent-card .status.data-v-e7ab54ac {
  width: 140rpx;
  height: 40rpx;
  position: absolute;
  right: 0;
  top: 0;
  border-radius: 0rpx 30rpx 0rpx 30rpx;
  background-color: rgba(253, 141, 43, 0.12);
  font-size: 20rpx;
  color: #FD8D2B;
  text-align: center;
  line-height: 40rpx;
}
.agent-list-page .agents-content .agent-card .status.success.data-v-e7ab54ac {
  background-color: #3478f6;
  color: #fff;
}
.agent-list-page .create-agent.data-v-e7ab54ac {
  position: fixed;
  width: 100%;
  bottom: 40px;
  left: 0;
  display: flex;
  justify-content: center;
}
.agent-list-page .create-agent .create-btn.data-v-e7ab54ac {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 300rpx;
  height: 90rpx;
  background: #3478f6;
  border-radius: 48rpx;
  border: none;
}
.agent-list-page .create-agent .create-btn .create-icon.data-v-e7ab54ac {
  width: 32rpx;
  height: 32rpx;
  margin-right: 12rpx;
}
.agent-list-page .create-agent .create-btn .create-text.data-v-e7ab54ac {
  font-size: 32rpx;
  color: #ffffff;
  line-height: 1;
}