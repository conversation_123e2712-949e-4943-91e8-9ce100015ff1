"use strict";const e=require("../../common/vendor.js"),r=require("../../common/assets.js"),o=require("../../api/index.js"),t=require("../../stores/user.js"),s={__name:"video-progress",setup(s){const a=t.useUserStore(),n=e.ref(""),i=e.ref(0),u=e.ref(0),c=e.ref("");let d=null;const l=async()=>{try{const r=await o.getVideoDetailApi({merchantGuid:a.merchantGuid,orderNo:c.value});if(0===r.code){const{progress:o,status:t}=r.data;i.value=o||0,u.value=t||0,(30===t||o>=100)&&(clearInterval(d),setTimeout((()=>{e.index.showToast({title:"视频生成完成！",icon:"success",duration:2e3}),setTimeout((()=>{e.index.redirectTo({url:`/pages/my/video-complete?orderNo=${c.value}`})}),2e3)}),500))}else console.error("获取视频详情失败:",r.msg)}catch(r){console.error("获取视频详情失败:",r)}};return e.onLoad((r=>{r.orderNo?(c.value=r.orderNo,n.value=r.previewUrl,l(),d=setInterval(l,3e3)):(e.index.showToast({title:"参数错误",icon:"none"}),setTimeout((()=>{e.index.navigateBack()}),2e3))})),e.onMounted((()=>{})),e.onUnmounted((()=>{d&&clearInterval(d)})),(o,t)=>({a:n.value,b:r._imports_0$6,c:e.t(Math.round(i.value)),d:i.value+"%"})}},a=e._export_sfc(s,[["__scopeId","data-v-6bd791f2"]]);wx.createPage(a);
