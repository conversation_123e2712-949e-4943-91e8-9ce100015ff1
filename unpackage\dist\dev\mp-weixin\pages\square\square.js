"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const stores_user = require("../../stores/user.js");
const api_index = require("../../api/index.js");
if (!Array) {
  const _easycom_uv_tabs2 = common_vendor.resolveComponent("uv-tabs");
  _easycom_uv_tabs2();
}
const _easycom_uv_tabs = () => "../../uni_modules/uv-tabs/components/uv-tabs/uv-tabs.js";
if (!Math) {
  _easycom_uv_tabs();
}
const _sfc_main = {
  __name: "square",
  setup(__props) {
    const userStore = stores_user.useUserStore();
    const currentTab = common_vendor.ref(0);
    const tabsList = common_vendor.ref([]);
    const containerHeight = common_vendor.ref("calc(100vh - 88rpx)");
    const initContainerHeight = () => {
      common_vendor.nextTick$1(() => {
        common_vendor.index.getSystemInfo({
          success: (res) => {
            const windowHeight = res.windowHeight;
            const availableHeight = windowHeight - 44;
            const finalHeight = Math.max(availableHeight, 600);
            containerHeight.value = `${finalHeight}px`;
            common_vendor.index.__f__("log", "at pages/square/square.vue:89", "Container height set to:", containerHeight.value);
          },
          fail: () => {
            containerHeight.value = "600px";
          }
        });
      });
    };
    const getCategoryList = async () => {
      let res = await api_index.getCategoryListApi({
        merchantGuid: userStore.merchantGuid
      });
      tabsList.value = res.data;
      categoryAgentData.value = new Array(tabsList.value.length);
      if (userStore.targetCategoryGuid) {
        switchToCategory(userStore.targetCategoryGuid);
        userStore.clear_target_category();
      } else {
        getAgentList();
      }
    };
    const categoryAgentData = common_vendor.ref([]);
    const getAgentList = async (categoryIndex = null) => {
      var _a;
      if (tabsList.value.length > 0) {
        const targetIndex = categoryIndex !== null ? categoryIndex : currentTab.value;
        let guid = tabsList.value[targetIndex].guid;
        let res = await api_index.getAgentListApi({
          merchantGuid: userStore.merchantGuid,
          categoryGuid: guid,
          pageSize: 100
        });
        common_vendor.index.__f__("log", "at pages/square/square.vue:128", "categoryAgentData before:", categoryAgentData.value);
        if (categoryAgentData.value.length <= targetIndex) {
          categoryAgentData.value = [...categoryAgentData.value, ...new Array(targetIndex + 1 - categoryAgentData.value.length)];
        }
        categoryAgentData.value[targetIndex] = res.data.data;
        common_vendor.index.__f__("log", "at pages/square/square.vue:134", "categoryAgentData after:", categoryAgentData.value);
        common_vendor.index.__f__("log", "at pages/square/square.vue:135", "categoryAgentData length:", (_a = categoryAgentData.value[targetIndex]) == null ? void 0 : _a.length);
      }
    };
    const handleTabChange = (event) => {
      common_vendor.index.__f__("log", "at pages/square/square.vue:143", "handleTabChange", event);
      currentTab.value = event.index;
      if (!categoryAgentData.value[event.index]) {
        getAgentList(event.index);
      }
    };
    const handleSwiperChange = (e) => {
      currentTab.value = e.detail.current;
      if (!categoryAgentData.value[e.detail.current]) {
        getAgentList(e.detail.current);
      }
    };
    const handleSearch = () => {
      common_vendor.index.__f__("log", "at pages/square/square.vue:164", "点击搜索");
      common_vendor.index.navigateTo({
        url: "/pages/square/search"
      });
    };
    const handleAgentClick = (item) => {
      common_vendor.index.__f__("log", "at pages/square/square.vue:172", "点击智能体:", item.agentName);
      common_vendor.index.navigateTo({
        url: `/pages/square/detail?sysId=${item.sysId}`
      });
    };
    const onSub = async (item) => {
      if (item.isSubscribed) {
        common_vendor.index.navigateTo({
          url: `/pages/msg/index?sessionGuid=${item.guid}`
        });
        return;
      } else {
        let req = {
          merchantGuid: userStore.merchantGuid,
          agentGuid: item.guid
        };
        try {
          await api_index.subscribeAgentApi(req);
          getAgentList(currentTab.value);
          common_vendor.index.showToast({
            title: "招募成功",
            icon: "none"
          });
        } catch (error) {
          common_vendor.index.showToast({
            title: "招募失败",
            icon: "none"
          });
        }
      }
    };
    common_vendor.onShareAppMessage(() => {
      return {
        title: userStore.appName || "智能体",
        path: `/pages/square/square?invite=${userStore.invitationCode}`,
        success(res) {
          common_vendor.index.__f__("log", "at pages/square/square.vue:211", "userStore.invitationCode", userStore.invitationCode);
          common_vendor.index.showToast({
            title: "分享成功"
          });
        },
        fail(res) {
          common_vendor.index.showToast({
            title: "分享失败",
            icon: "none"
          });
        }
      };
    });
    const findCategoryIndex = (categoryGuid) => {
      return tabsList.value.findIndex((item) => item.guid === categoryGuid);
    };
    const switchToCategory = (categoryGuid) => {
      const index = findCategoryIndex(categoryGuid);
      if (index !== -1) {
        currentTab.value = index;
        if (!categoryAgentData.value[index]) {
          getAgentList(index);
        }
      }
    };
    common_vendor.onShow(() => {
      initContainerHeight();
      if (userStore.targetCategoryGuid) {
        common_vendor.index.__f__("log", "at pages/square/square.vue:267", tabsList.value, "tabsList.valuetabsList.value");
        if (tabsList.value.length > 0) {
          switchToCategory(userStore.targetCategoryGuid);
          userStore.clear_target_category();
        } else {
          getCategoryList();
        }
      } else {
        getCategoryList();
      }
    });
    common_vendor.watch(
      () => userStore.userToken,
      (newValue, oldValue) => {
        common_vendor.index.__f__("log", "at pages/square/square.vue:303", "userToken changed", newValue, oldValue);
        if (newValue && oldValue === "") {
          getCategoryList();
        }
      }
    );
    return (_ctx, _cache) => {
      return {
        a: common_assets._imports_0$9,
        b: common_vendor.o(handleSearch),
        c: common_vendor.o(handleTabChange),
        d: common_vendor.p({
          list: tabsList.value,
          current: currentTab.value,
          lineColor: "#222222",
          keyName: "categoryName"
        }),
        e: common_vendor.f(tabsList.value, (tab, tabIndex, i0) => {
          var _a, _b;
          return common_vendor.e({
            a: common_vendor.f(categoryAgentData.value[tabIndex] || [], (item, k1, i1) => {
              return {
                a: item.agentAvatar,
                b: common_vendor.o(($event) => handleAgentClick(item), item.guid),
                c: common_vendor.t(item.agentName),
                d: common_vendor.t(item.agentDesc),
                e: common_vendor.t(item.creator.nickname),
                f: common_vendor.o(($event) => handleAgentClick(item), item.guid),
                g: common_vendor.t(item.isSubscribed ? "已合伙" : "去招募"),
                h: item.isSubscribed ? 1 : "",
                i: common_vendor.o(($event) => onSub(item), item.guid),
                j: item.guid
              };
            }),
            b: ((_a = categoryAgentData.value[tabIndex]) == null ? void 0 : _a.length) === 0
          }, ((_b = categoryAgentData.value[tabIndex]) == null ? void 0 : _b.length) === 0 ? {} : {}, {
            c: tab.guid
          });
        }),
        f: containerHeight.value,
        g: containerHeight.value,
        h: currentTab.value,
        i: common_vendor.o(handleSwiperChange),
        j: containerHeight.value,
        k: containerHeight.value
      };
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-6bc6c6b7"]]);
_sfc_main.__runtimeHooks = 6;
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/square/square.js.map
