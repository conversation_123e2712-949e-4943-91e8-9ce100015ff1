"use strict";const e=require("../../common/vendor.js"),t=require("../../common/assets.js"),n={__name:"index",setup(n){const o=e.ref([]);return e.onLoad((()=>{(async()=>{try{console.log("加载财务流水")}catch(t){console.error("加载财务流水失败:",t),e.index.showToast({title:"加载失败",icon:"none"})}})()})),(n,a)=>e.e({a:e.f(o.value,((t,n,o)=>{return e.e({a:e.t(t.amount),b:e.n((a=t.type,"income"===a?"amount-income":"amount-expense")),c:e.t(t.reason),d:t.time},t.time?{e:e.t(t.timeLabel),f:e.t(t.time)}:{},{g:e.t(t.afterBalance),h:n});var a})),b:0===o.value.length},0===o.value.length?{c:t._imports_0$10}:{})}},o=e._export_sfc(n,[["__scopeId","data-v-bf39a30b"]]);wx.createPage(o);
