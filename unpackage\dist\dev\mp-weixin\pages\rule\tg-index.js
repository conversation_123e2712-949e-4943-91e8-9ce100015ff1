"use strict";
const common_vendor = require("../../common/vendor.js");
const api_index = require("../../api/index.js");
const stores_user = require("../../stores/user.js");
const ruleTitleBg2 = "https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/3901e1ecde244f119393a75b5acf16b4.png";
const ruleBg2 = "https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/9c3e9e617f9a46f38419cb9f0fd1632d.png";
const _sfc_main = {
  __name: "tg-index",
  setup(__props) {
    const userStore = stores_user.useUserStore();
    const handleGoCreate = () => {
      common_vendor.index.switchTab({
        url: "/pages/square/square"
      });
    };
    let htmlContent = common_vendor.ref("");
    const platformRules = async () => {
      let res = await api_index.platformRulesApi({
        merchantGuid: userStore.merchantGuid
      });
      htmlContent.value = res.data.promotionRules.content;
    };
    platformRules();
    return (_ctx, _cache) => {
      return {
        a: ruleBg2,
        b: ruleTitleBg2,
        c: common_vendor.unref(htmlContent),
        d: common_vendor.o(handleGoCreate)
      };
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-fd97a914"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/rule/tg-index.js.map
