<view class="my-figure-container data-v-9264a81f"><z-paging wx:if="{{h}}" class="r data-v-9264a81f" u-s="{{['d']}}" u-r="paging" bindquery="{{f}}" u-i="9264a81f-0" bind:__l="__l" bindupdateModelValue="{{g}}" u-p="{{h}}"><view class="figure-content data-v-9264a81f"><view class="figure-grid data-v-9264a81f"><view class="figure-item figure-upload data-v-9264a81f" bindtap="{{b}}"><image class="create-icon data-v-9264a81f" src="{{a}}"/><text class="create-title data-v-9264a81f">定制数字人</text></view><view wx:for="{{c}}" wx:for-item="figure" wx:key="h" class="figure-item figure-list data-v-9264a81f" bindtap="{{figure.i}}"><view class="{{['avatar-container', 'data-v-9264a81f', figure.f && 'selected']}}"><view class="avatar-bg data-v-9264a81f"><image class="avatar-image data-v-9264a81f" src="{{figure.a}}" mode="heightFix"/></view><view class="badge data-v-9264a81f">{{figure.b}}</view><view wx:if="{{d}}" class="check-mark data-v-9264a81f"><image wx:if="{{figure.c}}" class="check-icon data-v-9264a81f" src="{{figure.d}}"/><image wx:else class="check-icon data-v-9264a81f" src="{{figure.e}}"/></view></view><view class="avatar-info data-v-9264a81f"><view class="avatar-name data-v-9264a81f">{{figure.g}}</view></view></view></view></view></z-paging><view class="bottom-actions data-v-9264a81f"><block wx:if="{{i}}"><view class="action-button edit data-v-9264a81f" bindtap="{{k}}"><image class="edit-icon data-v-9264a81f" src="{{j}}" mode="aspectFit"/><text class="action-text data-v-9264a81f">编辑</text></view></block><block wx:else><view class="action-button cancel data-v-9264a81f" bindtap="{{l}}"><text class="action-text data-v-9264a81f">取消</text></view><view class="action-button delete data-v-9264a81f" bindtap="{{m}}"><text class="action-text data-v-9264a81f">删除</text></view></block></view><view wx:if="{{n}}" class="upload-modal-overlay data-v-9264a81f"><view class="upload-modal-content data-v-9264a81f" catchtap="{{r}}"><view class="close-btn data-v-9264a81f" bindtap="{{p}}"><image class="close-icon data-v-9264a81f" src="{{o}}" mode="aspectFit"/></view><text class="upload-title data-v-9264a81f">请上传您的视频完成数字人制作</text><view class="upload-tips data-v-9264a81f"><view class="tip-item data-v-9264a81f"><text class="tip-title data-v-9264a81f">录制时可以自然微笑，保持放松</text><text class="tip-desc data-v-9264a81f">您好!我现在感觉很好，语调很轻松，我很有信心能做好这次视频录制。 我现在就在镜头前，准备开始。</text></view><view class="tip-item data-v-9264a81f"><text class="tip-title data-v-9264a81f">闭上嘴，用鼻子呼吸，停顿1s</text><text class="tip-desc data-v-9264a81f">光线很好，我的脸上没有任何刺眼的阴影，我的发音很清晰，感觉很放松，我会做一些自然、轻微的手部动作。</text></view><view class="tip-item data-v-9264a81f"><text class="tip-title data-v-9264a81f">闭上嘴，用鼻子呼吸，停顿1s</text><text class="tip-desc data-v-9264a81f">一些细微的姿态动作会让我看起来更自然更放松。 在整个视频录制过程中，我不会移动我的身体，也不会做太剧烈的任何动作。</text></view></view><view class="upload-btn data-v-9264a81f" bindtap="{{q}}"><text class="upload-btn-text data-v-9264a81f">上传视频</text></view></view></view></view>