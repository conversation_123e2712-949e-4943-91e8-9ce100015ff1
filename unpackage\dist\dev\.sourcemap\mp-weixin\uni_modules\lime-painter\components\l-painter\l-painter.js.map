{"version": 3, "file": "l-painter.js", "sources": ["uni_modules/lime-painter/components/l-painter/l-painter.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RToveW91bmdQcm9qZWN0L2FnZW50LW1pbmktdWkvdW5pX21vZHVsZXMvbGltZS1wYWludGVyL2NvbXBvbmVudHMvbC1wYWludGVyL2wtcGFpbnRlci52dWU"], "sourcesContent": ["<template>\r\n\t<view class=\"lime-painter\" ref=\"limepainter\">\r\n\t\t<view v-if=\"canvasId && size\" :style=\"styles\">\r\n\t\t\t<!-- #ifndef APP-NVUE -->\r\n\t\t\t<canvas class=\"lime-painter__canvas\" v-if=\"use2dCanvas\" :id=\"canvasId\" type=\"2d\" :style=\"size\"></canvas>\r\n\t\t\t<canvas class=\"lime-painter__canvas\" v-else :id=\"canvasId\" :canvas-id=\"canvasId\" :style=\"size\"\r\n\t\t\t\t:width=\"boardWidth * dpr\" :height=\"boardHeight * dpr\" :hidpi=\"hidpi\"></canvas>\r\n\r\n\t\t\t<!-- #endif -->\r\n\t\t\t<!-- #ifdef APP-NVUE -->\r\n\t\t\t<web-view :style=\"size\" ref=\"webview\"\r\n\t\t\t\tsrc=\"/uni_modules/lime-painter/hybrid/html/index.html\"\r\n\t\t\t\tclass=\"lime-painter__canvas\" @pagefinish=\"onPageFinish\" @error=\"onError\" @onPostMessage=\"onMessage\">\r\n\t\t\t</web-view>\r\n\t\t\t<!-- #endif -->\r\n\t\t</view>\r\n\t\t<slot />\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport { parent } from '../common/relation'\r\n\timport props from './props'\r\n\timport {toPx, base64ToPath, pathToBase64, isBase64, sleep, getImageInfo }from './utils';\r\n\t//  #ifndef APP-NVUE\r\n\timport { canIUseCanvas2d, isPC} from './utils';\r\n\timport Painter from './painter';\r\n\t// import Painter from '@painter'\r\n\tconst nvue = {}\r\n\t//  #endif\r\n\t//  #ifdef APP-NVUE\r\n\timport nvue from './nvue'\r\n\t//  #endif\r\n\texport default {\r\n\t\tname: 'lime-painter',\r\n\t\tmixins: [props, parent('painter'), nvue],\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tuse2dCanvas: false,\r\n\t\t\t\tcanvasHeight: 150,\r\n\t\t\t\tcanvasWidth: null,\r\n\t\t\t\tparentWidth: 0,\r\n\t\t\t\tinited: false,\r\n\t\t\t\tprogress: 0,\r\n\t\t\t\tfirstRender: 0,\r\n\t\t\t\tdone: false,\r\n\t\t\t\ttasks: []\r\n\t\t\t};\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tstyles() {\r\n\t\t\t\treturn `${this.size}${this.customStyle||''};` + (this.hidden && 'position: fixed; left: 1500rpx;')\r\n\t\t\t},\r\n\t\t\tcanvasId() {\r\n\t\t\t\treturn `l-painter${this._ && this._.uid || this._uid}`\r\n\t\t\t},\r\n\t\t\tsize() {\r\n\t\t\t\tif (this.boardWidth && this.boardHeight) {\r\n\t\t\t\t\treturn `width:${this.boardWidth}px; height: ${this.boardHeight}px;`;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tdpr() {\r\n\t\t\t\treturn this.pixelRatio || uni.getSystemInfoSync().pixelRatio;\r\n\t\t\t},\r\n\t\t\tboardWidth() {\r\n\t\t\t\tconst {width = 0} = (this.elements && this.elements.css) || this.elements || this\r\n\t\t\t\tconst w = toPx(width||this.width)\r\n\t\t\t\treturn w || Math.max(w, toPx(this.canvasWidth));\r\n\t\t\t},\r\n\t\t\tboardHeight() {\r\n\t\t\t\tconst {height = 0} = (this.elements && this.elements.css) || this.elements || this\r\n\t\t\t\tconst h = toPx(height||this.height)\r\n\t\t\t\treturn h || Math.max(h, toPx(this.canvasHeight));\r\n\t\t\t},\r\n\t\t\thasBoard() {\r\n\t\t\t\treturn this.board && Object.keys(this.board).length\r\n\t\t\t},\r\n\t\t\telements() {\r\n\t\t\t\treturn this.hasBoard ? this.board : JSON.parse(JSON.stringify(this.el))\r\n\t\t\t}\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\tthis.use2dCanvas = this.type === '2d' && canIUseCanvas2d() && !isPC\r\n\t\t},\r\n\t\tasync mounted() {\r\n\t\t\tawait sleep(30)\r\n\t\t\tawait this.getParentWeith()\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tthis.$watch('elements', this.watchRender, {\r\n\t\t\t\t\t\tdeep: true,\r\n\t\t\t\t\t\timmediate: true\r\n\t\t\t\t\t});\r\n\t\t\t\t}, 30)\r\n\t\t\t})\r\n\t\t},\r\n\t\t// #ifdef VUE3\r\n\t\tunmounted() {\r\n\t\t\tthis.done = false\r\n\t\t\tthis.inited = false\r\n\t\t\tthis.firstRender = 0\r\n\t\t\tthis.progress = 0\r\n\t\t\tthis.painter = null\r\n\t\t\tclearTimeout(this.rendertimer)\r\n\t\t},\r\n\t\t// #endif\r\n\t\t// #ifdef VUE2\r\n\t\tdestroyed() {\r\n\t\t\tthis.done = false\r\n\t\t\tthis.inited = false\r\n\t\t\tthis.firstRender = 0\r\n\t\t\tthis.progress = 0\r\n\t\t\tthis.painter = null\r\n\t\t\tclearTimeout(this.rendertimer)\r\n\t\t},\r\n\t\t// #endif\r\n\t\tmethods: {\r\n\t\t\tasync watchRender(val, old) {\r\n\t\t\t\tif (!val || !val.views || (!this.firstRender ? !val.views.length : !this.firstRender) || !Object.keys(val).length || JSON.stringify(val) == JSON.stringify(old)) return;\r\n\t\t\t\tthis.firstRender = 1\r\n\t\t\t\tthis.progress = 0\r\n\t\t\t\tthis.done = false\r\n\t\t\t\tclearTimeout(this.rendertimer)\r\n\t\t\t\tthis.rendertimer = setTimeout(() => {\r\n\t\t\t\t\tthis.render(val);\r\n\t\t\t\t}, this.beforeDelay)\r\n\t\t\t},\r\n\t\t\tasync setFilePath(path, param) {\r\n\t\t\t\tlet filePath = path\r\n\t\t\t\tconst {pathType = this.pathType} =  param || this\r\n\t\t\t\tif (pathType == 'base64' && !isBase64(path)) {\r\n\t\t\t\t\tfilePath = await pathToBase64(path)\r\n\t\t\t\t} else if (pathType == 'url' && isBase64(path)) {\r\n\t\t\t\t\tfilePath = await base64ToPath(path)\r\n\t\t\t\t}\r\n\t\t\t\tif (param && param.isEmit) {\r\n\t\t\t\t\tthis.$emit('success', filePath);\r\n\t\t\t\t}\r\n\t\t\t\treturn filePath\r\n\t\t\t},\r\n\t\t\tasync getSize(args) {\r\n\t\t\t\tconst {width} = args.css || args\r\n\t\t\t\tconst {height} = args.css || args\r\n\t\t\t\tif (!this.size) {\r\n\t\t\t\t\tif (width || height) {\r\n\t\t\t\t\t\tthis.canvasWidth = width || this.canvasWidth\r\n\t\t\t\t\t\tthis.canvasHeight = height || this.canvasHeight\r\n\t\t\t\t\t\tawait sleep(30);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tawait this.getParentWeith()\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tcanvasToTempFilePathSync(args) {\r\n\t\t\t\t// this.stopWatch && this.stopWatch()\r\n\t\t\t\t// this.stopWatch = this.$watch('done', (v) => {\r\n\t\t\t\t// \tif (v) {\r\n\t\t\t\t// \t\tthis.canvasToTempFilePath(args)\r\n\t\t\t\t// \t\tthis.stopWatch && this.stopWatch()\r\n\t\t\t\t// \t}\r\n\t\t\t\t// }, {\r\n\t\t\t\t// \timmediate: true\r\n\t\t\t\t// })\r\n\t\t\t\tthis.tasks.push(args)\r\n\t\t\t\tif(this.done){\r\n\t\t\t\t\tthis.runTask()\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\trunTask(){\r\n\t\t\t\twhile(this.tasks.length){\r\n\t\t\t\t\tconst task = this.tasks.shift()\t\r\n\t\t\t\t\t this.canvasToTempFilePath(task)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// #ifndef APP-NVUE\r\n\t\t\tgetParentWeith() {\r\n\t\t\t\treturn new Promise(resolve => {\r\n\t\t\t\t\tuni.createSelectorQuery()\r\n\t\t\t\t\t\t.in(this)\r\n\t\t\t\t\t\t.select(`.lime-painter`)\r\n\t\t\t\t\t\t.boundingClientRect()\r\n\t\t\t\t\t\t.exec(res => {\r\n\t\t\t\t\t\t\tconst {width, height} = res[0]||{}\r\n\t\t\t\t\t\t\tthis.parentWidth = Math.ceil(width||0)\r\n\t\t\t\t\t\t\tthis.canvasWidth = this.parentWidth || 300\r\n\t\t\t\t\t\t\tthis.canvasHeight = height || this.canvasHeight||150\r\n\t\t\t\t\t\t\tresolve(res[0])\r\n\t\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tasync render(args = {}) {\r\n\t\t\t\tif(!Object.keys(args).length) {\r\n\t\t\t\t\treturn console.error('空对象')\r\n\t\t\t\t}\r\n\t\t\t\tthis.progress = 0\r\n\t\t\t\tthis.done = false\r\n\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\tthis.tempFilePath.length = 0\r\n\t\t\t\t// #endif\r\n\t\t\t\tawait this.getSize(args)\r\n\t\t\t\tconst ctx = await this.getContext();\r\n\t\t\t\t\r\n\t\t\t\tlet {\r\n\t\t\t\t\tuse2dCanvas,\r\n\t\t\t\t\tboardWidth,\r\n\t\t\t\t\tboardHeight,\r\n\t\t\t\t\tcanvas,\r\n\t\t\t\t\tafterDelay\r\n\t\t\t\t} = this;\r\n\t\t\t\tif (use2dCanvas && !canvas) {\r\n\t\t\t\t\treturn Promise.reject(new Error('canvas 没创建'));\r\n\t\t\t\t}\r\n\t\t\t\tthis.boundary = {\r\n\t\t\t\t\ttop: 0,\r\n\t\t\t\t\tleft: 0,\r\n\t\t\t\t\twidth: boardWidth,\r\n\t\t\t\t\theight: boardHeight\r\n\t\t\t\t};\r\n\t\t\t\tthis.painter = null\r\n\t\t\t\tif (!this.painter) {\r\n\t\t\t\t\tconst {width} = args.css || args\r\n\t\t\t\t\tconst {height} = args.css || args\r\n\t\t\t\t\tif(!width && this.parentWidth) {\r\n\t\t\t\t\t\tObject.assign(args, {width: this.parentWidth})\r\n\t\t\t\t\t}\r\n\t\t\t\t\tconst param = {\r\n\t\t\t\t\t\tcontext: ctx,\r\n\t\t\t\t\t\tcanvas,\r\n\t\t\t\t\t\twidth: boardWidth,\r\n\t\t\t\t\t\theight: boardHeight,\r\n\t\t\t\t\t\tpixelRatio: this.dpr,\r\n\t\t\t\t\t\tuseCORS: this.useCORS,\r\n\t\t\t\t\t\tcreateImage: getImageInfo.bind(this),\r\n\t\t\t\t\t\tperformance: this.performance,\r\n\t\t\t\t\t\tlisten: {\r\n\t\t\t\t\t\t\tonProgress: (v) => {\r\n\t\t\t\t\t\t\t\tthis.progress = v\r\n\t\t\t\t\t\t\t\tthis.$emit('progress', v)\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tonEffectFail: (err) => {\r\n\t\t\t\t\t\t\t\tthis.$emit('faill', err)\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.painter = new Painter(param)\r\n\t\t\t\t} \r\n\t\t\t\ttry{\r\n\t\t\t\t\t// vue3 赋值给data会引起图片无法绘制\r\n\t\t\t\t\tconst { width, height } = await this.painter.source(JSON.parse(JSON.stringify(args)))\r\n\t\t\t\t\tthis.boundary.height = this.canvasHeight = height\r\n\t\t\t\t\tthis.boundary.width = this.canvasWidth = width\r\n\t\t\t\t\tawait sleep(this.sleep);\r\n\t\t\t\t\tawait this.painter.render()\r\n\t\t\t\t\tawait new Promise(resolve => this.$nextTick(resolve));\r\n\t\t\t\t\tif (!use2dCanvas) {\r\n\t\t\t\t\t\tawait this.canvasDraw();\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (afterDelay && use2dCanvas) {\r\n\t\t\t\t\t\tawait sleep(afterDelay);\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.$emit('done');\r\n\t\t\t\t\tthis.done = true\r\n\t\t\t\t\tif (this.isCanvasToTempFilePath) {\r\n\t\t\t\t\t\tthis.canvasToTempFilePath()\r\n\t\t\t\t\t\t\t.then(res => {\r\n\t\t\t\t\t\t\t\tthis.$emit('success', res.tempFilePath)\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t.catch(err => {\r\n\t\t\t\t\t\t\t\tthis.$emit('fail', new Error(JSON.stringify(err)));\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.runTask()\r\n\t\t\t\t\treturn Promise.resolve({\r\n\t\t\t\t\t\tctx,\r\n\t\t\t\t\t\tdraw: this.painter,\r\n\t\t\t\t\t\tnode: this.node\r\n\t\t\t\t\t});\r\n\t\t\t\t}catch(e){\r\n\t\t\t\t\t//TODO handle the exception\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t},\r\n\t\t\tcanvasDraw(flag = false) {\r\n\t\t\t\treturn new Promise((resolve, reject) => this.ctx.draw(flag, () => setTimeout(() => resolve(), this\r\n\t\t\t\t\t.afterDelay)));\r\n\t\t\t},\r\n\t\t\tasync getContext() {\r\n\t\t\t\tif (!this.canvasWidth) {\r\n\t\t\t\t\tthis.$emit('fail', 'painter no size')\r\n\t\t\t\t\tconsole.error('[lime-painter]: 给画板或父级设置尺寸')\r\n\t\t\t\t\treturn Promise.reject();\r\n\t\t\t\t}\r\n\t\t\t\tif (this.ctx && this.inited) {\r\n\t\t\t\t\treturn Promise.resolve(this.ctx);\r\n\t\t\t\t}\r\n\t\t\t\tconst { type, use2dCanvas, dpr, boardWidth, boardHeight } = this;\r\n\t\t\t\tconst _getContext = () => {\r\n\t\t\t\t\treturn new Promise(resolve => {\r\n\t\t\t\t\t\tuni.createSelectorQuery()\r\n\t\t\t\t\t\t\t.in(this)\r\n\t\t\t\t\t\t\t.select(`#${this.canvasId}`)\r\n\t\t\t\t\t\t\t.boundingClientRect()\r\n\t\t\t\t\t\t\t.exec(res => {\r\n\t\t\t\t\t\t\t\tif (res) {\r\n\t\t\t\t\t\t\t\t\tconst ctx = uni.createCanvasContext(this.canvasId, this);\r\n\t\t\t\t\t\t\t\t\tif (!this.inited) {\r\n\t\t\t\t\t\t\t\t\t\tthis.inited = true;\r\n\t\t\t\t\t\t\t\t\t\tthis.use2dCanvas = false;\r\n\t\t\t\t\t\t\t\t\t\tthis.canvas = res;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t// 钉钉小程序框架不支持 measureText 方法，用此方法 mock\r\n\t\t\t\t\t\t\t\t\tif (!ctx.measureText) {\r\n\t\t\t\t\t\t\t\t\t\tfunction strLen(str) {\r\n\t\t\t\t\t\t\t\t\t\t\tlet len = 0;\r\n\t\t\t\t\t\t\t\t\t\t\tfor (let i = 0; i < str.length; i++) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tif (str.charCodeAt(i) > 0 && str.charCodeAt(i) < 128) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tlen++;\r\n\t\t\t\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tlen += 2;\r\n\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\treturn len;\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\tctx.measureText = text => {\r\n\t\t\t\t\t\t\t\t\t\t\tlet fontSize = ctx.state && ctx.state.fontSize || 12;\r\n\t\t\t\t\t\t\t\t\t\t\tconst font = ctx.__font\r\n\t\t\t\t\t\t\t\t\t\t\tif (font && fontSize == 12) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tfontSize = parseInt(font.split(' ')[3], 10);\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\tfontSize /= 2;\r\n\t\t\t\t\t\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\t\t\t\t\t\twidth: strLen(text) * fontSize\r\n\t\t\t\t\t\t\t\t\t\t\t};\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t// #ifdef MP-ALIPAY\r\n\t\t\t\t\t\t\t\t\tctx.scale(dpr, dpr);\r\n\t\t\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t\t\t\tthis.ctx = ctx\r\n\t\t\t\t\t\t\t\t\tresolve(this.ctx);\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tconsole.error('[lime-painter] no node')\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t});\r\n\t\t\t\t};\r\n\t\t\t\tif (!use2dCanvas) {\r\n\t\t\t\t\treturn _getContext();\r\n\t\t\t\t}\r\n\t\t\t\treturn new Promise(resolve => {\r\n\t\t\t\t\tuni.createSelectorQuery()\r\n\t\t\t\t\t\t.in(this)\r\n\t\t\t\t\t\t.select(`#${this.canvasId}`)\r\n\t\t\t\t\t\t.node()\r\n\t\t\t\t\t\t.exec(res => {\r\n\t\t\t\t\t\t\tlet {node: canvas} = res && res[0]||{};\r\n\t\t\t\t\t\t\tif(canvas) {\r\n\t\t\t\t\t\t\t\tconst ctx = canvas.getContext(type);\r\n\t\t\t\t\t\t\t\tif (!this.inited) {\r\n\t\t\t\t\t\t\t\t\tthis.inited = true;\r\n\t\t\t\t\t\t\t\t\tthis.use2dCanvas = true;\r\n\t\t\t\t\t\t\t\t\tthis.canvas = canvas;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\tthis.ctx = ctx\r\n\t\t\t\t\t\t\t\tresolve(this.ctx);\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tconsole.error('[lime-painter]: no size')\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tcanvasToTempFilePath(args = {}) {\r\n\t\t\t\treturn new Promise(async (resolve, reject) => {\r\n\t\t\t\t\tconst { use2dCanvas, canvasId, dpr, fileType, quality } = this;\r\n\t\t\t\t\tconst success = async (res) => {\r\n\t\t\t\t\t\ttry {\r\n\t\t\t\t\t\t\tconst tempFilePath = await this.setFilePath(res.tempFilePath || res, args)\r\n\t\t\t\t\t\t\tconst result = Object.assign(res, {tempFilePath})\r\n\t\t\t\t\t\t\targs.success && args.success(result)\r\n\t\t\t\t\t\t\tresolve(result)\r\n\t\t\t\t\t\t} catch (e) {\r\n\t\t\t\t\t\t\tthis.$emit('fail', e)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\tlet { top: y = 0, left: x = 0, width, height } = this.boundary || this;\r\n\t\t\t\t\t// let destWidth = width * dpr;\r\n\t\t\t\t\t// let destHeight = height * dpr;\r\n\t\t\t\t\t// #ifdef MP-ALIPAY\r\n\t\t\t\t\t// width = destWidth;\r\n\t\t\t\t\t// height = destHeight;\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\t\r\n\t\t\t\t\tconst copyArgs = Object.assign({\r\n\t\t\t\t\t\t// x,\r\n\t\t\t\t\t\t// y,\r\n\t\t\t\t\t\t// width,\r\n\t\t\t\t\t\t// height,\r\n\t\t\t\t\t\t// destWidth,\r\n\t\t\t\t\t\t// destHeight,\r\n\t\t\t\t\t\tcanvasId,\r\n\t\t\t\t\t\tid: canvasId,\r\n\t\t\t\t\t\tfileType,\r\n\t\t\t\t\t\tquality,\r\n\t\t\t\t\t}, args, {success});\r\n\t\t\t\t\t// if(this.isPC || use2dCanvas) {\r\n\t\t\t\t\t// \tcopyArgs.canvas = this.canvas\r\n\t\t\t\t\t// }\r\n\t\t\t\t\tif (use2dCanvas) {\r\n\t\t\t\t\t\tcopyArgs.canvas = this.canvas\r\n\t\t\t\t\t\ttry{\r\n\t\t\t\t\t\t\t// #ifndef MP-ALIPAY\r\n\t\t\t\t\t\t\tconst oFilePath = this.canvas.toDataURL(`image/${args.fileType||fileType}`.replace(/pg/, 'peg'), args.quality||quality)\r\n\t\t\t\t\t\t\tif(/data:,/.test(oFilePath)) {\r\n\t\t\t\t\t\t\t\tuni.canvasToTempFilePath(copyArgs, this);\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tconst tempFilePath = await this.setFilePath(oFilePath, args)\r\n\t\t\t\t\t\t\t\targs.success && args.success({tempFilePath})\r\n\t\t\t\t\t\t\t\tresolve({tempFilePath})\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t\t// #ifdef MP-ALIPAY\r\n\t\t\t\t\t\t\tthis.canvas.toTempFilePath(copyArgs)\r\n\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t}catch(e){\r\n\t\t\t\t\t\t\targs.fail && args.fail(e)\r\n\t\t\t\t\t\t\treject(e)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// #ifdef MP-ALIPAY\r\n\t\t\t\t\t\tif(this.ctx.toTempFilePath) {\r\n\t\t\t\t\t\t\t// 钉钉\r\n\t\t\t\t\t\t\tconst ctx = uni.createCanvasContext(canvasId);\r\n\t\t\t\t\t\t\tctx.toTempFilePath(copyArgs);\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tmy.canvasToTempFilePath(copyArgs);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t// #ifndef MP-ALIPAY\r\n\t\t\t\t\t\tuni.canvasToTempFilePath(copyArgs, this);\r\n\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t\t// #endif\r\n\t\t}\r\n\t};\r\n</script>\r\n<style>\r\n\t.lime-painter,\r\n\t.lime-painter__canvas {\r\n\t\t// #ifndef APP-NVUE\r\n\t\twidth: 100%;\r\n\t\t// #endif\r\n\t\t// #ifdef APP-NVUE\r\n\t\tflex: 1;\r\n\t\t// #endif\r\n\t}\r\n</style>\r\n", "import Component from 'E:/youngProject/agent-mini-ui/uni_modules/lime-painter/components/l-painter/l-painter.vue'\nwx.createComponent(Component)"], "names": ["props", "parent", "uni", "toPx", "canIUseCanvas2d", "isPC", "sleep", "isBase64", "pathToBase64", "base64ToPath", "getImageInfo", "<PERSON>"], "mappings": ";;;;;;AA4BC,MAAM,OAAO,CAAC;AAKd,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,QAAQ,CAACA,kDAAK,OAAEC,mDAAM,OAAC,SAAS,GAAG,IAAI;AAAA,EACvC,OAAO;AACN,WAAO;AAAA,MACN,aAAa;AAAA,MACb,cAAc;AAAA,MACd,aAAa;AAAA,MACb,aAAa;AAAA,MACb,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,aAAa;AAAA,MACb,MAAM;AAAA,MACN,OAAO,CAAC;AAAA;EAET;AAAA,EACD,UAAU;AAAA,IACT,SAAS;AACR,aAAO,GAAG,KAAK,IAAI,GAAG,KAAK,eAAa,EAAE,OAAO,KAAK,UAAU;AAAA,IAChE;AAAA,IACD,WAAW;AACV,aAAO,YAAY,KAAK,KAAK,KAAK,EAAE,OAAO,KAAK,IAAI;AAAA,IACpD;AAAA,IACD,OAAO;AACN,UAAI,KAAK,cAAc,KAAK,aAAa;AACxC,eAAO,SAAS,KAAK,UAAU,eAAe,KAAK,WAAW;AAAA,MAC/D;AAAA,IACA;AAAA,IACD,MAAM;AACL,aAAO,KAAK,cAAcC,cAAG,MAAC,kBAAiB,EAAG;AAAA,IAClD;AAAA,IACD,aAAa;AACZ,YAAM,EAAC,QAAQ,EAAC,IAAK,KAAK,YAAY,KAAK,SAAS,OAAQ,KAAK,YAAY;AAC7E,YAAM,IAAIC,kDAAI,KAAC,SAAO,KAAK,KAAK;AAChC,aAAO,KAAK,KAAK,IAAI,GAAGA,kDAAAA,KAAK,KAAK,WAAW,CAAC;AAAA,IAC9C;AAAA,IACD,cAAc;AACb,YAAM,EAAC,SAAS,EAAC,IAAK,KAAK,YAAY,KAAK,SAAS,OAAQ,KAAK,YAAY;AAC9E,YAAM,IAAIA,kDAAI,KAAC,UAAQ,KAAK,MAAM;AAClC,aAAO,KAAK,KAAK,IAAI,GAAGA,kDAAAA,KAAK,KAAK,YAAY,CAAC;AAAA,IAC/C;AAAA,IACD,WAAW;AACV,aAAO,KAAK,SAAS,OAAO,KAAK,KAAK,KAAK,EAAE;AAAA,IAC7C;AAAA,IACD,WAAW;AACV,aAAO,KAAK,WAAW,KAAK,QAAQ,KAAK,MAAM,KAAK,UAAU,KAAK,EAAE,CAAC;AAAA,IACvE;AAAA,EACA;AAAA,EACD,UAAU;AACT,SAAK,cAAc,KAAK,SAAS,QAAQC,kDAAAA,gBAAkB,KAAG,CAACC,kDAAG;AAAA,EAClE;AAAA,EACD,MAAM,UAAU;AACf,UAAMC,kDAAAA,MAAM,EAAE;AACd,UAAM,KAAK,eAAe;AAC1B,SAAK,UAAU,MAAM;AACpB,iBAAW,MAAM;AAChB,aAAK,OAAO,YAAY,KAAK,aAAa;AAAA,UACzC,MAAM;AAAA,UACN,WAAW;AAAA,QACZ,CAAC;AAAA,MACD,GAAE,EAAE;AAAA,KACL;AAAA,EACD;AAAA,EAED,YAAY;AACX,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,SAAK,cAAc;AACnB,SAAK,WAAW;AAChB,SAAK,UAAU;AACf,iBAAa,KAAK,WAAW;AAAA,EAC7B;AAAA,EAYD,SAAS;AAAA,IACR,MAAM,YAAY,KAAK,KAAK;AAC3B,UAAI,CAAC,OAAO,CAAC,IAAI,UAAU,CAAC,KAAK,cAAc,CAAC,IAAI,MAAM,SAAS,CAAC,KAAK,gBAAgB,CAAC,OAAO,KAAK,GAAG,EAAE,UAAU,KAAK,UAAU,GAAG,KAAK,KAAK,UAAU,GAAG;AAAG;AACjK,WAAK,cAAc;AACnB,WAAK,WAAW;AAChB,WAAK,OAAO;AACZ,mBAAa,KAAK,WAAW;AAC7B,WAAK,cAAc,WAAW,MAAM;AACnC,aAAK,OAAO,GAAG;AAAA,MAChB,GAAG,KAAK,WAAW;AAAA,IACnB;AAAA,IACD,MAAM,YAAY,MAAM,OAAO;AAC9B,UAAI,WAAW;AACf,YAAM,EAAC,WAAW,KAAK,SAAQ,IAAK,SAAS;AAC7C,UAAI,YAAY,YAAY,CAACC,kDAAQ,SAAC,IAAI,GAAG;AAC5C,mBAAW,MAAMC,kDAAY,aAAC,IAAI;AAAA,MACnC,WAAW,YAAY,SAASD,kDAAQ,SAAC,IAAI,GAAG;AAC/C,mBAAW,MAAME,kDAAY,aAAC,IAAI;AAAA,MACnC;AACA,UAAI,SAAS,MAAM,QAAQ;AAC1B,aAAK,MAAM,WAAW,QAAQ;AAAA,MAC/B;AACA,aAAO;AAAA,IACP;AAAA,IACD,MAAM,QAAQ,MAAM;AACnB,YAAM,EAAC,MAAK,IAAI,KAAK,OAAO;AAC5B,YAAM,EAAC,OAAM,IAAI,KAAK,OAAO;AAC7B,UAAI,CAAC,KAAK,MAAM;AACf,YAAI,SAAS,QAAQ;AACpB,eAAK,cAAc,SAAS,KAAK;AACjC,eAAK,eAAe,UAAU,KAAK;AACnC,gBAAMH,kDAAAA,MAAM,EAAE;AAAA,eACR;AACN,gBAAM,KAAK,eAAe;AAAA,QAC3B;AAAA,MACD;AAAA,IACA;AAAA,IACD,yBAAyB,MAAM;AAU9B,WAAK,MAAM,KAAK,IAAI;AACpB,UAAG,KAAK,MAAK;AACZ,aAAK,QAAQ;AAAA,MACd;AAAA,IACA;AAAA,IACD,UAAS;AACR,aAAM,KAAK,MAAM,QAAO;AACvB,cAAM,OAAO,KAAK,MAAM,MAAM;AAC7B,aAAK,qBAAqB,IAAI;AAAA,MAChC;AAAA,IACA;AAAA,IAED,iBAAiB;AAChB,aAAO,IAAI,QAAQ,aAAW;AAC7BJ,sBAAAA,MAAI,oBAAoB,EACtB,GAAG,IAAI,EACP,OAAO,eAAe,EACtB,mBAAmB,EACnB,KAAK,SAAO;AACZ,gBAAM,EAAC,OAAO,OAAM,IAAI,IAAI,CAAC,KAAG,CAAC;AACjC,eAAK,cAAc,KAAK,KAAK,SAAO,CAAC;AACrC,eAAK,cAAc,KAAK,eAAe;AACvC,eAAK,eAAe,UAAU,KAAK,gBAAc;AACjD,kBAAQ,IAAI,CAAC,CAAC;AAAA,SACd;AAAA,OACF;AAAA,IACD;AAAA,IACD,MAAM,OAAO,OAAO,IAAI;AACvB,UAAG,CAAC,OAAO,KAAK,IAAI,EAAE,QAAQ;AAC7B,eAAOA,yGAAc,KAAK;AAAA,MAC3B;AACA,WAAK,WAAW;AAChB,WAAK,OAAO;AAIZ,YAAM,KAAK,QAAQ,IAAI;AACvB,YAAM,MAAM,MAAM,KAAK;AAEvB,UAAI;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACD,IAAI;AACJ,UAAI,eAAe,CAAC,QAAQ;AAC3B,eAAO,QAAQ,OAAO,IAAI,MAAM,YAAY,CAAC;AAAA,MAC9C;AACA,WAAK,WAAW;AAAA,QACf,KAAK;AAAA,QACL,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ;AAAA;AAET,WAAK,UAAU;AACf,UAAI,CAAC,KAAK,SAAS;AAClB,cAAM,EAAC,MAAK,IAAI,KAAK,OAAO;AACX,aAAK,OAAO;AAC7B,YAAG,CAAC,SAAS,KAAK,aAAa;AAC9B,iBAAO,OAAO,MAAM,EAAC,OAAO,KAAK,YAAW,CAAC;AAAA,QAC9C;AACA,cAAM,QAAQ;AAAA,UACb,SAAS;AAAA,UACT;AAAA,UACA,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,YAAY,KAAK;AAAA,UACjB,SAAS,KAAK;AAAA,UACd,aAAaQ,kDAAAA,aAAa,KAAK,IAAI;AAAA,UACnC,aAAa,KAAK;AAAA,UAClB,QAAQ;AAAA,YACP,YAAY,CAAC,MAAM;AAClB,mBAAK,WAAW;AAChB,mBAAK,MAAM,YAAY,CAAC;AAAA,YACxB;AAAA,YACD,cAAc,CAAC,QAAQ;AACtB,mBAAK,MAAM,SAAS,GAAG;AAAA,YACxB;AAAA,UACD;AAAA,QACD;AACA,aAAK,UAAU,IAAIC,oDAAO,GAAC,KAAK;AAAA,MACjC;AACA,UAAG;AAEF,cAAM,EAAE,OAAO,WAAW,MAAM,KAAK,QAAQ,OAAO,KAAK,MAAM,KAAK,UAAU,IAAI,CAAC,CAAC;AACpF,aAAK,SAAS,SAAS,KAAK,eAAe;AAC3C,aAAK,SAAS,QAAQ,KAAK,cAAc;AACzC,cAAML,kDAAK,MAAC,KAAK,KAAK;AACtB,cAAM,KAAK,QAAQ,OAAO;AAC1B,cAAM,IAAI,QAAQ,aAAW,KAAK,UAAU,OAAO,CAAC;AACpD,YAAI,CAAC,aAAa;AACjB,gBAAM,KAAK;QACZ;AACA,YAAI,cAAc,aAAa;AAC9B,gBAAMA,kDAAAA,MAAM,UAAU;AAAA,QACvB;AACA,aAAK,MAAM,MAAM;AACjB,aAAK,OAAO;AACZ,YAAI,KAAK,wBAAwB;AAChC,eAAK,qBAAqB,EACxB,KAAK,SAAO;AACZ,iBAAK,MAAM,WAAW,IAAI,YAAY;AAAA,WACtC,EACA,MAAM,SAAO;AACb,iBAAK,MAAM,QAAQ,IAAI,MAAM,KAAK,UAAU,GAAG,CAAC,CAAC;AAAA,UAClD,CAAC;AAAA,QACH;AACA,aAAK,QAAQ;AACb,eAAO,QAAQ,QAAQ;AAAA,UACtB;AAAA,UACA,MAAM,KAAK;AAAA,UACX,MAAM,KAAK;AAAA,QACZ,CAAC;AAAA,MACD,SAAM,GAAE;AAAA,MAET;AAAA,IAEA;AAAA,IACD,WAAW,OAAO,OAAO;AACxB,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW,KAAK,IAAI,KAAK,MAAM,MAAM,WAAW,MAAM,QAAS,GAAE,KAC5F,UAAU,CAAC,CAAC;AAAA,IACd;AAAA,IACD,MAAM,aAAa;AAClB,UAAI,CAAC,KAAK,aAAa;AACtB,aAAK,MAAM,QAAQ,iBAAiB;AACpCJ,sBAAAA,2FAAc,4BAA4B;AAC1C,eAAO,QAAQ;MAChB;AACA,UAAI,KAAK,OAAO,KAAK,QAAQ;AAC5B,eAAO,QAAQ,QAAQ,KAAK,GAAG;AAAA,MAChC;AACA,YAAM,EAAE,MAAM,aAAa,KAAK,YAAY,YAAY,IAAI;AAC5D,YAAM,cAAc,MAAM;AACzB,eAAO,IAAI,QAAQ,aAAW;AAC7BA,wBAAAA,MAAI,oBAAoB,EACtB,GAAG,IAAI,EACP,OAAO,IAAI,KAAK,QAAQ,EAAE,EAC1B,mBAAmB,EACnB,KAAK,SAAO;AACZ,gBAAI,KAAK;AACR,oBAAM,MAAMA,cAAAA,MAAI,oBAAoB,KAAK,UAAU,IAAI;AACvD,kBAAI,CAAC,KAAK,QAAQ;AACjB,qBAAK,SAAS;AACd,qBAAK,cAAc;AACnB,qBAAK,SAAS;AAAA,cACf;AAGA,kBAAI,CAAC,IAAI,aAAa;AACrB,oBAAS,SAAT,SAAgB,KAAK;AACpB,sBAAI,MAAM;AACV,2BAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACpC,wBAAI,IAAI,WAAW,CAAC,IAAI,KAAK,IAAI,WAAW,CAAC,IAAI,KAAK;AACrD;AAAA,2BACM;AACN,6BAAO;AAAA,oBACR;AAAA,kBACD;AACA,yBAAO;AAAA,gBACR;AACA,oBAAI,cAAc,UAAQ;AACzB,sBAAI,WAAW,IAAI,SAAS,IAAI,MAAM,YAAY;AAClD,wBAAM,OAAO,IAAI;AACjB,sBAAI,QAAQ,YAAY,IAAI;AAC3B,+BAAW,SAAS,KAAK,MAAM,GAAG,EAAE,CAAC,GAAG,EAAE;AAAA,kBAC3C;AACA,8BAAY;AACZ,yBAAO;AAAA,oBACN,OAAO,OAAO,IAAI,IAAI;AAAA;gBAExB;AAAA,cACD;AAKA,mBAAK,MAAM;AACX,sBAAQ,KAAK,GAAG;AAAA,mBACV;AACNA,4BAAAA,MAAA,MAAA,SAAA,sEAAc,wBAAwB;AAAA,YACvC;AAAA,UACD,CAAC;AAAA,QACH,CAAC;AAAA;AAEF,UAAI,CAAC,aAAa;AACjB,eAAO,YAAW;AAAA,MACnB;AACA,aAAO,IAAI,QAAQ,aAAW;AAC7BA,sBAAAA,MAAI,oBAAoB,EACtB,GAAG,IAAI,EACP,OAAO,IAAI,KAAK,QAAQ,EAAE,EAC1B,KAAK,EACL,KAAK,SAAO;AACZ,cAAI,EAAC,MAAM,OAAM,IAAI,OAAO,IAAI,CAAC,KAAG;AACpC,cAAG,QAAQ;AACV,kBAAM,MAAM,OAAO,WAAW,IAAI;AAClC,gBAAI,CAAC,KAAK,QAAQ;AACjB,mBAAK,SAAS;AACd,mBAAK,cAAc;AACnB,mBAAK,SAAS;AAAA,YACf;AACA,iBAAK,MAAM;AACX,oBAAQ,KAAK,GAAG;AAAA,iBACV;AACNA,0BAAAA,MAAc,MAAA,SAAA,sEAAA,yBAAyB;AAAA,UACxC;AAAA,QACD,CAAC;AAAA,MACH,CAAC;AAAA,IACD;AAAA,IACD,qBAAqB,OAAO,IAAI;AAC/B,aAAO,IAAI,QAAQ,OAAO,SAAS,WAAW;AAC7C,cAAM,EAAE,aAAa,UAAU,KAAK,UAAU,YAAY;AAC1D,cAAM,UAAU,OAAO,QAAQ;AAC9B,cAAI;AACH,kBAAM,eAAe,MAAM,KAAK,YAAY,IAAI,gBAAgB,KAAK,IAAI;AACzE,kBAAM,SAAS,OAAO,OAAO,KAAK,EAAC,aAAY,CAAC;AAChD,iBAAK,WAAW,KAAK,QAAQ,MAAM;AACnC,oBAAQ,MAAM;AAAA,UACf,SAAS,GAAG;AACX,iBAAK,MAAM,QAAQ,CAAC;AAAA,UACrB;AAAA,QACD;AAEiD,aAAK,YAAY;AAQlE,cAAM,WAAW,OAAO,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAO9B;AAAA,UACA,IAAI;AAAA,UACJ;AAAA,UACA;AAAA,QACD,GAAG,MAAM,EAAC,QAAO,CAAC;AAIlB,YAAI,aAAa;AAChB,mBAAS,SAAS,KAAK;AACvB,cAAG;AAEF,kBAAM,YAAY,KAAK,OAAO,UAAU,SAAS,KAAK,YAAU,QAAQ,GAAG,QAAQ,MAAM,KAAK,GAAG,KAAK,WAAS,OAAO;AACtH,gBAAG,SAAS,KAAK,SAAS,GAAG;AAC5BA,4BAAAA,MAAI,qBAAqB,UAAU,IAAI;AAAA,mBACjC;AACN,oBAAM,eAAe,MAAM,KAAK,YAAY,WAAW,IAAI;AAC3D,mBAAK,WAAW,KAAK,QAAQ,EAAC,aAAY,CAAC;AAC3C,sBAAQ,EAAC,aAAY,CAAC;AAAA,YACvB;AAAA,UAKA,SAAM,GAAE;AACR,iBAAK,QAAQ,KAAK,KAAK,CAAC;AACxB,mBAAO,CAAC;AAAA,UACT;AAAA,eACM;AAWNA,wBAAAA,MAAI,qBAAqB,UAAU,IAAI;AAAA,QAExC;AAAA,OACA;AAAA,IACF;AAAA,EAED;;;;;;;;;;;;;;;;;;;;;;AC9bF,GAAG,gBAAgB,SAAS;"}