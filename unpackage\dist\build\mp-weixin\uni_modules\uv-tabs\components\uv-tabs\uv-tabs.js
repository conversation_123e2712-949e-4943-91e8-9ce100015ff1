"use strict";const e=require("../../../uv-ui-tools/libs/mixin/mpMixin.js"),t=require("../../../uv-ui-tools/libs/mixin/mixin.js"),i=require("../../../uv-badge/components/uv-badge/props.js"),s=require("./props.js"),r=require("../../../../common/vendor.js"),n={name:"uv-tabs",emits:["click","change"],mixins:[e.mpMixin,t.mixin,s.props],data:()=>({firstTime:!0,scrollLeft:0,scrollViewWidth:0,lineOffsetLeft:0,tabsRect:{left:0},innerCurrent:0,moving:!1}),watch:{current:{immediate:!0,handler(e,t){e!==this.innerCurrent&&(this.innerCurrent=e,this.$nextTick((()=>{this.resize()})))}},list(){this.$nextTick((()=>{this.resize()}))}},computed:{textStyle(){return e=>{const t={},i=e==this.innerCurrent?this.$uv.addStyle(this.activeStyle):this.$uv.addStyle(this.inactiveStyle);return this.list[e].disabled&&(t.color="#c8c9cc"),this.$uv.deepMerge(i,t)}},propsBadge:()=>i.props},async mounted(){this.init()},methods:{setLineLeft(){const e=this.list[this.innerCurrent];if(!e)return;let t=this.list.slice(0,this.innerCurrent).reduce(((e,t)=>e+t.rect.width),0),i=this.$uv.getPx(this.lineWidth);this.$uv.test.number(this.lineWidth)&&this.$uv.unit&&(i=this.$uv.getPx(`${this.lineWidth}${this.$uv.unit}`)),this.lineOffsetLeft=t+(e.rect.width-i)/2,this.firstTime&&setTimeout((()=>{this.firstTime=!1}),20)},animation(e,t=0){},clickHandler(e,t){this.$emit("click",{...e,index:t}),e.disabled||(this.innerCurrent!=t&&this.$emit("change",{...e,index:t}),this.innerCurrent=t,this.$nextTick((()=>{this.resize()})))},init(){this.$uv.sleep().then((()=>{this.resize()}))},setScrollLeft(){const e=this.list[this.innerCurrent],t=this.list.slice(0,this.innerCurrent).reduce(((e,t)=>e+t.rect.width),0),i=this.$uv.sys().windowWidth;let s=t-(this.tabsRect.width-e.rect.width)/2-(i-this.tabsRect.right)/2+this.tabsRect.left/2;s=Math.min(s,this.scrollViewWidth-this.tabsRect.width),this.scrollLeft=Math.max(0,s)},resize(){0!==this.list.length&&Promise.all([this.getTabsRect(),this.getAllItemRect()]).then((([e,t=[]])=>{this.tabsRect=e,this.scrollViewWidth=0,t.map(((e,t)=>{this.scrollViewWidth+=e.width,this.list[t].rect=e})),this.setLineLeft(),this.setScrollLeft()}))},getTabsRect(){return new Promise((e=>{this.queryRect("uv-tabs__wrapper__scroll-view").then((t=>e(t)))}))},getAllItemRect(){return new Promise((e=>{const t=this.list.map(((e,t)=>this.queryRect(`uv-tabs__wrapper__nav__item-${t}`,!0)));Promise.all(t).then((t=>e(t)))}))},queryRect(e,t){return new Promise((t=>{this.$uvGetRect(`.${e}`).then((e=>{t(e)}))}))}}};if(!Array){r.resolveComponent("uv-badge")()}Math;const a=r._export_sfc(n,[["render",function(e,t,i,s,n,a){return{a:r.f(e.list,((t,i,s)=>({a:r.t(t[e.keyName]),b:r.n(t.disabled&&"uv-tabs__wrapper__nav__item__text--disabled"),c:r.s(a.textStyle(i)),d:"da7392b4-0-"+s,e:r.p({show:!(!t.badge||!(t.badge.show||t.badge.isDot||t.badge.value)),isDot:t.badge&&t.badge.isDot||a.propsBadge.isDot,value:t.badge&&t.badge.value||a.propsBadge.value,max:t.badge&&t.badge.max||a.propsBadge.max,type:t.badge&&t.badge.type||a.propsBadge.type,showZero:t.badge&&t.badge.showZero||a.propsBadge.showZero,bgColor:t.badge&&t.badge.bgColor||a.propsBadge.bgColor,color:t.badge&&t.badge.color||a.propsBadge.color,shape:t.badge&&t.badge.shape||a.propsBadge.shape,numberType:t.badge&&t.badge.numberType||a.propsBadge.numberType,inverted:t.badge&&t.badge.inverted||a.propsBadge.inverted,customStyle:"margin-left: 4px;"}),f:i,g:r.o((e=>a.clickHandler(t,i)),i),h:`uv-tabs__wrapper__nav__item-${i}`,i:r.n(`uv-tabs__wrapper__nav__item-${i}`),j:r.n(t.disabled&&"uv-tabs__wrapper__nav__item--disabled")}))),b:r.s({flex:e.scrollable?"":1}),c:r.s(e.$uv.addStyle(e.itemStyle)),d:r.s({width:e.$uv.addUnit(e.lineWidth),transform:`translate(${n.lineOffsetLeft}px)`,transitionDuration:`${n.firstTime?0:e.duration}ms`,height:n.firstTime?0:e.$uv.addUnit(e.lineHeight),background:e.lineColor,backgroundSize:e.lineBgSize}),e:e.scrollable?"":1,f:e.scrollable,g:n.scrollLeft,h:r.s(e.$uv.addStyle(e.customStyle))}}],["__scopeId","data-v-da7392b4"]]);wx.createComponent(a);
