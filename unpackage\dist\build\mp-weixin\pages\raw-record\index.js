"use strict";const e=require("../../common/vendor.js"),s=require("../../common/assets.js"),t={__name:"index",setup(t){const a=e.ref([]),r=e=>{switch(e){case"success":return"status-success";case"failed":return"status-failed";case"processing":return"status-processing";default:return""}};return e.onLoad((()=>{(async()=>{try{console.log("加载提现记录")}catch(s){console.error("加载提现记录失败:",s),e.index.showToast({title:"加载失败",icon:"none"})}})()})),(t,c)=>e.e({a:e.f(a.value,((s,t,a)=>({a:e.t(s.amount),b:e.t(s.statusText),c:e.n(r(s.status)),d:e.t(s.withdrawTime),e:t}))),b:0===a.value.length},0===a.value.length?{c:s._imports_0$10}:{})}},a=e._export_sfc(t,[["__scopeId","data-v-441b451f"]]);wx.createPage(a);
