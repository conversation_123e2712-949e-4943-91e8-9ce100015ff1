/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.secret-edit-page.data-v-358c5fc5 {
  background: #F5F5F5;
  min-height: 100vh;
}
.form-section.data-v-358c5fc5 {
  padding: 20px 32rpx;
}
.form-section .form-item.data-v-358c5fc5 {
  display: flex;
  align-items: center;
  padding: 20rpx 16rpx;
  background: #ffffff;
  border-radius: 20rpx;
  margin-bottom: 20px;
}
.form-section .form-item.data-v-358c5fc5:last-child {
  border-bottom: none;
}
.form-section .form-item .label.data-v-358c5fc5 {
  font-size: 30rpx;
  color: #1a1a1a;
  font-weight: 500;
  width: 120rpx;
  flex-shrink: 0;
}
.form-section .form-item .input.data-v-358c5fc5 {
  flex: 1;
  font-size: 28rpx;
  color: #1a1a1a;
  margin-left: 24rpx;
  height: 44rpx;
  line-height: 44rpx;
}
.form-section .form-item .input.placeholder.data-v-358c5fc5 {
  color: #CCCCCC;
}
.save-section.data-v-358c5fc5 {
  padding: 80rpx 32rpx;
  padding-bottom: calc(80rpx + env(safe-area-inset-bottom));
}
.save-section .save-btn.data-v-358c5fc5 {
  width: 100%;
  height: 96rpx;
  background: #3478f6;
  border-radius: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}
.save-section .save-btn.disabled.data-v-358c5fc5 {
  background: #CCCCCC;
}
.save-section .save-btn .save-text.data-v-358c5fc5 {
  font-size: 32rpx;
  color: #ffffff;
  font-weight: 600;
}

/* 占位符样式 */
.placeholder.data-v-358c5fc5 {
  color: #CCCCCC !important;
}