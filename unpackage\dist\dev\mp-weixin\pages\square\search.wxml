<view class="container data-v-ddee21bb"><view class="search-container data-v-ddee21bb"><view class="search-box data-v-ddee21bb"><image src="{{a}}" class="search-icon data-v-ddee21bb" mode="aspectFit"></image><input class="search-input data-v-ddee21bb" placeholder="搜索智能体" placeholder-class="placeholder" bindinput="{{b}}" bindconfirm="{{c}}" focus value="{{d}}"/><view wx:if="{{e}}" class="clear-btn data-v-ddee21bb" bindtap="{{f}}"><text class="clear-text data-v-ddee21bb">×</text></view></view><view class="cancel-btn data-v-ddee21bb" bindtap="{{g}}"><text class="cancel-text data-v-ddee21bb">取消</text></view></view><view class="content-container data-v-ddee21bb"><view wx:if="{{h}}" class="loading-container data-v-ddee21bb"><text class="loading-text data-v-ddee21bb">搜索中...</text></view><view wx:elif="{{i}}" class="empty-container data-v-ddee21bb"><image src="{{j}}" class="empty-icon data-v-ddee21bb" mode="aspectFit"></image><text class="empty-text data-v-ddee21bb">未找到相关智能体</text><text class="empty-desc data-v-ddee21bb">试试其他关键词吧</text></view><scroll-view wx:elif="{{k}}" scroll-y="true" class="scroll-view data-v-ddee21bb" bindscrolltolower="{{p}}"><view class="agent-list data-v-ddee21bb"><view wx:for="{{l}}" wx:for-item="item" wx:key="j" class="agent-item data-v-ddee21bb"><view class="avatar data-v-ddee21bb" bindtap="{{item.b}}"><image src="{{item.a}}" class="avatar-img data-v-ddee21bb" mode="aspectFill"></image></view><view class="content data-v-ddee21bb" bindtap="{{item.f}}"><view class="title data-v-ddee21bb">{{item.c}}</view><view class="description data-v-ddee21bb">{{item.d}}</view><view class="author data-v-ddee21bb">@{{item.e}}</view></view><view class="{{['action-btn', 'data-v-ddee21bb', item.h && 'subscribed']}}" catchtap="{{item.i}}"><text class="btn-text data-v-ddee21bb">{{item.g}}</text></view></view></view><view wx:if="{{m}}" class="load-more data-v-ddee21bb"><text class="load-more-text data-v-ddee21bb">{{n}}</text></view><view wx:elif="{{o}}" class="no-more data-v-ddee21bb"><text class="no-more-text data-v-ddee21bb">没有更多了</text></view></scroll-view><view wx:else class="default-container data-v-ddee21bb"><image src="{{q}}" class="default-icon data-v-ddee21bb" mode="aspectFit"></image><text class="default-text data-v-ddee21bb">输入关键词搜索智能体</text></view></view></view>