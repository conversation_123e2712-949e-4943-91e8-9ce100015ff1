"use strict";
const common_vendor = require("../../common/vendor.js");
const api_index = require("../../api/index.js");
const stores_user = require("../../stores/user.js");
const api_common = require("../../api/common.js");
const defaultAvatar = "https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/c4d5b3ada53740d5b862f274ce48ef0c.png";
const _sfc_main = {
  __name: "vip-list",
  setup(__props) {
    const userStore = stores_user.useUserStore();
    const userInfo = common_vendor.computed(() => {
      return userStore.userInfo;
    });
    const vipPlans = common_vendor.ref([]);
    const getVipPackageList = async () => {
      try {
        let res = await api_index.getVipPackageListApi({
          merchantGuid: userStore.merchantGuid
        });
        vipPlans.value = res.data.packages;
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/my/vip-list.vue:90", "获取会员套餐列表失败:", error);
        common_vendor.index.showToast({
          title: "加载失败",
          icon: "none"
        });
      }
    };
    const selectedPlan = common_vendor.ref(0);
    const queryStatusNum = common_vendor.ref(0);
    const rule = common_vendor.ref("");
    const getSubscriptionRule = async () => {
      try {
        const res = await api_index.getSubscriptionRuleApi({
          merchantGuid: userStore.merchantGuid
        });
        if (res.code === 0) {
          rule.value = res.data.member_card_notice;
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/my/vip-list.vue:113", "获取规则失败:", error);
      }
    };
    const isYearCrad = common_vendor.ref(false);
    const selectPlan = (index) => {
      selectedPlan.value = index;
      if (vipPlans.value[selectedPlan.value].packageType === 3) {
        isYearCrad.value = true;
      } else {
        isYearCrad.value = false;
      }
    };
    const shenqun_img = common_vendor.ref("");
    const showImageModal = common_vendor.ref(false);
    const showBannerUrls = async () => {
      let res = await api_index.showBannerUrlsApi({
        merchantGuid: userStore.merchantGuid
      });
      shenqun_img.value = res.data.buy_banner_img;
    };
    const onShowPoster = () => {
      if (isYearCrad.value) {
        showImageModal.value = true;
      }
    };
    const closeImageModal = () => {
      showImageModal.value = false;
    };
    const handleSubscribe = async () => {
      if (!userStore.userToken) {
        common_vendor.index.showToast({
          title: "请先登录",
          icon: "none"
        });
        return;
      }
      if (isYearCrad.value) {
        onShowPoster();
        return;
      }
      const currentPlan = vipPlans.value[selectedPlan.value];
      try {
        common_vendor.index.showLoading({
          title: "正在创建订单...",
          mask: true
        });
        const payInfo = await api_index.createVipOrderApi({
          merchantGuid: userStore.merchantGuid,
          packageGuid: currentPlan.guid,
          payEnv: "xcx"
        });
        common_vendor.index.hideLoading();
        api_common.miniPay(payInfo.data.payInfo).then(
          async () => {
            queryPayStatus(payInfo.data.orderNo, queryStatusNum.value);
          },
          (res) => {
            common_vendor.index.showToast({
              title: res.msg || "支付失败",
              icon: "none"
            });
          }
        );
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at pages/my/vip-list.vue:194", "创建订单失败:", error);
        common_vendor.index.showToast({
          title: error.message || "创建订单失败",
          icon: "none"
        });
      }
    };
    const queryPayStatus = async (orderNo, number) => {
      number++;
      try {
        const orderInfo = await api_index.queryVipOrderApi({
          orderNo
        });
        if (orderInfo.data.isPaid) {
          common_vendor.index.showToast({
            title: "支付成功",
            icon: "success"
          });
          setTimeout(() => {
            common_vendor.index.navigateBack();
          }, 1500);
        } else {
          if (number > 12) {
            common_vendor.index.showToast({
              title: "支付超时",
              icon: "none"
            });
          } else {
            setTimeout(() => {
              queryPayStatus(orderNo, number);
            }, 2e3);
          }
        }
      } catch (error) {
        common_vendor.index.showToast({
          title: error.msg || "查询支付状态失败",
          icon: "none"
        });
      }
    };
    let vipInfo = common_vendor.reactive({
      hasMembership: false,
      isExpired: true,
      membership: null,
      remainingDays: 0
    });
    const getVipInfo = async () => {
      try {
        let res = await api_index.getUserVipInfoApi({
          merchantGuid: userStore.merchantGuid
        });
        vipInfo = Object.assign(vipInfo, res.data);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/my/vip-list.vue:252", "获取会员信息失败:", error);
        common_vendor.index.showToast({
          title: "加载失败",
          icon: "none"
        });
      }
    };
    const isIos = common_vendor.ref(false);
    common_vendor.onLoad(() => {
      let systemInfomations = common_vendor.index.getSystemInfoSync();
      if (systemInfomations.osName === "ios") {
        isIos.value = true;
      }
      getSubscriptionRule();
      showBannerUrls();
    });
    common_vendor.onShow(() => {
      if (userStore.userToken) {
        getVipPackageList();
        getVipInfo();
        getSubscriptionRule();
        showBannerUrls();
      }
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: userInfo.value.headImgUrl || defaultAvatar,
        b: common_vendor.t(userInfo.value.nickname || "用户Aric"),
        c: !common_vendor.unref(vipInfo).hasMembership
      }, !common_vendor.unref(vipInfo).hasMembership ? {} : {
        d: common_vendor.t(common_vendor.unref(vipInfo).remainingDays)
      }, {
        e: !common_vendor.unref(vipInfo).hasMembership
      }, !common_vendor.unref(vipInfo).hasMembership ? {} : {}, {
        f: common_vendor.t(rule.value),
        g: common_vendor.f(vipPlans.value, (plan, index, i0) => {
          return {
            a: common_vendor.t(plan.packageName),
            b: common_vendor.t(plan.salePriceYuan),
            c: common_vendor.t(plan.durationDays),
            d: common_vendor.t(plan.packageDesc),
            e: index,
            f: common_vendor.n({
              "selected": selectedPlan.value === index
            }),
            g: common_vendor.o(($event) => selectPlan(index), index)
          };
        }),
        h: common_vendor.o(handleSubscribe),
        i: showImageModal.value
      }, showImageModal.value ? common_vendor.e({
        j: shenqun_img.value
      }, shenqun_img.value ? {
        k: shenqun_img.value,
        l: common_vendor.o((...args) => _ctx.onImageLoad && _ctx.onImageLoad(...args)),
        m: common_vendor.o((...args) => _ctx.onImageError && _ctx.onImageError(...args))
      } : {}, {
        n: common_vendor.o(closeImageModal)
      }) : {});
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-21e058a8"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/my/vip-list.js.map
