{"version": 3, "file": "back-to-top.js", "sources": ["uni_modules/z-paging/components/z-paging/js/modules/back-to-top.js"], "sourcesContent": ["// [z-paging]点击返回顶部view模块\r\nimport u from '.././z-paging-utils'\r\n\r\nexport default {\r\n\tprops: {\r\n\t\t// 自动显示点击返回顶部按钮，默认为否\r\n\t\tautoShowBackToTop: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('autoShowBackToTop', false)\r\n\t\t},\r\n\t\t// 点击返回顶部按钮显示/隐藏的阈值(滚动距离)，单位为px，默认为400rpx\r\n\t\tbackToTopThreshold: {\r\n\t\t\ttype: [Number, String],\r\n\t\t\tdefault: u.gc('backToTopThreshold', '400rpx')\r\n\t\t},\r\n\t\t// 点击返回顶部按钮的自定义图片地址，默认使用z-paging内置的图片\r\n\t\tbackToTopImg: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: u.gc('backToTopImg', '')\r\n\t\t},\r\n\t\t// 点击返回顶部按钮返回到顶部时是否展示过渡动画，默认为是\r\n\t\tbackToTopWithAnimate: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('backToTopWithAnimate', true)\r\n\t\t},\r\n\t\t// 点击返回顶部按钮与底部的距离，注意添加单位px或rpx，默认为160rpx\r\n\t\tbackToTopBottom: {\r\n\t\t\ttype: [Number, String],\r\n\t\t\tdefault: u.gc('backToTopBottom', '160rpx')\r\n\t\t},\r\n\t\t// 点击返回顶部按钮的自定义样式\r\n\t\tbackToTopStyle: {\r\n\t\t\ttype: Object,\r\n\t\t\tdefault: u.gc('backToTopStyle', {}),\r\n\t\t},\r\n\t\t// iOS点击顶部状态栏、安卓双击标题栏时，滚动条返回顶部，只支持竖向，默认为是\r\n\t\tenableBackToTop: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('enableBackToTop', true)\r\n\t\t},\r\n\t},\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\t// 点击返回顶部的class\r\n\t\t\tbackToTopClass: 'zp-back-to-top zp-back-to-top-hide',\r\n\t\t\t// 上次点击返回顶部的时间\r\n\t\t\tlastBackToTopShowTime: 0,\r\n\t\t\t// 点击返回顶部显示的class是否在展示中，使得按钮展示/隐藏过度效果更自然\r\n\t\t\tshowBackToTopClass: false,\r\n\t\t}\r\n\t},\r\n\tcomputed: {\r\n\t\tbackToTopThresholdUnitConverted() {\r\n\t\t\treturn u.addUnit(this.backToTopThreshold, this.unit);\r\n\t\t},\r\n\t\tbackToTopBottomUnitConverted() {\r\n\t\t\treturn u.addUnit(this.backToTopBottom, this.unit);\r\n\t\t},\r\n\t\tfinalEnableBackToTop() {\r\n\t\t\treturn this.usePageScroll ? false : this.enableBackToTop;\r\n\t\t},\r\n\t\tfinalBackToTopThreshold() {\r\n\t\t\treturn u.convertToPx(this.backToTopThresholdUnitConverted);\r\n\t\t},\r\n\t\tfinalBackToTopStyle() {\r\n\t\t\tconst backToTopStyle = this.backToTopStyle;\r\n\t\t\tif (!backToTopStyle.bottom) {\r\n\t\t\t\tbackToTopStyle.bottom = this.windowBottom + u.convertToPx(this.backToTopBottomUnitConverted) + 'px';\r\n\t\t\t}\r\n\t\t\tif(!backToTopStyle.position){\r\n\t\t\t\tbackToTopStyle.position = this.usePageScroll ? 'fixed': 'absolute';\r\n\t\t\t}\r\n\t\t\treturn backToTopStyle;\r\n\t\t},\r\n\t\tfinalBackToTopClass() {\r\n\t\t\treturn `${this.backToTopClass} zp-back-to-top-${this.unit}`;\r\n\t\t}\r\n\t},\r\n\tmethods: {\r\n\t\t// 点击了返回顶部\r\n\t\t_backToTopClick() {\r\n\t\t\tlet callbacked = false;\r\n\t\t\tthis.$emit('backToTopClick', toTop => {\r\n\t\t\t\t(toTop === undefined || toTop === true) && this._handleToTop();\r\n\t\t\t\tcallbacked = true;\r\n\t\t\t});\r\n\t\t\t// 如果用户没有禁止默认的返回顶部事件，则触发滚动到顶部\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t!callbacked && this._handleToTop();\r\n\t\t\t})\r\n\t\t},\r\n\t\t// 处理滚动到顶部\r\n\t\t_handleToTop() {\r\n\t\t\t!this.backToTopWithAnimate && this._checkShouldShowBackToTop(0);\r\n\t\t\tthis.scrollToTop(this.backToTopWithAnimate);\r\n\t\t},\r\n\t\t// 判断是否要显示返回顶部按钮\r\n\t\t_checkShouldShowBackToTop(scrollTop) {\r\n\t\t\tif (!this.autoShowBackToTop) {\r\n\t\t\t\tthis.showBackToTopClass = false;\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\tif (scrollTop > this.finalBackToTopThreshold) {\r\n\t\t\t\tif (!this.showBackToTopClass) {\r\n\t\t\t\t\t// 记录当前点击返回顶部按钮显示的class生效了\r\n\t\t\t\t\tthis.showBackToTopClass = true;\r\n\t\t\t\t\tthis.lastBackToTopShowTime = new Date().getTime();\r\n\t\t\t\t\t// 当滚动到需要展示返回顶部的阈值内，则延迟300毫秒展示返回到顶部按钮\r\n\t\t\t\t\tu.delay(() => {\r\n\t\t\t\t\t\tthis.backToTopClass = 'zp-back-to-top zp-back-to-top-show';\r\n\t\t\t\t\t}, 300)\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\t// 如果当前点击返回顶部按钮显示的class是生效状态并且滚动小于触发阈值，则隐藏返回顶部按钮\r\n\t\t\t\tif (this.showBackToTopClass) {\r\n\t\t\t\t\tthis.backToTopClass = 'zp-back-to-top zp-back-to-top-hide';\r\n\t\t\t\t\tu.delay(() => {\r\n\t\t\t\t\t\tthis.showBackToTopClass = false;\r\n\t\t\t\t\t}, new Date().getTime() - this.lastBackToTopShowTime < 500 ? 0 : 300)\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t}\r\n}\r\n\r\n"], "names": ["u"], "mappings": ";;AAGA,MAAe,kBAAA;AAAA,EACd,OAAO;AAAA;AAAA,IAEN,mBAAmB;AAAA,MAClB,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,qBAAqB,KAAK;AAAA,IACxC;AAAA;AAAA,IAED,oBAAoB;AAAA,MACnB,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAASA,uDAAC,EAAC,GAAG,sBAAsB,QAAQ;AAAA,IAC5C;AAAA;AAAA,IAED,cAAc;AAAA,MACb,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,gBAAgB,EAAE;AAAA,IAChC;AAAA;AAAA,IAED,sBAAsB;AAAA,MACrB,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,wBAAwB,IAAI;AAAA,IAC1C;AAAA;AAAA,IAED,iBAAiB;AAAA,MAChB,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAASA,uDAAC,EAAC,GAAG,mBAAmB,QAAQ;AAAA,IACzC;AAAA;AAAA,IAED,gBAAgB;AAAA,MACf,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,kBAAkB,CAAA,CAAE;AAAA,IAClC;AAAA;AAAA,IAED,iBAAiB;AAAA,MAChB,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,mBAAmB,IAAI;AAAA,IACrC;AAAA,EACD;AAAA,EACD,OAAO;AACN,WAAO;AAAA;AAAA,MAEN,gBAAgB;AAAA;AAAA,MAEhB,uBAAuB;AAAA;AAAA,MAEvB,oBAAoB;AAAA,IACpB;AAAA,EACD;AAAA,EACD,UAAU;AAAA,IACT,kCAAkC;AACjC,aAAOA,uDAAAA,EAAE,QAAQ,KAAK,oBAAoB,KAAK,IAAI;AAAA,IACnD;AAAA,IACD,+BAA+B;AAC9B,aAAOA,uDAAAA,EAAE,QAAQ,KAAK,iBAAiB,KAAK,IAAI;AAAA,IAChD;AAAA,IACD,uBAAuB;AACtB,aAAO,KAAK,gBAAgB,QAAQ,KAAK;AAAA,IACzC;AAAA,IACD,0BAA0B;AACzB,aAAOA,yDAAE,YAAY,KAAK,+BAA+B;AAAA,IACzD;AAAA,IACD,sBAAsB;AACrB,YAAM,iBAAiB,KAAK;AAC5B,UAAI,CAAC,eAAe,QAAQ;AAC3B,uBAAe,SAAS,KAAK,eAAeA,uDAAAA,EAAE,YAAY,KAAK,4BAA4B,IAAI;AAAA,MAC/F;AACD,UAAG,CAAC,eAAe,UAAS;AAC3B,uBAAe,WAAW,KAAK,gBAAgB,UAAS;AAAA,MACxD;AACD,aAAO;AAAA,IACP;AAAA,IACD,sBAAsB;AACrB,aAAO,GAAG,KAAK,cAAc,mBAAmB,KAAK,IAAI;AAAA,IACzD;AAAA,EACD;AAAA,EACD,SAAS;AAAA;AAAA,IAER,kBAAkB;AACjB,UAAI,aAAa;AACjB,WAAK,MAAM,kBAAkB,WAAS;AACrC,SAAC,UAAU,UAAa,UAAU,SAAS,KAAK;AAChD,qBAAa;AAAA,MACjB,CAAI;AAED,WAAK,UAAU,MAAM;AACpB,SAAC,cAAc,KAAK;MACxB,CAAI;AAAA,IACD;AAAA;AAAA,IAED,eAAe;AACd,OAAC,KAAK,wBAAwB,KAAK,0BAA0B,CAAC;AAC9D,WAAK,YAAY,KAAK,oBAAoB;AAAA,IAC1C;AAAA;AAAA,IAED,0BAA0B,WAAW;AACpC,UAAI,CAAC,KAAK,mBAAmB;AAC5B,aAAK,qBAAqB;AAC1B;AAAA,MACA;AACD,UAAI,YAAY,KAAK,yBAAyB;AAC7C,YAAI,CAAC,KAAK,oBAAoB;AAE7B,eAAK,qBAAqB;AAC1B,eAAK,yBAAwB,oBAAI,KAAM,GAAC,QAAO;AAE/CA,iEAAC,EAAC,MAAM,MAAM;AACb,iBAAK,iBAAiB;AAAA,UACtB,GAAE,GAAG;AAAA,QACN;AAAA,MACL,OAAU;AAEN,YAAI,KAAK,oBAAoB;AAC5B,eAAK,iBAAiB;AACtBA,iEAAC,EAAC,MAAM,MAAM;AACb,iBAAK,qBAAqB;AAAA,UAChC,IAAQ,oBAAI,KAAM,GAAC,QAAS,IAAG,KAAK,wBAAwB,MAAM,IAAI,GAAG;AAAA,QACpE;AAAA,MACD;AAAA,IACD;AAAA,EACD;AACF;;"}