"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const api_index = require("../../api/index.js");
const stores_user = require("../../stores/user.js");
const _sfc_main = {
  __name: "video-progress",
  setup(__props) {
    const userStore = stores_user.useUserStore();
    const currentAvatar = common_vendor.ref("");
    const progressPercent = common_vendor.ref(0);
    const videoStatus = common_vendor.ref(0);
    const orderNo = common_vendor.ref("");
    let progressTimer = null;
    const getVideoDetail = async () => {
      try {
        const res = await api_index.getVideoDetailApi({
          merchantGuid: userStore.merchantGuid,
          orderNo: orderNo.value
        });
        if (res.code === 0) {
          const { progress, status } = res.data;
          progressPercent.value = progress || 0;
          videoStatus.value = status || 0;
          if (status === 30 || progress >= 100) {
            clearInterval(progressTimer);
            setTimeout(() => {
              common_vendor.index.showToast({
                title: "视频生成完成！",
                icon: "success",
                duration: 2e3
              });
              setTimeout(() => {
                common_vendor.index.redirectTo({
                  url: `/pages/my/video-complete?orderNo=${orderNo.value}`
                });
              }, 2e3);
            }, 500);
          }
        } else {
          common_vendor.index.__f__("error", "at pages/my/video-progress.vue:83", "获取视频详情失败:", res.msg);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/my/video-progress.vue:86", "获取视频详情失败:", error);
      }
    };
    common_vendor.onLoad((options) => {
      if (options.orderNo) {
        orderNo.value = options.orderNo;
        currentAvatar.value = options.previewUrl;
        getVideoDetail();
        progressTimer = setInterval(getVideoDetail, 3e3);
      } else {
        common_vendor.index.showToast({
          title: "参数错误",
          icon: "none"
        });
        setTimeout(() => {
          common_vendor.index.navigateBack();
        }, 2e3);
      }
    });
    common_vendor.onMounted(() => {
    });
    common_vendor.onUnmounted(() => {
      if (progressTimer) {
        clearInterval(progressTimer);
      }
    });
    return (_ctx, _cache) => {
      return {
        a: currentAvatar.value,
        b: common_assets._imports_0$6,
        c: common_vendor.t(Math.round(progressPercent.value)),
        d: progressPercent.value + "%"
      };
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-dbc65c27"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/my/video-progress.js.map
