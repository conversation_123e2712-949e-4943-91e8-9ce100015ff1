{"version": 3, "file": "utils.js", "sources": ["uni_modules/lime-painter/components/l-painter/utils.js"], "sourcesContent": ["export const networkReg = /^(http|\\/\\/)/;\r\nexport const isBase64 = (path) => /^data:image\\/(\\w+);base64/.test(path);\r\nexport function sleep(delay) {\r\n\treturn new Promise(resolve => setTimeout(resolve, delay))\r\n}\r\nlet {platform, SDKVersion} = uni.getSystemInfoSync() \r\nexport const isPC = /windows|mac/.test(platform)\r\n// 缓存图片\r\nlet cache = {}\r\nexport function isNumber(value) {\r\n\treturn /^-?\\d+(\\.\\d+)?$/.test(value);\r\n}\r\nexport function toPx(value, baseSize, isDecimal = false) {\r\n\t// 如果是数字\r\n\tif (typeof value === 'number') {\r\n\t\treturn value\r\n\t}\r\n\t// 如果是字符串数字\r\n\tif (isNumber(value)) {\r\n\t\treturn value * 1\r\n\t}\r\n\t// 如果有单位\r\n\tif (typeof value === 'string') {\r\n\t\tconst reg = /^-?([0-9]+)?([.]{1}[0-9]+){0,1}(em|rpx|px|%)$/g\r\n\t\tconst results = reg.exec(value);\r\n\t\tif (!value || !results) {\r\n\t\t\treturn 0;\r\n\t\t}\r\n\t\tconst unit = results[3];\r\n\t\tvalue = parseFloat(value);\r\n\t\tlet res = 0;\r\n\t\tif (unit === 'rpx') {\r\n\t\t\tres = uni.upx2px(value);\r\n\t\t} else if (unit === 'px') {\r\n\t\t\tres = value * 1;\r\n\t\t} else if (unit === '%') {\r\n\t\t\tres = value * toPx(baseSize) / 100;\r\n\t\t} else if (unit === 'em') {\r\n\t\t\tres = value * toPx(baseSize || 14);\r\n\t\t}\r\n\t\treturn isDecimal ? res.toFixed(2) * 1 : Math.round(res);\r\n\t}\r\n\treturn 0\r\n}\r\n\r\n// 计算版本\r\nexport function compareVersion(v1, v2) {\r\n\tv1 = v1.split('.')\r\n\tv2 = v2.split('.')\r\n\tconst len = Math.max(v1.length, v2.length)\r\n\twhile (v1.length < len) {\r\n\t\tv1.push('0')\r\n\t}\r\n\twhile (v2.length < len) {\r\n\t\tv2.push('0')\r\n\t}\r\n\tfor (let i = 0; i < len; i++) {\r\n\t\tconst num1 = parseInt(v1[i], 10)\r\n\t\tconst num2 = parseInt(v2[i], 10)\r\n\r\n\t\tif (num1 > num2) {\r\n\t\t\treturn 1\r\n\t\t} else if (num1 < num2) {\r\n\t\t\treturn -1\r\n\t\t}\r\n\t}\r\n\treturn 0\r\n}\r\n\r\nfunction gte(version) {\r\n  // #ifdef MP-ALIPAY\r\n  SDKVersion = my.SDKVersion\r\n  // #endif\r\n  return compareVersion(SDKVersion, version) >= 0;\r\n}\r\nexport function canIUseCanvas2d() {\r\n\t// #ifdef MP-WEIXIN\r\n\treturn gte('2.9.2');\r\n\t// #endif\r\n\t// #ifdef MP-ALIPAY\r\n\treturn gte('2.7.15');\r\n\t// #endif\r\n\t// #ifdef MP-TOUTIAO\r\n\treturn gte('1.78.0');\r\n\t// #endif\r\n\treturn false\r\n}\r\n\r\n// #ifdef MP\r\nexport const prefix = () => {\r\n\t// #ifdef MP-TOUTIAO\r\n\treturn tt\r\n\t// #endif\r\n\t// #ifdef MP-WEIXIN\r\n\treturn wx\r\n\t// #endif\r\n\t// #ifdef MP-BAIDU\r\n\treturn swan\r\n\t// #endif\r\n\t// #ifdef MP-ALIPAY\r\n\treturn my\r\n\t// #endif\r\n\t// #ifdef MP-QQ\r\n\treturn qq\r\n\t// #endif\r\n\t// #ifdef MP-360\r\n\treturn qh\r\n\t// #endif\r\n}\r\n// #endif\r\n\r\n\r\n\r\n/**\r\n * base64转路径\r\n * @param {Object} base64\r\n */\r\nexport function base64ToPath(base64) {\r\n\tconst [, format] = /^data:image\\/(\\w+);base64,/.exec(base64) || [];\r\n\r\n\treturn new Promise((resolve, reject) => {\r\n\t\t// #ifdef MP\r\n\t\tconst fs = uni.getFileSystemManager()\r\n\t\t//自定义文件名\r\n\t\tif (!format) {\r\n\t\t\treject(new Error('ERROR_BASE64SRC_PARSE'))\r\n\t\t}\r\n\t\tconst time = new Date().getTime();\r\n\t\tlet pre = prefix()\r\n\t\t// #ifdef MP-TOUTIAO\r\n\t\tconst filePath = `${pre.getEnvInfoSync().common.USER_DATA_PATH}/${time}.${format}`\r\n\t\t// #endif\r\n\t\t// #ifndef MP-TOUTIAO\r\n\t\tconst filePath = `${pre.env.USER_DATA_PATH}/${time}.${format}`\r\n\t\t// #endif\r\n\t\tfs.writeFile({\r\n\t\t\tfilePath,\r\n\t\t\tdata: base64.split(',')[1],\r\n\t\t\tencoding: 'base64',\r\n\t\t\tsuccess() {\r\n\t\t\t\tresolve(filePath)\r\n\t\t\t},\r\n\t\t\tfail(err) {\r\n\t\t\t\tconsole.error(err)\r\n\t\t\t\treject(err)\r\n\t\t\t}\r\n\t\t})\r\n\t\t// #endif\r\n\r\n\t\t// #ifdef H5\r\n\t\t// mime类型\r\n\t\tlet mimeString = base64.split(',')[0].split(':')[1].split(';')[0];\r\n\t\t//base64 解码\r\n\t\tlet byteString = atob(base64.split(',')[1]);\r\n\t\t//创建缓冲数组\r\n\t\tlet arrayBuffer = new ArrayBuffer(byteString.length);\r\n\t\t//创建视图\r\n\t\tlet intArray = new Uint8Array(arrayBuffer);\r\n\t\tfor (let i = 0; i < byteString.length; i++) {\r\n\t\t\tintArray[i] = byteString.charCodeAt(i);\r\n\t\t}\r\n\t\tresolve(URL.createObjectURL(new Blob([intArray], {\r\n\t\t\ttype: mimeString\r\n\t\t})))\r\n\t\t// #endif\r\n\r\n\t\t// #ifdef APP-PLUS\r\n\t\tconst bitmap = new plus.nativeObj.Bitmap('bitmap' + Date.now())\r\n\t\tbitmap.loadBase64Data(base64, () => {\r\n\t\t\tif (!format) {\r\n\t\t\t\treject(new Error('ERROR_BASE64SRC_PARSE'))\r\n\t\t\t}\r\n\t\t\tconst time = new Date().getTime();\r\n\t\t\tconst filePath = `_doc/uniapp_temp/${time}.${format}`\r\n\t\t\tbitmap.save(filePath, {},\r\n\t\t\t\t() => {\r\n\t\t\t\t\tbitmap.clear()\r\n\t\t\t\t\tresolve(filePath)\r\n\t\t\t\t},\r\n\t\t\t\t(error) => {\r\n\t\t\t\t\tbitmap.clear()\r\n\t\t\t\t\treject(error)\r\n\t\t\t\t})\r\n\t\t}, (error) => {\r\n\t\t\tbitmap.clear()\r\n\t\t\treject(error)\r\n\t\t})\r\n\t\t// #endif\r\n\t})\r\n}\r\n\r\n/**\r\n * 路径转base64\r\n * @param {Object} string\r\n */\r\nexport function pathToBase64(path) {\r\n\tif (/^data:/.test(path)) return path\r\n\treturn new Promise((resolve, reject) => {\r\n\t\t// #ifdef H5\r\n\t\tlet image = new Image();\r\n\t\timage.setAttribute(\"crossOrigin\", 'Anonymous');\r\n\t\timage.onload = function() {\r\n\t\t\tlet canvas = document.createElement('canvas');\r\n\t\t\tcanvas.width = this.naturalWidth;\r\n\t\t\tcanvas.height = this.naturalHeight;\r\n\t\t\tcanvas.getContext('2d').drawImage(image, 0, 0);\r\n\t\t\tlet result = canvas.toDataURL('image/png')\r\n\t\t\tresolve(result);\r\n\t\t\tcanvas.height = canvas.width = 0\r\n\t\t}\r\n\t\timage.src = path + '?v=' + Math.random()\r\n\t\timage.onerror = (error) => {\r\n\t\t\treject(error);\r\n\t\t};\r\n\t\t// #endif\r\n\r\n\t\t// #ifdef MP\r\n\t\tif (uni.canIUse('getFileSystemManager')) {\r\n\t\t\tuni.getFileSystemManager().readFile({\r\n\t\t\t\tfilePath: path,\r\n\t\t\t\tencoding: 'base64',\r\n\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\tresolve('data:image/png;base64,' + res.data)\r\n\t\t\t\t},\r\n\t\t\t\tfail: (error) => {\r\n\t\t\t\t\tconsole.error({error, path})\r\n\t\t\t\t\treject(error)\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t}\r\n\t\t// #endif\r\n\r\n\t\t// #ifdef APP-PLUS\r\n\t\tplus.io.resolveLocalFileSystemURL(getLocalFilePath(path), (entry) => {\r\n\t\t\tentry.file((file) => {\r\n\t\t\t\tconst fileReader = new plus.io.FileReader()\r\n\t\t\t\tfileReader.onload = (data) => {\r\n\t\t\t\t\tresolve(data.target.result)\r\n\t\t\t\t}\r\n\t\t\t\tfileReader.onerror = (error) => {\r\n\t\t\t\t\treject(error)\r\n\t\t\t\t}\r\n\t\t\t\tfileReader.readAsDataURL(file)\r\n\t\t\t}, reject)\r\n\t\t}, reject)\r\n\t\t// #endif\r\n\t})\r\n}\r\n\r\n\r\n\r\nexport function getImageInfo(path, useCORS) {\n\tconst isCanvas2D = this && this.canvas && this.canvas.createImage\r\n\treturn new Promise(async (resolve, reject) => {\r\n\t\t// let time = +new Date()\r\n\t\tlet src = path.replace(/^@\\//,'/')\r\n\t\tif (cache[path] && cache[path].errMsg) {\r\n\t\t\tresolve(cache[path])\r\n\t\t} else {\r\n\t\t\ttry {\r\n\t\t\t\t// #ifdef MP || APP-PLUS\r\n\t\t\t\tif (isBase64(path) && (isCanvas2D ? isPC : true)) {\r\n\t\t\t\t\tsrc = await base64ToPath(path)\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\tif(useCORS) {\r\n\t\t\t\t\tsrc = await pathToBase64(path)\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t} catch (error) {\r\n\t\t\t\treject({\r\n\t\t\t\t\t...error,\r\n\t\t\t\t\tsrc\r\n\t\t\t\t})\r\n\t\t\t}\n\t\t\t// #ifndef APP-NVUE\r\n\t\t\tif(isCanvas2D && !isPC) {\r\n\t\t\t\tconst img = this.canvas.createImage()\r\n\t\t\t\timg.onload = function() {\r\n\t\t\t\t\tconst image = {\r\n\t\t\t\t\t\tpath: img,\r\n\t\t\t\t\t\twidth:  img.width,\r\n\t\t\t\t\t\theight:  img.height\r\n\t\t\t\t\t}\r\n\t\t\t\t\tcache[path] = image\r\n\t\t\t\t\tresolve(cache[path])\r\n\t\t\t\t}\r\n\t\t\t\timg.onerror = function(err) {\r\n\t\t\t\t\treject({err,path})\r\n\t\t\t\t}\r\n\t\t\t\timg.src = src\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\t\t\t// #endif\r\n\t\t\tuni.getImageInfo({\r\n\t\t\t\tsrc,\r\n\t\t\t\tsuccess: (image) => {\r\n\t\t\t\t\tconst localReg = /^\\.|^\\/(?=[^\\/])/;\r\n\t\t\t\t\t// #ifdef MP-WEIXIN || MP-BAIDU || MP-QQ || MP-TOUTIAO\r\n\t\t\t\t\timage.path = localReg.test(src) ?  `/${image.path}` : image.path;\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\tif(isCanvas2D) {\r\n\t\t\t\t\t\tconst img = this.canvas.createImage()\r\n\t\t\t\t\t\timg.onload = function() {\r\n\t\t\t\t\t\t\timage.path = img\r\n\t\t\t\t\t\t\tcache[path] = image\r\n\t\t\t\t\t\t\tresolve(cache[path])\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\timg.onerror = function(err) {\r\n\t\t\t\t\t\t\treject({err,path})\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\timg.src = src\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\t\t// console.log('getImageInfo', +new Date() - time)\r\n\t\t\t\t\t// ios 比较严格 可能需要设置跨域\r\n\t\t\t\t\tif(uni.getSystemInfoSync().osName == 'ios' && useCORS) {\r\n\t\t\t\t\t\tpathToBase64(image.path).then(base64 => {\r\n\t\t\t\t\t\t\timage.path = base64\r\n\t\t\t\t\t\t\tcache[path] = image\r\n\t\t\t\t\t\t\tresolve(cache[path])\r\n\t\t\t\t\t\t}).catch(err => {\r\n\t\t\t\t\t\t\tconsole.error({err, path})\r\n\t\t\t\t\t\t\treject({err,path})\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\tcache[path] = image\r\n\t\t\t\t\tresolve(cache[path])\r\n\t\t\t\t},\r\n\t\t\t\tfail(err) {\r\n\t\t\t\t\tconsole.error({err, path})\r\n\t\t\t\t\treject({err,path})\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t}\r\n\t})\r\n}\r\n\r\n\r\n// #ifdef APP-PLUS\r\nconst getLocalFilePath = (path) => {\r\n\tif (path.indexOf('_www') === 0 || path.indexOf('_doc') === 0 || path.indexOf('_documents') === 0 || path\r\n\t\t.indexOf('_downloads') === 0) {\r\n\t\treturn path\r\n\t}\r\n\tif (path.indexOf('file://') === 0) {\r\n\t\treturn path\r\n\t}\r\n\tif (path.indexOf('/storage/emulated/0/') === 0) {\r\n\t\treturn path\r\n\t}\r\n\tif (path.indexOf('/') === 0) {\r\n\t\tconst localFilePath = plus.io.convertAbsoluteFileSystem(path)\r\n\t\tif (localFilePath !== path) {\r\n\t\t\treturn localFilePath\r\n\t\t} else {\r\n\t\t\tpath = path.substr(1)\r\n\t\t}\r\n\t}\r\n\treturn '_www/' + path\r\n}\r\n// #endif\r\n\r\n\r\n"], "names": ["uni", "wx"], "mappings": ";;AACY,MAAC,WAAW,CAAC,SAAS,4BAA4B,KAAK,IAAI;AAChE,SAAS,MAAM,OAAO;AAC5B,SAAO,IAAI,QAAQ,aAAW,WAAW,SAAS,KAAK,CAAC;AACzD;AACA,IAAI,EAAC,UAAU,WAAU,IAAIA,cAAAA,MAAI,kBAAmB;AACxC,MAAC,OAAO,cAAc,KAAK,QAAQ;AAE/C,IAAI,QAAQ,CAAE;AACP,SAAS,SAAS,OAAO;AAC/B,SAAO,kBAAkB,KAAK,KAAK;AACpC;AACO,SAAS,KAAK,OAAO,UAAU,YAAY,OAAO;AAExD,MAAI,OAAO,UAAU,UAAU;AAC9B,WAAO;AAAA,EACP;AAED,MAAI,SAAS,KAAK,GAAG;AACpB,WAAO,QAAQ;AAAA,EACf;AAED,MAAI,OAAO,UAAU,UAAU;AAC9B,UAAM,MAAM;AACZ,UAAM,UAAU,IAAI,KAAK,KAAK;AAC9B,QAAI,CAAC,SAAS,CAAC,SAAS;AACvB,aAAO;AAAA,IACP;AACD,UAAM,OAAO,QAAQ,CAAC;AACtB,YAAQ,WAAW,KAAK;AACxB,QAAI,MAAM;AACV,QAAI,SAAS,OAAO;AACnB,YAAMA,cAAG,MAAC,OAAO,KAAK;AAAA,IACzB,WAAa,SAAS,MAAM;AACzB,YAAM,QAAQ;AAAA,IACjB,WAAa,SAAS,KAAK;AACxB,YAAM,QAAQ,KAAK,QAAQ,IAAI;AAAA,IAClC,WAAa,SAAS,MAAM;AACzB,YAAM,QAAQ,KAAK,YAAY,EAAE;AAAA,IACjC;AACD,WAAO,YAAY,IAAI,QAAQ,CAAC,IAAI,IAAI,KAAK,MAAM,GAAG;AAAA,EACtD;AACD,SAAO;AACR;AAGO,SAAS,eAAe,IAAI,IAAI;AACtC,OAAK,GAAG,MAAM,GAAG;AACjB,OAAK,GAAG,MAAM,GAAG;AACjB,QAAM,MAAM,KAAK,IAAI,GAAG,QAAQ,GAAG,MAAM;AACzC,SAAO,GAAG,SAAS,KAAK;AACvB,OAAG,KAAK,GAAG;AAAA,EACX;AACD,SAAO,GAAG,SAAS,KAAK;AACvB,OAAG,KAAK,GAAG;AAAA,EACX;AACD,WAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC7B,UAAM,OAAO,SAAS,GAAG,CAAC,GAAG,EAAE;AAC/B,UAAM,OAAO,SAAS,GAAG,CAAC,GAAG,EAAE;AAE/B,QAAI,OAAO,MAAM;AAChB,aAAO;AAAA,IACV,WAAa,OAAO,MAAM;AACvB,aAAO;AAAA,IACP;AAAA,EACD;AACD,SAAO;AACR;AAEA,SAAS,IAAI,SAAS;AAIpB,SAAO,eAAe,YAAY,OAAO,KAAK;AAChD;AACO,SAAS,kBAAkB;AAEjC,SAAO,IAAI,OAAO;AASnB;AAGO,MAAM,SAAS,MAAM;AAK3B,SAAOC,cAAE;AAcV;AASO,SAAS,aAAa,QAAQ;AACpC,QAAM,CAAA,EAAG,MAAM,IAAI,6BAA6B,KAAK,MAAM,KAAK;AAEhE,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AAEvC,UAAM,KAAKD,cAAG,MAAC,qBAAsB;AAErC,QAAI,CAAC,QAAQ;AACZ,aAAO,IAAI,MAAM,uBAAuB,CAAC;AAAA,IACzC;AACD,UAAM,QAAO,oBAAI,KAAM,GAAC,QAAO;AAC/B,QAAI,MAAM,OAAQ;AAKlB,UAAM,WAAW,GAAG,IAAI,IAAI,cAAc,IAAI,IAAI,IAAI,MAAM;AAE5D,OAAG,UAAU;AAAA,MACZ;AAAA,MACA,MAAM,OAAO,MAAM,GAAG,EAAE,CAAC;AAAA,MACzB,UAAU;AAAA,MACV,UAAU;AACT,gBAAQ,QAAQ;AAAA,MAChB;AAAA,MACD,KAAK,KAAK;AACTA,sBAAAA,MAAA,MAAA,SAAA,iEAAc,GAAG;AACjB,eAAO,GAAG;AAAA,MACV;AAAA,IACJ,CAAG;AAAA,EA0CH,CAAE;AACF;AAMO,SAAS,aAAa,MAAM;AAClC,MAAI,SAAS,KAAK,IAAI;AAAG,WAAO;AAChC,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AAoBvC,QAAIA,cAAG,MAAC,QAAQ,sBAAsB,GAAG;AACxCA,0BAAI,qBAAsB,EAAC,SAAS;AAAA,QACnC,UAAU;AAAA,QACV,UAAU;AAAA,QACV,SAAS,CAAC,QAAQ;AACjB,kBAAQ,2BAA2B,IAAI,IAAI;AAAA,QAC3C;AAAA,QACD,MAAM,CAAC,UAAU;AAChBA,wBAAc,MAAA,MAAA,SAAA,iEAAA,EAAC,OAAO,KAAI,CAAC;AAC3B,iBAAO,KAAK;AAAA,QACZ;AAAA,MACL,CAAI;AAAA,IACD;AAAA,EAiBH,CAAE;AACF;AAIO,SAAS,aAAa,MAAM,SAAS;AAC3C,QAAM,aAAa,QAAQ,KAAK,UAAU,KAAK,OAAO;AACtD,SAAO,IAAI,QAAQ,OAAO,SAAS,WAAW;AAE7C,QAAI,MAAM,KAAK,QAAQ,QAAO,GAAG;AACjC,QAAI,MAAM,IAAI,KAAK,MAAM,IAAI,EAAE,QAAQ;AACtC,cAAQ,MAAM,IAAI,CAAC;AAAA,IACtB,OAAS;AACN,UAAI;AAEH,YAAI,SAAS,IAAI,MAAM,aAAa,OAAO,OAAO;AACjD,gBAAM,MAAM,aAAa,IAAI;AAAA,QAC7B;AAAA,MAOD,SAAQ,OAAO;AACf,eAAO;AAAA,UACN,GAAG;AAAA,UACH;AAAA,QACL,CAAK;AAAA,MACD;AAED,UAAG,cAAc,CAAC,MAAM;AACvB,cAAM,MAAM,KAAK,OAAO,YAAa;AACrC,YAAI,SAAS,WAAW;AACvB,gBAAM,QAAQ;AAAA,YACb,MAAM;AAAA,YACN,OAAQ,IAAI;AAAA,YACZ,QAAS,IAAI;AAAA,UACb;AACD,gBAAM,IAAI,IAAI;AACd,kBAAQ,MAAM,IAAI,CAAC;AAAA,QACnB;AACD,YAAI,UAAU,SAAS,KAAK;AAC3B,iBAAO,EAAC,KAAI,KAAI,CAAC;AAAA,QACjB;AACD,YAAI,MAAM;AACV;AAAA,MACA;AAEDA,oBAAAA,MAAI,aAAa;AAAA,QAChB;AAAA,QACA,SAAS,CAAC,UAAU;AACnB,gBAAM,WAAW;AAEjB,gBAAM,OAAO,SAAS,KAAK,GAAG,IAAK,IAAI,MAAM,IAAI,KAAK,MAAM;AAE5D,cAAG,YAAY;AACd,kBAAM,MAAM,KAAK,OAAO,YAAa;AACrC,gBAAI,SAAS,WAAW;AACvB,oBAAM,OAAO;AACb,oBAAM,IAAI,IAAI;AACd,sBAAQ,MAAM,IAAI,CAAC;AAAA,YACnB;AACD,gBAAI,UAAU,SAAS,KAAK;AAC3B,qBAAO,EAAC,KAAI,KAAI,CAAC;AAAA,YACjB;AACD,gBAAI,MAAM;AACV;AAAA,UACA;AAgBD,gBAAM,IAAI,IAAI;AACd,kBAAQ,MAAM,IAAI,CAAC;AAAA,QACnB;AAAA,QACD,KAAK,KAAK;AACTA,wBAAA,MAAA,MAAA,SAAA,iEAAc,EAAC,KAAK,KAAI,CAAC;AACzB,iBAAO,EAAC,KAAI,KAAI,CAAC;AAAA,QACjB;AAAA,MACL,CAAI;AAAA,IACD;AAAA,EACH,CAAE;AACF;;;;;;;;;"}