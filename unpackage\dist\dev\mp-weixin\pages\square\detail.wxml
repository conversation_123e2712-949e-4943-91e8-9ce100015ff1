<view class="agent-detail-page data-v-efb7de88"><view wx:if="{{a}}" class="agent-info data-v-efb7de88"><view class="avatar-section data-v-efb7de88"><image class="agent-avatar data-v-efb7de88" src="{{b}}" mode="aspectFill"/><view class="nick-name data-v-efb7de88">@{{c}}</view></view><view class="info-section data-v-efb7de88"><text class="agent-name data-v-efb7de88">{{d}}</text><text class="agent-desc data-v-efb7de88">{{e}}</text></view><view class="action-buttons data-v-efb7de88"><view wx:if="{{f}}" class="subscribe-btn data-v-efb7de88" bindtap="{{g}}"><text class="btn-text data-v-efb7de88">立即招募</text></view><view wx:if="{{h}}" class="chat-btn data-v-efb7de88" bindtap="{{i}}"><text class="btn-text data-v-efb7de88">去聊天</text></view><view class="share-btn data-v-efb7de88" bindtap="{{j}}"><text class="btn-text data-v-efb7de88">生成分享海报</text></view></view></view><view wx:elif="{{k}}" class="loading-state data-v-efb7de88"><text class="loading-text data-v-efb7de88">加载中...</text></view><view wx:else class="empty-state data-v-efb7de88"><text class="empty-text data-v-efb7de88">智能体信息加载失败</text></view></view>