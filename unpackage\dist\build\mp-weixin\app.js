"use strict";Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const e=require("./common/vendor.js"),i=require("./api/index.js"),t=require("./stores/user.js");Math;const n={globalData:{recorderManager:e.wx$1.getRecorderManager(),scene:0},onLaunch:function(n){e.index.getProvider({service:"oauth",success:a=>{try{const o=t.useUserStore();~a.provider.indexOf("weixin")&&e.index.login({provider:"weixin",success:async t=>{let a=t.code;e.index.showLoading({title:"登录中",mask:!0});try{i.mpLoginApi({jsCode:a,merchantGuid:o.merchantGuid,wxappid:o.wxappid}).then((async t=>{if(o.set_user_token(t.data.token),o.set_user_info(t.data.userInfo),e.index.hideLoading(),!o.invitationCode){let e=await i.getInvitationCodeApi({merchantGuid:o.merchantGuid});o.set_invitation_code(e.data.inviteCode)}if(n.query.invite)try{await i.bindInvitationApi({merchantGuid:o.merchantGuid,invitationCode:n.query.invite})}catch(a){}i.updateUserDefaultProfileApi()}))}catch(r){e.index.hideLoading()}}})}catch(o){}}})},onShow:async function(e){if(e.query.invite){const a=t.useUserStore();if(a.userToken)try{await i.bindInvitationApi({merchantGuid:a.merchantGuid,invitationCode:e.query.invite})}catch(n){}}},onHide:function(){console.log("App Hide")}};function a(){const i=e.createSSRApp(n);return i.use(e.createPinia()),{app:i,Pinia:e.Pinia}}a().app.mount("#app"),exports.createApp=a;
