{"version": 3, "file": "props.js", "sources": ["uni_modules/lime-painter/components/l-painter/props.js"], "sourcesContent": ["export default {\r\n\tprops: {\r\n\t\tboard: Object,\r\n\t\tpathType: String, // 'base64'、'url'\r\n\t\tfileType: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: 'png'\r\n\t\t},\r\n\t\thidden: Boolean,\r\n\t\tquality: {\r\n\t\t\ttype: Number,\r\n\t\t\tdefault: 1\r\n\t\t},\r\n\t\tcss: [String, Object],\r\n\t\t// styles: [String, Object],\r\n\t\twidth: [Number, String],\r\n\t\theight: [Number, String],\r\n\t\tpixelRatio: Number,\r\n\t\tcustomStyle: String,\r\n\t\tisCanvasToTempFilePath: Boolean,\r\n\t\t// useCanvasToTempFilePath: Boolean,\r\n\t\tsleep: {\r\n\t\t\ttype: Number,\r\n\t\t\tdefault: 1000 / 30\r\n\t\t},\r\n\t\tbeforeDelay: {\r\n\t\t\ttype: Number,\r\n\t\t\tdefault: 100\r\n\t\t},\r\n\t\tafterDelay: {\r\n\t\t\ttype: Number,\r\n\t\t\tdefault: 100\r\n\t\t},\r\n\t\tperformance: Boolean,\r\n\t\t// #ifdef MP-WEIXIN || MP-TOUTIAO || MP-ALIPAY\r\n\t\ttype: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: '2d'\r\n\t\t},\r\n\t\t// #endif\r\n\t\t// #ifdef APP-NVUE\r\n\t\thybrid: Boolean,\r\n\t\ttimeout: {\r\n\t\t\ttype: Number,\r\n\t\t\tdefault: 2000\r\n\t\t},\r\n\t\t// #endif\r\n\t\t// #ifdef H5 || APP-PLUS\r\n\t\tuseCORS: Boolean,\r\n\t\thidpi: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: true\r\n\t\t}\r\n\t\t// #endif\r\n\t}\r\n}"], "names": [], "mappings": ";AAAA,MAAe,QAAA;AAAA,EACd,OAAO;AAAA,IACN,OAAO;AAAA,IACP,UAAU;AAAA;AAAA,IACV,UAAU;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA,IACD,QAAQ;AAAA,IACR,SAAS;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA,IACD,KAAK,CAAC,QAAQ,MAAM;AAAA;AAAA,IAEpB,OAAO,CAAC,QAAQ,MAAM;AAAA,IACtB,QAAQ,CAAC,QAAQ,MAAM;AAAA,IACvB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,wBAAwB;AAAA;AAAA,IAExB,OAAO;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAO;AAAA,IAChB;AAAA,IACD,aAAa;AAAA,MACZ,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA,IACD,YAAY;AAAA,MACX,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA,IACD,aAAa;AAAA,IAEb,MAAM;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA,EAgBD;AACF;;"}