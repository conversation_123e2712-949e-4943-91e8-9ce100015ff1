/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.sub-page.data-v-287e3f54 {
  background: #F8F9FA;
  padding: 32rpx 32rpx 0;
  min-height: 100vh;
}
.sub-intro.data-v-287e3f54 {
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
}
.intro-title.data-v-287e3f54 {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 32rpx;
  display: block;
}
.intro-item.data-v-287e3f54 {
  display: flex;
  margin-bottom: 32rpx;
}
.intro-item.data-v-287e3f54:last-child {
  margin-bottom: 0;
}
.icon-box.data-v-287e3f54 {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  margin-right: 24rpx;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}
.icon-box .icon.data-v-287e3f54 {
  width: 60rpx;
  height: 60rpx;
  display: block;
}
.intro-content.data-v-287e3f54 {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.intro-label.data-v-287e3f54 {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
}
.intro-desc.data-v-287e3f54 {
  font-size: 24rpx;
  color: #666666;
  line-height: 1.5;
}
.user-card.data-v-287e3f54 {
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  display: flex;
  align-items: center;
}
.user-avatar.data-v-287e3f54 {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 24rpx;
  flex-shrink: 0;
}
.avatar-img.data-v-287e3f54 {
  width: 100%;
  height: 100%;
}
.user-info.data-v-287e3f54 {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.user-name.data-v-287e3f54 {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
}
.user-points.data-v-287e3f54 {
  font-size: 24rpx;
  color: #1E90FF;
}
.buy-points-btn.data-v-287e3f54 {
  background: #1E90FF;
  border-radius: 40rpx;
  padding: 16rpx 32rpx;
  font-size: 24rpx;
  color: #FFFFFF;
  line-height: 1;
}
.subscription-section.data-v-287e3f54 {
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
}
.section-header.data-v-287e3f54 {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.section-title.data-v-287e3f54 {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}
.subscribe-btn.data-v-287e3f54 {
  background: #333333;
  border-radius: 40rpx;
  padding: 16rpx 32rpx;
  font-size: 24rpx;
  color: #FFFFFF;
  line-height: 1;
}
.modal-overlay.data-v-287e3f54 {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
}
.modal-content.data-v-287e3f54 {
  width: 90%;
  height: 600rpx;
  background: #FFFFFF;
  border-radius: 20rpx;
  position: relative;
  display: flex;
  flex-direction: column;
}
.modal-close.data-v-287e3f54 {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}
.close-icon.data-v-287e3f54 {
  font-size: 40rpx;
  color: #999999;
  font-weight: 300;
}
.modal-header.data-v-287e3f54 {
  padding: 40rpx 32rpx 20rpx;
  text-align: center;
  border-bottom: 1rpx solid #F0F0F0;
}
.modal-title.data-v-287e3f54 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}
.creator-list.data-v-287e3f54 {
  flex: 1;
  padding: 20rpx 0;
}
.creator-item.data-v-287e3f54 {
  display: flex;
  padding: 32rpx;
  border-bottom: 1rpx solid #F8F9FA;
}
.creator-item.data-v-287e3f54:last-child {
  border-bottom: none;
}
.creator-avatar.data-v-287e3f54 {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 24rpx;
  flex-shrink: 0;
}
.avatar-img.data-v-287e3f54 {
  width: 100%;
  height: 100%;
}
.creator-info.data-v-287e3f54 {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.creator-name.data-v-287e3f54 {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
}
.creator-desc.data-v-287e3f54 {
  font-size: 24rpx;
  color: #666666;
  line-height: 1.4;
  margin-bottom: 16rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.creator-stats.data-v-287e3f54 {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  flex-wrap: wrap;
}
.stats-text.data-v-287e3f54 {
  font-size: 24rpx;
  font-weight: 600;
  color: #333333;
  margin-right: 8rpx;
}
.stats-label.data-v-287e3f54 {
  font-size: 20rpx;
  color: #999999;
  margin-right: 32rpx;
}
.stats-price.data-v-287e3f54 {
  font-size: 28rpx;
  font-weight: 600;
  color: #1E90FF;
  margin-right: 8rpx;
}
.creator-actions.data-v-287e3f54 {
  display: flex;
  gap: 16rpx;
}
.action-btn.data-v-287e3f54 {
  padding: 12rpx 24rpx;
  border-radius: 40rpx;
  font-size: 24rpx;
  text-align: center;
  line-height: 1;
}
.detail-btn.data-v-287e3f54 {
  background: #F8F9FA;
  color: #666666;
  border: 1rpx solid #E5E5E5;
}
.subscribe-btn.data-v-287e3f54 {
  background: #1E90FF;
  color: #FFFFFF;
}
.subscribe-btn.subscribed.data-v-287e3f54 {
  background: #E5E5E5;
  color: #999999;
}
.subscribe-btn.ios-disabled.data-v-287e3f54 {
  background: #999999;
  color: #FFFFFF;
}
.btn-text.data-v-287e3f54 {
  font-size: 24rpx;
}