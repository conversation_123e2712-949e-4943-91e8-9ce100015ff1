{"version": 3, "file": "load-more.js", "sources": ["uni_modules/z-paging/components/z-paging/js/modules/load-more.js"], "sourcesContent": ["// [z-paging]滚动到底部加载更多模块\r\nimport u from '.././z-paging-utils'\r\nimport Enum from '.././z-paging-enum'\r\n\r\nexport default {\r\n\tprops: {\r\n\t\t// 自定义底部加载更多样式\r\n\t\tloadingMoreCustomStyle: {\r\n\t\t\ttype: Object,\r\n\t\t\tdefault: u.gc('loadingMoreCustomStyle', {})\r\n\t\t},\r\n\t\t// 自定义底部加载更多文字样式\r\n\t\tloadingMoreTitleCustomStyle: {\r\n\t\t\ttype: Object,\r\n\t\t\tdefault: u.gc('loadingMoreTitleCustomStyle', {})\r\n\t\t},\r\n\t\t// 自定义底部加载更多加载中动画样式\r\n\t\tloadingMoreLoadingIconCustomStyle: {\r\n\t\t\ttype: Object,\r\n\t\t\tdefault: u.gc('loadingMoreLoadingIconCustomStyle', {})\r\n\t\t},\r\n\t\t// 自定义底部加载更多加载中动画图标类型，可选flower或circle，默认为flower\r\n\t\tloadingMoreLoadingIconType: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: u.gc('loadingMoreLoadingIconType', 'flower')\r\n\t\t},\r\n\t\t// 自定义底部加载更多加载中动画图标图片\r\n\t\tloadingMoreLoadingIconCustomImage: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: u.gc('loadingMoreLoadingIconCustomImage', '')\r\n\t\t},\r\n\t\t// 底部加载更多加载中view是否展示旋转动画，默认为是\r\n\t\tloadingMoreLoadingAnimated: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('loadingMoreLoadingAnimated', true)\r\n\t\t},\r\n\t\t// 是否启用加载更多数据(含滑动到底部加载更多数据和点击加载更多数据)，默认为是\r\n\t\tloadingMoreEnabled: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('loadingMoreEnabled', true)\r\n\t\t},\r\n\t\t// 是否启用滑动到底部加载更多数据，默认为是\r\n\t\ttoBottomLoadingMoreEnabled: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('toBottomLoadingMoreEnabled', true)\r\n\t\t},\r\n\t\t// 滑动到底部状态为默认状态时，以加载中的状态展示，默认为否。若设置为是，可避免滚动到底部看到默认状态然后立刻变为加载中状态的问题，但分页数量未超过一屏时，不会显示【点击加载更多】\r\n\t\tloadingMoreDefaultAsLoading: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('loadingMoreDefaultAsLoading', false)\r\n\t\t},\r\n\t\t// 滑动到底部\"默认\"文字，默认为【点击加载更多】\r\n\t\tloadingMoreDefaultText: {\r\n\t\t\ttype: [String, Object],\r\n\t\t\tdefault: u.gc('loadingMoreDefaultText', null)\r\n\t\t},\r\n\t\t// 滑动到底部\"加载中\"文字，默认为【正在加载...】\r\n\t\tloadingMoreLoadingText: {\r\n\t\t\ttype: [String, Object],\r\n\t\t\tdefault: u.gc('loadingMoreLoadingText', null)\r\n\t\t},\r\n\t\t// 滑动到底部\"没有更多\"文字，默认为【没有更多了】\r\n\t\tloadingMoreNoMoreText: {\r\n\t\t\ttype: [String, Object],\r\n\t\t\tdefault: u.gc('loadingMoreNoMoreText', null)\r\n\t\t},\r\n\t\t// 滑动到底部\"加载失败\"文字，默认为【加载失败，点击重新加载】\r\n\t\tloadingMoreFailText: {\r\n\t\t\ttype: [String, Object],\r\n\t\t\tdefault: u.gc('loadingMoreFailText', null)\r\n\t\t},\r\n\t\t// 当没有更多数据且分页内容未超出z-paging时是否隐藏没有更多数据的view，默认为否\r\n\t\thideNoMoreInside: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('hideNoMoreInside', false)\r\n\t\t},\r\n\t\t// 当没有更多数据且分页数组长度少于这个值时，隐藏没有更多数据的view，默认为0，代表不限制。\r\n\t\thideNoMoreByLimit: {\r\n\t\t\ttype: Number,\r\n\t\t\tdefault: u.gc('hideNoMoreByLimit', 0)\r\n\t\t},\r\n\t\t// 是否显示默认的加载更多text，默认为是\r\n\t\tshowDefaultLoadingMoreText: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('showDefaultLoadingMoreText', true)\r\n\t\t},\r\n\t\t// 是否显示没有更多数据的view\r\n\t\tshowLoadingMoreNoMoreView: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('showLoadingMoreNoMoreView', true)\r\n\t\t},\r\n\t\t// 是否显示没有更多数据的分割线，默认为是\r\n\t\tshowLoadingMoreNoMoreLine: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('showLoadingMoreNoMoreLine', true)\r\n\t\t},\r\n\t\t// 自定义底部没有更多数据的分割线样式\r\n\t\tloadingMoreNoMoreLineCustomStyle: {\r\n\t\t\ttype: Object,\r\n\t\t\tdefault: u.gc('loadingMoreNoMoreLineCustomStyle', {})\r\n\t\t},\r\n\t\t// 当分页未满一屏时，是否自动加载更多，默认为否(nvue无效)\r\n\t\tinsideMore: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: u.gc('insideMore', false)\r\n\t\t},\r\n\t\t// 距底部/右边多远时（单位px），触发 scrolltolower 事件，默认为100rpx\r\n\t\tlowerThreshold: {\r\n\t\t\ttype: [Number, String],\r\n\t\t\tdefault: u.gc('lowerThreshold', '100rpx')\r\n\t\t},\r\n\t},\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tM: Enum.More,\r\n\t\t\t// 底部加载更多状态\r\n\t\t\tloadingStatus: Enum.More.Default,\r\n\t\t\t// 在渲染之后的底部加载更多状态\r\n\t\t\tloadingStatusAfterRender: Enum.More.Default,\r\n\t\t\t// 底部加载更多时间戳\r\n\t\t\tloadingMoreTimeStamp: 0,\r\n\t\t\t// 底部加载更多slot\r\n\t\t\tloadingMoreDefaultSlot: null,\r\n\t\t\t// 是否展示底部加载更多\r\n\t\t\tshowLoadingMore: false,\r\n\t\t\t// 是否是开发者自定义的加载更多，-1代表交由z-paging自行判断；1代表没有更多了；0代表还有更多数据\r\n\t\t\tcustomNoMore: -1,\r\n\t\t}\r\n\t},\r\n\tcomputed: {\r\n\t\t// 底部加载更多配置\r\n\t\tzLoadMoreConfig() {\r\n\t\t\treturn {\r\n\t\t\t\tstatus: this.loadingStatusAfterRender,\r\n\t\t\t\tdefaultAsLoading: this.loadingMoreDefaultAsLoading || (this.useChatRecordMode && this.chatLoadingMoreDefaultAsLoading),\r\n\t\t\t\tdefaultThemeStyle: this.finalLoadingMoreThemeStyle,\r\n\t\t\t\tcustomStyle: this.loadingMoreCustomStyle,\r\n\t\t\t\ttitleCustomStyle: this.loadingMoreTitleCustomStyle,\r\n\t\t\t\ticonCustomStyle: this.loadingMoreLoadingIconCustomStyle,\r\n\t\t\t\tloadingIconType: this.loadingMoreLoadingIconType,\r\n\t\t\t\tloadingIconCustomImage: this.loadingMoreLoadingIconCustomImage,\r\n\t\t\t\tloadingAnimated: this.loadingMoreLoadingAnimated,\r\n\t\t\t\tshowNoMoreLine: this.showLoadingMoreNoMoreLine,\r\n\t\t\t\tnoMoreLineCustomStyle: this.loadingMoreNoMoreLineCustomStyle,\r\n\t\t\t\tdefaultText: this.finalLoadingMoreDefaultText,\r\n\t\t\t\tloadingText: this.finalLoadingMoreLoadingText,\r\n\t\t\t\tnoMoreText: this.finalLoadingMoreNoMoreText,\r\n\t\t\t\tfailText: this.finalLoadingMoreFailText,\r\n\t\t\t\thideContent: !this.loadingMoreDefaultAsLoading && this.listRendering,\r\n\t\t\t\tunit: this.unit,\r\n\t\t\t\tisChat: this.useChatRecordMode,\r\n\t\t\t\tchatDefaultAsLoading: this.chatLoadingMoreDefaultAsLoading\r\n\t\t\t};\r\n\t\t},\r\n\t\t// 最终的底部加载更多主题\r\n\t\tfinalLoadingMoreThemeStyle() {\r\n\t\t\treturn this.loadingMoreThemeStyle.length ? this.loadingMoreThemeStyle : this.defaultThemeStyle;\r\n\t\t},\r\n\t\t// 最终的底部加载更多触发阈值\r\n\t\tfinalLowerThreshold() {\r\n\t\t\treturn u.convertToPx(this.lowerThreshold);\r\n\t\t},\r\n\t\t// 是否显示默认状态下的底部加载更多\r\n\t\tshowLoadingMoreDefault() {\r\n\t\t\treturn this._showLoadingMore('Default');\r\n\t\t},\r\n\t\t// 是否显示加载中状态下的底部加载更多\r\n\t\tshowLoadingMoreLoading() {\r\n\t\t\treturn this._showLoadingMore('Loading');\r\n\t\t},\r\n\t\t// 是否显示没有更多了状态下的底部加载更多\r\n\t\tshowLoadingMoreNoMore() {\r\n\t\t\treturn this._showLoadingMore('NoMore');\r\n\t\t},\r\n\t\t// 是否显示加载失败状态下的底部加载更多\r\n\t\tshowLoadingMoreFail() {\r\n\t\t\treturn this._showLoadingMore('Fail');\r\n\t\t},\r\n\t\t// 是否显示自定义状态下的底部加载更多\r\n\t\tshowLoadingMoreCustom() {\r\n\t\t\treturn this._showLoadingMore('Custom');\r\n\t\t}\r\n\t},\r\n\tmethods: {\r\n\t\t// 页面滚动到底部时通知z-paging进行进一步处理\r\n\t\tpageReachBottom() {\r\n\t\t\t!this.useChatRecordMode && this._onLoadingMore('toBottom');\r\n\t\t},\r\n\t\t// 手动触发上拉加载更多(非必须，可依据具体需求使用)\r\n\t\tdoLoadMore(type) {\r\n\t\t\tthis._onLoadingMore(type);\r\n\t\t},\r\n\t\t// 通过@scroll事件检测是否滚动到了底部(顺带检测下是否滚动到了顶部)\r\n\t\t_checkScrolledToBottom(scrollDiff, checked = false) {\r\n\t\t\t// 如果当前scroll-view高度未获取，则获取其高度\r\n\t\t\tif (this.cacheScrollNodeHeight === -1) {\r\n\t\t\t\t// 获取当前scroll-view高度\r\n\t\t\t\tthis._getNodeClientRect('.zp-scroll-view').then((res) => {\r\n\t\t\t\t\tif (res) {\r\n\t\t\t\t\t\tconst scrollNodeHeight = res[0].height;\r\n\t\t\t\t\t\t// 缓存当前scroll-view高度，如果获取过了不再获取\r\n\t\t\t\t\t\tthis.cacheScrollNodeHeight = scrollNodeHeight;\r\n\t\t\t\t\t\t// // scrollDiff - this.cacheScrollNodeHeight = 当前滚动区域的顶部与内容底部的距离 - scroll-view高度 = 当前滚动区域的底部与内容底部的距离(也就是最终的与底部的距离)\r\n\t\t\t\t\t\tif (scrollDiff - scrollNodeHeight <= this.finalLowerThreshold) {\r\n\t\t\t\t\t\t\t// 如果与底部的距离小于阈值，则判断为滚动到了底部，触发滚动到底部事件\r\n\t\t\t\t\t\t\tthis._onLoadingMore('toBottom');\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t} else {\r\n\t\t\t\t// scrollDiff - this.cacheScrollNodeHeight = 当前滚动区域的顶部与内容底部的距离 - scroll-view高度 = 当前滚动区域的底部与内容底部的距离(也就是最终的与底部的距离)\r\n\t\t\t\tif (scrollDiff - this.cacheScrollNodeHeight <= this.finalLowerThreshold) {\r\n\t\t\t\t\t// 如果与底部的距离小于阈值，则判断为滚动到了底部，触发滚动到底部事件\r\n\t\t\t\t\tthis._onLoadingMore('toBottom');\r\n\t\t\t\t} else if (scrollDiff - this.cacheScrollNodeHeight <= 500 && !checked) {\r\n\t\t\t\t\t// 如果与底部的距离小于500px，则获取当前滚动的位置，延迟150毫秒重复上述步骤再次检测(避免@scroll触发时获取的scrollTop不正确导致的其他问题，此时获取的scrollTop不一定可信)。防止因为部分性能较差安卓设备@scroll采样率过低导致的滚动到底部但是依然没有触发的问题\r\n\t\t\t\t\tu.delay(() => {\r\n\t\t\t\t\t\tthis._getNodeClientRect('.zp-scroll-view', true, true).then((res) => {\r\n\t\t\t\t\t\t\tif (res) {\r\n\t\t\t\t\t\t\t\tthis.oldScrollTop = res[0].scrollTop;\r\n\t\t\t\t\t\t\t\tconst newScrollDiff = res[0].scrollHeight - this.oldScrollTop;\r\n\t\t\t\t\t\t\t\tthis._checkScrolledToBottom(newScrollDiff, true);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}, 150, 'checkScrolledToBottomDelay')\r\n\t\t\t\t}\r\n\t\t\t\t// 检测一下是否已经滚动到了顶部了，因为在安卓中滚动到顶部时scrollTop不一定为0(和滚动到底部一样的原因)，所以需要在scrollTop小于150px时，通过获取.zp-scroll-view的scrollTop再判断一下\r\n\t\t\t\tif (this.oldScrollTop <= 150 && this.oldScrollTop !== 0) {\r\n\t\t\t\t\tu.delay(() => {\r\n\t\t\t\t\t\t// 这里再判断一下是否确实已经滚动到顶部了，如果已经滚动到顶部了，则不用再判断了，再次判断的原因是可能150毫秒之后oldScrollTop才是0\r\n\t\t\t\t\t\tif (this.oldScrollTop !== 0) {\r\n\t\t\t\t\t\t\tthis._getNodeClientRect('.zp-scroll-view', true, true).then((res) => {\r\n\t\t\t\t\t\t\t\t// 如果150毫秒后.zp-scroll-view的scrollTop为0，则认为已经滚动到了顶部了\r\n\t\t\t\t\t\t\t\tif (res && res[0].scrollTop === 0 && this.oldScrollTop !== 0) {\r\n\t\t\t\t\t\t\t\t\tthis._onScrollToUpper();\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}, 150, 'checkScrolledToTopDelay')\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 触发加载更多时调用,from:toBottom-滑动到底部触发；1、click-点击加载更多触发\r\n\t\t_onLoadingMore(from = 'click') {\r\n\t\t\t// 如果是ios并且是滚动到底部的，则在滚动到底部时候尝试将列表设置为禁止滚动然后设置为允许滚动，以禁止底部bounce的效果\r\n\t\t\tif (this.isIos && from === 'toBottom' && !this.scrollToBottomBounceEnabled && this.scrollEnable) {\r\n\t\t\t\tthis.scrollEnable = false;\r\n\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\tthis.scrollEnable = true;\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t\t// emit scrolltolower\r\n\t\t\tthis.$emit('scrolltolower', from);\r\n\t\t\t// 如果是只使用下拉刷新 或者 禁用底部加载更多 或者 底部加载更多不是默认状态或加载失败状态 或者 是加载中状态 或者 空数据图已经展示了，则return，不触发内部加载更多逻辑\r\n\t\t\tif (this.refresherOnly || !this.loadingMoreEnabled || !(this.loadingStatus === Enum.More.Default || this.loadingStatus === Enum.More.Fail) || this.loading || this.showEmpty) return;\r\n\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\tif (!this.isIos && !this.refresherOnly && !this.usePageScroll) {\r\n\t\t\t\tconst currentTimestamp = u.getTime();\r\n\t\t\t\t// 在非ios平台+scroll-view中节流处理\r\n\t\t\t\tif (this.loadingMoreTimeStamp > 0 && currentTimestamp - this.loadingMoreTimeStamp < 100) {\r\n\t\t\t\t\tthis.loadingMoreTimeStamp = 0;\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t// #endif\r\n\t\t\t// 处理加载更多数据\r\n\t\t\tthis._doLoadingMore();\r\n\t\t},\r\n\t\t// 处理开始加载更多\r\n\t\t_doLoadingMore() {\r\n\t\t\tif (this.pageNo >= this.defaultPageNo && this.loadingStatus !== Enum.More.NoMore) {\r\n\t\t\t\tthis.pageNo ++;\r\n\t\t\t\tthis._startLoading(false);\r\n\t\t\t\tif (this.isLocalPaging) {\r\n\t\t\t\t\t// 如果是本地分页，则在组件内部对数据进行分页处理，不触发@query事件\r\n\t\t\t\t\tthis._localPagingQueryList(this.pageNo, this.defaultPageSize, this.localPagingLoadingTime, res => {\r\n\t\t\t\t\t\tthis.completeByTotal(res, this.totalLocalPagingList.length);\r\n\t\t\t\t\t\tthis.queryFrom = Enum.QueryFrom.LoadingMore;\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// emit @query相关加载更多事件\r\n\t\t\t\t\tthis._emitQuery(this.pageNo, this.defaultPageSize, Enum.QueryFrom.LoadingMore);\r\n\t\t\t\t\tthis._callMyParentQuery();\r\n\t\t\t\t}\r\n\t\t\t\t// 设置当前加载状态为底部加载更多状态\r\n\t\t\t\tthis.loadingType = Enum.LoadingType.LoadingMore;\r\n\t\t\t}\r\n\t\t},\r\n\t\t// (预处理)判断当没有更多数据且分页内容未超出z-paging时是否显示没有更多数据的view\r\n\t\t_preCheckShowNoMoreInside(newVal, scrollViewNode, pagingContainerNode) {\r\n\t\t\tif (this.loadingStatus === Enum.More.NoMore && this.hideNoMoreByLimit > 0 && newVal.length) {\r\n\t\t\t\tthis.showLoadingMore = newVal.length > this.hideNoMoreByLimit;\r\n\t\t\t} else if ((this.loadingStatus === Enum.More.NoMore && this.hideNoMoreInside && newVal.length) || (this.insideMore && this.insideOfPaging !== false && newVal.length)) {\r\n\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\tthis._checkShowNoMoreInside(newVal, scrollViewNode, pagingContainerNode);\r\n\t\t\t\t})\r\n\t\t\t\tif (this.insideMore && this.insideOfPaging !== false && newVal.length) {\r\n\t\t\t\t\tthis.showLoadingMore = newVal.length;\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\tthis.showLoadingMore = newVal.length;\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 判断当没有更多数据且分页内容未超出z-paging时是否显示没有更多数据的view\r\n\t\tasync _checkShowNoMoreInside(totalData, oldScrollViewNode, oldPagingContainerNode) {\r\n\t\t\ttry {\r\n\t\t\t\tconst scrollViewNode = oldScrollViewNode || await this._getNodeClientRect('.zp-scroll-view');\r\n\t\t\t\t// 在页面滚动模式下\r\n\t\t\t\tif (this.usePageScroll) {\r\n\t\t\t\t\tif (scrollViewNode) {\r\n\t\t\t\t\t\t// 获取滚动内容总高度\r\n\t\t\t\t\t\tconst scrollViewTotalH = scrollViewNode[0].top + scrollViewNode[0].height;\r\n\t\t\t\t\t\t// 如果滚动内容总高度小于窗口高度，则认为内容未超出z-paging\r\n\t\t\t\t\t\tthis.insideOfPaging = scrollViewTotalH < this.windowHeight;\r\n\t\t\t\t\t\t// 如果需要没有更多数据时，隐藏底部加载更多view，并且内容未超过z-paging，则隐藏底部加载更多\r\n\t\t\t\t\t\tif (this.hideNoMoreInside) {\r\n\t\t\t\t\t\t\tthis.showLoadingMore = !this.insideOfPaging;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t// 如果需要内容未超过z-paging时自动加载更多，则触发加载更多\r\n\t\t\t\t\t\tthis._updateInsideOfPaging();\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 在scroll-view滚动模式下\r\n\t\t\t\t\tconst pagingContainerNode = oldPagingContainerNode || await this._getNodeClientRect('.zp-paging-container-content');\r\n\t\t\t\t\t// 获取滚动内容总高度\r\n\t\t\t\t\tconst pagingContainerH = pagingContainerNode ? pagingContainerNode[0].height : 0;\r\n\t\t\t\t\t// 获取z-paging内置scroll-view高度\r\n\t\t\t\t\tconst scrollViewH = scrollViewNode ? scrollViewNode[0].height : 0;\r\n\t\t\t\t\t// 如果滚动内容总高度小于z-paging内置scroll-view高度，则认为内容未超出z-paging\r\n\t\t\t\t\tthis.insideOfPaging = pagingContainerH < scrollViewH;\r\n\t\t\t\t\tif (this.hideNoMoreInside) {\r\n\t\t\t\t\t\tthis.showLoadingMore = !this.insideOfPaging;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// 如果需要内容未超过z-paging时自动加载更多，则触发加载更多\r\n\t\t\t\t\tthis._updateInsideOfPaging();\r\n\t\t\t\t}\r\n\t\t\t} catch (e) {\r\n\t\t\t\t// 如果发生了异常，判断totalData数组长度为0，则认为内容未超出z-paging\r\n\t\t\t\tthis.insideOfPaging = !totalData.length;\r\n\t\t\t\tif (this.hideNoMoreInside) {\r\n\t\t\t\t\tthis.showLoadingMore = !this.insideOfPaging;\r\n\t\t\t\t}\r\n\t\t\t\t// 如果需要内容未超过z-paging时自动加载更多，则触发加载更多\r\n\t\t\t\tthis._updateInsideOfPaging();\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 是否要展示上拉加载更多view\r\n\t\t_showLoadingMore(type) {\r\n\t\t\tif (!this.showLoadingMoreWhenReload && (!(this.loadingStatus === Enum.More.Default ? this.nShowBottom : true) || !this.realTotalData.length)) return false;\r\n\t\t\tif (((!this.showLoadingMoreWhenReload || this.isUserPullDown || this.loadingStatus !== Enum.More.Loading) && !this.showLoadingMore) || \r\n\t\t\t(!this.loadingMoreEnabled && (!this.showLoadingMoreWhenReload || this.isUserPullDown || this.loadingStatus !== Enum.More.Loading)) || this.refresherOnly) {\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t\tif (this.useChatRecordMode && type !== 'Loading') return false;\r\n\t\t\tif (!this.zSlots) return false;\r\n\t\t\tif (type === 'Custom') {\r\n\t\t\t\treturn this.showDefaultLoadingMoreText && !(this.loadingStatus === Enum.More.NoMore && !this.showLoadingMoreNoMoreView);\r\n\t\t\t}\r\n\t\t\tconst res = this.loadingStatus === Enum.More[type] && this.zSlots[`loadingMore${type}`] && (type === 'NoMore' ? this.showLoadingMoreNoMoreView : true);\r\n\t\t\tif (res) {\r\n\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\tif (!this.isIos) {\r\n\t\t\t\t\tthis.nLoadingMoreFixedHeight = false;\r\n\t\t\t\t}\r\n\t\t\t\t//  #endif\r\n\t\t\t}\r\n\t\t\treturn res;\r\n\t\t},\r\n\t}\r\n}\r\n"], "names": ["u", "Enum"], "mappings": ";;;AAIA,MAAe,iBAAA;AAAA,EACd,OAAO;AAAA;AAAA,IAEN,wBAAwB;AAAA,MACvB,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,0BAA0B,CAAA,CAAE;AAAA,IAC1C;AAAA;AAAA,IAED,6BAA6B;AAAA,MAC5B,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,+BAA+B,CAAA,CAAE;AAAA,IAC/C;AAAA;AAAA,IAED,mCAAmC;AAAA,MAClC,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,qCAAqC,CAAA,CAAE;AAAA,IACrD;AAAA;AAAA,IAED,4BAA4B;AAAA,MAC3B,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,8BAA8B,QAAQ;AAAA,IACpD;AAAA;AAAA,IAED,mCAAmC;AAAA,MAClC,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,qCAAqC,EAAE;AAAA,IACrD;AAAA;AAAA,IAED,4BAA4B;AAAA,MAC3B,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,8BAA8B,IAAI;AAAA,IAChD;AAAA;AAAA,IAED,oBAAoB;AAAA,MACnB,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,sBAAsB,IAAI;AAAA,IACxC;AAAA;AAAA,IAED,4BAA4B;AAAA,MAC3B,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,8BAA8B,IAAI;AAAA,IAChD;AAAA;AAAA,IAED,6BAA6B;AAAA,MAC5B,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,+BAA+B,KAAK;AAAA,IAClD;AAAA;AAAA,IAED,wBAAwB;AAAA,MACvB,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAASA,uDAAC,EAAC,GAAG,0BAA0B,IAAI;AAAA,IAC5C;AAAA;AAAA,IAED,wBAAwB;AAAA,MACvB,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAASA,uDAAC,EAAC,GAAG,0BAA0B,IAAI;AAAA,IAC5C;AAAA;AAAA,IAED,uBAAuB;AAAA,MACtB,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAASA,uDAAC,EAAC,GAAG,yBAAyB,IAAI;AAAA,IAC3C;AAAA;AAAA,IAED,qBAAqB;AAAA,MACpB,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAASA,uDAAC,EAAC,GAAG,uBAAuB,IAAI;AAAA,IACzC;AAAA;AAAA,IAED,kBAAkB;AAAA,MACjB,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,oBAAoB,KAAK;AAAA,IACvC;AAAA;AAAA,IAED,mBAAmB;AAAA,MAClB,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,qBAAqB,CAAC;AAAA,IACpC;AAAA;AAAA,IAED,4BAA4B;AAAA,MAC3B,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,8BAA8B,IAAI;AAAA,IAChD;AAAA;AAAA,IAED,2BAA2B;AAAA,MAC1B,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,6BAA6B,IAAI;AAAA,IAC/C;AAAA;AAAA,IAED,2BAA2B;AAAA,MAC1B,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,6BAA6B,IAAI;AAAA,IAC/C;AAAA;AAAA,IAED,kCAAkC;AAAA,MACjC,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,oCAAoC,CAAA,CAAE;AAAA,IACpD;AAAA;AAAA,IAED,YAAY;AAAA,MACX,MAAM;AAAA,MACN,SAASA,uDAAC,EAAC,GAAG,cAAc,KAAK;AAAA,IACjC;AAAA;AAAA,IAED,gBAAgB;AAAA,MACf,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAASA,uDAAC,EAAC,GAAG,kBAAkB,QAAQ;AAAA,IACxC;AAAA,EACD;AAAA,EACD,OAAO;AACN,WAAO;AAAA,MACN,GAAGC,sDAAI,KAAC;AAAA;AAAA,MAER,eAAeA,sDAAAA,KAAK,KAAK;AAAA;AAAA,MAEzB,0BAA0BA,sDAAAA,KAAK,KAAK;AAAA;AAAA,MAEpC,sBAAsB;AAAA;AAAA,MAEtB,wBAAwB;AAAA;AAAA,MAExB,iBAAiB;AAAA;AAAA,MAEjB,cAAc;AAAA,IACd;AAAA,EACD;AAAA,EACD,UAAU;AAAA;AAAA,IAET,kBAAkB;AACjB,aAAO;AAAA,QACN,QAAQ,KAAK;AAAA,QACb,kBAAkB,KAAK,+BAAgC,KAAK,qBAAqB,KAAK;AAAA,QACtF,mBAAmB,KAAK;AAAA,QACxB,aAAa,KAAK;AAAA,QAClB,kBAAkB,KAAK;AAAA,QACvB,iBAAiB,KAAK;AAAA,QACtB,iBAAiB,KAAK;AAAA,QACtB,wBAAwB,KAAK;AAAA,QAC7B,iBAAiB,KAAK;AAAA,QACtB,gBAAgB,KAAK;AAAA,QACrB,uBAAuB,KAAK;AAAA,QAC5B,aAAa,KAAK;AAAA,QAClB,aAAa,KAAK;AAAA,QAClB,YAAY,KAAK;AAAA,QACjB,UAAU,KAAK;AAAA,QACf,aAAa,CAAC,KAAK,+BAA+B,KAAK;AAAA,QACvD,MAAM,KAAK;AAAA,QACX,QAAQ,KAAK;AAAA,QACb,sBAAsB,KAAK;AAAA,MAC/B;AAAA,IACG;AAAA;AAAA,IAED,6BAA6B;AAC5B,aAAO,KAAK,sBAAsB,SAAS,KAAK,wBAAwB,KAAK;AAAA,IAC7E;AAAA;AAAA,IAED,sBAAsB;AACrB,aAAOD,yDAAE,YAAY,KAAK,cAAc;AAAA,IACxC;AAAA;AAAA,IAED,yBAAyB;AACxB,aAAO,KAAK,iBAAiB,SAAS;AAAA,IACtC;AAAA;AAAA,IAED,yBAAyB;AACxB,aAAO,KAAK,iBAAiB,SAAS;AAAA,IACtC;AAAA;AAAA,IAED,wBAAwB;AACvB,aAAO,KAAK,iBAAiB,QAAQ;AAAA,IACrC;AAAA;AAAA,IAED,sBAAsB;AACrB,aAAO,KAAK,iBAAiB,MAAM;AAAA,IACnC;AAAA;AAAA,IAED,wBAAwB;AACvB,aAAO,KAAK,iBAAiB,QAAQ;AAAA,IACrC;AAAA,EACD;AAAA,EACD,SAAS;AAAA;AAAA,IAER,kBAAkB;AACjB,OAAC,KAAK,qBAAqB,KAAK,eAAe,UAAU;AAAA,IACzD;AAAA;AAAA,IAED,WAAW,MAAM;AAChB,WAAK,eAAe,IAAI;AAAA,IACxB;AAAA;AAAA,IAED,uBAAuB,YAAY,UAAU,OAAO;AAEnD,UAAI,KAAK,0BAA0B,IAAI;AAEtC,aAAK,mBAAmB,iBAAiB,EAAE,KAAK,CAAC,QAAQ;AACxD,cAAI,KAAK;AACR,kBAAM,mBAAmB,IAAI,CAAC,EAAE;AAEhC,iBAAK,wBAAwB;AAE7B,gBAAI,aAAa,oBAAoB,KAAK,qBAAqB;AAE9D,mBAAK,eAAe,UAAU;AAAA,YAC9B;AAAA,UACD;AAAA,QACN,CAAK;AAAA,MACL,OAAU;AAEN,YAAI,aAAa,KAAK,yBAAyB,KAAK,qBAAqB;AAExE,eAAK,eAAe,UAAU;AAAA,QACnC,WAAe,aAAa,KAAK,yBAAyB,OAAO,CAAC,SAAS;AAEtEA,iEAAC,EAAC,MAAM,MAAM;AACb,iBAAK,mBAAmB,mBAAmB,MAAM,IAAI,EAAE,KAAK,CAAC,QAAQ;AACpE,kBAAI,KAAK;AACR,qBAAK,eAAe,IAAI,CAAC,EAAE;AAC3B,sBAAM,gBAAgB,IAAI,CAAC,EAAE,eAAe,KAAK;AACjD,qBAAK,uBAAuB,eAAe,IAAI;AAAA,cAC/C;AAAA,YACR,CAAO;AAAA,UACP,GAAQ,KAAK,4BAA4B;AAAA,QACpC;AAED,YAAI,KAAK,gBAAgB,OAAO,KAAK,iBAAiB,GAAG;AACxDA,iEAAC,EAAC,MAAM,MAAM;AAEb,gBAAI,KAAK,iBAAiB,GAAG;AAC5B,mBAAK,mBAAmB,mBAAmB,MAAM,IAAI,EAAE,KAAK,CAAC,QAAQ;AAEpE,oBAAI,OAAO,IAAI,CAAC,EAAE,cAAc,KAAK,KAAK,iBAAiB,GAAG;AAC7D,uBAAK,iBAAgB;AAAA,gBACrB;AAAA,cACT,CAAQ;AAAA,YACD;AAAA,UACP,GAAQ,KAAK,yBAAyB;AAAA,QACjC;AAAA,MACD;AAAA,IACD;AAAA;AAAA,IAED,eAAe,OAAO,SAAS;AAE9B,UAAI,KAAK,SAAS,SAAS,cAAc,CAAC,KAAK,+BAA+B,KAAK,cAAc;AAChG,aAAK,eAAe;AACpB,aAAK,UAAU,MAAM;AACpB,eAAK,eAAe;AAAA,QACzB,CAAK;AAAA,MACD;AAED,WAAK,MAAM,iBAAiB,IAAI;AAEhC,UAAI,KAAK,iBAAiB,CAAC,KAAK,sBAAsB,EAAE,KAAK,kBAAkBC,sDAAI,KAAC,KAAK,WAAW,KAAK,kBAAkBA,2DAAK,KAAK,SAAS,KAAK,WAAW,KAAK;AAAW;AAE9K,UAAI,CAAC,KAAK,SAAS,CAAC,KAAK,iBAAiB,CAAC,KAAK,eAAe;AAC9D,cAAM,mBAAmBD,yDAAE;AAE3B,YAAI,KAAK,uBAAuB,KAAK,mBAAmB,KAAK,uBAAuB,KAAK;AACxF,eAAK,uBAAuB;AAC5B;AAAA,QACA;AAAA,MACD;AAGD,WAAK,eAAc;AAAA,IACnB;AAAA;AAAA,IAED,iBAAiB;AAChB,UAAI,KAAK,UAAU,KAAK,iBAAiB,KAAK,kBAAkBC,sDAAAA,KAAK,KAAK,QAAQ;AACjF,aAAK;AACL,aAAK,cAAc,KAAK;AACxB,YAAI,KAAK,eAAe;AAEvB,eAAK,sBAAsB,KAAK,QAAQ,KAAK,iBAAiB,KAAK,wBAAwB,SAAO;AACjG,iBAAK,gBAAgB,KAAK,KAAK,qBAAqB,MAAM;AAC1D,iBAAK,YAAYA,2DAAK,UAAU;AAAA,UACtC,CAAM;AAAA,QACN,OAAW;AAEN,eAAK,WAAW,KAAK,QAAQ,KAAK,iBAAiBA,sDAAI,KAAC,UAAU,WAAW;AAC7E,eAAK,mBAAkB;AAAA,QACvB;AAED,aAAK,cAAcA,2DAAK,YAAY;AAAA,MACpC;AAAA,IACD;AAAA;AAAA,IAED,0BAA0B,QAAQ,gBAAgB,qBAAqB;AACtE,UAAI,KAAK,kBAAkBA,sDAAI,KAAC,KAAK,UAAU,KAAK,oBAAoB,KAAK,OAAO,QAAQ;AAC3F,aAAK,kBAAkB,OAAO,SAAS,KAAK;AAAA,MAChD,WAAe,KAAK,kBAAkBA,sDAAI,KAAC,KAAK,UAAU,KAAK,oBAAoB,OAAO,UAAY,KAAK,cAAc,KAAK,mBAAmB,SAAS,OAAO,QAAS;AACtK,aAAK,UAAU,MAAM;AACpB,eAAK,uBAAuB,QAAQ,gBAAgB,mBAAmB;AAAA,QAC5E,CAAK;AACD,YAAI,KAAK,cAAc,KAAK,mBAAmB,SAAS,OAAO,QAAQ;AACtE,eAAK,kBAAkB,OAAO;AAAA,QAC9B;AAAA,MACL,OAAU;AACN,aAAK,kBAAkB,OAAO;AAAA,MAC9B;AAAA,IACD;AAAA;AAAA,IAED,MAAM,uBAAuB,WAAW,mBAAmB,wBAAwB;AAClF,UAAI;AACH,cAAM,iBAAiB,qBAAqB,MAAM,KAAK,mBAAmB,iBAAiB;AAE3F,YAAI,KAAK,eAAe;AACvB,cAAI,gBAAgB;AAEnB,kBAAM,mBAAmB,eAAe,CAAC,EAAE,MAAM,eAAe,CAAC,EAAE;AAEnE,iBAAK,iBAAiB,mBAAmB,KAAK;AAE9C,gBAAI,KAAK,kBAAkB;AAC1B,mBAAK,kBAAkB,CAAC,KAAK;AAAA,YAC7B;AAED,iBAAK,sBAAqB;AAAA,UAC1B;AAAA,QACN,OAAW;AAEN,gBAAM,sBAAsB,0BAA0B,MAAM,KAAK,mBAAmB,8BAA8B;AAElH,gBAAM,mBAAmB,sBAAsB,oBAAoB,CAAC,EAAE,SAAS;AAE/E,gBAAM,cAAc,iBAAiB,eAAe,CAAC,EAAE,SAAS;AAEhE,eAAK,iBAAiB,mBAAmB;AACzC,cAAI,KAAK,kBAAkB;AAC1B,iBAAK,kBAAkB,CAAC,KAAK;AAAA,UAC7B;AAED,eAAK,sBAAqB;AAAA,QAC1B;AAAA,MACD,SAAQ,GAAG;AAEX,aAAK,iBAAiB,CAAC,UAAU;AACjC,YAAI,KAAK,kBAAkB;AAC1B,eAAK,kBAAkB,CAAC,KAAK;AAAA,QAC7B;AAED,aAAK,sBAAqB;AAAA,MAC1B;AAAA,IACD;AAAA;AAAA,IAED,iBAAiB,MAAM;AACtB,UAAI,CAAC,KAAK,8BAA8B,EAAE,KAAK,kBAAkBA,sDAAAA,KAAK,KAAK,UAAU,KAAK,cAAc,SAAS,CAAC,KAAK,cAAc;AAAS,eAAO;AACrJ,WAAM,CAAC,KAAK,6BAA6B,KAAK,kBAAkB,KAAK,kBAAkBA,sDAAAA,KAAK,KAAK,YAAY,CAAC,KAAK,mBAClH,CAAC,KAAK,uBAAuB,CAAC,KAAK,6BAA6B,KAAK,kBAAkB,KAAK,kBAAkBA,sDAAI,KAAC,KAAK,YAAa,KAAK,eAAe;AACzJ,eAAO;AAAA,MACP;AACD,UAAI,KAAK,qBAAqB,SAAS;AAAW,eAAO;AACzD,UAAI,CAAC,KAAK;AAAQ,eAAO;AACzB,UAAI,SAAS,UAAU;AACtB,eAAO,KAAK,8BAA8B,EAAE,KAAK,kBAAkBA,2DAAK,KAAK,UAAU,CAAC,KAAK;AAAA,MAC7F;AACD,YAAM,MAAM,KAAK,kBAAkBA,sDAAAA,KAAK,KAAK,IAAI,KAAK,KAAK,OAAO,cAAc,IAAI,EAAE,MAAM,SAAS,WAAW,KAAK,4BAA4B;AAQjJ,aAAO;AAAA,IACP;AAAA,EACD;AACF;;"}