"use strict";const e=require("../../../../common/vendor.js"),t=require("../function/index.js");const i=(new class{constructor(){this.config={type:"navigateTo",url:"",delta:1,params:{},animationType:"pop-in",animationDuration:300,intercept:!1,events:{}},this.route=this.route.bind(this)}addRootPath(e){return"/"===e[0]?e:`/${e}`}mixinParam(e,i){e=e&&this.addRootPath(e);let a="";return/.*\/.*\?.*=.*/.test(e)?(a=t.queryParams(i,!1),e+`&${a}`):(a=t.queryParams(i),e+a)}async route(e={},i={}){let a={};if("string"==typeof e?(a.url=this.mixinParam(e,i),a.type="navigateTo"):(a=t.deepMerge(this.config,e),a.url=this.mixinParam(e.url,e.params)),a.url!==t.page())if(i.intercept&&(a.intercept=i.intercept),a.params=i,a=t.deepMerge(this.config,a),"function"==typeof a.intercept){await new Promise(((e,t)=>{a.intercept(a,e)}))&&this.openPage(a)}else this.openPage(a)}openPage(t){const{url:i,type:a,delta:n,animationType:r,animationDuration:o,events:s}=t;"navigateTo"!=t.type&&"to"!=t.type||e.index.navigateTo({url:i,animationType:r,animationDuration:o,events:s}),"redirectTo"!=t.type&&"redirect"!=t.type||e.index.redirectTo({url:i}),"switchTab"!=t.type&&"tab"!=t.type||e.index.switchTab({url:i}),"reLaunch"!=t.type&&"launch"!=t.type||e.index.reLaunch({url:i}),"navigateBack"!=t.type&&"back"!=t.type||e.index.navigateBack({delta:n})}}).route;exports.route=i;
