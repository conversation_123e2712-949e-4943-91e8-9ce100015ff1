"use strict";
const common_vendor = require("../../common/vendor.js");
const stores_user = require("../../stores/user.js");
const api_index = require("../../api/index.js");
if (!Array) {
  const _easycom_uni_swipe_action_item2 = common_vendor.resolveComponent("uni-swipe-action-item");
  const _easycom_uni_swipe_action2 = common_vendor.resolveComponent("uni-swipe-action");
  (_easycom_uni_swipe_action_item2 + _easycom_uni_swipe_action2)();
}
const _easycom_uni_swipe_action_item = () => "../../uni_modules/uni-swipe-action/components/uni-swipe-action-item/uni-swipe-action-item.js";
const _easycom_uni_swipe_action = () => "../../uni_modules/uni-swipe-action/components/uni-swipe-action/uni-swipe-action.js";
if (!Math) {
  (_easycom_uni_swipe_action_item + _easycom_uni_swipe_action)();
}
const _sfc_main = {
  __name: "msg",
  setup(__props) {
    const userStore = stores_user.useUserStore();
    const sessionList = common_vendor.ref([]);
    const pageParams = common_vendor.reactive({
      page: 1,
      pageSize: 10
    });
    const loading = common_vendor.ref(false);
    const refreshing = common_vendor.ref(false);
    const loadingMore = common_vendor.ref(false);
    const noMoreData = common_vendor.ref(false);
    const pageInfo = common_vendor.reactive({
      current_page: 1,
      last_page: 1,
      per_page: 10,
      total: 0
    });
    const showEditModal = common_vendor.ref(false);
    const swipeOptions = common_vendor.ref([
      {
        text: "置顶",
        style: {
          backgroundColor: "#5A7BF7",
          color: "#fff"
        }
      },
      {
        text: "编辑",
        style: {
          backgroundColor: "#FF9F40",
          color: "#fff"
        }
      },
      {
        text: "删除",
        style: {
          backgroundColor: "#FF6B6B",
          color: "#fff"
        }
      }
    ]);
    const closeSwipeOptions = common_vendor.ref([
      {
        text: "取消置顶",
        style: {
          backgroundColor: "#FF6B6B",
          color: "#fff"
        }
      },
      {
        text: "编辑",
        style: {
          backgroundColor: "#FF9F40",
          color: "#fff"
        }
      },
      {
        text: "删除",
        style: {
          backgroundColor: "#FF6B6B",
          color: "#fff"
        }
      }
    ]);
    const swipeActionRef = common_vendor.ref(null);
    const onRefresh = () => {
      refreshing.value = true;
      pageParams.page = 1;
      noMoreData.value = false;
      getMySessionList(true);
    };
    const onRestore = () => {
      refreshing.value = false;
    };
    const onLoadMore = () => {
      if (loadingMore.value || noMoreData.value)
        return;
      if (pageInfo.current_page < pageInfo.last_page) {
        pageParams.page++;
        getMySessionList(false);
      } else {
        noMoreData.value = true;
      }
    };
    const handleSwipeClick = (event, item) => {
      const { index } = event;
      switch (index) {
        case 0:
          handleTop(item);
          break;
        case 1:
          handleEdit(item);
          break;
        case 2:
          handleDelete(item);
          break;
      }
    };
    const handleTop = async (item) => {
      let isTop = 1;
      let text = "置顶成功";
      if (item.isTop === 1) {
        isTop = 0;
        text = "取消置顶成功";
      } else {
        isTop = 1;
      }
      swipeActionRef.value.closeAll();
      await api_index.setSessionTopApi({
        merchantGuid: userStore.merchantGuid,
        sessionGuid: item.guid,
        isTop
      });
      await getMySessionList();
      common_vendor.index.showToast({
        title: text,
        icon: "success"
      });
    };
    const updateReq = common_vendor.reactive({
      merchantGuid: userStore.merchantGuid,
      //商户uuid
      sessionGuid: "",
      //智能体对话uuid
      sessionTitle: ""
      //对话自定义标题
    });
    const handleEdit = (item) => {
      common_vendor.index.__f__("log", "at pages/msg-list/msg.vue:229", "编辑:", item.sessionTitle);
      updateReq.sessionTitle = item.sessionTitle;
      updateReq.sessionGuid = item.guid;
      showEditModal.value = true;
    };
    const handleDelete = (item) => {
      common_vendor.index.showModal({
        title: "确认删除",
        content: "确定要删除这个对话吗？",
        success: async (res) => {
          if (res.confirm) {
            let res2 = await api_index.deleteSessionApi({
              merchantGuid: userStore.merchantGuid,
              sessionGuid: item.guid
            });
            if (res2.code === 0) {
              common_vendor.index.showToast({
                title: "已删除",
                icon: "success"
              });
              getMySessionList();
            } else {
              common_vendor.index.showToast({
                title: res2.msg,
                icon: "none"
              });
            }
          }
        }
      });
    };
    const closeEditModal = () => {
      showEditModal.value = false;
      updateReq.sessionTitle = "";
      updateReq.sessionGuid = "";
    };
    const confirmEdit = async () => {
      if (!updateReq.sessionTitle.trim()) {
        common_vendor.index.showToast({
          title: "请输入对话名称",
          icon: "none"
        });
        return;
      }
      await api_index.updateSessionTitleApi(updateReq);
      common_vendor.index.showToast({
        title: "修改成功",
        icon: "success"
      });
      closeEditModal();
      getMySessionList();
    };
    const getMySessionList = async (isRefresh = false) => {
      try {
        if (isRefresh) {
          refreshing.value = true;
        } else if (pageParams.page === 1) {
          loading.value = true;
        } else {
          loadingMore.value = true;
        }
        const res = await api_index.getMySessionListApi({
          merchantGuid: userStore.merchantGuid,
          pageSize: pageParams.pageSize,
          page: pageParams.page
        });
        if (res.data) {
          pageInfo.current_page = res.data.current_page;
          pageInfo.last_page = res.data.last_page;
          pageInfo.per_page = res.data.per_page;
          pageInfo.total = res.data.total;
          if (isRefresh || pageParams.page === 1) {
            sessionList.value = res.data.data || [];
          } else {
            sessionList.value.push(...res.data.data || []);
          }
          if (pageInfo.current_page >= pageInfo.last_page) {
            noMoreData.value = true;
          }
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/msg-list/msg.vue:338", "获取对话列表失败:", error);
        common_vendor.index.showToast({
          title: "加载失败",
          icon: "none"
        });
      } finally {
        loading.value = false;
        refreshing.value = false;
        loadingMore.value = false;
      }
    };
    const handleItemClick = (item) => {
      common_vendor.index.navigateTo({
        url: `/pages/msg/index?sessionGuid=${item.agentGuid}&sysId=${item.agent.sysId}`
      });
    };
    common_vendor.onShow(() => {
      if (userStore.userToken) {
        getMySessionList();
      }
    });
    common_vendor.watch(
      () => userStore.userToken,
      (newValue, oldValue) => {
        if (newValue && oldValue === "") {
          getMySessionList();
        }
      }
    );
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: loading.value && sessionList.value.length === 0
      }, loading.value && sessionList.value.length === 0 ? {} : !loading.value && sessionList.value.length === 0 ? {} : common_vendor.e({
        c: common_vendor.f(sessionList.value, (item, index, i0) => {
          return {
            a: item.agent.agentAvatar,
            b: common_vendor.t(item.sessionTitle),
            c: common_vendor.t(item.lastMessage.content),
            d: common_vendor.o(($event) => handleItemClick(item), item.guid),
            e: common_vendor.o(($event) => handleSwipeClick($event, item), item.guid),
            f: item.guid,
            g: "59e0655d-1-" + i0 + ",59e0655d-0",
            h: common_vendor.p({
              threshold: 0,
              ["right-options"]: item.isTop === 1 ? closeSwipeOptions.value : swipeOptions.value
            })
          };
        }),
        d: common_vendor.sr(swipeActionRef, "59e0655d-0", {
          "k": "swipeActionRef"
        }),
        e: sessionList.value.length > 0
      }, sessionList.value.length > 0 ? common_vendor.e({
        f: loadingMore.value
      }, loadingMore.value ? {} : noMoreData.value ? {} : {}, {
        g: noMoreData.value
      }) : {}), {
        b: !loading.value && sessionList.value.length === 0,
        h: refreshing.value,
        i: common_vendor.o(onRefresh),
        j: common_vendor.o(onRestore),
        k: common_vendor.o(onLoadMore),
        l: showEditModal.value
      }, showEditModal.value ? {
        m: showEditModal.value,
        n: updateReq.sessionTitle,
        o: common_vendor.o(($event) => updateReq.sessionTitle = $event.detail.value),
        p: common_vendor.o(closeEditModal),
        q: common_vendor.o(confirmEdit),
        r: common_vendor.o(() => {
        }),
        s: common_vendor.o(closeEditModal)
      } : {});
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-59e0655d"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/msg-list/msg.js.map
