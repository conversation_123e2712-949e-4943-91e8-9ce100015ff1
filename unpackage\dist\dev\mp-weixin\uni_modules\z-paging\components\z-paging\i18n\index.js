"use strict";
const en = {
  "zp.refresher.default": "Pull down to refresh",
  "zp.refresher.pulling": "Release to refresh",
  "zp.refresher.refreshing": "Refreshing...",
  "zp.refresher.complete": "Refresh succeeded",
  "zp.refresher.f2": "Refresh to enter 2f",
  "zp.loadingMore.default": "Click to load more",
  "zp.loadingMore.loading": "Loading...",
  "zp.loadingMore.noMore": "No more data",
  "zp.loadingMore.fail": "Load failed,click to reload",
  "zp.emptyView.title": "No data",
  "zp.emptyView.reload": "Reload",
  "zp.emptyView.error": "Sorry,load failed",
  "zp.refresherUpdateTime.title": "Last update: ",
  "zp.refresherUpdateTime.none": "None",
  "zp.refresherUpdateTime.today": "Today",
  "zp.refresherUpdateTime.yesterday": "Yesterday",
  "zp.systemLoading.title": "Loading..."
};
const zhHans = {
  "zp.refresher.default": "继续下拉刷新",
  "zp.refresher.pulling": "松开立即刷新",
  "zp.refresher.refreshing": "正在刷新...",
  "zp.refresher.complete": "刷新成功",
  "zp.refresher.f2": "松手进入二楼",
  "zp.loadingMore.default": "点击加载更多",
  "zp.loadingMore.loading": "正在加载...",
  "zp.loadingMore.noMore": "没有更多了",
  "zp.loadingMore.fail": "加载失败，点击重新加载",
  "zp.emptyView.title": "没有数据哦~",
  "zp.emptyView.reload": "重新加载",
  "zp.emptyView.error": "很抱歉，加载失败",
  "zp.refresherUpdateTime.title": "最后更新：",
  "zp.refresherUpdateTime.none": "无",
  "zp.refresherUpdateTime.today": "今天",
  "zp.refresherUpdateTime.yesterday": "昨天",
  "zp.systemLoading.title": "加载中..."
};
const zhHant = {
  "zp.refresher.default": "繼續下拉重繪",
  "zp.refresher.pulling": "鬆開立即重繪",
  "zp.refresher.refreshing": "正在重繪...",
  "zp.refresher.complete": "重繪成功",
  "zp.refresher.f2": "鬆手進入二樓",
  "zp.loadingMore.default": "點擊加載更多",
  "zp.loadingMore.loading": "正在加載...",
  "zp.loadingMore.noMore": "沒有更多了",
  "zp.loadingMore.fail": "加載失敗，點擊重新加載",
  "zp.emptyView.title": "沒有數據哦~",
  "zp.emptyView.reload": "重新加載",
  "zp.emptyView.error": "很抱歉，加載失敗",
  "zp.refresherUpdateTime.title": "最後更新：",
  "zp.refresherUpdateTime.none": "無",
  "zp.refresherUpdateTime.today": "今天",
  "zp.refresherUpdateTime.yesterday": "昨天",
  "zp.systemLoading.title": "加載中..."
};
const messages = {
  en,
  "zh-Hans": zhHans,
  "zh-Hant": zhHant
};
exports.messages = messages;
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/uni_modules/z-paging/components/z-paging/i18n/index.js.map
