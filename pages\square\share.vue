<template>
  <view class="share-container">
    <!-- 海报预览 -->
    <view class="poster-preview">
      <image :src="path" mode="widthFix" class="poster-image"></image>
      <!-- <view v-else class="loading-text">正在生成海报...</view> -->
    </view>

    <!-- 下载按钮 -->
    <view class="download-btn" @click="downloadPoster">
      <text class="download-text">下载海报</text>
    </view>

    <!-- 海报生成器 -->
    <!-- <l-painter ref="painterRef" @fail="onPosterFail" :board="poster" width="600rpx" height="835rpx" hidden
      class="painter-canvas">https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/f567f2e6e0cb40c48b2510b888bd3b40.png
    </l-painter> -->

    <!-- <image :src="path" mode="widthFix"></image> -->
    <l-painter isCanvasToTempFilePath @success="path = $event" pathType="url" hidden
      css="width: 600rpx; height: 835rpx; background-image: url(https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/f567f2e6e0cb40c48b2510b888bd3b40.png); background-size: cover;">
      <template v-if="dataReady">
        <l-painter-image :src="agentDetail.agentAvatar"
          css="width: 200rpx; height: 200rpx; display: block; border-radius: 50%; margin:80rpx auto 10rpx auto" />
        <l-painter-text :text="agentDetail.agentName"
          css=" width: 500rpx; text-align: center; font-size: 42rpx; font-weight: bold; color: #1a1a1a; margin: 45rpx auto 0 auto;" />
        <l-painter-text :text="agentDetail.agentDesc"
          css=" width: 450rpx; text-align: center; font-size: 26rpx; color: #666666; line-height: 38rpx;margin: 20rpx auto 0 auto" />
        <l-painter-image :src="qrcode"
          css="width: 200rpx; height: 200rpx; display: block; margin: 60rpx auto 0 auto; border-radius: 50%;" />
      </template>
    </l-painter>
  </view>
</template>
<script setup>
import { ref, nextTick, reactive } from 'vue'
import { onLoad } from '@dcloudio/uni-app'

// 页面数据
const path = ref('')
const dataReady = ref(false) // 控制数据是否准备完成
let agentDetail = reactive({
  agentName: '',
  agentDesc: '',
  agentAvatar: ''
})
const qrcode = ref('')
const painterRef = ref(null)


// 接收页面参数
onLoad(async (options) => {
  console.log('接收到的参数:', options)
  if (options && options.params) {
    try {
      const params = JSON.parse(decodeURIComponent(options.params))
      console.log('params', params)
      // 设置智能体数据
      agentDetail = Object.assign(agentDetail, {
        agentName: (params.agentName && params.agentName.trim()) || '智能体名称',
        agentDesc: (params.agentDesc && params.agentDesc.trim()) || '智能体描述',
        agentAvatar: (params.agentAvatar && params.agentAvatar.trim()) || ''
      })

      qrcode.value = (params.qrcode && params.qrcode.trim()) || ''

      console.log('agentDetail', agentDetail)

      // 等待下一个 tick 确保数据更新完成
      await nextTick()

      // 数据准备完成，可以开始渲染
      dataReady.value = true

    } catch (error) {
      console.log('error', error)
      // 即使出错也要设置默认数据并允许渲染
      Object.assign(agentDetail, {
        agentName: '智能体名称',
        agentDesc: '智能体描述',
        agentAvatar: ''
      })
      dataReady.value = true
    }
  } else {
    console.log('没有参数，使用默认配置')
    // 使用默认数据
    Object.assign(agentDetail, {
      agentName: '智能体名称',
      agentDesc: '智能体描述',
      agentAvatar: ''
    })
    dataReady.value = true
  }
})


// 下载海报
const downloadPoster = () => {
  console.log('开始下载海报')
  // 保存到相册
  uni.saveImageToPhotosAlbum({
    filePath: path.value,
    success: () => {
      uni.showToast({
        title: '保存成功',
        icon: 'success'
      })
    },
    fail: (error) => {
      console.error('保存失败:', error)
      uni.showToast({
        title: '保存失败',
        icon: 'none'
      })
    }
  })

}

</script>

<style scoped>
.share-container {
  min-height: 100vh;
  background: #f8f9fa;
  padding: 40rpx 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.poster-preview {
  width: 600rpx;
  min-height: 835rpx;
  background: #ffffff;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.poster-image {
  width: 100%;
  border-radius: 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #999999;
  text-align: center;
}

.download-btn {
  width: 500rpx;
  height: 88rpx;
  background: linear-gradient(135deg, #3478f6 0%, #4a90e2 100%);
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 20rpx rgba(52, 120, 246, 0.3);
}

.download-btn:active {
  transform: scale(0.98);
  opacity: 0.9;
}

.download-text {
  font-size: 32rpx;
  color: #ffffff;
  font-weight: 600;
}

.painter-canvas {
  position: fixed;
  top: -9999rpx;
  left: -9999rpx;
  width: 600rpx;
  height: 835rpx;
  z-index: -1;
}
</style>