/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.video-create-page.data-v-979d1c47 {
  background: #141115;
  min-height: 100vh;
  padding: 32rpx;
  box-sizing: border-box;
}
.video-create-page .video-preview-container .video-preview-card.data-v-979d1c47 {
  background: #141215;
  border-radius: 24rpx;
  padding: 0;
  margin-bottom: 32rpx;
  position: relative;
  overflow: hidden;
}
.video-create-page .video-preview-container .video-preview-card .digital-person-image.data-v-979d1c47 {
  width: 100%;
}
.video-create-page .video-preview-container .video-preview-card .digital-person-image .person-box.data-v-979d1c47 {
  width: 390rpx;
  height: 690rpx;
  margin: auto;
  position: relative;
}
.video-create-page .video-preview-container .video-preview-card .digital-person-image .person-box .person-avatar.data-v-979d1c47 {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.video-create-page .video-preview-container .video-preview-card .digital-person-image .person-box .video-tips.data-v-979d1c47 {
  position: absolute;
  bottom: 80rpx;
  right: 0;
  left: 0;
  text-align: center;
  color: white;
  font-size: 30rpx;
  font-weight: 500;
  /* 文字描边效果 */
  text-shadow: -1px -1px 0 #000, 1px -1px 0 #000, -1px 1px 0 #000, 1px 1px 0 #000, -2px 0 0 #000, 2px 0 0 #000, 0 -2px 0 #000, 0 2px 0 #000;
  /* 或者使用webkit的描边属性（备选方案） */
}
.video-create-page .video-preview-container .video-preview-card .digital-person-image .person-box .change-btn.data-v-979d1c47 {
  position: absolute;
  bottom: 14rpx;
  right: 14rpx;
  padding: 7rpx 14rpx;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  font-size: 24rpx;
  font-weight: 400;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.video-create-page .video-preview-container .video-preview-card .digital-person-image .person-box .change-btn .change-icon.data-v-979d1c47 {
  width: 30rpx;
  height: 30rpx;
}
.video-create-page .video-preview-container .video-preview-card .digital-person-image .video-controls.data-v-979d1c47 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 30rpx auto;
}
.video-create-page .video-preview-container .video-preview-card .digital-person-image .video-controls .video-duration.data-v-979d1c47 {
  color: #ffffff;
  font-size: 26rpx;
}
.video-create-page .video-preview-container .video-preview-card .digital-person-image .video-controls .subtitle-checkbox.data-v-979d1c47 {
  display: flex;
  align-items: center;
}
.video-create-page .video-preview-container .video-preview-card .digital-person-image .video-controls .subtitle-checkbox .subtitle-icon.data-v-979d1c47 {
  width: 30rpx;
  height: 30rpx;
  margin-right: 8rpx;
}
.video-create-page .video-preview-container .video-preview-card .digital-person-image .video-controls .subtitle-checkbox .subtitle-text.data-v-979d1c47 {
  color: #ffffff;
  font-size: 26rpx;
}
.video-create-page .video-preview-container .video-preview-card .text-content-area.data-v-979d1c47 {
  background: #212121;
  border-radius: 25rpx;
  padding: 32rpx;
  margin: 0;
  display: flex;
  flex-direction: column;
}
.video-create-page .video-preview-container .video-preview-card .text-content-area .content-textarea.data-v-979d1c47 {
  font-size: 28rpx;
  font-weight: 400;
  line-height: 1.6;
  color: #fff;
  width: 100%;
  height: 296rpx;
  border: none;
  outline: none;
  resize: none;
  background: transparent;
}
.video-create-page .video-preview-container .video-preview-card .text-content-area .bottom-actions.data-v-979d1c47 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 20rpx;
}
.video-create-page .video-preview-container .video-preview-card .text-content-area .bottom-actions .action-btn.data-v-979d1c47 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8rpx 14rpx;
  background: #333333;
  border-radius: 12rpx;
  color: white;
}
.video-create-page .video-preview-container .video-preview-card .text-content-area .bottom-actions .action-btn.data-v-979d1c47:active {
  background: #e9ecef;
}
.video-create-page .video-preview-container .video-preview-card .text-content-area .bottom-actions .action-btn .action-icon.data-v-979d1c47 {
  width: 30rpx;
  height: 30rpx;
  margin-right: 5rpx;
}
.video-create-page .video-preview-container .video-preview-card .text-content-area .bottom-actions .action-btn .action-text.data-v-979d1c47 {
  font-size: 24rpx;
  color: white;
  font-weight: 400;
}
.video-create-page .video-preview-container .generate-btn.data-v-979d1c47 {
  background: #3478f6;
  border-radius: 48rpx;
  padding: 32rpx;
  text-align: center;
}
.video-create-page .video-preview-container .generate-btn .generate-text.data-v-979d1c47 {
  font-size: 32rpx;
  color: #ffffff;
  font-weight: 600;
}
.video-create-page .video-overlay.data-v-979d1c47 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: flex-end;
  justify-content: center;
  z-index: 9999;
}
.video-create-page .video-modal.data-v-979d1c47 {
  background: #232325;
  border-radius: 32rpx 32rpx 0 0;
  padding: 48rpx 30rpx 32rpx;
  width: 100%;
  max-height: 80vh;
  position: relative;
  color: #ffffff;
}
.video-create-page .video-modal .video-header.data-v-979d1c47 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
  padding: 0 16rpx;
  position: relative;
}
.video-create-page .video-modal .video-header .primary-tabs.data-v-979d1c47 {
  display: flex;
  align-items: center;
}
.video-create-page .video-modal .video-header .primary-tabs .primary-tab.data-v-979d1c47 {
  margin-right: 32rpx;
  padding-bottom: 18rpx;
}
.video-create-page .video-modal .video-header .primary-tabs .primary-tab.active .primary-tab-text.data-v-979d1c47 {
  color: #ffffff;
  font-weight: 500;
  border-bottom: 3rpx solid #ffffff;
}
.video-create-page .video-modal .video-header .primary-tabs .primary-tab .primary-tab-text.data-v-979d1c47 {
  font-size: 30rpx;
  font-weight: 400;
  color: #999999;
  transition: color 0.3s ease;
  padding-bottom: 18rpx;
}
.video-create-page .video-modal .video-header .confirm-icon-wrapper.data-v-979d1c47 {
  position: absolute;
  right: 16rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.video-create-page .video-modal .video-header .confirm-icon-wrapper .confirm-icon.data-v-979d1c47 {
  width: 60rpx;
  height: 60rpx;
}
.video-create-page .video-modal .template-scroll.data-v-979d1c47 {
  height: 800rpx;
  padding: 0 16rpx;
}
.video-create-page .video-modal .empty-state.data-v-979d1c47 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 600rpx;
  padding: 40rpx;
}
.video-create-page .video-modal .empty-state .empty-icon-placeholder.data-v-979d1c47 {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 60rpx;
}
.video-create-page .video-modal .empty-state .empty-icon-placeholder .empty-icon-text.data-v-979d1c47 {
  font-size: 60rpx;
  opacity: 0.6;
}
.video-create-page .video-modal .empty-state .empty-text.data-v-979d1c47 {
  font-size: 32rpx;
  color: #666666;
  margin-bottom: 12rpx;
  font-weight: 500;
}
.video-create-page .video-modal .load-more.data-v-979d1c47 {
  padding: 32rpx 0;
  text-align: center;
}
.video-create-page .video-modal .load-more .loading-text.data-v-979d1c47 {
  font-size: 28rpx;
  color: #999999;
}
.video-create-page .video-modal .video-grid.data-v-979d1c47 {
  display: flex;
  flex-wrap: wrap;
  align-content: flex-start;
}
.video-create-page .video-modal .video-grid .video-template.data-v-979d1c47 {
  width: 220rpx;
  height: 350rpx;
  border-radius: 16rpx;
  overflow: hidden;
  border: 4rpx solid transparent;
  transition: border-color 0.3s ease;
  margin-bottom: 16rpx;
  box-sizing: border-box;
}
.video-create-page .video-modal .video-grid .video-template.selected.data-v-979d1c47 {
  border-color: #3478f6;
}
.video-create-page .video-modal .video-grid .video-template .template-image.data-v-979d1c47 {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.video-create-page .video-modal .video-grid .video-template.data-v-979d1c47:not(:nth-child(3n)) {
  margin-right: 12rpx;
}
.video-create-page .video-modal .close-icon-wrapper.data-v-979d1c47 {
  position: absolute;
  bottom: -80rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 45rpx;
  height: 45rpx;
  border-radius: 50%;
}
.video-create-page .video-modal .close-icon-wrapper .close-icon.data-v-979d1c47 {
  width: 45rpx;
  height: 45rpx;
}
.video-create-page .voice-overlay.data-v-979d1c47 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: flex-end;
  justify-content: center;
  z-index: 9999;
}
.video-create-page .voice-modal.data-v-979d1c47 {
  background: #232325;
  border-radius: 32rpx 32rpx 0 0;
  padding: 0;
  width: 100%;
  max-height: 80vh;
  position: relative;
  color: #ffffff;
}
.video-create-page .voice-modal .voice-header.data-v-979d1c47 {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 32rpx 30rpx 28rpx;
  position: relative;
  border-bottom: 2rpx solid #2A2A2A;
}
.video-create-page .voice-modal .voice-header .voice-title.data-v-979d1c47 {
  font-size: 30rpx;
  font-weight: 500;
  color: #ffffff;
}
.video-create-page .voice-modal .voice-header .confirm-icon-wrapper.data-v-979d1c47 {
  position: absolute;
  right: 30rpx;
  bottom: 8rpx;
  width: 60rpx;
  height: 60rpx;
}
.video-create-page .voice-modal .voice-header .confirm-icon-wrapper .confirm-icon.data-v-979d1c47 {
  width: 100%;
  height: 100%;
  display: block;
}
.video-create-page .voice-modal .voice-content.data-v-979d1c47 {
  padding: 32rpx 30rpx;
}
.video-create-page .voice-modal .voice-content .voice-tip.data-v-979d1c47 {
  margin-bottom: 32rpx;
}
.video-create-page .voice-modal .voice-content .voice-tip .tip-text.data-v-979d1c47 {
  font-size: 24rpx;
  color: #999999;
  font-weight: 400;
}
.video-create-page .voice-modal .voice-tabs.data-v-979d1c47 {
  display: flex;
  margin-bottom: 32rpx;
}
.video-create-page .voice-modal .voice-tabs .voice-tab.data-v-979d1c47 {
  background: #333333;
  border-radius: 32rpx;
  padding: 6rpx 18rpx;
  margin-right: 16rpx;
}
.video-create-page .voice-modal .voice-tabs .voice-tab.active.data-v-979d1c47 {
  background: #585858;
  border: 2rpx solid #8D8D8D;
}
.video-create-page .voice-modal .voice-tabs .voice-tab.active .tab-text.data-v-979d1c47 {
  font-weight: 500;
  font-size: 30rpx;
}
.video-create-page .voice-modal .voice-tabs .voice-tab .tab-text.data-v-979d1c47 {
  font-weight: 400;
  font-size: 26rpx;
  color: #ffffff;
}
.video-create-page .voice-modal .voice-grid.data-v-979d1c47 {
  display: flex;
  align-content: flex-start;
  flex-wrap: wrap;
  height: 600rpx;
  overflow-y: auto;
}
.video-create-page .voice-modal .voice-grid .voice-item.data-v-979d1c47 {
  background: #333333;
  border-radius: 16rpx;
  padding: 20rpx 6rpx;
  text-align: center;
  border: 4rpx solid transparent;
  transition: all 0.3s ease;
  box-sizing: border-box;
  width: 120rpx;
  height: 120rpx;
  overflow: hidden;
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}
.video-create-page .voice-modal .voice-grid .voice-item.selected.data-v-979d1c47 {
  border-color: #3478f6;
}
.video-create-page .voice-modal .voice-grid .voice-item.selected .voice-name.data-v-979d1c47 {
  color: #AFC6FF;
}
.video-create-page .voice-modal .voice-grid .voice-item .voice-name.data-v-979d1c47 {
  font-size: 24rpx;
  color: #ffffff;
  font-weight: 400;
  word-break: break-all;
}
.video-create-page .voice-modal .voice-grid .voice-item.data-v-979d1c47:not(:nth-child(5n)) {
  margin-right: 22rpx;
}
.video-create-page .privacy-overlay.data-v-979d1c47 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}
.video-create-page .privacy-modal.data-v-979d1c47 {
  background: #ffffff;
  border-radius: 24rpx;
  width: 600rpx;
  max-width: 90vw;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
.video-create-page .privacy-modal .privacy-header.data-v-979d1c47 {
  padding: 40rpx 40rpx 20rpx;
  text-align: center;
}
.video-create-page .privacy-modal .privacy-header .privacy-title.data-v-979d1c47 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}
.video-create-page .privacy-modal .privacy-content.data-v-979d1c47 {
  flex: 1;
  padding: 20rpx 40rpx 30rpx;
  max-height: 600rpx;
  box-sizing: border-box;
  overflow: hidden;
}
.video-create-page .privacy-modal .privacy-content .privacy-rich-content .privacy-rich-text.data-v-979d1c47 {
  font-size: 28rpx;
  line-height: 1.6;
  color: #666666;
  word-break: break-word;
}
.video-create-page .privacy-modal .privacy-footer.data-v-979d1c47 {
  padding: 30rpx 40rpx 40rpx;
}
.video-create-page .privacy-modal .privacy-footer .privacy-btn.data-v-979d1c47 {
  background: #3478f6;
  border-radius: 48rpx;
  padding: 24rpx;
  text-align: center;
  transition: all 0.3s ease;
}
.video-create-page .privacy-modal .privacy-footer .privacy-btn.disabled.data-v-979d1c47 {
  background: #cccccc;
  cursor: not-allowed;
}
.video-create-page .privacy-modal .privacy-footer .privacy-btn .privacy-btn-text.data-v-979d1c47 {
  font-size: 30rpx;
  color: #ffffff;
  font-weight: 500;
}