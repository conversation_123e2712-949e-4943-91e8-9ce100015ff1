<template>
	<view class="profile-page">
		<!-- 顶部用户信息 -->
		<view class="header">
			<view class="user-info">
				<image class="avatar" :src="userInfo.headImgUrl" mode="aspectFill" />
				<view class="info">
					<text class="name">{{ userInfo.nickname || '未设置昵称' }}</text>
					<text class="email">{{ userInfo.email || '未设置邮箱' }}</text>
				</view>
			</view>
			<view class="edit-btn" @tap="handleEditProfile">
				<!-- <image src="@/static/my/<EMAIL>" class="edit-icon" mode="aspectFit" /> -->
				<text class="edit-text">编辑资料</text>
			</view>
		</view>
		<!-- 会员卡 -->
		<view class="vip-card-warp">
			<view class="vip-card">
				<view class="info-box">
					<view class="title" v-if="!vipInfo.hasMembership">成为思链AI会员</view>
					<view class="title" v-else>已激活思链AI会员<text class="day">({{ vipInfo.remainingDays }}天)</text>
					</view>
					<view class="label" v-if="!vipInfo.hasMembership">让AI成为你的IP合伙人</view>
					<view class="label" v-else>让AI成为你的IP合伙人</view>
					<view class="icon-box" @click="handleOpenVip">
						<image class="icon" :src="vipIcon"></image>
						<view class="text" v-if="!vipInfo.hasMembership">开通会员</view>
						<view class="text" v-else>续费会员</view>
					</view>
				</view>
				<view class="dy-box">
					<view class="left">
						<image class="icon" src="@/static/my/vip-icon.png" mode="aspectFit"></image>
						<text class="sub-text">AI点数:{{ vipInfo.userPoints.totalPointsText }}</text>
					</view>
				</view>
				<!-- <view class="dy-box">
					<view class="left">
						<image class="icon" src="@/static/my/vip-icon.png" mode="aspectFit"></image>
						<text class="sub-text" @click="handleOpenSub">专属订阅({{ vipInfo.creatorSubscriptionCount }})</text>
					</view>
					<view class="right" @click="handleOpenSub">去订阅<image class="icon" src="@/static/my/right-arrow-icon.png">
						</image>
					</view>
				</view> -->
			</view>
		</view>
		<!-- Tab 切换栏 -->
		<view class="tab-container">
			<view class="tab-bar">
				<view v-for="(tab, idx) in tabs" :key="tab" :class="['tab', { active: activeTab === idx }]"
					@click="activeTab = idx">
					<text class="tab-text">{{ tab }}</text>
				</view>
			</view>
		</view>
		<!-- Tab 内容区 -->
		<view class="tab-content">
			<view v-if="activeTab === 0" class="agents-tab">
				<!-- 我的智能体 -->
				<view class="agents-list">
					<view class="create-agent">
						<view class="create-btn" @tap="handleCreateAgent">
							<image src="@/static/msg/<EMAIL>" class="create-icon" mode="aspectFit" />
							<text class="create-text">创建AI智能体</text>
						</view>
					</view>
					<view class="agent-card" v-for="(agent, index) in agentList" :key="index">
						<image class="agent-avatar" :src="agent.agentAvatar" mode="aspectFill" />
						<view class="agent-info" @click="handleAgentClick(agent)">
							<view class="agent-title">{{ agent.agentName }}</view>
							<text class="agent-desc">{{ agent.agentDesc }}</text>
							<view class="agent-tags">
								<!-- <view v-for="tag in agent.tags" :key="tag" class="tag primary">
										{{ tag.text }}
									</view> -->
								<view class="tag primary">
									{{agentTypes.find(item => item.value === agent.agentType).label}}
								</view>
								<view class="tag primary">
									{{ agent.priceText }}
								</view>
								<view class="tag primary">
									{{ agent.isPublicText }}
								</view>
							</view>
						</view>
						<view class="operate-box">
							<view class="edit" @click="handleEditAgent(agent)">编辑</view>
							<view class="delete" @click="handleDeleteAgent(agent)">删除</view>
						</view>
						<view class="status" v-if="agent.auditStatus != 2">{{ auditStatus[agent.auditStatus] }}</view>
						<view class="status success" v-if="agent.auditStatus === 2">{{ auditStatus[agent.auditStatus] }}</view>
					</view>
				</view>

				<!-- 查看更多按钮 -->
				<view class="view-more-btn" @tap="handleViewMoreAgents" v-if="agentList.length > 0">
					<text class="view-more-text">查看更多</text>
					<image src="@/static/index/<EMAIL>" class="arrow-icon" mode="aspectFit" />
				</view>


			</view>
			<view v-else-if="activeTab === 1" class="income-tab">
				<!-- 共创收益 -->
				<view class="income-content">
					<!-- 共创规则 -->
					<view class="rules-section">
						<text class="section-title">共创规则</text>
						<view class="rules-grid">
							<view v-for="(rule, index) in rulesList" :key="index" class="rule-item" @click="handleRuleClick(index)">
								<image :src="rule.icon" class="rule-icon" mode="aspectFit" />
								<text class="rule-text">{{ rule.name }}</text>
							</view>
						</view>
					</view>

					<!-- 我的收益 -->
					<view class="my-income-section">
						<text class="section-title">我的收益</text>
						<view class="income-card">
							<view class="income-amount">
								<text class="amount-value">{{ incomeData.availableAmountText }}</text>
								<text class="amount-unit">可用余额(元)</text>
							</view>
							<view class="withdraw-btn" @tap="handleWithdraw">
								<text class="withdraw-text">提现</text>
							</view>
						</view>
					</view>

					<!-- 收益总览 -->
					<view class="overview-section">
						<text class="section-title">收益总览</text>
						<view class="overview-grid">
							<view class="overview-item">
								<text class="overview-value">{{ incomeData.totalEarningsText }}</text>
								<text class="overview-label">总收益(元)</text>
							</view>
							<view class="overview-item">
								<text class="overview-value">{{ incomeData.totalWithdrawnText }}</text>
								<text class="overview-label">总提现(元)</text>
							</view>
						</view>
					</view>

					<!-- 收益流水 -->
					<view class="flow-section">
						<text class="section-title">收益流水</text>
						<view class="flow-buttons">
							<view class="flow-btn" @tap="handleWithdrawRecord">
								<image src="@/static/my/<EMAIL>" class="flow-icon" mode="aspectFit" />
								<text class="flow-text">提现记录</text>
							</view>
							<view class="flow-btn" @tap="handleFinanceFlow">
								<image src="@/static/my/<EMAIL>" class="flow-icon" mode="aspectFit" />
								<text class="flow-text">财务流水</text>
							</view>
						</view>
					</view>
				</view>
			</view>
			<view v-else class="workspace-tab">
				<!-- 工作台二级Tab -->
				<view class="workspace-tab-container">
					<view class="workspace-tab-bar">
						<view v-for="(tab, idx) in workspaceTabs" :key="tab"
							:class="['workspace-tab-item', { active: activeWorkspaceTab === idx }]"
							@click="activeWorkspaceTab = idx">
							<view class="tab-button">
								<text class="workspace-tab-text">{{ tab }}</text>
							</view>
						</view>
					</view>
				</view>

				<!-- 工作台内容区 -->
				<view class="workspace-content">
					<!-- 我的收藏 -->
					<view v-if="activeWorkspaceTab === 0" class="favorites-content">
						<view class="favorite-card" v-for="(item, index) in favoritesList" :key="index">
							<view class="favorite-header">
								<text class="favorite-date">{{ item.collectTime }}</text>
							</view>
							<view class="favorite-content">
								<text class="content-text">{{ item.contentPreview }}</text>
							</view>
							<view class="favorite-actions">
								<view class="action-btn" @tap="handleCopy(item)">
									<image src="@/static/my/<EMAIL>" class="action-icon" mode="aspectFit" />
									<text class="action-text">复制</text>
								</view>
								<view class="action-btn" @tap="handleViewAll(item)">
									<image src="@/static/my/<EMAIL>" class="action-icon" mode="aspectFit" />
									<text class="action-text">查看全部</text>
								</view>
								<view class="action-btn" @tap="handleUnfavorite(item)">
									<image src="@/static/my/<EMAIL>" class="action-icon" mode="aspectFit" />
									<text class="action-text">取消收藏</text>
								</view>
							</view>
						</view>

						<!-- 查看更多按钮 -->
						<view class="view-more-btn" @tap="handleViewMoreFavorites" v-if="favoritesList.length > 0">
							<text class="view-more-text">查看更多</text>
							<image src="@/static/index/<EMAIL>" class="arrow-icon" mode="aspectFit" />
						</view>

						<view class="empty-state" v-if="favoritesList.length === 0">
							<text class="empty-text">暂无收藏的内容</text>
						</view>
					</view>

					<!-- 我的数字人 -->
					<view v-else class="digital-person-content">
						<!-- 剩余算力 -->
						<view class="computing-power">
							<view class="power-info">
								<image class="power-icon" src="@/static/my/desk_balance_icon.png" mode="aspectFit" />
								<text class="power-text">剩余算力: <text class="power-text_number">{{userInfo.chat_count}}</text></text>
							</view>
							<view class="recharge-btn" @tap="openRechargeModal">去充值</view>
						</view>
						<!-- 数字人IP形象卡片 -->
						<view class="digital-person-banner" @tap="goToMyFigure">
							<view class="banner-content">
								<image class="banner-image banner-image-radius" src="@/static/my/desk_banner.png" mode="aspectFit" />
							</view>
						</view>

						<!-- 功能区域 -->
						<view class="function-area">
							<view class="function-row">
								<view class="function-item" @tap="goToMyVideoCreate">
									<view class="function-info">
										<text class="function-title">创建视频</text>
										<text class="function-desc">一键开启视频创作</text>
									</view>
									<image class="function-icon" src="@/static/my/desk_works-left.png" mode="aspectFit" />
								</view>
								<view class="function-item" @tap="goToMyWorks">
									<view class="function-info">
										<text class="function-title">我的作品</text>
										<text class="function-desc">生成专属身份</text>
									</view>
									<image class="function-icon" src="@/static/my/desk_works-right.png" mode="aspectFit" />
								</view>
							</view>
						</view>

						
					</view>
				</view>
			</view>
		</view>

		<!-- 自定义充值弹窗 -->
		<view v-if="showRechargeModal" class="recharge-overlay">
			<view class="recharge-modal" @tap.stop>
				<view class="modal-header">
					<text class="modal-title">充值点数</text>
				</view>

				<view class="recharge-grid" v-if="rechargeOptions.length > 0">
					<view
						v-for="(option, index) in rechargeOptions"
						:key="option.guid || index"
						class="recharge-item"
						:class="{ active: selectedOption === index }"
						@tap="selectOption(index)"
					>
						<text class="item-count">{{ option.goodsName || `${option.count}次` }}</text>
						<text class="item-price">¥{{ option.price }}</text>
					</view>
				</view>

				<view v-else class="no-data">
					<text class="no-data-text">暂无数据</text>
				</view>

				<view class="payment-section">
					<text class="payment-text">付款金额：</text>
					<text class="payment-price">¥{{ selectedPrice || '50.00' }}</text>
				</view>

				<view class="pay-button" @tap="confirmRecharge">
					<text class="pay-text">确认充值</text>
				</view>

				<view class="close-icon-wrapper" @tap="closeRechargeModal">
					<image class="close-icon" src="/static/my/popup-close.png" mode="aspectFit"></image>
				</view>
			</view>
		</view>

		<!-- 查看全部弹窗 -->
		<view v-if="showContentModal" class="modal-overlay" @tap="closeContentModal" catchtouchmove="true">
			<view class="modal-content" @tap.stop>
				<view class="modal-header">
					<text class="modal-title">消息详情</text>
					<view class="close-btn" @tap="closeContentModal">
						<text class="close-text">×</text>
					</view>
				</view>
				<scroll-view scroll-y="true" class="modal-body" show-scrollbar="false">
					<view class="rich-content">
						<rich-text :nodes="currentContent"></rich-text>
					</view>
				</scroll-view>
			</view>
		</view>
	</view>
</template>

<script setup>

import { reactive, ref, watch, computed } from 'vue'
import { onShow, onLoad } from '@dcloudio/uni-app';
import { getUserInfoApi, getMyAgentListApi, getMyCollectionListApi, cancelCollectMessageApi, getUserVipInfoApi, bindInvitationApi, deleteAgentApi, getMyEarningsApi, getChatGoodsApi, buyChatGoodsApi, queryPayChatStautsApi } from '@/api/index.js'
import {
	useUserStore
} from '@/stores/user.js';
import { miniPay } from '@/api/common.js';

const vipIcon = 'https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/c4d5b3ada53740d5b862f274ce48ef0c.png'

const userStore = useUserStore()
const tabs = ['我的智能体', '共创收益', '我的工作台']
const activeTab = ref(0)

// 工作台子Tab
const workspaceTabs = ['我的收藏', '我的数字人']
const activeWorkspaceTab = ref(1) // 默认选中"我的数字人"

// 充值相关
const selectedOption = ref(0)
const rechargeOptions = ref([])

const selectedPrice = computed(() => {
	return rechargeOptions.value[selectedOption.value]?.price || '0.00'
})

// 获取聊天点数商品列表
const getChatGoods = async () => {
	try {
		let res = await getChatGoodsApi()
		if (res.code === 0 && res.data) {
			rechargeOptions.value = res.data
		} else {
			rechargeOptions.value = []
		}
	} catch (error) {
		console.error('获取聊天点数商品列表失败:', error)
		rechargeOptions.value = []
	}
}
const agentTypes = ref([
	{ label: '内部', value: 1 },
	{ label: 'dify', value: 2 },
	{ label: 'coze', value: 3 },
	{ label: '阿里云百炼', value: 4 }
])

//审批状态：1-待审核；2-审核通过；3-审核拒绝
const auditStatus = {
	1: '待审核',
	2: '审核通过',
	3: '审核拒绝'
}
// 智能体列表数据
const agentList = ref([])
const getMyAgentList = async () => {
	let res = await getMyAgentListApi({
		merchantGuid: userStore.merchantGuid
	})
	agentList.value = res.data.data
}
import rule1 from '@/static/my/<EMAIL>'
import rule2 from '@/static/my/<EMAIL>'
import rule3 from '@/static/my/<EMAIL>'
// 共创规则数据
const rulesList = ref([
	{ name: '推广规则', icon: rule1 },
	{ name: '创作规则', icon: rule2 },
	{ name: '平台规则', icon: rule3 }
])

// 收益数据
const incomeData = ref({
	availableAmount: "0.00",
	availableAmountText: "¥0.00",
	frozenAmount: "0.00",
	frozenAmountText: "¥0.00",
	totalEarnings: "0.00",
	totalEarningsText: "¥0.00",
	totalWithdrawn: "0.00",
	totalWithdrawnText: "¥0.00"
})

// 收藏列表数据
const favoritesList = ref([])
let collectReq = reactive({
	merchantGuid: userStore.merchantGuid,
	page: 1,
	pageSize: 10
})

// 弹窗相关状态
const showContentModal = ref(false)
const currentContent = ref('')
const showRechargeModal = ref(false)

const getMyCollectionList = async () => {
	try {
		let res = await getMyCollectionListApi(collectReq)
		favoritesList.value = res.data.list
	} catch (error) {
		console.error('获取收藏列表失败:', error)
		uni.showToast({
			title: '加载失败',
			icon: 'none'
		})
	}
}
// 事件处理函数
const handleEditProfile = () => {
	uni.navigateTo({
		url: '/pages/profile/index'
	})
}
//进入聊天页
const handleAgentClick = (agent) => {
	uni.navigateTo({
		url: `/pages/msg/index?sessionGuid=${agent.guid}`
	})
}
const handleOpenVip = () => {
	uni.navigateTo({
		url: '/pages/my/vip-list'
	})
}

const handleOpenSub = () => {
	uni.navigateTo({
		url: '/pages/my/sub-list'
	})
}

const handleCreateAgent = () => {
	uni.navigateTo({
		url: '/pages/create-agent/index'
	})
}

const handleEditAgent = (agent) => {
	uni.navigateTo({
		url: `/pages/create-agent/index?guid=${agent.guid}`
	})
}
const handleDeleteAgent = async (agent) => {
	uni.showModal({
		title: '提示',
		content: '确定要删除该智能体吗？',
		success: async (res) => {
			if (res.confirm) {
				await deleteAgentApi({
					merchantGuid: userStore.merchantGuid,
					agentGuid: agent.guid
				})
				uni.showToast({
					title: '删除成功',
					icon: 'success'
				})
				getMyAgentList()
			}
		}
	})

}
const handleRuleClick = (index) => {
	if (index === 0) {
		uni.navigateTo({
			url: '/pages/rule/tg-index'
		})
	}
	if (index === 1) {
		uni.navigateTo({
			url: '/pages/rule/cz-index'
		})
	}
	if (index === 2) {
		uni.navigateTo({
			url: '/pages/rule/pt-index'
		})
	}

}

const handleWithdraw = () => {
	console.log('提现')
}

const handleWithdrawRecord = () => {
	uni.navigateTo({
		url: '/pages/raw-record/index'
	})
}

const handleFinanceFlow = () => {
	uni.navigateTo({
		url: '/pages/finance/index'
	})
}

const handleCopy = (item) => {
	uni.setClipboardData({
		data: item.messageContent,
		success() {
			uni.showToast({
				title: '复制成功',
				icon: 'none'
			})
		}
	})
}

const handleViewAll = (item) => {
	currentContent.value = item.messageContent || ''
	showContentModal.value = true
}

const closeContentModal = () => {
	showContentModal.value = false
	currentContent.value = ''
}

// 充值相关方法
const openRechargeModal = async () => {
	await getChatGoods() // 打开弹窗时获取最新的商品列表
	showRechargeModal.value = true
}

const closeRechargeModal = () => {
	showRechargeModal.value = false
}

const selectOption = (index) => {
	selectedOption.value = index
}

const confirmRecharge = async () => {
	const option = rechargeOptions.value[selectedOption.value]
	if (!option) {
		uni.showToast({
			title: '请选择充值选项',
			icon: 'none'
		})
		return
	}

	try {
		uni.showLoading({
			title: '正在创建订单...',
			mask: true
		})
		
		// 调用购买聊天点数接口
		const payInfo = await buyChatGoodsApi({
			chatGoodsGuid: option.guid,
			payEnv: 'xcx'
		})

		uni.hideLoading()
		// 调用微信支付
		miniPay(payInfo.data).then(
			async () => {
				queryPayChatStatus(payInfo.data.orderNo, 0)
			},
			(res) => {
				uni.showToast({
					title: res.msg || '支付失败',
					icon: 'none'
				})
			}
		)
	} catch (error) {
		uni.hideLoading()
		uni.showToast({
			title: error.message || '创建订单失败',
			icon: 'none'
		})
	}
}

// 查询聊天点数支付状态
const queryPayChatStatus = async (orderNo, number) => {
	number++
	try {
		const orderInfo = await queryPayChatStautsApi({
			orderNo
		})
		if (orderInfo.data.isPay) {
			uni.showToast({
				title: '充值成功',
				icon: 'success',
				duration: 3000
			})
			closeRechargeModal()
			// 刷新用户信息，更新点数显示
			if (userStore.userToken) {
				getVipInfo()
			}
		} else {
			setTimeout(() => {
				queryPayChatStatus(orderNo, number)
			}, 2000)
		}
	} catch (error) {
		uni.showToast({
			title: error.msg || '查询支付状态失败',
			icon: 'none'
		})
	}
}

// 跳转到我的作品页面
const goToMyWorks = () => {
	uni.navigateTo({
		url: '/pages/my/works-list'
	})
}
// 跳转到我的IP形象列表
const goToMyFigure = () => {
	uni.navigateTo({
		url: '/pages/my/figure-list'
	})
}

// 跳转到创建视频
const goToMyVideoCreate = () => {
	uni.navigateTo({
		url: '/pages/my/video-create'
	})
	return
	showVideoModal.value = true
	selectedTemplate.value = null // 重置选中状态
	activePrimaryTab.value = 0 // 重置到模板页面
}

const handleUnfavorite = async (item) => {
	await cancelCollectMessageApi({
		merchantGuid: userStore.merchantGuid,
		messageGuid: item.messageGuid
	})
	uni.showToast({
		title: '取消收藏成功',
		icon: 'none'
	})
	// 重新加载
	collectReq.page = 1
	getMyCollectionList()
}

// 查看更多智能体
const handleViewMoreAgents = () => {
	uni.navigateTo({
		url: '/pages/my/agent-list'
	})
}

// 查看更多收藏
const handleViewMoreFavorites = () => {
	uni.navigateTo({
		url: '/pages/my/favorite-list'
	})
}
let userInfo = reactive({
	headImgUrl: '',
	email: '',
	nickname: ''
})
const getUserInfo = async () => {
	let res = await getUserInfoApi()
	userInfo = Object.assign(userInfo, res.data)
}
//获取我的收益
const getMyEarnings = async () => {
	let res = await getMyEarningsApi({
		merchantGuid: userStore.merchantGuid
	})
	console.log(res.data, '---------res.data')
	incomeData.value = res.data
}
watch(
	() => userStore.userToken,
	(newValue, oldValue) => {
		if (newValue && oldValue === '') {
			getUserInfo()
			getMyAgentList()
			bindInvitation()
			getMyEarnings()
			getChatGoods() // 获取聊天点数商品列表
		}
	}
);
let invite = ref('')
const bindInvitation = async () => {
	try {
		console.log('invite.value', invite.value)
		await bindInvitationApi({
			merchantGuid: userStore.merchantGuid,
			invitationCode: invite.value
		})
	} catch (error) {
		console.error('绑定邀请码失败:', error)
	}
}
onLoad((params) => {
	if (params.invite) {
		invite.value = params.invite;
		if (userStore.userToken) {
			bindInvitation()
		}
	}
})
//获取会员信息
let vipInfo = reactive({
	hasMembership: false,
	isExpired: true,
	membership: null,
	remainingDays: 0,
	creatorSubscriptionCount: 0,
	categorySubscriptionCount: 0,
	userPoints: {
		totalPointsText: ''
	}

})
const getVipInfo = async () => {
	try {
		let res = await getUserVipInfoApi({
			merchantGuid: userStore.merchantGuid
		})
		vipInfo = Object.assign(vipInfo, res.data)
		console.log(res, '会员信息')
	} catch (error) {
		console.error('获取会员信息失败:', error)
		uni.showToast({
			title: '加载失败',
			icon: 'none'
		})
	}
}

onShow(() => {
	if (userStore.userToken) {
		getUserInfo()
		getMyAgentList()
		getMyCollectionList()
		getVipInfo()
		getMyEarnings()
		getChatGoods() // 获取聊天点数商品列表
	}
})
</script>

<style lang="scss" scoped>
.vip-card-warp {
	background-color: #ffffff;
	padding-bottom: 20px;
	margin-bottom: 32rpx;
}

.vip-card {
	width: 690rpx;
	height: 220rpx;
	margin: 0 auto 0 auto;
	background: linear-gradient(90deg, #F9F0E3 0%, #F7DFC1 100%);
	border-radius: 25rpx;
	border: 1rpx solid #F4E5C1;
	// overflow: hidden;
	display: flex;
	flex-direction: column;

	.info-box {
		display: flex;
		flex-direction: column;
		position: relative;
		flex: 1;
		justify-content: center;
		padding-left: 20px;

		.title {
			font-size: 30rpx;
			color: #54371A;
			margin-bottom: 10px;

			.day {
				font-size: 22rpx;
			}
		}

		.label {
			font-size: 22rpx;
			color: #9E876A;
		}

		.icon-box {
			position: absolute;
			right: 20px;
			top: -26px;

			.icon {
				width: 133rpx;
				height: 133rpx;
				display: block;
			}

			.text {
				width: 136rpx;
				height: 50rpx;
				display: flex;
				justify-content: center;
				align-items: center;
				font-size: 26rpx;
				color: #FDF0E0;
				background-color: #543718;
				position: absolute;
				right: 0;
				bottom: 0;
				border-radius: 33rpx;
			}
		}

	}

	.dy-box {
		height: 70rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 0 30rpx;
		color: #DECAAF;
		background: linear-gradient(90deg, #281905 0%, #533818 100%);
		border-bottom-right-radius: 25rpx;
		border-bottom-left-radius: 25rpx;

		.left {
			font-size: 22rpx;
			display: flex;
			align-items: center;

			.icon {
				display: block;
				width: 30rpx;
				height: 30rpx;
				margin-right: 6rpx;
			}
		}

		.right {
			font-size: 25rpx;
			display: flex;
			align-items: center;

			.icon {
				display: block;
				width: 25rpx;
				height: 25rpx;
				margin-left: 6rpx;
			}
		}
	}
}

.modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
	overflow: hidden;
	touch-action: none;

	.modal-content {
		background: #ffffff;
		width: 90%;
		height: 600px;
		border-radius: 24rpx;
		display: flex;
		flex-direction: column;
		overflow: hidden;

		.modal-header {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 32rpx;
			border-bottom: 1px solid #F0F0F0;
			flex-shrink: 0;

			.modal-title {
				font-size: 32rpx;
				font-weight: 600;
				color: #1a1a1a;
			}

			.close-btn {
				width: 48rpx;
				height: 48rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				border-radius: 50%;
				background: #F5F5F5;

				.close-text {
					font-size: 36rpx;
					color: #666666;
					line-height: 1;
				}
			}
		}

		.modal-body {
			flex: 1;
			height: 0;
			overflow: hidden;

			.rich-content {
				font-size: 28rpx;
				line-height: 1.6;
				color: #1a1a1a;
				word-break: break-all;
				padding: 32rpx;
			}
		}
	}
}

.profile-page {
	background: #F5F5F5;
	min-height: 100vh;

	.header {
		display: flex;
		//flex-direction: column;
		// align-items: center;
		justify-content: space-between;
		align-items: center;
		padding: 60rpx 32rpx 40rpx;
		background: #ffffff;


		.user-info {
			display: flex;
			//flex-direction: column;
			align-items: center;

			.avatar {
				width: 120rpx;
				height: 120rpx;
				border-radius: 50%;
			}

			.info {
				// text-align: center;
				display: flex;
				flex-direction: column;
				justify-content: center;
				margin-left: 10px;

				.name {
					font-size: 30rpx;
					font-weight: 600;
					color: #1a1a1a;
					margin-bottom: 8rpx;
					display: block;
				}

				.email {
					font-size: 28rpx;
					color: #999999;
					display: block;
				}
			}
		}

		.edit-btn {
			display: flex;
			align-items: center;
			justify-content: center;
			background: #F5F5F5;
			border-radius: 32rpx;
			border: none;
			width: 174rpx;
			height: 60rpx;

			// .edit-icon {
			// 	width: 28rpx;
			// 	height: 28rpx;
			// 	margin-right: 8rpx;
			// }

			.edit-text {
				font-size: 28rpx;
				color: #333;
				font-weight: 400;
			}
		}
	}

	.tab-container {
		background: #ffffff;
		border-bottom: 1px solid #F0F0F0;

		.tab-bar {
			display: flex;
			padding: 0 32rpx;

			.tab {
				flex: 1;
				text-align: center;
				padding: 32rpx 0;
				position: relative;

				.tab-text {
					font-size: 32rpx;
					color: #999999;
					font-weight: 400;
					transition: all 0.3s ease;
				}

				&.active {
					.tab-text {
						color: #1a1a1a;
						font-weight: 600;
					}

					&::after {
						content: '';
						position: absolute;
						bottom: 0;
						left: 50%;
						transform: translateX(-50%);
						width: 60rpx;
						height: 6rpx;
						background: #3478f6;
						border-radius: 3rpx;
					}
				}
			}
		}
	}

	.tab-content {
		// background: #ffffff;
		min-height: calc(100vh - 400rpx);
	}

	.agents-tab {
		padding: 32rpx;

		.agents-list {
			.agent-card {
				display: flex;
				align-items: center;
				background: #fff;
				border-radius: 24rpx;
				margin-bottom: 24rpx;
				padding: 32rpx;
				position: relative;

				.agent-avatar {
					width: 96rpx;
					height: 96rpx;
					border-radius: 50%;
					margin-right: 24rpx;
					flex-shrink: 0;
				}

				.agent-info {
					flex: 1;

					.agent-title {
						font-size: 32rpx;
						font-weight: 600;
						color: #222;
						margin-bottom: 8rpx;
					}

					.agent-desc {
						font-size: 24rpx;
						color: #999;
						line-height: 1.5;
						margin-bottom: 16rpx;
					}

					.agent-tags {
						display: flex;
						gap: 12rpx;
						flex-wrap: wrap;
						margin-top: 16rpx;

						.tag {
							border-radius: 16rpx;
							padding: 8rpx 16rpx;
							font-size: 22rpx;
							font-weight: 500;

							&.primary {
								background: #E6F0FF;
								color: #3478f6;
							}

							&.warning {
								background: #FFF7E6;
								color: #FF8C00;
							}

							&.success {
								background: #F0F9E6;
								color: #52C41A;
							}
						}
					}


				}

				.operate-box {
					display: flex;
					flex-direction: column;
					gap: 10px;

					.edit {
						width: 40px;
						height: 24px;
						font-size: 24rpx;
						background-color: #3478f6;
						color: #fff;
						display: flex;
						justify-content: center;
						align-items: center;
						border-radius: 16px;
					}

					.delete {
						width: 40px;
						height: 24px;
						font-size: 24rpx;
						background-color: #e60000;
						color: #fff;
						display: flex;
						justify-content: center;
						align-items: center;
						border-radius: 16px;
					}
				}

				.status {
					width: 140rpx;
					height: 40rpx;
					position: absolute;
					right: 0;
					top: 0;
					border-radius: 0rpx 30rpx 0rpx 30rpx;
					background-color: rgba(253, 141, 43, 0.12);
					font-size: 20rpx;
					color: #FD8D2B;
					text-align: center;
					line-height: 40rpx;

					&.success {
						background-color: #3478f6;
						color: #fff;
					}
				}
			}
		}

		.view-more-btn {
			display: flex;
			align-items: center;
			justify-content: center;
			//background: #F8F9FA;
			border-radius: 24rpx;
			padding: 24rpx;
			margin: 24rpx 0;
			//border: 1px solid #E6F0FF;

			.view-more-text {
				font-size: 28rpx;
				color: #3478f6;
				margin-right: 8rpx;
			}

			.arrow-icon {
				width: 24rpx;
				height: 24rpx;
			}
		}

		.create-agent {
			margin-bottom: 30rpx;
			display: flex;
			justify-content: center;
			// position: fixed;
			width: 100%;
			bottom: 40px;
			left: 0;

			.create-btn {
				display: flex;
				align-items: center;
				justify-content: center;
				width: 100%;
				height: 90rpx;
				background: #3478f6;
				border-radius: 48rpx;
				border: none;

				.create-icon {
					width: 32rpx;
					height: 32rpx;
					margin-right: 12rpx;
				}

				.create-text {
					font-size: 32rpx;
					color: #ffffff;
					line-height: 1;
				}
			}
		}
	}

	.income-tab {
		padding: 32rpx;

		.income-content {
			.section-title {
				font-size: 32rpx;
				font-weight: 600;
				color: #1a1a1a;
				margin-bottom: 24rpx;
				display: block;
			}

			.rules-section {
				margin-bottom: 40rpx;
				background-color: #fff;
				padding: 20rpx 10rpx;
				border: 30rpx;

				.rules-grid {
					display: flex;
					gap: 40rpx;
					justify-content: space-around;
					padding: 0 10rpx;

					.rule-item {
						display: flex;
						flex-direction: column;
						align-items: center;
						flex: 1;
						padding: 10px 0;
						background-color: #F9FAFB;
						border: 30rpx;

						.rule-icon {
							width: 64rpx;
							height: 64rpx;
							margin-bottom: 16rpx;
						}

						.rule-text {
							font-size: 26rpx;
							color: #666666;
						}
					}
				}
			}

			.my-income-section {
				margin-bottom: 40rpx;
				background-color: #fff;
				padding: 20rpx 10rpx;
				border: 30rpx;

				.income-card {
					background: #F8F9FA;
					border-radius: 24rpx;
					padding: 32rpx;
					display: flex;
					align-items: center;
					justify-content: space-between;
					margin: 0 10px;

					.income-amount {
						.amount-value {
							font-size: 48rpx;
							font-weight: 700;
							color: #1a1a1a;
							display: block;
							margin-bottom: 8rpx;
						}

						.amount-unit {
							font-size: 26rpx;
							color: #666666;
							display: block;
						}
					}

					.withdraw-btn {
						background: #3478f6;
						border-radius: 32rpx;
						padding: 20rpx 40rpx;
						border: none;

						.withdraw-text {
							font-size: 28rpx;
							color: #ffffff;
							font-weight: 600;
						}
					}
				}
			}

			.overview-section {
				margin-bottom: 40rpx;
				background-color: #fff;
				padding: 20rpx 10rpx;
				border: 30rpx;

				.overview-grid {
					display: flex;
					gap: 24rpx;
					margin: 0 10px;

					.overview-item {
						flex: 1;
						background: #F8F9FA;
						border-radius: 24rpx;
						padding: 32rpx 24rpx;
						text-align: center;

						.overview-value {
							font-size: 36rpx;
							font-weight: 700;
							color: #1a1a1a;
							display: block;
							margin-bottom: 8rpx;
						}

						.overview-label {
							font-size: 24rpx;
							color: #666666;
							display: block;
						}
					}
				}
			}

			.flow-section {
				background-color: #fff;
				padding: 20rpx 10rpx;
				border: 30rpx;

				.flow-buttons {
					display: flex;
					gap: 24rpx;
					margin: 0 10px;

					.flow-btn {
						flex: 1;
						background: #F8F9FA;
						border-radius: 24rpx;
						padding: 32rpx 24rpx;
						display: flex;
						// flex-direction: column;
						align-items: center;
						justify-content: center;
						border: none;

						.flow-icon {
							width: 48rpx;
							height: 48rpx;
							margin-right: 12rpx;
						}

						.flow-text {
							font-size: 26rpx;
							color: #666666;
						}
					}
				}
			}
		}
	}

	.workspace-tab {
		background: #F5F5F5;
		padding: 20rpx 30rpx;

		.workspace-tab-container {
			// padding: 32rpx 32rpx 0 32rpx;

			.workspace-tab-bar {
				display: flex;
				gap: 16rpx;

				.workspace-tab-item {
					.tab-button {
						padding: 16rpx 32rpx;
						border-radius: 40rpx;
						background: #ffffff;
						transition: all 0.3s ease;
						line-height: 1;

						.workspace-tab-text {
							font-size: 26rpx;
							color: #999999;
							font-weight: 400;
							transition: all 0.3s ease;
						}
					}

					&.active {
						.tab-button {
							background: #2B64F6;

							.workspace-tab-text {
								color: #ffffff;
								font-weight: 500;
							}
						}
					}
				}
			}
		}

		.workspace-content {
			padding-top: 20rpx;

			.favorites-content {
				.favorite-card {
					background: #fff;
					border-radius: 24rpx;
					margin-bottom: 24rpx;
					padding: 32rpx;

					.favorite-header {
						margin-bottom: 16rpx;

						.favorite-date {
							font-size: 24rpx;
							color: #999999;
							display: block;
						}
					}

					.favorite-content {
						margin-bottom: 24rpx;

						.content-text {
							font-size: 28rpx;
							color: #1a1a1a;
							line-height: 1.6;
							display: block;
						}
					}

					.favorite-actions {
						display: flex;
						gap: 16rpx;
						flex-wrap: wrap;

						.action-btn {
							display: flex;
							align-items: center;
							background: #E6F0FF;
							border-radius: 20rpx;
							padding: 12rpx 20rpx;
							border: none;
							flex-shrink: 0;
							min-width: 0;

							.action-icon {
								width: 38rpx;
								height: 38rpx;
								margin-right: 8rpx;
								flex-shrink: 0;
							}

							.action-text {
								font-size: 24rpx;
								color: #222222;
								font-weight: 500;
								white-space: nowrap;
							}
						}
					}
				}

				.view-more-btn {
					display: flex;
					align-items: center;
					justify-content: center;
					border-radius: 24rpx;
					padding: 24rpx;
					margin: 24rpx 0;

					.view-more-text {
						font-size: 28rpx;
						color: #3478f6;
						margin-right: 8rpx;
					}

					.arrow-icon {
						width: 24rpx;
						height: 24rpx;
					}
				}

				.empty-state {
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;
					padding: 200rpx 0;

					.empty-text {
						font-size: 28rpx;
						color: #999999;
					}
				}
			}
		}
	}



	.digital-person-content {
		.digital-person-banner {
			margin-bottom: 24rpx;
			overflow: hidden;
			position: relative;

			&.bottom-banner {
				margin-top: 0;
				margin-bottom: 0;
			}

			.banner-content {
				.banner-image {
					width: 690rpx;
					height: 230rpx;
					display: block;
				}
				.banner-image-radius{
					border-radius: 30rpx;
				}
			}
		}

		.function-area {
			.function-row {
				display: flex;
				align-items: center;
				justify-content: space-between;

				.function-item {
					background: #ffffff;
					border-radius: 24rpx;
					padding: 20rpx 0 20rpx 30rpx;
					height: 160rpx;
					width: 330rpx;
					display: flex;
					align-items: center;
					position: relative;
					box-sizing: border-box;

					.function-icon {
						position: absolute;
						right: 0;
						top: 20rpx;
						width: 120rpx;
						height: 120rpx;
					}

					.function-info {
						position: relative;
						z-index: 2;

						.function-title {
							display: block;
							font-size: 32rpx;
							font-weight: 500;
							color: #333;
							margin-bottom: 8rpx;
						}

						.function-desc {
							display: block;
							font-size: 24rpx;
							font-weight: 400;
							color: #5380F2;
						}
					}
				}
			}
		}

		.computing-power {
			background: #ffffff;
			border-radius: 24rpx;
			padding: 32rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin-bottom: 24rpx;

			.power-info {
				display: flex;
				align-items: center;

				.power-icon {
					width: 48rpx;
					height: 48rpx;
					margin-right: 16rpx;
				}

				.power-text {
					font-size: 32rpx;
					color: #333333;
					font-weight: 400;
					.power-text_number{
						color: #2A64F6;
						font-weight: 500;
						font-size: 34rpx;
					}
				}
			}

			.recharge-btn {
				background: #2A64F6;
				border-radius: 50rpx;
				width: 160rpx;
				height: 60rpx;
				line-height: 60rpx;
				font-size: 26rpx;
				color: #ffffff;
				text-align: center;
			}
		}
	}

	// 自定义充值弹窗样式
	.recharge-overlay {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.7);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 9999;
	}

	.recharge-modal {
		background: linear-gradient(180deg, #EAF0FF 2%, #FFFFFF 20%);
		border-radius: 20rpx;
		padding: 75rpx 40rpx 60rpx;
		width: 560rpx;
		max-width: 90vw;
		position: relative;

		.modal-header {
			text-align: center;
			margin-bottom: 58rpx;

			.modal-title {
				font-size: 35rpx;
				font-weight: 500;
				color: #5380F2;
			}
		}

		.recharge-grid {
			display: grid;
			grid-template-columns: repeat(3, 1fr);
			gap: 28rpx;
			margin-bottom: 60rpx;

			.recharge-item {
				background: #ffffff;
				border: 3rpx solid #E0E0E0;
				border-radius: 16rpx;
				padding: 32rpx 20rpx;
				text-align: center;
				transition: all 0.3s ease;

				&.active {
					border-color: #2B64F6;
					background: #E6EEFC;

					.item-count {
						color: #2A64F6;
					}
				}

				.item-count {
					display: block;
					font-size: 35rpx;
					font-weight: 500;
					color: #656565;
					margin-bottom: 8rpx;
				}

				.item-price {
					display: block;
					font-size: 20rpx;
					color: #FA5151;
					font-weight: 400;
				}
			}
		}

		.no-data {
			text-align: center;
			padding: 80rpx 0;

			.no-data-text {
				color: #999999;
				font-size: 28rpx;
			}
		}

		.payment-section {
			display: flex;
			align-items: center;
			justify-content: center;
			margin-bottom: 16rpx;
			font-weight: 500;

			.payment-text {
				font-size: 32rpx;
				color: #333333;
			}

			.payment-price {
				font-size: 36rpx;
				font-weight: 700;
				color: #FA5151;
			}
		}

		.pay-button {
			background: #2B64F6;
			border-radius: 48rpx;
			padding: 32rpx;
			text-align: center;

			.pay-text {
				font-size: 32rpx;
				color: #ffffff;
				font-weight: 500;
			}
		}

		.close-icon-wrapper {
			position: absolute;
			bottom: -80rpx;
			left: 50%;
			transform: translateX(-50%);
			width: 45rpx;
			height: 45rpx;
			border-radius: 50%;

			.close-icon {
				width: 45rpx;
				height: 45rpx;
			}
		}
	}

}
</style>