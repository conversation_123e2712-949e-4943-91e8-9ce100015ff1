<view class="create-agent-page data-v-6484fb96"><scroll-view class="page-scroll data-v-6484fb96" scroll-y><view class="avatar-section data-v-6484fb96"><view class="avatar-container data-v-6484fb96" bindtap="{{e}}"><image src="{{a}}" class="avatar data-v-6484fb96" mode="aspectFill"/><view wx:if="{{b}}" class="avatar-add data-v-6484fb96"><image src="{{c}}" class="add-icon data-v-6484fb96" mode="aspectFit"/></view><view wx:if="{{d}}" class="avatar-loading data-v-6484fb96"><view class="loading-spinner data-v-6484fb96"></view></view></view><text bindtap="{{g}}" class="{{['avatar-label', 'data-v-6484fb96', h && 'disabled']}}">{{f}}</text><view class="secret-bnt data-v-6484fb96" bindtap="{{i}}">秘钥管理</view></view><view class="form-section data-v-6484fb96"><view class="form-item data-v-6484fb96"><text class="label data-v-6484fb96">选择分类:</text><view class="category-selector data-v-6484fb96" bindtap="{{l}}"><text class="{{['category-text', 'data-v-6484fb96', k && 'placeholder']}}">{{j}}</text></view></view><view class="form-item data-v-6484fb96"><text class="label data-v-6484fb96">名称:</text><input class="input data-v-6484fb96" placeholder="输入名称" placeholder-class="placeholder" maxlength="50" value="{{m}}" bindinput="{{n}}"/></view><view class="form-item data-v-6484fb96"><text class="label data-v-6484fb96">智能体描述:</text><block wx:if="{{r0}}"><textarea class="textarea data-v-6484fb96" placeholder="输入描述" placeholder-class="placeholder" maxlength="500" value="{{o}}" bindinput="{{p}}"/></block></view><view class="form-item data-v-6484fb96"><text class="label data-v-6484fb96">选择智能体类型:</text><view class="radio-group data-v-6484fb96"><view wx:for="{{q}}" wx:for-item="type" wx:key="d" class="radio-item data-v-6484fb96" bindtap="{{type.e}}"><view class="{{['radio', 'data-v-6484fb96', type.b && 'checked']}}"><view wx:if="{{type.a}}" class="radio-inner data-v-6484fb96"></view></view><text class="radio-text data-v-6484fb96">{{type.c}}</text></view></view></view><view wx:if="{{r}}" class="form-item data-v-6484fb96"><text class="label data-v-6484fb96">角色设定提示词:</text><block wx:if="{{r0}}"><textarea class="textarea large data-v-6484fb96" placeholder="输入提示词" placeholder-class="placeholder" maxlength="2000" value="{{s}}" bindinput="{{t}}"/></block></view><view class="form-item data-v-6484fb96"><text class="label data-v-6484fb96">密钥读取类型:</text><view class="radio-group data-v-6484fb96"><view wx:for="{{v}}" wx:for-item="visibility" wx:key="d" class="radio-item data-v-6484fb96" bindtap="{{visibility.e}}"><view class="{{['radio', 'data-v-6484fb96', visibility.b && 'checked']}}"><view wx:if="{{visibility.a}}" class="radio-inner data-v-6484fb96"></view></view><text class="radio-text data-v-6484fb96">{{visibility.c}}</text></view></view></view><view wx:if="{{w}}" class="form-item data-v-6484fb96"><text class="label data-v-6484fb96">扣子智能体ID:</text><input class="input data-v-6484fb96" placeholder="输入密码" placeholder-class="placeholder" value="{{x}}" bindinput="{{y}}"/></view><view wx:if="{{z}}" class="form-item data-v-6484fb96"><text class="label data-v-6484fb96">智能体秘钥:</text><input class="input data-v-6484fb96" placeholder="输入秘钥" placeholder-class="placeholder" value="{{A}}" bindinput="{{B}}"/></view><view wx:if="{{C}}" class="form-item data-v-6484fb96"><text class="label data-v-6484fb96">dify自定义部署地址:</text><input class="input data-v-6484fb96" placeholder="输入地址" placeholder-class="placeholder" value="{{D}}" bindinput="{{E}}"/></view><view class="form-item data-v-6484fb96"><text class="label data-v-6484fb96">开场白:</text><input class="input data-v-6484fb96" placeholder="输入开场白" placeholder-class="placeholder" value="{{F}}" bindinput="{{G}}"/></view><view class="form-item data-v-6484fb96"><text class="label data-v-6484fb96">引导问题列表:</text><view class="question-list data-v-6484fb96"><view wx:for="{{H}}" wx:for-item="question" wx:key="d" class="question-item data-v-6484fb96"><input class="question-input data-v-6484fb96" placeholder="{{question.a}}" placeholder-class="placeholder" value="{{question.b}}" bindinput="{{question.c}}"/></view></view></view><view class="form-item data-v-6484fb96"><text class="label data-v-6484fb96">选择智能体类型:</text><view class="radio-group data-v-6484fb96"><view wx:for="{{I}}" wx:for-item="visibility" wx:key="d" class="radio-item data-v-6484fb96" bindtap="{{visibility.e}}"><view class="{{['radio', 'data-v-6484fb96', visibility.b && 'checked']}}"><view wx:if="{{visibility.a}}" class="radio-inner data-v-6484fb96"></view></view><text class="radio-text data-v-6484fb96">{{visibility.c}}</text></view></view></view><view wx:if="{{J}}" class="form-item data-v-6484fb96"><text class="label data-v-6484fb96">收费金额:</text><input class="input data-v-6484fb96" placeholder="输入金额" placeholder-class="placeholder" type="digit" value="{{K}}" bindinput="{{L}}"/></view><view wx:if="{{M}}" class="form-item data-v-6484fb96"><text class="label data-v-6484fb96">试用聊天次数:</text><input class="input data-v-6484fb96" placeholder="试用聊天次数" placeholder-class="placeholder" type="digit" value="{{N}}" bindinput="{{O}}"/></view><view class="form-note data-v-6484fb96"><text class="note-text data-v-6484fb96">注意：智能体不得违反相关法律法规，禁止涉及政治敏感话题，详情请查看《平台协议》</text></view></view><view class="create-section data-v-6484fb96"><view bindtap="{{Q}}" class="{{['create-btn', 'data-v-6484fb96', R && 'disabled']}}"><text class="create-text data-v-6484fb96">{{P}}</text></view></view></scroll-view><view wx:if="{{S}}" class="category-modal data-v-6484fb96" bindtap="{{af}}"><view class="modal-content data-v-6484fb96" catchtap="{{ae}}"><view class="modal-header data-v-6484fb96"><text class="modal-title data-v-6484fb96">选择分类</text></view><view class="search-section data-v-6484fb96"><view class="search-box data-v-6484fb96"><image src="{{T}}" class="search-icon data-v-6484fb96" mode="aspectFit"/><input class="search-input data-v-6484fb96" placeholder="搜索分类" placeholder-class="placeholder" bindinput="{{U}}" value="{{V}}"/><view wx:if="{{W}}" class="clear-btn data-v-6484fb96" bindtap="{{Y}}"><image src="{{X}}" class="clear-icon data-v-6484fb96" mode="aspectFit"/></view></view></view><scroll-view class="category-list data-v-6484fb96" scroll-y><view wx:for="{{Z}}" wx:for-item="category" wx:key="d" class="category-item data-v-6484fb96" bindtap="{{category.e}}"><view class="{{['radio', 'data-v-6484fb96', category.b && 'checked']}}"><view wx:if="{{category.a}}" class="radio-inner data-v-6484fb96"></view></view><text class="category-name data-v-6484fb96">{{category.c}}</text></view><view wx:if="{{aa}}" class="empty-state data-v-6484fb96"><text class="empty-text data-v-6484fb96">暂无匹配的分类</text></view></scroll-view><view class="modal-footer data-v-6484fb96"><view class="cancel-btn data-v-6484fb96" bindtap="{{ab}}"><text class="cancel-text data-v-6484fb96">取消</text></view><view bindtap="{{ac}}" class="{{['confirm-btn', 'data-v-6484fb96', ad && 'disabled']}}"><text class="confirm-text data-v-6484fb96">确认</text></view></view></view></view></view>