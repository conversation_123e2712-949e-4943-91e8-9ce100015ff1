{"version": 3, "file": "uni-swipe-action.js", "sources": ["uni_modules/uni-swipe-action/components/uni-swipe-action/uni-swipe-action.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RToveW91bmdQcm9qZWN0L2FnZW50LW1pbmktdWkvdW5pX21vZHVsZXMvdW5pLXN3aXBlLWFjdGlvbi9jb21wb25lbnRzL3VuaS1zd2lwZS1hY3Rpb24vdW5pLXN3aXBlLWFjdGlvbi52dWU"], "sourcesContent": ["<template>\r\n\t<view>\r\n\t\t<slot></slot>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\t/**\r\n\t * SwipeAction 滑动操作\r\n\t * @description 通过滑动触发选项的容器\r\n\t * @tutorial https://ext.dcloud.net.cn/plugin?id=181\r\n\t */\r\n\texport default {\n\t\tname:\"uniSwipeAction\",\r\n\t\tdata() {\r\n\t\t\treturn {};\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\tthis.children = [];\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 公开给用户使用，重制组件样式\r\n\t\t\tresize(){\r\n\t\t\t\t// wxs 会自己计算组件大小，所以无需执行下面代码\r\n\t\t\t\t// #ifndef APP-VUE || H5 || MP-WEIXIN\r\n\t\t\t\tthis.children.forEach(vm=>{\r\n\t\t\t\t\tvm.init()\r\n\t\t\t\t})\r\n\t\t\t\t// #endif\r\n\t\t\t},\n\t\t\t// 公开给用户使用，关闭全部 已经打开的组件\n\t\t\tcloseAll(){\n\t\t\t\tthis.children.forEach(vm=>{\n\t\t\t\t\t// #ifdef APP-VUE || H5 || MP-WEIXIN\n\t\t\t\t\tvm.is_show = 'none'\n\t\t\t\t\t// #endif\n\n\t\t\t\t\t// #ifndef APP-VUE || H5 || MP-WEIXIN\n\t\t\t\t\tvm.close()\n\t\t\t\t\t// #endif\n\t\t\t\t})\n\t\t\t},\r\n\t\t\tcloseOther(vm) {\r\n\t\t\t\tif (this.openItem && this.openItem !== vm) {\r\n\t\t\t\t\t// #ifdef APP-VUE || H5 || MP-WEIXIN\r\n\t\t\t\t\tthis.openItem.is_show = 'none'\r\n\t\t\t\t\t// #endif\r\n\r\n\t\t\t\t\t// #ifndef APP-VUE || H5 || MP-WEIXIN\r\n\t\t\t\t\tthis.openItem.close()\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t}\r\n\t\t\t\t// 记录上一个打开的 swipe-action-item ,用于 auto-close\r\n\t\t\t\tthis.openItem = vm\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n\r\n<style></style>\n", "import Component from 'E:/youngProject/agent-mini-ui/uni_modules/uni-swipe-action/components/uni-swipe-action/uni-swipe-action.vue'\nwx.createComponent(Component)"], "names": [], "mappings": ";;AAYC,MAAK,YAAU;AAAA,EACd,MAAK;AAAA,EACL,OAAO;AACN,WAAO;EACP;AAAA,EACD,UAAU;AACT,SAAK,WAAW;EAChB;AAAA,EACD,SAAS;AAAA;AAAA,IAER,SAAQ;AAAA,IAOP;AAAA;AAAA,IAED,WAAU;AACT,WAAK,SAAS,QAAQ,QAAI;AAEzB,WAAG,UAAU;AAAA,OAMb;AAAA,IACD;AAAA,IACD,WAAW,IAAI;AACd,UAAI,KAAK,YAAY,KAAK,aAAa,IAAI;AAE1C,aAAK,SAAS,UAAU;AAAA,MAMzB;AAEA,WAAK,WAAW;AAAA,IACjB;AAAA,EACD;;;;;;ACtDF,GAAG,gBAAgB,SAAS;"}