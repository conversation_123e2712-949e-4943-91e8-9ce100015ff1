{"version": 3, "file": "uni-swipe-action-item.js", "sources": ["uni_modules/uni-swipe-action/components/uni-swipe-action-item/uni-swipe-action-item.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RToveW91bmdQcm9qZWN0L2FnZW50LW1pbmktdWkvdW5pX21vZHVsZXMvdW5pLXN3aXBlLWFjdGlvbi9jb21wb25lbnRzL3VuaS1zd2lwZS1hY3Rpb24taXRlbS91bmktc3dpcGUtYWN0aW9uLWl0ZW0udnVl"], "sourcesContent": ["<template>\r\n\t<!-- 在微信小程序 app vue端 h5 使用wxs 实现-->\r\n\t<!-- #ifdef APP-VUE || APP-HARMONY || MP-WEIXIN || H5 -->\r\n\t<view class=\"uni-swipe\">\n\t\t<!--  #ifdef MP-WEIXIN || H5 -->\n\t\t<view class=\"uni-swipe_box\" :change:prop=\"wxsswipe.showWatch\" :prop=\"is_show\" :data-threshold=\"threshold\"\r\n\t\t\t:data-disabled=\"disabled\" @touchstart=\"wxsswipe.touchstart\" @touchmove=\"wxsswipe.touchmove\"\r\n\t\t\t@touchend=\"wxsswipe.touchend\">\r\n\t\t\t<!-- #endif -->\r\n\t\t\t<!--  #ifndef MP-WEIXIN || H5 -->\n\t\t\t<view class=\"uni-swipe_box\" :change:prop=\"renderswipe.showWatch\" :prop=\"is_show\" :data-threshold=\"threshold\"\r\n\t\t\t\t:data-disabled=\"disabled+''\" @touchstart=\"renderswipe.touchstart\" @touchmove=\"renderswipe.touchmove\"\r\n\t\t\t\t@touchend=\"renderswipe.touchend\">\r\n\t\t\t<!-- #endif -->\r\n\t\t\t\t<!-- 在微信小程序 app vue端 h5 使用wxs 实现-->\r\n\t\t\t\t<view class=\"uni-swipe_button-group button-group--left\">\r\n\t\t\t\t\t<slot name=\"left\">\r\n\t\t\t\t\t\t<view v-for=\"(item,index) in leftOptions\" :key=\"index\" :style=\"{\r\n\t\t\t\t\t  backgroundColor: item.style && item.style.backgroundColor ? item.style.backgroundColor : '#C7C6CD'\r\n\t\t\t\t\t}\" class=\"uni-swipe_button button-hock\" @touchstart.stop=\"appTouchStart\"\r\n\t\t\t\t\t\t\***************=\"appTouchEnd($event,index,item,'left')\" @click.stop=\"onClickForPC(index,item,'left')\">\r\n\t\t\t\t\t\t\t<text class=\"uni-swipe_button-text\"\r\n\t\t\t\t\t\t\t\t:style=\"{color: item.style && item.style.color ? item.style.color : '#FFFFFF',fontSize: item.style && item.style.fontSize ? item.style.fontSize : '16px'}\">{{ item.text }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</slot>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"uni-swipe_text--center\">\r\n\t\t\t\t\t<slot></slot>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"uni-swipe_button-group button-group--right\">\r\n\t\t\t\t\t<slot name=\"right\">\r\n\t\t\t\t\t\t<view v-for=\"(item,index) in rightOptions\" :key=\"index\" :style=\"{\r\n\t\t\t\t\t  backgroundColor: item.style && item.style.backgroundColor ? item.style.backgroundColor : '#C7C6CD'\r\n\t\t\t\t\t}\" class=\"uni-swipe_button button-hock\" @touchstart.stop=\"appTouchStart\"\r\n\t\t\t\t\t\t\***************=\"appTouchEnd($event,index,item,'right')\" @click.stop=\"onClickForPC(index,item,'right')\"><text\r\n\t\t\t\t\t\t\t\tclass=\"uni-swipe_button-text\"\r\n\t\t\t\t\t\t\t\t:style=\"{color: item.style && item.style.color ? item.style.color : '#FFFFFF',fontSize: item.style && item.style.fontSize ? item.style.fontSize : '16px'}\">{{ item.text }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</slot>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- #endif -->\r\n\t\t<!-- app nvue端 使用 bindingx -->\r\n\t\t<!-- #ifdef APP-NVUE -->\r\n\t\t<view ref=\"selector-box--hock\" class=\"uni-swipe\" @horizontalpan=\"touchstart\" @touchend=\"touchend\">\r\n\t\t\t<view ref='selector-left-button--hock' class=\"uni-swipe_button-group button-group--left\">\r\n\t\t\t\t<slot name=\"left\">\r\n\t\t\t\t\t<view v-for=\"(item,index) in leftOptions\" :key=\"index\" :style=\"{\r\n\t\t\t\t  backgroundColor: item.style && item.style.backgroundColor ? item.style.backgroundColor : '#C7C6CD'\r\n\t\t\t\t}\" class=\"uni-swipe_button button-hock\" @click.stop=\"onClick(index,item,'left')\">\r\n\t\t\t\t\t\t<text class=\"uni-swipe_button-text\"\r\n\t\t\t\t\t\t\t:style=\"{color: item.style && item.style.color ? item.style.color : '#FFFFFF', fontSize: item.style && item.style.fontSize ? item.style.fontSize : '16px'}\">\r\n\t\t\t\t\t\t\t{{ item.text }}\r\n\t\t\t\t\t\t</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</slot>\r\n\t\t\t</view>\r\n\t\t\t<view ref='selector-right-button--hock' class=\"uni-swipe_button-group button-group--right\">\r\n\t\t\t\t<slot name=\"right\">\r\n\t\t\t\t\t<view v-for=\"(item,index) in rightOptions\" :key=\"index\" :style=\"{\r\n\t\t\t\t  backgroundColor: item.style && item.style.backgroundColor ? item.style.backgroundColor : '#C7C6CD'\r\n\t\t\t\t}\" class=\"uni-swipe_button button-hock\" @click.stop=\"onClick(index,item,'right')\"><text\r\n\t\t\t\t\t\t\tclass=\"uni-swipe_button-text\"\r\n\t\t\t\t\t\t\t:style=\"{color: item.style && item.style.color ? item.style.color : '#FFFFFF',fontSize: item.style && item.style.fontSize ? item.style.fontSize : '16px'}\">{{ item.text }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</slot>\r\n\t\t\t</view>\r\n\t\t\t<view ref='selector-content--hock' class=\"uni-swipe_box\">\r\n\t\t\t\t<slot></slot>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- #endif -->\r\n\t\t<!-- 其他平台使用 js ，长列表性能可能会有影响-->\r\n\t\t<!-- #ifdef MP-ALIPAY || MP-BAIDU || MP-TOUTIAO || MP-QQ -->\r\n\t\t<view class=\"uni-swipe\">\r\n\t\t\t<view class=\"uni-swipe_box\" @touchstart=\"touchstart\" @touchmove=\"touchmove\" @touchend=\"touchend\"\r\n\t\t\t\t:style=\"{transform:moveLeft}\" :class=\"{ani:ani}\">\r\n\t\t\t\t<view class=\"uni-swipe_button-group button-group--left\" :class=\"[elClass]\">\r\n\t\t\t\t\t<slot name=\"left\">\r\n\t\t\t\t\t\t<view v-for=\"(item,index) in leftOptions\" :key=\"index\" :style=\"{\r\n\t\t\t\t\t  backgroundColor: item.style && item.style.backgroundColor ? item.style.backgroundColor : '#C7C6CD',\r\n\t\t\t\t\t  fontSize: item.style && item.style.fontSize ? item.style.fontSize : '16px'\r\n\t\t\t\t\t}\" class=\"uni-swipe_button button-hock\" @touchstart.stop=\"appTouchStart\"\r\n\t\t\t\t\t\t\***************=\"appTouchEnd($event,index,item,'left')\"><text class=\"uni-swipe_button-text\"\r\n\t\t\t\t\t\t\t\t:style=\"{color: item.style && item.style.color ? item.style.color : '#FFFFFF',}\">{{ item.text }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</slot>\r\n\t\t\t\t</view>\r\n\t\t\t\t<slot></slot>\r\n\t\t\t\t<view class=\"uni-swipe_button-group button-group--right\" :class=\"[elClass]\">\r\n\t\t\t\t\t<slot name=\"right\">\r\n\t\t\t\t\t\t<view v-for=\"(item,index) in rightOptions\" :key=\"index\" :style=\"{\r\n\t\t\t\t\t  backgroundColor: item.style && item.style.backgroundColor ? item.style.backgroundColor : '#C7C6CD',\r\n\t\t\t\t\t  fontSize: item.style && item.style.fontSize ? item.style.fontSize : '16px'\r\n\t\t\t\t\t}\" @touchstart.stop=\"appTouchStart\" @touchend.stop=\"appTouchEnd($event,index,item,'right')\"\r\n\t\t\t\t\t\t\tclass=\"uni-swipe_button button-hock\"><text class=\"uni-swipe_button-text\"\r\n\t\t\t\t\t\t\t\t:style=\"{color: item.style && item.style.color ? item.style.color : '#FFFFFF',}\">{{ item.text }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</slot>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- #endif -->\r\n\r\n</template>\r\n<script src=\"./wx.wxs\" module=\"wxsswipe\" lang=\"wxs\"></script>\r\n\r\n<script module=\"renderswipe\" lang=\"renderjs\">\r\n\timport render from './render.js'\r\n\texport default {\r\n\t\tmounted(e, ins, owner) {\r\n\t\t\tthis.state = {}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tshowWatch(newVal, oldVal, ownerInstance, instance) {\r\n\t\t\t\trender.showWatch(newVal, oldVal, ownerInstance, instance, this)\r\n\t\t\t},\r\n\t\t\ttouchstart(e, ownerInstance) {\r\n\t\t\t\trender.touchstart(e, ownerInstance, this)\r\n\t\t\t},\r\n\t\t\ttouchmove(e, ownerInstance) {\r\n\t\t\t\trender.touchmove(e, ownerInstance, this)\r\n\t\t\t},\r\n\t\t\ttouchend(e, ownerInstance) {\r\n\t\t\t\trender.touchend(e, ownerInstance, this)\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n<script>\r\n\timport mpwxs from './mpwxs'\r\n\timport bindingx from './bindingx.js'\r\n\timport mpother from './mpother'\r\n\r\n\t/**\r\n\t * SwipeActionItem 滑动操作子组件\r\n\t * @description 通过滑动触发选项的容器\r\n\t * @tutorial https://ext.dcloud.net.cn/plugin?id=181\r\n\t * @property {Boolean} show = [left|right｜none] \t开启关闭组件，auto-close = false 时生效\r\n\t * @property {Boolean} disabled = [true|false] \t\t是否禁止滑动\r\n\t * @property {Boolean} autoClose = [true|false] \t滑动打开当前组件，是否关闭其他组件\r\n\t * @property {Number}  threshold \t\t\t\t\t滑动缺省值\r\n\t * @property {Array} leftOptions \t\t\t\t\t左侧选项内容及样式\r\n\t * @property {Array} rightOptions \t\t\t\t\t右侧选项内容及样式\r\n\t * @event {Function} click \t\t\t\t\t\t\t点击选项按钮时触发事件，e = {content,index} ，content（点击内容）、index（下标)\r\n\t * @event {Function} change \t\t\t\t\t\t组件打开或关闭时触发，left\\right\\none\r\n\t */\r\n\r\n\texport default {\r\n\t\tmixins: [mpwxs, bindingx, mpother],\r\n\t\temits: ['click', 'change'],\r\n\t\tprops: {\r\n\t\t\t// 控制开关\r\n\t\t\tshow: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'none'\r\n\t\t\t},\r\n\r\n\t\t\t// 禁用\r\n\t\t\tdisabled: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\r\n\t\t\t// 是否自动关闭\r\n\t\t\tautoClose: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\r\n\t\t\t// 滑动缺省距离\r\n\t\t\tthreshold: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 20\r\n\t\t\t},\r\n\r\n\t\t\t// 左侧按钮内容\r\n\t\t\tleftOptions: {\r\n\t\t\t\ttype: Array,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn []\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 右侧按钮内容\r\n\t\t\trightOptions: {\r\n\t\t\t\ttype: Array,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn []\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t},\r\n\t\t// #ifndef VUE3\r\n\t\t// TODO vue2\r\n\t\tdestroyed() {\r\n\t\t\tif (this.__isUnmounted) return\r\n\t\t\tthis.uninstall()\r\n\t\t},\r\n\t\t// #endif\r\n\t\t// #ifdef VUE3\r\n\t\t// TODO vue3\r\n\t\tunmounted() {\r\n\t\t\tthis.__isUnmounted = true\r\n\t\t\tthis.uninstall()\r\n\t\t},\r\n\t\t// #endif\r\n\r\n\t\tmethods: {\r\n\t\t\tuninstall() {\r\n\t\t\t\tif (this.swipeaction) {\r\n\t\t\t\t\tthis.swipeaction.children.forEach((item, index) => {\r\n\t\t\t\t\t\tif (item === this) {\r\n\t\t\t\t\t\t\tthis.swipeaction.children.splice(index, 1)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 获取父元素实例\r\n\t\t\t */\r\n\t\t\tgetSwipeAction(name = 'uniSwipeAction') {\r\n\t\t\t\tlet parent = this.$parent;\r\n\t\t\t\tlet parentName = parent.$options.name;\r\n\t\t\t\twhile (parentName !== name) {\r\n\t\t\t\t\tparent = parent.$parent;\r\n\t\t\t\t\tif (!parent) return false;\r\n\t\t\t\t\tparentName = parent.$options.name;\r\n\t\t\t\t}\r\n\t\t\t\treturn parent;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n<style lang=\"scss\">\r\n\t.uni-swipe {\r\n\t\tposition: relative;\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\toverflow: hidden;\r\n\t\t/* #endif */\r\n\t}\r\n\r\n\t.uni-swipe_box {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\tflex-shrink: 0;\r\n\t\t// touch-action: none;\r\n\t\t/* #endif */\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.uni-swipe_content {\r\n\t\t// border: 1px red solid;\r\n\t}\r\n\r\n\t.uni-swipe_text--center {\r\n\t\twidth: 100%;\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tcursor: grab;\r\n\t\t/* #endif */\r\n\t}\r\n\r\n\t.uni-swipe_button-group {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tbox-sizing: border-box;\r\n\t\tdisplay: flex;\r\n\t\t/* #endif */\r\n\t\tflex-direction: row;\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tbottom: 0;\r\n\t\t/* #ifdef H5 */\r\n\t\tcursor: pointer;\r\n\t\t/* #endif */\r\n\t}\r\n\r\n\t.button-group--left {\r\n\t\tleft: 0;\r\n\t\ttransform: translateX(-100%)\r\n\t}\r\n\r\n\t.button-group--right {\r\n\t\tright: 0;\r\n\t\ttransform: translateX(100%)\r\n\t}\r\n\r\n\t.uni-swipe_button {\r\n\t\t/* #ifdef APP-NVUE */\r\n\t\tflex: 1;\r\n\t\t/* #endif */\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\t/* #endif */\r\n\t\tflex-direction: row;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\tpadding: 0 20px;\r\n\t}\r\n\r\n\t.uni-swipe_button-text {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tflex-shrink: 0;\r\n\t\t/* #endif */\r\n\t\tfont-size: 14px;\r\n\t}\r\n\r\n\t.ani {\r\n\t\ttransition-property: transform;\r\n\t\ttransition-duration: 0.3s;\r\n\t\ttransition-timing-function: cubic-bezier(0.165, 0.84, 0.44, 1);\r\n\t}\r\n\r\n\t/* #ifdef MP-ALIPAY */\r\n\t.movable-area {\r\n\t\t/* width: 100%; */\r\n\t\theight: 45px;\r\n\t}\r\n\r\n\t.movable-view {\r\n\t\tdisplay: flex;\r\n\t\t/* justify-content: center; */\r\n\t\tposition: relative;\r\n\t\tflex: 1;\r\n\t\theight: 45px;\r\n\t\tz-index: 2;\r\n\t}\r\n\r\n\t.movable-view-button {\r\n\t\tdisplay: flex;\r\n\t\tflex-shrink: 0;\r\n\t\tflex-direction: row;\r\n\t\theight: 100%;\r\n\t\tbackground: #C0C0C0;\r\n\t}\r\n\r\n\t/* .transition {\r\n\t\ttransition: all 0.3s;\r\n\t} */\r\n\r\n\t.movable-view-box {\r\n\t\tflex-shrink: 0;\r\n\t\theight: 100%;\r\n\t\tbackground-color: #fff;\r\n\t}\r\n\r\n\t/* #endif */\r\n</style>\r\n", "import Component from 'E:/youngProject/agent-mini-ui/uni_modules/uni-swipe-action/components/uni-swipe-action-item/uni-swipe-action-item.vue'\nwx.createComponent(Component)"], "names": ["mpwxs", "bindingx", "mpother"], "mappings": ";;;;;;;;;;;;AAqJC,MAAK,YAAU;AAAA,EACd,QAAQ,CAACA,+DAAAA,OAAOC,kEAAQ,gBAAEC,4EAAO;AAAA,EACjC,OAAO,CAAC,SAAS,QAAQ;AAAA,EACzB,OAAO;AAAA;AAAA,IAEN,MAAM;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA;AAAA,IAGD,UAAU;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA;AAAA,IAGD,WAAW;AAAA,MACV,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA;AAAA,IAGD,WAAW;AAAA,MACV,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA;AAAA,IAGD,aAAa;AAAA,MACZ,MAAM;AAAA,MACN,UAAW;AACV,eAAO,CAAC;AAAA,MACT;AAAA,IACA;AAAA;AAAA,IAGD,cAAc;AAAA,MACb,MAAM;AAAA,MACN,UAAW;AACV,eAAO,CAAC;AAAA,MACT;AAAA,IACD;AAAA,EAEA;AAAA;AAAA,EAUD,YAAY;AACX,SAAK,gBAAgB;AACrB,SAAK,UAAU;AAAA,EACf;AAAA,EAGD,SAAS;AAAA,IACR,YAAY;AACX,UAAI,KAAK,aAAa;AACrB,aAAK,YAAY,SAAS,QAAQ,CAAC,MAAM,UAAU;AAClD,cAAI,SAAS,MAAM;AAClB,iBAAK,YAAY,SAAS,OAAO,OAAO,CAAC;AAAA,UAC1C;AAAA,SACA;AAAA,MACF;AAAA,IACA;AAAA;AAAA;AAAA;AAAA,IAID,eAAe,OAAO,kBAAkB;AACvC,UAAI,SAAS,KAAK;AAClB,UAAI,aAAa,OAAO,SAAS;AACjC,aAAO,eAAe,MAAM;AAC3B,iBAAS,OAAO;AAChB,YAAI,CAAC;AAAQ,iBAAO;AACpB,qBAAa,OAAO,SAAS;AAAA,MAC9B;AACA,aAAO;AAAA,IACR;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACxOD,GAAG,gBAAgB,SAAS;"}