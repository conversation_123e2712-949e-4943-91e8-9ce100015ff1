"use strict";const e=require("../../common/vendor.js"),a=require("../../api/index.js"),i=require("../../stores/user.js"),t={__name:"detail",setup(t){const s=i.useUserStore(),n=e.ref(""),o=e.ref(""),r=e.ref(!1);e.ref(!1),e.ref(!1),e.ref(!1);const u=e.ref({agentName:"",agentDesc:"",agentAvatar:"",isPaid:0,price:0,isPublic:1,isSubscribed:!1}),c=async()=>{if(o.value)try{r.value=!0;const e=await a.agentDetailApi({merchantGuid:s.merchantGuid,agentSysId:o.value});if(0!==e.code)throw new Error(e.msg||"获取详情失败");u.value=e.data,o.value=e.data.sysId,n.value=e.data.guid}catch(i){console.error("获取智能体详情失败:",i),e.index.showToast({title:i.message||"加载失败",icon:"none"})}finally{r.value=!1}else e.index.showToast({title:"参数错误",icon:"none"})},d=async()=>{if(u.value.isSubscribed)e.index.navigateTo({url:`/pages/msg/index?sessionGuid=${n.value}`});else try{e.index.showLoading({title:"订阅中...",mask:!0});const i=await a.subscribeAgentApi({merchantGuid:s.merchantGuid,agentGuid:n.value});if(0!==i.code)throw new Error(i.msg||"订阅失败");e.index.showToast({title:"订阅成功",icon:"success"}),e.index.navigateTo({url:`/pages/msg/index?sessionGuid=${n.value}`})}catch(i){console.error("订阅失败:",i),e.index.showToast({title:i.message||"订阅失败",icon:"none"})}finally{e.index.hideLoading()}},l=async()=>{if(!g.value)return void e.index.showToast({title:"正在生成二维码，请稍后...",icon:"none"});const a={agentName:u.value.agentName||"智能体名称",agentDesc:u.value.agentDesc||"智能体描述",agentAvatar:u.value.agentAvatar||"",qrcode:g.value||""};console.log("传递给分享页的参数:",a),e.index.navigateTo({url:`/pages/square/share?params=${encodeURIComponent(JSON.stringify(a))}`})},v=async i=>{try{e.index.showLoading({title:"订阅中...",mask:!0});const i=await a.subscribeAgentApi({merchantGuid:s.merchantGuid,agentGuid:n.value});if(0!==i.code)throw new Error(i.msg||"订阅失败");e.index.showToast({title:"订阅成功",icon:"success"}),u.value.isSubscribed=!0,setTimeout((()=>{e.index.navigateTo({url:`/pages/msg/index?sessionGuid=${n.value}`})}),1500)}catch(t){console.error("订阅失败:",t),e.index.showToast({title:t.message||"订阅失败",icon:"none"})}finally{e.index.hideLoading()}};e.onShareAppMessage((()=>({title:s.appName||"智能体",path:`/pages/square/detail?invite=${m.value}`,success(a){e.index.showToast({title:"分享成功"})},fail(a){e.index.showToast({title:"分享失败",icon:"none"})}})));const g=e.ref(""),m=e.ref(""),h=async()=>{let e=await a.getInvitationCodeApi({merchantGuid:s.merchantGuid});m.value=e.data.inviteCode,(async()=>{let e=`sysId=${o.value}&invite=${s.invitationCode}`,i=await a.generateMiniCodeApi({merchantGuid:s.merchantGuid,miniPath:"pages/square/detail",pathQuery:e});g.value=i.data.miniCodeUrl})()};return e.onLoad((async a=>{a.sysId?(o.value=a.sysId,s.userToken&&(await c(),await h())):e.index.showToast({title:"参数错误",icon:"none"})})),e.watch((()=>s.userToken),(async(e,a)=>{e&&""===a&&(await c(),await h())})),(a,i)=>e.e({a:u.value.agentName},u.value.agentName?e.e({b:u.value.agentAvatar,c:e.t(u.value.creator.nickname),d:e.t(u.value.agentName),e:e.t(u.value.agentDesc),f:!u.value.isSubscribed},u.value.isSubscribed?{}:{g:e.o(v)},{h:u.value.isSubscribed},u.value.isSubscribed?{i:e.o(d)}:{},{j:e.o(l)}):(r.value,{}),{k:r.value})}},s=e._export_sfc(t,[["__scopeId","data-v-3c4ace4c"]]);t.__runtimeHooks=6,wx.createPage(s);
