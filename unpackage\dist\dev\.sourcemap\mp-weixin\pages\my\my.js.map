{"version": 3, "file": "my.js", "sources": ["pages/my/my.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvbXkvbXkudnVl"], "sourcesContent": ["<template>\r\n\t<view class=\"profile-page\">\r\n\t\t<!-- 顶部用户信息 -->\r\n\t\t<view class=\"header\">\r\n\t\t\t<view class=\"user-info\">\r\n\t\t\t\t<image class=\"avatar\" :src=\"userInfo.headImgUrl\" mode=\"aspectFill\" />\r\n\t\t\t\t<view class=\"info\">\r\n\t\t\t\t\t<text class=\"name\">{{ userInfo.nickname || '未设置昵称' }}</text>\r\n\t\t\t\t\t<text class=\"email\">{{ userInfo.email || '未设置邮箱' }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"edit-btn\" @tap=\"handleEditProfile\">\r\n\t\t\t\t<!-- <image src=\"@/static/my/<EMAIL>\" class=\"edit-icon\" mode=\"aspectFit\" /> -->\r\n\t\t\t\t<text class=\"edit-text\">编辑资料</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- 会员卡 -->\r\n\t\t<view class=\"vip-card-warp\">\r\n\t\t\t<view class=\"vip-card\">\r\n\t\t\t\t<view class=\"info-box\">\r\n\t\t\t\t\t<view class=\"title\" v-if=\"!vipInfo.hasMembership\">成为思链AI会员</view>\r\n\t\t\t\t\t<view class=\"title\" v-else>已激活思链AI会员<text class=\"day\">({{ vipInfo.remainingDays }}天)</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"label\" v-if=\"!vipInfo.hasMembership\">让AI成为你的IP合伙人</view>\r\n\t\t\t\t\t<view class=\"label\" v-else>让AI成为你的IP合伙人</view>\r\n\t\t\t\t\t<view class=\"icon-box\" @click=\"handleOpenVip\">\r\n\t\t\t\t\t\t<image class=\"icon\" :src=\"vipIcon\"></image>\r\n\t\t\t\t\t\t<view class=\"text\" v-if=\"!vipInfo.hasMembership\">开通会员</view>\r\n\t\t\t\t\t\t<view class=\"text\" v-else>续费会员</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"dy-box\">\r\n\t\t\t\t\t<view class=\"left\">\r\n\t\t\t\t\t\t<image class=\"icon\" src=\"@/static/my/vip-icon.png\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t\t<text class=\"sub-text\">AI点数:{{ vipInfo.userPoints.totalPointsText }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- <view class=\"dy-box\">\r\n\t\t\t\t\t<view class=\"left\">\r\n\t\t\t\t\t\t<image class=\"icon\" src=\"@/static/my/vip-icon.png\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t\t<text class=\"sub-text\" @click=\"handleOpenSub\">专属订阅({{ vipInfo.creatorSubscriptionCount }})</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"right\" @click=\"handleOpenSub\">去订阅<image class=\"icon\" src=\"@/static/my/right-arrow-icon.png\">\r\n\t\t\t\t\t\t</image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view> -->\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- Tab 切换栏 -->\r\n\t\t<view class=\"tab-container\">\r\n\t\t\t<view class=\"tab-bar\">\r\n\t\t\t\t<view v-for=\"(tab, idx) in tabs\" :key=\"tab\" :class=\"['tab', { active: activeTab === idx }]\"\r\n\t\t\t\t\t@click=\"activeTab = idx\">\r\n\t\t\t\t\t<text class=\"tab-text\">{{ tab }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- Tab 内容区 -->\r\n\t\t<view class=\"tab-content\">\r\n\t\t\t<view v-if=\"activeTab === 0\" class=\"agents-tab\">\r\n\t\t\t\t<!-- 我的智能体 -->\r\n\t\t\t\t<view class=\"agents-list\">\r\n\t\t\t\t\t<view class=\"create-agent\">\r\n\t\t\t\t\t\t<view class=\"create-btn\" @tap=\"handleCreateAgent\">\r\n\t\t\t\t\t\t\t<image src=\"@/static/msg/<EMAIL>\" class=\"create-icon\" mode=\"aspectFit\" />\r\n\t\t\t\t\t\t\t<text class=\"create-text\">创建AI智能体</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"agent-card\" v-for=\"(agent, index) in agentList\" :key=\"index\">\r\n\t\t\t\t\t\t<image class=\"agent-avatar\" :src=\"agent.agentAvatar\" mode=\"aspectFill\" />\r\n\t\t\t\t\t\t<view class=\"agent-info\" @click=\"handleAgentClick(agent)\">\r\n\t\t\t\t\t\t\t<view class=\"agent-title\">{{ agent.agentName }}</view>\r\n\t\t\t\t\t\t\t<text class=\"agent-desc\">{{ agent.agentDesc }}</text>\r\n\t\t\t\t\t\t\t<view class=\"agent-tags\">\r\n\t\t\t\t\t\t\t\t<!-- <view v-for=\"tag in agent.tags\" :key=\"tag\" class=\"tag primary\">\r\n\t\t\t\t\t\t\t\t\t\t{{ tag.text }}\r\n\t\t\t\t\t\t\t\t\t</view> -->\r\n\t\t\t\t\t\t\t\t<view class=\"tag primary\">\r\n\t\t\t\t\t\t\t\t\t{{agentTypes.find(item => item.value === agent.agentType).label}}\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"tag primary\">\r\n\t\t\t\t\t\t\t\t\t{{ agent.priceText }}\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"tag primary\">\r\n\t\t\t\t\t\t\t\t\t{{ agent.isPublicText }}\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"operate-box\">\r\n\t\t\t\t\t\t\t<view class=\"edit\" @click=\"handleEditAgent(agent)\">编辑</view>\r\n\t\t\t\t\t\t\t<view class=\"delete\" @click=\"handleDeleteAgent(agent)\">删除</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"status\" v-if=\"agent.auditStatus != 2\">{{ auditStatus[agent.auditStatus] }}</view>\r\n\t\t\t\t\t\t<view class=\"status success\" v-if=\"agent.auditStatus === 2\">{{ auditStatus[agent.auditStatus] }}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 查看更多按钮 -->\r\n\t\t\t\t<view class=\"view-more-btn\" @tap=\"handleViewMoreAgents\" v-if=\"agentList.length > 0\">\r\n\t\t\t\t\t<text class=\"view-more-text\">查看更多</text>\r\n\t\t\t\t\t<image src=\"@/static/index/<EMAIL>\" class=\"arrow-icon\" mode=\"aspectFit\" />\r\n\t\t\t\t</view>\r\n\r\n\r\n\t\t\t</view>\r\n\t\t\t<view v-else-if=\"activeTab === 1\" class=\"income-tab\">\r\n\t\t\t\t<!-- 共创收益 -->\r\n\t\t\t\t<view class=\"income-content\">\r\n\t\t\t\t\t<!-- 共创规则 -->\r\n\t\t\t\t\t<view class=\"rules-section\">\r\n\t\t\t\t\t\t<text class=\"section-title\">共创规则</text>\r\n\t\t\t\t\t\t<view class=\"rules-grid\">\r\n\t\t\t\t\t\t\t<view v-for=\"(rule, index) in rulesList\" :key=\"index\" class=\"rule-item\" @click=\"handleRuleClick(index)\">\r\n\t\t\t\t\t\t\t\t<image :src=\"rule.icon\" class=\"rule-icon\" mode=\"aspectFit\" />\r\n\t\t\t\t\t\t\t\t<text class=\"rule-text\">{{ rule.name }}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<!-- 我的收益 -->\r\n\t\t\t\t\t<view class=\"my-income-section\">\r\n\t\t\t\t\t\t<text class=\"section-title\">我的收益</text>\r\n\t\t\t\t\t\t<view class=\"income-card\">\r\n\t\t\t\t\t\t\t<view class=\"income-amount\">\r\n\t\t\t\t\t\t\t\t<text class=\"amount-value\">{{ incomeData.availableAmountText }}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"amount-unit\">可用余额(元)</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"withdraw-btn\" @tap=\"handleWithdraw\">\r\n\t\t\t\t\t\t\t\t<text class=\"withdraw-text\">提现</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<!-- 收益总览 -->\r\n\t\t\t\t\t<view class=\"overview-section\">\r\n\t\t\t\t\t\t<text class=\"section-title\">收益总览</text>\r\n\t\t\t\t\t\t<view class=\"overview-grid\">\r\n\t\t\t\t\t\t\t<view class=\"overview-item\">\r\n\t\t\t\t\t\t\t\t<text class=\"overview-value\">{{ incomeData.totalEarningsText }}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"overview-label\">总收益(元)</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"overview-item\">\r\n\t\t\t\t\t\t\t\t<text class=\"overview-value\">{{ incomeData.totalWithdrawnText }}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"overview-label\">总提现(元)</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<!-- 收益流水 -->\r\n\t\t\t\t\t<view class=\"flow-section\">\r\n\t\t\t\t\t\t<text class=\"section-title\">收益流水</text>\r\n\t\t\t\t\t\t<view class=\"flow-buttons\">\r\n\t\t\t\t\t\t\t<view class=\"flow-btn\" @tap=\"handleWithdrawRecord\">\r\n\t\t\t\t\t\t\t\t<image src=\"@/static/my/<EMAIL>\" class=\"flow-icon\" mode=\"aspectFit\" />\r\n\t\t\t\t\t\t\t\t<text class=\"flow-text\">提现记录</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"flow-btn\" @tap=\"handleFinanceFlow\">\r\n\t\t\t\t\t\t\t\t<image src=\"@/static/my/<EMAIL>\" class=\"flow-icon\" mode=\"aspectFit\" />\r\n\t\t\t\t\t\t\t\t<text class=\"flow-text\">财务流水</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view v-else class=\"workspace-tab\">\r\n\t\t\t\t<!-- 工作台二级Tab -->\r\n\t\t\t\t<view class=\"workspace-tab-container\">\r\n\t\t\t\t\t<view class=\"workspace-tab-bar\">\r\n\t\t\t\t\t\t<view v-for=\"(tab, idx) in workspaceTabs\" :key=\"tab\"\r\n\t\t\t\t\t\t\t:class=\"['workspace-tab-item', { active: activeWorkspaceTab === idx }]\"\r\n\t\t\t\t\t\t\t@click=\"activeWorkspaceTab = idx\">\r\n\t\t\t\t\t\t\t<view class=\"tab-button\">\r\n\t\t\t\t\t\t\t\t<text class=\"workspace-tab-text\">{{ tab }}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 工作台内容区 -->\r\n\t\t\t\t<view class=\"workspace-content\">\r\n\t\t\t\t\t<!-- 我的收藏 -->\r\n\t\t\t\t\t<view v-if=\"activeWorkspaceTab === 0\" class=\"favorites-content\">\r\n\t\t\t\t\t\t<view class=\"favorite-card\" v-for=\"(item, index) in favoritesList\" :key=\"index\">\r\n\t\t\t\t\t\t\t<view class=\"favorite-header\">\r\n\t\t\t\t\t\t\t\t<text class=\"favorite-date\">{{ item.collectTime }}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"favorite-content\">\r\n\t\t\t\t\t\t\t\t<text class=\"content-text\">{{ item.contentPreview }}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"favorite-actions\">\r\n\t\t\t\t\t\t\t\t<view class=\"action-btn\" @tap=\"handleCopy(item)\">\r\n\t\t\t\t\t\t\t\t\t<image src=\"@/static/my/<EMAIL>\" class=\"action-icon\" mode=\"aspectFit\" />\r\n\t\t\t\t\t\t\t\t\t<text class=\"action-text\">复制</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"action-btn\" @tap=\"handleViewAll(item)\">\r\n\t\t\t\t\t\t\t\t\t<image src=\"@/static/my/<EMAIL>\" class=\"action-icon\" mode=\"aspectFit\" />\r\n\t\t\t\t\t\t\t\t\t<text class=\"action-text\">查看全部</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"action-btn\" @tap=\"handleUnfavorite(item)\">\r\n\t\t\t\t\t\t\t\t\t<image src=\"@/static/my/<EMAIL>\" class=\"action-icon\" mode=\"aspectFit\" />\r\n\t\t\t\t\t\t\t\t\t<text class=\"action-text\">取消收藏</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<!-- 查看更多按钮 -->\r\n\t\t\t\t\t\t<view class=\"view-more-btn\" @tap=\"handleViewMoreFavorites\" v-if=\"favoritesList.length > 0\">\r\n\t\t\t\t\t\t\t<text class=\"view-more-text\">查看更多</text>\r\n\t\t\t\t\t\t\t<image src=\"@/static/index/<EMAIL>\" class=\"arrow-icon\" mode=\"aspectFit\" />\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"empty-state\" v-if=\"favoritesList.length === 0\">\r\n\t\t\t\t\t\t\t<text class=\"empty-text\">暂无收藏的内容</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<!-- 我的数字人 -->\r\n\t\t\t\t\t<view v-else class=\"digital-person-content\">\r\n\t\t\t\t\t\t<!-- 剩余算力 -->\r\n\t\t\t\t\t\t<view class=\"computing-power\">\r\n\t\t\t\t\t\t\t<view class=\"power-info\">\r\n\t\t\t\t\t\t\t\t<image class=\"power-icon\" src=\"@/static/my/desk_balance_icon.png\" mode=\"aspectFit\" />\r\n\t\t\t\t\t\t\t\t<text class=\"power-text\">剩余算力: <text class=\"power-text_number\">{{userInfo.chat_count}}</text></text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"recharge-btn\" @tap=\"openRechargeModal\">去充值</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<!-- 数字人IP形象卡片 -->\r\n\t\t\t\t\t\t<view class=\"digital-person-banner\" @tap=\"goToMyFigure\">\r\n\t\t\t\t\t\t\t<view class=\"banner-content\">\r\n\t\t\t\t\t\t\t\t<image class=\"banner-image banner-image-radius\" src=\"@/static/my/desk_banner.png\" mode=\"aspectFit\" />\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<!-- 功能区域 -->\r\n\t\t\t\t\t\t<view class=\"function-area\">\r\n\t\t\t\t\t\t\t<view class=\"function-row\">\r\n\t\t\t\t\t\t\t\t<view class=\"function-item\" @tap=\"goToMyVideoCreate\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"function-info\">\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"function-title\">创建视频</text>\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"function-desc\">一键开启视频创作</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<image class=\"function-icon\" src=\"@/static/my/desk_works-left.png\" mode=\"aspectFit\" />\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"function-item\" @tap=\"goToMyWorks\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"function-info\">\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"function-title\">我的作品</text>\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"function-desc\">生成专属身份</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<image class=\"function-icon\" src=\"@/static/my/desk_works-right.png\" mode=\"aspectFit\" />\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 自定义充值弹窗 -->\r\n\t\t<view v-if=\"showRechargeModal\" class=\"recharge-overlay\">\r\n\t\t\t<view class=\"recharge-modal\" @tap.stop>\r\n\t\t\t\t<view class=\"modal-header\">\r\n\t\t\t\t\t<text class=\"modal-title\">充值点数</text>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"recharge-grid\" v-if=\"rechargeOptions.length > 0\">\r\n\t\t\t\t\t<view\r\n\t\t\t\t\t\tv-for=\"(option, index) in rechargeOptions\"\r\n\t\t\t\t\t\t:key=\"option.guid || index\"\r\n\t\t\t\t\t\tclass=\"recharge-item\"\r\n\t\t\t\t\t\t:class=\"{ active: selectedOption === index }\"\r\n\t\t\t\t\t\t@tap=\"selectOption(index)\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<text class=\"item-count\">{{ option.goodsName || `${option.count}次` }}</text>\r\n\t\t\t\t\t\t<text class=\"item-price\">¥{{ option.price }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view v-else class=\"no-data\">\r\n\t\t\t\t\t<text class=\"no-data-text\">暂无数据</text>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"payment-section\">\r\n\t\t\t\t\t<text class=\"payment-text\">付款金额：</text>\r\n\t\t\t\t\t<text class=\"payment-price\">¥{{ selectedPrice || '50.00' }}</text>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"pay-button\" @tap=\"confirmRecharge\">\r\n\t\t\t\t\t<text class=\"pay-text\">确认充值</text>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"close-icon-wrapper\" @tap=\"closeRechargeModal\">\r\n\t\t\t\t\t<image class=\"close-icon\" src=\"/static/my/popup-close.png\" mode=\"aspectFit\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 查看全部弹窗 -->\r\n\t\t<view v-if=\"showContentModal\" class=\"modal-overlay\" @tap=\"closeContentModal\" catchtouchmove=\"true\">\r\n\t\t\t<view class=\"modal-content\" @tap.stop>\r\n\t\t\t\t<view class=\"modal-header\">\r\n\t\t\t\t\t<text class=\"modal-title\">消息详情</text>\r\n\t\t\t\t\t<view class=\"close-btn\" @tap=\"closeContentModal\">\r\n\t\t\t\t\t\t<text class=\"close-text\">×</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<scroll-view scroll-y=\"true\" class=\"modal-body\" show-scrollbar=\"false\">\r\n\t\t\t\t\t<view class=\"rich-content\">\r\n\t\t\t\t\t\t<rich-text :nodes=\"currentContent\"></rich-text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</scroll-view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script setup>\r\n\r\nimport { reactive, ref, watch, computed } from 'vue'\r\nimport { onShow, onLoad } from '@dcloudio/uni-app';\r\nimport { getUserInfoApi, getMyAgentListApi, getMyCollectionListApi, cancelCollectMessageApi, getUserVipInfoApi, bindInvitationApi, deleteAgentApi, getMyEarningsApi, getChatGoodsApi, buyChatGoodsApi, queryPayChatStautsApi } from '@/api/index.js'\r\nimport {\r\n\tuseUserStore\r\n} from '@/stores/user.js';\r\nimport { miniPay } from '@/api/common.js';\r\n\r\nconst vipIcon = 'https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/c4d5b3ada53740d5b862f274ce48ef0c.png'\r\n\r\nconst userStore = useUserStore()\r\nconst tabs = ['我的智能体', '共创收益', '我的工作台']\r\nconst activeTab = ref(0)\r\n\r\n// 工作台子Tab\r\nconst workspaceTabs = ['我的收藏', '我的数字人']\r\nconst activeWorkspaceTab = ref(1) // 默认选中\"我的数字人\"\r\n\r\n// 充值相关\r\nconst selectedOption = ref(0)\r\nconst rechargeOptions = ref([])\r\n\r\nconst selectedPrice = computed(() => {\r\n\treturn rechargeOptions.value[selectedOption.value]?.price || '0.00'\r\n})\r\n\r\n// 获取聊天点数商品列表\r\nconst getChatGoods = async () => {\r\n\ttry {\r\n\t\tlet res = await getChatGoodsApi()\r\n\t\tif (res.code === 0 && res.data) {\r\n\t\t\trechargeOptions.value = res.data\r\n\t\t} else {\r\n\t\t\trechargeOptions.value = []\r\n\t\t}\r\n\t} catch (error) {\r\n\t\tconsole.error('获取聊天点数商品列表失败:', error)\r\n\t\trechargeOptions.value = []\r\n\t}\r\n}\r\nconst agentTypes = ref([\r\n\t{ label: '内部', value: 1 },\r\n\t{ label: 'dify', value: 2 },\r\n\t{ label: 'coze', value: 3 },\r\n\t{ label: '阿里云百炼', value: 4 }\r\n])\r\n\r\n//审批状态：1-待审核；2-审核通过；3-审核拒绝\r\nconst auditStatus = {\r\n\t1: '待审核',\r\n\t2: '审核通过',\r\n\t3: '审核拒绝'\r\n}\r\n// 智能体列表数据\r\nconst agentList = ref([])\r\nconst getMyAgentList = async () => {\r\n\tlet res = await getMyAgentListApi({\r\n\t\tmerchantGuid: userStore.merchantGuid\r\n\t})\r\n\tagentList.value = res.data.data\r\n}\r\nimport rule1 from '@/static/my/<EMAIL>'\r\nimport rule2 from '@/static/my/<EMAIL>'\r\nimport rule3 from '@/static/my/<EMAIL>'\r\n// 共创规则数据\r\nconst rulesList = ref([\r\n\t{ name: '推广规则', icon: rule1 },\r\n\t{ name: '创作规则', icon: rule2 },\r\n\t{ name: '平台规则', icon: rule3 }\r\n])\r\n\r\n// 收益数据\r\nconst incomeData = ref({\r\n\tavailableAmount: \"0.00\",\r\n\tavailableAmountText: \"¥0.00\",\r\n\tfrozenAmount: \"0.00\",\r\n\tfrozenAmountText: \"¥0.00\",\r\n\ttotalEarnings: \"0.00\",\r\n\ttotalEarningsText: \"¥0.00\",\r\n\ttotalWithdrawn: \"0.00\",\r\n\ttotalWithdrawnText: \"¥0.00\"\r\n})\r\n\r\n// 收藏列表数据\r\nconst favoritesList = ref([])\r\nlet collectReq = reactive({\r\n\tmerchantGuid: userStore.merchantGuid,\r\n\tpage: 1,\r\n\tpageSize: 10\r\n})\r\n\r\n// 弹窗相关状态\r\nconst showContentModal = ref(false)\r\nconst currentContent = ref('')\r\nconst showRechargeModal = ref(false)\r\n\r\nconst getMyCollectionList = async () => {\r\n\ttry {\r\n\t\tlet res = await getMyCollectionListApi(collectReq)\r\n\t\tfavoritesList.value = res.data.list\r\n\t} catch (error) {\r\n\t\tconsole.error('获取收藏列表失败:', error)\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '加载失败',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t}\r\n}\r\n// 事件处理函数\r\nconst handleEditProfile = () => {\r\n\tuni.navigateTo({\r\n\t\turl: '/pages/profile/index'\r\n\t})\r\n}\r\n//进入聊天页\r\nconst handleAgentClick = (agent) => {\r\n\tuni.navigateTo({\r\n\t\turl: `/pages/msg/index?sessionGuid=${agent.guid}`\r\n\t})\r\n}\r\nconst handleOpenVip = () => {\r\n\tuni.navigateTo({\r\n\t\turl: '/pages/my/vip-list'\r\n\t})\r\n}\r\n\r\nconst handleOpenSub = () => {\r\n\tuni.navigateTo({\r\n\t\turl: '/pages/my/sub-list'\r\n\t})\r\n}\r\n\r\nconst handleCreateAgent = () => {\r\n\tuni.navigateTo({\r\n\t\turl: '/pages/create-agent/index'\r\n\t})\r\n}\r\n\r\nconst handleEditAgent = (agent) => {\r\n\tuni.navigateTo({\r\n\t\turl: `/pages/create-agent/index?guid=${agent.guid}`\r\n\t})\r\n}\r\nconst handleDeleteAgent = async (agent) => {\r\n\tuni.showModal({\r\n\t\ttitle: '提示',\r\n\t\tcontent: '确定要删除该智能体吗？',\r\n\t\tsuccess: async (res) => {\r\n\t\t\tif (res.confirm) {\r\n\t\t\t\tawait deleteAgentApi({\r\n\t\t\t\t\tmerchantGuid: userStore.merchantGuid,\r\n\t\t\t\t\tagentGuid: agent.guid\r\n\t\t\t\t})\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '删除成功',\r\n\t\t\t\t\ticon: 'success'\r\n\t\t\t\t})\r\n\t\t\t\tgetMyAgentList()\r\n\t\t\t}\r\n\t\t}\r\n\t})\r\n\r\n}\r\nconst handleRuleClick = (index) => {\r\n\tif (index === 0) {\r\n\t\tuni.navigateTo({\r\n\t\t\turl: '/pages/rule/tg-index'\r\n\t\t})\r\n\t}\r\n\tif (index === 1) {\r\n\t\tuni.navigateTo({\r\n\t\t\turl: '/pages/rule/cz-index'\r\n\t\t})\r\n\t}\r\n\tif (index === 2) {\r\n\t\tuni.navigateTo({\r\n\t\t\turl: '/pages/rule/pt-index'\r\n\t\t})\r\n\t}\r\n\r\n}\r\n\r\nconst handleWithdraw = () => {\r\n\tconsole.log('提现')\r\n}\r\n\r\nconst handleWithdrawRecord = () => {\r\n\tuni.navigateTo({\r\n\t\turl: '/pages/raw-record/index'\r\n\t})\r\n}\r\n\r\nconst handleFinanceFlow = () => {\r\n\tuni.navigateTo({\r\n\t\turl: '/pages/finance/index'\r\n\t})\r\n}\r\n\r\nconst handleCopy = (item) => {\r\n\tuni.setClipboardData({\r\n\t\tdata: item.messageContent,\r\n\t\tsuccess() {\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '复制成功',\r\n\t\t\t\ticon: 'none'\r\n\t\t\t})\r\n\t\t}\r\n\t})\r\n}\r\n\r\nconst handleViewAll = (item) => {\r\n\tcurrentContent.value = item.messageContent || ''\r\n\tshowContentModal.value = true\r\n}\r\n\r\nconst closeContentModal = () => {\r\n\tshowContentModal.value = false\r\n\tcurrentContent.value = ''\r\n}\r\n\r\n// 充值相关方法\r\nconst openRechargeModal = async () => {\r\n\tawait getChatGoods() // 打开弹窗时获取最新的商品列表\r\n\tshowRechargeModal.value = true\r\n}\r\n\r\nconst closeRechargeModal = () => {\r\n\tshowRechargeModal.value = false\r\n}\r\n\r\nconst selectOption = (index) => {\r\n\tselectedOption.value = index\r\n}\r\n\r\nconst confirmRecharge = async () => {\r\n\tconst option = rechargeOptions.value[selectedOption.value]\r\n\tif (!option) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请选择充值选项',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\r\n\ttry {\r\n\t\tuni.showLoading({\r\n\t\t\ttitle: '正在创建订单...',\r\n\t\t\tmask: true\r\n\t\t})\r\n\t\t\r\n\t\t// 调用购买聊天点数接口\r\n\t\tconst payInfo = await buyChatGoodsApi({\r\n\t\t\tchatGoodsGuid: option.guid,\r\n\t\t\tpayEnv: 'xcx'\r\n\t\t})\r\n\r\n\t\tuni.hideLoading()\r\n\t\t// 调用微信支付\r\n\t\tminiPay(payInfo.data).then(\r\n\t\t\tasync () => {\r\n\t\t\t\tqueryPayChatStatus(payInfo.data.orderNo, 0)\r\n\t\t\t},\r\n\t\t\t(res) => {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: res.msg || '支付失败',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t)\r\n\t} catch (error) {\r\n\t\tuni.hideLoading()\r\n\t\tuni.showToast({\r\n\t\t\ttitle: error.message || '创建订单失败',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t}\r\n}\r\n\r\n// 查询聊天点数支付状态\r\nconst queryPayChatStatus = async (orderNo, number) => {\r\n\tnumber++\r\n\ttry {\r\n\t\tconst orderInfo = await queryPayChatStautsApi({\r\n\t\t\torderNo\r\n\t\t})\r\n\t\tif (orderInfo.data.isPay) {\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '充值成功',\r\n\t\t\t\ticon: 'success',\r\n\t\t\t\tduration: 3000\r\n\t\t\t})\r\n\t\t\tcloseRechargeModal()\r\n\t\t\t// 刷新用户信息，更新点数显示\r\n\t\t\tif (userStore.userToken) {\r\n\t\t\t\tgetVipInfo()\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\tqueryPayChatStatus(orderNo, number)\r\n\t\t\t}, 2000)\r\n\t\t}\r\n\t} catch (error) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: error.msg || '查询支付状态失败',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t}\r\n}\r\n\r\n// 跳转到我的作品页面\r\nconst goToMyWorks = () => {\r\n\tuni.navigateTo({\r\n\t\turl: '/pages/my/works-list'\r\n\t})\r\n}\r\n// 跳转到我的IP形象列表\r\nconst goToMyFigure = () => {\r\n\tuni.navigateTo({\r\n\t\turl: '/pages/my/figure-list'\r\n\t})\r\n}\r\n\r\n// 跳转到创建视频\r\nconst goToMyVideoCreate = () => {\r\n\tuni.navigateTo({\r\n\t\turl: '/pages/my/video-create'\r\n\t})\r\n\treturn\r\n\tshowVideoModal.value = true\r\n\tselectedTemplate.value = null // 重置选中状态\r\n\tactivePrimaryTab.value = 0 // 重置到模板页面\r\n}\r\n\r\nconst handleUnfavorite = async (item) => {\r\n\tawait cancelCollectMessageApi({\r\n\t\tmerchantGuid: userStore.merchantGuid,\r\n\t\tmessageGuid: item.messageGuid\r\n\t})\r\n\tuni.showToast({\r\n\t\ttitle: '取消收藏成功',\r\n\t\ticon: 'none'\r\n\t})\r\n\t// 重新加载\r\n\tcollectReq.page = 1\r\n\tgetMyCollectionList()\r\n}\r\n\r\n// 查看更多智能体\r\nconst handleViewMoreAgents = () => {\r\n\tuni.navigateTo({\r\n\t\turl: '/pages/my/agent-list'\r\n\t})\r\n}\r\n\r\n// 查看更多收藏\r\nconst handleViewMoreFavorites = () => {\r\n\tuni.navigateTo({\r\n\t\turl: '/pages/my/favorite-list'\r\n\t})\r\n}\r\nlet userInfo = reactive({\r\n\theadImgUrl: '',\r\n\temail: '',\r\n\tnickname: ''\r\n})\r\nconst getUserInfo = async () => {\r\n\tlet res = await getUserInfoApi()\r\n\tuserInfo = Object.assign(userInfo, res.data)\r\n}\r\n//获取我的收益\r\nconst getMyEarnings = async () => {\r\n\tlet res = await getMyEarningsApi({\r\n\t\tmerchantGuid: userStore.merchantGuid\r\n\t})\r\n\tconsole.log(res.data, '---------res.data')\r\n\tincomeData.value = res.data\r\n}\r\nwatch(\r\n\t() => userStore.userToken,\r\n\t(newValue, oldValue) => {\r\n\t\tif (newValue && oldValue === '') {\r\n\t\t\tgetUserInfo()\r\n\t\t\tgetMyAgentList()\r\n\t\t\tbindInvitation()\r\n\t\t\tgetMyEarnings()\r\n\t\t\tgetChatGoods() // 获取聊天点数商品列表\r\n\t\t}\r\n\t}\r\n);\r\nlet invite = ref('')\r\nconst bindInvitation = async () => {\r\n\ttry {\r\n\t\tconsole.log('invite.value', invite.value)\r\n\t\tawait bindInvitationApi({\r\n\t\t\tmerchantGuid: userStore.merchantGuid,\r\n\t\t\tinvitationCode: invite.value\r\n\t\t})\r\n\t} catch (error) {\r\n\t\tconsole.error('绑定邀请码失败:', error)\r\n\t}\r\n}\r\nonLoad((params) => {\r\n\tif (params.invite) {\r\n\t\tinvite.value = params.invite;\r\n\t\tif (userStore.userToken) {\r\n\t\t\tbindInvitation()\r\n\t\t}\r\n\t}\r\n})\r\n//获取会员信息\r\nlet vipInfo = reactive({\r\n\thasMembership: false,\r\n\tisExpired: true,\r\n\tmembership: null,\r\n\tremainingDays: 0,\r\n\tcreatorSubscriptionCount: 0,\r\n\tcategorySubscriptionCount: 0,\r\n\tuserPoints: {\r\n\t\ttotalPointsText: ''\r\n\t}\r\n\r\n})\r\nconst getVipInfo = async () => {\r\n\ttry {\r\n\t\tlet res = await getUserVipInfoApi({\r\n\t\t\tmerchantGuid: userStore.merchantGuid\r\n\t\t})\r\n\t\tvipInfo = Object.assign(vipInfo, res.data)\r\n\t\tconsole.log(res, '会员信息')\r\n\t} catch (error) {\r\n\t\tconsole.error('获取会员信息失败:', error)\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '加载失败',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t}\r\n}\r\n\r\nonShow(() => {\r\n\tif (userStore.userToken) {\r\n\t\tgetUserInfo()\r\n\t\tgetMyAgentList()\r\n\t\tgetMyCollectionList()\r\n\t\tgetVipInfo()\r\n\t\tgetMyEarnings()\r\n\t\tgetChatGoods() // 获取聊天点数商品列表\r\n\t}\r\n})\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.vip-card-warp {\r\n\tbackground-color: #ffffff;\r\n\tpadding-bottom: 20px;\r\n\tmargin-bottom: 32rpx;\r\n}\r\n\r\n.vip-card {\r\n\twidth: 690rpx;\r\n\theight: 220rpx;\r\n\tmargin: 0 auto 0 auto;\r\n\tbackground: linear-gradient(90deg, #F9F0E3 0%, #F7DFC1 100%);\r\n\tborder-radius: 25rpx;\r\n\tborder: 1rpx solid #F4E5C1;\r\n\t// overflow: hidden;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\r\n\t.info-box {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tposition: relative;\r\n\t\tflex: 1;\r\n\t\tjustify-content: center;\r\n\t\tpadding-left: 20px;\r\n\r\n\t\t.title {\r\n\t\t\tfont-size: 30rpx;\r\n\t\t\tcolor: #54371A;\r\n\t\t\tmargin-bottom: 10px;\r\n\r\n\t\t\t.day {\r\n\t\t\t\tfont-size: 22rpx;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.label {\r\n\t\t\tfont-size: 22rpx;\r\n\t\t\tcolor: #9E876A;\r\n\t\t}\r\n\r\n\t\t.icon-box {\r\n\t\t\tposition: absolute;\r\n\t\t\tright: 20px;\r\n\t\t\ttop: -26px;\r\n\r\n\t\t\t.icon {\r\n\t\t\t\twidth: 133rpx;\r\n\t\t\t\theight: 133rpx;\r\n\t\t\t\tdisplay: block;\r\n\t\t\t}\r\n\r\n\t\t\t.text {\r\n\t\t\t\twidth: 136rpx;\r\n\t\t\t\theight: 50rpx;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\tcolor: #FDF0E0;\r\n\t\t\t\tbackground-color: #543718;\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tright: 0;\r\n\t\t\t\tbottom: 0;\r\n\t\t\t\tborder-radius: 33rpx;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t}\r\n\r\n\t.dy-box {\r\n\t\theight: 70rpx;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tpadding: 0 30rpx;\r\n\t\tcolor: #DECAAF;\r\n\t\tbackground: linear-gradient(90deg, #281905 0%, #533818 100%);\r\n\t\tborder-bottom-right-radius: 25rpx;\r\n\t\tborder-bottom-left-radius: 25rpx;\r\n\r\n\t\t.left {\r\n\t\t\tfont-size: 22rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\r\n\t\t\t.icon {\r\n\t\t\t\tdisplay: block;\r\n\t\t\t\twidth: 30rpx;\r\n\t\t\t\theight: 30rpx;\r\n\t\t\t\tmargin-right: 6rpx;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.right {\r\n\t\t\tfont-size: 25rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\r\n\t\t\t.icon {\r\n\t\t\t\tdisplay: block;\r\n\t\t\t\twidth: 25rpx;\r\n\t\t\t\theight: 25rpx;\r\n\t\t\t\tmargin-left: 6rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.modal-overlay {\r\n\tposition: fixed;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tbottom: 0;\r\n\tbackground: rgba(0, 0, 0, 0.5);\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tz-index: 1000;\r\n\toverflow: hidden;\r\n\ttouch-action: none;\r\n\r\n\t.modal-content {\r\n\t\tbackground: #ffffff;\r\n\t\twidth: 90%;\r\n\t\theight: 600px;\r\n\t\tborder-radius: 24rpx;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\toverflow: hidden;\r\n\r\n\t\t.modal-header {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\tpadding: 32rpx;\r\n\t\t\tborder-bottom: 1px solid #F0F0F0;\r\n\t\t\tflex-shrink: 0;\r\n\r\n\t\t\t.modal-title {\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tfont-weight: 600;\r\n\t\t\t\tcolor: #1a1a1a;\r\n\t\t\t}\r\n\r\n\t\t\t.close-btn {\r\n\t\t\t\twidth: 48rpx;\r\n\t\t\t\theight: 48rpx;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\tborder-radius: 50%;\r\n\t\t\t\tbackground: #F5F5F5;\r\n\r\n\t\t\t\t.close-text {\r\n\t\t\t\t\tfont-size: 36rpx;\r\n\t\t\t\t\tcolor: #666666;\r\n\t\t\t\t\tline-height: 1;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.modal-body {\r\n\t\t\tflex: 1;\r\n\t\t\theight: 0;\r\n\t\t\toverflow: hidden;\r\n\r\n\t\t\t.rich-content {\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tline-height: 1.6;\r\n\t\t\t\tcolor: #1a1a1a;\r\n\t\t\t\tword-break: break-all;\r\n\t\t\t\tpadding: 32rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.profile-page {\r\n\tbackground: #F5F5F5;\r\n\tmin-height: 100vh;\r\n\r\n\t.header {\r\n\t\tdisplay: flex;\r\n\t\t//flex-direction: column;\r\n\t\t// align-items: center;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tpadding: 60rpx 32rpx 40rpx;\r\n\t\tbackground: #ffffff;\r\n\r\n\r\n\t\t.user-info {\r\n\t\t\tdisplay: flex;\r\n\t\t\t//flex-direction: column;\r\n\t\t\talign-items: center;\r\n\r\n\t\t\t.avatar {\r\n\t\t\t\twidth: 120rpx;\r\n\t\t\t\theight: 120rpx;\r\n\t\t\t\tborder-radius: 50%;\r\n\t\t\t}\r\n\r\n\t\t\t.info {\r\n\t\t\t\t// text-align: center;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tflex-direction: column;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\tmargin-left: 10px;\r\n\r\n\t\t\t\t.name {\r\n\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\tfont-weight: 600;\r\n\t\t\t\t\tcolor: #1a1a1a;\r\n\t\t\t\t\tmargin-bottom: 8rpx;\r\n\t\t\t\t\tdisplay: block;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.email {\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\tcolor: #999999;\r\n\t\t\t\t\tdisplay: block;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.edit-btn {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\t\t\tbackground: #F5F5F5;\r\n\t\t\tborder-radius: 32rpx;\r\n\t\t\tborder: none;\r\n\t\t\twidth: 174rpx;\r\n\t\t\theight: 60rpx;\r\n\r\n\t\t\t// .edit-icon {\r\n\t\t\t// \twidth: 28rpx;\r\n\t\t\t// \theight: 28rpx;\r\n\t\t\t// \tmargin-right: 8rpx;\r\n\t\t\t// }\r\n\r\n\t\t\t.edit-text {\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tcolor: #333;\r\n\t\t\t\tfont-weight: 400;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.tab-container {\r\n\t\tbackground: #ffffff;\r\n\t\tborder-bottom: 1px solid #F0F0F0;\r\n\r\n\t\t.tab-bar {\r\n\t\t\tdisplay: flex;\r\n\t\t\tpadding: 0 32rpx;\r\n\r\n\t\t\t.tab {\r\n\t\t\t\tflex: 1;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tpadding: 32rpx 0;\r\n\t\t\t\tposition: relative;\r\n\r\n\t\t\t\t.tab-text {\r\n\t\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\t\tcolor: #999999;\r\n\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\ttransition: all 0.3s ease;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t&.active {\r\n\t\t\t\t\t.tab-text {\r\n\t\t\t\t\t\tcolor: #1a1a1a;\r\n\t\t\t\t\t\tfont-weight: 600;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t&::after {\r\n\t\t\t\t\t\tcontent: '';\r\n\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\tbottom: 0;\r\n\t\t\t\t\t\tleft: 50%;\r\n\t\t\t\t\t\ttransform: translateX(-50%);\r\n\t\t\t\t\t\twidth: 60rpx;\r\n\t\t\t\t\t\theight: 6rpx;\r\n\t\t\t\t\t\tbackground: #3478f6;\r\n\t\t\t\t\t\tborder-radius: 3rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.tab-content {\r\n\t\t// background: #ffffff;\r\n\t\tmin-height: calc(100vh - 400rpx);\r\n\t}\r\n\r\n\t.agents-tab {\r\n\t\tpadding: 32rpx;\r\n\r\n\t\t.agents-list {\r\n\t\t\t.agent-card {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tbackground: #fff;\r\n\t\t\t\tborder-radius: 24rpx;\r\n\t\t\t\tmargin-bottom: 24rpx;\r\n\t\t\t\tpadding: 32rpx;\r\n\t\t\t\tposition: relative;\r\n\r\n\t\t\t\t.agent-avatar {\r\n\t\t\t\t\twidth: 96rpx;\r\n\t\t\t\t\theight: 96rpx;\r\n\t\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t\tmargin-right: 24rpx;\r\n\t\t\t\t\tflex-shrink: 0;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.agent-info {\r\n\t\t\t\t\tflex: 1;\r\n\r\n\t\t\t\t\t.agent-title {\r\n\t\t\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\t\t\tfont-weight: 600;\r\n\t\t\t\t\t\tcolor: #222;\r\n\t\t\t\t\t\tmargin-bottom: 8rpx;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.agent-desc {\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\tcolor: #999;\r\n\t\t\t\t\t\tline-height: 1.5;\r\n\t\t\t\t\t\tmargin-bottom: 16rpx;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.agent-tags {\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\tgap: 12rpx;\r\n\t\t\t\t\t\tflex-wrap: wrap;\r\n\t\t\t\t\t\tmargin-top: 16rpx;\r\n\r\n\t\t\t\t\t\t.tag {\r\n\t\t\t\t\t\t\tborder-radius: 16rpx;\r\n\t\t\t\t\t\t\tpadding: 8rpx 16rpx;\r\n\t\t\t\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\t\t\t\tfont-weight: 500;\r\n\r\n\t\t\t\t\t\t\t&.primary {\r\n\t\t\t\t\t\t\t\tbackground: #E6F0FF;\r\n\t\t\t\t\t\t\t\tcolor: #3478f6;\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t&.warning {\r\n\t\t\t\t\t\t\t\tbackground: #FFF7E6;\r\n\t\t\t\t\t\t\t\tcolor: #FF8C00;\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t&.success {\r\n\t\t\t\t\t\t\t\tbackground: #F0F9E6;\r\n\t\t\t\t\t\t\t\tcolor: #52C41A;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.operate-box {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tflex-direction: column;\r\n\t\t\t\t\tgap: 10px;\r\n\r\n\t\t\t\t\t.edit {\r\n\t\t\t\t\t\twidth: 40px;\r\n\t\t\t\t\t\theight: 24px;\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\tbackground-color: #3478f6;\r\n\t\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\tborder-radius: 16px;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.delete {\r\n\t\t\t\t\t\twidth: 40px;\r\n\t\t\t\t\t\theight: 24px;\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\tbackground-color: #e60000;\r\n\t\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\tborder-radius: 16px;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.status {\r\n\t\t\t\t\twidth: 140rpx;\r\n\t\t\t\t\theight: 40rpx;\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\tright: 0;\r\n\t\t\t\t\ttop: 0;\r\n\t\t\t\t\tborder-radius: 0rpx 30rpx 0rpx 30rpx;\r\n\t\t\t\t\tbackground-color: rgba(253, 141, 43, 0.12);\r\n\t\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\t\tcolor: #FD8D2B;\r\n\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\tline-height: 40rpx;\r\n\r\n\t\t\t\t\t&.success {\r\n\t\t\t\t\t\tbackground-color: #3478f6;\r\n\t\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.view-more-btn {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\t\t\t//background: #F8F9FA;\r\n\t\t\tborder-radius: 24rpx;\r\n\t\t\tpadding: 24rpx;\r\n\t\t\tmargin: 24rpx 0;\r\n\t\t\t//border: 1px solid #E6F0FF;\r\n\r\n\t\t\t.view-more-text {\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tcolor: #3478f6;\r\n\t\t\t\tmargin-right: 8rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.arrow-icon {\r\n\t\t\t\twidth: 24rpx;\r\n\t\t\t\theight: 24rpx;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.create-agent {\r\n\t\t\tmargin-bottom: 30rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: center;\r\n\t\t\t// position: fixed;\r\n\t\t\twidth: 100%;\r\n\t\t\tbottom: 40px;\r\n\t\t\tleft: 0;\r\n\r\n\t\t\t.create-btn {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\theight: 90rpx;\r\n\t\t\t\tbackground: #3478f6;\r\n\t\t\t\tborder-radius: 48rpx;\r\n\t\t\t\tborder: none;\r\n\r\n\t\t\t\t.create-icon {\r\n\t\t\t\t\twidth: 32rpx;\r\n\t\t\t\t\theight: 32rpx;\r\n\t\t\t\t\tmargin-right: 12rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.create-text {\r\n\t\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\t\tcolor: #ffffff;\r\n\t\t\t\t\tline-height: 1;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.income-tab {\r\n\t\tpadding: 32rpx;\r\n\r\n\t\t.income-content {\r\n\t\t\t.section-title {\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tfont-weight: 600;\r\n\t\t\t\tcolor: #1a1a1a;\r\n\t\t\t\tmargin-bottom: 24rpx;\r\n\t\t\t\tdisplay: block;\r\n\t\t\t}\r\n\r\n\t\t\t.rules-section {\r\n\t\t\t\tmargin-bottom: 40rpx;\r\n\t\t\t\tbackground-color: #fff;\r\n\t\t\t\tpadding: 20rpx 10rpx;\r\n\t\t\t\tborder: 30rpx;\r\n\r\n\t\t\t\t.rules-grid {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tgap: 40rpx;\r\n\t\t\t\t\tjustify-content: space-around;\r\n\t\t\t\t\tpadding: 0 10rpx;\r\n\r\n\t\t\t\t\t.rule-item {\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\tflex-direction: column;\r\n\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\tflex: 1;\r\n\t\t\t\t\t\tpadding: 10px 0;\r\n\t\t\t\t\t\tbackground-color: #F9FAFB;\r\n\t\t\t\t\t\tborder: 30rpx;\r\n\r\n\t\t\t\t\t\t.rule-icon {\r\n\t\t\t\t\t\t\twidth: 64rpx;\r\n\t\t\t\t\t\t\theight: 64rpx;\r\n\t\t\t\t\t\t\tmargin-bottom: 16rpx;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t.rule-text {\r\n\t\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t\t\tcolor: #666666;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.my-income-section {\r\n\t\t\t\tmargin-bottom: 40rpx;\r\n\t\t\t\tbackground-color: #fff;\r\n\t\t\t\tpadding: 20rpx 10rpx;\r\n\t\t\t\tborder: 30rpx;\r\n\r\n\t\t\t\t.income-card {\r\n\t\t\t\t\tbackground: #F8F9FA;\r\n\t\t\t\t\tborder-radius: 24rpx;\r\n\t\t\t\t\tpadding: 32rpx;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tjustify-content: space-between;\r\n\t\t\t\t\tmargin: 0 10px;\r\n\r\n\t\t\t\t\t.income-amount {\r\n\t\t\t\t\t\t.amount-value {\r\n\t\t\t\t\t\t\tfont-size: 48rpx;\r\n\t\t\t\t\t\t\tfont-weight: 700;\r\n\t\t\t\t\t\t\tcolor: #1a1a1a;\r\n\t\t\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\t\t\tmargin-bottom: 8rpx;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t.amount-unit {\r\n\t\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t\t\tcolor: #666666;\r\n\t\t\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.withdraw-btn {\r\n\t\t\t\t\t\tbackground: #3478f6;\r\n\t\t\t\t\t\tborder-radius: 32rpx;\r\n\t\t\t\t\t\tpadding: 20rpx 40rpx;\r\n\t\t\t\t\t\tborder: none;\r\n\r\n\t\t\t\t\t\t.withdraw-text {\r\n\t\t\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\t\t\tcolor: #ffffff;\r\n\t\t\t\t\t\t\tfont-weight: 600;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.overview-section {\r\n\t\t\t\tmargin-bottom: 40rpx;\r\n\t\t\t\tbackground-color: #fff;\r\n\t\t\t\tpadding: 20rpx 10rpx;\r\n\t\t\t\tborder: 30rpx;\r\n\r\n\t\t\t\t.overview-grid {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tgap: 24rpx;\r\n\t\t\t\t\tmargin: 0 10px;\r\n\r\n\t\t\t\t\t.overview-item {\r\n\t\t\t\t\t\tflex: 1;\r\n\t\t\t\t\t\tbackground: #F8F9FA;\r\n\t\t\t\t\t\tborder-radius: 24rpx;\r\n\t\t\t\t\t\tpadding: 32rpx 24rpx;\r\n\t\t\t\t\t\ttext-align: center;\r\n\r\n\t\t\t\t\t\t.overview-value {\r\n\t\t\t\t\t\t\tfont-size: 36rpx;\r\n\t\t\t\t\t\t\tfont-weight: 700;\r\n\t\t\t\t\t\t\tcolor: #1a1a1a;\r\n\t\t\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\t\t\tmargin-bottom: 8rpx;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t.overview-label {\r\n\t\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\t\tcolor: #666666;\r\n\t\t\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.flow-section {\r\n\t\t\t\tbackground-color: #fff;\r\n\t\t\t\tpadding: 20rpx 10rpx;\r\n\t\t\t\tborder: 30rpx;\r\n\r\n\t\t\t\t.flow-buttons {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tgap: 24rpx;\r\n\t\t\t\t\tmargin: 0 10px;\r\n\r\n\t\t\t\t\t.flow-btn {\r\n\t\t\t\t\t\tflex: 1;\r\n\t\t\t\t\t\tbackground: #F8F9FA;\r\n\t\t\t\t\t\tborder-radius: 24rpx;\r\n\t\t\t\t\t\tpadding: 32rpx 24rpx;\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\t// flex-direction: column;\r\n\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\t\tborder: none;\r\n\r\n\t\t\t\t\t\t.flow-icon {\r\n\t\t\t\t\t\t\twidth: 48rpx;\r\n\t\t\t\t\t\t\theight: 48rpx;\r\n\t\t\t\t\t\t\tmargin-right: 12rpx;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t.flow-text {\r\n\t\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t\t\tcolor: #666666;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.workspace-tab {\r\n\t\tbackground: #F5F5F5;\r\n\t\tpadding: 20rpx 30rpx;\r\n\r\n\t\t.workspace-tab-container {\r\n\t\t\t// padding: 32rpx 32rpx 0 32rpx;\r\n\r\n\t\t\t.workspace-tab-bar {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tgap: 16rpx;\r\n\r\n\t\t\t\t.workspace-tab-item {\r\n\t\t\t\t\t.tab-button {\r\n\t\t\t\t\t\tpadding: 16rpx 32rpx;\r\n\t\t\t\t\t\tborder-radius: 40rpx;\r\n\t\t\t\t\t\tbackground: #ffffff;\r\n\t\t\t\t\t\ttransition: all 0.3s ease;\r\n\t\t\t\t\t\tline-height: 1;\r\n\r\n\t\t\t\t\t\t.workspace-tab-text {\r\n\t\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t\t\tcolor: #999999;\r\n\t\t\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\t\t\ttransition: all 0.3s ease;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t&.active {\r\n\t\t\t\t\t\t.tab-button {\r\n\t\t\t\t\t\t\tbackground: #2B64F6;\r\n\r\n\t\t\t\t\t\t\t.workspace-tab-text {\r\n\t\t\t\t\t\t\t\tcolor: #ffffff;\r\n\t\t\t\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.workspace-content {\r\n\t\t\tpadding-top: 20rpx;\r\n\r\n\t\t\t.favorites-content {\r\n\t\t\t\t.favorite-card {\r\n\t\t\t\t\tbackground: #fff;\r\n\t\t\t\t\tborder-radius: 24rpx;\r\n\t\t\t\t\tmargin-bottom: 24rpx;\r\n\t\t\t\t\tpadding: 32rpx;\r\n\r\n\t\t\t\t\t.favorite-header {\r\n\t\t\t\t\t\tmargin-bottom: 16rpx;\r\n\r\n\t\t\t\t\t\t.favorite-date {\r\n\t\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\t\tcolor: #999999;\r\n\t\t\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.favorite-content {\r\n\t\t\t\t\t\tmargin-bottom: 24rpx;\r\n\r\n\t\t\t\t\t\t.content-text {\r\n\t\t\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\t\t\tcolor: #1a1a1a;\r\n\t\t\t\t\t\t\tline-height: 1.6;\r\n\t\t\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.favorite-actions {\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\tgap: 16rpx;\r\n\t\t\t\t\t\tflex-wrap: wrap;\r\n\r\n\t\t\t\t\t\t.action-btn {\r\n\t\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\t\tbackground: #E6F0FF;\r\n\t\t\t\t\t\t\tborder-radius: 20rpx;\r\n\t\t\t\t\t\t\tpadding: 12rpx 20rpx;\r\n\t\t\t\t\t\t\tborder: none;\r\n\t\t\t\t\t\t\tflex-shrink: 0;\r\n\t\t\t\t\t\t\tmin-width: 0;\r\n\r\n\t\t\t\t\t\t\t.action-icon {\r\n\t\t\t\t\t\t\t\twidth: 38rpx;\r\n\t\t\t\t\t\t\t\theight: 38rpx;\r\n\t\t\t\t\t\t\t\tmargin-right: 8rpx;\r\n\t\t\t\t\t\t\t\tflex-shrink: 0;\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t.action-text {\r\n\t\t\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\t\t\tcolor: #222222;\r\n\t\t\t\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t\t\t\t\twhite-space: nowrap;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.view-more-btn {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\tborder-radius: 24rpx;\r\n\t\t\t\t\tpadding: 24rpx;\r\n\t\t\t\t\tmargin: 24rpx 0;\r\n\r\n\t\t\t\t\t.view-more-text {\r\n\t\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\t\tcolor: #3478f6;\r\n\t\t\t\t\t\tmargin-right: 8rpx;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.arrow-icon {\r\n\t\t\t\t\t\twidth: 24rpx;\r\n\t\t\t\t\t\theight: 24rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.empty-state {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tflex-direction: column;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\tpadding: 200rpx 0;\r\n\r\n\t\t\t\t\t.empty-text {\r\n\t\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\t\tcolor: #999999;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\r\n\r\n\t.digital-person-content {\r\n\t\t.digital-person-banner {\r\n\t\t\tmargin-bottom: 24rpx;\r\n\t\t\toverflow: hidden;\r\n\t\t\tposition: relative;\r\n\r\n\t\t\t&.bottom-banner {\r\n\t\t\t\tmargin-top: 0;\r\n\t\t\t\tmargin-bottom: 0;\r\n\t\t\t}\r\n\r\n\t\t\t.banner-content {\r\n\t\t\t\t.banner-image {\r\n\t\t\t\t\twidth: 690rpx;\r\n\t\t\t\t\theight: 230rpx;\r\n\t\t\t\t\tdisplay: block;\r\n\t\t\t\t}\r\n\t\t\t\t.banner-image-radius{\r\n\t\t\t\t\tborder-radius: 30rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.function-area {\r\n\t\t\t.function-row {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: space-between;\r\n\r\n\t\t\t\t.function-item {\r\n\t\t\t\t\tbackground: #ffffff;\r\n\t\t\t\t\tborder-radius: 24rpx;\r\n\t\t\t\t\tpadding: 20rpx 0 20rpx 30rpx;\r\n\t\t\t\t\theight: 160rpx;\r\n\t\t\t\t\twidth: 330rpx;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tposition: relative;\r\n\t\t\t\t\tbox-sizing: border-box;\r\n\r\n\t\t\t\t\t.function-icon {\r\n\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\tright: 0;\r\n\t\t\t\t\t\ttop: 20rpx;\r\n\t\t\t\t\t\twidth: 120rpx;\r\n\t\t\t\t\t\theight: 120rpx;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.function-info {\r\n\t\t\t\t\t\tposition: relative;\r\n\t\t\t\t\t\tz-index: 2;\r\n\r\n\t\t\t\t\t\t.function-title {\r\n\t\t\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t\t\t\tcolor: #333;\r\n\t\t\t\t\t\t\tmargin-bottom: 8rpx;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t.function-desc {\r\n\t\t\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\t\t\tcolor: #5380F2;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.computing-power {\r\n\t\t\tbackground: #ffffff;\r\n\t\t\tborder-radius: 24rpx;\r\n\t\t\tpadding: 32rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\tmargin-bottom: 24rpx;\r\n\r\n\t\t\t.power-info {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\r\n\t\t\t\t.power-icon {\r\n\t\t\t\t\twidth: 48rpx;\r\n\t\t\t\t\theight: 48rpx;\r\n\t\t\t\t\tmargin-right: 16rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.power-text {\r\n\t\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\t\tcolor: #333333;\r\n\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\t.power-text_number{\r\n\t\t\t\t\t\tcolor: #2A64F6;\r\n\t\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t\t\tfont-size: 34rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.recharge-btn {\r\n\t\t\t\tbackground: #2A64F6;\r\n\t\t\t\tborder-radius: 50rpx;\r\n\t\t\t\twidth: 160rpx;\r\n\t\t\t\theight: 60rpx;\r\n\t\t\t\tline-height: 60rpx;\r\n\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\tcolor: #ffffff;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t// 自定义充值弹窗样式\r\n\t.recharge-overlay {\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tbackground: rgba(0, 0, 0, 0.7);\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tz-index: 9999;\r\n\t}\r\n\r\n\t.recharge-modal {\r\n\t\tbackground: linear-gradient(180deg, #EAF0FF 2%, #FFFFFF 20%);\r\n\t\tborder-radius: 20rpx;\r\n\t\tpadding: 75rpx 40rpx 60rpx;\r\n\t\twidth: 560rpx;\r\n\t\tmax-width: 90vw;\r\n\t\tposition: relative;\r\n\r\n\t\t.modal-header {\r\n\t\t\ttext-align: center;\r\n\t\t\tmargin-bottom: 58rpx;\r\n\r\n\t\t\t.modal-title {\r\n\t\t\t\tfont-size: 35rpx;\r\n\t\t\t\tfont-weight: 500;\r\n\t\t\t\tcolor: #5380F2;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.recharge-grid {\r\n\t\t\tdisplay: grid;\r\n\t\t\tgrid-template-columns: repeat(3, 1fr);\r\n\t\t\tgap: 28rpx;\r\n\t\t\tmargin-bottom: 60rpx;\r\n\r\n\t\t\t.recharge-item {\r\n\t\t\t\tbackground: #ffffff;\r\n\t\t\t\tborder: 3rpx solid #E0E0E0;\r\n\t\t\t\tborder-radius: 16rpx;\r\n\t\t\t\tpadding: 32rpx 20rpx;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\ttransition: all 0.3s ease;\r\n\r\n\t\t\t\t&.active {\r\n\t\t\t\t\tborder-color: #2B64F6;\r\n\t\t\t\t\tbackground: #E6EEFC;\r\n\r\n\t\t\t\t\t.item-count {\r\n\t\t\t\t\t\tcolor: #2A64F6;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.item-count {\r\n\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\tfont-size: 35rpx;\r\n\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t\tcolor: #656565;\r\n\t\t\t\t\tmargin-bottom: 8rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.item-price {\r\n\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\t\tcolor: #FA5151;\r\n\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.no-data {\r\n\t\t\ttext-align: center;\r\n\t\t\tpadding: 80rpx 0;\r\n\r\n\t\t\t.no-data-text {\r\n\t\t\t\tcolor: #999999;\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.payment-section {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\t\t\tmargin-bottom: 16rpx;\r\n\t\t\tfont-weight: 500;\r\n\r\n\t\t\t.payment-text {\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tcolor: #333333;\r\n\t\t\t}\r\n\r\n\t\t\t.payment-price {\r\n\t\t\t\tfont-size: 36rpx;\r\n\t\t\t\tfont-weight: 700;\r\n\t\t\t\tcolor: #FA5151;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.pay-button {\r\n\t\t\tbackground: #2B64F6;\r\n\t\t\tborder-radius: 48rpx;\r\n\t\t\tpadding: 32rpx;\r\n\t\t\ttext-align: center;\r\n\r\n\t\t\t.pay-text {\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tcolor: #ffffff;\r\n\t\t\t\tfont-weight: 500;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.close-icon-wrapper {\r\n\t\t\tposition: absolute;\r\n\t\t\tbottom: -80rpx;\r\n\t\t\tleft: 50%;\r\n\t\t\ttransform: translateX(-50%);\r\n\t\t\twidth: 45rpx;\r\n\t\t\theight: 45rpx;\r\n\t\t\tborder-radius: 50%;\r\n\r\n\t\t\t.close-icon {\r\n\t\t\t\twidth: 45rpx;\r\n\t\t\t\theight: 45rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n}\r\n</style>", "import MiniProgramPage from 'E:/youngProject/agent-mini-ui/pages/my/my.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useUserStore", "ref", "computed", "getChatGoodsApi", "uni", "getMyAgentListApi", "rule1", "rule2", "rule3", "reactive", "getMyCollectionListApi", "deleteAgentApi", "buyChatGoodsApi", "miniPay", "queryPayChatStautsApi", "cancelCollectMessageApi", "getUserInfoApi", "getMyEarningsApi", "watch", "bindInvitationApi", "onLoad", "getUserVipInfoApi", "onShow"], "mappings": ";;;;;;AAuUA,MAAM,UAAU;;;;AAEhB,UAAM,YAAYA,YAAAA,aAAc;AAChC,UAAM,OAAO,CAAC,SAAS,QAAQ,OAAO;AACtC,UAAM,YAAYC,cAAG,IAAC,CAAC;AAGvB,UAAM,gBAAgB,CAAC,QAAQ,OAAO;AACtC,UAAM,qBAAqBA,cAAG,IAAC,CAAC;AAGhC,UAAM,iBAAiBA,cAAG,IAAC,CAAC;AAC5B,UAAM,kBAAkBA,cAAG,IAAC,EAAE;AAE9B,UAAM,gBAAgBC,cAAQ,SAAC,MAAM;;AACpC,eAAO,qBAAgB,MAAM,eAAe,KAAK,MAA1C,mBAA6C,UAAS;AAAA,IAC9D,CAAC;AAGD,UAAM,eAAe,YAAY;AAChC,UAAI;AACH,YAAI,MAAM,MAAMC,0BAAiB;AACjC,YAAI,IAAI,SAAS,KAAK,IAAI,MAAM;AAC/B,0BAAgB,QAAQ,IAAI;AAAA,QAC/B,OAAS;AACN,0BAAgB,QAAQ,CAAE;AAAA,QAC1B;AAAA,MACD,SAAQ,OAAO;AACfC,sBAAAA,MAAc,MAAA,SAAA,0BAAA,iBAAiB,KAAK;AACpC,wBAAgB,QAAQ,CAAE;AAAA,MAC1B;AAAA,IACF;AACA,UAAM,aAAaH,cAAAA,IAAI;AAAA,MACtB,EAAE,OAAO,MAAM,OAAO,EAAG;AAAA,MACzB,EAAE,OAAO,QAAQ,OAAO,EAAG;AAAA,MAC3B,EAAE,OAAO,QAAQ,OAAO,EAAG;AAAA,MAC3B,EAAE,OAAO,SAAS,OAAO,EAAG;AAAA,IAC7B,CAAC;AAGD,UAAM,cAAc;AAAA,MACnB,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,IACJ;AAEA,UAAM,YAAYA,cAAG,IAAC,EAAE;AACxB,UAAM,iBAAiB,YAAY;AAClC,UAAI,MAAM,MAAMI,4BAAkB;AAAA,QACjC,cAAc,UAAU;AAAA,MAC1B,CAAE;AACD,gBAAU,QAAQ,IAAI,KAAK;AAAA,IAC5B;AAKA,UAAM,YAAYJ,cAAAA,IAAI;AAAA,MACrB,EAAE,MAAM,QAAQ,MAAMK,oBAAO;AAAA,MAC7B,EAAE,MAAM,QAAQ,MAAMC,oBAAO;AAAA,MAC7B,EAAE,MAAM,QAAQ,MAAMC,oBAAO;AAAA,IAC9B,CAAC;AAGD,UAAM,aAAaP,cAAAA,IAAI;AAAA,MACtB,iBAAiB;AAAA,MACjB,qBAAqB;AAAA,MACrB,cAAc;AAAA,MACd,kBAAkB;AAAA,MAClB,eAAe;AAAA,MACf,mBAAmB;AAAA,MACnB,gBAAgB;AAAA,MAChB,oBAAoB;AAAA,IACrB,CAAC;AAGD,UAAM,gBAAgBA,cAAG,IAAC,EAAE;AAC5B,QAAI,aAAaQ,cAAAA,SAAS;AAAA,MACzB,cAAc,UAAU;AAAA,MACxB,MAAM;AAAA,MACN,UAAU;AAAA,IACX,CAAC;AAGD,UAAM,mBAAmBR,cAAG,IAAC,KAAK;AAClC,UAAM,iBAAiBA,cAAG,IAAC,EAAE;AAC7B,UAAM,oBAAoBA,cAAG,IAAC,KAAK;AAEnC,UAAM,sBAAsB,YAAY;AACvC,UAAI;AACH,YAAI,MAAM,MAAMS,UAAsB,uBAAC,UAAU;AACjD,sBAAc,QAAQ,IAAI,KAAK;AAAA,MAC/B,SAAQ,OAAO;AACfN,sBAAAA,+CAAc,aAAa,KAAK;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACT,CAAG;AAAA,MACD;AAAA,IACF;AAEA,UAAM,oBAAoB,MAAM;AAC/BA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACP,CAAE;AAAA,IACF;AAEA,UAAM,mBAAmB,CAAC,UAAU;AACnCA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK,gCAAgC,MAAM,IAAI;AAAA,MACjD,CAAE;AAAA,IACF;AACA,UAAM,gBAAgB,MAAM;AAC3BA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACP,CAAE;AAAA,IACF;AAQA,UAAM,oBAAoB,MAAM;AAC/BA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACP,CAAE;AAAA,IACF;AAEA,UAAM,kBAAkB,CAAC,UAAU;AAClCA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK,kCAAkC,MAAM,IAAI;AAAA,MACnD,CAAE;AAAA,IACF;AACA,UAAM,oBAAoB,OAAO,UAAU;AAC1CA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,OAAO,QAAQ;AACvB,cAAI,IAAI,SAAS;AAChB,kBAAMO,yBAAe;AAAA,cACpB,cAAc,UAAU;AAAA,cACxB,WAAW,MAAM;AAAA,YACtB,CAAK;AACDP,0BAAAA,MAAI,UAAU;AAAA,cACb,OAAO;AAAA,cACP,MAAM;AAAA,YACX,CAAK;AACD,2BAAgB;AAAA,UAChB;AAAA,QACD;AAAA,MACH,CAAE;AAAA,IAEF;AACA,UAAM,kBAAkB,CAAC,UAAU;AAClC,UAAI,UAAU,GAAG;AAChBA,sBAAAA,MAAI,WAAW;AAAA,UACd,KAAK;AAAA,QACR,CAAG;AAAA,MACD;AACD,UAAI,UAAU,GAAG;AAChBA,sBAAAA,MAAI,WAAW;AAAA,UACd,KAAK;AAAA,QACR,CAAG;AAAA,MACD;AACD,UAAI,UAAU,GAAG;AAChBA,sBAAAA,MAAI,WAAW;AAAA,UACd,KAAK;AAAA,QACR,CAAG;AAAA,MACD;AAAA,IAEF;AAEA,UAAM,iBAAiB,MAAM;AAC5BA,oBAAAA,6CAAY,IAAI;AAAA,IACjB;AAEA,UAAM,uBAAuB,MAAM;AAClCA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACP,CAAE;AAAA,IACF;AAEA,UAAM,oBAAoB,MAAM;AAC/BA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACP,CAAE;AAAA,IACF;AAEA,UAAM,aAAa,CAAC,SAAS;AAC5BA,oBAAAA,MAAI,iBAAiB;AAAA,QACpB,MAAM,KAAK;AAAA,QACX,UAAU;AACTA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACV,CAAI;AAAA,QACD;AAAA,MACH,CAAE;AAAA,IACF;AAEA,UAAM,gBAAgB,CAAC,SAAS;AAC/B,qBAAe,QAAQ,KAAK,kBAAkB;AAC9C,uBAAiB,QAAQ;AAAA,IAC1B;AAEA,UAAM,oBAAoB,MAAM;AAC/B,uBAAiB,QAAQ;AACzB,qBAAe,QAAQ;AAAA,IACxB;AAGA,UAAM,oBAAoB,YAAY;AACrC,YAAM,aAAc;AACpB,wBAAkB,QAAQ;AAAA,IAC3B;AAEA,UAAM,qBAAqB,MAAM;AAChC,wBAAkB,QAAQ;AAAA,IAC3B;AAEA,UAAM,eAAe,CAAC,UAAU;AAC/B,qBAAe,QAAQ;AAAA,IACxB;AAEA,UAAM,kBAAkB,YAAY;AACnC,YAAM,SAAS,gBAAgB,MAAM,eAAe,KAAK;AACzD,UAAI,CAAC,QAAQ;AACZA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACT,CAAG;AACD;AAAA,MACA;AAED,UAAI;AACHA,sBAAAA,MAAI,YAAY;AAAA,UACf,OAAO;AAAA,UACP,MAAM;AAAA,QACT,CAAG;AAGD,cAAM,UAAU,MAAMQ,0BAAgB;AAAA,UACrC,eAAe,OAAO;AAAA,UACtB,QAAQ;AAAA,QACX,CAAG;AAEDR,sBAAAA,MAAI,YAAa;AAEjBS,2BAAQ,QAAQ,IAAI,EAAE;AAAA,UACrB,YAAY;AACX,+BAAmB,QAAQ,KAAK,SAAS,CAAC;AAAA,UAC1C;AAAA,UACD,CAAC,QAAQ;AACRT,0BAAAA,MAAI,UAAU;AAAA,cACb,OAAO,IAAI,OAAO;AAAA,cAClB,MAAM;AAAA,YACX,CAAK;AAAA,UACD;AAAA,QACD;AAAA,MACD,SAAQ,OAAO;AACfA,sBAAAA,MAAI,YAAa;AACjBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO,MAAM,WAAW;AAAA,UACxB,MAAM;AAAA,QACT,CAAG;AAAA,MACD;AAAA,IACF;AAGA,UAAM,qBAAqB,OAAO,SAAS,WAAW;AACrD;AACA,UAAI;AACH,cAAM,YAAY,MAAMU,gCAAsB;AAAA,UAC7C;AAAA,QACH,CAAG;AACD,YAAI,UAAU,KAAK,OAAO;AACzBV,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UACd,CAAI;AACD,6BAAoB;AAEpB,cAAI,UAAU,WAAW;AACxB,uBAAY;AAAA,UACZ;AAAA,QACJ,OAAS;AACN,qBAAW,MAAM;AAChB,+BAAmB,SAAS,MAAM;AAAA,UAClC,GAAE,GAAI;AAAA,QACP;AAAA,MACD,SAAQ,OAAO;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO,MAAM,OAAO;AAAA,UACpB,MAAM;AAAA,QACT,CAAG;AAAA,MACD;AAAA,IACF;AAGA,UAAM,cAAc,MAAM;AACzBA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACP,CAAE;AAAA,IACF;AAEA,UAAM,eAAe,MAAM;AAC1BA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACP,CAAE;AAAA,IACF;AAGA,UAAM,oBAAoB,MAAM;AAC/BA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACP,CAAE;AACD;AAAA,IAID;AAEA,UAAM,mBAAmB,OAAO,SAAS;AACxC,YAAMW,kCAAwB;AAAA,QAC7B,cAAc,UAAU;AAAA,QACxB,aAAa,KAAK;AAAA,MACpB,CAAE;AACDX,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAE;AAED,iBAAW,OAAO;AAClB,0BAAqB;AAAA,IACtB;AAGA,UAAM,uBAAuB,MAAM;AAClCA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACP,CAAE;AAAA,IACF;AAGA,UAAM,0BAA0B,MAAM;AACrCA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACP,CAAE;AAAA,IACF;AACA,QAAI,WAAWK,cAAAA,SAAS;AAAA,MACvB,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,UAAU;AAAA,IACX,CAAC;AACD,UAAM,cAAc,YAAY;AAC/B,UAAI,MAAM,MAAMO,yBAAgB;AAChC,iBAAW,OAAO,OAAO,UAAU,IAAI,IAAI;AAAA,IAC5C;AAEA,UAAM,gBAAgB,YAAY;AACjC,UAAI,MAAM,MAAMC,2BAAiB;AAAA,QAChC,cAAc,UAAU;AAAA,MAC1B,CAAE;AACDb,oBAAY,MAAA,MAAA,OAAA,0BAAA,IAAI,MAAM,mBAAmB;AACzC,iBAAW,QAAQ,IAAI;AAAA,IACxB;AACAc,kBAAK;AAAA,MACJ,MAAM,UAAU;AAAA,MAChB,CAAC,UAAU,aAAa;AACvB,YAAI,YAAY,aAAa,IAAI;AAChC,sBAAa;AACb,yBAAgB;AAChB,yBAAgB;AAChB,wBAAe;AACf,uBAAc;AAAA,QACd;AAAA,MACD;AAAA,IACF;AACA,QAAI,SAASjB,cAAG,IAAC,EAAE;AACnB,UAAM,iBAAiB,YAAY;AAClC,UAAI;AACHG,sBAAA,MAAA,MAAA,OAAA,0BAAY,gBAAgB,OAAO,KAAK;AACxC,cAAMe,4BAAkB;AAAA,UACvB,cAAc,UAAU;AAAA,UACxB,gBAAgB,OAAO;AAAA,QAC1B,CAAG;AAAA,MACD,SAAQ,OAAO;AACff,sBAAAA,+CAAc,YAAY,KAAK;AAAA,MAC/B;AAAA,IACF;AACAgB,kBAAM,OAAC,CAAC,WAAW;AAClB,UAAI,OAAO,QAAQ;AAClB,eAAO,QAAQ,OAAO;AACtB,YAAI,UAAU,WAAW;AACxB,yBAAgB;AAAA,QAChB;AAAA,MACD;AAAA,IACF,CAAC;AAED,QAAI,UAAUX,cAAAA,SAAS;AAAA,MACtB,eAAe;AAAA,MACf,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,eAAe;AAAA,MACf,0BAA0B;AAAA,MAC1B,2BAA2B;AAAA,MAC3B,YAAY;AAAA,QACX,iBAAiB;AAAA,MACjB;AAAA,IAEF,CAAC;AACD,UAAM,aAAa,YAAY;AAC9B,UAAI;AACH,YAAI,MAAM,MAAMY,4BAAkB;AAAA,UACjC,cAAc,UAAU;AAAA,QAC3B,CAAG;AACD,kBAAU,OAAO,OAAO,SAAS,IAAI,IAAI;AACzCjB,sBAAAA,MAAA,MAAA,OAAA,0BAAY,KAAK,MAAM;AAAA,MACvB,SAAQ,OAAO;AACfA,sBAAAA,+CAAc,aAAa,KAAK;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACT,CAAG;AAAA,MACD;AAAA,IACF;AAEAkB,kBAAAA,OAAO,MAAM;AACZ,UAAI,UAAU,WAAW;AACxB,oBAAa;AACb,uBAAgB;AAChB,4BAAqB;AACrB,mBAAY;AACZ,sBAAe;AACf,qBAAc;AAAA,MACd;AAAA,IACF,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7vBD,GAAG,WAAW,eAAe;"}