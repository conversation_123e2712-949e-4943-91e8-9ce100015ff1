{"version": 3, "file": "common-layout.js", "sources": ["uni_modules/z-paging/components/z-paging/js/modules/common-layout.js"], "sourcesContent": ["// [z-paging]通用布局相关模块\r\n\r\n// #ifdef APP-NVUE\r\nconst weexDom = weex.requireModule('dom');\r\n// #endif\r\n\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tsystemInfo: null,\r\n\t\t\tcssSafeAreaInsetBottom: -1,\r\n\t\t\tisReadyDestroy: false,\r\n\t\t}\r\n\t},\r\n\tcomputed: {\r\n\t\t// 顶部可用距离\r\n\t\twindowTop() {\r\n\t\t\tif (!this.systemInfo) return 0;\r\n\t\t\t// 暂时修复vue3中隐藏系统导航栏后windowTop获取不正确的问题，具体bug详见https://ask.dcloud.net.cn/question/141634\r\n\t\t\t// 感谢litangyu！！https://github.com/SmileZXLee/uni-z-paging/issues/25\r\n\t\t\t// #ifdef VUE3 && H5\r\n\t\t\tconst pageHeadNode = document.getElementsByTagName(\"uni-page-head\");\r\n\t\t\tif (!pageHeadNode.length) return 0;\r\n\t\t\t// #endif\r\n\t\t\treturn this.systemInfo.windowTop || 0;\r\n\t\t},\r\n\t\t// 底部安全区域高度\r\n\t\tsafeAreaBottom() {\r\n\t\t\tif (!this.systemInfo) return 0;\r\n\t\t\tlet safeAreaBottom = 0;\r\n\t\t\t// #ifdef APP-PLUS\r\n\t\t\tsafeAreaBottom = this.systemInfo.safeAreaInsets.bottom || 0 ;\r\n\t\t\t// #endif\r\n\t\t\t// #ifndef APP-PLUS\r\n\t\t\tsafeAreaBottom = Math.max(this.cssSafeAreaInsetBottom, 0);\r\n\t\t\t// #endif\r\n\t\t\treturn safeAreaBottom;\r\n\t\t},\r\n\t\t// 是否是比较老的webview，在一些老的webview中，需要进行一些特殊处理\r\n\t\tisOldWebView() {\r\n\t\t\t// #ifndef APP-NVUE || MP-KUAISHOU\r\n\t\t\ttry {\r\n\t\t\t\tconst systemInfos = uni.getSystemInfoSync().system.split(' ');\r\n\t\t\t\tconst deviceType = systemInfos[0];\r\n\t\t\t\tconst version = parseInt(systemInfos[1]);\r\n\t\t\t\tif ((deviceType === 'iOS' && version <= 10) || (deviceType === 'Android' && version <= 6)) {\r\n\t\t\t\t\treturn true;\r\n\t\t\t\t}\r\n\t\t\t} catch(e) {\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t\t// #endif\r\n\t\t\treturn false;\r\n\t\t},\r\n\t\t// 当前组件的$slots，兼容不同平台\r\n\t\tzSlots() {\r\n\t\t\t// #ifdef VUE2\r\n\t\t\t\r\n\t\t\t// #ifdef MP-ALIPAY\r\n\t\t\treturn this.$slots;\r\n\t\t\t// #endif\r\n\t\t\t\r\n\t\t\treturn this.$scopedSlots || this.$slots;\r\n\t\t\t// #endif\r\n\t\t\t\r\n\t\t\treturn this.$slots;\r\n\t\t},\r\n\t},\r\n\tbeforeDestroy() {\r\n\t\tthis.isReadyDestroy = true;\r\n\t},\r\n\t// #ifdef VUE3\r\n\tunmounted() {\r\n\t\tthis.isReadyDestroy = true;\r\n\t},\r\n\t// #endif\r\n\tmethods: {\r\n\t\t// 更新fixed模式下z-paging的布局\r\n\t\tupdateFixedLayout() {\r\n\t\t\tthis.fixed && this.$nextTick(() => {\r\n\t\t\t\tthis.systemInfo = uni.getSystemInfoSync();\r\n\t\t\t})\r\n\t\t},\r\n\t\t// 获取节点尺寸\r\n\t\t_getNodeClientRect(select, inDom = true, scrollOffset = false) {\r\n\t\t\tif (this.isReadyDestroy) {\r\n\t\t\t\treturn Promise.resolve(false);\r\n\t\t\t};\r\n\t\t\t// nvue中获取节点信息\r\n\t\t\t// #ifdef APP-NVUE\r\n\t\t\tselect = select.replace(/[.|#]/g, '');\r\n\t\t\tconst ref = this.$refs[select];\r\n\t\t\treturn new Promise((resolve, reject) => {\r\n\t\t\t\tif (ref) {\r\n\t\t\t\t\tweexDom.getComponentRect(ref, option => {\r\n\t\t\t\t\t\tresolve(option && option.result ? [option.size] : false);\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\tresolve(false);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\treturn;\r\n\t\t\t// #endif\r\n\t\t\t\r\n\t\t\t// vue中获取节点信息\r\n\t\t\t//#ifdef MP-ALIPAY\r\n\t\t\tinDom = false;\r\n\t\t\t//#endif\r\n\t\t\tlet res = !!inDom ? uni.createSelectorQuery().in(inDom === true ? this : inDom) : uni.createSelectorQuery();\r\n\t\t\tscrollOffset ? res.select(select).scrollOffset() : res.select(select).boundingClientRect();\r\n\t\t\treturn new Promise((resolve, reject) => {\r\n\t\t\t\tres.exec(data => {\r\n\t\t\t\t\tresolve((data && data != '' && data != undefined && data.length) ? data : false);\r\n\t\t\t\t});\r\n\t\t\t});\r\n\t\t},\r\n\t\t// 获取slot=\"left\"和slot=\"right\"宽度并且更新布局\r\n\t\t_updateLeftAndRightWidth(targetStyle, parentNodePrefix) {\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\tlet delayTime = 0;\r\n\t\t\t\t// #ifdef MP-BAIDU\r\n\t\t\t\tdelayTime = 10;\r\n\t\t\t\t// #endif\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t['left','right'].map(position => {\r\n\t\t\t\t\t\tthis._getNodeClientRect(`.${parentNodePrefix}-${position}`).then(res => {\r\n\t\t\t\t\t\t\tthis.$set(targetStyle, position, res ? res[0].width + 'px' : '0px');\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t})\r\n\t\t\t\t}, delayTime)\r\n\t\t\t})\r\n\t\t},\r\n\t\t// 通过获取css设置的底部安全区域占位view高度设置bottom距离（直接通过systemInfo在部分平台上无法获取到底部安全区域）\r\n\t\t_getCssSafeAreaInsetBottom(success) {\r\n\t\t\tthis._getNodeClientRect('.zp-safe-area-inset-bottom').then(res => {\r\n\t\t\t\tthis.cssSafeAreaInsetBottom = res ? res[0].height : -1;\r\n\t\t\t\tres && success && success();\r\n\t\t\t});\r\n\t\t}\r\n\t}\r\n}\r\n"], "names": ["uni"], "mappings": ";;AAMA,MAAe,qBAAA;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,YAAY;AAAA,MACZ,wBAAwB;AAAA,MACxB,gBAAgB;AAAA,IAChB;AAAA,EACD;AAAA,EACD,UAAU;AAAA;AAAA,IAET,YAAY;AACX,UAAI,CAAC,KAAK;AAAY,eAAO;AAO7B,aAAO,KAAK,WAAW,aAAa;AAAA,IACpC;AAAA;AAAA,IAED,iBAAiB;AAChB,UAAI,CAAC,KAAK;AAAY,eAAO;AAC7B,UAAI,iBAAiB;AAKrB,uBAAiB,KAAK,IAAI,KAAK,wBAAwB,CAAC;AAExD,aAAO;AAAA,IACP;AAAA;AAAA,IAED,eAAe;AAEd,UAAI;AACH,cAAM,cAAcA,cAAAA,MAAI,kBAAiB,EAAG,OAAO,MAAM,GAAG;AAC5D,cAAM,aAAa,YAAY,CAAC;AAChC,cAAM,UAAU,SAAS,YAAY,CAAC,CAAC;AACvC,YAAK,eAAe,SAAS,WAAW,MAAQ,eAAe,aAAa,WAAW,GAAI;AAC1F,iBAAO;AAAA,QACP;AAAA,MACD,SAAO,GAAG;AACV,eAAO;AAAA,MACP;AAED,aAAO;AAAA,IACP;AAAA;AAAA,IAED,SAAS;AAUR,aAAO,KAAK;AAAA,IACZ;AAAA,EACD;AAAA,EACD,gBAAgB;AACf,SAAK,iBAAiB;AAAA,EACtB;AAAA,EAED,YAAY;AACX,SAAK,iBAAiB;AAAA,EACtB;AAAA,EAED,SAAS;AAAA;AAAA,IAER,oBAAoB;AACnB,WAAK,SAAS,KAAK,UAAU,MAAM;AAClC,aAAK,aAAaA,oBAAI;MAC1B,CAAI;AAAA,IACD;AAAA;AAAA,IAED,mBAAmB,QAAQ,QAAQ,MAAM,eAAe,OAAO;AAC9D,UAAI,KAAK,gBAAgB;AACxB,eAAO,QAAQ,QAAQ,KAAK;AAAA,MAEhC;AAoBG,UAAI,MAAM,CAAC,CAAC,QAAQA,cAAG,MAAC,oBAAmB,EAAG,GAAG,UAAU,OAAO,OAAO,KAAK,IAAIA,cAAG,MAAC,oBAAmB;AACzG,qBAAe,IAAI,OAAO,MAAM,EAAE,iBAAiB,IAAI,OAAO,MAAM,EAAE,mBAAkB;AACxF,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACvC,YAAI,KAAK,UAAQ;AAChB,kBAAS,QAAQ,QAAQ,MAAM,QAAQ,UAAa,KAAK,SAAU,OAAO,KAAK;AAAA,QACpF,CAAK;AAAA,MACL,CAAI;AAAA,IACD;AAAA;AAAA,IAED,yBAAyB,aAAa,kBAAkB;AACvD,WAAK,UAAU,MAAM;AACpB,YAAI,YAAY;AAIhB,mBAAW,MAAM;AAChB,WAAC,QAAO,OAAO,EAAE,IAAI,cAAY;AAChC,iBAAK,mBAAmB,IAAI,gBAAgB,IAAI,QAAQ,EAAE,EAAE,KAAK,SAAO;AACvE,mBAAK,KAAK,aAAa,UAAU,MAAM,IAAI,CAAC,EAAE,QAAQ,OAAO,KAAK;AAAA,YACzE,CAAO;AAAA,UACP,CAAM;AAAA,QACD,GAAE,SAAS;AAAA,MAChB,CAAI;AAAA,IACD;AAAA;AAAA,IAED,2BAA2B,SAAS;AACnC,WAAK,mBAAmB,4BAA4B,EAAE,KAAK,SAAO;AACjE,aAAK,yBAAyB,MAAM,IAAI,CAAC,EAAE,SAAS;AACpD,eAAO,WAAW;MACtB,CAAI;AAAA,IACD;AAAA,EACD;AACF;;"}