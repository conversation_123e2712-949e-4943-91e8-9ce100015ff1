{"version": 3, "file": "index.js", "sources": ["pages/msg/index.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvbXNnL2luZGV4LnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"container\">\r\n    <!-- :style=\"{backgroundImage: 'url(https://api.zhimoai.com/storage/topic/20240830/22c0fd242cddcc1d0f7ccbbf8f4fcff1.png)',}\" -->\r\n    <z-paging ref=\"paging\" v-model=\"dataList\" @query=\"queryList\" :auto-hide-keyboard-when-chat=\"false\"\r\n      :hide-empty-view=\"true\" :use-chat-record-mode=\"true\" :auto=\"false\" :auto-clean-list-when-reload=\"false\"\r\n      :show-chat-loading-when-reload=\"true\">\r\n      <view class=\"chat-main\">\r\n        <view class=\"chat-ls\" v-for=\"(item, index) in dataList\"\r\n          :key=\"item.guid || `temp_${index}_${item.role}_${Date.now()}`\">\r\n          <view style=\"transform: scaleY(-1)\">\r\n            <view class=\"msg-m msg-left\" v-if=\"item.role === 'assistant'\">\r\n              <!-- <view class=\"logo-box gpt-box\">\r\n                <image class=\"user-img\" :src=\"agentDetail.agentAvatar\"></image>\r\n              </view> -->\r\n              <!-- 文字 -->\r\n              <view class=\"msg-text\" :class=\"{ hasReply: item.aiReplyTitle.length === 0 }\">\r\n                <view class=\"ai-reply-msg\" v-if=\"item.aiReplyTitle && item.aiReplyTitle.length > 0\">{{ item.aiReplyTitle\r\n                }}</view>\r\n                <view class=\"rich-text-box\" v-if=\"item.type === 'richText'\">\r\n                  <rich-text :user-select=\"true\" :nodes=\"item.content\"></rich-text>\r\n                  <view class=\"img-list-box\" :class=\"{ noWrap: item.imageList.length < 3 }\"\r\n                    v-if=\"item.imageList && item.imageList.length > 0\">\r\n                    <image class=\"img\" mode=\"widthFix\" @click=\"onLookImg(item.imageList, true, index)\"\r\n                      v-for=\"(path, index) in item.imageList\" :src=\"path\" :key=\"index\">\r\n                    </image>\r\n                  </view>\r\n                </view>\r\n                <view class=\"rich-text-box\" v-if=\"item.type === 'text'\">\r\n                  <!-- <text :user-select=\"true\" class=\"t\">{{ item.content }}</text> -->\r\n                  <!-- <rich-text :user-select=\"true\" :nodes=\"item.content\"></rich-text> -->\r\n                  <ua-markdown :source=\"item.content\" />\r\n                  <view class=\"loading\" v-if=\"item.isLoading\">\r\n                    <view class=\"loading-dot\"></view>\r\n                    <view class=\"loading-dot\"></view>\r\n                    <view class=\"loading-dot\"></view>\r\n                  </view>\r\n                  <view class=\"often-questions\" v-if=\"item.isFirst && !isFirstSend\">\r\n                    <view class=\"item\" @click=\"onOftenClick(item)\" v-for=\"(item, index) in commonQuestions\"\r\n                      :key=\"index\">\r\n                      {{ item }}\r\n                    </view>\r\n                  </view>\r\n                </view>\r\n                <view class=\"img-box\" v-if=\"item.type === 'image'\">\r\n                  <image @click=\"onLookImg(item.content, false, 0)\" :show-menu-by-longpress=\"true\" class=\"img\"\r\n                    mode=\"widthFix\" :src=\"item.content\">\r\n                  </image>\r\n                </view>\r\n                <view @click=\"onMsgLink(item.content)\" class=\"text-box\" v-if=\"item.type === 'link'\">\r\n                  <text class=\"link\" :user-select=\"true\">{{ item.content }}</text>\r\n                </view>\r\n                <view class=\"video-box\" v-if=\"item.type === 'video'\">\r\n                  <video class=\"video-dom\" id=\"myVideo\" :src=\"item.content\" controls></video>\r\n                </view>\r\n                <view class=\"mini-box\" v-if=\"item.type === 'mini'\">\r\n                  <view class=\"btn\" @click=\"onGotoMini(item.content)\">点击前往小程序</view>\r\n                </view>\r\n                <view class=\"other-box\" v-if=\"!item.isFirst\">\r\n                  <image @click=\"handleCollect(item)\" v-if=\"item.guid\" class=\"icon\" mode=\"widthFix\"\r\n                    :src=\"item.isCollected ? scOnIcon : scIcon\">\r\n                  </image>\r\n                  <!-- <image @click=\"handleDelete(item.guid)\" class=\"icon\" mode=\"widthFix\"\r\n                    src=\"@/static/msg/<EMAIL>\">\r\n                  </image> -->\r\n                  <image @click=\"handleCopy(item.content)\" class=\"icon\" mode=\"widthFix\"\r\n                    src=\"@/static/msg/copy-icon.png\">\r\n                  </image>\r\n                  <view class=\"text-btn\" @click=\"onGoAi\"\r\n                    v-if=\"index === dataList.findIndex(msg => msg.role === 'assistant')\">生成视频</view>\r\n                </view>\r\n              </view>\r\n            </view>\r\n            <view class=\"msg-m msg-right\" v-if=\"item.role === 'user'\">\r\n              <!-- <view class=\"logo-box\">\r\n                <image class=\"user-img\" :src=\"headImgUrl\"></image>\r\n              </view> -->\r\n              <view class=\"msg-text\">\r\n                <text :user-select=\"true\">{{ item.content }}</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        <!-- <view class=\"on-load-more-box\" style=\"transform: scaleY(-1)\" @click=\"onGetHistory\" v-if=\"isFirstLoad\">点击加载更多\r\n        </view> -->\r\n        <view class=\"agent-logo\" style=\"transform: scaleY(-1)\">\r\n          <image class=\"logo\" :src=\"agentDetail.agentAvatar\"></image>\r\n        </view>\r\n      </view>\r\n      <template #bottom>\r\n        <view class=\"clear-msg\" v-if=\"isMsgInput && !ended\">\r\n          <text class=\"txt\" @click=\"handleClearAll\">重置对话</text>\r\n        </view>\r\n        <view class=\"sy-end-box\" v-if=\"ended\">\r\n          <view>试用已结束，请订阅后继续使用</view>\r\n          <view class=\"end-btn\" @click=\"showSubscribePopup\">立即订阅</view>\r\n        </view>\r\n        <view class=\"submit-box\" v-else>\r\n          <!-- v-if=\"isMsgInput\" -->\r\n          <!-- <view class=\"send-record-box\" @touchstart=\"onLongTap\" @touchend=\"onTouchend\" @touchmove=\"onTouchMove\"\r\n            :class=\"{ 'longPress': recordConfig.recorderMode == 2, 'is-close-send': recordConfig.isCloseSend }\">\r\n            <image class=\"icon\" src=\"@/static/<EMAIL>\" @click=\"onViceoSend\"></image> {{ recordConfig.voiceText\r\n            }}\r\n          </view> -->\r\n          <!-- :class=\"{ mt: isMsgInput }\" mt -->\r\n          <view class=\"sub-input-box \">\r\n            <view class=\"input-box\" v-if=\"!recordConfig.isRecorderMode\">\r\n              <view class=\"textarea-box\">\r\n                <textarea class=\"textarea-input\" :disabled=\"historyMsgLoading\" :fixed=\"true\" @focus=\"onMsgFocus\"\r\n                  @blur=\"onMsgBlur\" v-model=\"msgContent\" placeholder=\"也可以打字哦，点击输入吧\" confirmType=\"send\" @confirm=\"onSend\"\r\n                  :adjust-position=\"false\" maxlength=\"-1\" :show-confirm-bar=\"false\" auto-height></textarea>\r\n              </view>\r\n              <!-- <view class=\"btn-box\"> -->\r\n              <!-- <view class=\"upload\">\r\n                  <image class=\"icon\" src=\"@/static/<EMAIL>\" @click=\"onSend\"></image>\r\n                </view> -->\r\n              <!-- <view class=\"send\">\r\n                  <image class=\"icon\" src=\"@/static/<EMAIL>\" @click=\"onSend\"></image>\r\n                </view> -->\r\n              <!-- <view class=\"send\">\r\n                  <image class=\"icon\" src=\"@/static/<EMAIL>\" @touchstart=\"onLongTap\" @touchend=\"onTouchend\"\r\n                    @touchmove=\"onTouchMove\"></image>\r\n                </view> -->\r\n              <!-- </view> -->\r\n              <view class=\"vicoe-box\">\r\n                <view class=\"send\" v-if=\"isMsgInput\">\r\n                  <image class=\"icon\" src=\"@/static/<EMAIL>\" @touchstart=\"onLongTap\" @touchend=\"onTouchend\"\r\n                    @touchmove=\"onTouchMove\"></image>\r\n                </view>\r\n                <view class=\"send\" v-else>\r\n                  <image class=\"icon\" src=\"@/static/<EMAIL>\" @click=\"onSend\"></image>\r\n                </view>\r\n              </view>\r\n            </view>\r\n            <template v-if=\"recordConfig.recorderMode == 2\">\r\n              <view class=\"spinner-title\">{{ recordConfig.voiceTitle }}</view>\r\n              <view class=\"recode-loading-box\" :class=\"{ 'is-close-send': recordConfig.isCloseSend }\">\r\n                <view class=\"spinner\">\r\n                  <view class=\"sub-spinner spinner-part-0\"></view>\r\n                  <view class=\"sub-spinner spinner-part-1\"></view>\r\n                  <view class=\"sub-spinner spinner-part-2\"></view>\r\n                  <view class=\"sub-spinner spinner-part-3\"></view>\r\n                  <view class=\"sub-spinner spinner-part-0\"></view>\r\n                  <view class=\"sub-spinner spinner-part-1\"></view>\r\n                  <view class=\"sub-spinner spinner-part-2\"></view>\r\n                  <view class=\"sub-spinner spinner-part-3\"></view>\r\n                </view>\r\n              </view>\r\n            </template>\r\n          </view>\r\n          <!-- <view class=\"recode-box record-layer\" :class=\"{ 'record-layer': recordConfig.recorderMode == 2 }\">\r\n            <view class=\"recode-loading-box\" :class=\"{ 'is-close-send': recordConfig.isCloseSend }\">\r\n              <view class=\"spinner\">\r\n                <view class=\"sub-spinner spinner-part-0\"></view>\r\n                <view class=\"sub-spinner spinner-part-1\"></view>\r\n                <view class=\"sub-spinner spinner-part-2\"></view>\r\n                <view class=\"sub-spinner spinner-part-3\"></view>\r\n                <view class=\"sub-spinner spinner-part-0\"></view>\r\n                <view class=\"sub-spinner spinner-part-1\"></view>\r\n                <view class=\"sub-spinner spinner-part-2\"></view>\r\n                <view class=\"sub-spinner spinner-part-3\"></view>\r\n              </view>\r\n              <view class=\"spinner-title\">{{ recordConfig.voiceTitle }}</view>\r\n            </view>\r\n          </view> -->\r\n        </view>\r\n\r\n      </template>\r\n    </z-paging>\r\n    <uni-popup ref=\"authPopup\">\r\n      <view class=\"auth-pop-box\">\r\n        <view class=\"title\">麦克风未授权，请授权使用麦克风。</view>\r\n        <button class=\"btn\" open-type=\"openSetting\" @click=\"authCallback\">打开设置页</button>\r\n      </view>\r\n    </uni-popup>\r\n    <!-- 订阅弹窗 -->\r\n    <subscribe-popup :show=\"showSubscribeModal\" :agentInfo=\"agentDetail\" @close=\"closeSubscribePopup\"\r\n      @subscribe=\"handleSubscribeConfirm\" />\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport {\r\n  getMessageHistoryApi,\r\n  saveMsgApi,\r\n  subscribeAgentApi,\r\n  deleteAllMessagesApi,\r\n  collectMessageApi,\r\n  cancelCollectMessageApi,\r\n  agentDetailApi,\r\n  createPurchaseOrderApi,\r\n  queryPurchaseOrderApi\r\n} from '@/api';\r\nimport {\r\n  ref,\r\n  reactive,\r\n  nextTick,\r\n} from 'vue';\r\nimport {\r\n  onLoad,\r\n} from '@dcloudio/uni-app';\r\nimport {\r\n  TextDecoder\r\n} from \"text-encoding/lib/encoding\";\r\nimport {\r\n  useUserStore\r\n} from '@/stores/user.js';\r\nimport {\r\n  miniPay\r\n} from '@/api/common.js'\r\nimport {\r\n  debounce,\r\n  throttle\r\n} from '@/utils/utils.js'\r\nimport SubscribePopup from '@/components/subscribe-popup/subscribe-popup.vue';\r\nimport scIcon from '@/static/msg/<EMAIL>';\r\nimport scOnIcon from '@/static/msg/sc-on-icon.png';\r\nimport base from '@/config/config.js';\r\nconst paging = ref(null)\r\nconst authPopup = ref(null);\r\nconst userStore = useUserStore();\r\nconst agentGuid = ref('');\r\nconst sessionGuid = ref('');\r\nconst isFirstLoad = ref(true)\r\nconst conversation_id = ref('')\r\nconst dataList = ref([]);\r\nconst isSending = ref(false)\r\n//试用是否结束\r\nconst ended = ref(false)\r\n// #ifdef MP-WEIXIN\r\n// const app = getApp();\r\n// const wxRecorder = app.globalData.recorderManager;\r\nconst plugin = requirePlugin(\"WechatSI\")\r\n// 获取**全局唯一**的语音识别管理器**recordRecoManager**\r\nconst manager = plugin.getRecordRecognitionManager()\r\nconst recorderPermission = ref(false)\r\n// #endif\r\nconst agentDetail = reactive({\r\n  agentName: '',\r\n  agentAvatar: '',\r\n  agentDesc: '',\r\n  price: 0,\r\n})\r\nconst commonQuestions = ref([])\r\nconst showSubscribeModal = ref(false) // 控制订阅弹窗显示\r\nconst queryStatusNum = ref(0)\r\n\r\nconst subscribeAgent = async () => {\r\n  try {\r\n    const res = await subscribeAgentApi({\r\n      merchantGuid: userStore.merchantGuid,\r\n      agentGuid: agentGuid.value\r\n    })\r\n    sessionGuid.value = res.data.sessionGuid;\r\n  } catch (error) {\r\n    uni.showToast({\r\n      title: error.msg || '订阅失败',\r\n      icon: 'none'\r\n    })\r\n  }\r\n\r\n}\r\n//清空历史消息\r\nconst handleClearAll = async () => {\r\n  uni.showModal({\r\n    title: '确认重置',\r\n    content: '确定要重置对话吗？',\r\n    success: async (res) => {\r\n      if (res.confirm) {\r\n        let res = await deleteAllMessagesApi({\r\n          merchantGuid: userStore.merchantGuid,\r\n          sessionGuid: sessionGuid.value\r\n        })\r\n        if (res.code === 0) {\r\n          uni.showToast({\r\n            title: '已重置',\r\n            icon: 'success'\r\n          })\r\n          //清除轮训加载文字\r\n          clearInterval(globalTimer.value)\r\n          msgConfig.msgId = '';\r\n          isSending.value = false;\r\n          isFirstLoad.value = false;\r\n          await paging.value.reload()\r\n          addClearChat(agentDetail.agentDesc)\r\n          isFirstSend.value = false;\r\n        } else {\r\n          uni.showToast({\r\n            title: res.msg,\r\n            icon: 'none'\r\n          })\r\n        }\r\n\r\n      }\r\n    }\r\n  })\r\n}\r\nconst handleCollect = async (item) => {\r\n  let guid = item.guid;\r\n  let title = '收藏成功';\r\n  try {\r\n    if (item.isCollected) {\r\n      await cancelCollectMessageApi({\r\n        merchantGuid: userStore.merchantGuid,\r\n        messageGuid: guid\r\n      })\r\n      title = '取消收藏成功';\r\n      item.isCollected = false\r\n    } else {\r\n      await collectMessageApi({\r\n        merchantGuid: userStore.merchantGuid,\r\n        messageGuid: guid\r\n      })\r\n      item.isCollected = true\r\n    }\r\n    uni.showToast({\r\n      title: title,\r\n      icon: 'success'\r\n    })\r\n  } catch (error) {\r\n    uni.showToast({\r\n      title: error.msg,\r\n      icon: 'none'\r\n    })\r\n  }\r\n\r\n}\r\nconst handleCopy = (content) => {\r\n  uni.setClipboardData({\r\n    data: content,\r\n    success() {\r\n      uni.showToast({\r\n        title: '复制成功',\r\n        icon: 'none'\r\n      })\r\n    }\r\n  })\r\n}\r\nconst onGoAi = () => {\r\n  uni.showToast({\r\n    title: '近期上线，敬请关注',\r\n    icon: 'none'\r\n  })\r\n}\r\n//删除单条\r\n// const handleDelete = async (guid) => {\r\n//   uni.showModal({\r\n//     title: '确认删除',\r\n//     content: '确定要删除该条消息吗？',\r\n//     success: async (res) => {\r\n//       if (res.confirm) {\r\n//         let res = await deleteMessageApi({\r\n//           merchantGuid: userStore.merchantGuid,\r\n//           sessionGuid: sessionGuid.value,\r\n//           messageGuid: guid\r\n//         })\r\n//         if (res.code === 0) {\r\n//           uni.showToast({\r\n//             title: '已删除',\r\n//             icon: 'success'\r\n//           })\r\n//           paging.value.reload()\r\n//         } else {\r\n//           uni.showToast({\r\n//             title: res.msg,\r\n//             icon: 'none'\r\n//           })\r\n//         }\r\n\r\n//       }\r\n//     }\r\n//   })\r\n// }\r\n//获取智能体详情\r\n\r\nconst getAgentDetail = async () => {\r\n  let res = await agentDetailApi({\r\n    merchantGuid: userStore.merchantGuid,\r\n    agentGuid: agentGuid.value\r\n  })\r\n  agentDetail.agentName = res.data.agentName\r\n  agentDetail.agentAvatar = res.data.agentAvatar;\r\n  commonQuestions.value = res.data.commonQuestions;\r\n  agentDetail.agentDesc = res.data.agentDesc;\r\n  agentDetail.price = res.data.priceYuan;\r\n}\r\nlet historyMsgLoading = ref(true)\r\nconst queryList = async (page, pageSize) => {\r\n  // is_all: 1\r\n  let req = {\r\n    merchantGuid: userStore.merchantGuid,\r\n    sessionGuid: sessionGuid.value,\r\n    startId: 0, //开始的聊天记录ID，当用户清除历史消息时使用\r\n    page: page, //当前页数\r\n    isAll: 0\r\n  };\r\n  try {\r\n\r\n    let res = await getMessageHistoryApi(req);\r\n    historyMsgLoading.value = false\r\n    uni.setNavigationBarTitle({\r\n      title: res.data.sessionInfo.agentInfo.agentName\r\n    })\r\n    let msg = [];\r\n    if (res.data.list.length > 0) {\r\n      msgConfig.msgId = res.data.list[0].msgId;\r\n      res.data.list.forEach((item) => {\r\n        if (item.chatRole === 'assistant') {\r\n          let type = item.contentType;\r\n          if (item.contentType === 'img') {\r\n            type = 'image'\r\n          }\r\n          if (item.contentType === 'url') {\r\n            type = 'link'\r\n          }\r\n          if (item.imageList === null) {\r\n            item.imageList = [];\r\n          }\r\n          msg.push({\r\n            role: 'assistant',\r\n            content: item.chatContent,\r\n            isSuccessData: true,\r\n            isNewMsg: false,\r\n            type: type,\r\n            guid: item.guid,\r\n            aiReplyTitle: '',\r\n            msgId: item.lastMsgId,\r\n            isCollected: item.isCollected,\r\n            imageList: item.imageList\r\n          })\r\n        } else if (item.chatRole === 'user') {\r\n          msg.push({\r\n            role: 'user',\r\n            content: item.chatContent,\r\n            msgId: item.msgId,\r\n          });\r\n        }\r\n      });\r\n    }\r\n\r\n    if (msg.length > 0 && msg[msg.length - 1].role === 'assistant') {\r\n      msg[msg.length - 1].isFirst = true\r\n    } else {\r\n      if (isFirstLoad.value) {\r\n        console.log('这里什么情况')\r\n        msg.push({\r\n          role: 'assistant',\r\n          content: agentDetail.agentDesc,\r\n          isSuccessData: false,\r\n          isLoading: false,\r\n          type: 'text',\r\n          aiReplyTitle: '',\r\n          isFirst: true,\r\n          msgId: '',\r\n          guid: 'first_none_guid',\r\n          imageList: []\r\n        })\r\n        isFirstLoad.value = false;\r\n      }\r\n    }\r\n    await paging.value.complete(msg)\r\n  } catch (error) {\r\n    historyMsgLoading.value = false\r\n    paging.value.complete(false);\r\n  }\r\n};\r\nconst onOftenClick = throttle((item) => {\r\n  //发送消息\r\n  msgContent.value = item;\r\n  onSend()\r\n}, 1000)\r\nconst headImgUrl =\r\n  'https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/2848bb198410482f9d3e6cc343a4e51e.png';\r\n//设置AI回复消息\r\nconst addAiChat = (answer, type = 'text', isFirst = false) => {\r\n  //type 'richText' 'image' 'link'  'video'\r\n  let parame = {\r\n    content: '',\r\n    title: '',\r\n    imageList: [],\r\n    // ...answer\r\n  }\r\n  let reMsg = {\r\n    role: 'assistant',\r\n    content: answer,\r\n    isSuccessData: false,\r\n    isLoading: true,\r\n    type: type,\r\n    aiReplyTitle: parame.title,\r\n    isFirst: isFirst,\r\n    isCollected: false,\r\n    msgId: '',\r\n    guid: `ai_${Date.now()}_${Math.random()}`, // 添加临时唯一标识符，后续会被服务器返回的guid替换\r\n    imageList: parame.imageList\r\n  };\r\n  paging.value.addChatRecordData(reMsg);\r\n};\r\n//设置重置消息\r\nconst addClearChat = (answer) => {\r\n  let reMsg = {\r\n    role: 'assistant',\r\n    content: answer,\r\n    isSuccessData: false,\r\n    isLoading: false,\r\n    type: 'text',\r\n    aiReplyTitle: '',\r\n    isFirst: true,\r\n    isCollected: false,\r\n    msgId: '',\r\n    guid: 'first_none_guid',\r\n    imageList: []\r\n  };\r\n  paging.value.addChatRecordData(reMsg);\r\n};\r\n//设置user发送消息\r\nconst addUserChat = (content) => {\r\n  let reMsg = {\r\n    role: 'user',\r\n    content: content,\r\n    msgId: '',\r\n    isLoading: true,\r\n    guid: `user_${Date.now()}_${Math.random()}`, // 添加唯一标识符\r\n  };\r\n  paging.value.addChatRecordData(reMsg);\r\n};\r\n// const saveUserMsg = async (content, type = 'text') => {\r\n//   try {\r\n//     msgConfig.nowAiMsgIndex = 0;\r\n//     msgUserReqParame.content = content;\r\n//     msgUserReqParame.lastMsgId = msgConfig.msgId;\r\n//     msgUserReqParame.contentType = type;\r\n//     let msgReqRes = await saveMsgApi(msgUserReqParame);\r\n//     console.log(msgReqRes, '----------msgReqRes')\r\n//     if (msgReqRes.code !== 0) {\r\n\r\n//       dataList.value[msgConfig.nowAiMsgIndex].content = 'e-2服务繁忙，请稍候重试';\r\n//       isSending.value = false\r\n//       return;\r\n//     }\r\n//     msgConfig.msgId = msgReqRes.data.msgId;\r\n//     dataList.value[msgConfig.nowAiMsgIndex + 1].msgId = msgReqRes.data.msgId;\r\n//     msgContent.value = \"\";\r\n//     createContact()\r\n//   } catch (error) {\r\n//     console.log(error, '----------error')\r\n//     dataList.value[msgConfig.nowAiMsgIndex].isLoading = false;\r\n//     if (msgReqRes.code === 900001) {\r\n//       ended.value = true;\r\n//       isSending.value = false\r\n//       return\r\n//     }\r\n\r\n//   }\r\n// }\r\nconst onGotoMini = (appid) => {\r\n  uni.navigateToMiniProgram({\r\n    appId: appid,\r\n    success(res) {\r\n      // 打开成功\r\n      console.log(res, 'success')\r\n    },\r\n    fail(res) {\r\n      console.log(res, 'fail')\r\n    }\r\n  })\r\n}\r\n\r\n// 聊天输入框内容\r\nconst msgContent = ref('');\r\n//聊天全局配置\r\nconst msgConfig = reactive({\r\n  isNewGlobalMsg: false,\r\n  nowAiMsgIndex: 0,\r\n  msgId: '',\r\n  nowChatLunciGuid: ''\r\n})\r\nlet isMsgInput = ref(true)\r\nconst onMsgFocus = () => {\r\n  isMsgInput.value = false\r\n};\r\nconst onMsgBlur = () => {\r\n  isMsgInput.value = true\r\n};\r\nconst convertStringToArr = (str) => {\r\n  // 格式化返回的流式数据 这个方法也可以提出去 我这里方便展示'\r\n  let arr = []\r\n  str.trim().split('\\n').forEach((item) => {\r\n    if (item.length > 0 && item) {\r\n      const [key, value] = item.split(': ');\r\n      if (value && value.trim()) {\r\n        arr.push({\r\n          [key]: value\r\n        })\r\n      }\r\n    }\r\n  });\r\n  return arr;\r\n}\r\nconst base64ToUtf8 = (base64String) => {\r\n  // new TextDecoder() 小程序真机中没有这个方法，得下载一个这个 text-encoding\r\n  // npm install text-encoding --save-dev\r\n  // 引入import { TextDecoder } from \"text-encoding/lib/encoding\"; \r\n  const bytes = uni.base64ToArrayBuffer(base64String);\r\n  const utf8String = new TextDecoder().decode(bytes);\r\n  return utf8String;\r\n}\r\nconst globalTimer = ref('')\r\nconst showTime = ref(30)\r\nconst createContact = async () => {\r\n  let alltext = '';\r\n  let isalltext = false;\r\n  let isstarted = true;\r\n\r\n  // 清除之前的定时器，防止多个定时器同时运行\r\n  if (globalTimer.value) {\r\n    clearInterval(globalTimer.value);\r\n    globalTimer.value = '';\r\n  }\r\n  // https://xyapi.cloneyou.cn/ https://ai-api.deepcity.cn/ &conversation_id=${conversation_id.value}\r\n  let url = `${base.baseUrl}/useragent/api.AiAgentChat/sendOpen?msgId=${msgConfig.msgId}&chatLunciGuid=${sessionGuid.value}&merchantGuid=${userStore.merchantGuid}&conversation_id=${conversation_id.value}`\r\n  const requestTask = wx.request({\r\n    url: url,\r\n    method: 'GET',\r\n    enableChunked: true,\r\n  })\r\n\r\n  requestTask.onChunkReceived((res) => {\r\n    if (isstarted) {\r\n      isstarted = false;\r\n      //设置 是否是AI回答列表中的最新一条\r\n      // dataList.value[msgConfig.nowAiMsgIndex].isNewMsg = false\r\n      let temp = '' //临时显示文本\r\n      let index = 0 //初始下标为0\r\n      globalTimer.value = setInterval(async () => {\r\n        alltext = alltext.replace(/\\\\n/g, '\\n')\r\n        let previousTemp = temp;\r\n        if (temp.length < alltext.length) {\r\n          //如果比原始文本短\r\n          temp += alltext[index] //就往临时显示文本添加一个字符\r\n          index = index + 1 //下标+1\r\n        } else {\r\n          if (isalltext) {\r\n            //清除定时器\r\n            isSending.value = false\r\n            clearInterval(globalTimer.value)\r\n            globalTimer.value = ''\r\n            msgAiReqParame.content = temp;\r\n            //更新msgID\r\n            msgAiReqParame.lastMsgId = msgConfig.msgId;\r\n            msgAiReqParame.contentType = \"richText\";\r\n            msgAiReqParame.sessionGuid = sessionGuid.value;\r\n            dataList.value[msgConfig.nowAiMsgIndex].isLoading = false;\r\n            try {\r\n              let msgReqRes = await saveMsgApi(msgAiReqParame);\r\n              if (msgReqRes.code !== 0) {\r\n                msgConfig.isNewGlobalMsg = false;\r\n                dataList.value[msgConfig.nowAiMsgIndex].content = 'e-2服务繁忙，请稍候重试';\r\n                return;\r\n              }\r\n              msgConfig.msgId = msgReqRes.data.msgId;\r\n              msgConfig.isNewGlobalMsg = false;\r\n              dataList.value[msgConfig.nowAiMsgIndex].guid = msgReqRes.data.messageGuid;\r\n              dataList.value[msgConfig.nowAiMsgIndex].isSuccessData = true;\r\n              nextTick(() => {\r\n                paging.value.scrollToBottom();\r\n              });\r\n            } catch (error) {\r\n              if (error.code === 900001) {\r\n                ended.value = true;\r\n                isSending.value = false\r\n                return\r\n              }\r\n            }\r\n\r\n          }\r\n        }\r\n        //只有当内容发生变化时才更新显示文本，减少不必要的DOM更新\r\n        if (temp !== previousTemp) {\r\n          dataList.value[msgConfig.nowAiMsgIndex].content = temp\r\n        }\r\n      }, showTime.value)\r\n    }\r\n    let arrayBuffer = res.data; // 接收持续返回的数据\r\n    let uint8Array = new Uint8Array(arrayBuffer);\r\n    let text = uni.arrayBufferToBase64(uint8Array);\r\n    text = base64ToUtf8(text);\r\n    let textArr = convertStringToArr(text)\r\n    // console.log(textArr, 'textArr');\r\n    textArr.map((v) => {\r\n      if (v.hasOwnProperty('data') && v.data) {\r\n        // 这里的xx为流式传输的关键词  如果有多个关键词 需要写多个if判断\r\n        if (v.data == '[DONE]') {\r\n          conversation_id.value = textArr[textArr.length - 2].id;\r\n          isalltext = true;\r\n          requestTask.abort()\r\n          return\r\n        }\r\n        if (alltext === '') {\r\n          alltext = v.data.replace(/^\\n+/, '')\r\n        } else {\r\n          alltext += v.data\r\n        }\r\n      }\r\n    })\r\n\r\n  });\r\n};\r\nlet msgUserReqParame = reactive({\r\n  merchantGuid: userStore.merchantGuid,\r\n  sessionGuid: '',\r\n  role: 'user',\r\n  content: '',\r\n  lastMsgId: '',\r\n  contentType: 'text',\r\n  sceneValue: \"\",\r\n  chatLunciGuid: \"\"\r\n})\r\nlet msgAiReqParame = reactive({\r\n  merchantGuid: userStore.merchantGuid,\r\n  role: 'assistant',\r\n  sessionGuid: '',\r\n  content: '',\r\n  lastMsgId: '',\r\n  contentType: '',\r\n  sceneValue: \"\",\r\n  chatLunciGuid: \"\",\r\n  imgs: [],\r\n})\r\nconst onViceoSend = () => {\r\n  onSend()\r\n}\r\n\r\nlet isFirstSend = ref(false)\r\nconst onSend = async () => {\r\n  if (msgContent.value.trim().length === 0) {\r\n    wx.showToast({\r\n      title: \"请输入问题 ...\",\r\n      icon: \"none\",\r\n      duration: 1000\r\n    });\r\n    return;\r\n  }\r\n  if (isSending.value) {\r\n    wx.showToast({\r\n      title: \"回答中...\",\r\n      icon: \"none\",\r\n      duration: 1000\r\n    });\r\n    return\r\n  }\r\n  isSending.value = true\r\n\r\n  // 清除之前的定时器，防止多个定时器同时运行\r\n  if (globalTimer.value) {\r\n    clearInterval(globalTimer.value);\r\n    globalTimer.value = '';\r\n  }\r\n\r\n  //写入用户发送的消息\r\n  addUserChat(msgContent.value)\r\n  // addAiChat({\r\n  //   content: ''\r\n  // })\r\n  addAiChat(' ')\r\n  msgConfig.nowAiMsgIndex = 0;\r\n  msgUserReqParame.content = msgContent.value;\r\n  msgUserReqParame.lastMsgId = msgConfig.msgId;\r\n  msgUserReqParame.sessionGuid = sessionGuid.value;\r\n  try {\r\n    let msgReqRes = await saveMsgApi(msgUserReqParame);\r\n    isFirstSend.value = true;\r\n    console.log(msgReqRes, '----------msgReqRes')\r\n    if (msgReqRes.code !== 0) {\r\n\r\n      dataList.value[msgConfig.nowAiMsgIndex].content = 'e-2服务繁忙，请稍候重试';\r\n      isSending.value = false\r\n      return;\r\n    }\r\n    msgConfig.msgId = msgReqRes.data.msgId;\r\n    dataList.value[msgConfig.nowAiMsgIndex + 1].msgId = msgReqRes.data.msgId;\r\n    msgContent.value = \"\";\r\n    createContact()\r\n  } catch (error) {\r\n    console.log(error, '---------error');\r\n    isSending.value = false;\r\n    dataList.value[msgConfig.nowAiMsgIndex].isLoading = false;\r\n    if (error.code === 900001) {\r\n      dataList.value[msgConfig.nowAiMsgIndex].content = '试用次数已用完，请订阅激活';\r\n      ended.value = true;\r\n      return\r\n    } else {\r\n      dataList.value[msgConfig.nowAiMsgIndex].content = `${error.msg}-${error.code}`;\r\n    }\r\n  }\r\n};\r\n\r\n\r\nconst onMsgLink = (link) => {\r\n  uni.navigateTo({\r\n    url: '/pages/webview/webview',\r\n    success: (res) => {\r\n      res.eventChannel.emit('urlEvent', decodeURIComponent(link));\r\n    },\r\n    fail(res) {\r\n      console.log(res,)\r\n    }\r\n  });\r\n}\r\n\r\n\r\nconst getAuthSetting = () => {\r\n  wx.getSetting({\r\n    success(res) {\r\n      if (!res.authSetting['scope.record']) {\r\n        authPopup.value.open();\r\n      } else {\r\n        recordConfig.isRecorderMode = !recordConfig.isRecorderMode;\r\n      }\r\n    }\r\n  })\r\n}\r\nconst authCallback = (res) => {\r\n  authPopup.value.close();\r\n}\r\n\r\n//点击预览大图\r\nconst onLookImg = (urls, isMore, index) => {\r\n  let img = [];\r\n  if (isMore) {\r\n    img = urls;\r\n  } else {\r\n    img[0] = urls\r\n  }\r\n  uni.previewImage({\r\n    current: index,\r\n    urls: img,\r\n    longPressActions: {\r\n      itemList: ['发送给朋友', '保存图片', '收藏'],\r\n      success: function (data) { },\r\n      fail: function (err) {\r\n        console.log(err.errMsg);\r\n      },\r\n    },\r\n  });\r\n}\r\n\r\n\r\n//进入发送语音模式\r\nconst recordConfig = reactive({\r\n  recorderMode: 1, // 1显示 按住说话 2显示 说话中\r\n  isRecorderMode: false, //是否显示 语音输入按钮\r\n  voiceText: '按住说话',\r\n  voiceTitle: '松手结束录音',\r\n  // delShow: false,\r\n  sendLock: true, //发送锁，当为true时上锁，false时解锁发送\r\n  isCloseSend: false, //是否取消发送\r\n  // time: 0, //录音时长\r\n  // tempFilePath: '', //音频路径\r\n  duration: 60000, //录音最大值ms 60000/1分钟\r\n  startPoint: null, //记录长按录音开始点信息,用于后面计算滑动距离。\r\n  isAnalyzeDisabled: false, //是否在文本解析过程中\r\n  recording: false // 正在录音\r\n})\r\n// const onRecorderMode = () => {\r\n//   getAuthSetting()\r\n// }\r\n// 长按录音事件\r\nconst onLongTap = (e) => {\r\n  if (!recorderPermission.value) {\r\n    wx.showToast({\r\n      title: \"当前版本不支持,或您未打开麦克风使用权限\",\r\n      icon: \"none\",\r\n      duration: 1000\r\n    });\r\n    return;\r\n  }\r\n  if (recordConfig.isAnalyzeDisabled) {\r\n    wx.showToast({\r\n      title: \"正在识别语音中...\",\r\n      icon: \"none\",\r\n      duration: 1000\r\n    });\r\n    return;\r\n  }\r\n  if (msgConfig.isNewGlobalMsg) {\r\n    wx.showToast({\r\n      title: \"正在回答中...\",\r\n      icon: \"none\",\r\n      duration: 1000\r\n    });\r\n    return;\r\n  }\r\n\r\n  recordConfig.startPoint = e.touches[0];\r\n  recordConfig.recorderMode = 2;\r\n  recordConfig.voiceText = '说话中...';\r\n  recordConfig.recording = true;\r\n  //插件开始录音\r\n  manager.start({\r\n    duration: 60000,\r\n    lang: 'zh_CN'\r\n  })\r\n  recordConfig.sendLock = false;\r\n}\r\n//长按松开录音事件\r\nconst onTouchend = () => {\r\n  if (recordConfig.isCloseSend) {\r\n    recordInit()\r\n    return;\r\n  }\r\n  if (recordConfig.isAnalyzeDisabled) {\r\n    return;\r\n  }\r\n  if (msgConfig.isNewGlobalMsg) {\r\n    return;\r\n  }\r\n  recordInit()\r\n  if (!recordConfig.recording) {\r\n    return\r\n  }\r\n  //插件停止录音\r\n  manager.stop()\r\n  recordConfig.isAnalyzeDisabled = true;\r\n  if (!recordConfig.sendLock) {\r\n    uni.showLoading({\r\n      title: '识别中',\r\n      mask: true,\r\n    })\r\n  }\r\n}\r\n\r\n// 删除录音\r\nconst onTouchMove = (e) => {\r\n  if (!e || !e.touches || e.touches.length === 0 || !recordConfig.startPoint) return;\r\n  //touchmove时触发\r\n  let moveLenght = e.touches[e.touches.length - 1].clientY - recordConfig.startPoint.clientY; //移动距离\r\n  if (Math.abs(moveLenght) > 30) {\r\n    recordConfig.voiceTitle = \"松开手指,取消发送\";\r\n    recordConfig.voiceText = '松开手指,取消发送';\r\n    recordConfig.sendLock = true; //触发了上滑取消发送，上锁\r\n    recordConfig.isCloseSend = true; //触发了上滑取消发送 改变样式\r\n  } else {\r\n    recordConfig.voiceTitle = \"松手结束录音\";\r\n    recordConfig.voiceText = '松手结束录音';\r\n    recordConfig.sendLock = false; //上划距离不足，依然可以发送，不上锁\r\n    recordConfig.isCloseSend = false; //触发了上滑取消发送 改变样式\r\n  }\r\n}\r\n//初始化录音配置\r\nconst recordInit = () => {\r\n  // recordConfig.delShow = false;\r\n  // recordConfig.time = 0;\r\n  // recordConfig.tempFilePath = '';\r\n  recordConfig.recorderMode = 1;\r\n  recordConfig.voiceText = '按住说话';\r\n  recordConfig.voiceTitle = '松手结束录音';\r\n  recordConfig.isCloseSend = false;\r\n}\r\n\r\nconst initRecord = () => {\r\n  //有新的识别内容返回，则会调用此事件\r\n  manager.onRecognize = (res) => {\r\n    if (recordConfig.sendLock) {\r\n      return\r\n    } else {\r\n      msgContent.value = res.result;\r\n      onViceoSend()\r\n    }\r\n  }\r\n\r\n  // 识别结束事件\r\n  manager.onStop = (res) => {\r\n    console.log('onStoponStop', res)\r\n    recordConfig.isAnalyzeDisabled = false;\r\n    recordConfig.recording = false;\r\n    if (recordConfig.sendLock) {\r\n      return\r\n    }\r\n    uni.hideLoading();\r\n    let text = res.result;\r\n    if (text == '') {\r\n      // wx.showToast({\r\n      // \ttitle: \"未识别到内容\",\r\n      // \ticon: \"none\",\r\n      // \tduration: 1000\r\n      // });\r\n      return\r\n    }\r\n    msgContent.value = text;\r\n    onViceoSend()\r\n  }\r\n\r\n  // 识别错误事件\r\n  manager.onError = (res) => {\r\n    uni.hideLoading();\r\n    recordConfig.isAnalyzeDisabled = false;\r\n    recordConfig.recording = false;\r\n    let code = res.retcode;\r\n    switch (code) {\r\n      case -30003:\r\n        wx.showToast({\r\n          title: \"未检测到语音\",\r\n          icon: \"none\",\r\n          duration: 1000\r\n        });\r\n        break;\r\n      default:\r\n        wx.showToast({\r\n          title: \"未检测到语音~\",\r\n          icon: \"none\",\r\n          duration: 1000\r\n        });\r\n        break;\r\n    }\r\n  }\r\n}\r\n\r\n\r\n//获取麦克风权限\r\nconst getRecordPer = () => {\r\n  wx.getSetting({\r\n    success(res) {\r\n      if (!res.authSetting['scope.record']) {\r\n        wx.authorize({\r\n          scope: 'scope.record',\r\n          success() {\r\n            recorderPermission.value = true;\r\n          },\r\n          fail() {\r\n            recorderPermission.value = false;\r\n          },\r\n        });\r\n      } else {\r\n        recorderPermission.value = true;\r\n      }\r\n    },\r\n  });\r\n}\r\n// // 显示订阅弹窗\r\nconst showSubscribePopup = () => {\r\n  showSubscribeModal.value = true\r\n}\r\n\r\n// // 关闭订阅弹窗\r\nconst closeSubscribePopup = () => {\r\n  showSubscribeModal.value = false\r\n}\r\nconst handleSubscribeConfirm = async () => {\r\n  let payInfo = await createPurchaseOrderApi({\r\n    merchantGuid: userStore.merchantGuid,\r\n    agentGuid: agentGuid.value,\r\n    payEnv: 'xcx'\r\n  })\r\n  miniPay(payInfo.data.payInfo).then(\r\n    async res => {\r\n      queryPayChatStauts(payInfo.data.orderNo, queryStatusNum);\r\n    },\r\n    res => {\r\n      uni.showToast({\r\n        title: res.msg,\r\n      });\r\n    }\r\n  );\r\n}\r\nconst queryPayChatStauts = async (orderNo, number) => {\r\n  number++;\r\n  try {\r\n    let orderInfo = await queryPurchaseOrderApi({\r\n      orderNo,\r\n    });\r\n    if (orderInfo.data.isPaid) {\r\n      uni.showToast({\r\n        title: '支付成功',\r\n      });\r\n      ended.value = false\r\n      showSubscribeModal.value = false\r\n    } else {\r\n      if (number > 12) {\r\n        uni.showToast({\r\n          title: '支付失败',\r\n        });\r\n      } else {\r\n        queryPayChatStauts(orderNo, number);\r\n      }\r\n    }\r\n  } catch (e) {\r\n    uni.showToast({\r\n      title: e.msg ? e.msg : '支付失败',\r\n    });\r\n  }\r\n};\r\nonLoad(async (params) => {\r\n  // #ifdef MP-WEIXIN\r\n  getRecordPer();\r\n  initRecord();\r\n  // #endif\r\n  if (params.sessionGuid) {\r\n    agentGuid.value = params.sessionGuid;\r\n    await subscribeAgent()\r\n    await getAgentDetail()\r\n  }\r\n  if (userStore.userToken) {\r\n    nextTick(() => {\r\n      if (paging.value) {\r\n        paging.value.reload()\r\n      }\r\n    })\r\n  }\r\n});\r\n</script>\r\n<style scoped lang=\"scss\">\r\n.container {\r\n  background: #f7f7f7;\r\n  min-height: 100vh;\r\n  background-repeat: no-repeat;\r\n  background-size: 100% 518rpx;\r\n  width: 100%;\r\n  overflow-x: hidden;\r\n}\r\n\r\n.onAdminEdit {\r\n  // position: absolute;\r\n  // left: 0;\r\n  // top: 0;\r\n  width: 100%;\r\n  height: 80rpx;\r\n  background-color: #ffffff;\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  align-items: center;\r\n\r\n  .edit-btn {\r\n    border-radius: 10rpx;\r\n    border: 2rpx solid #333333;\r\n    padding: 2rpx 10rpx;\r\n    width: fit-content;\r\n    margin-right: 20rpx;\r\n    font-size: 22rpx;\r\n\r\n    &.on {\r\n      color: #FA5151;\r\n      border-color: #FA5151;\r\n    }\r\n  }\r\n}\r\n\r\n.auth-pop-box {\r\n  width: 680rpx;\r\n  padding: 20px;\r\n  box-sizing: border-box;\r\n  background-color: #fff;\r\n  border-radius: 8rpx;\r\n\r\n  .title {\r\n    font-size: 28rpx;\r\n    text-align: center;\r\n  }\r\n\r\n  .btn {\r\n    margin-top: 20px;\r\n    width: 100%;\r\n    height: 90rpx;\r\n    background: linear-gradient(90deg, #8d40f8 0%, #5e24f5 100%);\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    border-radius: 12rpx;\r\n    font-weight: 600;\r\n    font-size: 30rpx;\r\n    color: #ffffff;\r\n  }\r\n}\r\n\r\n// .spinner {\r\n//   --accent: #ffffff;\r\n//   --max-scale: 4;\r\n//   --speed: 0.2;\r\n//   display: flex;\r\n//   gap: 0.5em;\r\n//   transform: skew(15deg, 10deg);\r\n//   margin-top: 40rpx;\r\n// }\r\n\r\n// .spinner .sub-spinner {\r\n//   display: block;\r\n//   background-color: var(--accent);\r\n//   box-shadow: 1px 1px 5px 0.2px var(--accent);\r\n//   width: 1px;\r\n//   height: 0.6em;\r\n// }\r\n\r\n// .spinner .spinner-part-0 {\r\n//   animation: load432 calc(1s/var(--speed)) linear infinite;\r\n// }\r\n\r\n// .spinner .spinner-part-1 {\r\n//   animation: load432 calc(0.16s/var(--speed)) linear infinite;\r\n// }\r\n\r\n// .spinner .spinner-part-2 {\r\n//   animation: load432 calc(0.4s/var(--speed)) linear infinite;\r\n// }\r\n\r\n// .spinner .spinner-part-3 {\r\n//   animation: load432 calc(0.5s/var(--speed)) linear infinite;\r\n// }\r\n\r\n// @keyframes load432 {\r\n//   50% {\r\n//     transform: scaleY(var(--max-scale));\r\n//   }\r\n// }\r\n\r\n// .recode-box {\r\n//   width: 0;\r\n//   height: 0;\r\n//   background-color: rgba(0, 0, 0, .6);\r\n//   position: fixed;\r\n//   z-index: 10;\r\n//   bottom: 0;\r\n//   left: 0;\r\n//   overflow: hidden;\r\n\r\n//   &.record-layer {\r\n//     width: 100vw;\r\n//     height: 100vh;\r\n//   }\r\n\r\n//   .recode-loading-box {\r\n//     width: 70vw;\r\n//     height: 200rpx;\r\n//     color: #ffffff;\r\n//     border-radius: 10rpx;\r\n//     background: linear-gradient(136deg, #7531F6 0%, #686BF2 100%);\r\n//     position: absolute;\r\n//     bottom: 300rpx;\r\n//     left: 50%;\r\n//     transform: translateX(-50%);\r\n//     display: flex;\r\n//     flex-direction: column;\r\n//     justify-content: space-around;\r\n//     align-items: center;\r\n//     transition: all 0.3s;\r\n\r\n//     &.is-close-send {\r\n//       background: linear-gradient(136deg, #7e2c2c 0%, #ac3838 100%)\r\n//     }\r\n\r\n//     .spinner-title {\r\n//       margin-top: 40rpx;\r\n//       font-size: 24rpx;\r\n//     }\r\n//   }\r\n// }\r\n\r\n\r\n.on-load-more-box {\r\n  text-align: center;\r\n  padding: 20rpx 0;\r\n  color: #66648a;\r\n  font-size: 24rpx;\r\n}\r\n\r\n.chat-main {\r\n  padding-left: 20rpx;\r\n  padding-right: 20rpx;\r\n  padding-top: 40rpx;\r\n  display: flex;\r\n  flex-direction: column;\r\n  // padding-bottom: 202rpx;\r\n}\r\n\r\n.agent-logo {\r\n  display: flex;\r\n  justify-content: center;\r\n  margin: 30px 0 20px 0;\r\n\r\n  .logo {\r\n    display: block;\r\n    width: 200rpx;\r\n    height: 200rpx;\r\n    border-radius: 50%;\r\n  }\r\n}\r\n\r\n.chat-ls {\r\n  padding-bottom: 20px;\r\n  position: relative;\r\n\r\n  .msg-m {\r\n    display: flex;\r\n    padding: 20rpx 0;\r\n    width: 100%;\r\n\r\n    .logo-box {\r\n      width: 60rpx;\r\n      height: 60rpx;\r\n      border-radius: 50%;\r\n      box-sizing: border-box;\r\n\r\n      .user-img {\r\n        flex: none;\r\n        display: block;\r\n        width: 100%;\r\n        height: 100%;\r\n        border-radius: 50%;\r\n      }\r\n    }\r\n\r\n    .msg-text {\r\n      font-size: 32rpx;\r\n      line-height: 44rpx;\r\n      padding: 18rpx 24rpx;\r\n      box-sizing: border-box;\r\n\r\n      .rich-text-box {\r\n        .t {\r\n          word-wrap: break-word;\r\n        }\r\n\r\n        .richImg {\r\n          width: 100% !important;\r\n          height: auto !important;\r\n        }\r\n\r\n        .often-questions {\r\n          margin-top: 30rpx;\r\n          // position: relative;\r\n\r\n          .item {\r\n            color: #000;\r\n            font-size: 28rpx;\r\n            padding-left: 30rpx;\r\n            position: relative;\r\n\r\n            &::before {\r\n              content: '';\r\n              display: block;\r\n              width: 10rpx;\r\n              height: 10rpx;\r\n              background-color: #000;\r\n              border-radius: 50%;\r\n              position: absolute;\r\n              left: 8rpx;\r\n              top: 50%;\r\n              transform: translateY(-50%);\r\n            }\r\n          }\r\n\r\n        }\r\n\r\n        .img-list-box {\r\n          margin-top: 10rpx;\r\n          // display: flex;\r\n          // flex-wrap: wrap;\r\n          // justify-content: space-between;\r\n\r\n          &.noWrap {\r\n            .img {\r\n              flex: 1;\r\n              // width: 48%;\r\n              height: auto;\r\n            }\r\n          }\r\n\r\n          .img {\r\n            max-width: 100%;\r\n            height: auto;\r\n            display: block;\r\n          }\r\n        }\r\n      }\r\n\r\n\r\n\r\n      .img-box {\r\n        // margin-top: 10rpx;\r\n\r\n        .img {\r\n          max-width: 510rpx;\r\n          // height: 300rpx;\r\n          display: block;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .msg-left {\r\n    flex-direction: row;\r\n    position: relative;\r\n\r\n    .msg-text {\r\n      margin-left: 16rpx;\r\n      font-size: 32rpx;\r\n      // background-color: rgba(247, 247, 247, 1);\r\n      // background: linear-gradient(254deg, rgba(76, 106, 254, 0.1), rgba(91, 118, 254, 0.1));\r\n      background-color: #F9F9F9;\r\n      box-shadow: 0rpx 3rpx 6rpx 1rpx rgba(35, 0, 131, 0.05);\r\n      border-radius: 0rpx 20rpx 20rpx 20rpx;\r\n      max-width: 680rpx;\r\n      color: #333333;\r\n      margin-top: 50rpx;\r\n      position: relative;\r\n\r\n      &.hasReply {\r\n        margin-top: 0px;\r\n      }\r\n\r\n      .text-box {\r\n        .link {\r\n          word-break: break-all;\r\n          text-decoration: underline;\r\n          border-bottom: 1px solid #66648a;\r\n        }\r\n      }\r\n\r\n      .video-box {\r\n        width: 512rpx;\r\n\r\n        .video-dom {\r\n          width: 100%;\r\n          height: 300rpx;\r\n        }\r\n      }\r\n\r\n      .mini-box {\r\n        width: 512rpx;\r\n\r\n        .btn {\r\n          width: 100%;\r\n          font-size: 26rpx;\r\n          height: 70rpx;\r\n          line-height: 70rpx;\r\n          text-align: center;\r\n          transition: background 0.3s;\r\n          color: #ffffff;\r\n          background: linear-gradient(90deg, #8D40F8 0%, #5E24F5 100%);\r\n          border-radius: 12rpx 12rpx 12rpx 12rpx;\r\n        }\r\n      }\r\n    }\r\n\r\n    .other-box {\r\n      border-top: 1px solid #ECECEC;\r\n      padding-top: 20rpx;\r\n      margin-top: 20rpx;\r\n      display: flex;\r\n\r\n      .icon {\r\n        display: block;\r\n        width: 60rpx;\r\n        height: 60rpx;\r\n        margin-right: 8rpx;\r\n      }\r\n\r\n      .text-btn {\r\n        background-color: #E7EDFA;\r\n        color: #2963F6;\r\n        font-size: 24rpx;\r\n        height: 60rpx;\r\n        border-radius: 16rpx;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        padding: 0 16rpx;\r\n        margin-left: auto;\r\n      }\r\n    }\r\n\r\n    .ai-reply-msg {\r\n      color: #66648a;\r\n      font-size: 26rpx;\r\n      padding-left: 16rpx;\r\n      overflow: hidden;\r\n      white-space: nowrap;\r\n      text-overflow: ellipsis;\r\n      position: absolute;\r\n      top: -50rpx;\r\n      left: 0;\r\n    }\r\n\r\n\r\n  }\r\n\r\n  .msg-right {\r\n    flex-direction: row-reverse;\r\n    position: relative;\r\n\r\n    .msg-text {\r\n      margin-right: 16rpx;\r\n      font-size: 32rpx;\r\n      // background: linear-gradient(136deg, #7531f6 0%, #686bf2 100%);\r\n      color: #fff;\r\n      background-color: #5380F2;\r\n      box-shadow: 0rpx 3rpx 6rpx 1rpx rgba(35, 0, 131, 0.05);\r\n      border-radius: 20rpx 0rpx 20rpx 20rpx;\r\n      // border-radius: 10rpx;\r\n      max-width: 560rpx;\r\n      word-break: break-all;\r\n      word-wrap: break-word;\r\n    }\r\n  }\r\n}\r\n\r\n//试用结束\r\n.sy-end-box {\r\n  padding: 20px 0 30px 0;\r\n  background-color: #fff;\r\n  font-size: 30rpx;\r\n  text-align: center;\r\n  color: #2A64F6;\r\n  z-index: 9;\r\n\r\n  .end-btn {\r\n    width: 530rpx;\r\n    margin: 20px auto 0 auto;\r\n    height: 90rpx;\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    background: #2A64F6;\r\n    border-radius: 50rpx 50rpx 50rpx 50rpx;\r\n    color: #fff;\r\n  }\r\n}\r\n\r\n.submit-box {\r\n  width: 100%;\r\n  font-size: 30rpx;\r\n  display: flex;\r\n  flex-direction: column;\r\n  box-sizing: border-box;\r\n  background-color: #fff;\r\n  position: relative;\r\n  padding-top: 20rpx;\r\n  padding-left: 30rpx;\r\n  padding-right: 30rpx;\r\n  /*  #ifdef MP-WEIXIN */\r\n  /* 为兼容某些Android版本微信小程序，添加最小padding保障 */\r\n  padding-bottom: calc(env(safe-area-inset-bottom, 40rpx) - 20rpx);\r\n  /* 降级方案：当env()不支持时使用固定值 */\r\n  padding-bottom: 40rpx;\r\n  // padding-bottom: calc(env(safe-area-inset-bottom) + 20rpx);\r\n  /*  #endif  */\r\n  /*  #ifdef H5 */\r\n  padding-bottom: 20rpx;\r\n  /*  #endif  */\r\n\r\n  .util-question-box {\r\n    display: flex;\r\n    overflow-x: scroll;\r\n    overflow-y: hidden;\r\n    margin-top: 20rpx;\r\n\r\n    .item {\r\n      display: flex;\r\n      background-color: #efefff;\r\n      border-radius: 12rpx;\r\n      padding: 19rpx;\r\n      margin-right: 20rpx;\r\n      align-items: center;\r\n      width: fit-content;\r\n      flex: 0 0 auto;\r\n\r\n      .icon {\r\n        display: block;\r\n        width: 32rpx;\r\n        height: 32rpx;\r\n      }\r\n\r\n      .text {\r\n        margin-left: 6rpx;\r\n        font-size: 26rpx;\r\n        background: linear-gradient(95deg, #40aaf8 0%, #6732f6 52%, #5e24f5 100%);\r\n        -webkit-background-clip: text;\r\n        -webkit-text-fill-color: transparent;\r\n      }\r\n    }\r\n  }\r\n\r\n  .sub-input-box {\r\n    display: flex;\r\n    align-items: center;\r\n    border-radius: 12rpx;\r\n    // border: 4rpx solid #686bf2;\r\n    padding: 10rpx 0px;\r\n    background-color: #F7F7F7;\r\n    position: relative;\r\n\r\n    &.mt {\r\n      margin-top: 20rpx;\r\n    }\r\n\r\n    .recode-loading-box {\r\n      position: absolute;\r\n      width: 100%;\r\n      height: 100%;\r\n      display: flex;\r\n      background: linear-gradient(136deg, #7531F6 0%, #686BF2 100%);\r\n      justify-content: center;\r\n      align-items: center;\r\n      border-radius: 12rpx;\r\n\r\n      &.is-close-send {\r\n        background: linear-gradient(136deg, #d45757 0%, #ca3333 100%)\r\n      }\r\n    }\r\n\r\n    .spinner-title {\r\n      position: absolute;\r\n      width: 100%;\r\n      text-align: center;\r\n      top: -40px;\r\n      left: 0;\r\n      z-index: 9;\r\n    }\r\n\r\n    .spinner {\r\n      --accent: #ffffff;\r\n      --max-scale: 4;\r\n      --speed: 0.2;\r\n      display: flex;\r\n      gap: 0.5em;\r\n    }\r\n\r\n    .spinner .sub-spinner {\r\n      display: block;\r\n      background-color: var(--accent);\r\n      box-shadow: 1px 1px 5px 0.2px var(--accent);\r\n      width: 1px;\r\n      height: 0.4em;\r\n    }\r\n\r\n    .spinner .spinner-part-0 {\r\n      animation: load432 calc(1s/var(--speed)) linear infinite;\r\n    }\r\n\r\n    .spinner .spinner-part-1 {\r\n      animation: load432 calc(0.16s/var(--speed)) linear infinite;\r\n    }\r\n\r\n    .spinner .spinner-part-2 {\r\n      animation: load432 calc(0.4s/var(--speed)) linear infinite;\r\n    }\r\n\r\n    .spinner .spinner-part-3 {\r\n      animation: load432 calc(0.5s/var(--speed)) linear infinite;\r\n    }\r\n\r\n    @keyframes load432 {\r\n      50% {\r\n        transform: scaleY(var(--max-scale));\r\n      }\r\n    }\r\n  }\r\n\r\n\r\n  .voice-box {\r\n    .icon {\r\n      width: 60rpx;\r\n      height: 60rpx;\r\n      display: block;\r\n    }\r\n  }\r\n\r\n  .send-record-box {\r\n    width: 100%;\r\n    // flex: 1;\r\n    height: 90rpx;\r\n    line-height: 90rpx;\r\n    text-align: center;\r\n    transition: background 0.3s;\r\n    // height: 90rpx;\r\n    color: #ffffff;\r\n    background-color: #333333;\r\n    border-radius: 12rpx 12rpx 12rpx 12rpx;\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n\r\n    .icon {\r\n      display: block;\r\n      height: 60rpx;\r\n      width: 60rpx;\r\n    }\r\n\r\n    &.longPress {\r\n      color: #ffffff;\r\n      padding-top: 10rpx;\r\n      width: 1500rpx;\r\n      position: fixed;\r\n      left: -50%;\r\n      height: 750rpx;\r\n      background: linear-gradient(136deg, #7531F6 0%, #686BF2 100%);\r\n      z-index: 11;\r\n      bottom: 0;\r\n      transform: translateY(75%);\r\n      border-radius: 50%;\r\n    }\r\n\r\n    &.is-close-send {\r\n      background: linear-gradient(136deg, #f63131 0%, #f26868 100%)\r\n    }\r\n  }\r\n\r\n  .input-box {\r\n    width: 100%;\r\n    display: flex;\r\n    align-items: center;\r\n    // background-color: #ccc;\r\n    // padding: 5rpx;\r\n    min-height: 60rpx;\r\n    max-height: 300rpx;\r\n    box-sizing: border-box;\r\n\r\n    .textarea-box {\r\n      flex: 1;\r\n      // border: 1px solid #f2f2f2;\r\n      min-height: 60rpx;\r\n      // background-color: #f2f2f2;\r\n      display: flex;\r\n      align-items: center;\r\n      box-sizing: border-box;\r\n      padding: 0 20rpx;\r\n      border-radius: 20rpx;\r\n    }\r\n\r\n    .textarea-input {\r\n      width: 100%;\r\n      color: #333333;\r\n      max-height: 60px;\r\n      overflow-y: scroll;\r\n      box-sizing: border-box;\r\n      font-size: 26rpx;\r\n    }\r\n\r\n    .input {\r\n      flex: 1;\r\n      color: #333333;\r\n      height: 60rpx;\r\n      line-height: 60rpx;\r\n    }\r\n\r\n    .btn-box {\r\n      display: flex;\r\n      width: fit-content;\r\n\r\n      // width: 60rpx;\r\n      // height: 60rpx;\r\n      .upload {\r\n        margin-right: 12rpx;\r\n      }\r\n\r\n      .icon {\r\n        width: 60rpx;\r\n        height: 60rpx;\r\n        display: block;\r\n      }\r\n    }\r\n\r\n    .vicoe-box {\r\n      display: flex;\r\n      width: fit-content;\r\n\r\n      .icon {\r\n        width: 60rpx;\r\n        height: 60rpx;\r\n        display: block;\r\n      }\r\n\r\n    }\r\n  }\r\n\r\n\r\n}\r\n\r\n.clear-msg {\r\n  color: #66648a;\r\n  font-size: 30rpx;\r\n  z-index: 99;\r\n  text-align: right;\r\n  margin-bottom: 6px;\r\n  position: absolute;\r\n  width: 100%;\r\n  right: 0px;\r\n  bottom: 140rpx;\r\n  z-index: 8;\r\n\r\n  .txt {\r\n    margin-right: 20px;\r\n  }\r\n}\r\n\r\n// 三个闪烁的加载小圆点\r\n.loading {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-top: 10rpx;\r\n\r\n  .loading-dot {\r\n    width: 8rpx;\r\n    height: 8rpx;\r\n    border-radius: 50%;\r\n    background-color: #999;\r\n    margin-right: 6rpx;\r\n    animation: loading-blink 1.4s infinite both;\r\n\r\n    &:nth-child(1) {\r\n      animation-delay: 0s;\r\n    }\r\n\r\n    &:nth-child(2) {\r\n      animation-delay: 0.2s;\r\n    }\r\n\r\n    &:nth-child(3) {\r\n      animation-delay: 0.4s;\r\n      margin-right: 0;\r\n    }\r\n  }\r\n}\r\n\r\n@keyframes loading-blink {\r\n\r\n  0%,\r\n  80%,\r\n  100% {\r\n    opacity: 0.3;\r\n    transform: scale(0.8);\r\n  }\r\n\r\n  40% {\r\n    opacity: 1;\r\n    transform: scale(1);\r\n  }\r\n}\r\n</style>", "import MiniProgramPage from 'E:/youngProject/agent-mini-ui/pages/msg/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "useUserStore", "reactive", "subscribeAgentApi", "uni", "res", "deleteAllMessagesApi", "cancelCollectMessageApi", "collectMessageApi", "agentDetailApi", "getMessageHistoryApi", "throttle", "TextDecoder", "base", "wx", "saveMsgApi", "nextTick", "createPurchaseOrderApi", "miniPay", "queryPurchaseOrderApi", "onLoad"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAqNA,MAAM,iBAAiB,MAAW;;;;AAIlC,UAAM,SAASA,cAAG,IAAC,IAAI;AACvB,UAAM,YAAYA,cAAAA,IAAI,IAAI;AAC1B,UAAM,YAAYC,YAAY,aAAA;AAC9B,UAAM,YAAYD,cAAAA,IAAI,EAAE;AACxB,UAAM,cAAcA,cAAAA,IAAI,EAAE;AAC1B,UAAM,cAAcA,cAAG,IAAC,IAAI;AAC5B,UAAM,kBAAkBA,cAAG,IAAC,EAAE;AAC9B,UAAM,WAAWA,cAAAA,IAAI,CAAA,CAAE;AACvB,UAAM,YAAYA,cAAG,IAAC,KAAK;AAE3B,UAAM,QAAQA,cAAG,IAAC,KAAK;AAIvB,UAAM,SAAS,cAAc,UAAU;AAEvC,UAAM,UAAU,OAAO,4BAA6B;AACpD,UAAM,qBAAqBA,cAAG,IAAC,KAAK;AAEpC,UAAM,cAAcE,cAAAA,SAAS;AAAA,MAC3B,WAAW;AAAA,MACX,aAAa;AAAA,MACb,WAAW;AAAA,MACX,OAAO;AAAA,IACT,CAAC;AACD,UAAM,kBAAkBF,cAAG,IAAC,EAAE;AAC9B,UAAM,qBAAqBA,cAAG,IAAC,KAAK;AACpC,UAAM,iBAAiBA,cAAG,IAAC,CAAC;AAE5B,UAAM,iBAAiB,YAAY;AACjC,UAAI;AACF,cAAM,MAAM,MAAMG,4BAAkB;AAAA,UAClC,cAAc,UAAU;AAAA,UACxB,WAAW,UAAU;AAAA,QAC3B,CAAK;AACD,oBAAY,QAAQ,IAAI,KAAK;AAAA,MAC9B,SAAQ,OAAO;AACdC,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,MAAM,OAAO;AAAA,UACpB,MAAM;AAAA,QACZ,CAAK;AAAA,MACF;AAAA,IAEH;AAEA,UAAM,iBAAiB,YAAY;AACjCA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,OAAO,QAAQ;AACtB,cAAI,IAAI,SAAS;AACf,gBAAIC,OAAM,MAAMC,+BAAqB;AAAA,cACnC,cAAc,UAAU;AAAA,cACxB,aAAa,YAAY;AAAA,YACnC,CAAS;AACD,gBAAID,KAAI,SAAS,GAAG;AAClBD,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,cAClB,CAAW;AAED,4BAAc,YAAY,KAAK;AAC/B,wBAAU,QAAQ;AAClB,wBAAU,QAAQ;AAClB,0BAAY,QAAQ;AACpB,oBAAM,OAAO,MAAM,OAAQ;AAC3B,2BAAa,YAAY,SAAS;AAClC,0BAAY,QAAQ;AAAA,YAC9B,OAAe;AACLA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAOC,KAAI;AAAA,gBACX,MAAM;AAAA,cAClB,CAAW;AAAA,YACF;AAAA,UAEF;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AACA,UAAM,gBAAgB,OAAO,SAAS;AACpC,UAAI,OAAO,KAAK;AAChB,UAAI,QAAQ;AACZ,UAAI;AACF,YAAI,KAAK,aAAa;AACpB,gBAAME,kCAAwB;AAAA,YAC5B,cAAc,UAAU;AAAA,YACxB,aAAa;AAAA,UACrB,CAAO;AACD,kBAAQ;AACR,eAAK,cAAc;AAAA,QACzB,OAAW;AACL,gBAAMC,4BAAkB;AAAA,YACtB,cAAc,UAAU;AAAA,YACxB,aAAa;AAAA,UACrB,CAAO;AACD,eAAK,cAAc;AAAA,QACpB;AACDJ,sBAAAA,MAAI,UAAU;AAAA,UACZ;AAAA,UACA,MAAM;AAAA,QACZ,CAAK;AAAA,MACF,SAAQ,OAAO;AACdA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,MAAM;AAAA,UACb,MAAM;AAAA,QACZ,CAAK;AAAA,MACF;AAAA,IAEH;AACA,UAAM,aAAa,CAAC,YAAY;AAC9BA,oBAAAA,MAAI,iBAAiB;AAAA,QACnB,MAAM;AAAA,QACN,UAAU;AACRA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AACA,UAAM,SAAS,MAAM;AACnBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAG;AAAA,IACH;AAgCA,UAAM,iBAAiB,YAAY;AACjC,UAAI,MAAM,MAAMK,yBAAe;AAAA,QAC7B,cAAc,UAAU;AAAA,QACxB,WAAW,UAAU;AAAA,MACzB,CAAG;AACD,kBAAY,YAAY,IAAI,KAAK;AACjC,kBAAY,cAAc,IAAI,KAAK;AACnC,sBAAgB,QAAQ,IAAI,KAAK;AACjC,kBAAY,YAAY,IAAI,KAAK;AACjC,kBAAY,QAAQ,IAAI,KAAK;AAAA,IAC/B;AACA,QAAI,oBAAoBT,cAAG,IAAC,IAAI;AAChC,UAAM,YAAY,OAAO,MAAM,aAAa;AAE1C,UAAI,MAAM;AAAA,QACR,cAAc,UAAU;AAAA,QACxB,aAAa,YAAY;AAAA,QACzB,SAAS;AAAA;AAAA,QACT;AAAA;AAAA,QACA,OAAO;AAAA,MACX;AACE,UAAI;AAEF,YAAI,MAAM,MAAMU,+BAAqB,GAAG;AACxC,0BAAkB,QAAQ;AAC1BN,sBAAAA,MAAI,sBAAsB;AAAA,UACxB,OAAO,IAAI,KAAK,YAAY,UAAU;AAAA,QAC5C,CAAK;AACD,YAAI,MAAM,CAAA;AACV,YAAI,IAAI,KAAK,KAAK,SAAS,GAAG;AAC5B,oBAAU,QAAQ,IAAI,KAAK,KAAK,CAAC,EAAE;AACnC,cAAI,KAAK,KAAK,QAAQ,CAAC,SAAS;AAC9B,gBAAI,KAAK,aAAa,aAAa;AACjC,kBAAI,OAAO,KAAK;AAChB,kBAAI,KAAK,gBAAgB,OAAO;AAC9B,uBAAO;AAAA,cACR;AACD,kBAAI,KAAK,gBAAgB,OAAO;AAC9B,uBAAO;AAAA,cACR;AACD,kBAAI,KAAK,cAAc,MAAM;AAC3B,qBAAK,YAAY;cAClB;AACD,kBAAI,KAAK;AAAA,gBACP,MAAM;AAAA,gBACN,SAAS,KAAK;AAAA,gBACd,eAAe;AAAA,gBACf,UAAU;AAAA,gBACV;AAAA,gBACA,MAAM,KAAK;AAAA,gBACX,cAAc;AAAA,gBACd,OAAO,KAAK;AAAA,gBACZ,aAAa,KAAK;AAAA,gBAClB,WAAW,KAAK;AAAA,cAC5B,CAAW;AAAA,YACX,WAAmB,KAAK,aAAa,QAAQ;AACnC,kBAAI,KAAK;AAAA,gBACP,MAAM;AAAA,gBACN,SAAS,KAAK;AAAA,gBACd,OAAO,KAAK;AAAA,cACxB,CAAW;AAAA,YACF;AAAA,UACT,CAAO;AAAA,QACF;AAED,YAAI,IAAI,SAAS,KAAK,IAAI,IAAI,SAAS,CAAC,EAAE,SAAS,aAAa;AAC9D,cAAI,IAAI,SAAS,CAAC,EAAE,UAAU;AAAA,QACpC,OAAW;AACL,cAAI,YAAY,OAAO;AACrBA,0BAAAA,MAAA,MAAA,OAAA,8BAAY,QAAQ;AACpB,gBAAI,KAAK;AAAA,cACP,MAAM;AAAA,cACN,SAAS,YAAY;AAAA,cACrB,eAAe;AAAA,cACf,WAAW;AAAA,cACX,MAAM;AAAA,cACN,cAAc;AAAA,cACd,SAAS;AAAA,cACT,OAAO;AAAA,cACP,MAAM;AAAA,cACN,WAAW,CAAE;AAAA,YACvB,CAAS;AACD,wBAAY,QAAQ;AAAA,UACrB;AAAA,QACF;AACD,cAAM,OAAO,MAAM,SAAS,GAAG;AAAA,MAChC,SAAQ,OAAO;AACd,0BAAkB,QAAQ;AAC1B,eAAO,MAAM,SAAS,KAAK;AAAA,MAC5B;AAAA,IACH;AACA,UAAM,eAAeO,YAAAA,SAAS,CAAC,SAAS;AAEtC,iBAAW,QAAQ;AACnB,aAAQ;AAAA,IACV,GAAG,GAAI;AAIP,UAAM,YAAY,CAAC,QAAQ,OAAO,QAAQ,UAAU,UAAU;AAE5D,UAAI,SAAS;AAAA,QACX,SAAS;AAAA,QACT,OAAO;AAAA,QACP,WAAW,CAAE;AAAA;AAAA,MAEd;AACD,UAAI,QAAQ;AAAA,QACV,MAAM;AAAA,QACN,SAAS;AAAA,QACT,eAAe;AAAA,QACf,WAAW;AAAA,QACX;AAAA,QACA,cAAc,OAAO;AAAA,QACrB;AAAA,QACA,aAAa;AAAA,QACb,OAAO;AAAA,QACP,MAAM,MAAM,KAAK,IAAK,CAAA,IAAI,KAAK,OAAM,CAAE;AAAA;AAAA,QACvC,WAAW,OAAO;AAAA,MACtB;AACE,aAAO,MAAM,kBAAkB,KAAK;AAAA,IACtC;AAEA,UAAM,eAAe,CAAC,WAAW;AAC/B,UAAI,QAAQ;AAAA,QACV,MAAM;AAAA,QACN,SAAS;AAAA,QACT,eAAe;AAAA,QACf,WAAW;AAAA,QACX,MAAM;AAAA,QACN,cAAc;AAAA,QACd,SAAS;AAAA,QACT,aAAa;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,QACN,WAAW,CAAE;AAAA,MACjB;AACE,aAAO,MAAM,kBAAkB,KAAK;AAAA,IACtC;AAEA,UAAM,cAAc,CAAC,YAAY;AAC/B,UAAI,QAAQ;AAAA,QACV,MAAM;AAAA,QACN;AAAA,QACA,OAAO;AAAA,QACP,WAAW;AAAA,QACX,MAAM,QAAQ,KAAK,IAAK,CAAA,IAAI,KAAK,OAAM,CAAE;AAAA;AAAA,MAC7C;AACE,aAAO,MAAM,kBAAkB,KAAK;AAAA,IACtC;AA8BA,UAAM,aAAa,CAAC,UAAU;AAC5BP,oBAAAA,MAAI,sBAAsB;AAAA,QACxB,OAAO;AAAA,QACP,QAAQ,KAAK;AAEXA,wBAAAA,MAAY,MAAA,OAAA,8BAAA,KAAK,SAAS;AAAA,QAC3B;AAAA,QACD,KAAK,KAAK;AACRA,wBAAAA,MAAA,MAAA,OAAA,8BAAY,KAAK,MAAM;AAAA,QACxB;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,aAAaJ,cAAAA,IAAI,EAAE;AAEzB,UAAM,YAAYE,cAAAA,SAAS;AAAA,MACzB,gBAAgB;AAAA,MAChB,eAAe;AAAA,MACf,OAAO;AAAA,MACP,kBAAkB;AAAA,IACpB,CAAC;AACD,QAAI,aAAaF,cAAG,IAAC,IAAI;AACzB,UAAM,aAAa,MAAM;AACvB,iBAAW,QAAQ;AAAA,IACrB;AACA,UAAM,YAAY,MAAM;AACtB,iBAAW,QAAQ;AAAA,IACrB;AACA,UAAM,qBAAqB,CAAC,QAAQ;AAElC,UAAI,MAAM,CAAE;AACZ,UAAI,KAAI,EAAG,MAAM,IAAI,EAAE,QAAQ,CAAC,SAAS;AACvC,YAAI,KAAK,SAAS,KAAK,MAAM;AAC3B,gBAAM,CAAC,KAAK,KAAK,IAAI,KAAK,MAAM,IAAI;AACpC,cAAI,SAAS,MAAM,QAAQ;AACzB,gBAAI,KAAK;AAAA,cACP,CAAC,GAAG,GAAG;AAAA,YACjB,CAAS;AAAA,UACF;AAAA,QACF;AAAA,MACL,CAAG;AACD,aAAO;AAAA,IACT;AACA,UAAM,eAAe,CAAC,iBAAiB;AAIrC,YAAM,QAAQI,cAAAA,MAAI,oBAAoB,YAAY;AAClD,YAAM,aAAa,IAAIQ,cAAAA,gBAAAA,YAAa,EAAC,OAAO,KAAK;AACjD,aAAO;AAAA,IACT;AACA,UAAM,cAAcZ,cAAG,IAAC,EAAE;AAC1B,UAAM,WAAWA,cAAG,IAAC,EAAE;AACvB,UAAM,gBAAgB,YAAY;AAChC,UAAI,UAAU;AACd,UAAI,YAAY;AAChB,UAAI,YAAY;AAGhB,UAAI,YAAY,OAAO;AACrB,sBAAc,YAAY,KAAK;AAC/B,oBAAY,QAAQ;AAAA,MACrB;AAED,UAAI,MAAM,GAAGa,cAAI,KAAC,OAAO,6CAA6C,UAAU,KAAK,kBAAkB,YAAY,KAAK,iBAAiB,UAAU,YAAY,oBAAoB,gBAAgB,KAAK;AACxM,YAAM,cAAcC,cAAE,KAAC,QAAQ;AAAA,QAC7B;AAAA,QACA,QAAQ;AAAA,QACR,eAAe;AAAA,MACnB,CAAG;AAED,kBAAY,gBAAgB,CAAC,QAAQ;AACnC,YAAI,WAAW;AACb,sBAAY;AAGZ,cAAI,OAAO;AACX,cAAI,QAAQ;AACZ,sBAAY,QAAQ,YAAY,YAAY;AAC1C,sBAAU,QAAQ,QAAQ,QAAQ,IAAI;AACtC,gBAAI,eAAe;AACnB,gBAAI,KAAK,SAAS,QAAQ,QAAQ;AAEhC,sBAAQ,QAAQ,KAAK;AACrB,sBAAQ,QAAQ;AAAA,YAC1B,OAAe;AACL,kBAAI,WAAW;AAEb,0BAAU,QAAQ;AAClB,8BAAc,YAAY,KAAK;AAC/B,4BAAY,QAAQ;AACpB,+BAAe,UAAU;AAEzB,+BAAe,YAAY,UAAU;AACrC,+BAAe,cAAc;AAC7B,+BAAe,cAAc,YAAY;AACzC,yBAAS,MAAM,UAAU,aAAa,EAAE,YAAY;AACpD,oBAAI;AACF,sBAAI,YAAY,MAAMC,qBAAW,cAAc;AAC/C,sBAAI,UAAU,SAAS,GAAG;AACxB,8BAAU,iBAAiB;AAC3B,6BAAS,MAAM,UAAU,aAAa,EAAE,UAAU;AAClD;AAAA,kBACD;AACD,4BAAU,QAAQ,UAAU,KAAK;AACjC,4BAAU,iBAAiB;AAC3B,2BAAS,MAAM,UAAU,aAAa,EAAE,OAAO,UAAU,KAAK;AAC9D,2BAAS,MAAM,UAAU,aAAa,EAAE,gBAAgB;AACxDC,gCAAAA,WAAS,MAAM;AACb,2BAAO,MAAM;kBAC7B,CAAe;AAAA,gBACF,SAAQ,OAAO;AACd,sBAAI,MAAM,SAAS,QAAQ;AACzB,0BAAM,QAAQ;AACd,8BAAU,QAAQ;AAClB;AAAA,kBACD;AAAA,gBACF;AAAA,cAEF;AAAA,YACF;AAED,gBAAI,SAAS,cAAc;AACzB,uBAAS,MAAM,UAAU,aAAa,EAAE,UAAU;AAAA,YACnD;AAAA,UACT,GAAS,SAAS,KAAK;AAAA,QAClB;AACD,YAAI,cAAc,IAAI;AACtB,YAAI,aAAa,IAAI,WAAW,WAAW;AAC3C,YAAI,OAAOZ,cAAAA,MAAI,oBAAoB,UAAU;AAC7C,eAAO,aAAa,IAAI;AACxB,YAAI,UAAU,mBAAmB,IAAI;AAErC,gBAAQ,IAAI,CAAC,MAAM;AACjB,cAAI,EAAE,eAAe,MAAM,KAAK,EAAE,MAAM;AAEtC,gBAAI,EAAE,QAAQ,UAAU;AACtB,8BAAgB,QAAQ,QAAQ,QAAQ,SAAS,CAAC,EAAE;AACpD,0BAAY;AACZ,0BAAY,MAAO;AACnB;AAAA,YACD;AACD,gBAAI,YAAY,IAAI;AAClB,wBAAU,EAAE,KAAK,QAAQ,QAAQ,EAAE;AAAA,YAC7C,OAAe;AACL,yBAAW,EAAE;AAAA,YACd;AAAA,UACF;AAAA,QACP,CAAK;AAAA,MAEL,CAAG;AAAA,IACH;AACA,QAAI,mBAAmBF,cAAAA,SAAS;AAAA,MAC9B,cAAc,UAAU;AAAA,MACxB,aAAa;AAAA,MACb,MAAM;AAAA,MACN,SAAS;AAAA,MACT,WAAW;AAAA,MACX,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB,CAAC;AACD,QAAI,iBAAiBA,cAAAA,SAAS;AAAA,MAC5B,cAAc,UAAU;AAAA,MACxB,MAAM;AAAA,MACN,aAAa;AAAA,MACb,SAAS;AAAA,MACT,WAAW;AAAA,MACX,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,eAAe;AAAA,MACf,MAAM,CAAE;AAAA,IACV,CAAC;AACD,UAAM,cAAc,MAAM;AACxB,aAAQ;AAAA,IACV;AAEA,QAAI,cAAcF,cAAG,IAAC,KAAK;AAC3B,UAAM,SAAS,YAAY;AACzB,UAAI,WAAW,MAAM,KAAI,EAAG,WAAW,GAAG;AACxCc,sBAAAA,KAAG,UAAU;AAAA,UACX,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QAChB,CAAK;AACD;AAAA,MACD;AACD,UAAI,UAAU,OAAO;AACnBA,sBAAAA,KAAG,UAAU;AAAA,UACX,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QAChB,CAAK;AACD;AAAA,MACD;AACD,gBAAU,QAAQ;AAGlB,UAAI,YAAY,OAAO;AACrB,sBAAc,YAAY,KAAK;AAC/B,oBAAY,QAAQ;AAAA,MACrB;AAGD,kBAAY,WAAW,KAAK;AAI5B,gBAAU,GAAG;AACb,gBAAU,gBAAgB;AAC1B,uBAAiB,UAAU,WAAW;AACtC,uBAAiB,YAAY,UAAU;AACvC,uBAAiB,cAAc,YAAY;AAC3C,UAAI;AACF,YAAI,YAAY,MAAMC,qBAAW,gBAAgB;AACjD,oBAAY,QAAQ;AACpBX,sBAAAA,MAAY,MAAA,OAAA,8BAAA,WAAW,qBAAqB;AAC5C,YAAI,UAAU,SAAS,GAAG;AAExB,mBAAS,MAAM,UAAU,aAAa,EAAE,UAAU;AAClD,oBAAU,QAAQ;AAClB;AAAA,QACD;AACD,kBAAU,QAAQ,UAAU,KAAK;AACjC,iBAAS,MAAM,UAAU,gBAAgB,CAAC,EAAE,QAAQ,UAAU,KAAK;AACnE,mBAAW,QAAQ;AACnB,sBAAe;AAAA,MAChB,SAAQ,OAAO;AACdA,sBAAA,MAAA,MAAA,OAAA,8BAAY,OAAO,gBAAgB;AACnC,kBAAU,QAAQ;AAClB,iBAAS,MAAM,UAAU,aAAa,EAAE,YAAY;AACpD,YAAI,MAAM,SAAS,QAAQ;AACzB,mBAAS,MAAM,UAAU,aAAa,EAAE,UAAU;AAClD,gBAAM,QAAQ;AACd;AAAA,QACN,OAAW;AACL,mBAAS,MAAM,UAAU,aAAa,EAAE,UAAU,GAAG,MAAM,GAAG,IAAI,MAAM,IAAI;AAAA,QAC7E;AAAA,MACF;AAAA,IACH;AAGA,UAAM,YAAY,CAAC,SAAS;AAC1BA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,QACL,SAAS,CAAC,QAAQ;AAChB,cAAI,aAAa,KAAK,YAAY,mBAAmB,IAAI,CAAC;AAAA,QAC3D;AAAA,QACD,KAAK,KAAK;AACRA,wBAAAA,MAAY,MAAA,OAAA,8BAAA,GAAK;AAAA,QAClB;AAAA,MACL,CAAG;AAAA,IACH;AAcA,UAAM,eAAe,CAAC,QAAQ;AAC5B,gBAAU,MAAM;IAClB;AAGA,UAAM,YAAY,CAAC,MAAM,QAAQ,UAAU;AACzC,UAAI,MAAM,CAAA;AACV,UAAI,QAAQ;AACV,cAAM;AAAA,MACV,OAAS;AACL,YAAI,CAAC,IAAI;AAAA,MACV;AACDA,oBAAAA,MAAI,aAAa;AAAA,QACf,SAAS;AAAA,QACT,MAAM;AAAA,QACN,kBAAkB;AAAA,UAChB,UAAU,CAAC,SAAS,QAAQ,IAAI;AAAA,UAChC,SAAS,SAAU,MAAM;AAAA,UAAG;AAAA,UAC5B,MAAM,SAAU,KAAK;AACnBA,2EAAY,IAAI,MAAM;AAAA,UACvB;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAIA,UAAM,eAAeF,cAAAA,SAAS;AAAA,MAC5B,cAAc;AAAA;AAAA,MACd,gBAAgB;AAAA;AAAA,MAChB,WAAW;AAAA,MACX,YAAY;AAAA;AAAA,MAEZ,UAAU;AAAA;AAAA,MACV,aAAa;AAAA;AAAA;AAAA;AAAA,MAGb,UAAU;AAAA;AAAA,MACV,YAAY;AAAA;AAAA,MACZ,mBAAmB;AAAA;AAAA,MACnB,WAAW;AAAA;AAAA,IACb,CAAC;AAKD,UAAM,YAAY,CAAC,MAAM;AACvB,UAAI,CAAC,mBAAmB,OAAO;AAC7BY,sBAAAA,KAAG,UAAU;AAAA,UACX,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QAChB,CAAK;AACD;AAAA,MACD;AACD,UAAI,aAAa,mBAAmB;AAClCA,sBAAAA,KAAG,UAAU;AAAA,UACX,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QAChB,CAAK;AACD;AAAA,MACD;AACD,UAAI,UAAU,gBAAgB;AAC5BA,sBAAAA,KAAG,UAAU;AAAA,UACX,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QAChB,CAAK;AACD;AAAA,MACD;AAED,mBAAa,aAAa,EAAE,QAAQ,CAAC;AACrC,mBAAa,eAAe;AAC5B,mBAAa,YAAY;AACzB,mBAAa,YAAY;AAEzB,cAAQ,MAAM;AAAA,QACZ,UAAU;AAAA,QACV,MAAM;AAAA,MACV,CAAG;AACD,mBAAa,WAAW;AAAA,IAC1B;AAEA,UAAM,aAAa,MAAM;AACvB,UAAI,aAAa,aAAa;AAC5B,mBAAY;AACZ;AAAA,MACD;AACD,UAAI,aAAa,mBAAmB;AAClC;AAAA,MACD;AACD,UAAI,UAAU,gBAAgB;AAC5B;AAAA,MACD;AACD,iBAAY;AACZ,UAAI,CAAC,aAAa,WAAW;AAC3B;AAAA,MACD;AAED,cAAQ,KAAM;AACd,mBAAa,oBAAoB;AACjC,UAAI,CAAC,aAAa,UAAU;AAC1BV,sBAAAA,MAAI,YAAY;AAAA,UACd,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACF;AAAA,IACH;AAGA,UAAM,cAAc,CAAC,MAAM;AACzB,UAAI,CAAC,KAAK,CAAC,EAAE,WAAW,EAAE,QAAQ,WAAW,KAAK,CAAC,aAAa;AAAY;AAE5E,UAAI,aAAa,EAAE,QAAQ,EAAE,QAAQ,SAAS,CAAC,EAAE,UAAU,aAAa,WAAW;AACnF,UAAI,KAAK,IAAI,UAAU,IAAI,IAAI;AAC7B,qBAAa,aAAa;AAC1B,qBAAa,YAAY;AACzB,qBAAa,WAAW;AACxB,qBAAa,cAAc;AAAA,MAC/B,OAAS;AACL,qBAAa,aAAa;AAC1B,qBAAa,YAAY;AACzB,qBAAa,WAAW;AACxB,qBAAa,cAAc;AAAA,MAC5B;AAAA,IACH;AAEA,UAAM,aAAa,MAAM;AAIvB,mBAAa,eAAe;AAC5B,mBAAa,YAAY;AACzB,mBAAa,aAAa;AAC1B,mBAAa,cAAc;AAAA,IAC7B;AAEA,UAAM,aAAa,MAAM;AAEvB,cAAQ,cAAc,CAAC,QAAQ;AAC7B,YAAI,aAAa,UAAU;AACzB;AAAA,QACN,OAAW;AACL,qBAAW,QAAQ,IAAI;AACvB,sBAAa;AAAA,QACd;AAAA,MACF;AAGD,cAAQ,SAAS,CAAC,QAAQ;AACxBA,sBAAAA,MAAY,MAAA,OAAA,8BAAA,gBAAgB,GAAG;AAC/B,qBAAa,oBAAoB;AACjC,qBAAa,YAAY;AACzB,YAAI,aAAa,UAAU;AACzB;AAAA,QACD;AACDA,sBAAG,MAAC,YAAW;AACf,YAAI,OAAO,IAAI;AACf,YAAI,QAAQ,IAAI;AAMd;AAAA,QACD;AACD,mBAAW,QAAQ;AACnB,oBAAa;AAAA,MACd;AAGD,cAAQ,UAAU,CAAC,QAAQ;AACzBA,sBAAG,MAAC,YAAW;AACf,qBAAa,oBAAoB;AACjC,qBAAa,YAAY;AACzB,YAAI,OAAO,IAAI;AACf,gBAAQ,MAAI;AAAA,UACV,KAAK;AACHU,0BAAAA,KAAG,UAAU;AAAA,cACX,OAAO;AAAA,cACP,MAAM;AAAA,cACN,UAAU;AAAA,YACpB,CAAS;AACD;AAAA,UACF;AACEA,0BAAAA,KAAG,UAAU;AAAA,cACX,OAAO;AAAA,cACP,MAAM;AAAA,cACN,UAAU;AAAA,YACpB,CAAS;AACD;AAAA,QACH;AAAA,MACF;AAAA,IACH;AAIA,UAAM,eAAe,MAAM;AACzBA,oBAAAA,KAAG,WAAW;AAAA,QACZ,QAAQ,KAAK;AACX,cAAI,CAAC,IAAI,YAAY,cAAc,GAAG;AACpCA,0BAAAA,KAAG,UAAU;AAAA,cACX,OAAO;AAAA,cACP,UAAU;AACR,mCAAmB,QAAQ;AAAA,cAC5B;AAAA,cACD,OAAO;AACL,mCAAmB,QAAQ;AAAA,cAC5B;AAAA,YACX,CAAS;AAAA,UACT,OAAa;AACL,+BAAmB,QAAQ;AAAA,UAC5B;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAEA,UAAM,qBAAqB,MAAM;AAC/B,yBAAmB,QAAQ;AAAA,IAC7B;AAGA,UAAM,sBAAsB,MAAM;AAChC,yBAAmB,QAAQ;AAAA,IAC7B;AACA,UAAM,yBAAyB,YAAY;AACzC,UAAI,UAAU,MAAMG,iCAAuB;AAAA,QACzC,cAAc,UAAU;AAAA,QACxB,WAAW,UAAU;AAAA,QACrB,QAAQ;AAAA,MACZ,CAAG;AACDC,iBAAAA,QAAQ,QAAQ,KAAK,OAAO,EAAE;AAAA,QAC5B,OAAM,QAAO;AACX,6BAAmB,QAAQ,KAAK,SAAS,cAAc;AAAA,QACxD;AAAA,QACD,SAAO;AACLd,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO,IAAI;AAAA,UACnB,CAAO;AAAA,QACF;AAAA,MACL;AAAA,IACA;AACA,UAAM,qBAAqB,OAAO,SAAS,WAAW;AACpD;AACA,UAAI;AACF,YAAI,YAAY,MAAMe,gCAAsB;AAAA,UAC1C;AAAA,QACN,CAAK;AACD,YAAI,UAAU,KAAK,QAAQ;AACzBf,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,UACf,CAAO;AACD,gBAAM,QAAQ;AACd,6BAAmB,QAAQ;AAAA,QACjC,OAAW;AACL,cAAI,SAAS,IAAI;AACfA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,YACjB,CAAS;AAAA,UACT,OAAa;AACL,+BAAmB,SAAS,MAAM;AAAA,UACnC;AAAA,QACF;AAAA,MACF,SAAQ,GAAG;AACVA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,EAAE,MAAM,EAAE,MAAM;AAAA,QAC7B,CAAK;AAAA,MACF;AAAA,IACH;AACAgB,kBAAM,OAAC,OAAO,WAAW;AAEvB;AACA;AAEA,UAAI,OAAO,aAAa;AACtB,kBAAU,QAAQ,OAAO;AACzB,cAAM,eAAgB;AACtB,cAAM,eAAgB;AAAA,MACvB;AACD,UAAI,UAAU,WAAW;AACvBJ,sBAAAA,WAAS,MAAM;AACb,cAAI,OAAO,OAAO;AAChB,mBAAO,MAAM,OAAQ;AAAA,UACtB;AAAA,QACP,CAAK;AAAA,MACF;AAAA,IACH,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACllCD,GAAG,WAAW,eAAe;"}