"use strict";const e=require("../../common/vendor.js"),a=require("../../api/index.js"),n=require("../../stores/user.js"),i=require("../../api/common.js"),t={__name:"vip-list",setup(t){const o=n.useUserStore(),r=e.computed((()=>o.userInfo)),s=e.ref([]),c=e.ref(0),u=e.ref(0),d=e.ref(""),l=async()=>{try{const e=await a.getSubscriptionRuleApi({merchantGuid:o.merchantGuid});0===e.code&&(d.value=e.data.member_card_notice)}catch(e){console.error("获取规则失败:",e)}},m=e.ref(!1),h=e.ref(""),p=e.ref(!1),g=async()=>{let e=await a.showBannerUrlsApi({merchantGuid:o.merchantGuid});h.value=e.data.buy_banner_img},v=()=>{p.value=!1},f=async()=>{if(!o.userToken)return void e.index.showToast({title:"请先登录",icon:"none"});if(m.value)return void(m.value&&(p.value=!0));const n=s.value[c.value];try{e.index.showLoading({title:"正在创建订单...",mask:!0});const t=await a.createVipOrderApi({merchantGuid:o.merchantGuid,packageGuid:n.guid,payEnv:"xcx"});e.index.hideLoading(),i.miniPay(t.data.payInfo).then((async()=>{y(t.data.orderNo,u.value)}),(a=>{e.index.showToast({title:a.msg||"支付失败",icon:"none"})}))}catch(t){e.index.hideLoading(),console.error("创建订单失败:",t),e.index.showToast({title:t.message||"创建订单失败",icon:"none"})}},y=async(n,i)=>{i++;try{(await a.queryVipOrderApi({orderNo:n})).data.isPaid?(e.index.showToast({title:"支付成功",icon:"success"}),setTimeout((()=>{e.index.navigateBack()}),1500)):i>12?e.index.showToast({title:"支付超时",icon:"none"}):setTimeout((()=>{y(n,i)}),2e3)}catch(t){e.index.showToast({title:t.msg||"查询支付状态失败",icon:"none"})}};let x=e.reactive({hasMembership:!1,isExpired:!0,membership:null,remainingDays:0});const w=e.ref(!1);return e.onLoad((()=>{"ios"===e.index.getSystemInfoSync().osName&&(w.value=!0),l(),g()})),e.onShow((()=>{o.userToken&&((async()=>{try{let e=await a.getVipPackageListApi({merchantGuid:o.merchantGuid});s.value=e.data.packages}catch(n){console.error("获取会员套餐列表失败:",n),e.index.showToast({title:"加载失败",icon:"none"})}})(),(async()=>{try{let e=await a.getUserVipInfoApi({merchantGuid:o.merchantGuid});x=Object.assign(x,e.data)}catch(n){console.error("获取会员信息失败:",n),e.index.showToast({title:"加载失败",icon:"none"})}})(),l(),g())})),(a,n)=>e.e({a:r.value.headImgUrl||"https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/c4d5b3ada53740d5b862f274ce48ef0c.png",b:e.t(r.value.nickname||"用户Aric"),c:!e.unref(x).hasMembership},e.unref(x).hasMembership?{d:e.t(e.unref(x).remainingDays)}:{},{e:!e.unref(x).hasMembership},(e.unref(x).hasMembership,{}),{f:e.t(d.value),g:e.f(s.value,((a,n,i)=>({a:e.t(a.packageName),b:e.t(a.salePriceYuan),c:e.t(a.durationDays),d:e.t(a.packageDesc),e:n,f:e.n({selected:c.value===n}),g:e.o((e=>(e=>{c.value=e,3===s.value[c.value].packageType?m.value=!0:m.value=!1})(n)),n)}))),h:e.o(f),i:p.value},p.value?e.e({j:h.value},h.value?{k:h.value,l:e.o(((...e)=>a.onImageLoad&&a.onImageLoad(...e))),m:e.o(((...e)=>a.onImageError&&a.onImageError(...e)))}:{},{n:e.o(v)}):{})}},o=e._export_sfc(t,[["__scopeId","data-v-58cdf557"]]);wx.createPage(o);
