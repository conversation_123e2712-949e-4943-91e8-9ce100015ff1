"use strict";const e=require("../../../../../../common/vendor.js"),t=require("../../i18n/index.js"),r=require("../z-paging-interceptor.js"),{t:n}=e.initVueI18n(t.messages),i=e.index.getSystemInfoSync().language,a={data:()=>({language:i}),computed:{finalLanguage(){try{const t=e.index.getLocale(),n=this.language;return"auto"===t?r.interceptor._handleLanguage2Local(n,this._language2Local(n)):t}catch(t){return"zh-Hans"}},finalRefresherDefaultText(){return this._getI18nText("zp.refresher.default",this.refresherDefaultText)},finalRefresherPullingText(){return this._getI18nText("zp.refresher.pulling",this.refresherPullingText)},finalRefresherRefreshingText(){return this._getI18nText("zp.refresher.refreshing",this.refresherRefreshingText)},finalRefresherCompleteText(){return this._getI18nText("zp.refresher.complete",this.refresherCompleteText)},finalRefresherUpdateTimeTextMap:()=>({title:n("zp.refresherUpdateTime.title"),none:n("zp.refresherUpdateTime.none"),today:n("zp.refresherUpdateTime.today"),yesterday:n("zp.refresherUpdateTime.yesterday")}),finalRefresherGoF2Text(){return this._getI18nText("zp.refresher.f2",this.refresherGoF2Text)},finalLoadingMoreDefaultText(){return this._getI18nText("zp.loadingMore.default",this.loadingMoreDefaultText)},finalLoadingMoreLoadingText(){return this._getI18nText("zp.loadingMore.loading",this.loadingMoreLoadingText)},finalLoadingMoreNoMoreText(){return this._getI18nText("zp.loadingMore.noMore",this.loadingMoreNoMoreText)},finalLoadingMoreFailText(){return this._getI18nText("zp.loadingMore.fail",this.loadingMoreFailText)},finalEmptyViewText(){return this.isLoadFailed?this.finalEmptyViewErrorText:this._getI18nText("zp.emptyView.title",this.emptyViewText)},finalEmptyViewReloadText(){return this._getI18nText("zp.emptyView.reload",this.emptyViewReloadText)},finalEmptyViewErrorText(){return this.customerEmptyViewErrorText||this._getI18nText("zp.emptyView.error",this.emptyViewErrorText)},finalSystemLoadingText(){return this._getI18nText("zp.systemLoading.title",this.systemLoadingText)}},methods:{getLanguage(){return this.finalLanguage},_getI18nText(e,t){const r=Object.prototype.toString.call(t);if("[object Object]"===r){const e=t[this.finalLanguage];if(e)return e}else if("[object String]"===r)return t;return n(e)},_language2Local(e){const t=e.toLowerCase().replace(new RegExp("_",""),"-");return-1!==t.indexOf("zh")?"zh"===t||"zh-cn"===t||-1!==t.indexOf("zh-hans")?"zh-Hans":"zh-Hant":-1!==t.indexOf("en")?"en":e}}};exports.i18nModule=a;
