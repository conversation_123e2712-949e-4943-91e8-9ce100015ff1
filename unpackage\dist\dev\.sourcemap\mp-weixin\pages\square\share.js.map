{"version": 3, "file": "share.js", "sources": ["pages/square/share.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvc3F1YXJlL3NoYXJlLnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"share-container\">\r\n    <!-- 海报预览 -->\r\n    <view class=\"poster-preview\">\r\n      <image :src=\"path\" mode=\"widthFix\" class=\"poster-image\"></image>\r\n      <!-- <view v-else class=\"loading-text\">正在生成海报...</view> -->\r\n    </view>\r\n\r\n    <!-- 下载按钮 -->\r\n    <view class=\"download-btn\" @click=\"downloadPoster\">\r\n      <text class=\"download-text\">下载海报</text>\r\n    </view>\r\n\r\n    <!-- 海报生成器 -->\r\n    <!-- <l-painter ref=\"painterRef\" @fail=\"onPosterFail\" :board=\"poster\" width=\"600rpx\" height=\"835rpx\" hidden\r\n      class=\"painter-canvas\">https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/f567f2e6e0cb40c48b2510b888bd3b40.png\r\n    </l-painter> -->\r\n\r\n    <!-- <image :src=\"path\" mode=\"widthFix\"></image> -->\r\n    <l-painter isCanvasToTempFilePath @success=\"path = $event\" pathType=\"url\" hidden\r\n      css=\"width: 600rpx; height: 835rpx; background-image: url(https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/f567f2e6e0cb40c48b2510b888bd3b40.png); background-size: cover;\">\r\n      <template v-if=\"dataReady\">\r\n        <l-painter-image :src=\"agentDetail.agentAvatar\"\r\n          css=\"width: 200rpx; height: 200rpx; display: block; border-radius: 50%; margin:80rpx auto 10rpx auto\" />\r\n        <l-painter-text :text=\"agentDetail.agentName\"\r\n          css=\" width: 500rpx; text-align: center; font-size: 42rpx; font-weight: bold; color: #1a1a1a; margin: 45rpx auto 0 auto;\" />\r\n        <l-painter-text :text=\"agentDetail.agentDesc\"\r\n          css=\" width: 450rpx; text-align: center; font-size: 26rpx; color: #666666; line-height: 38rpx;margin: 20rpx auto 0 auto\" />\r\n        <l-painter-image :src=\"qrcode\"\r\n          css=\"width: 200rpx; height: 200rpx; display: block; margin: 60rpx auto 0 auto; border-radius: 50%;\" />\r\n      </template>\r\n    </l-painter>\r\n  </view>\r\n</template>\r\n<script setup>\r\nimport { ref, nextTick, reactive } from 'vue'\r\nimport { onLoad } from '@dcloudio/uni-app'\r\n\r\n// 页面数据\r\nconst path = ref('')\r\nconst dataReady = ref(false) // 控制数据是否准备完成\r\nlet agentDetail = reactive({\r\n  agentName: '',\r\n  agentDesc: '',\r\n  agentAvatar: ''\r\n})\r\nconst qrcode = ref('')\r\nconst painterRef = ref(null)\r\n\r\n\r\n// 接收页面参数\r\nonLoad(async (options) => {\r\n  console.log('接收到的参数:', options)\r\n  if (options && options.params) {\r\n    try {\r\n      const params = JSON.parse(decodeURIComponent(options.params))\r\n      console.log('params', params)\r\n      // 设置智能体数据\r\n      agentDetail = Object.assign(agentDetail, {\r\n        agentName: (params.agentName && params.agentName.trim()) || '智能体名称',\r\n        agentDesc: (params.agentDesc && params.agentDesc.trim()) || '智能体描述',\r\n        agentAvatar: (params.agentAvatar && params.agentAvatar.trim()) || ''\r\n      })\r\n\r\n      qrcode.value = (params.qrcode && params.qrcode.trim()) || ''\r\n\r\n      console.log('agentDetail', agentDetail)\r\n\r\n      // 等待下一个 tick 确保数据更新完成\r\n      await nextTick()\r\n\r\n      // 数据准备完成，可以开始渲染\r\n      dataReady.value = true\r\n\r\n    } catch (error) {\r\n      console.log('error', error)\r\n      // 即使出错也要设置默认数据并允许渲染\r\n      Object.assign(agentDetail, {\r\n        agentName: '智能体名称',\r\n        agentDesc: '智能体描述',\r\n        agentAvatar: ''\r\n      })\r\n      dataReady.value = true\r\n    }\r\n  } else {\r\n    console.log('没有参数，使用默认配置')\r\n    // 使用默认数据\r\n    Object.assign(agentDetail, {\r\n      agentName: '智能体名称',\r\n      agentDesc: '智能体描述',\r\n      agentAvatar: ''\r\n    })\r\n    dataReady.value = true\r\n  }\r\n})\r\n\r\n\r\n// 下载海报\r\nconst downloadPoster = () => {\r\n  console.log('开始下载海报')\r\n  // 保存到相册\r\n  uni.saveImageToPhotosAlbum({\r\n    filePath: path.value,\r\n    success: () => {\r\n      uni.showToast({\r\n        title: '保存成功',\r\n        icon: 'success'\r\n      })\r\n    },\r\n    fail: (error) => {\r\n      console.error('保存失败:', error)\r\n      uni.showToast({\r\n        title: '保存失败',\r\n        icon: 'none'\r\n      })\r\n    }\r\n  })\r\n\r\n}\r\n\r\n</script>\r\n\r\n<style scoped>\r\n.share-container {\r\n  min-height: 100vh;\r\n  background: #f8f9fa;\r\n  padding: 40rpx 30rpx;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n}\r\n\r\n.poster-preview {\r\n  width: 600rpx;\r\n  min-height: 835rpx;\r\n  background: #ffffff;\r\n  border-radius: 20rpx;\r\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);\r\n  margin-bottom: 60rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.poster-image {\r\n  width: 100%;\r\n  border-radius: 20rpx;\r\n}\r\n\r\n.loading-text {\r\n  font-size: 28rpx;\r\n  color: #999999;\r\n  text-align: center;\r\n}\r\n\r\n.download-btn {\r\n  width: 500rpx;\r\n  height: 88rpx;\r\n  background: linear-gradient(135deg, #3478f6 0%, #4a90e2 100%);\r\n  border-radius: 44rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  box-shadow: 0 8rpx 20rpx rgba(52, 120, 246, 0.3);\r\n}\r\n\r\n.download-btn:active {\r\n  transform: scale(0.98);\r\n  opacity: 0.9;\r\n}\r\n\r\n.download-text {\r\n  font-size: 32rpx;\r\n  color: #ffffff;\r\n  font-weight: 600;\r\n}\r\n\r\n.painter-canvas {\r\n  position: fixed;\r\n  top: -9999rpx;\r\n  left: -9999rpx;\r\n  width: 600rpx;\r\n  height: 835rpx;\r\n  z-index: -1;\r\n}\r\n</style>", "import MiniProgramPage from 'E:/youngProject/agent-mini-ui/pages/square/share.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "reactive", "onLoad", "uni", "nextTick"], "mappings": ";;;;;;;;;;;;;;;;;AAuCA,UAAM,OAAOA,cAAG,IAAC,EAAE;AACnB,UAAM,YAAYA,cAAG,IAAC,KAAK;AAC3B,QAAI,cAAcC,cAAAA,SAAS;AAAA,MACzB,WAAW;AAAA,MACX,WAAW;AAAA,MACX,aAAa;AAAA,IACf,CAAC;AACD,UAAM,SAASD,cAAG,IAAC,EAAE;AACFA,kBAAG,IAAC,IAAI;AAI3BE,kBAAM,OAAC,OAAO,YAAY;AACxBC,oBAAAA,mDAAY,WAAW,OAAO;AAC9B,UAAI,WAAW,QAAQ,QAAQ;AAC7B,YAAI;AACF,gBAAM,SAAS,KAAK,MAAM,mBAAmB,QAAQ,MAAM,CAAC;AAC5DA,wBAAAA,MAAY,MAAA,OAAA,gCAAA,UAAU,MAAM;AAE5B,wBAAc,OAAO,OAAO,aAAa;AAAA,YACvC,WAAY,OAAO,aAAa,OAAO,UAAU,KAAI,KAAO;AAAA,YAC5D,WAAY,OAAO,aAAa,OAAO,UAAU,KAAI,KAAO;AAAA,YAC5D,aAAc,OAAO,eAAe,OAAO,YAAY,KAAI,KAAO;AAAA,UAC1E,CAAO;AAED,iBAAO,QAAS,OAAO,UAAU,OAAO,OAAO,KAAI,KAAO;AAE1DA,wBAAAA,MAAY,MAAA,OAAA,gCAAA,eAAe,WAAW;AAGtC,gBAAMC,yBAAU;AAGhB,oBAAU,QAAQ;AAAA,QAEnB,SAAQ,OAAO;AACdD,wBAAAA,MAAY,MAAA,OAAA,gCAAA,SAAS,KAAK;AAE1B,iBAAO,OAAO,aAAa;AAAA,YACzB,WAAW;AAAA,YACX,WAAW;AAAA,YACX,aAAa;AAAA,UACrB,CAAO;AACD,oBAAU,QAAQ;AAAA,QACnB;AAAA,MACL,OAAS;AACLA,sBAAAA,mDAAY,aAAa;AAEzB,eAAO,OAAO,aAAa;AAAA,UACzB,WAAW;AAAA,UACX,WAAW;AAAA,UACX,aAAa;AAAA,QACnB,CAAK;AACD,kBAAU,QAAQ;AAAA,MACnB;AAAA,IACH,CAAC;AAID,UAAM,iBAAiB,MAAM;AAC3BA,oBAAAA,MAAY,MAAA,OAAA,iCAAA,QAAQ;AAEpBA,oBAAAA,MAAI,uBAAuB;AAAA,QACzB,UAAU,KAAK;AAAA,QACf,SAAS,MAAM;AACbA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AAAA,QACF;AAAA,QACD,MAAM,CAAC,UAAU;AACfA,wBAAAA,MAAc,MAAA,SAAA,iCAAA,SAAS,KAAK;AAC5BA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IAEH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrHA,GAAG,WAAW,eAAe;"}