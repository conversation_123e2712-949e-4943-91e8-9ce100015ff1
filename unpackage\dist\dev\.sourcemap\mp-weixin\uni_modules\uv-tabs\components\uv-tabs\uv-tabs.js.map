{"version": 3, "file": "uv-tabs.js", "sources": ["uni_modules/uv-tabs/components/uv-tabs/uv-tabs.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RToveW91bmdQcm9qZWN0L2FnZW50LW1pbmktdWkvdW5pX21vZHVsZXMvdXYtdGFicy9jb21wb25lbnRzL3V2LXRhYnMvdXYtdGFicy52dWU"], "sourcesContent": ["<template>\r\n\t<view class=\"uv-tabs\" :style=\"[$uv.addStyle(customStyle)]\">\r\n\t\t<view class=\"uv-tabs__wrapper\">\r\n\t\t\t<slot name=\"left\" />\r\n\t\t\t<view class=\"uv-tabs__wrapper__scroll-view-wrapper\">\r\n\t\t\t\t<scroll-view\r\n\t\t\t\t\t:scroll-x=\"scrollable\"\r\n\t\t\t\t\t:scroll-left=\"scrollLeft\"\r\n\t\t\t\t\tscroll-with-animation\r\n\t\t\t\t\tclass=\"uv-tabs__wrapper__scroll-view\"\r\n\t\t\t\t\t:show-scrollbar=\"false\"\r\n\t\t\t\t\tref=\"uv-tabs__wrapper__scroll-view\"\r\n\t\t\t\t>\r\n\t\t\t\t\t<view\r\n\t\t\t\t\t\tclass=\"uv-tabs__wrapper__nav\"\r\n\t\t\t\t\t\tref=\"uv-tabs__wrapper__nav\"\r\n\t\t\t\t\t\t:style=\"{\r\n\t\t\t\t\t\t\tflex: scrollable ? '' : 1\r\n\t\t\t\t\t\t}\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<view\r\n\t\t\t\t\t\t\tclass=\"uv-tabs__wrapper__nav__item\"\r\n\t\t\t\t\t\t\tv-for=\"(item, index) in list\"\r\n\t\t\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\t\t\t@tap=\"clickHandler(item, index)\"\r\n\t\t\t\t\t\t\t:ref=\"`uv-tabs__wrapper__nav__item-${index}`\"\r\n\t\t\t\t\t\t\t:style=\"[{flex: scrollable ? '' : 1},$uv.addStyle(itemStyle)]\"\r\n\t\t\t\t\t\t\t:class=\"[`uv-tabs__wrapper__nav__item-${index}`, item.disabled && 'uv-tabs__wrapper__nav__item--disabled']\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<text\r\n\t\t\t\t\t\t\t\t:class=\"[item.disabled && 'uv-tabs__wrapper__nav__item__text--disabled']\"\r\n\t\t\t\t\t\t\t\tclass=\"uv-tabs__wrapper__nav__item__text\"\r\n\t\t\t\t\t\t\t\t:style=\"[textStyle(index)]\"\r\n\t\t\t\t\t\t\t>{{ item[keyName] }}</text>\r\n\t\t\t\t\t\t\t<uv-badge\r\n\t\t\t\t\t\t\t\t:show=\"!!(item.badge && (item.badge.show || item.badge.isDot || item.badge.value))\"\r\n\t\t\t\t\t\t\t\t:isDot=\"item.badge && item.badge.isDot || propsBadge.isDot\"\r\n\t\t\t\t\t\t\t\t:value=\"item.badge && item.badge.value || propsBadge.value\"\r\n\t\t\t\t\t\t\t\t:max=\"item.badge && item.badge.max || propsBadge.max\"\r\n\t\t\t\t\t\t\t\t:type=\"item.badge && item.badge.type || propsBadge.type\"\r\n\t\t\t\t\t\t\t\t:showZero=\"item.badge && item.badge.showZero || propsBadge.showZero\"\r\n\t\t\t\t\t\t\t\t:bgColor=\"item.badge && item.badge.bgColor || propsBadge.bgColor\"\r\n\t\t\t\t\t\t\t\t:color=\"item.badge && item.badge.color || propsBadge.color\"\r\n\t\t\t\t\t\t\t\t:shape=\"item.badge && item.badge.shape || propsBadge.shape\"\r\n\t\t\t\t\t\t\t\t:numberType=\"item.badge && item.badge.numberType || propsBadge.numberType\"\r\n\t\t\t\t\t\t\t\t:inverted=\"item.badge && item.badge.inverted || propsBadge.inverted\"\r\n\t\t\t\t\t\t\t\tcustomStyle=\"margin-left: 4px;\"\r\n\t\t\t\t\t\t\t></uv-badge>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<!-- #ifdef APP-NVUE -->\r\n\t\t\t\t\t\t<view\r\n\t\t\t\t\t\t\tclass=\"uv-tabs__wrapper__nav__line\"\r\n\t\t\t\t\t\t\tref=\"uv-tabs__wrapper__nav__line\"\r\n\t\t\t\t\t\t\t:style=\"[{\r\n\t\t\t\t\t\t\t\twidth: $uv.addUnit(lineWidth),\r\n\t\t\t\t\t\t\t\theight: firstTime?0:$uv.addUnit(lineHeight),\r\n\t\t\t\t\t\t\t\tbackground: lineColor,\r\n\t\t\t\t\t\t\t\tbackgroundSize: lineBgSize\r\n\t\t\t\t\t\t\t}]\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t\t<!-- #ifndef APP-NVUE -->\r\n\t\t\t\t\t\t<view\r\n\t\t\t\t\t\t\tclass=\"uv-tabs__wrapper__nav__line\"\r\n\t\t\t\t\t\t\tref=\"uv-tabs__wrapper__nav__line\"\r\n\t\t\t\t\t\t\t:style=\"[{\r\n\t\t\t\t\t\t\t\t\twidth: $uv.addUnit(lineWidth),\r\n\t\t\t\t\t\t\t\t\ttransform: `translate(${lineOffsetLeft}px)`,\r\n\t\t\t\t\t\t\t\t\ttransitionDuration: `${firstTime ? 0 : duration}ms`,\r\n\t\t\t\t\t\t\t\t\theight: firstTime?0:$uv.addUnit(lineHeight),\r\n\t\t\t\t\t\t\t\t\tbackground: lineColor,\r\n\t\t\t\t\t\t\t\t\tbackgroundSize: lineBgSize,\r\n\t\t\t\t\t\t\t\t}]\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</scroll-view>\r\n\t\t\t</view>\r\n\t\t\t<slot name=\"right\" />\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport mpMixin from '@/uni_modules/uv-ui-tools/libs/mixin/mpMixin.js'\r\n\timport mixin from '@/uni_modules/uv-ui-tools/libs/mixin/mixin.js'\r\n\timport uvBadgeProps from '@/uni_modules/uv-badge/components/uv-badge/props.js'\r\n\t// #ifdef APP-NVUE\r\n\tconst animation = uni.requireNativePlugin('animation')\r\n\tconst dom = uni.requireNativePlugin('dom')\r\n\t// #endif\r\n\timport props from './props.js';\r\n\t/**\r\n\t * Tabs 标签\r\n\t * @description tabs标签组件，在标签多的时候，可以配置为左右滑动，标签少的时候，可以禁止滑动。 该组件的一个特点是配置为滚动模式时，激活的tab会自动移动到组件的中间位置。\r\n\t * @tutorial https://www.uvui.cn/components/tabs.html\r\n\t * @property {Array}\tlist\t标签数组，元素为对象，如[{name: '推荐'}]\r\n\t * @property {String | Number}\tduration\t\t\t滑块移动一次所需的时间，单位秒（默认 200 ）\r\n\t * @property {String | Object} activeStyle\t菜单选择中时的样式（默认{ color: '#303133' }）\r\n\t * @property {String | Object} inactiveStyle\t菜单非选择中时的样式（默认{ color: '#606266' }）\r\n\t * @property {String | Number} lineWidth\t滑块长度（默认 20）\r\n\t * @property {String | Number} lineHeight\t滑块高度（默认 3）\r\n\t * @property {String}\tlineColor\t滑块颜色（默认：'#3c9cff'）\r\n\t * @property {String} lineBgSize\t滑块背景显示大小，当滑块背景设置为图片时使用（默认 cover）\r\n\t * @property {String | Number} itemStyle\t菜单item的样式（默认 { height: '44px' }）\r\n\t * @property {String}\tscrollable\t菜单是否可滚动，选项很少的时候设置为false整个tabs自动居中显示（默认：true）\r\n\t * @property {String | Number}\tcurrent\t当前选中标签的索引（默认 0 ）\r\n\t * @property {String}\tkeyName\t从list元素对象中读取的键名（默认 'name' ）\r\n\t * @property {String | Number}\tswierWidth\t\t\tswiper的宽度（默认 '750rpx' ）\r\n\t * @property {String | Object}\tcustomStyle\t 自定义外部样式\r\n\t * \r\n\t * @event {Function(index)} change 标签改变时触发 index: 点击了第几个tab，索引从0开始\r\n\t * @event {Function(index)} click 点击标签时触发 index: 点击了第几个tab，索引从0开始\r\n\t * @example <uv-tabs :list=\"list\" :is-scroll=\"false\" :current=\"current\" @change=\"change\"></uv-tabs>\r\n\t */\r\n\texport default {\r\n\t\tname: 'uv-tabs',\r\n\t\temits: ['click','change'],\r\n\t\tmixins: [mpMixin, mixin, props],\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tfirstTime: true,\r\n\t\t\t\tscrollLeft: 0,\r\n\t\t\t\tscrollViewWidth: 0,\r\n\t\t\t\tlineOffsetLeft: 0,\r\n\t\t\t\ttabsRect: {\r\n\t\t\t\t\tleft: 0\r\n\t\t\t\t},\r\n\t\t\t\tinnerCurrent: 0,\r\n\t\t\t\tmoving: false,\r\n\t\t\t}\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\tcurrent: {\r\n\t\t\t\timmediate: true,\r\n\t\t\t\thandler (newValue, oldValue) {\r\n\t\t\t\t\t// 内外部值不相等时，才尝试移动滑块\r\n\t\t\t\t\tif (newValue !== this.innerCurrent) {\r\n\t\t\t\t\t\tthis.innerCurrent = newValue\r\n\t\t\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\t\t\tthis.resize()\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// list变化时，重新渲染list各项信息\r\n\t\t\tlist() {\r\n\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\tthis.resize()\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\ttextStyle() {\r\n\t\t\t\treturn index => {\r\n\t\t\t\t\tconst style = {}\r\n\t\t\t\t\t// 取当期是否激活的样式\r\n\t\t\t\t\tconst customeStyle = index == this.innerCurrent ? this.$uv.addStyle(this.activeStyle) : this.$uv\r\n\t\t\t\t\t\t.addStyle(\r\n\t\t\t\t\t\t\tthis.inactiveStyle)\r\n\t\t\t\t\t// 如果当前菜单被禁用，则加上对应颜色，需要在此做处理，是因为nvue下，无法在style样式中通过!import覆盖标签的内联样式\r\n\t\t\t\t\tif (this.list[index].disabled) {\r\n\t\t\t\t\t\tstyle.color = '#c8c9cc'\r\n\t\t\t\t\t}\r\n\t\t\t\t\treturn this.$uv.deepMerge(customeStyle, style)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tpropsBadge() {\r\n\t\t\t\treturn uvBadgeProps\r\n\t\t\t}\r\n\t\t},\r\n\t\tasync mounted() {\r\n\t\t\tthis.init()\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tsetLineLeft() {\r\n\t\t\t\tconst tabItem = this.list[this.innerCurrent];\r\n\t\t\t\tif (!tabItem) {\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t// 获取滑块该移动的位置\r\n\t\t\t\tlet lineOffsetLeft = this.list\r\n\t\t\t\t\t.slice(0, this.innerCurrent)\r\n\t\t\t\t\t.reduce((total, curr) => total + curr.rect.width, 0);\r\n        // 获取下划线的数值px表示法\r\n\t\t\t\tlet lineWidth = this.$uv.getPx(this.lineWidth);\r\n\t\t\t\t// 如果传的值未带单位+设置了全局单位，则带上单位计算，这样才没有误差\r\n\t\t\t\tif (this.$uv.test.number(this.lineWidth) && this.$uv.unit) {\r\n\t\t\t\t\tlineWidth = this.$uv.getPx(`${this.lineWidth}${this.$uv.unit}`);\r\n\t\t\t\t}\r\n\t\t\t\tthis.lineOffsetLeft = lineOffsetLeft + (tabItem.rect.width - lineWidth) / 2\r\n\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\t// 第一次移动滑块，无需过渡时间\r\n\t\t\t\tthis.animation(this.lineOffsetLeft, this.firstTime ? 0 : parseInt(this.duration))\r\n\t\t\t\t// #endif\r\n\r\n\t\t\t\t// 如果是第一次执行此方法，让滑块在初始化时，瞬间滑动到第一个tab item的中间\r\n\t\t\t\t// 这里需要一个定时器，因为在非nvue下，是直接通过style绑定过渡时间，需要等其过渡完成后，再设置为false(非第一次移动滑块)\r\n\t\t\t\tif (this.firstTime) {\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tthis.firstTime = false\r\n\t\t\t\t\t}, 20);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// nvue下设置滑块的位置\r\n\t\t\tanimation(x, duration = 0) {\r\n\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\tconst ref = this.$refs['uv-tabs__wrapper__nav__line']\r\n\t\t\t\tanimation.transition(ref, {\r\n\t\t\t\t\tstyles: {\r\n\t\t\t\t\t\ttransform: `translateX(${x}px)`\r\n\t\t\t\t\t},\r\n\t\t\t\t\tduration\r\n\t\t\t\t})\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\t// 点击某一个标签\r\n\t\t\tclickHandler(item, index) {\r\n\t\t\t\t// 因为标签可能为disabled状态，所以click是一定会发出的，但是change事件是需要可用的状态才发出\r\n\t\t\t\tthis.$emit('click', {\r\n\t\t\t\t\t...item,\r\n\t\t\t\t\tindex\r\n\t\t\t\t})\r\n\t\t\t\t// 如果disabled状态，返回\r\n\t\t\t\tif (item.disabled) return\r\n\t\t\t\tif(this.innerCurrent != index) {\r\n\t\t\t\t\tthis.$emit('change', {\r\n\t\t\t\t\t\t...item,\r\n\t\t\t\t\t\tindex\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\tthis.innerCurrent = index\r\n\t\t\t\t// #ifndef APP-NVUE\r\n\t\t\t\tthis.$nextTick(()=>{\r\n\t\t\t\t\tthis.resize()\r\n\t\t\t\t})\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\tthis.$nextTick(()=>{\r\n\t\t\t\t\t// nvue模式下再给点延时，确保万无一失\r\n\t\t\t\t\tthis.$uv.sleep(30).then(res=>{\r\n\t\t\t\t\t\tthis.resize()\r\n\t\t\t\t\t});\r\n\t\t\t\t})\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\tinit() {\r\n\t\t\t\tthis.$uv.sleep().then(() => {\r\n\t\t\t\t\tthis.resize()\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tsetScrollLeft() {\r\n\t\t\t\t// 当前活动tab的布局信息，有tab菜单的width和left(为元素左边界到父元素左边界的距离)等信息\r\n\t\t\t\tconst tabRect = this.list[this.innerCurrent]\r\n\t\t\t\t// 累加得到当前item到左边的距离\r\n\t\t\t\tconst offsetLeft = this.list\r\n\t\t\t\t\t.slice(0, this.innerCurrent)\r\n\t\t\t\t\t.reduce((total, curr) => {\r\n\t\t\t\t\t\treturn total + curr.rect.width\r\n\t\t\t\t\t}, 0)\r\n\t\t\t\t// 此处为屏幕宽度\r\n\t\t\t\tconst windowWidth = this.$uv.sys().windowWidth\r\n\t\t\t\t// 将活动的tabs-item移动到屏幕正中间，实际上是对scroll-view的移动\r\n\t\t\t\tlet scrollLeft = offsetLeft - (this.tabsRect.width - tabRect.rect.width) / 2 - (windowWidth - this.tabsRect\r\n\t\t\t\t\t.right) / 2 + this.tabsRect.left / 2\r\n\t\t\t\t// 这里做一个限制，限制scrollLeft的最大值为整个scroll-view宽度减去tabs组件的宽度\r\n\t\t\t\tscrollLeft = Math.min(scrollLeft, this.scrollViewWidth - this.tabsRect.width)\r\n\t\t\t\tthis.scrollLeft = Math.max(0, scrollLeft)\r\n\t\t\t},\r\n\t\t\t// 获取所有标签的尺寸\r\n\t\t\tresize() {\r\n\t\t\t\t// 如果不存在list，则不处理\r\n\t\t\t\tif(this.list.length === 0) {\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tPromise.all([this.getTabsRect(), this.getAllItemRect()]).then(([tabsRect, itemRect = []]) => {\r\n\t\t\t\t\tthis.tabsRect = tabsRect\r\n\t\t\t\t\tthis.scrollViewWidth = 0\r\n\t\t\t\t\titemRect.map((item, index) => {\r\n\t\t\t\t\t\t// 计算scroll-view的宽度，这里\r\n\t\t\t\t\t\tthis.scrollViewWidth += item.width\r\n\t\t\t\t\t\t// 另外计算每一个item的中心点X轴坐标\r\n\t\t\t\t\t\tthis.list[index].rect = item\r\n\t\t\t\t\t})\r\n\t\t\t\t\t// 获取了tabs的尺寸之后，设置滑块的位置\r\n\t\t\t\t\tthis.setLineLeft()\r\n\t\t\t\t\tthis.setScrollLeft()\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 获取导航菜单的尺寸\r\n\t\t\tgetTabsRect() {\r\n\t\t\t\treturn new Promise(resolve => {\r\n\t\t\t\t\tthis.queryRect('uv-tabs__wrapper__scroll-view').then(size => resolve(size))\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 获取所有标签的尺寸\r\n\t\t\tgetAllItemRect() {\r\n\t\t\t\treturn new Promise(resolve => {\r\n\t\t\t\t\tconst promiseAllArr = this.list.map((item, index) => this.queryRect(\r\n\t\t\t\t\t\t`uv-tabs__wrapper__nav__item-${index}`, true))\r\n\t\t\t\t\tPromise.all(promiseAllArr).then(sizes => resolve(sizes))\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 获取各个标签的尺寸\r\n\t\t\tqueryRect(el, item) {\r\n\t\t\t\t// #ifndef APP-NVUE\r\n\t\t\t\t// $uvGetRect为uni-ui自带的节点查询简化方法，详见文档介绍：https://www.uvui.cn/js/getRect.html\r\n\t\t\t\t// 组件内部一般用this.$uvGetRect，对外的为getRect，二者功能一致，名称不同\r\n\t\t\t\treturn new Promise(resolve => {\r\n\t\t\t\t\tthis.$uvGetRect(`.${el}`).then(size => {\r\n\t\t\t\t\t\tresolve(size)\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t\t// #endif\r\n\r\n\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\t// nvue下，使用dom模块查询元素高度\r\n\t\t\t\t// 返回一个promise，让调用此方法的主体能使用then回调\r\n\t\t\t\treturn new Promise(resolve => {\r\n\t\t\t\t\tdom.getComponentRect(item ? this.$refs[el][0] : this.$refs[el], res => {\r\n\t\t\t\t\t\tresolve(res.size)\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t},\r\n\t}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n\t@import '@/uni_modules/uv-ui-tools/libs/css/components.scss';\r\n\t@import '@/uni_modules/uv-ui-tools/libs/css/color.scss';\r\n\t.uv-tabs {\r\n\r\n\t\t&__wrapper {\r\n\t\t\t@include flex;\r\n\t\t\talign-items: center;\r\n\r\n\t\t\t&__scroll-view-wrapper {\r\n\t\t\t\tflex: 1;\r\n\t\t\t\t/* #ifndef APP-NVUE */\r\n\t\t\t\toverflow: auto hidden;\r\n\t\t\t\t/* #endif */\r\n\t\t\t}\r\n\r\n\t\t\t&__scroll-view {\r\n\t\t\t\t@include flex;\r\n\t\t\t\tflex: 1;\r\n\t\t\t}\r\n\r\n\t\t\t&__nav {\r\n\t\t\t\t@include flex;\r\n\t\t\t\tposition: relative;\r\n\r\n\t\t\t\t&__item {\r\n\t\t\t\t\tpadding: 0 11px;\r\n\t\t\t\t\t@include flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tjustify-content: center;\r\n\r\n\t\t\t\t\t&--disabled {\r\n\t\t\t\t\t\t/* #ifndef APP-NVUE */\r\n\t\t\t\t\t\tcursor: not-allowed;\r\n\t\t\t\t\t\t/* #endif */\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t&__text {\r\n\t\t\t\t\t\tfont-size: 15px;\r\n\t\t\t\t\t\tcolor: $uv-content-color;\r\n\r\n\t\t\t\t\t\t&--disabled {\r\n\t\t\t\t\t\t\tcolor: $uv-disabled-color !important;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t&__line {\r\n\t\t\t\t\theight: 3px;\r\n\t\t\t\t\tbackground: $uv-primary;\r\n\t\t\t\t\twidth: 30px;\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\tbottom: 2px;\r\n\t\t\t\t\tborder-radius: 100px;\r\n\t\t\t\t\ttransition-property: transform;\r\n\t\t\t\t\ttransition-duration: 300ms;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import Component from 'E:/youngProject/agent-mini-ui/uni_modules/uv-tabs/components/uv-tabs/uv-tabs.vue'\nwx.createComponent(Component)"], "names": ["mpMixin", "mixin", "props", "uvBadgeProps"], "mappings": ";;;;;;AAoHC,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,OAAO,CAAC,SAAQ,QAAQ;AAAA,EACxB,QAAQ,CAACA,yCAAAA,SAASC,uCAAK,OAAEC,gDAAK;AAAA,EAC9B,OAAO;AACN,WAAO;AAAA,MACN,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,MAChB,UAAU;AAAA,QACT,MAAM;AAAA,MACN;AAAA,MACD,cAAc;AAAA,MACd,QAAQ;AAAA,IACT;AAAA,EACA;AAAA,EACD,OAAO;AAAA,IACN,SAAS;AAAA,MACR,WAAW;AAAA,MACX,QAAS,UAAU,UAAU;AAE5B,YAAI,aAAa,KAAK,cAAc;AACnC,eAAK,eAAe;AACpB,eAAK,UAAU,MAAM;AACpB,iBAAK,OAAO;AAAA,WACZ;AAAA,QACF;AAAA,MACD;AAAA,IACA;AAAA;AAAA,IAED,OAAO;AACN,WAAK,UAAU,MAAM;AACpB,aAAK,OAAO;AAAA,OACZ;AAAA,IACF;AAAA,EACA;AAAA,EACD,UAAU;AAAA,IACT,YAAY;AACX,aAAO,WAAS;AACf,cAAM,QAAQ,CAAC;AAEf,cAAM,eAAe,SAAS,KAAK,eAAe,KAAK,IAAI,SAAS,KAAK,WAAW,IAAI,KAAK,IAC3F;AAAA,UACA,KAAK;AAAA,QAAa;AAEpB,YAAI,KAAK,KAAK,KAAK,EAAE,UAAU;AAC9B,gBAAM,QAAQ;AAAA,QACf;AACA,eAAO,KAAK,IAAI,UAAU,cAAc,KAAK;AAAA,MAC9C;AAAA,IACA;AAAA,IACD,aAAa;AACZ,aAAOC,6CAAW;AAAA,IACnB;AAAA,EACA;AAAA,EACD,MAAM,UAAU;AACf,SAAK,KAAK;AAAA,EACV;AAAA,EACD,SAAS;AAAA,IACR,cAAc;AACb,YAAM,UAAU,KAAK,KAAK,KAAK,YAAY;AAC3C,UAAI,CAAC,SAAS;AACb;AAAA,MACD;AAEA,UAAI,iBAAiB,KAAK,KACxB,MAAM,GAAG,KAAK,YAAY,EAC1B,OAAO,CAAC,OAAO,SAAS,QAAQ,KAAK,KAAK,OAAO,CAAC;AAEpD,UAAI,YAAY,KAAK,IAAI,MAAM,KAAK,SAAS;AAE7C,UAAI,KAAK,IAAI,KAAK,OAAO,KAAK,SAAS,KAAK,KAAK,IAAI,MAAM;AAC1D,oBAAY,KAAK,IAAI,MAAM,GAAG,KAAK,SAAS,GAAG,KAAK,IAAI,IAAI,EAAE;AAAA,MAC/D;AACA,WAAK,iBAAiB,kBAAkB,QAAQ,KAAK,QAAQ,aAAa;AAQ1E,UAAI,KAAK,WAAW;AACnB,mBAAW,MAAM;AAChB,eAAK,YAAY;AAAA,QACjB,GAAE,EAAE;AAAA,MACN;AAAA,IACA;AAAA;AAAA,IAED,UAAU,GAAG,WAAW,GAAG;AAAA,IAU1B;AAAA;AAAA,IAED,aAAa,MAAM,OAAO;AAEzB,WAAK,MAAM,SAAS;AAAA,QACnB,GAAG;AAAA,QACH;AAAA,OACA;AAED,UAAI,KAAK;AAAU;AACnB,UAAG,KAAK,gBAAgB,OAAO;AAC9B,aAAK,MAAM,UAAU;AAAA,UACpB,GAAG;AAAA,UACH;AAAA,SACA;AAAA,MACF;AACA,WAAK,eAAe;AAEpB,WAAK,UAAU,MAAI;AAClB,aAAK,OAAO;AAAA,OACZ;AAAA,IAUD;AAAA,IACD,OAAO;AACN,WAAK,IAAI,MAAO,EAAC,KAAK,MAAM;AAC3B,aAAK,OAAO;AAAA,OACZ;AAAA,IACD;AAAA,IACD,gBAAgB;AAEf,YAAM,UAAU,KAAK,KAAK,KAAK,YAAY;AAE3C,YAAM,aAAa,KAAK,KACtB,MAAM,GAAG,KAAK,YAAY,EAC1B,OAAO,CAAC,OAAO,SAAS;AACxB,eAAO,QAAQ,KAAK,KAAK;AAAA,MACzB,GAAE,CAAC;AAEL,YAAM,cAAc,KAAK,IAAI,IAAK,EAAC;AAEnC,UAAI,aAAa,cAAc,KAAK,SAAS,QAAQ,QAAQ,KAAK,SAAS,KAAK,cAAc,KAAK,SACjG,SAAS,IAAI,KAAK,SAAS,OAAO;AAEpC,mBAAa,KAAK,IAAI,YAAY,KAAK,kBAAkB,KAAK,SAAS,KAAK;AAC5E,WAAK,aAAa,KAAK,IAAI,GAAG,UAAU;AAAA,IACxC;AAAA;AAAA,IAED,SAAS;AAER,UAAG,KAAK,KAAK,WAAW,GAAG;AAC1B;AAAA,MACD;AACA,cAAQ,IAAI,CAAC,KAAK,YAAW,GAAI,KAAK,eAAgB,CAAA,CAAC,EAAE,KAAK,CAAC,CAAC,UAAU,WAAW,CAAE,CAAA,MAAM;AAC5F,aAAK,WAAW;AAChB,aAAK,kBAAkB;AACvB,iBAAS,IAAI,CAAC,MAAM,UAAU;AAE7B,eAAK,mBAAmB,KAAK;AAE7B,eAAK,KAAK,KAAK,EAAE,OAAO;AAAA,SACxB;AAED,aAAK,YAAY;AACjB,aAAK,cAAc;AAAA,OACnB;AAAA,IACD;AAAA;AAAA,IAED,cAAc;AACb,aAAO,IAAI,QAAQ,aAAW;AAC7B,aAAK,UAAU,+BAA+B,EAAE,KAAK,UAAQ,QAAQ,IAAI,CAAC;AAAA,OAC1E;AAAA,IACD;AAAA;AAAA,IAED,iBAAiB;AAChB,aAAO,IAAI,QAAQ,aAAW;AAC7B,cAAM,gBAAgB,KAAK,KAAK,IAAI,CAAC,MAAM,UAAU,KAAK;AAAA,UACzD,+BAA+B,KAAK;AAAA,UAAI;AAAA,QAAI,CAAC;AAC9C,gBAAQ,IAAI,aAAa,EAAE,KAAK,WAAS,QAAQ,KAAK,CAAC;AAAA,OACvD;AAAA,IACD;AAAA;AAAA,IAED,UAAU,IAAI,MAAM;AAInB,aAAO,IAAI,QAAQ,aAAW;AAC7B,aAAK,WAAW,IAAI,EAAE,EAAE,EAAE,KAAK,UAAQ;AACtC,kBAAQ,IAAI;AAAA,SACZ;AAAA,OACD;AAAA,IAYD;AAAA,EACD;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtUD,GAAG,gBAAgB,SAAS;"}